package com.kerryprops.kip.bill.log4j;

import lombok.Getter;
import lombok.Setter;
import org.apache.logging.log4j.core.LogEvent;
import org.apache.logging.log4j.core.config.plugins.Plugin;
import org.apache.logging.log4j.core.pattern.ConverterKeys;
import org.apache.logging.log4j.core.pattern.LogEventPatternConverter;
import org.apache.logging.log4j.message.Message;
import org.apache.logging.log4j.message.MultiformatMessage;

import java.io.PrintStream;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.MessageFormat;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Scanner;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 日志敏感数据脱敏转换器.
 * <pre>
 * 使用教程：
 * 1、在 resources 目录下创建：log-masking-patterns.txt
 * 2、在 log-masking-patterns.txt 中写过滤规则
 *
 * 过滤规则写法： 字段名;;保留前几位数,保留后几位数
 * 如：
 * name;;1,0 匹配name字段，保留前一位
 * phone;;3,4 匹配phone字段，保留前3位，后4位数
 *
 * 字段类型设置：
 * 默认为普通文本字段匹配，如 name;;1,0 ，构建正则表达式为 name=(.*)[,|]
 * json类型字段匹配，字段被双引号包含： "字段名";;保留前几位数,保留后几位数
 * xml标签匹配，字段被中括号包含： <字段名>;;保留前几位数,保留后几位数
 * </pre>
 */
@Plugin(name = "LogMaskConverter", category = "Converter")
@ConverterKeys({"m", "msg", "message"})
public class LogMaskConverter extends LogEventPatternConverter {

    private static LogMaskConverter logMaskConverter;

    private final List<PatternHolder> patternHolders = new LinkedList<>();

    private final String[] formats;

    private LogMaskConverter(String[] options) {
        super("m", "m");
        formats = options;
        init();
    }

    public static synchronized LogMaskConverter newInstance(String[] options) {
        LOGGER.info("create LogMaskConverter Thread: " + Thread.currentThread().getName());
        if (Objects.isNull(LogMaskConverter.logMaskConverter)) {
            LogMaskConverter.logMaskConverter = new LogMaskConverter(options);
        }
        return LogMaskConverter.logMaskConverter;
    }

    @Override
    public void format(LogEvent event, StringBuilder builder) {
        Message msg = event.getMessage();
        String result = null;
        if (Objects.nonNull(msg)) {
            if (msg instanceof MultiformatMessage) {
                result = ((MultiformatMessage) msg).getFormattedMessage(formats);
            } else {
                result = msg.getFormattedMessage();
            }
        }
        mask(result, builder);
    }

    protected void init() {
        try (var scanner = new Scanner(
                Objects.requireNonNull(getClass().getResourceAsStream("/log-masking-patterns.txt")))) {
            while (scanner.hasNextLine()) {
                String conf = scanner.nextLine().trim();
                doInit(conf);
            }
        } catch (Exception e) {
            LOGGER.error("Failed to load masking patterns for log4j: " + e.getStackTrace()[0].getClassName()
                    + "." + e.getStackTrace()[0].getMethodName() + ":" + e.getStackTrace()[0].getLineNumber());
        }
    }

    private static String replace(PatternHolder patternHolder, String source) {
        if (Objects.isNull(patternHolder.getPattern())) {
            return source;
        }

        Matcher matcher = patternHolder.getPattern().matcher(source);
        StringBuilder sb = new StringBuilder();
        while (matcher.find()) {
            matcher.appendReplacement(sb, patternHolder.getReplacementFun().apply(matcher));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    private void doInit(String conf) {
        try {
            if (conf.contains(";;")) {
                PatternHolder patternHolder = LogMaskPatternBuilder.fromConfig(conf).build();
                patternHolders.add(patternHolder);
            }
        } catch (Exception e) {
            LOGGER.error("{}: Failed to load patterns for log4j: {}.{}:{}:{}", conf,
                         e.getStackTrace()[0].getClassName(), e.getStackTrace()[0].getMethodName(),
                         e.getStackTrace()[0].getLineNumber(), e.getMessage());
        }
    }

    /**
     * @param msg     Log message.
     * @param builder StringBuilder.
     */
    private void mask(String msg, StringBuilder builder) {
        String message = msg;
        if (Objects.isNull(message)) {
            builder.append("null");
        } else {
            for (PatternHolder patternHolder : patternHolders) {
                message = replace(patternHolder, message);
            }
            builder.append(message);
        }
    }

    @Getter
    @Setter
    public static class PatternHolder {

        private Pattern pattern;

        private int rightPartLength;

        private int leftPartLength;

        private String fieldName;

        private Function<Matcher, String> replacementFun;
    }

    public static class BootLogger {

        private final String messagePattern;

        private final PrintStream out;

        private final PrintStream err;

        public BootLogger(String loggerName) {
            this(loggerName, System.out, System.err);
        }

        public BootLogger(String loggerName, PrintStream out, PrintStream err) {
            Objects.requireNonNull(loggerName, "loggerName must not be null");

            messagePattern = "{0,date,yyyy-MM-dd HH:mm:ss} [{1}](" + loggerName + ") {2}{3}";
            this.out = out;
            this.err = err;
        }

        public static BootLogger getLogger(String loggerName) {
            return new BootLogger(loggerName);
        }

        public void info(String msg) {
            String formatMessage = format("INFO ", msg, "");
            out.println(formatMessage);
        }

        public void error(String msg) {
            error(msg, null);
        }

        public void error(String msg, Throwable throwable) {
            String exceptionMessage = toString(throwable);
            String formatMessage = format("ERROR ", msg, exceptionMessage);
            err.println(formatMessage);
        }

        private String format(String logLevel, String msg, String exceptionMessage) {
            exceptionMessage = defaultString(exceptionMessage);
            MessageFormat messageFormat = new MessageFormat(messagePattern);
            long date = System.currentTimeMillis();
            Object[] parameter = {date, logLevel, msg, exceptionMessage};
            return messageFormat.format(parameter);
        }

        private String toString(Throwable throwable) {
            if (throwable == null) {
                return "";
            }
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            pw.println();
            throwable.printStackTrace(pw);
            pw.close();
            return sw.toString();
        }

        private String defaultString(String exceptionMessage) {
            return Objects.requireNonNullElse(exceptionMessage, "");
        }

    }

    public abstract static class LogMaskPatternBuilder {

        public static final String BASE_STR_FORMAT = "(.*?)";

        public static final String TAG_SUFFIX_FORMAT = "</";

        public static final String JSON_SUFFIX_FORMAT = "\"";

        public static final String JSON_INTER_FIELD_FORMAT = ": *\"";

        public static final String PLAIN_SUFFIX_FORMAT = "[,|)]";

        protected PatternHolder patternHolder = new PatternHolder();

        public static LogMaskPatternBuilder fromConfig(String conf) {
            if (conf.startsWith("<")) {
                return new XmlMaskPatternBuilder(conf);
            } else if (conf.startsWith("\"")) {
                return new JsonMaskPatternBuilder(conf);
            } else {
                return new PlainTextPatternBuilder(conf);
            }
        }

        public static String remain(String str, int remainLeft, int remainRight) {
            if (Objects.isNull(str)) {
                return null;
            }
            if ((remainLeft + remainRight) >= str.length()) {
                return str;
            }

            return str.substring(0, remainLeft)
                    + "*".repeat(str.length() - (remainLeft + remainRight))
                    + str.substring(str.length() - remainRight);
        }

        protected void splitFieldNameAndPadding(String conf) {
            String[] elements = conf.split(";;");
            String fieldName = elements[0].trim();
            if (fieldName.isEmpty()) {
                return;
            }

            String paddingConf = elements.length > 1 ? elements[1].trim() : "";
            patternHolder.setFieldName(fieldName);
            if (paddingConf.isEmpty()) {
                patternHolder.setRightPartLength(0);
                patternHolder.setLeftPartLength(0);
            } else {
                splitPadding(paddingConf);
            }
        }

        protected void splitPadding(String paddingConf) {
            String[] elements = paddingConf.split(",");
            String leftPadding = elements[0].trim();
            String rightPadding = elements.length > 1 ? elements[1].trim() : "0";
            Optional.of(Integer.parseInt(rightPadding))
                    .filter(i -> i > 0)
                    .ifPresent(patternHolder::setRightPartLength);
            Optional.of(Integer.parseInt(leftPadding))
                    .filter(i -> i > 0)
                    .ifPresent(patternHolder::setLeftPartLength);
        }

        abstract PatternHolder build();

        public static class JsonMaskPatternBuilder extends LogMaskPatternBuilder {

            public JsonMaskPatternBuilder(String conf) {
                splitFieldNameAndPadding(conf);
            }

            @Override
            PatternHolder build() {
                patternHolder.setPattern(Pattern.compile(
                        patternHolder.getFieldName() + JSON_INTER_FIELD_FORMAT + BASE_STR_FORMAT + JSON_SUFFIX_FORMAT
                ));
                patternHolder.setReplacementFun(matcher ->
                        patternHolder.getFieldName() + ": \""
                                + remain(matcher.group(1), patternHolder.getLeftPartLength(), patternHolder.getRightPartLength())
                                + JSON_SUFFIX_FORMAT
                );
                return patternHolder;
            }

        }

        public static class XmlMaskPatternBuilder extends LogMaskPatternBuilder {

            public XmlMaskPatternBuilder(String conf) {
                splitFieldNameAndPadding(conf);
            }

            @Override
            PatternHolder build() {
                patternHolder.setPattern(Pattern.compile(
                        patternHolder.getFieldName() + BASE_STR_FORMAT + TAG_SUFFIX_FORMAT)
                );
                patternHolder.setReplacementFun(matcher ->
                        patternHolder.getFieldName()
                                + remain(matcher.group(1), patternHolder.getLeftPartLength(), patternHolder.getRightPartLength())
                                + TAG_SUFFIX_FORMAT
                );
                return patternHolder;
            }

        }

        public static class PlainTextPatternBuilder extends LogMaskPatternBuilder {

            public PlainTextPatternBuilder(String conf) {
                splitFieldNameAndPadding(conf);
            }

            @Override
            PatternHolder build() {
                patternHolder.setPattern(Pattern.compile(patternHolder.getFieldName() + "="
                        + BASE_STR_FORMAT + PLAIN_SUFFIX_FORMAT));
                patternHolder.setReplacementFun(matcher -> {
                    String value = matcher.group();
                    return patternHolder.getFieldName() + "="
                            + remain(matcher.group(1), patternHolder.getLeftPartLength(), patternHolder.getRightPartLength())
                            + value.substring(value.length() - 1);
                });
                return patternHolder;
            }

        }

    }

}
