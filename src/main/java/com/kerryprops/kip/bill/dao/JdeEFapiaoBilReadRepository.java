package com.kerryprops.kip.bill.dao;

import com.kerryprops.kip.bill.dao.entity.EFapiaoJDEBill;

/**
 * e-fapiao：JDE发票信息已读表（已请求开票）-Dao层操作接口
 *
 * <AUTHOR>
 * @Date 2023-3-28
 */
public interface JdeEFapiaoBilReadRepository {

    int updateStatusReaded(String kco, String billType, int doc, String paymentItem, String eFapiaoJdeBillReadedStaus);

    /**
     * 新增已读信息
     */
    int insertReadedInfo(EFapiaoJDEBill eFapiaoJdeBill);

    Integer countBySalesbillNo(EFapiaoJDEBill eFapiaoJdeBill);

}
