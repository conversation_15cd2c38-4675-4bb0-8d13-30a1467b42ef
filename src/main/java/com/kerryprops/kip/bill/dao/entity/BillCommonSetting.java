package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Entity
@Table(name = "bill_common_setting")
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class BillCommonSetting extends BaseEntity {

    @Column(name = "type")
    private String type;

    @Column(name = "content")
    private String content;

}
