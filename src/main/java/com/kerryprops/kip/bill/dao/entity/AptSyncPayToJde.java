package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 名   称：apt_sync_jde_log
 * 描   述：
 * 作   者：David <PERSON>
 * 时   间：2021/09/06 10:39:38
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Getter
@Setter
@Entity
@Table(name = "apt_sync_pay_to_jde")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AptSyncPayToJde extends BaseEntity {

    private static final String BLANK = " ";

    /**/
    /*len: 10*/
    @Column(name = "job_id")
    private Long jobId;

    @Column(name = "pay_id")
    private Long payId;

    /**/
    /*len: 10*/
    @Column(name = "status")
    private Integer status;

    /**/
    /*len: 65535*/
    @Column(name = "error_msg")
    private String errorMsg;

    /**/
    /*len: 32*/
    @Column(name = "RCCKNU")
    private String rccknu;

    /**/
    /*len: 10*/
    @Column(name = "RCLNID")
    private Integer rclnid;

    /**/
    /*len: 1*/
    @Column(name = "RCTYIN")
    @Builder.Default
    private String rctyin = "U";

    /**/
    /*len: 5*/
    @Column(name = "RCKCO")
    @Builder.Default
    private String rckco = BLANK;

    /**/
    /*len: 2*/
    @Column(name = "RCDCT")
    @Builder.Default
    private String rcdct = BLANK;

    /**/
    /*len: 10*/
    @Column(name = "RCDOC")
    @Builder.Default
    private Long rcdoc = 0l;

    /**/
    /*len: 3*/
    @Column(name = "RCSFX")
    @Builder.Default
    private String rcsfx = BLANK;

    /**/
    /*len: 10*/
    @Column(name = "RCAN8")
    private Long rcan8;

    /**/
    /*len: 40*/
    @Column(name = "RCALPH")
    @Builder.Default
    private String rcalph = BLANK;

    /**/
    /*len: 10*/
    @Column(name = "RCDMTJ")
    private Integer rcdmtj;

    /**/
    /*len: 11*/
    @Column(name = "RCAG")
    private Double rcag;

    /**/
    /*len: 8*/
    @Column(name = "RCGLBA")
    private String rcglba;

    /**/
    /*len: 3*/
    @Column(name = "RCCRCD")
    @Builder.Default
    private String rccrcd = "CNY";

    /**/
    /*len: 4*/
    @Column(name = "RCGLC")
    @Builder.Default
    private String rcglc = "A003";

    /**/
    /*len: 8*/
    @Column(name = "RCPO")
    private String rcpo;

    /**/
    /*len: 12*/
    @Column(name = "RCMCU")
    private String rcmcu;

    /**/
    /*len: 8*/
    @Column(name = "RCUNIT")
    private String rcunit;

    /**/
    /*len: 1*/
    @Column(name = "RCEDSP")
    @Builder.Default
    private String rcedsp = "0";

    /**/
    /*len: 30*/
    @Column(name = "RCRMK")
    private String rcrmk;

    /**/
    /*len: 10*/
    @Column(name = "RCAAAJ")
    private Double rcaaaj;


}
