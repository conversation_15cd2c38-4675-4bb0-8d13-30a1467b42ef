package com.kerryprops.kip.bill.dao.convert;

import com.kerryprops.kip.bill.common.enums.InvoiceState;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter
public class InvoiceStateConverter implements AttributeConverter<InvoiceState, Integer> {

    @Override
    public Integer convertToDatabaseColumn(InvoiceState attribute) {
        return attribute.getIndex();
    }

    @Override
    public InvoiceState convertToEntityAttribute(Integer dbData) {
        return InvoiceState.fromIndex(dbData);
    }

}
