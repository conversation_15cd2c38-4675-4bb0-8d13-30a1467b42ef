package com.kerryprops.kip.bill.dao.entity;

import jakarta.persistence.Column;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * apt bill
 * table: F03B11
 */
@Data
@Builder
public class JDEAptBill {

    public static final RowMapper<JDEAptBill> ROW_MAPPER = new JDEBillRowMapper();

    /* 单据号 */
    @Column(name = "RPDOC")
    private long documentCode;

    /* 单据类型 */
    @Column(name = "RPDCT")
    private String documentType;

    /* company code */
    @Column(name = "RPKCO")
    private String companyCode;

    /* 单据付款项 */
    @Column(name = "RPSFX")
    private String paymentSfx;

    /* JDE付款状态: 'A' 未付款, RPPST='P' 已付款 */
    @Column(name = "RPPST")
    private String paymentStatus;

    /* JDE账单的更新日期，年月日的数字表达式 */
    @Column(name = "RPUPMJ")
    private long updatedDate;

    /* JDE账单的更新时刻，时分秒的数字表达式 */
    @Column(name = "RPUPMT")
    private long updatedTime;

    /* JDE账单的未结金额 */
    @Column(name = "RPAAP")
    private long unpaidAmount;

    public String groupByBill() {
        StringBuilder sb = new StringBuilder();
        if (StringUtils.isNotEmpty(companyCode)) {
            sb.append(companyCode).append("_");
        }
        sb.append(documentCode).append("_");
        if (StringUtils.isNotEmpty(documentType)) {
            sb.append(documentType).append("_");
        }
        if (StringUtils.isNotEmpty(paymentSfx)) {
            sb.append(paymentSfx);
        }

        return sb.toString();
    }

    private static class JDEBillRowMapper implements RowMapper<JDEAptBill> {

        @Override
        public JDEAptBill mapRow(ResultSet rs, int rowNum) throws SQLException {
            JDEAptBill jdeAptBill = JDEAptBill.builder()
                    .documentType(rs.getString("RPDCT"))
                    .documentCode(rs.getLong("RPDOC"))
                    .companyCode(rs.getString("RPKCO"))
                    .paymentSfx(rs.getString("RPSFX"))
                    .paymentStatus(rs.getString("RPPST"))
                    .updatedDate(rs.getLong("RPUPMJ"))
                    .updatedTime(rs.getLong("RPUPMT"))
                    .unpaidAmount(rs.getLong("RPAAP"))
                    .build();
            return jdeAptBill;
        }

    }

}
