package com.kerryprops.kip.bill.dao;

import com.kerryprops.kip.bill.common.enums.ContentTypeEnum;
import com.kerryprops.kip.bill.common.jpa.JpaAndQueryDslExecutor;
import com.kerryprops.kip.bill.dao.entity.BizContentReadEntity;
import com.kerryprops.kip.bill.dao.entity.QBizContentReadEntity;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * kerry bill: B端-已读表-Repository类
 *
 * <AUTHOR>
 * @date 2023-6-12
 */
public interface BizContentReadRepository extends JpaAndQueryDslExecutor<BizContentReadEntity, Long> {

    default List<Long> findContentIdsByTypeAndUserIdAndContentIds(ContentTypeEnum type, long userId, Collection<Long> contentIds) {
        QBizContentReadEntity qBizContentReadEntity = QBizContentReadEntity.bizContentReadEntity;
        List<Long> readContentIds = this.getJpaQueryFactory()
                .select(qBizContentReadEntity.contentId)
                .from(qBizContentReadEntity)
                .where(qBizContentReadEntity.type.eq(type)
                        .and(qBizContentReadEntity.userId.eq(userId)).and(qBizContentReadEntity.contentId.in(contentIds)))
                .fetch();

        return Optional.ofNullable(readContentIds).orElse(Collections.emptyList());
    }

    Optional<BizContentReadEntity> findFirstByContentIdAndAndTypeAndUserId(long contentId, ContentTypeEnum type, long userId);

}
