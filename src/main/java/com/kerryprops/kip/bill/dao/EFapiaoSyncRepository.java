package com.kerryprops.kip.bill.dao;

import com.kerryprops.kip.bill.common.jpa.JpaAndQueryDslExecutor;
import com.kerryprops.kip.bill.dao.entity.EFapiaoSyncBill;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

/**
 * e-fapiao: 同步数据Repository类
 *
 * <AUTHOR>
 * @date 2023-4-11
 */
public interface EFapiaoSyncRepository extends JpaAndQueryDslExecutor<EFapiaoSyncBill, Long> {

    @Transactional
    @Modifying(clearAutomatically = true)
    @Query(value = "update tb_kerry_bill_sync_e_fapiao set `invoice_upload_status` = :invoiceUploadStatus" +
            " where sales_bill_no = :salesBillNo order by `created_time` desc limit 1;", nativeQuery = true)
    void updateStateBySalesBillNo(@Param("salesBillNo") String salesBillNo
            , @Param("invoiceUploadStatus") int invoiceUploadStatus);

    EFapiaoSyncBill findTopBySalesBillNo(String salesBillNo);

}
