package com.kerryprops.kip.bill.dao;

import com.kerryprops.kip.bill.webservice.vo.req.CallBackKerryInvoiceMainVo;

import java.math.BigDecimal;

/**
 * e-fapiao：发票信息JDE回写-Dao层操作接口
 *
 * <AUTHOR>
 * @Date 2023-3-28
 */
public interface JdeEFapiaoBillWriteBackRepository {

    int insert(CallBackKerryInvoiceMainVo mainVo, String paperDrewDate
            , int jdeFormatDate, BigDecimal jdeAmountWithTax
            , BigDecimal jdeAmountWithoutTax, BigDecimal amountTax, String jdeStatus);

    int updateStatusFalse(CallBackKerryInvoiceMainVo mainVo);

}
