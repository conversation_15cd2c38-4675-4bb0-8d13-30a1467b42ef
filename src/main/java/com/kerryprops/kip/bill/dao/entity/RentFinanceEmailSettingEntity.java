package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 名   称：kerry_rent_finance_email_setting
 * 描   述：
 * 作   者：David <PERSON>
 * 时   间：2021/07/05 13:40:17
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Getter
@Setter
@Builder
@Entity
@Table(name = "kerry_rent_finance_email_setting")
@NoArgsConstructor
@AllArgsConstructor
public class RentFinanceEmailSettingEntity extends BaseEntity {

    /*楼盘id	len: 19*/
    @Column(name = "building_id")
    private String buildingId;

    /*大楼id	len: 19*/
    @Column(name = "house_id")
    private long houseId;

    /*邮箱集合	len: 255*/
    @Column(name = "email_collection")
    private String emailCollection;

    /*	len: 1*/
    @Column(name = "del_flag")
    private String delFlag;

    /*	len: 255*/
    @Column(name = "create_by")
    private String createBy;

    /*	len: 255*/
    @Column(name = "update_by")
    private String updateBy;


}
