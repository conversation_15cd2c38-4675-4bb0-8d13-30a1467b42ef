package com.kerryprops.kip.bill.dao.entity;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @ClassName: AptPaymentBill
 * @Description: KIP-2018
 * @Author: <PERSON>non
 * @Date: 2022/1/13 15:13
 * @Version: 2.0
 */
@Getter
@Setter
@Entity
@Table(name = "apt_payment_bill")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AptPaymentBill {

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "id")
    private int id;

    @Column(name = "payment_info_id")
    private String paymentInfoId;

    @Column(name = "bill_id")
    private Long billId;

    @Column(name = "deleted")
    private Integer deleted;


}
