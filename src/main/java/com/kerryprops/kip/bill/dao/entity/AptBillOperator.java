package com.kerryprops.kip.bill.dao.entity;

import lombok.Getter;

public enum AptBillOperator {

    UNKNOWN(1),
    ADD(2),
    AUTO_SYNC(3),
    MANUAL_SYNC(4)

    // direct debits
    ,
    DIRECT_DEBIT_ADD(5),
    DIRECT_DEBIT_DEL(6),
    DIRECT_DEBIT_DEL_BILLS(7),
    DIRECT_DEBIT_PUSH(8),
    DIRECT_DEBIT_CONDUCT(9),
    PUSH_ALL(10) // 全量推送
    ,
    PUSH_CONDITIONAL(11)  // 按条件推送
    ,
    PUSH_SELECTED(12) // 多选推送
    ;

    @Getter
    int index;

    AptBillOperator(int index) {
        this.index = index;
    }

    public static AptBillOperator fromIndex(int index) {
        for (AptBillOperator s : AptBillOperator.values()) {
            if (s.index == index) {
                return s;
            }
        }
        return UNKNOWN;
    }

}
