package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.enums.DirectDebitsBatchStatus;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.time.ZonedDateTime;

@Data
@Entity
@Table(name = "tb_apt_bill_direct_debits_batch")
public class AptBillDirectDebitsBatch {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "batch_no")
    private String batchNo;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "last_modified_by")
    private String lastModifiedBy;

    @Column(name = "status")
    private DirectDebitsBatchStatus status;

    @Column(name = "is_del")
    private Integer isDel;

    @Column(name = "psp_name")
    private String pspName;

    @Column(name = "project_id")
    private String projectId;

    @Column(name = "project_name")
    private String projectName;

    @Column(name = "bill_count")
    private Integer billCount;

    @Column(name = "payment_confirmed_count")
    private Integer paymentConfirmedCount;

    @Column(name = "bill_sent_count")
    private Integer billSentCount;

    @Column(name = "closing_month")
    private String closingMonth;

    @Column(name = "created_time", insertable = false, updatable = false, columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private ZonedDateTime createdTime;

    @Column(name = "updated_time", insertable = false, columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private ZonedDateTime updatedTime;

}
