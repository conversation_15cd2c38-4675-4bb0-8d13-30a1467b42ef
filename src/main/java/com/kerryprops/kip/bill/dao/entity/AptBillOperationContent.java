package com.kerryprops.kip.bill.dao.entity;

import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.io.Serializable;

@Data
@Entity
@Table(name = "tb_apt_bill_operation_content")
public class AptBillOperationContent implements Serializable {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @Basic
    @Column(name = "operation_id")
    private Long operationId;

    @Basic
    @Column(name = "field_type")
    private String fieldType;

    @Basic
    @Column(name = "field_name")
    private String fieldName;

    @Basic
    @Column(name = "field_alias")
    private String fieldAlias;

    @Basic
    @Column(name = "field_old_value")
    private String fieldOldValue;

    @Basic
    @Column(name = "field_new_value")
    private String fieldNewValue;

}
