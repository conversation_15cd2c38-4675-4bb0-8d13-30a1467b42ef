package com.kerryprops.kip.bill.dao.impl;

import com.kerryprops.kip.bill.config.EFapiaoConfig;
import com.kerryprops.kip.bill.config.SyncJdeConfig;
import com.kerryprops.kip.bill.dao.JdeEFapiaoBillOriginRepository;
import com.kerryprops.kip.bill.dao.entity.EFapiaoJDEBill;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * e-fapiao：发票信息JDE读取-Dao层操作实现类
 *
 * <AUTHOR>
 * @Date 2023-3-28
 */
@Slf4j
@Repository
public class JDBCJdeEFapiaoBillOriginRepository implements JdeEFapiaoBillOriginRepository {

    @Autowired
    EFapiaoConfig eFapiaoConfig;

    @Autowired
    @Qualifier("jdeJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    @Override
    public List<EFapiaoJDEBill> queryByCompanyCodeAndMonthAndBu(String companyCode, String bu, String month) {
        String selectSqlFormat = "SELECT * FROM %s a" +
                " WHERE a.TOALPH IS NOT NULL AND a.TOTXR1 IS NOT NULL AND a.TODL01 IS NOT NULL AND a.TOPCM IS NOT NULL" +
                " AND TOPCF = 1 AND a.TOCO = ? AND a.TOMCU = ? AND a.TOFX02 LIKE ? " +
                " AND NOT EXISTS" +
                " (SELECT * FROM %s b WHERE b.TOEV02 = 1 AND a.TOKCO = b.TOKCO AND a.TODCT = b.TODCT" +
                " AND a.TODOC = b.TODOC AND a.TOSFX = b.TOSFX)";

        String selectSql = String.format(selectSqlFormat, SyncJdeConfig.getJdeBillInvoiceOrigin()
                , SyncJdeConfig.getJdeBillInvoiceRead());
        log.info("sync_e_fapiao_bills_from_jde select_sql: [{}], company_code: [{}], bu: [{}], month: [{}]"
                , selectSql, companyCode, bu, month);

        return jdbcTemplate.query(selectSql, pstmt -> {
            int paramIndex = 1;
            pstmt.setString(paramIndex++, companyCode);
            pstmt.setString(paramIndex++, bu);
            pstmt.setString(paramIndex, "%" + month + "%");
        }, EFapiaoJDEBill.ROW_MAPPER);
    }

    @Override
    public String queryMaxDateByCompanyCodeAndBu(String companyCode, String bu) {
        String selectSqlFormat = "SELECT MAX(a.TOFX02) as max_date FROM %s a" +
                " WHERE a.TOALPH IS NOT NULL AND a.TOTXR1 IS NOT NULL AND a.TODL01 IS NOT NULL AND a.TOPCM IS NOT NULL" +
                " AND TOPCF = 1 AND a.TOCO ='%s' AND a.TOMCU ='%s' ";

        // 加空格，兼容Oracle填充字段
        bu = eFapiaoConfig.getBlanks() + bu;
        String selectSql = String.format(selectSqlFormat, SyncJdeConfig.getJdeBillInvoiceOrigin(), companyCode, bu);
        log.info("sync_e_fapiao_bills_from_jde select_sql: [{}], company_code: [{}], bu: [{}]"
                , selectSql, companyCode, bu);

        return String.valueOf(jdbcTemplate.queryForObject(selectSql, String.class));
    }

    @Override
    public EFapiaoJDEBill queryBySalesBillNo(String salesBillNo) {
        String selectSqlFormat = "SELECT * FROM %s WHERE TOKCO||TODCT||TODOC||TOSFX =? ";

        String selectSql = String.format(selectSqlFormat, SyncJdeConfig.getJdeBillInvoiceOrigin()
                , SyncJdeConfig.getJdeBillInvoiceRead());
        log.info("sync_e_fapiao_bills_from_jde select_sql: [{}], sales_bill_no: [{}]"
                , selectSql, salesBillNo);

        List<EFapiaoJDEBill> eFapiaoJdeBills = jdbcTemplate.query(selectSql, pstmt -> {
            pstmt.setString(1, salesBillNo);
        }, EFapiaoJDEBill.ROW_MAPPER);

        return CollectionUtils.isEmpty(eFapiaoJdeBills) ? null : eFapiaoJdeBills.get(0);
    }

}