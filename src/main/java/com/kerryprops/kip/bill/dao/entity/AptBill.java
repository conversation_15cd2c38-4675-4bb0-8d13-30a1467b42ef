package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.annotation.LogOperation;
import com.kerryprops.kip.bill.annotation.PaymentStatusLogOperationSerializer;
import com.kerryprops.kip.bill.annotation.PushStatusLogOperationSerializer;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillPushStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import com.kerryprops.kip.bill.dao.convert.PositionItemConverter;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.DynamicInsert;

import java.util.Date;

/**
 * 名   称：apt_bill
 * 描   述：
 * 作   者：David Wei
 * 时   间：2021/08/27 12:03:38
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Getter
@Setter
@Entity
@Table(name = "apt_bill")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true, exclude = {"amt"})
@DynamicInsert
public class AptBill extends BaseEntity implements HiveContextAware {

    /*	len: 32*/
    @Column(name = "bill_no")
    private String billNo;

    @Column(name = "category")
    private String category;

    /*	len: 32*/
    @Column(name = "bu")
    private String bu;

    /*	len: 32*/
    @Column(name = "unit")
    private String unit;

    /*	len: 32*/
    @Column(name = "an8")
    private String an8;

    @Column(name = "alph")
    private String alph;

    @Column(name = "doco")
    private String doco;

    /*	len: 19*/
    @Column(name = "begin_date")
    private Date beginDate;

    /*	len: 19*/
    @Column(name = "end_date")
    private Date endDate;

    @Column(name = "year")
    private Integer year;

    @Column(name = "month")
    private Integer month;

    @Column(name = "bill_month")
    private Integer billMonth;

    /*	len: 32*/
    @Column(name = "amt")
    @LogOperation(fieldAlias = "金额")
    private double amt;

    /*	len: 10*/
    @Enumerated(value = EnumType.STRING)
    @Column(name = "status")
    private BillStatus status;

    @Enumerated(value = EnumType.STRING)
    @Column(name = "payment_status")
    @LogOperation(fieldAlias = "状态", using = PaymentStatusLogOperationSerializer.class)
    private BillPaymentStatus paymentStatus;

    @Column(name = "payment_result")
    private String paymentResult;

    @Enumerated(value = EnumType.STRING)
    @Column(name = "push_status")
    @LogOperation(fieldAlias = "推送状态", using = PushStatusLogOperationSerializer.class)
    private BillPushStatus pushStatus;

    /*	len: 10*/
    @Column(name = "project_id")
    private String projectId;

    /*	len: 10*/
    @Column(name = "building_id")
    private String buildingId;

    /*	len: 10*/
    @Column(name = "floor_id")
    private String floorId;

    /*	len: 10*/
    @Column(name = "room_id")
    private String roomId;

    @Column(name = "rd_glc")
    private String rdGlc;

    @Convert(converter = PositionItemConverter.class)
    @Builder.Default
    @Column(name = "position_item", updatable = false)
    private PositionItemResponse positionItem = new PositionItemResponse();

    @Column(name = "pay_session")
    private String paySession;

    @Builder.Default
    @Column(name = "deleted_at")
    private Integer deletedAt = 0;

    @Column(name = "pay_time")
    private Date payTime;

    public String key() {
        return bu + "_" + unit;
    }

}
