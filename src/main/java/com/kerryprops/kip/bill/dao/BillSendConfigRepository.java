package com.kerryprops.kip.bill.dao;

import com.kerryprops.kip.bill.common.jpa.JpaAndQueryDslExecutor;
import com.kerryprops.kip.bill.dao.entity.BillSendConfig;

import java.util.List;

public interface BillSendConfigRepository extends JpaAndQueryDslExecutor<BillSendConfig, Long> {

    List<BillSendConfig> queryByDocoAndIsDel(String doco, int isDel);

    default List<BillSendConfig> queryActiveConfigsByDoco(String doco) {
        return this.queryByDocoAndIsDel(doco, 0);
    }

}
