package com.kerryprops.kip.bill.dao.convert;

import com.kerryprops.kip.bill.dao.entity.AptBillOperationStatus;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.springframework.stereotype.Component;

@Converter
@Component
public class OperationStatusConverter implements AttributeConverter<AptBillOperationStatus, Integer> {

    @Override
    public Integer convertToDatabaseColumn(AptBillOperationStatus status) {
        return status.getCode();
    }

    @Override
    public AptBillOperationStatus convertToEntityAttribute(Integer index) {
        return AptBillOperationStatus.fromCode(index);
    }

}
