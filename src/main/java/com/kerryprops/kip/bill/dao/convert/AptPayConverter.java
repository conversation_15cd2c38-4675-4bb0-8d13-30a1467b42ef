package com.kerryprops.kip.bill.dao.convert;

import com.kerryprops.kip.bill.common.enums.AptPayVerifyStatus;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.springframework.stereotype.Component;

/**
 * 公寓小区-收款记录，支付审核状态转换
 *
 * @auth <PERSON><PERSON><PERSON>
 * @Date 2023-2-27 12:03:08
 */
@Converter
@Component
public class AptPayConverter implements AttributeConverter<AptPayVerifyStatus, Integer> {

    @Override
    public Integer convertToDatabaseColumn(AptPayVerifyStatus status) {
        return status.getCode();
    }

    @Override
    public AptPayVerifyStatus convertToEntityAttribute(Integer code) {
        return AptPayVerifyStatus.fromIndex(code);
    }

}
