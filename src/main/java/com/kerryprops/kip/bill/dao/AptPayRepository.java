package com.kerryprops.kip.bill.dao;

import com.kerryprops.kip.bill.common.enums.InvoicedStatus;
import com.kerryprops.kip.bill.common.enums.PayCancelTypeEnum;
import com.kerryprops.kip.bill.common.jpa.JpaAndQueryDslExecutor;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.QAptPay;
import com.kerryprops.kip.bill.dao.entity.QAptPaymentInfo;
import com.kerryprops.kip.bill.service.model.s.AptPayBo;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;
import jakarta.persistence.QueryHint;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.stream.Stream;

import static org.hibernate.jpa.HibernateHints.HINT_FETCH_SIZE;

/***********************************************************************************************************************
 * Project - accelerator
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - David Wei
 * Created Date - 06/04/2021 10:39
 **********************************************************************************************************************/

public interface AptPayRepository extends JpaAndQueryDslExecutor<AptPay, Long> {

    AptPay findTopByPaymentInfoId(String paymentInfoId);

    @Transactional
    default void updateInvoicedStatus(String paymentId, InvoicedStatus invoicedStatus) {
        QAptPay t1 = QAptPay.aptPay;
        BooleanExpression expression;
        if (paymentId == null) {
            expression = t1.paymentInfoId.isNull();
        } else {
            expression = t1.paymentInfoId.eq(paymentId);
        }
        getJpaQueryFactory()
                .update(t1)
                .set(t1.invoicedStatus, invoicedStatus)
                .where(expression)
                .execute();
    }

    @Transactional(readOnly = true)
    @QueryHints(@QueryHint(name = HINT_FETCH_SIZE, value = Integer.MIN_VALUE + ""))
    Stream<AptPay> streamAllByInvoicedStatus(InvoicedStatus invoicedStatus);

    default Page<AptPay> findAptPays(AptPayBo bo, Pageable pageable) {
        var aptPayQuery = QAptPay.aptPay;
        var aptPaymentInfoQuery = QAptPaymentInfo.aptPaymentInfo;
        String[] excludeCancelTypes = {PayCancelTypeEnum.TIMEOUT_CANCELLED.name(), PayCancelTypeEnum.USER_CANCELLED.name()};

        BooleanBuilder where = new BooleanBuilder();
        where.and(bo.toPredicates());
        where.and(aptPaymentInfoQuery.cancelType.isNull().or(aptPaymentInfoQuery.cancelType.notIn(excludeCancelTypes)));
        JPAQuery<AptPay> query = getJpaQueryFactory().select(aptPayQuery)
                .from(aptPayQuery)
                .join(aptPaymentInfoQuery).on(aptPayQuery.paymentInfoId.eq(aptPaymentInfoQuery.id))
                .where(where);
        long count = query.fetchCount();
        if (count == 0) {
            return new PageImpl<>(Collections.emptyList(), pageable, count);
        }
        query.orderBy(aptPayQuery.createTime.desc(), aptPayQuery.updateTime.desc())
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize());
        return new PageImpl<>(query.fetch(), pageable, count);
    }

}
