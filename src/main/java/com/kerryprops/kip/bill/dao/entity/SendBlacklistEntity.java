package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 名   称：kerry_send_blacklist
 * 描   述：
 * 作   者：David <PERSON>
 * 时   间：2021/07/05 13:40:18
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Getter
@Setter
@Builder
@Entity
@Table(name = "kerry_send_blacklist")
@NoArgsConstructor
@AllArgsConstructor
public class SendBlacklistEntity extends BaseEntity {

    /*黑名单的信息地址	len: 128*/
    @Column(name = "address")
    private String address;

    /*类型  1为手机号码，2为邮件	len: 10*/
    @Column(name = "category")
    private Integer category;

    /*备注	len: 255*/
    @Column(name = "remark")
    private String remark;

    /*预留类型：后期可以进行区分哪些黑名单也发送	len: 10*/
    @Column(name = "type")
    private Integer type;


}
