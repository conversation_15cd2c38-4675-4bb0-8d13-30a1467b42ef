package com.kerryprops.kip.bill.dao;

import com.kerryprops.kip.bill.common.jpa.JpaAndQueryDslExecutor;
import com.kerryprops.kip.bill.dao.entity.AptPayBill;

import java.util.List;

/***********************************************************************************************************************
 * Project - accelerator
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - <PERSON>
 * Created Date - 06/04/2021 10:39
 **********************************************************************************************************************/

public interface AptPayBillRepository extends JpaAndQueryDslExecutor<AptPayBill, Long> {

    List<AptPayBill> findAllByPayId(long payId);

    List<AptPayBill> findAllByConfirmed(int confirmed);

    List<AptPayBill> findAllByBillId(long billId);

}
