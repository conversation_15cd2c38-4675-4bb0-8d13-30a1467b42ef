package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.enums.JobType;
import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * 名   称：apt_sync_jde_job
 * 描   述：
 * 作   者：David <PERSON>
 * 时   间：2021/09/06 10:39:38
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Getter
@Setter
@Entity
@Table(name = "apt_sync_jde_job")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AptSyncJdeJob extends BaseEntity {

    /*0 已创建 3 处理中 4处理完成 5 处理失败*/
    /*len: 10*/
    @Column(name = "status")
    private Integer status;

    @Column(name = "type")
    @Enumerated(value = EnumType.STRING)
    private JobType type;

    /**/
    /*len: 10*/
    @Column(name = "local_cnt")
    @Builder.Default
    private Integer localCnt = 0;

    @Column(name = "remote_cnt")
    @Builder.Default
    private Integer remoteCnt = 0;

    /**/
    /*len: 65535*/
    @Column(name = "error_msg")
    private String errorMsg;

    /**/
    /*len: 19*/
    @Column(name = "start_time")
    private Date startTime;

    /**/
    /*len: 19*/
    @Column(name = "end_time")
    private Date endTime;

    /**/
    /*len: 32*/
    @Column(name = "create_by")
    private String createBy;

}
