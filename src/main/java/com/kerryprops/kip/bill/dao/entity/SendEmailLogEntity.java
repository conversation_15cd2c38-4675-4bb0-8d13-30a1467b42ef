package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 名   称：kerry_send_email_log
 * 描   述：
 * 作   者：David <PERSON>
 * 时   间：2021/07/05 13:40:18
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Getter
@Setter
@Builder
@Entity
@Table(name = "kerry_send_email_log")
@NoArgsConstructor
@AllArgsConstructor
public class SendEmailLogEntity extends BaseEntity {

    /*外键ids	len: 512*/
    @Column(name = "fkey_ids")
    private String fkeyIds;

    /*外键类型 1通知表、2账单表、3没有表	len: 10*/
    @Column(name = "fkey_type")
    private Integer fkeyType;

    /*发送人	len: 19*/
    @Column(name = "send_user_id")
    private long sendUserId;

    /*批次号	len: 64*/
    @Column(name = "batch_code")
    private String batchCode;

    /*发送的通道	len: 10*/
    @Column(name = "send_channel")
    private Integer sendChannel;

    /*要发送的邮件主题	len: 255*/
    @Column(name = "send_subject")
    private String sendSubject;

    /*要发送的邮件的内容	len: 2147483647*/
    @Column(name = "send_content")
    private String sendContent;

    /*收件方的 邮箱，多个以逗号分隔	len: 65535*/
    @Column(name = "send_emails")
    private String sendEmails;

    /*发送状态 0未发送，5已发送	len: 10*/
    @Column(name = "send_status")
    private Integer sendStatus;

    /*解析过后，更改为已删除	len: 2*/
    @Column(name = "del_flag")
    private String delFlag;

    /*邮件是否包含附件	len: 3*/
    @Column(name = "attachment_flag")
    private byte attachmentFlag;

    /*	len: 65535*/
    @Column(name = "send_result")
    private String sendResult;


}
