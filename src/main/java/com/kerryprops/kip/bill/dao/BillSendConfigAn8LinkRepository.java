package com.kerryprops.kip.bill.dao;

import com.kerryprops.kip.bill.common.jpa.JpaAndQueryDslExecutor;
import com.kerryprops.kip.bill.dao.entity.BillSendConfigAn8Link;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface BillSendConfigAn8LinkRepository extends JpaAndQueryDslExecutor<BillSendConfigAn8Link, Long> {

    @Query(value = "select * from kerry_bill_send_config_an8_link where config_id = :configId", nativeQuery = true)
    List<BillSendConfigAn8Link> selectAn8Links(@Param("configId") Long configId);

    @Query(value = "select * from kerry_bill_send_config_an8_link WHERE config_id in (:configIds) and is_del = 0", nativeQuery = true)
    List<BillSendConfigAn8Link> selectActiveAn8Links(@Param("configIds") List<Long> configIds);

}
