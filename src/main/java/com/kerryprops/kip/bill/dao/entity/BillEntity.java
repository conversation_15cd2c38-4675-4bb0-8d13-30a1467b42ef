package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 名   称：kerry_bill
 * 描   述：
 * 作   者：David <PERSON>
 * 时   间：2021/07/02 09:53:17
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Getter
@Setter
@Builder
@Entity
@Table(name = "kerry_bill")
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
public class BillEntity extends BaseEntity {

    /*账单类型	len: 50*/
    @Column(name = "tp_dct")
    private String tpDct;

    /*账单中文描述	len: 255*/
    @Column(name = "tp_dl01")
    private String tpDl01;

    /*账单文件的链接地址	len: 255*/
    @Column(name = "file_url")
    private String fileUrl;

    /*账单文件名	len: 255*/
    @Column(name = "tp_gtfilenm")
    private String tpGtfilenm;

    /*公司编号	len: 64*/
    @Column(name = "tp_co")
    private String tpCo;

    /*公司名称	len: 255*/
    @Column(name = "tp_dl03")
    private String tpDl03;

    /*用户编号	len: 32*/
    @Column(name = "tp_an8")
    private String tpAn8;

    /*用户名称	len: 255*/
    @Column(name = "tp_alph")
    private String tpAlph;

    /*jde合同号	len: 32*/
    @Column(name = "tp_doco")
    private String tpDoco;

    /*建筑物编号	len: 100*/
    @Column(name = "tp_mcu")
    private String tpMcu;

    /*建筑物描述	len: 200*/
    @Column(name = "tp_dc")
    private String tpDc;

    /*账单生成日期	len: 19*/
    @Column(name = "tp_crtutime")
    private Date tpCrtutime;

    /*账单状态 0未发送、5发送成功、10发送失败	len: 10*/
    @Column(name = "tp_status")
    private Integer tpStatus;

    /*账单年	len: 10*/
    @Column(name = "tp_fyr")
    private Integer tpFyr;

    /*账单月	len: 10*/
    @Column(name = "tp_pn")
    private Integer tpPn;

    @Column(name = "bill_month")
    private Integer billMonth;

    /*网站显示 Y为显示	len: 2*/
    @Column(name = "tp_ev01")
    private String tpEv01;

    /*账单来源	len: 4*/
    @Column(name = "tp_ev02")
    private String tpEv02;

    /*JDE同步的回写标志位	len: 4*/
    @Column(name = "tp_ev03")
    private String tpEv03;

    /*删除标志	len: 1*/
    @Builder.Default
    @Column(name = "del_flag")
    private String delFlag = "0";

    @Column(name = "delete_time")
    private Date deleteTime;

    @Column(name = "delete_by")
    private String deleteBy;

    /*账单打印时间	len: 20*/
    @Column(name = "format_date")
    private String formatDate;

    /*账单文件名称	len: 200*/
    @Column(name = "tp_gtitnm")
    private String tpGtitnm;

    /*账单单元	len: 200*/
    @Column(name = "tp_unit")
    private String tpUnit;

    /*更新人	len: 64*/
    @Column(name = "update_by")
    private String updateBy;

    /*账单站内信发送状态	len: 10*/
    @Column(name = "mail_status")
    private Integer mailStatus;

    /*账单邮件发送状态	len: 10*/
    @Column(name = "email_status")
    private Integer emailStatus;

    /*账单站内信发送时间	len: 19*/
    @Column(name = "mail_date")
    private Date mailDate;

    /*账单邮件发送时间	len: 19*/
    @Column(name = "email_date")
    private Date emailDate;

    /*账单的邮件解析结果	len: 65535*/
    @Column(name = "email_err")
    private String emailErr;

    /*站内信最早阅读时间	len: 19*/
    @Column(name = "mail_read_time")
    private Date mailReadTime;

    /*手机端最早阅读时间	len: 19*/
    @Column(name = "mobile_read_time")
    private Date mobileReadTime;

    /*阅读状态 0 未读、1已读	len: 10*/
    @Column(name = "read_status")
    private Integer readStatus;

    @Column(name = "source_id")
    private Integer sourceId;

    @Column(name = "ext_1")
    private Integer ext1;

}
