package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.enums.InvoiceIssueStatus;
import com.kerryprops.kip.bill.dao.convert.InvoiceUploadStatusConverter;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * kerry bill：e-fapiao发票：JDE账单同步表
 *
 * <AUTHOR>
 * @Date 2023-4-11
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "tb_kerry_bill_sync_e_fapiao")
public class EFapiaoSyncBill implements Serializable {

    private static final long serialVersionUID = 1L;

    /* 数电，行业特殊票种类型,房屋产权证书/不动产权证号码 */
    @Column(name = "real_estate_no")
    String realEstateNo = StringUtils.EMPTY;

    /* 数电，行业特殊票种类型,不动产地址，省市区，如：辽宁省沈阳市沈河区 */
    @Column(name = "real_estate_place")
    String realEstatePlace = StringUtils.EMPTY;

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /* 单据公司 */
    @Column(name = "kco")
    private String kco = StringUtils.EMPTY;

    /* 公司 */
    @Column(name = "company_code")
    private String companyCode = StringUtils.EMPTY;

    /* 单据号，示例：19004780 */
    @Column(name = "doc")
    private int doc;

    /* 单据付款项｜示例：001 */
    @Column(name = "payment_item")
    private String paymentItem = StringUtils.EMPTY;

    /* an8| 示例：10023780 */
    @Column(name = "an8")
    private String an8 = StringUtils.EMPTY;

    /* 购方名称｜xxx有限公司 */
    @Column(name = "purchaser_name")
    private String purchaserName = StringUtils.EMPTY;

    /* 购方税号｜示例：91110105625911314P */
    @Column(name = "purchaser_tax_no")
    private String purchaserTaxNo = StringUtils.EMPTY;

    /* 购方公司地址 */
    @Column(name = "purchaser_address")
    private String purchaserAddress = StringUtils.EMPTY;

    /* 购方银行名称｜花旗银行北京分行********** */
    @Column(name = "purchaser_bank_name")
    private String purchaserBankName = StringUtils.EMPTY;

    /* 发票类型, gvat-纸质普票，gvate-电子普票，svat-专票，svate-电子专票
              ，gvatq-数电普通发票，svatq-数电专用发票，gvatz-数电纸质普通发票，svatz-数电纸质专用发票 */
    @Column(name = "invoice_type")
    private String invoiceType = StringUtils.EMPTY;

    /* 邮寄地址 */
    @Column(name = "mailing_address")
    private String mailingAddress = StringUtils.EMPTY;

    /* mcu，经营单位，Business Unit */
    @Column(name = "mcu")
    private String mcu = StringUtils.EMPTY;

    /* 账单类型｜示例：RN */
    @Column(name = "bill_type")
    private String billType = StringUtils.EMPTY;

    /* JDE合同号｜示例：250644 */
    @Column(name = "doco")
    private String doco = StringUtils.EMPTY;

    /* 缴费票据码｜示例：U02M */
    @Column(name = "bill_code")
    private String billCode = StringUtils.EMPTY;

    /* 商品货物税收分类编码 */
    @Column(name = "goods_tax_no")
    private String goodsTaxNo = StringUtils.EMPTY;

    /* 货物或应税劳务名称｜示例：电费 */
    @Column(name = "item_name")
    private String itemName = StringUtils.EMPTY;

    /* 货物规格型号 */
    @Column(name = "item_spec")
    private String itemSpec = StringUtils.EMPTY;

    /* 不含税金额, 单位（分）｜示例：10005 */
    @Column(name = "amount_without_tax")
    private BigDecimal amountWithoutTax;

    /* 税率 示例：6000 */
    @Column(name = "tax_rate")
    private BigDecimal taxRate;

    /* 税额, 单位（分）｜示例：830 */
    @Column(name = "tax_amount")
    private BigDecimal taxAmount;

    /* 含税金额, 单位（分） */
    @Column(name = "amount_with_tax")
    private BigDecimal amountWithTax;

    /* 是否税收优惠 */
    @Column(name = "tax_discount")
    private String taxDiscount = StringUtils.EMPTY;

    /* 税收优惠描述 */
    @Column(name = "tax_discount_desc")
    private String taxDiscountDesc = StringUtils.EMPTY;

    /* 税收分类说明， 示例：其他不动产经营租赁，企业管理服务 */
    @Column(name = "tax_classify_desc")
    private String taxClassifyDesc = StringUtils.EMPTY;

    /* JDE单元号 */
    @Column(name = "jde_unit")
    private String jdeUnit = StringUtils.EMPTY;

    /* 订单号：TOKCO&TODCT&TODOC&TOSFX */
    @Column(name = "sales_bill_no")
    private String salesBillNo = StringUtils.EMPTY;

    /* 开票请求状态 */
    @Column(name = "invoice_upload_status")
    @Convert(converter = InvoiceUploadStatusConverter.class)
    private InvoiceIssueStatus invoiceIssueStatus = InvoiceIssueStatus.UPLOADING;

    @Column(name = "created_time", insertable = false, updatable = false, columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private ZonedDateTime createdTime;

    @Column(name = "updated_time", insertable = false, columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private ZonedDateTime updatedTime;

}

