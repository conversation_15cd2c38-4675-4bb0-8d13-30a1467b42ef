package com.kerryprops.kip.bill.dao;


import com.kerryprops.kip.bill.common.enums.InvoicedStatus;
import com.kerryprops.kip.bill.common.jpa.JpaAndQueryDslExecutor;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.dao.entity.AptPaymentTransInfo;
import com.kerryprops.kip.bill.dao.entity.QAptPaymentInfo;
import jakarta.persistence.QueryHint;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.stream.Stream;

import static org.hibernate.jpa.HibernateHints.HINT_FETCH_SIZE;


public interface AptPaymentInfoRepository extends JpaAndQueryDslExecutor<AptPaymentInfo, String> {

    @Query(value = "select new com.kerryprops.kip.bill.dao.entity" +
            ".AptPaymentTransInfo(t.id, t.paymentTransNo, t.pspTransNo, t.failedReason, t.agreementNo, t.paymentStatus) " +
            "from AptPaymentInfo t where t.id in (?1) ")
    List<AptPaymentTransInfo> findTransInfoByIdIn(Collection<String> ids);

    @Query(value = "select * from apt_payment_info where room_id = ?3 " +
            "and (bind_user_id = ?1 or user_profile_id = ?2)  and amt > 0 order by create_time desc", nativeQuery = true)
    List<AptPaymentInfo> findAllByUserAndRoom(Long userId, String cid, String roomId);

    AptPaymentInfo findTopById(String id);

    @Transactional(readOnly = true)
    @QueryHints(@QueryHint(name = HINT_FETCH_SIZE, value = Integer.MIN_VALUE + ""))
    Stream<AptPaymentInfo> streamAllByInvoicedStatus(InvoicedStatus invoicedStatus);

    @Transactional
    default void updateInvoicedStatus(String paymentId, InvoicedStatus invoicedStatus) {
        if (paymentId == null) {
            return;
        }
        QAptPaymentInfo t1 = QAptPaymentInfo.aptPaymentInfo;
        getJpaQueryFactory()
                .update(t1)
                .set(t1.invoicedStatus, invoicedStatus)
                .where(t1.id.eq(paymentId))
                .execute();
    }

}
