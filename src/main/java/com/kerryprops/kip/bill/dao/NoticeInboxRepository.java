package com.kerryprops.kip.bill.dao;

import com.kerryprops.kip.bill.common.jpa.JpaAndQueryDslExecutor;
import com.kerryprops.kip.bill.dao.entity.NoticeInboxEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/***********************************************************************************************************************
 * Project - accelerator
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - <PERSON>
 * Created Date - 06/04/2021 10:39
 **********************************************************************************************************************/

public interface NoticeInboxRepository extends JpaAndQueryDslExecutor<NoticeInboxEntity, Long> {

    /**
     * 查找当前账单 站内信 的第一条记录  按照是否已读排序
     *
     * @param billId
     * @return
     */
    @Query(value = "select * from kerry_notice_inbox where bill_id = :billId ORDER BY read_flag DESC limit 1", nativeQuery = true)
    NoticeInboxEntity selectReadedBill(@Param("billId") Long billId);

}
