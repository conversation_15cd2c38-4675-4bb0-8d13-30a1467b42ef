package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * 名   称：kerry_notice_outbox
 * 描   述：
 * 作   者：<PERSON>
 * 时   间：2021/07/05 13:40:16
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Getter
@Setter
@Entity
@Table(name = "kerry_notice_outbox")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NoticeOutboxEntity extends BaseEntity {

    /*发送人id	len: 19*/
    @Column(name = "sender_user")
    private Long senderUser;

    /*发送主体（楼盘、大楼、公司）	len: 19*/
    @Column(name = "sender_subject")
    private Long senderSubject;

    /*发送主体类型1楼盘、2大楼、3公司	len: 10*/
    @Column(name = "subject_type")
    private Integer subjectType;

    /*通知通道 1站内信、2站内信/短信、3站内信/邮件、4站内信/短信/邮件	len: 10*/
    @Column(name = "notice_channel")
    private Integer noticeChannel;

    /*标题	len: 255*/
    @Column(name = "title")
    private String title;

    /*站内信邮件内容	len: 2147483647*/
    @Column(name = "mail_content")
    private String mailContent;

    /*短信内容	len: 512*/
    @Column(name = "sms_content")
    private String smsContent;

    /*通知类型0公告、1通知	len: 10*/
    @Column(name = "notice_type")
    private Integer noticeType;

    /*所属功能 BusinessDefinition中取值	len: 10*/
    @Column(name = "fun_type")
    private Integer funType;

    /*上架时间	len: 19*/
    @Column(name = "up_shelf_time")
    private Date upShelfTime;

    /*下架时间	len: 19*/
    @Column(name = "down_shelf_time")
    private Date downShelfTime;

    /*下架类型 0 未下架，1已下架	len: 10*/
    @Column(name = "shelf_type")
    private Integer shelfType;

    /*是否主动发出 1是,0否	len: 3*/
    @Column(name = "self_flag")
    private Boolean selfFlag;

    /*是否为时效性 0否,1是	len: 3*/
    @Column(name = "aging_flag")
    private Boolean agingFlag;

    /*是否发送给员工	len: 3*/
    @Column(name = "sender_emp_flag")
    private Boolean senderEmpFlag;

    /*发送方类型 1物业、2用户	len: 10*/
    @Column(name = "sender_type")
    private Integer senderType;

    /*删除标志	len: 2*/
    @Column(name = "del_flag")
    private String delFlag;

    /*	len: 100*/
    @Column(name = "create_by")
    private String createBy;

    /*	len: 100*/
    @Column(name = "update_by")
    private String updateBy;

    /*合同号	len: 20*/
    @Column(name = "doco")
    private String doco;

    /*用户编号	len: 20*/
    @Column(name = "an8")
    private String an8;

    /*发送账单时的mcu	len: 20*/
    @Column(name = "mcu")
    private String mcu;

    /*账单id	len: 19*/
    @Column(name = "bill_id")
    private Long billId;

    /*发件箱附件	len: 1024*/
    @Column(name = "file_url")
    private String fileUrl;

    /*	len: 19*/
    @Column(name = "temp_id")
    private Long tempId;

    /*设置定时通知是否发送 0 未发送、1已发送	len: 2*/
    @Column(name = "send_flag")
    private String sendFlag;

    /*设定的定时发送的时间	len: 19*/
    @Column(name = "send_date")
    private Date sendDate;

    /*默认为0 非草稿状态，1为草稿状态	len: 2*/
    @Column(name = "draft_flag")
    private String draftFlag;

    /*适用全部的用户角色值放0	len: 100*/
    @Column(name = "role_ids")
    private String roleIds;


}
