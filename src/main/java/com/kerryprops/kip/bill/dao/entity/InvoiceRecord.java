package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.enums.InvoiceCustomerType;
import com.kerryprops.kip.bill.common.enums.InvoiceRecordStatus;
import com.kerryprops.kip.bill.common.enums.OrderType;
import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.logging.log4j.util.Strings;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.kerryprops.kip.bill.common.enums.InvoiceRecordStatus.PROCESSING;

@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "invoice_record")
@Entity
public class InvoiceRecord extends BaseEntity {

    @Column(name = "invoice_no")
    private String invoiceNo;

    @Enumerated(EnumType.STRING)
    private InvoiceCustomerType customerType;

    /**
     * 发票编号
     */
    @Column(name = "invoice_code")
    private String invoiceCode;

    /**
     * 发票类型
     */
    @Column(name = "invoice_type")
    private String invoiceType;

    @Column(name = "customer_name")
    private String customerName;

    /**
     * 销方名称
     */
    @Column(name = "seller_name")
    private String sellerName;

    @Column(name = "tax_no")
    private String taxNo;

    @Column(name = "address")
    private String address;

    @Column(name = "area_code")
    private String areaCode;

    @Column(name = "phone")
    private String phone;

    @Column(name = "bank_name")
    private String bankName;

    @Column(name = "bank_account")
    private String bankAccount;

    @Column(name = "email")
    private String email;

    @Column(name = "order_no")
    private String orderNo;

    @Enumerated(EnumType.STRING)
    private OrderType orderType;

    @Column(name = "user_id")
    private String userId;

    @Builder.Default
    private LocalDateTime applyTime = LocalDateTime.now();

    @Builder.Default
    @Enumerated(EnumType.STRING)
    private InvoiceRecordStatus status = PROCESSING;

    @Builder.Default
    private Boolean isInvoiceInvoked = Boolean.FALSE;

    @Column(name = "amount")
    private BigDecimal amount;

    @Column(name = "tax_rate")
    private BigDecimal taxRate;

    @Column(name = "tax_amount")
    private BigDecimal taxAmount;

    @Column(name = "total_amount")
    private BigDecimal totalAmount;

    @Column(name = "total_amount_after_split")
    private String totalAmountAfterSplit = "";

    @Column(name = "invoice_time")
    private LocalDateTime invoiceTime;

    @Column(name = "payee")
    private String payee;

    @Column(name = "checker")
    private String checker;

    @Column(name = "issuer")
    private String issuer;

    @Column(name = "pdf_url")
    private String pdfUrl;

    // 数电xml地址，
    @Column(name = "xml_url")
    private String xmlUrl = Strings.EMPTY;

    @Column(name = "ofd_url")
    private String ofdUrl = Strings.EMPTY;

    /// 【Kerry+暂不关注红票回调】
    // 数电：红冲场景回调字段：开具原因
    // @Column(name = "making_reason")
    // private String makingReason;

    @Column(name = "error_message")
    private String errorMessage;

//    @Column(name = "state")
//    private InvoiceState state;

}