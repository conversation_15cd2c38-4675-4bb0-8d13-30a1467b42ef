package com.kerryprops.kip.bill.dao;

import com.kerryprops.kip.bill.dao.entity.EFapiaoJDEBill;

import java.util.List;

/**
 * e-fapiao：发票信息JDE读取-Dao层操作接口
 *
 * <AUTHOR>
 * @Date 2023-3-28
 */
public interface JdeEFapiaoBillOriginRepository {

    List<EFapiaoJDEBill> queryByCompanyCodeAndMonthAndBu(String companyCode, String bu, String month);

    String queryMaxDateByCompanyCodeAndBu(String companyCode, String bu);

    EFapiaoJDEBill queryBySalesBillNo(String salesBillNo);

}
