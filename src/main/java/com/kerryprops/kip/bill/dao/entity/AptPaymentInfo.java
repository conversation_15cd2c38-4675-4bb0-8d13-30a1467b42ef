package com.kerryprops.kip.bill.dao.entity;


import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import com.kerryprops.kip.bill.common.enums.BillPayModule;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.InvoicedStatus;
import com.kerryprops.kip.bill.common.enums.PaymentCateEnum;
import com.kerryprops.kip.bill.common.enums.PaymentPayType;
import com.kerryprops.kip.bill.dao.convert.PositionItemConverter;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.logging.log4j.util.Strings;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 远程支付记录，存在未支付的收款记录
 * KIP-2018
 *
 * <AUTHOR> 2022/1/13 15:13
 */
@Getter
@Setter
@Entity
@Table(name = "apt_payment_info")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@DynamicInsert
public class AptPaymentInfo {

    @Id
    @Column(name = "id")
    private String id;

    @Basic
    @Column(name = "amt")
    private Double amt;

    @Column(name = "cancel_type")
    private String cancelType;

    @Column(name = "project_id")
    private String projectId;

    @Column(name = "building_id")
    private String buildingId;

    @Enumerated(value = EnumType.STRING)
    @Column(name = "payment_status")
    private BillPaymentStatus paymentStatus;

    @Convert(converter = PositionItemConverter.class)
    @Builder.Default
    private PositionItemResponse positionItem = new PositionItemResponse();

    @Column(name = "bind_user_id")
    private Long bindUserId;

    @Column(name = "pay_session")
    private String paySession;

    @Column(name = "psp_trans_no")
    private String pspTransNo;

    @Column(name = "payment_trans_no")
    private String paymentTransNo;

    /**
     * 后端处理逻辑： -1 - 取消不能开票，0 - 初始值，1 - 未开票，2 - 开票成功，3 - 开票失败
     * 前端处理逻辑： 1 - 申请开票，2 或者 3 - 查看发票
     */
    @Column(name = "applied_invoice")
    private Integer appliedInvoice = 0;

    @CreationTimestamp
    @Column(name = "create_time")
    private Date createTime;

    @UpdateTimestamp
    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "deleted")
    private Integer deleted;

    /*	len: 10*/
    @Column(name = "floor_id")
    private String floorId;

    /*	len: 10*/
    @Column(name = "room_id")
    private String roomId;

    @Column(name = "payment_time")
    private Date paymentTime;

    @Enumerated(value = EnumType.STRING)
    @Column(name = "pay_type")
    private PaymentPayType payType;

    @Column(name = "invoice_url")
    private String invoiceUrl;

    // 数电发票xml地址
    @Column(name = "xml_url")
    private String xmlUrl = Strings.EMPTY;

    // 发票ofd地址
    @Column(name = "ofd_url")
    private String ofdUrl = Strings.EMPTY;

    @Column(name = "failed_reason")
    private String failedReason;

    @Column(name = "agreement_no")
    private String agreementNo;

    @Column(name = "user_profile_id")
    private String userProfileId;

    @Column(name = "description")
    private String description;

    @Column(name = "advance_amount")
    private BigDecimal advanceAmount;

    @Enumerated(value = EnumType.STRING)
    @Column(name = "payment_cate")
    private PaymentCateEnum paymentCate;

    @Enumerated(value = EnumType.STRING)
    @Column(name = "bill_pay_module")
    private BillPayModule billPayModule;

    @Column(name = "fee_id")
    private Long feeId;

    @Column(name = "fee_name")
    private String feeName;

    @Column(name = "fee_tax")
    private BigDecimal feeTax;

    @Column(name = "fee_tax_amount")
    private BigDecimal feeTaxAmount;

    @Column(name = "pay_act")
    private String payAct;

    @Column(name = "create_by")
    private String createBy;

    @Column(name = "pay_channel")
    @Enumerated(value = EnumType.STRING)
    private BillPayChannel payChannel;

    @Column(name = "bu")
    private String bu;

    @Column(name = "unit")
    private String unit;

    @Column(name = "an8")
    private String an8;

    @Column(name = "alph")
    private String alph;

    @Column(name = "doco")
    private String doco;

    /**
     * 对应apt_pay中的pay_type
     */
    @Column(name = "pay_type_info")
    private String payTypeInfo;

    @Builder.Default
    @Column(name = "invoiced_status")
    @Enumerated(value = EnumType.STRING)
    private InvoicedStatus invoicedStatus = InvoicedStatus.NO_INVOICED;

}
