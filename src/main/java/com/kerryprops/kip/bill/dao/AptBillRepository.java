package com.kerryprops.kip.bill.dao;

import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillPushStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.common.jpa.JpaAndQueryDslExecutor;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.QAptBill;
import com.kerryprops.kip.bill.utils.BillUtil;
import com.kerryprops.kip.bill.webservice.vo.resp.RoomAn8RespVo;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.Nullable;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/***********************************************************************************************************************
 * Project - accelerator
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - David Wei
 * Created Date - 06/04/2021 10:39
 **********************************************************************************************************************/

public interface AptBillRepository extends JpaAndQueryDslExecutor<AptBill, Long> {

    List<AptBill> findByIdIn(Collection<Long> ids);

    @Query(value = "select distinct rd_alph from apt_sync_bills where rd_mcu =: bu and rd_unit := unit group by rd_mcu, rd_unit, rd_alph", nativeQuery = true)
    List<String> roomAn8(@Param("bu") String bu, @Param("unit") String unit);

    @Query(value = "select b.* from apt_payment_bill p left join apt_bill b on p.bill_id = b.id where p.payment_info_id = :aptApyMengId ", nativeQuery = true)
    List<AptBill> queryAptBillByPaymentId(@Param("aptApyMengId") String aptApyMengId);

    List<AptBill> findAllByBillNo(String billNo);

    @Query(value = "select b.alph from apt_bill b " +
            " where b.deleted_at=0 " +
            " and if(:projectId is not null and :projectId !='', b.project_id=:projectId, 1 = 1 ) " +
            " and if(:isMaxScopeNotNull, b.building_id in (:maxBindingScope), 1 = 1 ) " +
            " and if(:roomId is not null and :roomId !='', b.room_id = (:roomId), 1 = 1 ) " +
            " group by b.alph " +
            " order by b.alph asc", nativeQuery = true)
    List<String> queryAlphs(@Param("isMaxScopeNotNull") boolean isMaxScopeNotNull,
                            @Param("maxBindingScope") List<String> maxBindingScope,
                            @Param("projectId") String projectId,
                            @Param("roomId") String roomId);

    @Query(value = "select b.category from apt_bill b " +
            " where b.deleted_at=0 " +
            " and if(:projectId is not null and :projectId !='', b.project_id=:projectId, 1 = 1 ) " +
            " and if(:isMaxScopeNotNull, b.building_id in (:maxBindingScope), 1 = 1 ) " +
            " and if(:roomId is not null and :roomId !='', b.room_id = (:roomId), 1 = 1 ) " +
            " group by b.category " +
            " order by b.category asc", nativeQuery = true)
    List<String> queryCategorys(@Param("isMaxScopeNotNull") boolean isMaxScopeNotNull,
                                @Param("maxBindingScope") List<String> maxBindingScope,
                                @Param("projectId") String projectId,
                                @Param("roomId") String roomId);

    @Transactional
    default void updateBillStatus4cashier(List<Long> ids) {
        JPAQueryFactory query = getJpaQueryFactory();
        QAptBill bill = QAptBill.aptBill;

        query.update(bill)
                .set(bill.status, BillStatus.CASHIER_PAID)
                .set(bill.paymentResult, BillStatus.CASHIER_PAID.getInfo())
                .set(bill.paymentStatus, BillPaymentStatus.PAID)
                .where(bill.id.in(ids))
                .execute();
    }

    @Transactional
    default boolean updateBillStatus4Push(Long id, BillPushStatus billPushStatus
            , @Nullable BillStatus billStatus) {

        QAptBill t1 = QAptBill.aptBill;
        var query = getJpaQueryFactory();
        var updateClause = query.update(t1);
        if (Objects.nonNull(billStatus)) {
            updateClause.set(t1.status, billStatus);
        }
        return updateClause.set(t1.pushStatus, billPushStatus)
                .where(t1.id.eq(id))
                .execute() > 0;
    }

    default Optional<AptBill> findLastBillByRoomId(String roomId) {
        JPAQueryFactory query = getJpaQueryFactory();
        QAptBill t1 = QAptBill.aptBill;

        AptBill vo = query.selectFrom(t1)
                .where(t1.roomId.eq(roomId))
                .orderBy(t1.updateTime.desc())
                .limit(1)
                .fetchOne();
        return Optional.ofNullable(vo);
    }

    default Optional<RoomAn8RespVo> findLastRoomAn8(String roomId) {
        JPAQueryFactory query = getJpaQueryFactory();
        QAptBill t1 = QAptBill.aptBill;

        Tuple vo = query.select(t1.bu, t1.unit, t1.doco, t1.an8, t1.alph)
                .from(t1)
                .where(t1.roomId.eq(roomId).and(t1.an8.gt(StringUtils.EMPTY)))
                .orderBy(t1.createTime.desc())
                .limit(1)
                .fetchOne();
        return Optional.ofNullable(vo)
                .map(v -> {
                    RoomAn8RespVo vo1 = new RoomAn8RespVo();
                    vo1.setBu(v.get(t1.bu));
                    vo1.setUnit(v.get(t1.unit));
                    vo1.setAn8(v.get(t1.an8));
                    vo1.setAlph(v.get(t1.alph));
                    vo1.setDoco(v.get(t1.doco));
                    return vo1;
                });
    }

    /**
     * 查找未支付账单的时间段.
     *
     * @param projectId  项目标识符
     * @param buildingId 建筑标识符
     * @param roomId     单元标识符
     * @param size       要返回的记录数
     * @return 包含未支付账单持续时间的列表[202201、202202...]
     */
    default List<Integer> findUnpaidBillDurations(String projectId, String buildingId, String roomId, int size) {
        QAptBill aptBillQuery = QAptBill.aptBill;
        BillPaymentStatus[] unpaidStatus = {BillPaymentStatus.TO_BE_PAID, BillPaymentStatus.PART_PAID};
        return getJpaQueryFactory().select(aptBillQuery.year, aptBillQuery.month).from(aptBillQuery)
                .where(aptBillQuery.projectId.eq(projectId)
                        .and(aptBillQuery.buildingId.eq(buildingId))
                        .and(aptBillQuery.roomId.eq(roomId))
                        .and(aptBillQuery.paymentStatus.in(unpaidStatus)))
                .orderBy(aptBillQuery.year.asc(), aptBillQuery.month.asc()).limit(size).fetch()
                .stream()
                .map(v -> BillUtil.getBillYearMonth(v.get(aptBillQuery.year), v.get(aptBillQuery.month)))
                .sorted()
                .toList();
    }

    /**
     * 计算指定单元未支付账单的数量.
     *
     * @param projectId  项目ID.
     * @param buildingId 楼栋ID.
     * @param roomId     单元ID.
     * @return 未支付账单的数量.
     */
    default long countUnpaidBills(String projectId, String buildingId, String roomId) {
        QAptBill aptBillQuery = QAptBill.aptBill;
        BillPaymentStatus[] unpaidStatus = {BillPaymentStatus.TO_BE_PAID, BillPaymentStatus.PART_PAID};
        return count(aptBillQuery.projectId.eq(projectId)
                .and(aptBillQuery.buildingId.eq(buildingId))
                .and(aptBillQuery.roomId.eq(roomId))
                .and(aptBillQuery.paymentStatus.in(unpaidStatus)));
    }

}
