package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 名   称：kerry_message_template
 * 描   述：
 * 作   者：David <PERSON>
 * 时   间：2021/07/05 13:40:15
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Getter
@Setter
@Builder
@Entity
@Table(name = "kerry_message_template")
@NoArgsConstructor
@AllArgsConstructor
public class MessageTemplateEntity extends BaseEntity {

    /*发送邮件的主题	len: 100*/
    @Column(name = "temp_subject")
    private String tempSubject;

    /*模板名称	len: 255*/
    @Column(name = "temp_name")
    private String tempName;

    /*	len: 65535*/
    @Column(name = "head_content")
    private String headContent;

    /*需要进行循环替换的内容	len: 65535*/
    @Column(name = "foreach_content")
    private String foreachContent;

    /*	len: 65535*/
    @Column(name = "tail_content")
    private String tailContent;

    /*0为UMail商务通道、1为嘉里通道	len: 10*/
    @Column(name = "send_channel")
    private Integer sendChannel;

    /*	len: 100*/
    @Column(name = "create_by")
    private String createBy;

    /*	len: 100*/
    @Column(name = "update_by")
    private String updateBy;

    /*删除标志（0代表存在 2代表删除）	len: 1*/
    @Column(name = "del_flag")
    private String delFlag;


}
