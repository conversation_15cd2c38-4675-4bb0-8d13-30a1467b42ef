package com.kerryprops.kip.bill.dao;

import com.kerryprops.kip.bill.dao.entity.AptBillOperationContent;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Collection;
import java.util.List;

public interface AptBillOperationContentRepository extends JpaRepository<AptBillOperationContent, Long> {

    List<AptBillOperationContent> findAllByOperationId(long operationId);

    List<AptBillOperationContent> findAllByOperationIdIn(Collection<Long> operationIds);

}
