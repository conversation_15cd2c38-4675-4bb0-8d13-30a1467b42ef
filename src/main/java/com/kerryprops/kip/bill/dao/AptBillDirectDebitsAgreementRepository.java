package com.kerryprops.kip.bill.dao;


import com.kerryprops.kip.bill.common.enums.DirectDebitAgreementStatus;
import com.kerryprops.kip.bill.common.jpa.JpaAndQueryDslExecutor;
import com.kerryprops.kip.bill.dao.entity.AptBillDirectDebitsAgreement;

import java.util.Collection;
import java.util.List;

public interface AptBillDirectDebitsAgreementRepository
        extends JpaAndQueryDslExecutor<AptBillDirectDebitsAgreement, Long> {

    AptBillDirectDebitsAgreement findTopByAgreementNo(String agreementNo);

    List<AptBillDirectDebitsAgreement> findAllByRoomIdInAndAgreementStatusAndIsDel(Collection<String> roomIds
            , DirectDebitAgreementStatus status, int isDel);

    List<AptBillDirectDebitsAgreement> findAllByProjectIdAndAgreementStatusAndIsDel(String projectId
            , DirectDebitAgreementStatus status, int isDel);

    List<AptBillDirectDebitsAgreement> findAllByAgreementNoIn(Collection<String> agreementNos);

    AptBillDirectDebitsAgreement findTopByRoomIdAndAgreementStatusAndIsDelOrderByCreatedTimeDesc(String roomId
            , DirectDebitAgreementStatus status, int isDel);

}
