package com.kerryprops.kip.bill.dao.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.ZonedDateTime;

@Getter
@Setter
@Builder
@Entity
@Table(name = "kerry_bill_send_config_an8_link")
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
public class BillSendConfigAn8Link implements Serializable {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /*账单发送配置ID	*/
    @Column(name = "config_id")
    private Long configId;

    /*用户编号	len: 32*/
    @Column(name = "an8")
    private String an8;

    /*用户名称	len: 255*/
    @Column(name = "alph")
    private String alph;

    /*删除标志	len: 1*/
    @Column(name = "is_del")
    private Integer isDel;

    @Column(name = "created_time", insertable = false, updatable = false, columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private ZonedDateTime createdTime;

    @Column(name = "updated_time", insertable = false, columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private ZonedDateTime updatedTime;

    public String groupByKey() {
        StringBuffer sb = new StringBuffer();
        sb.append(an8).append("_").append(alph).append("_");
        return sb.toString();
    }

}
