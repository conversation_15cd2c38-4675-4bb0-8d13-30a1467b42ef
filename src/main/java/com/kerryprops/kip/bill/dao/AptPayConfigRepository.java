package com.kerryprops.kip.bill.dao;

import com.kerryprops.kip.bill.common.jpa.JpaAndQueryDslExecutor;
import com.kerryprops.kip.bill.dao.entity.AptPayConfig;

import java.util.Collection;

/***********************************************************************************************************************
 * Project - accelerator
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - <PERSON>
 * Created Date - 06/04/2021 10:39
 **********************************************************************************************************************/

public interface AptPayConfigRepository extends JpaAndQueryDslExecutor<AptPayConfig, Long> {

    AptPayConfig findTopByProjectIdAndPaymentType(String projectId, String paymentType);

    boolean existsByProjectIdAndPaymentTypeIn(String projectId, Collection<String> paymentTypes);

    AptPayConfig findTopByProjectIdAndCompanyCodeAndPaymentType(String projectId, String companyCode, String paymentType);

}
