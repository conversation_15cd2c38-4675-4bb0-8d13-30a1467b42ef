package com.kerryprops.kip.bill.dao.entity;

import jakarta.persistence.Column;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Optional;

/**
 * to B - kerry bill：sync data jde source: F55GT01K（read only）
 *
 * <AUTHOR>
 * @Date 2023-3-29
 */

@Data
@Builder
public class EFapiaoJDEBill {

    public static final RowMapper<EFapiaoJDEBill> ROW_MAPPER = new JDEBillInvoiceRowMapper();

    /* 数电，行业特殊票种类型,房屋产权证书/不动产权证号码 */
    @Column(name = "TOADD3")
    String realEstateNo;

    /* 数电，行业特殊票种类型,不动产地址，省市区，如：辽宁省沈阳市沈河区 */
    @Column(name = "TOADD2")
    String realEstatePlace;

    /* 单据公司 */
    @Column(name = "TOKCO")
    private String kco;

    /* 公司 */
    @Column(name = "TOCO")
    private String companyCode;

    /* 单据号，示例：19004780 */
    @Column(name = "TODOC")
    private int doc;

    /* 单据付款项｜示例：001 */
    @Column(name = "TOSFX")
    private String paymentItem;

    /* an8| 示例：******** */
    @Column(name = "TOAN8")
    private String an8;

    /* 购方名称｜xxx有限公司 */
    @Column(name = "TOALPH")
    private String purchaserName;

    /* 购方税号｜示例：91110105625911314P */
    @Column(name = "TOATTL")
    private String purchaserTaxNo;

    /* 购方公司地址 */
    @Column(name = "TODL11")
    private String purchaserAddress;

    /* 购方银行名称｜花旗银行北京分行********** */
    @Column(name = "TODL12")
    private String purchaserBankName;

    /* 发票类型, N：gvat-纸质普票，NE：gvate-电子普票，NQ:gvatq-数电电子普票，Y：svat-专票，YE:svate-电子专票，YQ:svatq-数电电子专票 */
    @Column(name = "TOPCM")
    private String invoiceType;

    /* 邮寄地址/数电:不动产详细地址，如：青年大街188号101单元 */
    @Column(name = "TOEMLA")
    private String mailingAddress;

    /* mcu，经营单位，Business Unit */
    @Column(name = "TOMCU")
    private String mcu;

    /* JDE单元号 */
    @Column(name = "TOUNIT")
    private String jdeUnit;

    /* 账单类型｜示例：RN */
    @Column(name = "TODCT")
    private String billType;

    /* JDE合同号｜示例：250644 */
    @Column(name = "TODOCO")
    private String doco;

    /* 缴费票据码｜示例：U02M */
    @Column(name = "TOGLC")
    private String billCode;

    /* 商品货物税收分类编码 */
    @Column(name = "TODL01")
    private String goodsTaxNo;

    /* 货物或应税劳务名称｜示例：电费 */
    @Column(name = "TODL03")
    private String itemName;

    /* 账单期间/数电行业特殊票种，租赁期起/止｜示例：2015/07/01-2015/09/30 */
    @Column(name = "TOFX03")
    private String itemSpec;

    /* 不含税金额, 单位（分）｜示例：10005 */
    @Column(name = "TOATXA")
    private BigDecimal amountWithoutTax;

    /* 税率 示例：6000 */
    @Column(name = "TOTXR1")
    private BigDecimal taxRate;

    /* 税额, 单位（分）｜示例：830 */
    @Column(name = "TOSTAM")
    private BigDecimal taxAmount;

    /* 含税金额, 单位（分） */
    @Column(name = "TOAG")
    private BigDecimal amountWithTax;

    /* 是否税收优惠 */
    @Column(name = "CMEV01")
    private String taxDiscount;

    /* 税收优惠描述 */
    @Column(name = "CMDL03")
    private String taxDiscountDesc;

    /* 第二字母名称，类似别名，可不填 */
    @Column(name = "TOALPH1")
    private String secondName;

    /* 合同号，内容是doco，经过格式矫正，示例：250644 */
    @Column(name = "TOPO")
    private String formatedDoco;

    /* 是否主要联系人， 示例：1 */
    @Column(name = "TOPCF")
    private String isContactPerson;

    /* 是否一般纳税人, 示例：Y,N */
    @Column(name = "TOTYC")
    private String isTaxpayer;

    /* 常量1，示例：1 */
    @Column(name = "TOGFL1")
    private String constant1;

    /* 税收分类说明， 示例：其他不动产经营租赁，企业管理服务 */
    @Column(name = "TODL02")
    private String taxClassifyDesc;

    /* 常量2 */
    @Column(name = "TOFX04")
    private String constant2;

    /* 常量3，示例：0 */
    @Column(name = "TOUORG")
    private BigDecimal constant3;

    /* 常量4，示例：0 */
    @Column(name = "TOUPRC")
    private BigDecimal constant4;

    /* 常量5，示例：0 */
    @Column(name = "TOPRRC")
    private BigDecimal constant5;

    /* 单据号，内容上是TOKCO&TODCT&TODOC，示例：42010RN19006196001 */
    @Column(name = "TOFX01")
    private String extendedDoc;

    /* 发票日期（可理解为JDE账单日期），示例：2019-11-01 */
    @Column(name = "TOFX02")
    private String billDrawDate;

    /* 描述1 */
    @Column(name = "TODSCRP1")
    private String dscrp1;

    /* 描述2 */
    @Column(name = "TODSCRP2")
    private String dscrp2;

    /* 描述3 */
    @Column(name = "TODSCRP3")
    private String dscrp3;

    /* 用户，示例：INTERFACE */
    @Column(name = "TOUSER")
    private String userName;

    /* 程序号，示例：SP */
    @Column(name = "TOPID")
    private String programId;

    /* 常量6 */
    @Column(name = "TOJOBN")
    private String constant6;

    /* JDE入表日期，示例：123064，1就是21世纪，23就是23年，064就是23年的第64天，所以123064对应的就是2023/3/5， */
    @Column(name = "TOUPMJ")
    private int createDateJde;

    /* JDE入表时间 */
    @Column(name = "TOUPMT")
    private int createTimeJde;

    /* 事件点1 */
    @Column(name = "TOEV01")
    private String event1;

    /* 事件点2 */
    @Column(name = "TOEV02")
    private String event2;

    /* 事件点3 */
    @Column(name = "TOEV03")
    private String event3;

    private static String nonNullConvert(String objString) {
        return Optional.ofNullable(objString).orElse(StringUtils.EMPTY);
    }

    private static class JDEBillInvoiceRowMapper implements RowMapper<EFapiaoJDEBill> {

        @Override
        public EFapiaoJDEBill mapRow(ResultSet rs, int rowNum) throws SQLException {
            String kco = StringUtils.trim(rs.getString("TOKCO")); // 单据公司
            String billType = StringUtils.trim(rs.getString("TODCT")); // 账单类型｜示例：RN
            int doc = rs.getInt("TODOC"); // 单据号
            String paymentItem = StringUtils.trim(rs.getString("TOSFX")); // 付款项
            String companyCode = StringUtils.trim(rs.getString("TOCO")); // 公司
            String an8 = StringUtils.trim(rs.getString("TOAN8")); // 租户号
            String purchaserName = StringUtils.trim(rs.getString("TOALPH")); // 租户名称
            String purchaserTaxNo = StringUtils.trim(rs.getString("TOATTL")); // 纳税人识别号
            String purchaserAddress = StringUtils.trim(rs.getString("TODL11")); // 地址电话
            String purchaserBankName = StringUtils.trim(rs.getString("TODL12")); // 开户行及帐号
            String invoiceTypeJde = StringUtils.trim(rs.getString("TOPCM")); // 发票类型，普票/专票/电子普票/电子专票
            String mailingAddress = StringUtils.trim(rs.getString("TOEMLA")); // 邮寄地址/数电:不动产详细地址，如：青年大街188号101单元
            String mcu = StringUtils.trim(rs.getString("TOMCU"));// 经营单位，Business Unit
            String jdeUnit = StringUtils.trim(rs.getString("TOUNIT"));// JDE单元
            String doco = StringUtils.trim(rs.getString("TODOCO")); // 合同号
            String billCode = StringUtils.trim(rs.getString("TOGLC")); // 票据码
            String goodsTaxNo = StringUtils.trim(rs.getString("TODL01")); // 税收分类码
            String itemName = StringUtils.trim(rs.getString("TODL03")); // 票据码说明
            String itemSpec = StringUtils.trim(rs.getString("TOFX03")); // 账单期间/数电行业特殊票种，租赁期起/止
            BigDecimal amountWithoutTax = rs.getBigDecimal("TOATXA"); // 不含税金额
            BigDecimal taxRate = rs.getBigDecimal("TOTXR1"); // 税率
            BigDecimal taxAmount = rs.getBigDecimal("TOSTAM"); // 税额
            BigDecimal amountWithTax = rs.getBigDecimal("TOAG"); // 含税金额
            String taxDiscount = StringUtils.trim(rs.getString("CMEV01")); // 是否税收优惠 0-不,1-享受
            String taxDiscountDesc = StringUtils.trim(rs.getString("CMDL03")); // 税收优惠描述
            String extendedDoc = StringUtils.trim(rs.getString("TOFX01")); // 单据号（加长版，内容上是TOKCO&TODCT&TODOC）
            String billDrawDate = StringUtils.trim(rs.getString("TOFX02")); // 发票日期
            String secondName = StringUtils.trim(rs.getString("TOALPH1")); // 第二字母名称，类似别名，可不填
            String formatedDoco = StringUtils.trim(rs.getString("TOPO")); // 合同号，内容是doco，经过格式矫正
            String isContactPerson = StringUtils.trim(rs.getString("TOPCF")); // 是否主要联系人
            String isTaxpayer = StringUtils.trim(rs.getString("TOTYC")); // 是否一般纳税人
            String taxClassifyDesc = StringUtils.trim(rs.getString("TODL02")); // 税收分类说明
            String userName = StringUtils.trim(rs.getString("TOUSER")); // 用户
            String programId = StringUtils.trim(rs.getString("TOPID")); // 程序号
            int createDateJde = rs.getInt("TOUPMJ"); // 入表日期
            int createTimeJde = rs.getInt("TOUPMT"); // 入表时间
            String dscrp1 = StringUtils.trim(rs.getString("TODSCRP1")); // 描述1
            String dscrp2 = StringUtils.trim(rs.getString("TODSCRP2")); // 描述2
            String dscrp3 = StringUtils.trim(rs.getString("TODSCRP3")); // 描述3
            String event1 = StringUtils.trim(rs.getString("TOEV01")); // 事件点1
            String event2 = StringUtils.trim(rs.getString("TOEV02")); // 事件点2
            String event3 = StringUtils.trim(rs.getString("TOEV03")); // 事件点3
            String constant1 = StringUtils.trim(rs.getString("TOGFL1")); // 常量
            String constant2 = StringUtils.trim(rs.getString("TOFX04")); // 常量
            BigDecimal constant3 = rs.getBigDecimal("TOUORG"); // 常量
            BigDecimal constant4 = rs.getBigDecimal("TOUPRC"); // 常量
            BigDecimal constant5 = rs.getBigDecimal("TOPRRC"); // 常量
            String constant6 = StringUtils.trim(rs.getString("TOJOBN")); // 常量

            // 数电，行业特殊票种类型
            String realEstateNo = StringUtils.trim(rs.getString("TOADD3")); // 房屋产权证书/不动产权证号码
            String realEstatePlace = StringUtils.trim(rs.getString("TOADD2")); // 不动产地址，省市区，如：辽宁省沈阳市沈河区

            return EFapiaoJDEBill.builder()
                    .kco(nonNullConvert(kco))
                    .companyCode(nonNullConvert(companyCode))
                    .doc(doc)
                    .paymentItem(nonNullConvert(paymentItem))
                    .an8(nonNullConvert(an8))
                    .purchaserName(nonNullConvert(purchaserName))
                    .purchaserTaxNo(nonNullConvert(purchaserTaxNo))
                    .purchaserAddress(nonNullConvert(purchaserAddress))
                    .purchaserBankName(nonNullConvert(purchaserBankName))
                    .invoiceType(nonNullConvert(invoiceTypeJde))
                    .mailingAddress(nonNullConvert(mailingAddress))
                    .mcu(nonNullConvert(mcu))
                    .jdeUnit(nonNullConvert(jdeUnit))
                    .billType(nonNullConvert(billType))
                    .doco(nonNullConvert(doco))
                    .billCode(nonNullConvert(billCode))
                    .goodsTaxNo(nonNullConvert(goodsTaxNo))
                    .itemName(nonNullConvert(itemName))
                    .itemSpec(nonNullConvert(itemSpec))
                    .amountWithoutTax(amountWithoutTax)
                    .taxRate(taxRate)
                    .taxAmount(taxAmount)
                    .amountWithTax(amountWithTax)
                    .taxDiscount(nonNullConvert(taxDiscount))
                    .taxDiscountDesc(nonNullConvert(taxDiscountDesc))
                    .extendedDoc(nonNullConvert(extendedDoc))
                    .billDrawDate(nonNullConvert(billDrawDate))
                    .secondName(nonNullConvert(secondName))
                    .formatedDoco(nonNullConvert(formatedDoco))
                    .isContactPerson(nonNullConvert(isContactPerson))
                    .isTaxpayer(nonNullConvert(isTaxpayer))
                    .taxClassifyDesc(nonNullConvert(taxClassifyDesc))
                    .userName(nonNullConvert(userName))
                    .programId(nonNullConvert(programId))
                    .createDateJde(createDateJde)
                    .createTimeJde(createTimeJde)
                    .realEstateNo(nonNullConvert(realEstateNo))
                    .realEstatePlace(nonNullConvert(realEstatePlace))
                    .event1(nonNullConvert(event1))
                    .event2(nonNullConvert(event2))
                    .event3(nonNullConvert(event3))
                    .dscrp1(nonNullConvert(dscrp1))
                    .dscrp2(nonNullConvert(dscrp2))
                    .dscrp3(nonNullConvert(dscrp3))
                    .constant1(nonNullConvert(constant1))
                    .constant2(nonNullConvert(constant2))
                    .constant3(constant3)
                    .constant4(constant4)
                    .constant5(constant5)
                    .constant6(nonNullConvert(constant6))
                    .build();
        }

    }

}
