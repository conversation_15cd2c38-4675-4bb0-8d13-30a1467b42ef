package com.kerryprops.kip.bill.dao;


import com.kerryprops.kip.bill.common.jpa.JpaAndQueryDslExecutor;
import com.kerryprops.kip.bill.dao.entity.AptPaymentBill;

import java.util.Collection;
import java.util.List;

public interface AptPaymentBillRepository extends JpaAndQueryDslExecutor<AptPaymentBill, Long> {

    List<AptPaymentBill> findAllByBillIdIn(Collection<Long> billIds);

    List<AptPaymentBill> findAllByPaymentInfoIdAndDeleted(String paymentInfoId, Integer deleted);

}
