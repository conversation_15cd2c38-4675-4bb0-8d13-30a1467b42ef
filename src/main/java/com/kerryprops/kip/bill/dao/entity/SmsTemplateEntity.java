package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 名   称：kerry_sms_template
 * 描   述：
 * 作   者：David <PERSON>
 * 时   间：2021/07/05 13:40:19
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Getter
@Setter
@Builder
@Entity
@Table(name = "kerry_sms_template")
@NoArgsConstructor
@AllArgsConstructor
public class SmsTemplateEntity extends BaseEntity {

    /*模板名称	len: 100*/
    @Column(name = "temp_name")
    private String tempName;

    /*模板内容	len: 512*/
    @Column(name = "content")
    private String content;

    /*	len: 100*/
    @Column(name = "create_by")
    private String createBy;

    /*	len: 100*/
    @Column(name = "update_by")
    private String updateBy;

    /*	len: 255*/
    @Column(name = "remark")
    private String remark;

    /*	len: 2*/
    @Column(name = "del_flag")
    private String delFlag;


}
