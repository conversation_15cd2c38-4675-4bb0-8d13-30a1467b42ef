package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 名   称：apt_sync_paid_bill_to_jde
 * 描   述：
 * 作   者：David <PERSON>
 * 时   间：2021/09/07 13:33:28
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Getter
@Setter
@Entity
@Table(name = "apt_sync_paid_bill_to_jde")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AptSyncPaidBillToJde extends BaseEntity {

    /**/
    /*len: 10*/
    @Column(name = "job_id")
    private Long jobId;

    /**/
    /*len: 10*/
    @Column(name = "pay_bill_id")
    private Long payBillId;

    /**/
    /*len: 10*/
    @Column(name = "status")
    @Builder.Default
    private Integer status = 0;

    /**/
    /*len: 65535*/
    @Column(name = "error_msg")
    private String errorMsg;

    /**/
    /*len: 32*/
    @Column(name = "RBCKNU")
    private String rbcknu;

    /**/
    /*len: 5*/
    @Column(name = "RCKCO")
    private String rckco;

    /**/
    /*len: 2*/
    @Column(name = "RCDCT")
    private String rcdct;

    /**/
    /*len: 10*/
    @Column(name = "RCDOC")
    private Long rcdoc;

    /**/
    /*len: 3*/
    @Column(name = "RCSFX")
    private String rcsfx;

    /**/
    /*len: 11*/
    @Column(name = "RCAG")
    private Integer rcag;

    /**/
    /*len: 10*/
    @Column(name = "RCDMTJ")
    private Integer rcdmtj;

    /**/
    /*len: 1*/
    @Column(name = "RCEDSP")
    @Builder.Default
    private String rcedsp = "0";


}
