package com.kerryprops.kip.bill.dao.convert;

import com.kerryprops.kip.bill.common.enums.InvoiceRecordStatus;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter
public class InvoiceRecordStatusConverter implements AttributeConverter<InvoiceRecordStatus, Integer> {

    @Override
    public Integer convertToDatabaseColumn(InvoiceRecordStatus attribute) {
        return attribute.getIndex();
    }

    @Override
    public InvoiceRecordStatus convertToEntityAttribute(Integer dbData) {
        return InvoiceRecordStatus.fromIndex(dbData);
    }

}
