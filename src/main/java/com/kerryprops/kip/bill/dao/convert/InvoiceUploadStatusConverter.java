package com.kerryprops.kip.bill.dao.convert;

import com.kerryprops.kip.bill.common.enums.InvoiceIssueStatus;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

/**
 * 开票请求状态Converter类
 *
 * <AUTHOR>
 * @date 2023-4-11
 */
@Converter
public class InvoiceUploadStatusConverter implements AttributeConverter<InvoiceIssueStatus, Integer> {

    @Override
    public Integer convertToDatabaseColumn(InvoiceIssueStatus attribute) {
        return attribute.getIndex();
    }

    @Override
    public InvoiceIssueStatus convertToEntityAttribute(Integer dbData) {
        return InvoiceIssueStatus.fromIndex(dbData);
    }

}
