package com.kerryprops.kip.bill.dao;

import com.kerryprops.kip.bill.common.enums.InvoiceRecordStatus;
import com.kerryprops.kip.bill.common.jpa.JpaAndQueryDslExecutor;
import com.kerryprops.kip.bill.dao.entity.InvoiceRecord;
import com.kerryprops.kip.bill.dao.entity.QInvoiceRecord;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.apache.commons.collections4.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

public interface InvoiceRecordRepository extends JpaAndQueryDslExecutor<InvoiceRecord, String> {

    Optional<InvoiceRecord> findByInvoiceNo(String invoiceNo);

    List<InvoiceRecord> findByOrderNo(String orderNo);

    List<InvoiceRecord> findByOrderNoIn(Collection<String> orderNos);

    default InvoiceRecord getByOrderNo(String orderNo) {
        List<InvoiceRecord> invoiceRecords = findByOrderNo(orderNo);
        if (CollectionUtils.isEmpty(invoiceRecords)) {
            return null;
        }

        InvoiceRecord invoiceRecord = invoiceRecords.stream().max(Comparator.comparing(InvoiceRecord::getId)).get();
        return invoiceRecord;
    }

    default List<InvoiceRecord> findUnFinishedInvoices(boolean isInvoiceInvoked, int limit) {
        JPAQueryFactory factory = getJpaQueryFactory();
        return factory.from(QInvoiceRecord.invoiceRecord)
                .select(QInvoiceRecord.invoiceRecord)
                .where(
                        QInvoiceRecord.invoiceRecord.applyTime.before(LocalDateTime.now())
                                .and(QInvoiceRecord.invoiceRecord.status.eq(InvoiceRecordStatus.PROCESSING))
                                .and(QInvoiceRecord.invoiceRecord.isInvoiceInvoked.eq(isInvoiceInvoked)))
                .limit(limit)
                .orderBy(QInvoiceRecord.invoiceRecord.applyTime.desc())
                .fetch();
    }

}