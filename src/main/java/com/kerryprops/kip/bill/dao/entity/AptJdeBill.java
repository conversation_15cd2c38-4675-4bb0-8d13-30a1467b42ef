package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * 名   称：wxq_sync_bills
 * 描   述：
 * 作   者：David <PERSON>
 * 时   间：2021/08/25 16:17:48
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Getter
@Setter
@Entity
@SuperBuilder
@Table(name = "apt_sync_bills")
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AptJdeBill extends BaseEntity {

    /* EDI批号, len: 32*/
    @Column(name = "rd_edbt")
    private String rdEdbt;

    /* 单据号, len: 32*/
    @Column(name = "rd_doc")
    private Long rdDoc;

    /* 单据类型, len: 32*/
    @Column(name = "rd_dct")
    private String rdDct;

    /* 单据公司, len: 32*/
    @Column(name = "rd_kco")
    private String rdKco;

    /* 单据付款项, len: 32*/
    @Column(name = "rd_sfx")
    private String rdSfx;

    /*总账冲销-票据码, len: 32*/
    @Column(name = "rd_glc")
    private String rdGlc;

    /* 费用类型说明, len: 32*/
    @Column(name = "rd_dl01")
    private String rdDl01;

    /* 费用属性/管理费/非管理费, len: 32*/
    @Column(name = "rd_dl02")
    private String rdDl02;

    /* 账单开始日期1+YY+天数, len: 32*/
    @Column(name = "rd_date01")
    private String rdDate01;

    /* 账单结束日期1+YY+天数,	len: 32*/
    @Column(name = "rd_date02")
    private String rdDate02;

    /* 总金额, len: 32*/
    @Column(name = "rd_ag")
    private Double rdAg;

    /* BU, len: 32*/
    @Column(name = "rd_mcu")
    private String rdMcu;

    /* 建筑物, len: 32*/
    @Column(name = "rd_dl03")
    private String rdDl03;

    /* 单元, len: 32*/
    @Column(name = "rd_unit")
    private String rdUnit;

    /* 地址号, len: 32*/
    @Column(name = "rd_an8")
    private String rdAn8;

    /* 租户名称,	len: 256*/
    @Column(name = "rd_alph")
    private String rdAlph;

    /* 租赁合同号, len: 32*/
    @Column(name = "rd_doco")
    private String rdDoco;

    /* 账单开始日期YYYYMMDD, len: 32*/
    @Column(name = "rd_uds1")
    private String rdUds1;

    /* 账单结束日期YYYYMMDD, len: 32*/
    @Column(name = "rd_uds2")
    private String rdUds2;

    /* 账单创建日期, len: 32*/
    @Column(name = "rd_uds3")
    private String rdUds3;

    /* 预留标志位1, len: 32*/
    @Column(name = "rd_ev01")
    private String rdEv01;

    /* 预留标志位2, len: 32*/
    @Column(name = "rd_ev02")
    private String rdEv02;

    /* 预留标志位3, len: 32*/
    @Column(name = "rd_ev03")
    private String rdEv03;

    /* 用户号, len: 32*/
    @Column(name = "rd_user")
    private String rdUser;

    /* 程序号 更新日期 */
    @Column(name = "rd_upmj")
    private Long rdUpmj;

    /* 工作站号 更新时间 */
    @Column(name = "rd_upmt")
    private String rdUpmt;

    /*	len: 32*/
    @Column(name = "rd_pid")
    private String rdPid;

    /*	len: 32*/
    @Column(name = "rd_jobn")
    private String rdJobn;

    /* 收款号, len: 32*/
    @Column(name = "pay_number_jde")
    private String payNumberJde;

    /* 账单号, len: 32*/
    @Column(name = "bill_number")
    private String billNumber;

    /* 差异导入, len: 64*/
    @Column(name = "new_import")
    private String newImport;

    /* 微小区读取标记, len: 64*/
    @Column(name = "rd_edsp")
    private String rdEdsp;

    @Builder.Default
    @Column(name = "online_verification")
    private Integer onlineVerification = 0;

    @Column(name = "online_verification_time")
    private Date onlineVerificationTime;

    @Builder.Default
    @Column(name = "jde_verification")
    private Integer jdeVerification = 0;

    @Column(name = "jde_verification_time")
    private Date jdeVerificationTime;

    @Builder.Default
    @Column(name = "deleted_at")
    private Integer deletedAt = 0;

    public String groupByBill() {
        StringBuffer sb = new StringBuffer();
        if (StringUtils.isNotEmpty(rdKco)) {
            sb.append(rdKco).append("_");
        }
        sb.append(rdDoc).append("_");
        if (StringUtils.isNotEmpty(this.rdDct)) {
            sb.append(rdDct).append("_");
        }
        if (StringUtils.isNotEmpty(this.rdSfx)) {
            sb.append(rdSfx);
        }

        return sb.toString();
    }

    ////rd_kco,rd_glc,rd_dl01,rd_date01,rd_date02,rd_mcu,rd_an8,rd_unit
    public String groupByKey() {
        StringBuffer sb = new StringBuffer();
        if (StringUtils.isNotEmpty(rdKco)) {
            sb.append(rdKco).append("_");
        }
        if (StringUtils.isNotEmpty(rdGlc)) {
            sb.append(rdGlc).append("_");
        }
        if (StringUtils.isNotEmpty(rdDl01)) {
            sb.append(rdDl01).append("_");
        }
        if (StringUtils.isNotEmpty(rdDate01)) {
            sb.append(rdDate01).append("_");
        }
        if (StringUtils.isNotEmpty(rdDate02)) {
            sb.append(rdDate02).append("_");
        }
        if (StringUtils.isNotEmpty(rdMcu)) {
            sb.append(rdMcu).append("_");
        }
        if (StringUtils.isNotEmpty(rdAn8)) {
            sb.append(rdAn8).append("_");
        }
        if (StringUtils.isNotEmpty(rdUnit)) {
            sb.append(rdUnit).append("_");
        }
        return sb.toString();
    }

}
