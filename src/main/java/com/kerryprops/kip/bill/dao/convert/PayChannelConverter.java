package com.kerryprops.kip.bill.dao.convert;

import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter
public class PayChannelConverter implements AttributeConverter<BillPayChannel, Integer> {

    @Override
    public Integer convertToDatabaseColumn(BillPayChannel attribute) {
        return attribute.getCode();
    }

    @Override
    public BillPayChannel convertToEntityAttribute(Integer dbData) {
        return BillPayChannel.fromCode(dbData);
    }

}
