package com.kerryprops.kip.bill.dao.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.io.Serializable;
import java.time.ZonedDateTime;

@Data
@Entity
@Table(name = "tb_apt_direct_debits_batch_bill")
public class AptDirectDebitsBatchBill implements Serializable {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @Column(name = "batch_no")
    private String batchNo;

    @Column(name = "company_code")
    private String companyCode;

    @Column(name = "building_id")
    private String buildingId;

    @Column(name = "room_id")
    private String roomId;

    @Column(name = "bill_id")
    private long billId;

    @Column(name = "bill_no")
    private String billNo;

    @Column(name = "payment_info_id")
    private String paymentInfoId;

    @Column(name = "created_time", insertable = false, updatable = false, columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private ZonedDateTime createdTime;

    @Column(name = "updated_time", insertable = false, columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private ZonedDateTime updatedTime;

    @Column(name = "psp_name")
    private String pspName;

}
