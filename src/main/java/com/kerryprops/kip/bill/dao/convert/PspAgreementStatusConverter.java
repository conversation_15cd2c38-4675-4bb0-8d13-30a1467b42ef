package com.kerryprops.kip.bill.dao.convert;

import com.kerryprops.kip.bill.common.enums.DirectDebitAgreementStatus;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.springframework.stereotype.Component;

@Component
@Converter
public class PspAgreementStatusConverter implements AttributeConverter<DirectDebitAgreementStatus, Integer> {

    @Override
    public Integer convertToDatabaseColumn(DirectDebitAgreementStatus productType) {
        return productType.getIndex();
    }

    @Override
    public DirectDebitAgreementStatus convertToEntityAttribute(Integer index) {
        return DirectDebitAgreementStatus.fromIndex(index);
    }

}
