package com.kerryprops.kip.bill.dao.impl;

import com.kerryprops.kip.bill.config.EFapiaoConfig;
import com.kerryprops.kip.bill.config.SyncJdeConfig;
import com.kerryprops.kip.bill.dao.JdeEFapiaoBillWriteBackRepository;
import com.kerryprops.kip.bill.dao.entity.EFapiaoJDEBill;
import com.kerryprops.kip.bill.webservice.vo.req.CallBackKerryInvoiceMainVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;

import static com.kerryprops.kip.bill.common.constants.JdeConstants.JDE_MULTIPLY_NUMBER_100;
import static com.kerryprops.kip.bill.common.utils.InvoiceUtils.splitJdeSalesbillNo;

/**
 * e-fapiao：发票信息JDE回写-Dao层操作实现类
 *
 * <AUTHOR> Yan
 * @Date 2023-3-28
 */
@Slf4j
@Repository
public class JDBCJdeEFapiaoBillWriteBackRepository implements JdeEFapiaoBillWriteBackRepository {

    @Autowired
    EFapiaoConfig eFapiaoConfig;

    @Autowired
    @Qualifier("jdeJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    @Override
    public int insert(CallBackKerryInvoiceMainVo mainVo, String paperDrewDate
            , int jdeFormatDate, BigDecimal jdeAmountWithTax
            , BigDecimal jdeAmountWithoutTax, BigDecimal amountTax, String jdeStatus) {
        // 回写发票信息
        String insertSqlFormat = "INSERT INTO %s " +
                "(IFDOC, IFDCT, IFKCO, IFSFX, IFLINN, IFMCU, IFGUI, IFTAX, IFAG, IFATXA, IFSTAM" +
                ",IFURRF, IFEV01, IFEV02, IFUSER, IFUPMJ, IFUPMT, IFCINV, IFPCM)" +
                " VALUES (? ,? ,? ,? ,? ,? ,? ,? ,? ,? ,? ,? ,? ,? ,? ,? ,? ,? ,?)";
        String insertSql = String.format(insertSqlFormat, SyncJdeConfig.getJdeBillInvoiceWriteBack());
        log.info("e_fapiao_write_back_invoice insert_sql: [{}]", insertSql);

        String salesbillNo = mainVo.getSalesbillNo();
        String[] jdeSalesbillNoArray = splitJdeSalesbillNo(salesbillNo);
        return jdbcTemplate.update(insertSql, pstmt -> {
            int paramIndex = 1;
            LocalDateTime tempLocalDateTime = LocalDateTime.now();
            // IFDOC
            pstmt.setInt(paramIndex++, Integer.parseInt(jdeSalesbillNoArray[2]));
            // IFDCT
            pstmt.setString(paramIndex++, jdeSalesbillNoArray[1]);
            // IFKCO
            pstmt.setString(paramIndex++, jdeSalesbillNoArray[0]);
            // IFSFX
            pstmt.setString(paramIndex++, jdeSalesbillNoArray[3]);
            // IFLINN
            pstmt.setInt(paramIndex++, new Random().nextInt(900) + 100);
            // IFMCU
            pstmt.setString(paramIndex++, eFapiaoConfig.getBlanks() + mainVo.getBusinessType());
            // IFGUI
            pstmt.setString(paramIndex++, mainVo.getInvoiceNo());
            // IFTAX
            pstmt.setString(paramIndex++, mainVo.getPurchaserTaxNo());
            // IFAG
            pstmt.setInt(paramIndex++, (JDE_MULTIPLY_NUMBER_100.multiply(jdeAmountWithTax)).intValue());
            // IFATXA
            pstmt.setInt(paramIndex++, (JDE_MULTIPLY_NUMBER_100.multiply(jdeAmountWithoutTax)).intValue());
            // IFSTAM
            pstmt.setInt(paramIndex++, (JDE_MULTIPLY_NUMBER_100.multiply(amountTax)).intValue());
            // IFURRF
            pstmt.setString(paramIndex++, paperDrewDate);
            // IFEV01
            pstmt.setString(paramIndex++, jdeStatus);
            // IFEV02
            pstmt.setString(paramIndex++, "3");
            // IFUSER
            pstmt.setString(paramIndex++, "Kerry+");
            // IFUPMJ
            pstmt.setInt(paramIndex++, jdeFormatDate);
            // IFUPMT
            pstmt.setInt(paramIndex++, Integer.parseInt(tempLocalDateTime
                    .format(DateTimeFormatter.ofPattern("HHmmss"))));
            // IFCINV
            pstmt.setString(paramIndex++, mainVo.getInvoiceCode());
            // IFPCM
            pstmt.setString(paramIndex, mainVo.getInvoiceType());
        });
    }

    @Override
    public int updateStatusFalse(CallBackKerryInvoiceMainVo mainVo) {
        String updateWriteBackSqlFormat = "UPDATE %s SET IFEV01=0" +
                " WHERE IFDOC = ? AND IFDCT = ? AND IFKCO = ? AND IFSFX = ? AND IFGUI LIKE ? ";

        String updateWriteBackSql = String
                .format(updateWriteBackSqlFormat, SyncJdeConfig.getJdeBillInvoiceWriteBack());
        log.info("e_fapiao_write_back_invoice cancel_invoice_sql: [{}]", updateWriteBackSql);

        String[] JdeSalesbillNoArray = splitJdeSalesbillNo(mainVo.getSalesbillNo());
        return jdbcTemplate.update(updateWriteBackSql, pstmt -> {
            int paramIndex = 1;
            pstmt.setInt(paramIndex++, Integer.parseInt(JdeSalesbillNoArray[2]));
            pstmt.setString(paramIndex++, JdeSalesbillNoArray[1]);
            pstmt.setString(paramIndex++, JdeSalesbillNoArray[0]);
            pstmt.setString(paramIndex++, JdeSalesbillNoArray[3]);
            pstmt.setString(paramIndex, "%" + mainVo.getInvoiceNo() + "%");
        });
    }

}