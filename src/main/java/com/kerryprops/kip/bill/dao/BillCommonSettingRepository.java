package com.kerryprops.kip.bill.dao;

import com.kerryprops.kip.bill.common.jpa.JpaAndQueryDslExecutor;
import com.kerryprops.kip.bill.dao.entity.BillCommonSetting;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface BillCommonSettingRepository extends JpaAndQueryDslExecutor<BillCommonSetting, Long> {

    @Query(value = "select content from bill_common_setting where type = :type", nativeQuery = true)
    public String querySettingContent(@Param("type") String type);

}
