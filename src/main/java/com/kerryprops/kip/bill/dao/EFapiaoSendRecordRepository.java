package com.kerryprops.kip.bill.dao;

import com.kerryprops.kip.bill.common.jpa.JpaAndQueryDslExecutor;
import com.kerryprops.kip.bill.dao.entity.EFapiaoSendRecord;

import java.util.Collection;
import java.util.List;

public interface EFapiaoSendRecordRepository extends JpaAndQueryDslExecutor<EFapiaoSendRecord, Long> {

    List<EFapiaoSendRecord> findAllByBillFapiaoIdIn(Collection<Long> fapiaoIds);

    List<EFapiaoSendRecord> findAllByRequestId(String requestId);

    List<EFapiaoSendRecord> findAllByBatchNo(String batchNo);

}
