package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.enums.DirectDebitAgreementSignType;
import com.kerryprops.kip.bill.common.enums.DirectDebitAgreementStatus;
import com.kerryprops.kip.bill.common.enums.DirectDebitTerminatedType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.time.ZonedDateTime;

@Data
@Entity
@Table(name = "tb_apt_bill_direct_debits_agreement")
public class AptBillDirectDebitsAgreement {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "user_profile_id")
    private String userProfileId;

    @Column(name = "psp_logon_id")
    private String pspLogonId;

    @Column(name = "user_phone_number")
    private String userPhoneNumber;

    @Column(name = "psp_name")
    private String pspName;

    @Column(name = "agreement_no")
    private String agreementNo;

    @Column(name = "agreement_status")
    private DirectDebitAgreementStatus agreementStatus;

    @Column(name = "order_no")
    private String orderNo;

    @Column(name = "project_id")
    private String projectId;

    @Column(name = "project_name")
    private String projectName;

    @Column(name = "building_id")
    private String buildingId;

    @Column(name = "building_name")
    private String buildingName;

    @Column(name = "floor_id")
    private String floorId;

    @Column(name = "floor_name")
    private String floorName;

    @Column(name = "room_id")
    private String roomId;

    @Column(name = "room_name")
    private String roomName;

    @Column(name = "invalid_time")
    private ZonedDateTime invalidTime;

    @Column(name = "sign_type")
    private DirectDebitAgreementSignType signType;

    @Column(name = "sign_time")
    private ZonedDateTime signTime;

    @Column(name = "unsign_type")
    private DirectDebitTerminatedType unsignType;

    @Column(name = "unsign_time")
    private ZonedDateTime unsignTime;

    @Column(name = "created_time", insertable = false, updatable = false, columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private ZonedDateTime createdTime;

    @Column(name = "updated_time", insertable = false, columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private ZonedDateTime updatedTime;

    @Column(name = "is_del")
    private Integer isDel;

    @Column(name = "personal_product_code")
    private String personalProductCode;

}
