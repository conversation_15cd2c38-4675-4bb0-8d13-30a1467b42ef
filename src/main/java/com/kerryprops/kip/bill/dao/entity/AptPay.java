package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.enums.AptPayVerifyStatus;
import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import com.kerryprops.kip.bill.common.enums.BillPayModule;
import com.kerryprops.kip.bill.common.enums.InvoicedStatus;
import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import com.kerryprops.kip.bill.dao.convert.AptPayConverter;
import com.kerryprops.kip.bill.dao.convert.PositionItemConverter;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.DynamicInsert;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 已支付的收款记录
 *
 * <AUTHOR> Wei 2021/08/31 13:58:03
 */
@Getter
@Setter
@Entity
@Table(name = "apt_pay")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@DynamicInsert
public class AptPay extends BaseEntity {

    /*描述	len: 64*/
    @Column(name = "pay_desc")
    private String payDesc;

    /*收费时间段开始	len: 19*/
    @Column(name = "begin_date")
    private String beginDate;

    /*收费时间段结束	len: 19*/
    @Column(name = "end_date")
    private String endDate;

    /*支付配置ID	len: 10*/
    @Column(name = "pay_config_id")
    private Long payConfigId;

    /*费用类型说明	len: 30*/
    @Column(name = "pay_channel")
    @Enumerated(value = EnumType.STRING)
    private BillPayChannel payChannel;

    /*支付配置类型	len: 10*/
    @Column(name = "pay_type")
    private String payType;

    /*科目账	len: 11*/
    @Column(name = "payment_cate")
    private String paymentCate;

    /*手续费率	len: 32*/
    @Column(name = "tax")
    private double tax;

    /*手续费	len: 32*/
    @Column(name = "tax_amt")
    private double taxAmt;

    /*合计	len: 32*/
    @Column(name = "total_amt")
    private double totalAmt;

    /*支付时间	len: 19*/
    @Column(name = "pay_date")
    private Date payDate;

    /*支付流水	len: 32*/
    @Column(name = "pay_tranx")
    private String payTranx;

    /*收款号	len: 64*/
    @Column(name = "pay_act")
    private String payAct;

    /*支付详情	len: 512*/
    @Column(name = "pay_detail")
    private String payDetail;

    /* 是否写入JDE,0 未复核 1 已复核; len: 32 */
    @Builder.Default
    @Column(name = "verify_status")
    @Convert(converter = AptPayConverter.class)
    private AptPayVerifyStatus verifyStatus = AptPayVerifyStatus.TO_BE_VERIFIED;

    /*是否写入JDE 0未写，2写入中间表成功 3写入中间表失败 4 写入JDE成功 5 写入JDE失败	len: 32*/
    @Builder.Default
    @Column(name = "send_jde_status")
    private Integer sendJdeStatus = 0;

    /*	len: 64*/
    @Column(name = "project_id")
    private String projectId;

    /*	len: 10*/
    @Column(name = "building_id")
    private String buildingId;

    /*	len: 10*/
    @Column(name = "floor_id")
    private String floorId;

    /*	len: 10*/
    @Column(name = "room_id")
    private String roomId;

    /*	len: 32*/
    @Column(name = "bu")
    private String bu;

    /*	len: 32*/
    @Column(name = "unit")
    private String unit;

    /*	len: 32*/
    @Column(name = "an8")
    private String an8;

    @Column(name = "alph")
    private String alph;

    @Column(name = "doco")
    private String doco;

    /*	len: 500*/
    @Column(name = "position_item")
    @Convert(converter = PositionItemConverter.class)
    @Builder.Default
    private PositionItemResponse positionItem = new PositionItemResponse();

    /*备注	len: 64*/
    @Column(name = "comments")
    private String comments;

    /*收款人	len: 10*/
    @Column(name = "deleted_at")
    @Builder.Default
    private Integer deletedAt = 0;

    @Column(name = "deleted_by")
    private String deletedBy;

    @Column(name = "deleted_time")
    private Date deletedTime;

    /*	len: 64*/
    @Column(name = "create_by")
    private String createBy;

    /* 账单id */
    @Column(name = "payment_info_id")
    private String paymentInfoId;

    @Column(name = "version_num")
    @Builder.Default
    private Integer versionNum = 0;

    @Column(name = "status")
    @Builder.Default
    private Integer status = 1;

    @Column(name = "advance_amount")
    private BigDecimal advanceAmount;

    @Enumerated(value = EnumType.STRING)
    @Column(name = "bill_pay_module")
    private BillPayModule billPayModule;

    @Column(name = "fee_id")
    private Long feeId;

    @Column(name = "fee_name")
    private String feeName;

    @Column(name = "fee_tax")
    private BigDecimal feeTax;

    @Column(name = "fee_tax_amount")
    private BigDecimal feeTaxAmount;

    @Builder.Default
    @Column(name = "invoiced_status")
    @Enumerated(value = EnumType.STRING)
    private InvoicedStatus invoicedStatus = InvoicedStatus.NO_INVOICED;

}
