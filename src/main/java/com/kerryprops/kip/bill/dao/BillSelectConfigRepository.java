package com.kerryprops.kip.bill.dao;

import com.kerryprops.kip.bill.common.enums.BillSelectMode;
import com.kerryprops.kip.bill.common.jpa.JpaAndQueryDslExecutor;
import com.kerryprops.kip.bill.dao.entity.BillSelectConfig;
import com.kerryprops.kip.bill.dao.entity.QBillSelectConfig;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

/**
 * BillSelectConfigRepository.
 *
 * <AUTHOR> Yu 2025-02-27 15:45:53
 **/
public interface BillSelectConfigRepository extends JpaAndQueryDslExecutor<BillSelectConfig, Long> {

    /**
     * 根据项目编号、楼栋编号和房间编号查找账单选择模式.
     * 方法按照优先级尝试从房间级、楼栋级和项目级查找匹配的账单选择模式.
     * 如果找不到匹配的模式，默认返回 {@link BillSelectMode#ANY_BILLS}.
     *
     * @param projectId  项目的唯一标识符
     * @param buildingId 楼栋的唯一标识符
     * @param roomId     房间的唯一标识符
     * @return 匹配的账单选择模式，如果未找到，返回默认模式 {@link BillSelectMode#ANY_BILLS}
     */
    default BillSelectMode findBillSelectMode(String projectId, String buildingId, String roomId) {
        var billSelectConfigQuery = QBillSelectConfig.billSelectConfig;
        var selectMode = getJpaQueryFactory().select(billSelectConfigQuery.billSelectMode)
                .from(billSelectConfigQuery)
                .where(billSelectConfigQuery.projectId.eq(projectId)
                        .and(billSelectConfigQuery.buildingId.eq(buildingId))
                        .and(billSelectConfigQuery.roomId.eq(roomId)))
                .limit(1)
                .fetchOne();
        if (selectMode == null) {
            selectMode = getJpaQueryFactory().select(billSelectConfigQuery.billSelectMode)
                    .from(billSelectConfigQuery)
                    .where(billSelectConfigQuery.projectId.eq(projectId)
                            .and(billSelectConfigQuery.buildingId.eq(buildingId))
                            .and(billSelectConfigQuery.roomId.eq(StringUtils.EMPTY)))
                    .limit(1)
                    .fetchOne();
        }
        if (selectMode == null) {
            selectMode = getJpaQueryFactory().select(billSelectConfigQuery.billSelectMode)
                    .from(billSelectConfigQuery)
                    .where(billSelectConfigQuery.projectId.eq(projectId)
                            .and(billSelectConfigQuery.buildingId.eq(StringUtils.EMPTY))
                            .and(billSelectConfigQuery.roomId.eq(StringUtils.EMPTY)))
                    .limit(1)
                    .fetchOne();
        }
        return Optional.ofNullable(selectMode).orElse(BillSelectMode.ANY_BILLS);
    }

    /**
     * 检查是否存在与给定参数匹配的账单选择配置.
     *
     * @param projectId  项目唯一标识
     * @param buildingId 楼栋唯一标识
     * @param roomId     单元唯一标识
     * @return 如果存在匹配的账单选择配置则返回true，否则返回false
     */
    default boolean existsConfig(String projectId, String buildingId, String roomId) {
        var billSelectConfigQuery = QBillSelectConfig.billSelectConfig;
        if (StringUtils.isNotBlank(roomId)) {
            return exists(billSelectConfigQuery.projectId.eq(projectId)
                    .and(billSelectConfigQuery.buildingId.eq(buildingId))
                    .and(billSelectConfigQuery.roomId.eq(roomId)));
        }
        if (StringUtils.isNotBlank(buildingId)) {
            return exists(billSelectConfigQuery.projectId.eq(projectId)
                    .and(billSelectConfigQuery.buildingId.eq(buildingId))
                    .and(billSelectConfigQuery.roomId.eq(StringUtils.EMPTY)));
        }
        return exists(billSelectConfigQuery.projectId.eq(projectId)
                .and(billSelectConfigQuery.buildingId.eq(StringUtils.EMPTY))
                .and(billSelectConfigQuery.roomId.eq(StringUtils.EMPTY)));
    }

}
