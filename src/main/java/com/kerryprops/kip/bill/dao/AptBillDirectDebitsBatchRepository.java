package com.kerryprops.kip.bill.dao;

import com.kerryprops.kip.bill.common.jpa.JpaAndQueryDslExecutor;
import com.kerryprops.kip.bill.dao.entity.AptBillDirectDebitsBatch;

public interface AptBillDirectDebitsBatchRepository
        extends JpaAndQueryDslExecutor<AptBillDirectDebitsBatch, Long> {

    AptBillDirectDebitsBatch findTopByBatchNo(String batchNo);

    /*@Modifying
    @Query("update AptBillDirectDebitsBatch SET isDel = 1 where batchNo = :batchNo")
    boolean delBatch(String batchNo);*/

}
