package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.enums.ContentTypeEnum;
import com.kerryprops.kip.bill.dao.convert.ContentTypeConverter;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.ZonedDateTime;

/**
 * kerry bill B端App 已读信息表
 *
 * <AUTHOR>
 * @Date 2023-06-08
 */
@Getter
@Setter
@Table(name = "tb_biz_content_read")
@Entity
public class BizContentReadEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /* 类型：1-B端发票，2-B端账单 */
    @Column(name = "type")
    @Convert(converter = ContentTypeConverter.class)
    private ContentTypeEnum type;

    /* 信息在源表中的id */
    @Column(name = "content_id")
    private Long contentId;

    /* 用户id */
    @Column(name = "user_id")
    private Long userId;

    @Column(name = "created_time", insertable = false, updatable = false, columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private ZonedDateTime createdTime;

    @Column(name = "updated_time", insertable = false, columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private ZonedDateTime updatedTime;

}

