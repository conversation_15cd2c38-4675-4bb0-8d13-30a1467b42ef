package com.kerryprops.kip.bill.dao.convert;

import com.kerryprops.kip.bill.common.enums.SendStatus;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter
public class SendStatusConverter implements AttributeConverter<SendStatus, Integer> {

    @Override
    public Integer convertToDatabaseColumn(SendStatus attribute) {
        return attribute.getIndex();
    }

    @Override
    public SendStatus convertToEntityAttribute(Integer dbData) {
        return SendStatus.fromIndex(dbData);
    }

}
