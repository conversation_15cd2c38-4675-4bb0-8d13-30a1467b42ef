package com.kerryprops.kip.bill.dao;

import com.kerryprops.kip.bill.common.jpa.JpaAndQueryDslExecutor;
import com.kerryprops.kip.bill.dao.entity.NoticeOutboxEntity;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

/***********************************************************************************************************************
 * Project - accelerator
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - <PERSON>
 * Created Date - 06/04/2021 10:39
 **********************************************************************************************************************/

public interface NoticeOutboxRepository extends JpaAndQueryDslExecutor<NoticeOutboxEntity, Long> {


    /**
     * 删除通知发件箱
     *
     * @param id 通知发件箱ID
     * @return 结果
     */
    @Transactional
    @Modifying(clearAutomatically = true)
    @Query(value = "update kerry_notice_outbox set del_flag = '2' where Id = :id", nativeQuery = true)
    int deleteNoticeOutboxById(@Param("id") Long id);

}
