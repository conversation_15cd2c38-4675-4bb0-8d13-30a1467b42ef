package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.enums.NotifyType;
import com.kerryprops.kip.bill.common.enums.SendStatus;
import com.kerryprops.kip.bill.dao.convert.NotifyTypeConverter;
import com.kerryprops.kip.bill.dao.convert.SendStatusConverter;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.time.ZonedDateTime;

@Data
@Entity
@Table(name = "tb_e_fapiao_send_record")
public class EFapiaoSendRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "bill_fapiao_id")
    private Long billFapiaoId;

    @Column(name = "batch_no")
    private String batchNo;

    @Column(name = "notify_type")
    @Convert(converter = NotifyTypeConverter.class)
    private NotifyType notifyType;

    @Column(name = "request_id")
    private String requestId;

    @Column(name = "send_status")
    @Convert(converter = SendStatusConverter.class)
    private SendStatus sendStatus;

    @Column(name = "send_desc")
    private String sendDesc;

    @Column(name = "user_id")
    private String userId;

    @Column(name = "user_name")
    private String userName;

    @Column(name = "send_time")
    private ZonedDateTime sendTime;

    @Column(name = "email")
    private String email;

    @Column(name = "sms")
    private String sms;

    @Column(name = "created_time", insertable = false, updatable = false, columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private ZonedDateTime createdTime;

    @Column(name = "updated_time", insertable = false, columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private ZonedDateTime updatedTime;

    @Column(name = "operate_user_id")
    private String operateUserId;

    @Column(name = "operate_user_email")
    private String operateUserEmail;

}
