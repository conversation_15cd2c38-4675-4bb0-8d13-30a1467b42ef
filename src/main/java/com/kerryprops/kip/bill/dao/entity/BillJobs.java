package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.enums.BillJobType;
import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 名   称：bill_jobs
 * 描   述：
 * 作   者：David <PERSON>
 * 时   间：2021/10/21 11:57:16
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Getter
@Setter
@Entity
@Table(name = "bill_jobs")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Deprecated
public class BillJobs extends BaseEntity {

    /*类型*/
    /*len: 32*/
    @Enumerated(value = EnumType.STRING)
    @Column(name = "type")
    private BillJobType type;

    /*状态*/
    /*len: 10*/
    @Column(name = "status")
    private Integer status;

}
