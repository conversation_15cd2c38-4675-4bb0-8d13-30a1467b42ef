package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.enums.InvoiceRecordStatus;
import com.kerryprops.kip.bill.common.enums.InvoiceState;
import com.kerryprops.kip.bill.common.enums.SendStatus;
import com.kerryprops.kip.bill.dao.convert.InvoiceRecordStatusConverter;
import com.kerryprops.kip.bill.dao.convert.InvoiceStateConverter;
import com.kerryprops.kip.bill.dao.convert.SendStatusConverter;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * to B - kerry bill：e-fapiao发票回写表
 *
 * <AUTHOR> Yan
 * @Date 2023-3-28
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tb_kerry_bill_e_fapiao")
@Entity
public class EFapiaoBillInvoice implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /* 单据公司 */
    @Column(name = "kco")
    private String kco = StringUtils.EMPTY;

    /* 公司 */
    @Column(name = "company_code")
    private String companyCode = StringUtils.EMPTY;

    /* 单据号，示例：19004780 */
    @Column(name = "doc")
    private int doc;

    /* 单据付款项｜示例：001 */
    @Column(name = "payment_item")
    private String paymentItem = StringUtils.EMPTY;

    /* an8| 示例：10023780 */
    @Column(name = "an8")
    private String an8 = StringUtils.EMPTY;

    /* 购方名称｜xxx有限公司 */
    @Column(name = "purchaser_name")
    private String purchaserName = StringUtils.EMPTY;

    /* 购方税号｜示例：91110105625911314P */
    @Column(name = "purchaser_tax_no")
    private String purchaserTaxNo = StringUtils.EMPTY;

    /* 购方公司地址 */
    @Column(name = "purchaser_address")
    private String purchaserAddress = StringUtils.EMPTY;

    /* 购方银行名称｜花旗银行北京分行********** */
    @Column(name = "purchaser_bank_name")
    private String purchaserBankName = StringUtils.EMPTY;

    /* 发票类型, gvat-纸质普票，gvate-电子普票，svat-专票，svate-电子专票
              ，gvatq-数电普通发票，svatq-数电专用发票，gvatz-数电纸质普通发票，svatz-数电纸质专用发票 */
    @Column(name = "invoice_type")
    private String invoiceType = StringUtils.EMPTY;

    /* 邮寄地址 */
    @Column(name = "mailing_address")
    private String mailingAddress = StringUtils.EMPTY;

    /* mcu，经营单位，Business Unit */
    @Column(name = "mcu")
    private String mcu = StringUtils.EMPTY;

    /* 账单类型｜示例：RN */
    @Column(name = "bill_type")
    private String billType = StringUtils.EMPTY;

    /* JDE合同号｜示例：250644 */
    @Column(name = "doco")
    private String doco = StringUtils.EMPTY;

    /* 缴费票据码｜示例：U02M */
    @Column(name = "bill_code")
    private String billCode = StringUtils.EMPTY;

    /* 商品货物税收分类编码 */
    @Column(name = "goods_tax_no")
    private String goodsTaxNo = StringUtils.EMPTY;

    /* 货物或应税劳务名称｜示例：电费 */
    @Column(name = "item_name")
    private String itemName = StringUtils.EMPTY;

    /* 货物规格型号｜示例：2015/07/01-2015/09/30 */
    @Column(name = "item_spec")
    private String itemSpec = StringUtils.EMPTY;

    /* 不含税金额, 单位（分）｜示例：10005 */
    @Column(name = "amount_without_tax")
    private BigDecimal amountWithoutTax;

    /* 税率 示例：6000 */
    @Column(name = "tax_rate")
    private BigDecimal taxRate;

    /* 税额, 单位（分）｜示例：830 */
    @Column(name = "tax_amount")
    private BigDecimal taxAmount;

    /* 含税金额, 单位（分） */
    @Column(name = "amount_with_tax")
    private BigDecimal amountWithTax;

    /* 是否税收优惠 */
    @Column(name = "tax_discount")
    private String taxDiscount = StringUtils.EMPTY;

    /* 税收优惠描述 */
    @Column(name = "tax_discount_desc")
    private String taxDiscountDesc = StringUtils.EMPTY;

    /* 第二字母名称，类似别名，可不填 */
    @Column(name = "second_name")
    private String secondName = StringUtils.EMPTY;

    /* 合同号，内容是doco，经过格式矫正，示例：250644 */
    @Column(name = "formated_doco")
    private String formatedDoco = StringUtils.EMPTY;

    /* 是否主要联系人， 示例：1 */
    @Column(name = "is_contact_person")
    private String isContactPerson = StringUtils.EMPTY;

    /* 是否一般纳税人, 示例：Y,N */
    @Column(name = "is_tax_payer")
    private String isTaxpayer = StringUtils.EMPTY;

    /* 税收分类说明， 示例：其他不动产经营租赁，企业管理服务 */
    @Column(name = "tax_classify_desc")
    private String taxClassifyDesc = StringUtils.EMPTY;

    /* 常量，示例：1 */
    @Column(name = "constant1")
    private String constant1 = StringUtils.EMPTY;

    /* 常量2 */
    @Column(name = "constant2")
    private String constant2 = StringUtils.EMPTY;

    /* 常量3，示例：0 */
    @Column(name = "constant3")
    private BigDecimal constant3;

    /* 常量4，示例：0 */
    @Column(name = "constant4")
    private BigDecimal constant4;

    /* 常量5，示例：0 */
    @Column(name = "constant5")
    private BigDecimal constant5;

    /* 单据号，内容上是TOKCO&TODCT&TODOC ，示例：42010RN19006196001*/
    @Column(name = "extended_doc")
    private String extendedDoc = StringUtils.EMPTY;

    /* 发票日期（可理解为JDE账单日期） */
    @Column(name = "bill_draw_date")
    private String billDrawDate = StringUtils.EMPTY;

    /* 数电票-行业特殊票种-不动产租赁场景：租赁期起，格式：yyyyMMdd */
    @Column(name = "lease_term_start")
    private String leaseTermStart = StringUtils.EMPTY;

    /* 数电票-行业特殊票种-不动产租赁场景：租赁期止，格式：yyyyMMdd */
    @Column(name = "lease_term_end")
    private String leaseTermEnd = StringUtils.EMPTY;

    /* 描述1 */
    @Column(name = "dscrp1")
    private String dscrp1 = StringUtils.EMPTY;

    /* 描述2 */
    @Column(name = "dscrp2")
    private String dscrp2 = StringUtils.EMPTY;

    /* 描述3 */
    @Column(name = "dscrp3")
    private String dscrp3 = StringUtils.EMPTY;

    /* 用户，示例：INTERFACE */
    @Column(name = "user_name_info")
    private String userName = StringUtils.EMPTY;

    /* 程序号，示例：SP */
    @Column(name = "program_id")
    private String programId = StringUtils.EMPTY;

    /* 固定 */
    @Column(name = "constant6")
    private String constant6 = StringUtils.EMPTY;

    /* JDE入表日期，示例：123064，1就是21世纪，23就是23年，064就是23年的第64天，所以123064对应的就是2023/3/5 */
    @Column(name = "create_date_jde")
    private int createDateJde;

    /* JDE入表时间 */
    @Column(name = "create_time_jde")
    private int createTimeJde;

    /* 事件点1 */
    @Column(name = "event1")
    private String event1 = StringUtils.EMPTY;

    /* 事件点2 */
    @Column(name = "event2")
    private String event2 = StringUtils.EMPTY;

    /* 事件点3 */
    @Column(name = "event3")
    private String event3 = StringUtils.EMPTY;

    /* 订单号，内容为TOKCO&TODCT&TODOC&TOSFX */
    @Column(name = "sales_bill_no")
    private String salesBillNo = StringUtils.EMPTY;

    /* 是否删除：0-未删除，1-已删除 */
    @Column(name = "is_delete")
    private int isDelete = 0;

    @Column(name = "email_send_status")
    @Convert(converter = SendStatusConverter.class)
    private SendStatus emailSendStatus = SendStatus.MSG_NOT_SEND;

    @Column(name = "sms_send_status")
    @Convert(converter = SendStatusConverter.class)
    private SendStatus smsSendStatus = SendStatus.MSG_NOT_SEND;

    @Column(name = "message_send_status")
    @Convert(converter = SendStatusConverter.class)
    private SendStatus messageSendStatus = SendStatus.MSG_NOT_SEND;

    /* JDE单元号 */
    @Column(name = "jde_unit")
    private String jdeUnit = StringUtils.EMPTY;

    /* 开票状态 */
    @Column(name = "invoice_status")
    @Convert(converter = InvoiceRecordStatusConverter.class)
    private InvoiceRecordStatus invoiceRecordStatus = InvoiceRecordStatus.PROCESSING;

    /* 销方名称 */
    @Column(name = "seller_name")
    private String sellerName = StringUtils.EMPTY;

    /* 发票PDF地址 */
    @Column(name = "pdf_url")
    private String pdfUrl = StringUtils.EMPTY;

    /* 发票OFD地址 */
    @Column(name = "ofd_url")
    private String ofdUrl = StringUtils.EMPTY;

    /* 数电xml地址 */
    @Column(name = "xml_url")
    private String xmlUrl = StringUtils.EMPTY;

    /* 数电：红冲场景回调字段：开具原因 */
    @Column(name = "making_reason")
    private String makingReason = StringUtils.EMPTY;

    /* 发票号 */
    @Column(name = "invoice_no")
    private String invoiceNo = StringUtils.EMPTY;

    /* 发票代码 */
    @Column(name = "invoice_code")
    private String invoiceCode = StringUtils.EMPTY;

    /* 业务类型 */
    @Column(name = "biz_type")
    private String bizType = StringUtils.EMPTY;

    /* 发票状态：0未知；1正常；2作废；3已红冲；4红冲票 */
    @Column(name = "state")
    @Convert(converter = InvoiceStateConverter.class)
    private InvoiceState state = InvoiceState.UNKNOWN;

    /* 失败信息描述 */
    @Column(name = "error_message")
    private String errorMessage = StringUtils.EMPTY;

    /* 开票日期 */
    @Column(name = "paper_drew_date")
    private String paperDrewDate;

    @Column(name = "created_time", insertable = false, updatable = false, columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private ZonedDateTime createdTime;

    @Column(name = "updated_time", insertable = false, columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private ZonedDateTime updatedTime;

}

