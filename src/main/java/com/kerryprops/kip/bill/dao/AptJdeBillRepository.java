package com.kerryprops.kip.bill.dao;

import com.kerryprops.kip.bill.common.jpa.JpaAndQueryDslExecutor;
import com.kerryprops.kip.bill.dao.entity.AptJdeBill;
import com.kerryprops.kip.bill.dao.entity.AptJdeBillAndCo;
import org.springframework.data.jpa.repository.Query;

import java.util.Collection;
import java.util.List;

/***********************************************************************************************************************
 * Project - accelerator
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - David <PERSON>
 * Created Date - 06/04/2021 10:39
 **********************************************************************************************************************/

public interface AptJdeBillRepository extends JpaAndQueryDslExecutor<AptJdeBill, Long> {

    @Query(value = "select distinct rd_glc from apt_sync_bills", nativeQuery = true)
    List<String> queryGlcGroup();

    List<AptJdeBill> findAllByBillNumber(String billNo);

    List<AptJdeBill> findAllByBillNumberIn(Collection<String> billNos);

    @Query(value = "select new com.kerryprops.kip.bill.dao.entity" +
            ".AptJdeBillAndCo(t.billNumber, t.rdKco) " +
            "from AptJdeBill t where t.billNumber in (?1) ")
    List<AptJdeBillAndCo> findCoByBillNoIn(Collection<String> billNos);


}
