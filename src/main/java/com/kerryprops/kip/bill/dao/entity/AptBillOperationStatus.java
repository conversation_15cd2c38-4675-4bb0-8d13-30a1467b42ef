package com.kerryprops.kip.bill.dao.entity;


import lombok.Getter;

public enum AptBillOperationStatus {

    UNKNOWN(-1, "未知"),
    PUSHED(2, "已推送"),
    PUSH_FAILED(3, "推送失败");

    @Getter
    private int code;

    @Getter
    private String info;

    AptBillOperationStatus(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

    public static AptBillOperationStatus fromCode(int index) {
        for (AptBillOperationStatus s : AptBillOperationStatus.values()) {
            if (s.code == index) {
                return s;
            }
        }
        return UNKNOWN;
    }

}
