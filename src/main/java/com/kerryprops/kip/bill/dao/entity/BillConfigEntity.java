package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 名   称：kerry_bill_config
 * 描   述：
 * 作   者：David <PERSON>
 * 时   间：2021/07/02 09:53:19
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Getter
@Setter
@Builder
@Entity
@Table(name = "kerry_bill_config")
@AllArgsConstructor
@NoArgsConstructor
public class BillConfigEntity extends BaseEntity {

    /*来自不同 服务的账单类型	len: 2*/
    @Column(name = "source_type")
    private String sourceType;

    /*账单来源的中文描述	len: 100*/
    @Column(name = "source_name")
    private String sourceName;

    /*去对应的平台获取账单的服务地址	len: 255*/
    @Column(name = "http_service")
    private String httpService;

    /*	len: 100*/
    @Column(name = "create_by")
    private String createBy;

    /*	len: 100*/
    @Column(name = "update_by")
    private String updateBy;

    /*	len: 2*/
    @Column(name = "del_flag")
    private String delFlag;

}
