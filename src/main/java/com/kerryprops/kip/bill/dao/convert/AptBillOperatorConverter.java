package com.kerryprops.kip.bill.dao.convert;

import com.kerryprops.kip.bill.dao.entity.AptBillOperator;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.springframework.stereotype.Component;

@Converter
@Component
public class AptBillOperatorConverter implements AttributeConverter<AptBillOperator, Integer> {

    @Override
    public Integer convertToDatabaseColumn(AptBillOperator status) {
        return status.getIndex();
    }

    @Override
    public AptBillOperator convertToEntityAttribute(Integer index) {
        return AptBillOperator.fromIndex(index);
    }

}
