package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.enums.BillSelectMode;
import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * BillChooseConfig.
 *
 * <AUTHOR> Yu 2025-02-27 14:59:09
 **/
@Getter
@Setter
@ToString
@Entity
@DynamicInsert
@DynamicUpdate
@Table(name = "tb_c_bill_selection_config",
        uniqueConstraints = {@UniqueConstraint(name = "uniq_room_bill_select_mode",
                columnNames = {"room_id", "building_id", "project_id"})},
        indexes = {@Index(name = "idx_project_id", columnList = "project_id"),
                @Index(name = "idx_building_id", columnList = "building_id")})
public class BillSelectConfig extends BaseEntity {

    @Schema(title = "项目唯一标识")
    @Column(name = "project_id", length = 32, nullable = false)
    private String projectId;

    @Schema(title = "楼栋id")
    @Column(name = "building_id", length = 32, nullable = false)
    private String buildingId;

    @Schema(title = "楼栋名")
    @Column(name = "building_name", length = 45, nullable = false)
    private String buildingName;

    @Schema(title = "单元id")
    @Column(name = "room_id", length = 64, nullable = false)
    private String roomId;

    @Schema(title = "单元名")
    @Column(name = "room_name", length = 45, nullable = false)
    private String roomName;

    @Schema(title = "账单选择模式")
    @Enumerated(EnumType.STRING)
    @Column(name = "bill_select_mode", length = 20, nullable = false)
    private BillSelectMode billSelectMode;

    @Schema(title = "最后操作人姓名")
    @Column(name = "operator_name", length = 50, nullable = false)
    private String operatorName;

}
