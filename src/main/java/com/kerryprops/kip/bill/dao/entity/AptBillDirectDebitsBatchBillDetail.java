package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillPushStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Deprecated
public class AptBillDirectDebitsBatchBillDetail {

    private String batchNo;

    private String billNo;

    private String category;

    private String bu;

    private String unit;

    private String an8;

    private String alph;

    private String doco;

    private Date beginDate;

    private Date endDate;

    private Integer year;

    private Integer month;

    private Integer billMonth;

    private double amt;

    private BillStatus status;

    private BillPaymentStatus paymentStatus;

    private String paymentResult;

    private BillPushStatus pushStatus;

    private String projectId;

    private String buildingId;

    private String floorId;

    private String roomId;

    private String rdGlc;

    private PositionItemResponse positionItem = new PositionItemResponse();

    private String paySession;

    private Integer deletedAt = 0;

    private Date payTime;

}
