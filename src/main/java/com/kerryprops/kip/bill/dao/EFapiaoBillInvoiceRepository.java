package com.kerryprops.kip.bill.dao;

import com.kerryprops.kip.bill.common.enums.SendStatus;
import com.kerryprops.kip.bill.common.jpa.JpaAndQueryDslExecutor;
import com.kerryprops.kip.bill.dao.entity.EFapiaoBillInvoice;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

/**
 * e-fapiao: 回写发票数据Repository类
 *
 * <AUTHOR>
 * @date 2023-3-30
 */
public interface EFapiaoBillInvoiceRepository extends JpaAndQueryDslExecutor<EFapiaoBillInvoice, Long> {

    @Transactional
    @Modifying(clearAutomatically = true)
    @Query(value = "update tb_kerry_bill_e_fapiao set `state` = :state" +
            " where sales_bill_no = :salesBillNo and invoice_no = :invoiceNo", nativeQuery = true)
    void updateStateBySalesBillNoAndInvoiceNo(@Param("salesBillNo") String salesBillNo
            , @Param("invoiceNo") String invoiceNo, @Param("state") int state);

    @Modifying
    @Query(value = "update EFapiaoBillInvoice set messageSendStatus = :status where id in :ids")
    int updateSendMessageStatusByIds(@Param("ids") Set<Long> ids, @Param("status") SendStatus status);

    @Modifying
    @Query(value = "update EFapiaoBillInvoice set emailSendStatus = :status where id in :ids")
    int updateSendEmailStatusByIds(@Param("ids") Set<Long> ids, @Param("status") SendStatus status);

    @Modifying
    @Query(value = "update EFapiaoBillInvoice set smsSendStatus = :status where id in :ids")
    int updateSendSmsStatusByIds(@Param("ids") Set<Long> ids, @Param("status") SendStatus status);

    List<EFapiaoBillInvoice> findAllBySalesBillNo(String salesBillNo);

}
