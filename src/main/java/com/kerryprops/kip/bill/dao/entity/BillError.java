package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 名   称：bill_error
 * 描   述：
 * 作   者：<PERSON>
 * 时   间：2021/10/21 12:09:08
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Getter
@Setter
@Entity
@Table(name = "bill_error")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Deprecated
public class BillError extends BaseEntity {

    /*错误类型*/
    /*len: 64*/
    @Column(name = "type")
    private String type;

    /*错误明细*/
    /*len: 65535*/
    @Column(name = "err")
    private String err;

    @Column(name = "reviewed")
    @Builder.Default
    private Integer reviewed = 0;

}
