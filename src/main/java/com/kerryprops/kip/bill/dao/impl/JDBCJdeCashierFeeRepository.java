package com.kerryprops.kip.bill.dao.impl;

import com.alibaba.fastjson.JSON;
import com.kerryprops.kip.bill.common.enums.AptPaySendJdeStatusEnum;
import com.kerryprops.kip.bill.common.utils.StringFormatUtils;
import com.kerryprops.kip.bill.config.SyncJdeConfig;
import com.kerryprops.kip.bill.dao.AptPayConfigRepository;
import com.kerryprops.kip.bill.dao.AptPayRepository;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPayConfig;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.clients.KipInvoiceClient;
import com.kerryprops.kip.bill.feign.entity.FeeConfigResponse;
import com.kerryprops.kip.bill.feign.entity.FeeConfigVo;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.utils.BillUtil;
import com.kerryprops.kip.hiveas.webservice.vo.resp.BuildingResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.common.constants.JdeConstants.JDE_BU_PREFIX_BLANK;
import static com.kerryprops.kip.bill.common.constants.JdeConstants.JDE_MULTIPLY_NUMBER_100;

/**
 * 杂费管理-JDE回写-Repository类
 *
 * <AUTHOR> Yan
 * Created Date - 2024-1-3 19:33:05
 */
@Slf4j
@Repository
public class JDBCJdeCashierFeeRepository {

    @Autowired
    @Qualifier("jdeJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private AptPayConfigRepository aptPayConfigRepository;


    @Autowired
    private AptPayRepository aptPayRepository;

    @Autowired
    private HiveAsClient hiveAsClient;

    @Autowired
    private KipInvoiceClient kipInvoiceClient;

    public int saveCashierFee(AptPay aptPay, FeeConfigVo feeConfigVo) {
        log.info("cashier_fee jdbc_jde_save_cashier_fee, apt_pay=[{}], fee_config_vo=[{}]"
                , JSON.toJSONString(aptPay), JSON.toJSONString(feeConfigVo));

        if (Objects.isNull(feeConfigVo)) {
            throw new RuntimeException("cashier_fee jdbc_jde_save_cashier_fee fee_config_not_found");
        }

        String insertSqlFormat
                = "INSERT INTO %s (MSUKID, MSEDLN, MSKCO, MSMCU, MSOBJ, MSSUB, MSSBL, MSSBLT, MSEXA, MSAA, MSHDBU, MSDMBU, MSMSMT, MSUSER, MSPID, MSPOST) " +
                "VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

        String insertSql = String.format(insertSqlFormat, SyncJdeConfig.getJdeCashierFee());
        log.info("cashier_fee jdbc_jde_save_cashier_fee insert_sql: [{}]", insertSql);

        BigDecimal uniqueKey = new BigDecimal(cutPayAct(aptPay.getPayAct()));
        String propertyManagementCo = hiveAsClient.getPropertyManagementCo(aptPay.getBuildingId());
        String companyCode = getLastestCompany(propertyManagementCo);

        String propertyManagementBu = getPropertyManagementBu(aptPay.getBuildingId());
        int line = 100000 + new Random().nextInt(900000);

        // ------- 银行科目: 付费配置 --------
        AptPayConfig aptPayConfig = aptPayConfigRepository.findTopByProjectIdAndCompanyCodeAndPaymentType(aptPay.getProjectId(), companyCode, aptPay.getPayType());
        if (Objects.isNull(aptPayConfig)) {
            throw new RuntimeException("cashier_fee jdbc_jde_save_cashier_fee apt_pay_config_not_found");
        }

        // 手续费
        BigDecimal commissionFeeTemp = BigDecimal.valueOf(aptPay.getTotalAmt() * aptPayConfig.getTax());
        BigDecimal commissionFeeMax = BigDecimal.valueOf(aptPayConfig.getMax());
        BigDecimal commissionFee = commissionFeeTemp.compareTo(commissionFeeMax) > 0
                ? commissionFeeMax : commissionFeeTemp;

        String bu = aptPayConfig.getMcu();
        BigDecimal amt = BigDecimal.valueOf(aptPay.getTotalAmt()).subtract(commissionFee);

        jdbcTemplate.update(insertSql, pstmt -> {
            int paramIndex = 1;

            pstmt.setBigDecimal(paramIndex++, uniqueKey);
            pstmt.setBigDecimal(paramIndex++, new BigDecimal(line));
            pstmt.setString(paramIndex++, companyCode);
            pstmt.setString(paramIndex++, JDE_BU_PREFIX_BLANK + bu);
            pstmt.setString(paramIndex++, aptPayConfig.getPaymentCate());
            pstmt.setString(paramIndex++, aptPayConfig.getPaymentDetail());

            setPstmt(pstmt, paramIndex, aptPay, amt);
        });

        // 有一条回写，则标记jde已回写
        aptPay.setSendJdeStatus(AptPaySendJdeStatusEnum.WRITE_TO_JDE_SUCCESS.getCode());
        aptPayRepository.save(aptPay);

        List<FeeConfigResponse> feeConfigList = kipInvoiceClient.listFeeConfigs(aptPay.getProjectId(), companyCode);
        feeConfigList = Optional.ofNullable(feeConfigList).orElse(Collections.emptyList());

        // ------- 手续费:杂费-手续费 -------
        List<FeeConfigResponse> feeConfigLevel4List = feeConfigList.stream()
                .filter(e -> "level-4".equalsIgnoreCase(e.getType()))
                .collect(Collectors.toList());

        String buLevel4;
        FeeConfigResponse feeConfigLevel4;
        if (CollectionUtils.isNotEmpty(feeConfigLevel4List)) {
            feeConfigLevel4 = feeConfigLevel4List.get(0);
            buLevel4 = StringUtils.isNotEmpty(feeConfigLevel4.getBu()) ? feeConfigLevel4.getBu() : propertyManagementBu;
        } else {
            feeConfigLevel4 = null;
            buLevel4 = propertyManagementBu;
        }

        jdbcTemplate.update(insertSql, pstmt -> {
            int paramIndex = 1;

            pstmt.setBigDecimal(paramIndex++, uniqueKey);
            pstmt.setBigDecimal(paramIndex++, new BigDecimal(line + 1));
            pstmt.setString(paramIndex++, companyCode);
            pstmt.setString(paramIndex++, JDE_BU_PREFIX_BLANK + buLevel4);
            pstmt.setString(paramIndex++, Optional.ofNullable(feeConfigLevel4)
                    .map(FeeConfigResponse::getPaymentCate).orElse(StringUtils.EMPTY));
            pstmt.setString(paramIndex++, Optional.ofNullable(feeConfigLevel4)
                    .map(FeeConfigResponse::getPaymentDetail).orElse(StringUtils.EMPTY));

            setPstmt(pstmt, paramIndex, aptPay, commissionFee);
        });

        // ------- 收入科目:杂费-二级科目 -------
        BigDecimal taxRate = new BigDecimal(feeConfigVo.getTaxRate());

        // 税额
        BigDecimal taxAmt = BigDecimal.valueOf(aptPay.getTotalAmt())
                .divide(taxRate.add(BigDecimal.ONE), 4, RoundingMode.HALF_UP).multiply(taxRate);
        String buLevel2 = StringUtils.isNotEmpty(feeConfigVo.getBu()) ? feeConfigVo.getBu() : propertyManagementBu;

        // （收款金额-税额）* -1
        BigDecimal amtLevel2 = taxAmt.subtract(BigDecimal.valueOf(aptPay.getTotalAmt()));
        jdbcTemplate.update(insertSql, pstmt -> {
            int paramIndex = 1;

            pstmt.setBigDecimal(paramIndex++, uniqueKey);
            pstmt.setBigDecimal(paramIndex++, new BigDecimal(line + 2));
            pstmt.setString(paramIndex++, companyCode);
            pstmt.setString(paramIndex++, JDE_BU_PREFIX_BLANK + buLevel2);
            pstmt.setString(paramIndex++, feeConfigVo.getPaymentCate());
            pstmt.setString(paramIndex++, feeConfigVo.getPaymentDetail());

            setPstmt(pstmt, paramIndex, aptPay, amtLevel2);
        });

        // ------- 税额:杂费-税金 -------
        List<FeeConfigResponse> feeConfigLevel3List = feeConfigList.stream()
                .filter(e -> "level-3".equalsIgnoreCase(e.getType())).collect(Collectors.toList());

        String buLevel3;
        FeeConfigResponse feeConfigLevel3;
        if (CollectionUtils.isNotEmpty(feeConfigLevel3List)) {
            feeConfigLevel3 = feeConfigLevel3List.get(0);
            buLevel3 = StringUtils.isNotEmpty(feeConfigLevel3.getBu()) ? feeConfigLevel3.getBu() : propertyManagementBu;
        } else {
            feeConfigLevel3 = null;
            buLevel3 = propertyManagementBu;
        }

        // （收款金额/(1+税率）*税率）*-1  （税率取自杂费-二级科目配置）
        BigDecimal amtLevel3 = taxAmt.multiply(BigDecimal.valueOf(-1));

        jdbcTemplate.update(insertSql, pstmt -> {
            int paramIndex = 1;

            pstmt.setBigDecimal(paramIndex++, uniqueKey);
            pstmt.setBigDecimal(paramIndex++, new BigDecimal(line + 3));
            pstmt.setString(paramIndex++, companyCode);
            pstmt.setString(paramIndex++, JDE_BU_PREFIX_BLANK + buLevel3);
            pstmt.setString(paramIndex++, Optional.ofNullable(feeConfigLevel3)
                    .map(FeeConfigResponse::getPaymentCate).orElse(StringUtils.EMPTY));
            pstmt.setString(paramIndex++, Optional.ofNullable(feeConfigLevel3)
                    .map(FeeConfigResponse::getPaymentDetail).orElse(StringUtils.EMPTY));

            setPstmt(pstmt, paramIndex, aptPay, amtLevel3);
        });

        return 1;
    }

    private void setPstmt(PreparedStatement pstmt, int paramIndex, AptPay aptPay
            , BigDecimal amt) throws SQLException {
        boolean isAn8Empty = StringUtils.isEmpty(aptPay.getAn8());
        pstmt.setString(paramIndex++, isAn8Empty ? StringUtils.EMPTY : aptPay.getAn8());
        pstmt.setString(paramIndex++, isAn8Empty ? StringUtils.EMPTY : "A");
        pstmt.setString(paramIndex++, aptPay.getComments());
        pstmt.setBigDecimal(paramIndex++, BillUtil.formatAmount(JDE_MULTIPLY_NUMBER_100.multiply(amt)));

        SimpleDateFormat sdf = new SimpleDateFormat("ddMMyyyy");
        pstmt.setString(paramIndex++, sdf.format(aptPay.getPayDate()));

        LocalDateTime localDateTime = LocalDateTime.now();
        pstmt.setString(paramIndex++, localDateTime.format(DateTimeFormatter.ofPattern("ddMMyyyy")));
        pstmt.setBigDecimal(paramIndex++, new BigDecimal(localDateTime.format(DateTimeFormatter.ofPattern("HHmmss"))));
        pstmt.setString(paramIndex++, cutLoginAccount());
        pstmt.setString(paramIndex++, "WXQ");
        pstmt.setString(paramIndex, " ");
    }

    private String getPropertyManagementBu(String buildingId) {
        BuildingResponseVo buildingResponseVo = hiveAsClient.getBuildingById(buildingId);
        if (Objects.isNull(buildingResponseVo) || Objects.isNull(buildingResponseVo.getBuilding())) {
            log.error("can_not_configured_for_building data_is_null" + buildingId);
            return StringUtils.EMPTY;
        }
        String propertyManagementBu = buildingResponseVo.getBuilding().getPropertyManagementBU();
        if (StringUtils.isEmpty(propertyManagementBu)) {
            log.error("can_not_configured_for_building property_management_bu_is_null" + buildingId);
            return StringUtils.EMPTY;
        }

        return getLastestCompany(propertyManagementBu);
    }

    // 获取最新（最后配置）的数据
    private String getLastestCompany(String companyCodes) {
        if (StringUtils.isEmpty(companyCodes)) {
            return StringUtils.EMPTY;
        }

        String[] companyCodeArray = companyCodes.split(",");
        return companyCodeArray[companyCodeArray.length - 1];
    }

    private String cutPayAct(String payAct) {
        final int PAY_ACT_LENGTH_LIMIT = 10;
        if (StringUtils.isNotEmpty(payAct) && payAct.length() > PAY_ACT_LENGTH_LIMIT) {
            int len = payAct.length();
            return payAct.substring(len - PAY_ACT_LENGTH_LIMIT, len);
        } else {
            return Optional.ofNullable(payAct).orElse(StringUtils.EMPTY);
        }
    }

    private String cutLoginAccount() {
        String userName = UserInfoUtils.getUser().getLoginAccount();

        if (StringFormatUtils.isEmailFormat(userName)) {
            int flagIndex = userName.lastIndexOf('@');
            userName = userName.substring(0, flagIndex);
        }

        final int LOGIN_ACCOUNT_LENGTH_LIMIT = 10;
        if (StringUtils.isNotEmpty(userName) && userName.length() > LOGIN_ACCOUNT_LENGTH_LIMIT) {
            return userName.substring(0, LOGIN_ACCOUNT_LENGTH_LIMIT);
        } else {
            return Optional.ofNullable(userName).orElse(StringUtils.EMPTY);
        }
    }

}

