package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 名   称：apt_bill_link
 * 描   述：
 * 作   者：David <PERSON>
 * 时   间：2021/08/27 12:03:38
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Getter
@Setter
@Entity
@Table(name = "apt_bill_link")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AptBillLink extends BaseEntity {

    /*	len: 32*/
    @Column(name = "bill_no")
    private String billNo;

    /*	len: 10*/
    @Column(name = "jde_bill_id")
    private Long jdeBillId;

    @Builder.Default
    @Column(name = "deleted_at")
    private Long deletedAt = 0l;


}
