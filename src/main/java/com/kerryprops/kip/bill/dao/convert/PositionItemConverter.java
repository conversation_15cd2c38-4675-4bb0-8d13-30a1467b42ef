package com.kerryprops.kip.bill.dao.convert;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import jakarta.persistence.AttributeConverter;
import org.springframework.stereotype.Component;

@Component
public class PositionItemConverter implements AttributeConverter<PositionItemResponse, String> {

    private final ObjectMapper objectMapper;

    public PositionItemConverter(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    public String convertToDatabaseColumn(PositionItemResponse attribute) {
        try {
            return objectMapper.writeValueAsString(attribute);
        } catch (Exception ex) {
            return null;
        }
    }

    @Override
    public PositionItemResponse convertToEntityAttribute(String dbData) {
        try {
            return objectMapper.readValue(dbData, PositionItemResponse.class);
        } catch (Exception ex) {
            return null;
        }
    }

}
