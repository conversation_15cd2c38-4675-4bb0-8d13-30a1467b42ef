package com.kerryprops.kip.bill.dao;

import com.kerryprops.kip.bill.common.jpa.JpaAndQueryDslExecutor;
import com.kerryprops.kip.bill.dao.entity.AptDirectDebitsBatchBill;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface AptDirectDebitsBatchBillRepository
        extends JpaAndQueryDslExecutor<AptDirectDebitsBatchBill, Long> {

    List<AptDirectDebitsBatchBill> findByBatchNo(String batchNo);

    List<AptDirectDebitsBatchBill> findByBatchNoAndPspName(String batchNo, String pspName);

    List<AptDirectDebitsBatchBill> findByBatchNoAndBillNoIn(String batchNo, Collection<String> billNos);

    List<AptDirectDebitsBatchBill> findByPaymentInfoId(String paymentInfoId);

    /*@Query(value = "select a.batch_no, sum(b.amt) sum_amt from tb_apt_direct_debits_batch_bill a left join apt_payment_info b " +
            "on a.payment_info_id = b.id where a.batch_no in (:bns) and b.payment_status = 'DIRECT_DEBIT_FAILED' " +
            "group by a.batch_no", nativeQuery = true)*/
    @Query(value = "select batch_no, sum(amt) sum_amt from (" +
            "select a.batch_no, b.amt, b.id from tb_apt_direct_debits_batch_bill a left join  apt_payment_info b " +
            "on a.payment_info_id = b.id where batch_no in (:bns) and b.payment_status = 'DIRECT_DEBIT_FAILED'  group by b.id, a.batch_no" +
            ") c group by batch_no", nativeQuery = true)
    List<Map<String, Object>> queryFailedAmount(@Param("bns") Collection<String> bns);

}
