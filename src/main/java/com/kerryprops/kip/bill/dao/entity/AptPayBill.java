package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;


/**
 * 名   称：wxq_sync_bills
 * 描   述：
 * 作   者：<PERSON>
 * 时   间：2021/08/25 16:17:48
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Get<PERSON>
@Setter
@Entity
@SuperBuilder
@Table(name = "apt_pay_bill")
@NoArgsConstructor
@AllArgsConstructor
public class AptPayBill extends BaseEntity {

    /*	len: 32*/
    @Column(name = "pay_id")
    private Long payId;

    /*	len: 32*/
    @Column(name = "bill_id")
    private Long billId;

    @Column(name = "bill_no")
    private String billNo;

    @Column(name = "confirmed")
    @Builder.Default
    private Integer confirmed = 0;

}
