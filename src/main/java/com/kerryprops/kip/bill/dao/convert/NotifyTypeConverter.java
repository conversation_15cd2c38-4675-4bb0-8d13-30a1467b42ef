package com.kerryprops.kip.bill.dao.convert;

import com.kerryprops.kip.bill.common.enums.NotifyType;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter
public class NotifyTypeConverter implements AttributeConverter<NotifyType, Integer> {

    @Override
    public Integer convertToDatabaseColumn(NotifyType attribute) {
        return attribute.getIndex();
    }

    @Override
    public NotifyType convertToEntityAttribute(Integer dbData) {
        return NotifyType.fromIndex(dbData);
    }

}
