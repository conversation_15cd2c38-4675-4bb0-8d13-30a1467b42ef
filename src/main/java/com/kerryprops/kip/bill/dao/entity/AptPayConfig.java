package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import com.kerryprops.kip.bill.dao.convert.PositionItemConverter;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 名   称：apt_pay_config
 * 描   述：
 * 作   者：David <PERSON>
 * 时   间：2021/08/30 15:03:37
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Getter
@Setter
@Entity
@Table(name = "apt_pay_config")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AptPayConfig extends BaseEntity {

    /*付费配置类型	len: 11*/
    @Enumerated(value = EnumType.STRING)
    @Column(name = "channel")
    private BillPayChannel channel;

    /*付费方式	len: 11*/
    @Column(name = "payment_type")
    private String paymentType;

    /*科目账	len: 11*/
    @Column(name = "payment_cate")
    private String paymentCate;

    /*明细账	len: 11*/
    @Column(name = "payment_detail")
    private String paymentDetail;

    /*总账银行账户	len: 64*/
    @Column(name = "bank_account")
    private String bankAccount;

    /*mcu	len: 64*/
    @Column(name = "mcu")
    private String mcu;

    /*	len: 10*/
    @Column(name = "project_id")
    private String projectId;

    /*	len: 10*/
    @Column(name = "building_id")
    private String buildingId;

    @Convert(converter = PositionItemConverter.class)
    @Builder.Default
    private PositionItemResponse positionItem = new PositionItemResponse();

    /*手续费率	len: 32*/
    @Column(name = "tax")
    private Double tax;

    /*封顶	len: 10*/
    @Column(name = "max")
    private Double max;

    /*备注	len: 256*/
    @Column(name = "comments")
    private String comments;

    @Column(name = "company_code")
    private String companyCode;

    /*	len: 10*/
    @Builder.Default
    @Column(name = "delete_at")
    private Long deleteAt = 0l;

    // 是否启用：1-启用，0-禁用
    @Column(name = "enabled_status")
    private Integer enabledStatus = 1;

}
