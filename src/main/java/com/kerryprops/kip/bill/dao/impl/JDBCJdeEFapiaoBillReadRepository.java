package com.kerryprops.kip.bill.dao.impl;

import com.kerryprops.kip.bill.config.EFapiaoConfig;
import com.kerryprops.kip.bill.config.SyncJdeConfig;
import com.kerryprops.kip.bill.dao.JdeEFapiaoBilReadRepository;
import com.kerryprops.kip.bill.dao.entity.EFapiaoJDEBill;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

/**
 * e-fapiao：JDE发票信息已读表（已请求开票）-Dao层操作实现类
 *
 * <AUTHOR>
 * @Date 2023-3-29
 */
@Slf4j
@Repository
public class JDBCJdeEFapiaoBillReadRepository implements JdeEFapiaoBilReadRepository {

    @Autowired
    EFapiaoConfig eFapiaoConfig;

    @Autowired
    @Qualifier("jdeJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    @Override
    public int updateStatusReaded(String kco, String billType, int doc
            , String paymentItem, String eFapiaoJdeBillReadedStaus) {
        String updateReadSqlFormat
                = "UPDATE %s SET TOEV02 = ? WHERE TODOC = ? AND TODCT = ? AND TOKCO = ? AND TOSFX = ? ";

        String updateReadSql = String.format(updateReadSqlFormat, SyncJdeConfig.getJdeBillInvoiceRead());
        log.info("e_fapiao_write_back_invoice set_read_sql: [{}]", updateReadSql);

        return jdbcTemplate.update(updateReadSql, pstmt -> {
            int paramIndex = 1;
            pstmt.setString(paramIndex++, eFapiaoJdeBillReadedStaus);
            pstmt.setInt(paramIndex++, doc);
            pstmt.setString(paramIndex++, billType);
            pstmt.setString(paramIndex++, kco);
            pstmt.setString(paramIndex, paymentItem);
        });
    }

    @Override
    public int insertReadedInfo(EFapiaoJDEBill eFapiaoJDEBill) {
        String insertSqlFormat = "INSERT INTO %s (TOKCO, TODCT, TODOC, TOSFX, TOFX01, TOAN8, TOEV02)" +
                " VALUES (?,?,?,?,?,?,1)";
        String insertSql = String.format(insertSqlFormat, SyncJdeConfig.getJdeBillInvoiceRead());
        log.info("e_fapiao_write_back_uploaded insert_sql: [{}]", insertSql);

        return jdbcTemplate.update(insertSql, pstmt -> {
            int paramIndex = 1;
            pstmt.setString(paramIndex++, eFapiaoJDEBill.getKco());
            pstmt.setString(paramIndex++, eFapiaoJDEBill.getBillType());
            pstmt.setInt(paramIndex++, eFapiaoJDEBill.getDoc());
            pstmt.setString(paramIndex++, eFapiaoJDEBill.getPaymentItem());
            pstmt.setString(paramIndex++, eFapiaoJDEBill.getKco() + eFapiaoJDEBill.getBillType()
                    + eFapiaoJDEBill.getDoc() + eFapiaoJDEBill.getPaymentItem());
            pstmt.setString(paramIndex, eFapiaoJDEBill.getAn8());
        });
    }

    @Override
    public Integer countBySalesbillNo(EFapiaoJDEBill eFapiaoJDEBill) {
        String selectSqlFormat = "SELECT count(*) FROM %s" +
                " WHERE TOKCO = ? AND TODCT = ? AND TODOC = ? AND TOSFX = ?";
        String selectSql = String.format(selectSqlFormat, SyncJdeConfig.getJdeBillInvoiceRead());

        log.info("e_fapiao_write_back_uploaded select_sql: [{}]", selectSql);

        return jdbcTemplate.query(selectSql, pstmt -> {
            int paramIndex = 1;
            pstmt.setString(paramIndex++, eFapiaoJDEBill.getKco());
            pstmt.setString(paramIndex++, eFapiaoJDEBill.getBillType());
            pstmt.setInt(paramIndex++, eFapiaoJDEBill.getDoc());
            pstmt.setString(paramIndex, eFapiaoJDEBill.getPaymentItem());
        }, rs -> {
            rs.next();
            return rs.getInt(1);
        });
    }

}

