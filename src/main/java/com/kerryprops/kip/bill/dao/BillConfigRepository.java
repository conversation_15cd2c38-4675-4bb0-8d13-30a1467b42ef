package com.kerryprops.kip.bill.dao;

import com.kerryprops.kip.bill.common.jpa.JpaAndQueryDslExecutor;
import com.kerryprops.kip.bill.dao.entity.BillConfigEntity;
import com.kerryprops.kip.bill.service.model.leg.BillConfig;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/***********************************************************************************************************************
 * Project - accelerator
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - David <PERSON>
 * Created Date - 06/04/2021 10:39
 **********************************************************************************************************************/
public interface BillConfigRepository extends JpaAndQueryDslExecutor<BillConfigEntity, Long> {

    @Query(value = "select * from kerry_bill_config where del_flag = '0'", nativeQuery = true)
    List<BillConfigEntity> selectBillConfigList(BillConfig billConfig);

}
