package com.kerryprops.kip.bill.dao.impl;

import com.kerryprops.kip.bill.config.SyncJdeConfig;
import com.kerryprops.kip.bill.dao.JDEBillRepository;
import com.kerryprops.kip.bill.dao.entity.JDEAptBill;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.dao.IncorrectResultSizeDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

import static com.kerryprops.kip.bill.dao.entity.JDEAptBill.ROW_MAPPER;

@Slf4j
@Repository
public class JDBCJdeBillRepository implements JDEBillRepository {

    private static final String QUERY_TEMPLATE = "select * from %s.F03B11 where " +
            " RPDOC=%s and RPKCO='%s' and RPDCT='%s' and RPSFX='%s'";

    @Autowired
    @Qualifier("jdeJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    @Override
    public List<JDEAptBill> findAllByCombineKeys(long documentNo, String companyCode, String type, String paymentSfx) {
        String querySql = String.format(QUERY_TEMPLATE, SyncJdeConfig.getJdeDbPrefix()
                , documentNo, companyCode, type, paymentSfx);
        try {
            return jdbcTemplate.query(querySql, ROW_MAPPER);
        } catch (IncorrectResultSizeDataAccessException e) {
            log.error("findJdeBillByCombineKeys error", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<JDEAptBill> queryPaymentStatus(String sql) {
        if (Strings.isEmpty(sql)) {
            log.info("[queryPaymentStatus]empty sql");
            return Collections.emptyList();
        }

        try {
            return jdbcTemplate.query(sql, ROW_MAPPER);
        } catch (IncorrectResultSizeDataAccessException e) {
            log.error("queryPaymentStatus error", e);
            return Collections.emptyList();
        }
    }

}
