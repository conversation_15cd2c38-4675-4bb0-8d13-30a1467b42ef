package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 名   称：kerry_bill_email_trace
 * 描   述：
 * 作   者：David <PERSON>
 * 时   间：2021/10/22 09:36:03
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Getter
@Setter
@Entity
@Table(name = "kerry_bill_email_trace")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BillEmailTrace extends BaseEntity {

    @Column(name = "batch_no")
    private String batchNo;

    @Column(name = "request_id")
    private String requestId;

    /*账单ID*/
    /*len: 10*/
    @Column(name = "bill_id")
    private Long billId;

    /*状态*/
    /*len: 10*/
    @Column(name = "status")
    private Integer status;

    @Column(name = "email")
    private String email;

    @Column(name = "email_state")
    private Boolean emailState;

    @Column(name = "message")
    private String message;


}
