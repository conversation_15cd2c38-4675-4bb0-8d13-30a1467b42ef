package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * 名   称：kerry_notice_inbox
 * 描   述：
 * 作   者：<PERSON>
 * 时   间：2021/07/05 13:40:16
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Getter
@Setter
@Entity
@Table(name = "kerry_notice_inbox")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NoticeInboxEntity extends BaseEntity {

    /*接收主体id	len: 19*/
    @Column(name = "receive_subject")
    private long receiveSubject;

    /*接收主体类型 2大楼、3公司	len: 10*/
    @Column(name = "subject_type")
    private Integer subjectType;

    /*是否已读0否、1是	len: 3*/
    @Column(name = "read_flag")
    private Boolean readFlag;

    /*阅读时间	len: 19*/
    @Column(name = "read_time")
    private Date readTime;

    /*接收的通知类型0公告，1通知	len: 10*/
    @Column(name = "notice_type")
    private Integer noticeType;

    /*发件箱id	len: 19*/
    @Column(name = "outbox_id")
    private long outboxId;

    /*所属功能 BusinessDefinitionz中取值	len: 10*/
    @Column(name = "fun_type")
    private Integer funType;

    /*模板id	len: 19*/
    @Column(name = "temp_id")
    private long tempId;

    /*账单id	len: 19*/
    @Column(name = "bill_id")
    private long billId;

    /*合同号	len: 20*/
    @Column(name = "doco")
    private String doco;

    /*用户编号	len: 20*/
    @Column(name = "an8")
    private String an8;

    /*	len: 20*/
    @Column(name = "mcu")
    private String mcu;

    /*	len: 100*/
    @Column(name = "update_by")
    private String updateBy;

    /*	len: 2*/
    @Column(name = "del_flag")
    private String delFlag;

    /*适用全部的用户角色值放0	len: 100*/
    @Column(name = "role_ids")
    private String roleIds;


}
