package com.kerryprops.kip.bill.dao.convert;

import com.kerryprops.kip.bill.common.enums.ContentTypeEnum;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

/**
 * kerry bill: B端-已读信息类型Converter类
 *
 * <AUTHOR>
 * @date 2023-6-13
 */
@Converter
public class ContentTypeConverter implements AttributeConverter<ContentTypeEnum, Integer> {

    @Override
    public Integer convertToDatabaseColumn(ContentTypeEnum attribute) {
        return attribute.getIndex();
    }

    @Override
    public ContentTypeEnum convertToEntityAttribute(Integer dbData) {
        return ContentTypeEnum.fromIndex(dbData);
    }

}
