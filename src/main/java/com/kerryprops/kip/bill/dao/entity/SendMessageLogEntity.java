package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * 名   称：kerry_send_message_log
 * 描   述：
 * 作   者：<PERSON>
 * 时   间：2021/07/05 13:40:19
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Get<PERSON>
@Setter
@Entity
@Table(name = "kerry_send_message_log")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SendMessageLogEntity extends BaseEntity {

    /*	len: 19*/
    @Column(name = "temp_id")
    private long tempId;

    /*	len: 19*/
    @Column(name = "created_time")
    private Date createdTime;

    /*	len: 65535*/
    @Column(name = "send_tels")
    private String sendTels;

    /*	len: 19*/
    @Column(name = "plan_time")
    private Date planTime;

    /*	len: 19*/
    @Column(name = "status")
    private long status;

    /*	len: 255*/
    @Column(name = "created_by")
    private String createdBy;

    /*	len: 19*/
    @Column(name = "local_day")
    private long localDay;

    /*	len: 19*/
    @Column(name = "user_id")
    private long userId;

    /*	len: 19*/
    @Column(name = "del_flag")
    private long delFlag;

    /*	len: 19*/
    @Column(name = "draw_id")
    private long drawId;


}
