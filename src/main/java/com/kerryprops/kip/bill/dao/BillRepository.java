package com.kerryprops.kip.bill.dao;

import com.kerryprops.kip.bill.common.jpa.JpaAndQueryDslExecutor;
import com.kerryprops.kip.bill.dao.entity.BillEntity;
import com.kerryprops.kip.bill.service.model.leg.Bill;
import com.kerryprops.kip.bill.service.model.s.BillSendHistoryReqDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/***********************************************************************************************************************
 * Project - accelerator
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - David Wei
 * Created Date - 06/04/2021 10:39
 **********************************************************************************************************************/

public interface BillRepository extends JpaAndQueryDslExecutor<BillEntity, Long> {

    @Query(value = "select distinct tp_doco from kerry_bill where del_flag = '0' and tp_mcu in (:bus) order by tp_doco asc", nativeQuery = true)
    List<String> selectDocos(@Param("bus") List<String> bus);

    @Query(value = "select tp_dct from kerry_bill where del_flag = '0' and tp_mcu in (:bus) group by tp_dct order by tp_dct asc", nativeQuery = true)
    List<String> selectDcts(@Param("bus") List<String> bus);

    @Query(value = "select distinct tp_fyr from kerry_bill where tp_ev01='Y' and del_flag ='0' order by tp_fyr desc", nativeQuery = true)
    List<Integer> selectBillDistinctYears();

    @Query(value = "select distinct tp_fyr, tp_pn from kerry_bill where tp_ev01='Y' and del_flag ='0' order by tp_fyr desc,tp_pn desc", nativeQuery = true)
    List<Bill> selectBillDistinctYearMonths();

    @Query(value = "select * from kerry_bill where tp_co = :tpCo and tp_an8 = :tpAn8 and" +
            " tp_crtutime <= :tpCrtutime and tp_dct = :tpDct and tp_doco = :tpDoco and" +
            " tp_fyr = :tpFyr and tp_pn = :tpPn and tp_status = 0 and tp_ev01='Y' and del_flag ='0'",
            nativeQuery = true)
    List<BillEntity> selectOldBill(@Param("tpCo") String tpCo, @Param("tpAn8") String tpAn8,
                                   @Param("tpDct") String tpDct, @Param("tpDoco") String tpDoco,
                                   @Param("tpFyr") Integer tpFyr, @Param("tpPn") Integer tpPn,
                                   @Param("tpCrtutime") Date tpCrtutime);

    @Query(value = "select * from kerry_bill where tp_co = :tpCo and tp_an8 = :tpAn8 and" +
            " tp_dct = :tpDct and tp_doco = :tpDoco and" +
            " tp_fyr = :tpFyr and tp_pn = :tpPn and tp_ev01='Y' and del_flag ='0'",
            nativeQuery = true)
    List<BillEntity> selectDuplicateBill(@Param("tpCo") String tpCo, @Param("tpAn8") String tpAn8,
                                         @Param("tpDct") String tpDct, @Param("tpDoco") String tpDoco,
                                         @Param("tpFyr") Integer tpFyr, @Param("tpPn") Integer tpPn);

    @Transactional
    @Modifying(clearAutomatically = true)
    @Query(value = "update  kerry_bill set mail_read_time= sysdate() where id = :id and mail_read_time is null", nativeQuery = true)
    int updateReadTime(@Param("id") Long id);

    @Transactional
    @Modifying(clearAutomatically = true)
    @Query(value = "update kerry_bill " +
            "set tp_status = :tpStatus, " +
            "mail_status = :mailStatus, " +
            "mail_date = STR_TO_DATE(:mailDate, '%Y-%m-%d %H:%i:%s'), " +
            "update_by = :updateBy " +
            "where id = :id",
            nativeQuery = true)
    int updateSendMailInfo(@Param("id") Long id,
                           @Param("tpStatus") Integer tpStatus,
                           @Param("mailStatus") Integer mailStatus,
                           @Param("mailDate") String mailDate,
                           @Param("updateBy") String updateBy);

    @Transactional
    @Modifying(clearAutomatically = true)
    @Query(value = "update kerry_bill " +
            "set tp_status = :tpStatus, " +
            "email_status = :emailStatus, " +
            "email_date = STR_TO_DATE(:emailDate, '%Y-%m-%d %H:%i:%s'), " +
            "update_by = :updateBy " +
            "where id = :id",
            nativeQuery = true)
    int updateSendEMailInfo(@Param("id") Long id,
                            @Param("tpStatus") Integer tpStatus,
                            @Param("emailStatus") Integer emailStatus,
                            @Param("emailDate") String emailDate,
                            @Param("updateBy") String updateBy);

    @Transactional
    @Modifying(clearAutomatically = true)
    @Query(value = "update kerry_bill set email_err = :emailErr where id = :id", nativeQuery = true)
    int updateEmailErr(@Param("id") Long id, @Param("emailErr") String emailErr);

    @Transactional
    @Modifying(clearAutomatically = true)
    @Query(value = "update kerry_bill set email_status = :emailStatus, email_err = :emailErr where id = :id", nativeQuery = true)
    int updateEmailErr(@Param("id") Long id, @Param("emailStatus") Integer emailStatus, @Param("emailErr") String emailErr);

    @Transactional
    @Modifying(clearAutomatically = true)
    @Query(value = "update kerry_bill set tp_status = :tpStatus, email_status = :emailStatus where id = :id", nativeQuery = true)
    int updateEmailStatus(@Param("id") Long id, @Param("tpStatus") Integer tpStatus, @Param("emailStatus") Integer emailStatus);

    @Transactional
    @Modifying(clearAutomatically = true)
    @Query(value = "update kerry_bill set email_status = :emailStatus where id = :id", nativeQuery = true)
    int updateEmailStatus(@Param("id") Long id, @Param("emailStatus") Integer emailStatus);

    @Query(value = "SELECT *  FROM kerry_bill b " +
            "where b.del_flag='0'  " +
            " and b.tp_ev01 = 'Y' " +
            " and b.tp_status<>0" +
            " and if(:tpAlph is not null and :tpAlph !='', b.tp_alph like CONCAT('%', :tpAlph, '%'), 1 = 1 )" +
            " and if(:#{#bshReqDto.tpMcu} is not null and :#{#bshReqDto.tpMcu} !='', b.tp_mcu = :#{#bshReqDto.tpMcu}, 1 = 1 )" +
            " and if(:#{#bshReqDto.tpDoco} is not null and :#{#bshReqDto.tpDoco} !='', b.tp_doco = :#{#bshReqDto.tpDoco}, 1 = 1 )" +
            " and if(:#{#bshReqDto.tpDct} is not null and :#{#bshReqDto.tpDct} !='', b.tp_dct = :#{#bshReqDto.tpDct}, 1 = 1 )" +
            " and if(:#{#bshReqDto.tpFyr} is not null, b.tp_fyr = :#{#bshReqDto.tpFyr}, 1 = 1 )" +
            " and if(:#{#bshReqDto.tpPn} is not null, b.tp_pn = :#{#bshReqDto.tpPn}, 1 = 1 )" +
            " and if(COALESCE(:bus) is not null, b.tp_mcu in (:bus), 1 = 1 )" +
            " and (b.email_date is not null or b.mail_date is not null) " +
            " and if(:#{#bshReqDto.emailDateStart} is not null and :#{#bshReqDto.emailDateStart} != '', " +
            "((b.email_date is not null and DATE_FORMAT(b.email_date, '%Y-%m-%d') >= :#{#bshReqDto.emailDateStart}) " +
            "or " +
            "(b.email_date is null and DATE_FORMAT(b.mail_date, '%Y-%m-%d') >= :#{#bshReqDto.emailDateStart}))" +
            ", 1 = 1)" +
            " and if(:#{#bshReqDto.emailDateEnd} is not null and :#{#bshReqDto.emailDateEnd} != '', " +
            "((b.email_date is not null and DATE_FORMAT(b.email_date, '%Y-%m-%d') <= :#{#bshReqDto.emailDateEnd}) " +
            "or " +
            "(b.email_date is null and DATE_FORMAT(b.mail_date, '%Y-%m-%d') <= :#{#bshReqDto.emailDateEnd}))" +
            ", 1 = 1)" +
            " order by ifnull(b.email_date, b.mail_date) desc, b.tp_fyr desc, b.tp_pn desc "
            , nativeQuery = true)
    Page<BillEntity> queryList(@Param("bshReqDto") BillSendHistoryReqDto billSendHistoryReqDto,
                               @Param("tpAlph") String tpAlph,
                               @Param("bus") List<String> bus,
                               Pageable pageable);

    @Query(value = "SELECT *  FROM kerry_bill b " +
            "where b.del_flag='0'  " +
            " and b.tp_ev01 = 'Y' " +
            " and b.tp_status<>0" +
            " and if(:tpAlph is not null and :tpAlph !='', b.tp_alph like CONCAT('%', :tpAlph, '%'), 1 = 1 )" +
            " and if(:#{#bshReqDto.tpMcu} is not null and :#{#bshReqDto.tpMcu} !='', b.tp_mcu = :#{#bshReqDto.tpMcu}, 1 = 1 )" +
            " and if(:#{#bshReqDto.tpDoco} is not null and :#{#bshReqDto.tpDoco} !='', b.tp_doco = :#{#bshReqDto.tpDoco}, 1 = 1 )" +
            " and if(:#{#bshReqDto.tpDct} is not null and :#{#bshReqDto.tpDct} !='', b.tp_dct = :#{#bshReqDto.tpDct}, 1 = 1 )" +
            " and if(:#{#bshReqDto.tpFyr} is not null, b.tp_fyr = :#{#bshReqDto.tpFyr}, 1 = 1 )" +
            " and if(:#{#bshReqDto.tpPn} is not null, b.tp_pn = :#{#bshReqDto.tpPn}, 1 = 1 )" +
            " and if(COALESCE(:bus) is not null, tp_mcu in (:bus), 1 = 1 )" +
            " and (b.email_date is not null or b.mail_date is not null) " +
            " and if(:#{#bshReqDto.emailDateStart} is not null and :#{#bshReqDto.emailDateStart} != '', " +
            "((b.email_date is not null and DATE_FORMAT(b.email_date, '%Y-%m-%d') >= :#{#bshReqDto.emailDateStart}) " +
            "or " +
            "(b.email_date is null and DATE_FORMAT(b.mail_date, '%Y-%m-%d') >= :#{#bshReqDto.emailDateStart}))" +
            ", 1 = 1)" +
            " and if(:#{#bshReqDto.emailDateEnd} is not null and :#{#bshReqDto.emailDateEnd} != '', " +
            "((b.email_date is not null and DATE_FORMAT(b.email_date, '%Y-%m-%d') <= :#{#bshReqDto.emailDateEnd}) " +
            "or " +
            "(b.email_date is null and DATE_FORMAT(b.mail_date, '%Y-%m-%d') <= :#{#bshReqDto.emailDateEnd}))" +
            ", 1 = 1)" +
            " order by ifnull(b.email_date, b.mail_date) desc, b.tp_fyr desc, b.tp_pn desc ", nativeQuery = true)
    List<BillEntity> queryList(@Param("bshReqDto") BillSendHistoryReqDto billSendHistoryReqDto,
                               @Param("tpAlph") String tpAlph,
                               @Param("bus") List<String> bus);

}
