package com.kerryprops.kip.bill.webservice.vo.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;

/**
 * 名   称：apt_pay_config
 * 描   述：
 * 作   者：<PERSON>
 * 时   间：2021/08/30 15:03:37
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Data
@Schema
public class AptPaySaveVo {

    @Schema(title = "单元ID")
    @NotNull
    private String roomId;

    @Schema(title = "JDE BU")
    @NotNull
    private String bu;

    @Schema(title = "JDE单元ID")
    @NotNull
    private String unit;

    @Schema(title = "JDE地址号")
    @NotNull
    private String an8;

    @Schema(title = "JDE姓名")
    @NotNull
    private String alph;

    @Schema(title = "JDE合同号")
    @NotNull
    private String doco;

    @Schema(title = "单元ID")
    @NotNull
    private String payDesc;

    @Schema(title = "支付时间")
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date payDate;

    @Schema(title = "支付配置ID")
    @NotNull
    private Long payConfigId;

    @Schema(title = "支付方式")
    @NotNull
    private String payType;

    @Schema(title = "预收科目,写死A003")
    @NotNull
    private String paymentCate;

    @Schema(title = "支付流水")
    @NotNull
    private String payTranx;

    @Schema(title = "合计金额")
    @NotNull
    private double totalAmt;

    @Schema(title = "补充信息")
    private String comments;

}
