package com.kerryprops.kip.bill.webservice.vo.resp;

import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 名   称：apt_pay_config
 * 描   述：
 * 作   者：<PERSON>
 * 时   间：2021/08/30 15:03:37
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Data
@Schema
public class AptPayConfigVo {

    @Schema(title = "id")
    private Long id;

    @Schema(title = "付费渠道类型")
    @Enumerated(value = EnumType.STRING)
    @NotNull
    private BillPayChannel channel;

    @Schema(title = "付费配置类型")
    @Enumerated(value = EnumType.STRING)
    private String paymentType;

    @Schema(title = "科目账")
    private String paymentCate;

    @Schema(title = "明细账")
    private String paymentDetail;

    @Schema(title = "总账银行账户")
    private String bankAccount;

    @Schema(title = "银行科目MCU")
    private String mcu;

    @Schema(title = "楼盘CODE")
    private String projectId;

    @Schema(title = "楼栋ID")
    private String buildingId;

    @Schema(title = "地址明细")
    private PositionItemResponse positionItem;

    @Schema(title = "手续费率")
    private Double tax;

    @Schema(title = "封顶")
    private Double max;

    @Schema(title = "备注")
    private String comments;

    @Schema(name = "是否启用：1-启用，0-禁用")
    private Integer enabledStatus;

    @Schema(title = "公司")
    private String companyCode;

}
