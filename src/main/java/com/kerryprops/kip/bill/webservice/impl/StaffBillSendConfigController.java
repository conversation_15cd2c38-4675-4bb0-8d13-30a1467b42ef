package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.aop.RedisLock;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.exceptions.RestInvalidParamException;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.config.DataMigrationConfig;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.IBillSendConfigService;
import com.kerryprops.kip.bill.webservice.vo.req.BillSendConfigReqVo;
import com.kerryprops.kip.bill.webservice.vo.req.StaffBillSendConfigInsertVo;
import com.kerryprops.kip.bill.webservice.vo.req.StaffBillSendConfigUpdateVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillPayer;
import com.kerryprops.kip.bill.webservice.vo.resp.BillSendConfigResource;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.NOT_LOGIN;

@Slf4j
@RestController
@Tag(name = "办公商场-S端账单管理-账单发送配置")
@RequestMapping(value = "/s/bill/sendconfig", produces = MediaType.APPLICATION_JSON_VALUE)
public class StaffBillSendConfigController {

    @Autowired
    private IBillSendConfigService configService;

    @Autowired
    private DataMigrationConfig dataMigrationConfig;


    /**
     * 合同号/付款人信息为可输入，也可下拉选择
     */
    @PostMapping("/create")
    @Operation(summary = "创建配置")
    public RespWrapVo<BillSendConfigResource> create(@Valid @RequestBody StaffBillSendConfigInsertVo saveReqVo) {
        LoginUser loginUser = UserInfoUtils.getUser();
        if (Objects.isNull(loginUser)) {
            throw new RestInvalidParamException(NOT_LOGIN);
        }

        BillSendConfigResource resource = configService.saveBillSendConfig(saveReqVo);
        RespWrapVo<BillSendConfigResource> respWrapVo = new RespWrapVo<>(resource);
        return respWrapVo;
    }

    @PutMapping("/update")
    @Operation(summary = "更新配置")
    public RespWrapVo<BillSendConfigResource> update(@Valid @RequestBody StaffBillSendConfigUpdateVo updateReqVo) {
        LoginUser loginUser = UserInfoUtils.getUser();
        if (Objects.isNull(loginUser)) {
            throw new RestInvalidParamException(NOT_LOGIN);
        }

        BillSendConfigResource resource = configService.updateBillSendConfig(updateReqVo);
        RespWrapVo<BillSendConfigResource> respWrapVo = new RespWrapVo<>(resource);
        return respWrapVo;
    }

    @GetMapping("/list")
    @Operation(summary = "查询配置")
    public Page<BillSendConfigResource> list(@SortDefault.SortDefaults(value = {
            @SortDefault(sort = "doco", direction = Sort.Direction.ASC)
            , @SortDefault(sort = "createdTime", direction = Sort.Direction.DESC)}) Pageable pageable
            , @Valid @ModelAttribute BillSendConfigReqVo vo) {
        return configService.searchBillSendConfig(pageable, vo);
    }

    @GetMapping("/{projectId}/payers")
    @Operation(summary = "模糊查询当前楼盘下的付款人列表")
    public List<BillPayer> fuzzyQueryPayers(@PathVariable("projectId") String projectId
            , @RequestParam(value = "query", required = false) String query) {
        return configService.fuzzyQueryPayers(projectId, query);
    }

    @DeleteMapping
    @Operation(summary = "根据路径ID删除")
    public RespWrapVo<Boolean> deleteById(@RequestParam("ids") List<Long> ids) {
        boolean result = configService.deleteBillSendConfig(ids);
        return new RespWrapVo<>(result);
    }

    @GetMapping("/syncMcus")
    @Operation(summary = "同步Mcu和JDE单元号")
    @RedisLock(key = "kip:billing:s:syncMcuFromTenantBills", expire = 90L)
    public RespWrapVo<Boolean> syncMcuFromTenantBills() {
        boolean result = configService.syncMcuFromTenantBills();
        return new RespWrapVo<>(result);
    }

    @GetMapping("/syncBAccounts")
    @Operation(summary = "批量查询企业版账号（B端账号）")
    @RedisLock(key = "kip:billing:s:syncEnterpriseAccounts", expire = 30L)
    public RespWrapVo<Boolean> syncEnterpriseAccounts() {
        boolean result = configService.syncEnterpriseAccounts();
        return new RespWrapVo<>(result);
    }

/*    @PostMapping("/sync")
    @Operation(summary = "从Profile-Service迁移账单接收人数据")
    public Object sync() {
        List<String> projectIds = new ArrayList<>();
        String configuredProjectStr = dataMigrationConfig.getProjects();
        if (StringUtils.isNotEmpty(configuredProjectStr)) {
            projectIds = Arrays.stream(StringUtils.split(configuredProjectStr, ",")).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(projectIds)) {
            log.error("project NOT found in current configuration");
            return new RespWrapVo("Processed 0 project");
        }

        Map<String, String> resultMap = Maps.newHashMap();
        for (String projectId : projectIds) {
            String result = configService.syncFromProfileService(projectId);
            resultMap.put(projectId, result);
        }
        return resultMap;
    }*/


/*    @PostMapping("/sync/{projectId}")
    @Operation(summary = "从Profile-Service迁移账单接收人数据")
    public String sync(@PathVariable("projectId") String projectId) {
        return configService.syncFromProfileService(projectId);
    }*/


    @GetMapping("/id/{id}")
    public BillSendConfigResource queryConfig(@PathVariable("id") long id) {
        return configService.queryById(id);
    }

}
