package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.QAptBill;
import com.kerryprops.kip.bill.service.impl.AptBillService;
import com.kerryprops.kip.bill.webservice.StaffSyncAptBillFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/***********************************************************************************************************************
 * Project - accelerator
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - David Wei
 * Created Date - 06/03/2021 14:05
 **********************************************************************************************************************/

@Slf4j
@RestController
public class StaffSyncAptBillController implements StaffSyncAptBillFacade {

    @Autowired
    private AptBillService billService;

    @Autowired
    private AptBillRepository billRepository;

    @Override
    public RespWrapVo<Boolean> sync(@PathVariable("projectId") String projectId) {
        billService.syncAptBill(projectId);
        return new RespWrapVo<>(true);
    }

    @Override
    public RespWrapVo<Boolean> syncHive() {
        billService.syncHive();
        return new RespWrapVo<>(true);
    }

    @Override
    public RespWrapVo<Boolean> verifyUser(@RequestParam("roomId") String roomId, @RequestParam("billNo") String billNo) {
        if (StringUtils.isAnyEmpty(roomId, billNo)) {
            throw new RuntimeException("roomId or billNo is empty.");
        }
        //Roger确认，用户绑定的"JDE账单"是指AN8，"JDE账单"名称也将修改为"业主ID"。只需要验证历史所有数据是否存在此记录。9/9/2021
        Iterable<AptBill> billIterable = billRepository.findAll(QAptBill.aptBill.deletedAt.eq(0).and(QAptBill.aptBill.an8.eq(billNo)).and(QAptBill.aptBill.roomId.eq(roomId)));
        boolean isExist = false;
        if (billIterable != null && billIterable.iterator() != null && billIterable.iterator().hasNext()) {
            isExist = true;
        }
        return new RespWrapVo<>(isExist);
    }

}
