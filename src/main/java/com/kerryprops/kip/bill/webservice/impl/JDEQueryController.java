package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.JDEQueryService;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.sql.SQLException;
import java.sql.SQLSyntaxErrorException;
import java.sql.SQLTimeoutException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Pattern;

/**
 * JDE Administrator Operations Controller.
 * This controller provides secure access to JDE data for administrators only.
 *
 * <AUTHOR> Yan
 * <AUTHOR>
 * @since 2025-5-13
 **/
@Slf4j
@Hidden
@RestController
@RequiredArgsConstructor
@Validated
@RequestMapping("/admin/jde")
public class JDEQueryController {

    private static final int MAX_PAGE_SIZE = 1000;

    private static final int QUERY_TIMEOUT_SECONDS = 300;

    private static final Pattern SELECT_PATTERN = Pattern.compile("^\\s*SELECT\\s+", Pattern.CASE_INSENSITIVE);

    private final JDEQueryService jdeQueryService;

    /**
     * IMPORTANT: This API is for internal development team use ONLY.
     * It allows direct SQL execution without parameterization, which could be a security risk.
     * Access is restricted to admin users.
     * 
     * Retrieves data from JDE database using a SELECT query.
     *
     * @param queryRequest The query request containing SQL
     * @param page         The page number (0-based) for pagination
     * @param size         The page size for pagination
     * @return Paginated query results or error message
     */
    @PostMapping("/query")
    public ResponseEntity<?> getJDEData(@Valid @RequestBody JDEQueryRequest queryRequest,
                                        @RequestParam(defaultValue = "0") int page,
                                        @RequestParam(defaultValue = "100") int size) {

        try {
            validateAdmin();

            // Validate and sanitize pagination parameters
            var sanitizedSize = Math.min(Math.max(1, size), MAX_PAGE_SIZE);
            var sanitizedPage = Math.max(0, page);

            // Get the SQL query from the request and validate
            var sql = Optional.ofNullable(queryRequest.sql())
                              .map(String::trim)
                              .filter(s -> !s.isEmpty())
                              .orElseThrow(() -> new IllegalArgumentException("Query cannot be empty"));

            log.info("Executing JDE query. Page: {}, Size: {}", sanitizedPage, sanitizedSize);

            // Validate that it's a SELECT query
            if (!isSelectQuery(sql)) {
                log.warn("Attempt to execute non-SELECT query: {}", maskSensitiveData(sql));
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                                     .body(new ErrorResponse("Only SELECT statements are allowed"));
            }

            // Add pagination to the query if not already present
            var paginatedSql = addPaginationToQuery(sql, sanitizedPage, sanitizedSize);

            // Execute the query through the service
            var result = jdeQueryService.executeQuery(paginatedSql, QUERY_TIMEOUT_SECONDS);

            if (result.data()
                      .isEmpty()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                                     .body(new ErrorResponse("No data found for the query"));
            }

            // Create a response with pagination metadata
            var response = new QueryResultResponse(result.data(), result.count(), sanitizedPage, sanitizedSize,
                                                   calculateTotalPages(result.count(), sanitizedSize));

            return ResponseEntity.ok(response);

        } catch (IllegalArgumentException e) {
            log.warn("Invalid request: {}", e.getMessage());
            return ResponseEntity.badRequest()
                                 .body(new ErrorResponse(e.getMessage()));
        } catch (SecurityException e) {
            log.error("Security violation: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                                 .body(new ErrorResponse("Access denied: Administrator privileges required"));
        } catch (SQLSyntaxErrorException e) {
            log.error("SQL syntax error: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                                 .body(new ErrorResponse("Invalid SQL syntax: " + e.getMessage()));
        } catch (SQLTimeoutException e) {
            log.error("SQL query timeout: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT)
                                 .body(new ErrorResponse("Query execution timed out. Please simplify your query."));
        } catch (SQLException e) {
            log.error("Database error executing query", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                 .body(new ErrorResponse("Database error: " + e.getMessage()));
        } catch (Exception e) {
            log.error("Unexpected error executing query", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                 .body(new ErrorResponse("An unexpected error occurred: " + e.getMessage()));
        }
    }

    /**
     * Checks if the provided SQL is a SELECT query.
     *
     * @param sql The SQL query to validate
     * @return true if it's a SELECT query, false otherwise
     */
    private boolean isSelectQuery(String sql) {
        return Optional.ofNullable(sql)
                       .map(String::trim)
                       .map(s -> SELECT_PATTERN.matcher(s)
                                               .find())
                       .orElse(false);
    }

    /**
     * Adds pagination to the SQL query if not already present.
     * This is a simplified implementation and may need to be adjusted based on the specific Oracle version.
     *
     * @param sql  The original SQL query
     * @param page The page number (0-based)
     * @param size The page size
     * @return SQL query with pagination
     */
    private String addPaginationToQuery(String sql, int page, int size) {
        var offset = page * size;
        return String.format("SELECT * FROM (SELECT a.*, ROWNUM rnum FROM (%s) a WHERE ROWNUM <= %d) WHERE rnum > %d",
                             sql, offset + size, offset);
    }


    /**
     * Validates that the current user has admin privileges.
     *
     * @throws SecurityException If the user is not an admin
     */
    private void validateAdmin() {
        if (!Boolean.TRUE.equals(UserInfoUtils.isSuperAdmin())) {
            throw new SecurityException("Administrator privileges required");
        }
    }

    /**
     * Masks sensitive data in SQL queries for logging purposes.
     *
     * @param sql The SQL query to mask
     * @return Masked SQL query
     */
    private String maskSensitiveData(String sql) {
        return Optional.ofNullable(sql)
                       .map(s -> s.replaceAll("(?i)(password|secret|key|credential)\\s*=\\s*'[^']*'",
                                              "$1='***'")
                                  .replaceAll("(?i)(password|secret|key|credential)\\s*=\\s*\"[^\"]*\"",
                                              "$1=\"***\""))
                       .orElse("");
    }

    /**
     * Request object for JDE queries.
     */
    public record JDEQueryRequest(@NotBlank(message = "SQL query cannot be empty") String sql) {

    }

    /**
     * Calculate the total number of pages based on count and page size.
     *
     * @param count Total number of records
     * @param size  Page size
     * @return Number of pages
     */
    private int calculateTotalPages(int count, int size) {
        return size > 0 ? (int) Math.ceil((double) count / size) : 0;
    }

    /**
     * Response object for query results.
     */
    public record QueryResult(List<Map<String, Object>> data, int count) {

        public QueryResult(List<Map<String, Object>> data) {
            this(data, data.size());
        }

    }

    /**
     * Enhanced response object with pagination metadata.
     */
    public record QueryResultResponse(List<Map<String, Object>> data, int count, int page, int size, int totalPages) {

    }


    /**
     * Error response object.
     */
    public record ErrorResponse(String message, long timestamp) {

        public ErrorResponse(String message) {
            this(message, System.currentTimeMillis());
        }

    }

}
