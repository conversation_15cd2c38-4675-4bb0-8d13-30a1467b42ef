package com.kerryprops.kip.bill.webservice.vo.req;

import com.google.common.collect.ImmutableList;
import com.kerryprops.kip.bill.common.enums.AptPayDeleteStatusEnum;
import com.kerryprops.kip.bill.common.enums.AptPaySendJdeStatusEnum;
import com.kerryprops.kip.bill.common.enums.AptPayVerifyStatus;
import com.kerryprops.kip.bill.common.jpa.QueryFilter;
import com.kerryprops.kip.bill.dao.entity.QAptPay;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.querydsl.core.types.Predicate;
import lombok.Data;

import java.util.List;
import java.util.Optional;

import static com.kerryprops.kip.bill.common.enums.BillPayModule.CASHIER_FEE;

/**
 * 杂费配置-回写JDE-DO
 *
 * <AUTHOR>
 * Created Date - 2024-1-3 19:33:05
 */
@Data
public class CashierFeeSendJdeDO implements QueryFilter {

    private String projectId;

    @Override
    public List<Optional<Predicate>> predicates() {
        ImmutableList.Builder<Optional<Predicate>> builder = ImmutableList.builder();
        builder.add(Optional.ofNullable(projectId).map(QAptPay.aptPay.projectId::eq))
                .add(Optional.of(CASHIER_FEE).map(QAptPay.aptPay.billPayModule::eq))
                .add(Optional.ofNullable(UserInfoUtils.maxBindingScope()).map(QAptPay.aptPay.buildingId::in))
                .add(Optional.of(AptPayDeleteStatusEnum.NOT_DELETED.getCode()).map(QAptPay.aptPay.deletedAt::eq))
                .add(Optional.of(AptPaySendJdeStatusEnum.NOT_YET.getCode()).map(QAptPay.aptPay.sendJdeStatus::eq))
                .add(Optional.of(AptPayVerifyStatus.VERIFIED).map(QAptPay.aptPay.verifyStatus::eq));
        return builder.build();
    }

}
