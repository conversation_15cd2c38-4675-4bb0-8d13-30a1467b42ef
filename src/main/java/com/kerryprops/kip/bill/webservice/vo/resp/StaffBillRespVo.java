package com.kerryprops.kip.bill.webservice.vo.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.Set;

/**
 * 账单查询的 view
 */
@Data
@Schema
public class StaffBillRespVo {

    @Schema(title = "账单在KIP平台ID")
    private Long id;

    @Schema(title = "jde合同号")
    private String tpDoco;

    @Schema(title = "用户编号")
    private String tpAn8;

    @Schema(title = "用户名称")
    private String tpAlph;

    @Schema(title = "账单类型")
    private String tpDct;

    @Schema(title = "账单类型名称")
    private String tpDl01;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(title = "账单打印时间")
    private String formatDate;

    @Schema(title = "建筑物编号")
    private String tpMcu;

    @Schema(title = "账单单元")
    private String tpUnit;

    @Schema(title = "账单年")
    private Integer tpFyr;

    @Schema(title = "账单月")
    private Integer tpPn;

    @Schema(title = "收件人列表")
    private Set<StaffBillReceiverRespVo> userInfoList;

    @Schema(title = "账单文件的链接地址")
    private String fileUrl;

    @Schema(title = "账单文件名")
    private String tpGtfilenm;

    @Schema(title = "账单状态 0未发送、5发送成功、10发送失败")
    private Integer tpStatus;

    @Schema(title = "账单站内信发送状态")
    private Integer mailStatus;

    @Schema(title = "账单邮件发送状态")
    private Integer emailStatus;

    @Schema(title = "账单的邮件解析结果")
    private String emailErr;

    @Schema(title = "查看状态：0未读 1已读")
    private Integer readStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(title = "查看时间")
    private Date mailReadTime;

    @Schema(title = "是否能够发送账单: true可以 false不可以")
    private boolean checkedFlag;

    private Integer sourceId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(title = "移除时间")
    private Date deleteTime;

    @Schema(title = "移除人")
    private String deleteBy;

    @Schema(title = "楼栋ID")
    private String buildingId;

    @Schema(title = "楼栋名称")
    private String buildingName;

}
