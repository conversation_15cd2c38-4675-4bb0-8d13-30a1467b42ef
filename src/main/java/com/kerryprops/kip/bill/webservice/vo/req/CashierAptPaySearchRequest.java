package com.kerryprops.kip.bill.webservice.vo.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.ImmutableList;
import com.kerryprops.kip.bill.common.enums.CashierPayStatus;
import com.kerryprops.kip.bill.common.enums.InvoicedStatus;
import com.kerryprops.kip.bill.common.jpa.QueryFilter;
import com.kerryprops.kip.bill.dao.entity.QAptPay;
import com.querydsl.core.types.Predicate;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Data
public class CashierAptPaySearchRequest implements QueryFilter {

    @Schema(title = "楼盘ID", required = true)
    @NotBlank
    private String projectId;

    @Schema(title = "楼栋ID")
    private String buildingId;

    @Schema(title = "单元ID")
    private String roomId;

    // 状态
    @Schema(title = "状态")
    private CashierPayStatus status;

    // 支付方式，比如："其他付费"
    @Schema(title = "支付方式")
    private List<String> payType;

    @Schema(title = "支付开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payDateStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "支付结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payDateEnd;

    @Schema(title = "是否已开票状态")
    private InvoicedStatus invoicedStatus;

    @Override
    public List<Optional<Predicate>> predicates() {
        var aptPayQuery = QAptPay.aptPay;
        ImmutableList.Builder<Optional<Predicate>> builder = ImmutableList.builder();
        builder.add(Optional.of(projectId).map(aptPayQuery.projectId::eq))
                .add(Optional.ofNullable(buildingId).map(aptPayQuery.buildingId::eq))
                .add(Optional.ofNullable(roomId).map(aptPayQuery.roomId::eq))
                .add(Optional.ofNullable(payDateStart).map(aptPayQuery.createTime::goe))
                .add(Optional.ofNullable(payDateEnd).map(aptPayQuery.createTime::loe))
                .add(Optional.ofNullable(invoicedStatus).map(aptPayQuery.invoicedStatus::eq));

        if (CollectionUtils.isNotEmpty(payType)) {
            builder.add(Optional.of(payType).map(aptPayQuery.payType::in));
        }
        return builder.build();
    }

}
