package com.kerryprops.kip.bill.webservice;

import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.entity.InvoiceRecord;
import com.kerryprops.kip.bill.webservice.impl.InvoiceRecordController;
import com.kerryprops.kip.bill.webservice.vo.req.CallBackKerryInvoiceMainVo;
import com.kerryprops.kip.bill.webservice.vo.req.InvoiceUploadResource;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Tag(name = "c端发票业务")
@RequestMapping(value = "/c/bill/invoice", produces = MediaType.APPLICATION_JSON_VALUE)
public interface InvoiceRecordFacade {

    @PostMapping("/invoiceUpload")
    @Operation(summary = "账单开票申请")
    RespWrapVo<InvoiceRecord> invoiceUpload(@Valid @RequestBody InvoiceUploadResource resource);


    @GetMapping("/record")
    @Operation(summary = "查询订单的开票记录信息")
    RespWrapVo<InvoiceRecord> getInvoiceRecord(@RequestParam("orderNo") String orderNo);

    @PostMapping("/callback")
    @Operation(summary = "账单开票回调")
    InvoiceRecordController.XForceResponse invoiceCallback(@RequestBody CallBackKerryInvoiceMainVo mainVo);

}
