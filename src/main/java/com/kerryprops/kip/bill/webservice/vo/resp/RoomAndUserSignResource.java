package com.kerryprops.kip.bill.webservice.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 账单查询的 view
 */
@Data
@Schema
public class RoomAndUserSignResource implements ISignInfoResource {

    @Schema(title = "是否已经签约物业费代扣服务")
    private boolean signedDirectDebits;

    @Schema(title = "此用户账号是否为【物业费代扣服务】的签署人账号")
    private boolean isDirectDebitsSignatory;

    @Schema(title = "此单元是否存在多个有效的合约，如果被用户重复签约，需要排查原因")
    private boolean isDuplicateSigned;

    @Schema(title = "签约的代扣平台。wechatpay、alipay")
    private String signedPsp;

}
