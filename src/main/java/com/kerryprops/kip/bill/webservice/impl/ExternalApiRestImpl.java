package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.enums.PaymentCateEnum;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.AptJdeBillRepository;
import com.kerryprops.kip.bill.webservice.ExternalApiRest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@RestController
public class ExternalApiRestImpl implements ExternalApiRest {

    @Autowired
    private AptJdeBillRepository aptJdeBillRepository;

    @Override
    public RespWrapVo<List<String>> queryGlcGroup() {
        List<String> glcGroup = aptJdeBillRepository.queryGlcGroup();
        glcGroup.add(PaymentCateEnum.A003.name());
        glcGroup.add(PaymentCateEnum.A004.name());
        List<String> cos = glcGroup.stream()
                .filter(StringUtils::isNotBlank)
                .distinct()
                .sorted(Comparator.naturalOrder())
                .collect(Collectors.toList());
        return new RespWrapVo<>(cos);
    }

}
