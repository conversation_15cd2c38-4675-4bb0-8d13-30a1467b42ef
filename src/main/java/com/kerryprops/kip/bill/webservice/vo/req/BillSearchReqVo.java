package com.kerryprops.kip.bill.webservice.vo.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 账单查询的 view
 */
@Data
@Schema
public class BillSearchReqVo extends BaseReqVo {

    private static final long serialVersionUID = 1L;

    /**
     * 账单年
     */
    @Schema(title = "账单年")
    private Integer tpFyr;

    /**
     * 账单月
     */
    @Schema(title = "账单月")
    private Integer tpPn;

    /**
     * jde合同号
     */
    @Schema(title = "jde合同号", example = "32001")
    private String tpDoco;

    @Schema(title = "公司名称")
    private String tpAlph;

    @Schema(title = "bu")
    private String tpMcu;

    @Schema(title = "楼盘Code")
    private String projectId;

    /**
     * 用户编号
     */
    @Schema(title = "用户编号", example = "20275864")
    private String tpAn8;

    /**
     * 账单单元
     */
    @Schema(title = "账单单元", example = "B1-B39")
    private String tpUnit;

    /**
     * 账单状态 0未发送、5发送成功、10发送失败;15已删除；20文件缺失
     */
    @Schema(title = "账单状态 0未发送、5发送成功、10发送失败", example = "0")
    private Integer tpStatus;

    /**
     * 账单站内信发送状态 0未发送，5已发送
     */
    @Schema(title = "账单站内信发送状态", example = "0")
    private Integer mailStatus;

    /**
     * 账单邮件发送状态 0未发送，5已发送 10发送失败 15部分成功 30都成功
     */
    @Schema(title = "账单邮件发送状态", example = "0")
    private Integer emailStatus;

    /**
     * 账单打印时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(title = "账单打印时间", example = "2021-07-02")
    private String formatDate;


    /**
     * 账单类型
     */
    @Schema(title = "账单类型", example = "DN")
    private String tpDct;

    /**
     * 用户阅读状态 0 未读、1已读  不管是哪里进行阅读了都是设置为已读
     */
    @Schema(title = "用户阅读状态 0 未读、1已读  不管是哪里进行阅读了都是设置为已读", example = "1")
    private Integer readStatus;

    @Schema(title = "是否移除 0 正常 1 已移除")
    private String delFlag;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(title = "账单移除时间范围开始")
    private Date deleteTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(title = "账单移除时间范围结束")
    private Date deleteTimeEnd;

}
