package com.kerryprops.kip.bill.webservice.vo.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import com.kerryprops.kip.bill.common.enums.InvoicedStatus;
import com.kerryprops.kip.bill.common.enums.PaymentCateEnum;
import com.kerryprops.kip.bill.common.enums.PaymentPayType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 名   称：apt_offline_pay
 * 描   述：
 * 作   者：<PERSON>
 * 时   间：2021/08/31 15:38:36
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AptPayVo {

    @Schema(title = "")
    private Long id;

    @Schema(title = "业主姓名")
    private String alph;

    @Schema(title = "费用类型说明")
    //收费类型, 例如：协议代扣
    @Enumerated(value = EnumType.STRING)
    private BillPayChannel payChannel;

    @Schema(title = "描述")
    private String payDesc;

    @Schema(title = "收费时间段开始")
    private String beginDate;

    @Schema(title = "收费时间段结束")
    private String endDate;

    @Schema(title = "支付配置ID")
    private Long payConfigId;

    @Schema(title = "支付方式")
    // 支付方式
    private String payType;

    @Schema(title = "科目账")
    private String paymentCate;

    @Schema(title = "手续费率")
    private double tax;

    @Schema(title = "手续费")
    private double taxAmt;

    @Schema(title = "合计金额(实收金额)")
    private Double totalAmt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(title = "支付时间")
    private Date payDate;

    @Schema(title = "支付流水")
    private String payTranx;

    @Schema(title = "收款号")
    private String payAct;

    @Schema(title = "支付详情")
    private String payDetail;

    @Schema(title = "是否已复核")
    private Integer verifyStatus;

    @Schema(title = "是否写入JDE")
    private Integer sendJdeStatus;

    @Schema(title = "")
    private String projectId;

    @Schema(title = "")
    private String buildingId;

    @Schema(title = "")
    private String floorId;

    @Schema(title = "")
    private String roomId;

    @Schema(title = "")
    private PositionItemResponse positionItem;

    @Schema(title = "备注")
    private String comments;

    @Schema(title = "是否已删除 0 没有 1 已删除")
    private Long deletedAt;

    @Schema(title = "移除人")
    private String deletedBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(title = "移除时间")
    private Date deletedTime;

    @Schema(title = "")
    private String createBy;

    @Schema(title = "")
    private Date createTime;

    @Schema(title = "")
    private Date updateTime;

    @Schema(title = "预收金额")
    private BigDecimal advanceAmount;

    @Schema(title = "账单支付id(业务单号)")
    private String paymentInfoId;

    @Schema(title = "开票金额")
    private BigDecimal canInvoiceBillAmount;

    @Schema(title = "预收账单能否开票标识")
    private String isAdvanceBilling = "0";

    @Schema(title = "是否已开票状态")
    private InvoicedStatus invoicedStatus;

    public String getPaymentCate() {
        if (PaymentCateEnum.UNKNOWN.name().equals(paymentCate)) {
            return null;
        }
        return paymentCate;
    }

    public String getPayType() {
        if (PaymentPayType.UNKNOWN.getInfo().equals(payType)) {
            return null;
        }
        return payType;
    }

}
