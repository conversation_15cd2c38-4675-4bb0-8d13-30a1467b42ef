package com.kerryprops.kip.bill.webservice;

import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptOtherPayConfigSaveVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptOtherPayConfigUpdateVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptPayConfigSaveVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptPayConfigSearchVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptPayConfigUpdateVo;
import com.kerryprops.kip.bill.webservice.vo.resp.AptPayConfigVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/***********************************************************************************************************************
 * Project - accelerator
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - David Wei
 * Created Date - 06/03/2021 14:05
 **********************************************************************************************************************/

@Tag(name = "公寓小区-S端付费配置管理")
@RequestMapping(value = "/s/apt/payconfig", produces = MediaType.APPLICATION_JSON_VALUE)
public interface StaffAptPayConfigFacade {

    @PostMapping("/save")
    @Operation(summary = "保存费用配置")
    RespWrapVo<Boolean> save(@Valid @RequestBody AptPayConfigSaveVo saveReqVo);

    @PutMapping("/update")
    @Operation(summary = "更新费用配置")
    RespWrapVo<Boolean> update(@Valid @RequestBody AptPayConfigUpdateVo updateReqVo);

    @PostMapping("/other/save")
    @Operation(summary = "保存其他配置")
    RespWrapVo<Boolean> saveOtherPay(@Valid @RequestBody AptOtherPayConfigSaveVo saveReqVo);

    @PutMapping("/other/update")
    @Operation(summary = "更新其他配置")
    RespWrapVo<Boolean> updateOtherPay(@Valid @RequestBody AptOtherPayConfigUpdateVo updateReqVo);

    @GetMapping("/{id}")
    @Operation(summary = "根据路径ID查询")
    RespWrapVo<AptPayConfigVo> getById(@PathVariable("id") Long id);

    @GetMapping("/paymentType")
    @Operation(summary = "查询支付方式")
    RespWrapVo<List<String>> getPaymentType(@RequestParam(value = "projectId", required = false) String projectId);

    @DeleteMapping("/{id}")
    @Operation(summary = "根据路径ID查询")
    RespWrapVo<Boolean> deleteById(@PathVariable("id") Long id);

    @PutMapping("/ids/{id}/disable")
    @Operation(summary = "根据路径ID禁用")
    AptPayConfigVo disableById(@PathVariable("id") Long id);

    @PutMapping("/ids/{id}/enable")
    @Operation(summary = "根据路径ID启用")
    AptPayConfigVo enableById(@PathVariable("id") Long id);

    @GetMapping("/search")
    @Operation(summary = "查询所有对象列表")
    RespWrapVo<Page<AptPayConfigVo>> search(@SortDefault.SortDefaults(value = {
            @SortDefault(sort = "createTime", direction = Sort.Direction.DESC),
            @SortDefault(sort = "updateTime", direction = Sort.Direction.DESC)}) Pageable pageable,
                                            @ModelAttribute AptPayConfigSearchVo searchReqVo);

}
