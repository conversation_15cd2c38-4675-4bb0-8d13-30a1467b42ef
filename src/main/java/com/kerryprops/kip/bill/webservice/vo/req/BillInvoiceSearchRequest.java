package com.kerryprops.kip.bill.webservice.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class BillInvoiceSearchRequest {

    @Schema(title = "建筑物")
    private String mcu;

    @Schema(title = "楼栋")
    private List<String> buildingIds;

    @Schema(title = "合同号")
    private String doco;

    @Schema(title = "站内信发送状态。0: MSG_NOT_SEND, 3: MSG_SENDING, 5: MSG_SENT, 10: MSG_FAILED, 15: MSG_PARTIAL_SUCCESS, 30: MSG_SUCCESS")
    private Integer messageStatus;

    @Schema(title = "邮件发送状态。0: MSG_NOT_SEND, 3: MSG_SENDING, 5: MSG_SENT, 10: MSG_FAILED, 15: MSG_PARTIAL_SUCCESS, 30: MSG_SUCCESS")
    private Integer emailStatus;

    @Schema(title = "短信发送状态。0: MSG_NOT_SEND, 3: MSG_SENDING, 5: MSG_SENT, 10: MSG_FAILED, 15: MSG_PARTIAL_SUCCESS, 30: MSG_SUCCESS")
    private Integer smsStatus;

    @Schema(title = "楼盘ID")
    private String projectId;

    @Schema(title = "购方公司an8", example = "20275864")
    private String an8;

    @Schema(title = "购方公司alph")
    private String alpha;

    @Schema(title = "销方公司名称（模糊搜索）")
    private String sellerName;

    @Schema(title = "销方公司CO")
    private String companyCode;

    @Schema(title = "业务类型: LEA-租赁账单，UTI-电表系统")
    private String bizType;

    @Schema(title = "发票状态: 正常、作废、红冲")
    private String invoiceStatus;

    @Schema(title = "发票日期: yyyy-MM-dd")
    private String issuanceDate;

    @Schema(title = "发票号")
    private String invoiceNo;

    @Schema(description = "发票类型. gvat:增值税普通发票, svat:增值税专用发票, gvate:增值税电子普通发票, svate:增值税电子专用发票" +
            ", gvatq:数电普通发票, svatq:数电专用发票, gvatz:数电纸质普通发票, svatz:数电纸质专用发票")
    private String invoiceType;

}
