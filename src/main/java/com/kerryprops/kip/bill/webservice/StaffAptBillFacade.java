package com.kerryprops.kip.bill.webservice;

import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptBillExportReqVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptBillManageSearchReqVo;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffAptBillManageRespVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;
import java.util.Map;

/***********************************************************************************************************************
 * Project - accelerator
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - David Wei
 * Created Date - 06/03/2021 14:05
 **********************************************************************************************************************/

@Tag(name = "公寓小区-S端账单管理")
@RequestMapping(value = "/s/apt/bill", produces = MediaType.APPLICATION_JSON_VALUE)
public interface StaffAptBillFacade {

    @GetMapping("/room/queryPaymentStatus")
    @Operation(summary = "获取账单支付状态下拉选项")
    RespWrapVo<Map<String, String>> queryPaymentStatus();

    @GetMapping("/room/queryAlphs/{projectId}")
    @Operation(summary = "获取付款人下拉选项")
    RespWrapVo<List<String>> queryAlphs(@PathVariable("projectId") String projectId, String roomId);

    @GetMapping("/room/queryCategorys/{projectId}")
    @Operation(summary = "获取费项下拉选项")
    RespWrapVo<List<String>> queryCategorys(@PathVariable("projectId") String projectId, String roomId);

    @GetMapping("/room/bills")
    @Operation(summary = "查询电子账单明细列表")
    RespWrapVo<Page<StaffAptBillManageRespVo>> list(@SortDefault.SortDefaults(value = {
            @SortDefault(sort = "year", direction = Sort.Direction.DESC)
            , @SortDefault(sort = "month", direction = Sort.Direction.DESC)
            , @SortDefault(sort = "beginDate", direction = Sort.Direction.ASC)
            , @SortDefault(sort = "endDate", direction = Sort.Direction.ASC)}) Pageable pageable
            , @ModelAttribute AptBillManageSearchReqVo vo);

    /**
     * 全量推送指定楼盘账单
     */
    @PostMapping("/push/{projectId}")
    @Operation(summary = "推送账单")
    RespWrapVo<Boolean> pushAll(@PathVariable("projectId") String projectId);

    /**
     * 按过滤条件推送账单
     */
    @PostMapping("/pushAll")
    @Operation(summary = "推送账单（带过滤条件全量推送）")
    RespWrapVo<Boolean> pushConditional(@RequestBody AptBillManageSearchReqVo vo);

    @PostMapping("/pushSelected")
    @Operation(summary = "推送账单（多选场景)")
    RespWrapVo<Boolean> pushSelected(@RequestBody List<Long> billIds);

    @PostMapping("/export")
    @Operation(summary = "导出账单")
    void export(@RequestBody AptBillExportReqVo exportReqVo, HttpServletResponse response);


//    @GetMapping(value = "/{id}")
//    @Operation(summary = "获取电子账单详细信息")
//    RespWrapVo<BillDetailRespVo> getInfo(@PathVariable("id") Long id);
//
//    @PostMapping("/sendBillList")
//    @Operation(summary = "发送账单")
//    RespWrapVo<Integer> sendBillList(@RequestBody BillSendReqVo inputVo);
//
//    @GetMapping("/history")
//    @ApiOperation(value = "查询账单发送历史" )
//    RespWrapVo<Page<StaffBillHistoryRespVo>> sendHistory(@SortDefault.SortDefaults(value = {
//            @SortDefault(sort = "createTime", direction = Sort.Direction.DESC),
//            @SortDefault(sort = "updateTime", direction = Sort.Direction.DESC)}) Pageable pageable,
//                                                                @ModelAttribute BillSendHistoryReqVo billSendHistoryReqVo);
//    @GetMapping("/rooms")
//    @ApiOperation(value = "查询电子账单明细列表" )
//    RespWrapVo<Page<StaffAptRoomRespVo>> rooms(@SortDefault.SortDefaults(value = {
//            @SortDefault(sort = "createTime", direction = Sort.Direction.DESC),
//            @SortDefault(sort = "updateTime", direction = Sort.Direction.DESC)}) Pageable pageable,
//                                               @ModelAttribute List<Long> roomIds);

}
