package com.kerryprops.kip.bill.webservice.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class BizBillInvoiceResource {

    @Schema(title = "发票类型")
    public String invoiceType;

    @Schema(title = "发票类型名称")
    public String invoiceTypeName;

    @Schema(title = "Bill invoice ID")
    private Long id;

    @Schema(title = "业务类型")
    private String bizType;

    @Schema(title = "购方公司")
    private String purchaserName;

    @Schema(title = "销方公司")
    private String sellerName;

    @Schema(title = "发票日期")
    private String issuanceDate;

    @Schema(title = "发票号码")
    private String invoiceNo;

    @Schema(title = "发票金额")
    private BigDecimal invoiceAmt;

    @Schema(title ="数电票-行业特殊票种-不动产租赁场景：租赁期起，格式：yyyyMMdd ")
    private String leaseTermStart;

    @Schema(title ="数电票-行业特殊票种-不动产租赁场景：租赁期止，格式：yyyyMMdd")
    private String leaseTermEnd;

    @Schema(title = "发票pdf链接")
    private String invoiceUrl;

    @Schema(title = "发票ofd链接")
    private String invoiceOfdUrl;

    @Schema(title = "发票xml链接")
    private String invoiceXmlUrl;

    @Schema(title = "发票状态: 正常、作废、红冲")
    private String invoiceState;

    @Schema(title = "对当前用户是否已读：true-已读，false-未读")
    private Boolean isRead;

}
