package com.kerryprops.kip.bill.webservice.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class BillPayer {

    @Schema(title = "用户名称")
    private String tpAlph;

    @Schema(title = "用户编号")
    private String tpAn8;

}
