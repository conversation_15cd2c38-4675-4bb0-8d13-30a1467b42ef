package com.kerryprops.kip.bill.webservice.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema
public class PositionItemResponse {

    @Schema(title = "楼盘名")
    private String projectName;

    @Schema(title = "楼栋名")
    private String buildingName;

    @Schema(title = "楼层名")
    private String floorName;

    @Schema(title = "房间名")
    private String roomName;

}
