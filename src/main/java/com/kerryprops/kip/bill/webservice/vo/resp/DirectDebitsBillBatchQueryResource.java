package com.kerryprops.kip.bill.webservice.vo.resp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.kerryprops.kip.bill.common.enums.DirectDebitsBatchStatus;
import com.kerryprops.kip.bill.config.ZonedDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

@Data
public class DirectDebitsBillBatchQueryResource {

    private String batchNo;

    @Schema(title = "S端创建人")
    private String createdBy;

    @Schema(title = "S端更新人")
    private String lastModifiedBy;

    @Schema(title = "所有状态：AWAIT(待发送)，SENT(已发送)，LAPSED(已失效)")
    private DirectDebitsBatchStatus status;

    private String pspName;

    private String projectId;

    private String projectName;

    private Integer billCount;

    private Integer paymentConfirmedCount;

    private Integer billSentCount;

    @Schema(title = "账单截止日期")
    private String closingMonth;

    @Schema(title = "批次头生成日期")
    @JsonSerialize(using = ZonedDateTimeSerializer.class)
    private ZonedDateTime createdTime;

    @JsonSerialize(using = ZonedDateTimeSerializer.class)
    private ZonedDateTime updatedTime;

    private BigDecimal directDebitFailedAmount = BigDecimal.ZERO;

}
