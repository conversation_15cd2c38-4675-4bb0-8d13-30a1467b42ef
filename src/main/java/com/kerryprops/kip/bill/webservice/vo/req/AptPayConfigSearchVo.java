package com.kerryprops.kip.bill.webservice.vo.req;

import com.google.common.collect.ImmutableList;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import com.kerryprops.kip.bill.common.jpa.QueryFilter;
import com.kerryprops.kip.bill.dao.entity.QAptPayConfig;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.querydsl.core.types.Predicate;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Optional;

/**
 * 名   称：apt_pay_config
 * 描   述：
 * 作   者：<PERSON>
 * 时   间：2021/08/30 15:03:37
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema
public class AptPayConfigSearchVo implements QueryFilter {

    @Schema(title = "id")
    private Long id;

    @Schema(title = "楼盘Id")
    private String projectId;

    @Schema(title = "付费渠道")
    @Enumerated(value = EnumType.STRING)
    private BillPayChannel channel;

    @Schema(title = "付费方式")
    private String paymentType;

    @Schema(title = "科目账")
    private String paymentCate;

    @Schema(title = "明细账")
    private String paymentDetail;

    @Schema(title = "总账银行账户")
    private String bankAccount;

//	@Schema(title = "mcu")
//	private String mcu;

    @Schema(title = "手续费率")
    private Double tax;

    @Schema(title = "封顶")
    private Double max;

    @Schema(title = "备注")
    private String comments;

    @Schema(title = "公司")
    private String companyCode;

    @Override
    public List<Optional<Predicate>> predicates() {
        return ImmutableList.<Optional<Predicate>>builder()
                .add(Optional.ofNullable(0l).map(QAptPayConfig.aptPayConfig.deleteAt::eq))
                .add(Optional.ofNullable(maxBindingScope()).map(QAptPayConfig.aptPayConfig.projectId::in))
                .add(Optional.ofNullable(channel).map(QAptPayConfig.aptPayConfig.channel::eq))
                .add(Optional.ofNullable(id).map(QAptPayConfig.aptPayConfig.id::eq))
                .add(Optional.ofNullable(projectId).map(QAptPayConfig.aptPayConfig.projectId::eq))
                .add(Optional.ofNullable(paymentType).map(QAptPayConfig.aptPayConfig.paymentType::eq))
                .add(Optional.ofNullable(companyCode).map(QAptPayConfig.aptPayConfig.companyCode::eq))
                .build();
    }

    public List<String> maxBindingScope() {
        LoginUser loginUser = UserInfoUtils.getUser();
        if (loginUser == null) {
            throw new RuntimeException("not login.");
        }
        if (loginUser.isSuperAdmin()) {
            return null;
        }
        return loginUser.toProjectIdList();
    }

}
