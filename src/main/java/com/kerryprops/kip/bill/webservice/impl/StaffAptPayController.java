package com.kerryprops.kip.bill.webservice.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.kerryprops.kip.bill.common.aop.RedisLock;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.enums.AptPayVerifyStatus;
import com.kerryprops.kip.bill.common.enums.AptSyncJobStatus;
import com.kerryprops.kip.bill.common.enums.BillCommonSettingEnum;
import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import com.kerryprops.kip.bill.common.enums.BillPayModule;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.common.enums.JobType;
import com.kerryprops.kip.bill.common.enums.PaymentCateEnum;
import com.kerryprops.kip.bill.common.enums.PaymentPayType;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.utils.DiffFieldUtils;
import com.kerryprops.kip.bill.common.utils.IdWorker;
import com.kerryprops.kip.bill.common.utils.jde.OracleSql;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.config.SyncJdeConfig;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptJdeBillRepository;
import com.kerryprops.kip.bill.dao.AptPayBillRepository;
import com.kerryprops.kip.bill.dao.AptPayConfigRepository;
import com.kerryprops.kip.bill.dao.AptPayRepository;
import com.kerryprops.kip.bill.dao.AptPaymentBillRepository;
import com.kerryprops.kip.bill.dao.AptPaymentInfoRepository;
import com.kerryprops.kip.bill.dao.AptSyncJdeJobLogRepository;
import com.kerryprops.kip.bill.dao.AptSyncJdeJobRepository;
import com.kerryprops.kip.bill.dao.AptSyncPaidBillToJdeRepository;
import com.kerryprops.kip.bill.dao.AptSyncPayToJdeRepository;
import com.kerryprops.kip.bill.dao.BillCommonSettingRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptJdeBill;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPayBill;
import com.kerryprops.kip.bill.dao.entity.AptPayConfig;
import com.kerryprops.kip.bill.dao.entity.AptPaymentBill;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.dao.entity.AptSyncJdeJob;
import com.kerryprops.kip.bill.dao.entity.AptSyncJdeJobLog;
import com.kerryprops.kip.bill.dao.entity.AptSyncPaidBillToJde;
import com.kerryprops.kip.bill.dao.entity.AptSyncPayToJde;
import com.kerryprops.kip.bill.dao.entity.BillCommonSetting;
import com.kerryprops.kip.bill.dao.entity.QAptBill;
import com.kerryprops.kip.bill.dao.entity.QAptJdeBill;
import com.kerryprops.kip.bill.dao.entity.QAptPayBill;
import com.kerryprops.kip.bill.dao.entity.QBillCommonSetting;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.clients.MessageClient;
import com.kerryprops.kip.bill.feign.entity.EmailSendCommand;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.AptBillDirectDebitsBatchBillService;
import com.kerryprops.kip.bill.service.AptBillOperationService;
import com.kerryprops.kip.bill.service.AptSyncPayStatusService;
import com.kerryprops.kip.bill.service.PaymentBillService;
import com.kerryprops.kip.bill.service.impl.AptBillService;
import com.kerryprops.kip.bill.service.model.AptPaymentTimestamp;
import com.kerryprops.kip.bill.service.model.JdeConfirmResult;
import com.kerryprops.kip.bill.service.model.s.AptBillSearchReqBo;
import com.kerryprops.kip.bill.service.model.s.AptPayBo;
import com.kerryprops.kip.bill.service.model.s.AptPayInvoiceBo;
import com.kerryprops.kip.bill.service.model.s.BillInvoiceCalcDto;
import com.kerryprops.kip.bill.utils.BillUtil;
import com.kerryprops.kip.bill.webservice.StaffAptPayFacade;
import com.kerryprops.kip.bill.webservice.vo.req.AptPaySaveVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptPaySearchVo;
import com.kerryprops.kip.bill.webservice.vo.resp.AptPayDetailVo;
import com.kerryprops.kip.bill.webservice.vo.resp.AptPayExportVo;
import com.kerryprops.kip.bill.webservice.vo.resp.AptPayVo;
import com.kerryprops.kip.bill.webservice.vo.resp.OperationChangedFiledRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import com.kerryprops.kip.bill.webservice.vo.resp.RoomAn8RespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffAptBillRespVo;
import com.kerryprops.kip.hiveas.feign.dto.resp.RoomRespDto;
import com.kerryprops.kip.hiveas.webservice.resource.resp.BuildingSimple;
import com.kerryprops.kip.hiveas.webservice.resource.resp.FloorSimple;
import com.kerryprops.kip.hiveas.webservice.resource.resp.ProjectSimple;
import com.kerryprops.kip.hiveas.webservice.resource.resp.RoomResp;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.dsl.BooleanExpression;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IteratorUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.common.constants.AppConstants.ALERT_MAIL_RECEIVERS;
import static com.kerryprops.kip.bill.common.enums.PayCancelTypeEnum.ADMIN_CANCELLED;
import static com.kerryprops.kip.bill.common.utils.BillingFun.exportByEasyExcel;
import static com.kerryprops.kip.bill.log4j.BSConversationFilter.refreshCorrelationId;

/**
 * 1. super admin: can do any thing;
 * 2. other: can do any thing under bu
 */
@Slf4j
@RestController
public class StaffAptPayController implements StaffAptPayFacade {

    private static final String NO_PERMISSION_TIPS = "no_permission";

    private final BigDecimal BIG_DECIMAL_100 = new BigDecimal("100");

    private final String FEE_TYPE_RECEIVABLE_AMOUNT = "A"; // 应收金额

    private final String FEE_TYPE_ADVANCE_PAYMENT_AMOUNT = "U"; // 预收金额

    @Autowired
    private AptPayRepository aptPayRepository;

    @Autowired
    private AptPaymentInfoRepository aptPaymentInfoRepository;

    @Autowired
    private AptBillRepository aptBillRepository;

    @Autowired
    private AptSyncJdeJobRepository syncJdeJobRepository;

    @Autowired
    private AptSyncJdeJobLogRepository syncJdeJobLogRepository;

    @Autowired
    private AptSyncPayToJdeRepository syncJdeLogRepository;

    @Autowired
    private AptPayConfigRepository payConfigRepository;

    @Autowired
    private AptBillRepository billRepository;

    @Autowired
    private AptPayBillRepository payBillRepository;

    @Autowired
    private AptPaymentBillRepository aptPaymentBillRepository;

    @Autowired
    private AptSyncPaidBillToJdeRepository paidBillToJdeRepository;

    @Autowired
    private AptJdeBillRepository jdeBillRepository;

    @Autowired
    private HiveAsClient hiveAsClient;

    @Value("${env}")
    private String env;

    @Autowired
    private BillCommonSettingRepository billCommonSettingRepository;

    @Autowired
    private AptBillService aptBillService;

    @Autowired
    private AptSyncPayStatusService aptSyncPayStatusService;

    @Autowired
    private MessageClient messageClient;

    @Autowired
    private AptBillOperationService operationService;

    @Autowired
    private AptBillDirectDebitsBatchBillService batchBillService;

    @Autowired
    private PaymentBillService paymentBillService;

    @Override
    public RespWrapVo<List<Map<String, String>>> queryPaymentCate() {
        return new RespWrapVo(PaymentCateEnum.paymentCates);
    }

    /**
     * 首页/账单及收款/收款记录/录入收款信息
     * <p>
     * 手动录入收款信息到 apt_pay 表中。
     */
    @Override
    public RespWrapVo<Boolean> save(@Valid @RequestBody AptPaySaveVo saveReqVo) {
        // query room info from hive
        RespWrapVo<RoomResp> roomRespVoRespWrapVo = hiveAsClient.getRoomById(saveReqVo.getRoomId());
        if (!RespWrapVo.isResponseValidWithData(roomRespVoRespWrapVo)) {
            throw new RuntimeException("room not found: " + saveReqVo.getRoomId());
        }
        RoomResp roomRespVo = roomRespVoRespWrapVo.getData();
        BuildingSimple buildingSimple = roomRespVo.getBuilding();
        ProjectSimple projectSimple = roomRespVo.getProject();
        RoomRespDto roomResp = roomRespVo.getRoom();
        FloorSimple floorSimple = roomRespVo.getFloor();

        // query apartment payConfig: 首页/账单及收款/付费配置
        AptPayConfig payConfig = payConfigRepository.findById(saveReqVo.getPayConfigId()).orElse(null);
        if (Objects.isNull(payConfig)) {
            log.info("pay config not found: {}", saveReqVo.getPayConfigId());
            throw new RuntimeException("pay config not found");
        }

        BillPayChannel payChannel = payConfig.getChannel();
        if (BillPayChannel.ONLINE.equals(payChannel)) {
            log.info("no offline pay config");
            throw new RuntimeException("not offline pay method");
        }
        // construct aptPay
        AptPay pay = BeanUtil.copy(saveReqVo, AptPay.class);
        // tax rate
        pay.setTax(payConfig.getTax());
        // calculate tax amount
        pay.setTaxAmt(BillUtil.calcTaxAmount(saveReqVo.getTotalAmt(), payConfig.getTax(), payConfig.getMax()));
        // online or offline
        pay.setPayChannel(payChannel);
        // 现金|微信支付|银行转账（中行）|...
        pay.setPayType(payConfig.getPaymentType());
        // 收款号
        pay.setPayAct(String.valueOf(IdWorker.getFlowIdWorkerInstance().nextId()));
        pay.setProjectId(projectSimple.getId());
        pay.setBuildingId(buildingSimple.getId());
        pay.setFloorId(floorSimple.getId());
        pay.setRoomId(roomResp.getId());
        pay.setBillPayModule(BillPayModule.KERRY);
        pay.setCreateBy(UserInfoUtils.getKerryAccount());

        PositionItemResponse positionItem = PositionItemResponse.builder()
                .projectName(projectSimple.getName())
                .buildingName(buildingSimple.getName())
                .floorName(floorSimple.getName())
                .roomName(roomResp.getRoomNo())
                .build();
        pay.setPositionItem(positionItem);
        pay.setComments("线下缴费");
        pay.setAdvanceAmount(BigDecimal.ZERO);
        // save db
        aptPayRepository.save(pay);
        return new RespWrapVo<>(true);
    }

    /**
     * 首页/账单及收款/收款记录/操作/审核
     */
    @Override
    public RespWrapVo<Boolean> verify(@PathVariable("id") Long id) {
        Optional<AptPay> aptPayOp = findAptPayById(id);
        if (aptPayOp.isEmpty()) {
            throw new RuntimeException(NO_PERMISSION_TIPS);
        }
        AptPay offlinePay = aptPayOp.get();
        if (!AptPayVerifyStatus.VERIFIED.equals(offlinePay.getVerifyStatus())) {
            offlinePay.setVerifyStatus(AptPayVerifyStatus.VERIFIED);
            aptPayRepository.save(offlinePay);
        }
        return new RespWrapVo<>(true);
    }

    /**
     * 首页/账单及收款/收款记录/操作/按条件批量审核
     */
    @Override
    public Integer batchVerify(@RequestBody AptPaySearchVo searchReqVo) {
        AptPayBo toVerifyPayBo = BeanUtil.copy(searchReqVo, AptPayBo.class);
        Iterable<AptPay> aptPayIterable = aptPayRepository.findAll(toVerifyPayBo.toPredicates());

        if (!aptPayIterable.iterator().hasNext()) {
            throw new RuntimeException("no_data_after_filter");
        }

        List<AptPay> verifiedAptPays = new ArrayList<>();
        aptPayIterable.forEach(aptPay -> {
            if (!AptPayVerifyStatus.VERIFIED.equals(aptPay.getVerifyStatus())) {
                aptPay.setVerifyStatus(AptPayVerifyStatus.VERIFIED);
                verifiedAptPays.add(aptPay);
            }
        });

        aptPayRepository.saveAll(verifiedAptPays);
        return verifiedAptPays.size();
    }

    /**
     * 首页/账单及收款/收款记录/操作/切换审核状态
     */
    @Override
    public Boolean switchVerifyById(@PathVariable("id") Long id) {
        Optional<AptPay> aptPayOp = findAptPayById(id);
        if (aptPayOp.isEmpty()) {
            throw new RuntimeException(NO_PERMISSION_TIPS);
        }

        AptPay aptPay = aptPayOp.get();
        if (!Integer.valueOf(AptSyncJobStatus.CREATED.getStatus()).equals(aptPay.getSendJdeStatus())) {
            throw new RuntimeException("apt_pay_already_write_back");
        }

        AptPayVerifyStatus verifyStatus = AptPayVerifyStatus.VERIFIED.equals(aptPay.getVerifyStatus()) ?
                AptPayVerifyStatus.TO_BE_VERIFIED : AptPayVerifyStatus.VERIFIED;
        aptPay.setVerifyStatus(verifyStatus);
        aptPayRepository.save(aptPay);

        return true;
    }

    /**
     * 首页/账单及收款/收款记录/操作/取消
     */
    @Override
    @Transactional
    public RespWrapVo<Boolean> deleteOfflinePay(@PathVariable("id") Long id) {
        Optional<AptPay> aptPayOp = findAptPayById(id);
        if (aptPayOp.isEmpty()) {
            throw new RuntimeException("apt_delete_pay no_permission");
        }

        AptPay aptPay = aptPayOp.get();

        if (!Integer.valueOf(1).equals(aptPay.getDeletedAt())) {
            aptPay.setDeletedAt(1);
            aptPay.setDeletedTime(new Date());
            aptPay.setDeletedBy(UserInfoUtils.getUser().getNickName());
            aptPayRepository.save(aptPay);
        }

        if (StringUtils.isEmpty(aptPay.getPaymentInfoId())) {
            throw new RuntimeException("apt_delete_pay payment_info_id_empty");
        }

        AptPaymentInfo aptPaymentInfo = aptPaymentInfoRepository.findTopById(aptPay.getPaymentInfoId());

        if (Objects.isNull(aptPaymentInfo)) {
            throw new RuntimeException("apt_delete_pay payment_info_not_found");
        }

        final int CANCEL_INVOICE = -1;
        aptPaymentInfo.setPaymentStatus(BillPaymentStatus.CANCEL);
        aptPaymentInfo.setCancelType(ADMIN_CANCELLED.name());
        aptPaymentInfo.setAppliedInvoice(CANCEL_INVOICE);
        aptPaymentInfo.setUpdateTime(new Date());
        aptPaymentInfoRepository.save(aptPaymentInfo);


        List<AptBill> aptBills = aptBillRepository.queryAptBillByPaymentId(aptPay.getPaymentInfoId());
        if (CollectionUtils.isNotEmpty(aptBills)) {
            aptBills.forEach(bill -> {
                bill.setStatus(BillStatus.TO_BE_PAID);
                bill.setPaymentStatus(BillPaymentStatus.TO_BE_PAID);
                bill.setPaymentResult(StringUtils.EMPTY);
                bill.setUpdateTime(new Date());
                aptBillRepository.save(bill);
            });
        }

        return new RespWrapVo<>(true);
    }

    /**
     * 首页/账单及收款/收款记录/导出
     */
    @Override
    public void export(@ModelAttribute AptPaySearchVo searchReqVo, HttpServletResponse response) {
        AptPayBo offlinePayBo = BeanUtil.copy(searchReqVo, AptPayBo.class);
        Iterable<AptPay> payIterable = aptPayRepository.findAll(offlinePayBo.toPredicates());
        if (payIterable == null) {
            return;
        }
        List<AptPay> pays = IteratorUtils.toList(payIterable.iterator());
        List<AptPayExportVo> exportVos = new ArrayList<>(pays.size());
        for (AptPay pay : pays) {
            AptPayExportVo exportVo = BeanUtil.copy(pay, AptPayExportVo.class);
            exportVo.setPayChannel(pay.getPayChannel().getInfo());
            exportVo.setProjectName(pay.getPositionItem().getProjectName());
            exportVo.setBuildingName(pay.getPositionItem().getBuildingName());
            exportVo.setRoomNo(pay.getPositionItem().getRoomName());

            if (!BillPayChannel.OFFLINE.equals(pay.getPayChannel())) {
                exportVo.setComments(pay.getPayDesc());
            }

            String paymentCate = exportVo.getPaymentCate();
            paymentCate = PaymentCateEnum.UNKNOWN.name().equals(paymentCate) ? null : paymentCate;
            exportVo.setPaymentCate(paymentCate);

            String payType = exportVo.getPayType();
            payType = PaymentPayType.UNKNOWN.getInfo().equals(payType) ? null : payType;
            exportVo.setPayType(payType);

            exportVos.add(exportVo);
        }

        final String FILE_NAME = "pay_list_file";
        final String SHEET_NAME = "pay_list";
        exportByEasyExcel(response, exportVos, AptPayExportVo.class, FILE_NAME, SHEET_NAME);
    }

    /**
     * 首页/账单及收款/收款记录/操作/详情
     */
    @Override
    public RespWrapVo<AptPayDetailVo> detail(@PathVariable("id") Long id) {
        Optional<AptPay> aptPayOp = findAptPayById(id);
        if (aptPayOp.isEmpty()) {
            throw new RuntimeException("no permission");
        }
        AptPay pay = aptPayOp.get();
        AptPayDetailVo detailVo = BeanUtil.copy(pay, AptPayDetailVo.class);
        RespWrapVo<AptPayDetailVo> resp = new RespWrapVo<>(detailVo);

        List<AptBill> bills;
        if (BillPayChannel.DIRECT_DEBITS.equals(pay.getPayChannel())) {
            bills = batchBillService.getAptBillsByPaymentOrderNo(pay.getPaymentInfoId());
        } else {
            List<AptPayBill> payBills = payBillRepository.findAllByPayId(id);
            if (CollectionUtils.isEmpty(payBills)) {
                return resp;
            }
            List<Long> billIds = payBills.stream()
                    .map(AptPayBill::getBillId)
                    .collect(Collectors.toList());
            bills = billRepository.findAllById(billIds);
        }

        if (CollectionUtils.isEmpty(bills)) {
            return resp;
        }
        List<StaffAptBillRespVo> billVos = bills.stream()
                .map(e -> BeanUtil.copy(e, StaffAptBillRespVo.class))
                .collect(Collectors.toList());
        detailVo.setBills(billVos);
        return resp;
    }

    /**
     * 首页/账单及收款/收款记录/搜索
     */
    @Override
    public RespWrapVo<Page<AptPayVo>> search(@SortDefault.SortDefaults(value = {
                                                     @SortDefault(sort = "createTime", direction = Sort.Direction.DESC)
                                                     , @SortDefault(sort = "updateTime", direction = Sort.Direction.DESC)}) Pageable pageable
            , @ModelAttribute AptPaySearchVo searchReqVo) {

        AptPayBo offlinePayBo = BeanUtil.copy(searchReqVo, AptPayBo.class);
        Page<AptPay> offlinePays = aptPayRepository.findAptPays(offlinePayBo, pageable);
        Page<AptPayVo> vos = offlinePays.map(e -> {
            BillInvoiceCalcDto dto = BillInvoiceCalcDto.of(e);
            AptPayInvoiceBo bo = paymentBillService.calcBillInvoice(dto);

            AptPayVo vo = BeanUtil.copy(e, AptPayVo.class);
            vo.setCanInvoiceBillAmount(bo.getCanInvoiceAmount());
            vo.setIsAdvanceBilling(bo.getIsAdvanceBilling());
            return vo;
        });
        return new RespWrapVo<>(vos);
    }

    /**
     * 首页/账单及收款/账单管理/账单明细
     * <p>
     * 查询历史住户姓名列表，线下收款页面使用
     */
    @Override
    public RespWrapVo<List<RoomAn8RespVo>> roomAn8(@SortDefault.SortDefaults(value = {
                                                           @SortDefault(sort = "createTime", direction = Sort.Direction.DESC)
                                                           , @SortDefault(sort = "updateTime", direction = Sort.Direction.DESC)}) Pageable pageable
            , @PathVariable("roomId") String roomId) {
        AptBillSearchReqBo searchReqBo = AptBillSearchReqBo.builder()
                .roomIds(Lists.newArrayList(roomId))
                .build();
        List<Tuple> tuples = billRepository.getJpaQueryFactory()
                .query()
                .select(QAptBill.aptBill.bu, QAptBill.aptBill.unit, QAptBill.aptBill.doco
                        , QAptBill.aptBill.an8, QAptBill.aptBill.alph)
                .from(QAptBill.aptBill)
                .where(searchReqBo.toPredicates())
                .groupBy(QAptBill.aptBill.bu, QAptBill.aptBill.unit, QAptBill.aptBill.doco
                        , QAptBill.aptBill.an8, QAptBill.aptBill.alph)
                .fetch();
        if (CollectionUtils.isEmpty(tuples)) {
            return new RespWrapVo<>();
        }
        List<RoomAn8RespVo> an8RespVos = tuples.stream().map(e -> {
            int index = 0;
            return RoomAn8RespVo.builder()
                    // JDE BU
                    .bu(e.get(index++, String.class))
                    // JDE单元ID
                    .unit(e.get(index++, String.class))
                    // JDE合同号
                    .doco(e.get(index++, String.class))
                    // JDE地址号
                    .an8(e.get(index++, String.class))
                    // JDE姓名
                    .alph(e.get(index, String.class))
                    .build();
        }).collect(Collectors.toList());
        return new RespWrapVo<>(Lists.reverse(an8RespVos));
    }

    /**
     * API that would sync all apt_pay_bill record to JDE on condition of
     * `apt_pay_bill.confirmed` equals to {@link AptSyncJobStatus#CREATED} and then
     * mark field `apt_pay_bill.confirmed` to {@link AptSyncJobStatus#DONE}
     */
    @Override
    public RespWrapVo<String> confirmToJde() {
        AptSyncJdeJob syncJdeJob = createJobInstance(JobType.SYNC_PAID_BILL_TO_JDE);
        if (!updateJdeJobStatus(syncJdeJob, AptSyncJobStatus.CREATED.getStatus()
                , AptSyncJobStatus.PROCESSING.getStatus())) {
            log.info("job {} executed by other thread.", JSONObject.toJSONString(syncJdeJob));
            new RespWrapVo<>(0);
        }
        // do query & handle
        batchWritePaidBillBack(syncJdeJob);
        // done and update job status
        updateJdeJobStatus(syncJdeJob, AptSyncJobStatus.PROCESSING.getStatus()
                , AptSyncJobStatus.DONE.getStatus());
        return new RespWrapVo<>("AptSyncJdeJob " + syncJdeJob.getId() + " processed "
                + syncJdeJob.getRemoteCnt() + "/" + syncJdeJob.getLocalCnt());
    }

    /**
     * do try sync single record of apt_pay_bill to JDE
     */
    @Transactional
    public AptSyncPaidBillToJde handleOneRecord(AptPayBill payBill, long jobId) {
        AptSyncPaidBillToJde paidBillToJde = writerLocal(payBill, jobId);
        boolean result = writerRemote(paidBillToJde);
        if (Boolean.FALSE.equals(result)) {
            log.error("write payment info back to jde failed.");
            throw new RuntimeException("write payment info back to jde failed");
        }
        return paidBillToJde;
    }

    /**
     * 线下支付账单 匹配到KIP
     */
    @Override
    @RedisLock(key = "kip:billing:c:confirmJdePay:auto", expire = 30L)
    public RespWrapVo<String> autoConfirmJdePayStatus(@RequestParam(value = "auto", required = false) Boolean auto) {
        String buString = aptBillService.buildHiveBuString(null);
        if (StringUtils.isEmpty(buString)) {
            log.error("no hive buString is found");
            throw new RuntimeException("fail to query hive bu");
        }

        //查询时间戳
        String timestampCode = BillCommonSettingEnum.APT_JDE_PAYMENT_TIMESTAMP.getCode();
        BooleanExpression expression = QBillCommonSetting.billCommonSetting.type.eq(timestampCode);
        Iterable<BillCommonSetting> settingIterable = billCommonSettingRepository.findAll(expression);
        List<BillCommonSetting> billCommonSettings = Optional.ofNullable(settingIterable)
                .map(Lists::newLinkedList).orElse(Lists.newLinkedList());
        Optional<BillCommonSetting> optional = billCommonSettings.stream()
                .filter(e -> timestampCode.equalsIgnoreCase(e.getType())).findFirst();

        boolean verifyAll = optional.isEmpty();
        BillCommonSetting billCommonSetting = verifyAll ? new BillCommonSetting() : optional.get();
        log.info("verifyAll is {}, auto is {}, BillCommonSetting is {}", verifyAll, auto, billCommonSetting);

        JdeConfirmResult jdeConfirmResult = null;
        if (Boolean.FALSE.equals(auto) || Boolean.TRUE.equals(verifyAll)) {
            //如果时间戳不存在，则全量匹配
            JdeConfirmResult paidJdeConfirmResult = aptSyncPayStatusService.confirmAllPaidJdeItems(buString);
            log.info("[confirmed]paid timestamp is {}", paidJdeConfirmResult.getTimestamp());
            JdeConfirmResult unpaidConfirmResult = aptSyncPayStatusService.confirmAllUnpaidJdeItems(buString);
            log.info("[confirmed]unpaid timestamp is {}", unpaidConfirmResult.getTimestamp());
            jdeConfirmResult = paidJdeConfirmResult.merge(unpaidConfirmResult);
            log.info("[all]merged timestamp is {}", jdeConfirmResult.getTimestamp());
        } else {
            //如果时间戳存在，则增量匹配
            AptPaymentTimestamp lastTimestamp = JSONObject.parseObject(billCommonSetting.getContent()
                    , AptPaymentTimestamp.class);
            jdeConfirmResult = aptSyncPayStatusService.confirmIncrementalJdeItems(buString, lastTimestamp);
            log.info("[confirmed]incremental timestamp is {}", jdeConfirmResult.getTimestamp());
        }

        //查询支付中账单在JDE侧的时间戳，比较上述时间戳，两者获取最小值
        AptPaymentTimestamp payingTimestamp = aptSyncPayStatusService.confirmPayingBillTimestamp();
        AptPaymentTimestamp timestamp = jdeConfirmResult.getTimestamp();
        timestamp.updateToEarlier(payingTimestamp);
        log.info("[all]final timestamp is {}", timestamp);

        //更新时间戳
        billCommonSetting.setType(timestampCode);
        billCommonSetting.setContent(JSONObject.toJSONString(timestamp));
        billCommonSettingRepository.save(billCommonSetting);

        //核销JDE账单
        List<AptJdeBill> jdeBills = jdeConfirmResult.getAptJdeBills();
        writeOffBills(jdeBills);

        return new RespWrapVo<>("Processed " + jdeBills.size());
    }

    @Transactional
    public List<AptSyncPayToJde> handleOneRecord(AptPay aptPay, long jobId, AtomicInteger index) {
        List<AptSyncPayToJde> syncJdeLogs = writerLocal(aptPay, jobId, index);
        Map<Long, Boolean> result = writerRemote(syncJdeLogs);

        int failedCnt = 0;
        for (AptSyncPayToJde syncJdeLog : syncJdeLogs) {
            if (Boolean.TRUE.equals(result.get(syncJdeLog.getId()))) {
                syncJdeLog.setStatus(AptSyncJobStatus.DONE.getStatus());
            } else {
                syncJdeLog.setErrorMsg("write payment info back to jde failed.");
                syncJdeLog.setStatus(AptSyncJobStatus.FAILED.getStatus());
                failedCnt++;
            }
        }
        syncJdeLogRepository.saveAll(syncJdeLogs);

        if (failedCnt == 0) {
            aptPay.setSendJdeStatus(AptSyncJobStatus.DONE.getStatus());
        } else if (failedCnt == syncJdeLogs.size()) {
            aptPay.setSendJdeStatus(AptSyncJobStatus.FAILED.getStatus());
        } else {
            aptPay.setSendJdeStatus(AptSyncJobStatus.PART_COMPLETED.getStatus());
        }
        aptPayRepository.save(aptPay);

//        if(!result){
//            log.error("write payment info back to jde failed.");
//            throw new RuntimeException("write payment info back to jde failed");
//        }
        verifyJdeBill(aptPay, result);
        return syncJdeLogs;
    }

    public boolean updateJdeJobStatus(AptSyncJdeJob syncJdeJob, Integer from, Integer to) {
        // status: 0 已创建 3 处理中 4处理完成 5 处理失败
        syncJdeJob.setStatus(to);
        syncJdeJob = syncJdeJobRepository.save(syncJdeJob);

//        JPAUpdateClause jdeUpdate = syncJdeJobRepository.getJpaQueryFactory()
//                .update(QAptSyncJdeJob.aptSyncJdeJob)
//                .set(QAptSyncJdeJob.aptSyncJdeJob.status, to);
//        BooleanExpression whereClause = QAptSyncJdeJob.aptSyncJdeJob
//                .id.eq(syncJdeJob.getId())
//                .and(QAptSyncJdeJob.aptSyncJdeJob.status.eq(from));
//        long result = jdeUpdate.where(whereClause).execute();
//        if(result == 0l){
//            return false;
//        }
//        syncJdeJob = syncJdeJobRepository.findById(syncJdeJob.getId()).orElseGet(() -> null);

        saveJobLogVersion(syncJdeJob);
        return true;
    }

    /**
     * 付款信息回写->JDE
     */
    @Override
    @RedisLock(key = "kip:billing:c:writepaytojde", expire = 30L)
    public RespWrapVo<String> writeBack(String projectId) {
        AptSyncJdeJob syncJdeJob = createJobInstance(JobType.SYNC_PAY_TO_JDE);
        if (!updateJdeJobStatus(syncJdeJob, AptSyncJobStatus.CREATED.getStatus()
                , AptSyncJobStatus.PROCESSING.getStatus())) {
            log.error("job {} executed by other thread.", syncJdeJob);
            new RespWrapVo<>(0);
        }
        batchWriteBack(syncJdeJob, projectId);
        updateJdeJobStatus(syncJdeJob, AptSyncJobStatus.PROCESSING.getStatus()
                , AptSyncJobStatus.DONE.getStatus());
        return new RespWrapVo<>("processed " + syncJdeJob.getRemoteCnt() + "/" + syncJdeJob.getLocalCnt());
    }

    public AptSyncJdeJob createJobInstance(JobType jobType) {
        LoginUser loginUser = UserInfoUtils.getUser();
        AptSyncJdeJob syncJdeJob = AptSyncJdeJob.builder().type(jobType).status(0)
                .createBy(loginUser.getUniqueUserId() + "-" + loginUser.getNickName()).build();
        syncJdeJob = syncJdeJobRepository.save(syncJdeJob);
        saveJobLogVersion(syncJdeJob);
        return syncJdeJob;
    }

    public void batchWriteBack(AptSyncJdeJob syncJdeJob, String projectId) {
        syncJdeJob.setStartTime(new Date());
        AptPayBo offlinePayBo = AptPayBo.builder()
                .projectId(projectId)
                .sendJdeStatus(0)
                .verifyStatus(AptPayVerifyStatus.VERIFIED.getCode())
                .deletedAt(0)
                .build();
        Iterable<AptPay> payIterable = aptPayRepository.findAll(offlinePayBo.toPredicates());
        if (payIterable == null) {
            return;
        }
        List<AptPay> aptPays = Lists.newLinkedList(payIterable);
        log.info("query aptPay: [{}]", JSON.toJSON(aptPays));
        if (CollectionUtils.isEmpty(aptPays)) {
            return;
        }
        int successCnt = 0;
        AtomicInteger index = new AtomicInteger(1);
        for (AptPay aptPay : aptPays) {
            refreshCorrelationId();
            if (writerBackOneRecord(aptPay, syncJdeJob.getId(), index)) {
                successCnt++;
            }
        }
        syncJdeJob.setLocalCnt(aptPays.size());
        syncJdeJob.setRemoteCnt(successCnt);
        syncJdeJob.setEndTime(new Date());
    }


    /**
     * query all apt_pay_bill record that confirmed = 0(CREATED)
     * and then do further handle
     */
    private void batchWritePaidBillBack(AptSyncJdeJob syncJdeJob) {
        syncJdeJob.setStartTime(new Date());
        long syncJdeJobId = syncJdeJob.getId();
        // query all apt_pay_bill record that confirmed = 0(CREATED)
        List<AptPayBill> payBills = payBillRepository.findAllByConfirmed(AptSyncJobStatus.CREATED.getStatus());
        if (CollectionUtils.isEmpty(payBills)) {
            log.info("payBill has no record that confirmed is 0(CREATED)");
            return;
        }
        int successCnt = 0;
        for (AptPayBill payBill : payBills) {
            if (writerBackOneRecord(payBill, syncJdeJobId)) {
                successCnt = successCnt + 1;
            }
        }
        syncJdeJob.setLocalCnt(payBills.size());
        syncJdeJob.setRemoteCnt(successCnt);
        syncJdeJob.setEndTime(new Date());
    }

    /**
     * trying to sync single record of apt_pay_bill to JDE
     * trace single record sync detail to `apt_sync_paid_bill_to_jde`
     */
    private boolean writerBackOneRecord(AptPayBill payBill, long jobId) {
        AptSyncPaidBillToJde paidBillToJde = null;
        try {
            payBill.setConfirmed(AptSyncJobStatus.PROCESSING.getStatus());
            payBillRepository.save(payBill);
            //
            paidBillToJde = handleOneRecord(payBill, jobId);
            paidBillToJde.setStatus(AptSyncJobStatus.DONE.getStatus());
            paidBillToJdeRepository.save(paidBillToJde);
            payBill.setConfirmed(AptSyncJobStatus.DONE.getStatus());
            payBillRepository.save(payBill);
        } catch (Exception e) {
            log.error("write to remote failed.", e);
            payBill.setConfirmed(AptSyncJobStatus.FAILED.getStatus());
            payBillRepository.save(payBill);

            if (paidBillToJde != null) {
                paidBillToJde.setStatus(AptSyncJobStatus.FAILED.getStatus());
                paidBillToJde.setErrorMsg(ExceptionUtils.getStackTrace(e));
                paidBillToJdeRepository.save(paidBillToJde);
            }
            return false;
        }
        return true;
    }

    /**
     * query record of `apt_sync_bills` with `apt_bill.bill_no`
     * construct record of `apt_sync_paid_bill_to_jde`
     *
     * @return {@link AptSyncPaidBillToJde}: instance of `apt_sync_paid_bill_to_jde`
     */
    private AptSyncPaidBillToJde writerLocal(AptPayBill payBill, long jobId) {
        // query `apt_bill` record
        Optional<AptBill> billOp = billRepository.findById(payBill.getBillId());
        if (billOp.isEmpty()) {
            log.error("bill not found: {}", payBill.getBillId());
            throw new RuntimeException("bill not found");
        }
        AptBill bill = billOp.get();
        String aptBillNo = bill.getBillNo();
        // query `apt_pay` record
        Optional<AptPay> payOp = aptPayRepository.findById(payBill.getPayId());
        if (payOp.isEmpty()) {
            log.error("bill pay not found: {}", payBill.getPayId());
            throw new RuntimeException("bill pay not found");
        }
        AptPay aptPay = payOp.get();
        // query `apt_pay_config` record
        Optional<AptPayConfig> payConfigOp = payConfigRepository.findById(aptPay.getPayConfigId());
        if (payConfigOp.isEmpty()) {
            log.error("pay config not found: {}", aptPay.getPayConfigId());
            throw new RuntimeException("pay config not found");
        }
        AptPayConfig aptPayConfig = payConfigOp.get();
        // query `apt_sync_bills` correlate to current bill
        List<AptJdeBill> jdeBills = jdeBillRepository.findAllByBillNumber(aptBillNo);
        if (CollectionUtils.isEmpty(jdeBills)) {
            log.error("JDE bill not found: {}", aptBillNo);
            throw new RuntimeException("JDE bill not found: " + aptBillNo);
        }
        AptJdeBill jdeBill = jdeBills.get(0);
        AptSyncPaidBillToJde paidBillToJde = AptSyncPaidBillToJde.builder()
                .jobId(jobId)
                .payBillId(payBill.getId())
                // 账单号; 例：B20214654911291059322271
                .rbcknu(aptBillNo)
                // 单据公司, companyCode; 例：41022
                .rckco(jdeBill.getRdKco())
                // 单据类型; 例：RD
                .rcdct(jdeBill.getRdDct())
                // 单据号; 例：22000004
                .rcdoc(jdeBill.getRdDoc())
                // 单据付款项; 例：001
                .rcsfx(jdeBill.getRdSfx())
                // 总账冲销-票据码; 例：7.00
                .rcag(AptPayBo.toTaxFeelFeeFenWithRate(bill.getAmt(), aptPayConfig.getTax(), aptPayConfig.getMax()))
                // 支付时间; 例：121358
                .rcdmtj(getRuDate(aptPay.getPayDate()))
                .build();
        return paidBillToJdeRepository.save(paidBillToJde);
    }

    //"+SyncJdeConfig.getBillPay()+"
    private boolean writerRemote(AptSyncPaidBillToJde paidBilltoJde) {
        String insertPaidBillToJDESql = "insert into %s.F560314 (" +
                "RBCKNU,RBKCO,RBDCT,RBDOC,RBSFX,RBAG,RBDMTJ,RBEDSP)" +
                "values ('%s','%s','%s','%s','%s',%s,'%s',%s)";
        String db = SyncJdeConfig.getJdeDbPrefix();
        insertPaidBillToJDESql = String.format(insertPaidBillToJDESql,
                db,
                // 账单号
                paidBilltoJde.getRbcknu(),
                // 单据公司
                paidBilltoJde.getRckco(),
                // 单据类型
                paidBilltoJde.getRcdct(),
                // 单据号
                paidBilltoJde.getRcdoc(),
                // 单据付款项
                paidBilltoJde.getRcsfx(),
                // 总账冲销-票据码
                paidBilltoJde.getRcag(),
                // 支付时间
                paidBilltoJde.getRcdmtj(),
                // 微小区读取标记??, default 0
                paidBilltoJde.getRcedsp());
        log.info("select sql: {}", insertPaidBillToJDESql);
        return OracleSql.getInsertResult(insertPaidBillToJDESql);
    }

    private List<AptBill> writeOffBills(List<AptJdeBill> jdeBills) {
        List<AptBill> aptBills = new LinkedList<>();
        if (CollectionUtils.isEmpty(jdeBills)) {
            log.info("no jde bills to write off");
            return aptBills;
        }

        boolean success = true;
        jdeBillRepository.saveAll(jdeBills);
        Map<String, List<AptJdeBill>> groupedJdeBills = jdeBills.stream()
                .collect(Collectors.groupingBy(AptJdeBill::getBillNumber));
        for (Map.Entry<String, List<AptJdeBill>> entry : groupedJdeBills.entrySet()) {
            String billNo = entry.getKey();
            List<AptJdeBill> subJdeBills = entry.getValue();
            try {
                AptBill bill = billRepository.findOne(QAptBill.aptBill.billNo.eq(billNo)).get();
                AptBill bakBill = new AptBill();
                BeanUtils.copyProperties(bill, bakBill);
                long jdeVerifiedCnt = subJdeBills.stream().filter(e -> e.getJdeVerification() == 1).count();
                double jdeTotalAmt = subJdeBills.stream().mapToDouble(AptJdeBill::getRdAg).sum() / 100d;
                int totalCnt = subJdeBills.size();
                boolean updated = false;
                if (jdeVerifiedCnt == totalCnt) {
                    if (!bill.getStatus().equals(BillStatus.JDE_VERIFIED)) {
                        updated = true;
                        bill.setStatus(BillStatus.JDE_VERIFIED);
                        bill.setPaymentStatus(BillPaymentStatus.PAID);
                        bill.setPaymentResult("JDE核销");
                    }
                } else if (jdeVerifiedCnt == 0) {
                    if (!bill.getStatus().equals(BillStatus.TO_BE_PAID)) {
                        updated = true;
                        bill.setStatus(BillStatus.TO_BE_PAID);
                        bill.setPaymentStatus(BillPaymentStatus.TO_BE_PAID);
                        bill.setPaymentResult(StringUtils.EMPTY);
                    }
                } else {
                    updated = true;
                    bill.setStatus(BillStatus.TO_BE_PAID);
                    bill.setPaymentStatus(BillPaymentStatus.PART_PAID);
                    bill.setPaymentResult("JDE部分核销，请线下与物业确认并解决");
                }
                if (bill.getAmt() != jdeTotalAmt) {
                    updated = true;
                    bill.setAmt(jdeTotalAmt);
                }

                if (updated) {
                    bill = saveOrUpdateBill(bill);
                    aptBills.add(bill);
                    List<OperationChangedFiledRespVo> changedFields = DiffFieldUtils.diffFields(bakBill, bill);
                    operationService.saveAutoOperationLog(bill, changedFields);
                }
            } catch (Exception e) {
                success = false;
                log.error("write off bills failed. ErrMsg={}", e.getMessage());
            }
        }
        if (!success) {
            aptBills.clear();
        }
        return aptBills;
    }

    private AptBill saveOrUpdateBill(AptBill aptBill) {
        aptBill = billRepository.save(aptBill);
        log.info("save AptBill. [{}]", aptBill);
        return aptBill;
    }

    private boolean writerBackOneRecord(AptPay aptPay, long jobId, AtomicInteger index) {
        try {
            aptPay.setSendJdeStatus(AptSyncJobStatus.PROCESSING.getStatus());
            aptPayRepository.save(aptPay);
            handleOneRecord(aptPay, jobId, index);
        } catch (Exception e) {
            EmailSendCommand command = new EmailSendCommand();
            command.setSubject(env + ": write to remote failed");
            command.setSendTos(ALERT_MAIL_RECEIVERS);
            command.setText(ExceptionUtils.getStackTrace(e) + "\r" + JSONObject.toJSONString(aptPay));
            messageClient.sendWithReplyAlicloud(command);

            log.error("write to remote failed.", e);
            aptPay.setSendJdeStatus(AptSyncJobStatus.FAILED.getStatus());
            aptPayRepository.save(aptPay);

            return false;
        }
        return true;
    }

    private void verifyJdeBill(AptPay aptPay, Map<Long, Boolean> result) {
        if (aptPay.getPayChannel().equals(BillPayChannel.OFFLINE)) {
            log.info("verify via jde");
            return;
        }
        Iterable<AptPayBill> payBillIterable = payBillRepository.findAll(QAptPayBill.aptPayBill.payId.eq(aptPay.getId()));
//        if(payBillIterable == null){
//            log.error("Bill not found by pay. {}", aptPay);
//            return;
//        }
        List<AptPayBill> payBills = IteratorUtils.toList(payBillIterable.iterator());
//        if(CollectionUtils.isEmpty(payBills)){
//            log.error("Bill not found by pay. {}", aptPay);
//            return;
//        }
        List<String> billNos = payBills.stream().map(e -> e.getBillNo()).collect(Collectors.toList());
        Iterable<AptJdeBill> jdeBillIterable = jdeBillRepository.findAll(
                QAptJdeBill.aptJdeBill.deletedAt.eq(0)
                        .and(QAptJdeBill.aptJdeBill.billNumber.in(billNos))
        );
//        if(jdeBillIterable == null){
//            log.error("jde bill not found by pay. {}", Arrays.toString(billNos.toArray()));
//            return;
//        }
        Iterator<AptJdeBill> jdeBillIterator = jdeBillIterable.iterator();
        List<AptJdeBill> jdeBills = new ArrayList<>();
        while (jdeBillIterator.hasNext()) {
            AptJdeBill jdeBill = jdeBillIterator.next();
            jdeBill.setOnlineVerification(1);
            jdeBill.setOnlineVerificationTime(new Date());
            jdeBills.add(jdeBill);
        }
        log.info("Verified jde bill cnt: {}, payId: {}", jdeBills.size(), aptPay.getId());
        jdeBillRepository.saveAll(jdeBills);
    }

    private List<AptSyncPayToJde> writerLocal(AptPay aptPay, long jobId, AtomicInteger index) {
        List<AptSyncPayToJde> syncJdeLogs = new ArrayList<>();

        AptPayConfig payConfig = payConfigRepository.findById(aptPay.getPayConfigId()).get();
        if (aptPay.getPayChannel().equals(BillPayChannel.OFFLINE)) {
            // 账单
            List<AptJdeBill> jdeBills = new ArrayList<>();
            if (StringUtils.isNotEmpty(aptPay.getPaymentInfoId())) {
                List<AptPaymentBill> aptPaymentBills = aptPaymentBillRepository
                        .findAllByPaymentInfoIdAndDeleted(aptPay.getPaymentInfoId(), 0);
                aptPaymentBills = Optional.ofNullable(aptPaymentBills).orElse(Collections.emptyList());

                List<Long> billIds = aptPaymentBills.stream().map(AptPaymentBill::getBillId).collect(Collectors.toList());
                List<AptBill> aptBills = aptBillRepository.findByIdIn(billIds);

                aptBills = Optional.ofNullable(aptBills).orElse(Collections.emptyList());
                aptBills = aptBills.stream().filter(aptBill -> Integer.valueOf(0).equals(aptBill.getDeletedAt())).collect(Collectors.toList());
                aptBills.forEach(aptBill -> {
                    Iterable<AptJdeBill> jdeBillIterable = jdeBillRepository
                            .findAll(QAptJdeBill.aptJdeBill.billNumber.eq(aptBill.getBillNo()));
                    jdeBills.addAll(Lists.newArrayList(jdeBillIterable));
                });
            }

            log.info("apt_write_local_offline jde_bills_size=[{}], payment_info_id=[{}]", jdeBills.size(), aptPay.getPaymentInfoId());
            final int COMMENTS_MAX_LENGTH = 30;
            String aptPayComments = StringUtils.abbreviate(aptPay.getComments(), COMMENTS_MAX_LENGTH);

            // 预收款
            if (BigDecimal.ZERO.compareTo(aptPay.getAdvanceAmount()) < 0) {
                log.info("apt_write_local_offline apt_pay_advance_amount=[{}], payment_info_id=[{}]", aptPay.getAdvanceAmount(), aptPay.getPaymentInfoId());

                AptSyncPayToJde syncJdeLogAdvancePay = generateAptSyncPayToJde(aptPay, jobId, index, payConfig);
                syncJdeLogAdvancePay.setRcglc(aptPay.getPaymentCate());
                syncJdeLogAdvancePay.setRcag(Optional.ofNullable(aptPay.getAdvanceAmount())
                        .map(e -> e.multiply(BIG_DECIMAL_100).doubleValue()).orElse(BigDecimal.ZERO.doubleValue()));
                syncJdeLogAdvancePay.setRcrmk(aptPayComments);
                syncJdeLogAdvancePay.setRctyin(FEE_TYPE_ADVANCE_PAYMENT_AMOUNT);

                syncJdeLogs.add(syncJdeLogAdvancePay);
            }

            for (int i = 0; i < jdeBills.size(); i++) {
                AptJdeBill jdeBill = jdeBills.get(i);
                if (Objects.isNull(jdeBill)) {
                    log.info("apt_write_local_offline apt_pay_bill_null, payment_info_id=[{}]", aptPay.getPaymentInfoId());
                    continue;
                }

                AptSyncPayToJde syncJdeLogBill = generateAptSyncPayToJde(aptPay, jobId, index, payConfig);
                syncJdeLogBill.setRcag(Optional.ofNullable(jdeBill.getRdAg()).orElse(Double.valueOf("0"))); // 总金额
                syncJdeLogBill.setRckco(jdeBill.getRdKco());
                syncJdeLogBill.setRcdct(jdeBill.getRdDct());
                syncJdeLogBill.setRcdoc(jdeBill.getRdDoc());
                syncJdeLogBill.setRcsfx(jdeBill.getRdSfx());
                syncJdeLogBill.setRcaaaj(BigDecimal.ZERO.doubleValue()); // 手续费
                syncJdeLogBill.setRcrmk(aptPayComments); // 注释
                syncJdeLogBill.setRcalph(jdeBill.getRdAlph());
                syncJdeLogBill.setRcglc(jdeBill.getRdGlc());
                syncJdeLogBill.setRctyin(FEE_TYPE_RECEIVABLE_AMOUNT); // 收款类型，A-账单，U-预收款

                syncJdeLogs.add(syncJdeLogBill);
            }

            if (CollectionUtils.isNotEmpty(syncJdeLogs)) {
                AptSyncPayToJde firstAptSyncPayToJde = syncJdeLogs.get(0);
                BigDecimal billTaxAmt = BigDecimal.valueOf(aptPay.getTaxAmt()).multiply(BIG_DECIMAL_100);
                firstAptSyncPayToJde.setRcaaaj(billTaxAmt.doubleValue());
                firstAptSyncPayToJde.setRcag(BigDecimal.valueOf(firstAptSyncPayToJde.getRcag()).subtract(billTaxAmt).doubleValue());
            }
        } else if (BillPayChannel.ONLINE.equals(aptPay.getPayChannel())
                || BillPayChannel.DIRECT_DEBITS.equals(aptPay.getPayChannel())) {
            List<AptJdeBill> jdeBills = new ArrayList<>();
            if (aptPay.getPayChannel().equals(BillPayChannel.ONLINE)) {
                Iterable<AptPayBill> iterable = payBillRepository.findAll(QAptPayBill.aptPayBill.payId.eq(aptPay.getId()));
                Iterator<AptPayBill> iterator = iterable.iterator();
                while (iterator.hasNext()) {
                    AptPayBill payBill = iterator.next();
                    Iterable<AptJdeBill> jdeBillIterable = jdeBillRepository
                            .findAll(QAptJdeBill.aptJdeBill.billNumber.eq(payBill.getBillNo()));
                    jdeBills.addAll(IteratorUtils.toList(jdeBillIterable.iterator()));
                }
            } else {
                List<AptBill> bills = batchBillService.getAptBillsByPaymentOrderNo(aptPay.getPaymentInfoId());
                for (AptBill tmpBill : bills) {
                    Iterable<AptJdeBill> jdeBillIterable = jdeBillRepository
                            .findAll(QAptJdeBill.aptJdeBill.billNumber.eq(tmpBill.getBillNo()));
                    jdeBills.addAll(Lists.newArrayList(jdeBillIterable));
                }
            }
            BigDecimal currentTotalAmt = BigDecimal.valueOf(0d);
            for (int i = 0; i < jdeBills.size(); i++) {
                AptJdeBill jdeBill = jdeBills.get(i);
                BigDecimal billAmt = new BigDecimal(jdeBill.getRdAg());
                BigDecimal billTaxAmt = BigDecimal.valueOf(0d);
                if (i == 0) {//手续费统一写第一行
                    billTaxAmt = BigDecimal.valueOf(aptPay.getTaxAmt()).multiply(BigDecimal.valueOf(100d));
                }
                BigDecimal billAmtAfterTax = billAmt.subtract(billTaxAmt);
                currentTotalAmt = currentTotalAmt.add(billAmt);
                log.info("pay: {}, bill: {}, jdeBill: {}, billAmtDouble: {}, billTaxDouble: {}, billAmtAfterTax: {}, currentTotalAmt: {}", aptPay.getId(), jdeBill.getBillNumber(), jdeBill, billAmt.doubleValue(), billTaxAmt.doubleValue(), billAmtAfterTax, currentTotalAmt);

                AptSyncPayToJde syncJdeLog = generateAptSyncPayToJde(aptPay, jobId, index, payConfig);
                syncJdeLog.setRcag(billAmtAfterTax.doubleValue());
                syncJdeLog.setRcrmk(aptPay.getPayDesc());
                syncJdeLog.setRcaaaj(billTaxAmt.doubleValue());
                syncJdeLog.setRckco(jdeBill.getRdKco());
                syncJdeLog.setRcdct(jdeBill.getRdDct());
                syncJdeLog.setRcdoc(jdeBill.getRdDoc());
                syncJdeLog.setRcsfx(jdeBill.getRdSfx());
                syncJdeLog.setRcalph(jdeBill.getRdAlph());
                syncJdeLog.setRcglc(jdeBill.getRdGlc());
                syncJdeLog.setRctyin(FEE_TYPE_RECEIVABLE_AMOUNT);

                syncJdeLogs.add(syncJdeLog);
            }

            if (currentTotalAmt.divide(BigDecimal.valueOf(100d)).doubleValue() != aptPay.getTotalAmt()) {
                log.error("paid total amt does not equal to bill amt total, paidAmt: {}, billTotalAmt: {}"
                        , aptPay.getTotalAmt(), currentTotalAmt.doubleValue());
                throw new RuntimeException("支付金额与实际账单金额不相等");
            }
        }
        syncJdeLogs = syncJdeLogRepository.saveAll(syncJdeLogs);
        return syncJdeLogs;
    }

    private AptSyncPayToJde generateAptSyncPayToJde(AptPay aptPay, long jobId
            , AtomicInteger index, AptPayConfig payConfig) {
        return AptSyncPayToJde.builder()
                .jobId(jobId)
                .payId(aptPay.getId())
                .status(0)
                .rccknu(aptPay.getPayAct())
                .rclnid(index.getAndIncrement())
                .rcan8(Long.parseLong(aptPay.getAn8()))
                .rcdmtj(getRuDate(aptPay.getPayDate()))
                .rcglba(payConfig.getBankAccount())
                .rcpo(aptPay.getDoco()) // doco
                .rcmcu("    " + aptPay.getBu())
                .rcunit(aptPay.getUnit())
                .build();
    }

    //"+SyncJdeConfig.getBillPay()+"
    private Map<Long, Boolean> writerRemote(List<AptSyncPayToJde> syncJdeLogs) {
        Map<Long, Boolean> result = new HashMap<>();
        String db = SyncJdeConfig.getJdeDbPrefix();
        for (AptSyncPayToJde syncJdeLog : syncJdeLogs) {
            String insertPayJDESql = "insert into %s.F560313 (RCCKNU,RCLNID,RCTYIN," +
                    "RCKCO,RCDCT,RCDOC," +
                    "RCSFX,RCAN8,RCALPH," +
                    "RCDMTJ,RCAG,RCGLBA," +
                    "RCCRCD,RCGLC,RCPO," +
                    "RCMCU,RCUNIT,RCEDSP,RCRMK,RCAAAJ)" +
                    "values ('%s',%s,'%s','%s','%s',%s,'%s',%s,'%s','%s',%s,'%s','%s','%s','%s','%s','%s','%s','%s',%s)";

            insertPayJDESql = String.format(insertPayJDESql,
                    db,
                    syncJdeLog.getRccknu(),
                    syncJdeLog.getRclnid(),
                    syncJdeLog.getRctyin(),
                    syncJdeLog.getRckco(),
                    syncJdeLog.getRcdct(),
                    syncJdeLog.getRcdoc(),
                    syncJdeLog.getRcsfx(),
                    syncJdeLog.getRcan8(),
                    syncJdeLog.getRcalph(),
                    syncJdeLog.getRcdmtj(),
                    syncJdeLog.getRcag(),
                    syncJdeLog.getRcglba(),
                    syncJdeLog.getRccrcd(),
                    syncJdeLog.getRcglc(),
                    syncJdeLog.getRcpo(),
                    syncJdeLog.getRcmcu(),
                    syncJdeLog.getRcunit(),
                    syncJdeLog.getRcedsp(),
                    syncJdeLog.getRcrmk(),
                    syncJdeLog.getRcaaaj());
            log.info("insert F560313 sql: {}", insertPayJDESql);
            Boolean res = OracleSql.getInsertResult(insertPayJDESql);
            result.put(syncJdeLog.getId(), res);
        }
        return result;
    }

    private void saveJobLogVersion(AptSyncJdeJob syncJdeJob) {
        if (syncJdeJob == null) {
            throw new RuntimeException("jde job record not found.");
        }
        AptSyncJdeJobLog syncJdeJobLog = BeanUtil.copy(syncJdeJob, AptSyncJdeJobLog.class);
        syncJdeJobLog.setId(null);
        syncJdeJobLog.setJobId(syncJdeJob.getId());
        syncJdeJobLogRepository.save(syncJdeJobLog);
    }

    private Optional<AptPay> findAptPayById(Long id) {
        AptPayBo aptPayBo = AptPayBo.builder().id(id).build();
        return aptPayRepository.findOne(aptPayBo.toPredicates());
    }

    private Integer getRuDate(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        StringBuilder sb = new StringBuilder("1");
        sb.append(String.valueOf(c.get(Calendar.YEAR)).substring(2));
        String dayOfYear = String.valueOf(c.get(Calendar.DAY_OF_YEAR));
        sb.append(StringUtils.leftPad(dayOfYear, 3, '0'));
        return Integer.parseInt(sb.toString());
    }

}
