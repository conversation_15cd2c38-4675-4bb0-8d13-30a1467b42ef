package com.kerryprops.kip.bill.webservice.vo.resp;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.PaymentPayType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.ZonedDateTime;


@Data
public class CashierAptPaymentInfoResource {

    @Schema(title = "ID")
    private String id;

    @Schema(title = "金额")
    private BigDecimal amt;

    @Schema(title = "支付状态")
    private BillPaymentStatus paymentStatus;

    @Schema(title = "支付session")
    private String paySession;

    @Schema(title = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX", timezone = "GMT+8")
    private ZonedDateTime createTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX", timezone = "GMT+8")
    @Schema(title = "更新时间")
    private ZonedDateTime updateTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX", timezone = "GMT+8")
    @Schema(title = "支付时间")
    private ZonedDateTime paymentTime;

    @Schema(title = "楼栋信息")
    private PositionItemResponse positionItem;

    /**
     * 后端处理逻辑： 0 - 初始值，1 - 未开票，2 - 开票成功，3 - 开票失败
     */
    @Schema(title = "是否可以开票")
    private Integer appliedInvoice;

    @Schema(title = "支付方式")
    private PaymentPayType payType;

    @Schema(title = "取消类型")
    private String cancelType;

    @Schema(title = "开票地址")
    private String invoiceUrl;

    @Schema(title = "发票地址，xml格式")
    private String xmlUrl;

    @Schema(title = "发票地址，ofd格式")
    private String ofdUrl;

    @Schema(title = "代扣失败原因")
    private String failedReason;

    @Schema(title = "billing payment channel")
    private BillPayChannel payChannel;

    @Schema(title = "描述")
    private String description;

    @Schema(title = "收款号")
    private String payAct;

}
