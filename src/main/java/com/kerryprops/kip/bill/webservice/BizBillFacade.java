package com.kerryprops.kip.bill.webservice;

import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.webservice.vo.req.BizBillReadReqVo;
import com.kerryprops.kip.bill.webservice.vo.req.BizBillSearchReqVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillUnreadInfoVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BizBillDetailRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BizBillRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.ContentUnreadInfoVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/***********************************************************************************************************************
 * Project - accelerator
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - David Wei
 * Created Date - 06/03/2021 14:05
 **********************************************************************************************************************/

@Tag(name = "办公商场-B端账单管理")
@RequestMapping(value = "/b/bill", produces = MediaType.APPLICATION_JSON_VALUE)
public interface BizBillFacade {

    @GetMapping("/billpayers")
    @Operation(summary = "查询付款人列表")
    RespWrapVo<List<String>> queryBillPayers();

    @GetMapping("/userBillInfoList")
    @Operation(summary = "查询公司账单")
    RespWrapVo<Page<BizBillRespVo>> userBillInfoList(@SortDefault.SortDefaults(value = {
            @SortDefault(sort = "tpCrtutime", direction = Sort.Direction.DESC)
            , @SortDefault(sort = "createTime", direction = Sort.Direction.DESC)
            , @SortDefault(sort = "updateTime", direction = Sort.Direction.DESC)}) Pageable pageable
            , @ModelAttribute BizBillSearchReqVo bizBillSearchReqVo);

    @GetMapping(value = "/{id}")
    @Operation(summary = "获取电子账单详细信息，PC+Mobile")
    RespWrapVo<BizBillDetailRespVo> getInfo(@PathVariable("id") Long id);

    @GetMapping(value = "/months")
    @Operation(summary = "获取已有账单年份列表")
    RespWrapVo<List<Integer>> selectBillDistinctYears();

    @PutMapping("/updateBillReadStatus")
    @Operation(summary = "进行更新账单的用户阅读状态和初次阅读时间")
    RespWrapVo<Integer> updateBillReadStatus(BizBillReadReqVo billReadReqVo);

    @Deprecated
    @GetMapping(value = "/updateReadTime/{id}")
    @Operation(summary = "更新账单阅读时间")
    RespWrapVo<Integer> updateReadTime(@PathVariable("id") Long id);

    /**
     * /b/bill/has_unread
     * 仅筛选对当前登录账号为账单接收人时可见的账单
     */
    @GetMapping(value = "/has_unread")
    @Operation(summary = "当前登录账号的未读取账单")
    RespWrapVo<BillUnreadInfoVo> userHasUnreadBill();

    @GetMapping(value = "/read_status")
    @Operation(summary = "当前登录账号的账单是否全部已读")
    ContentUnreadInfoVo userBillReadStatus();

    @PostMapping(value = "/id/{id}/set_readed")
    @Operation(summary = "标记账单信息当前用户已读")
    Boolean setUserBillReaded(@PathVariable(name = "id") Long id);

    @PostMapping(value = "/all_readed")
    @Operation(summary = "标记账单信息当前用户全部已读")
    Boolean setUserBillAllReaded();

}
