package com.kerryprops.kip.bill.webservice.vo.resp;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 账单查询的 view
 */
@Data
@Schema
public class AptDirectDebitsBatchBillExportVo {

    @ExcelProperty("楼盘")
    @Schema(title = "楼盘")
    private String projectName;

    @ExcelProperty("楼栋")
    @Schema(title = "楼栋")
    private String buildingName;

    @ExcelProperty("单元")
    @Schema(title = "单元")
    private String unitName;

    @ExcelProperty("公司")
    @Schema(title = "公司")
    private String companyCode;

    @ExcelProperty("BU")
    @Schema(title = "JDE bu")
    private String bu;

    @ExcelProperty("账单行ID")
    @Schema(title = "账单在KIP平台ID")
    private Long id;

    @ExcelProperty("账单号")
    @Schema(title = "账单号")
    private String billNo;

    @ExcelProperty("年份")
    @Schema(title = "账单年")
    private Integer year;

    @ExcelProperty("月份")
    @Schema(title = "账单月")
    private Integer month;

    @ExcelProperty("账单开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(title = "账单周期开始")
    private String beginDate;

    @ExcelProperty("账单结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(title = "账单周期结束")
    private String endDate;

    @ExcelProperty("代扣方案")
    @Schema(title = "代扣方案")
    private String pspName;

    @ExcelProperty("C端账号")
    @Schema(title = "用户手机号")
    private String userPhoneNumber;

    @ExcelProperty("订单号")
    @Schema(title = "billing模块的支付订单号")
    private String paymentOrderNo;

    @ExcelProperty("平台流水号")
    @Schema(title = "支付平台的平台流水号")
    private String pspTransNo;

    @ExcelProperty("费项")
    @Schema(title = "描述")
    private String category;

    @ExcelProperty("代扣总额")
    @Schema(title = "金额")
    private Double amt;

    @ExcelProperty("扣款结果")
    @Schema(title = "用户支付状态")
    private String paymentStatus;

    @ExcelProperty("失败原因")
    @Schema(title = "失败原因")
    private String failedReason;

    @ExcelIgnore
    @Schema(title = "地址明细")
    private PositionItemResponse positionItem;

    /*public String getProjectName() {
        return this.positionItem.getProjectName();
    }

    public String getBuildingName() {
        return this.positionItem.getBuildingName();
    }

    public String getUnitName() {
        return this.positionItem.getFloorName()+"-"+this.positionItem.getRoomName();
    }*/

    public void setPositionItem(PositionItemResponse positionItem) {
        this.positionItem = positionItem;
        this.projectName = this.positionItem.getProjectName();
        this.buildingName = this.positionItem.getBuildingName();
        this.unitName = this.positionItem.getRoomName();
    }

    public void setPaymentStatus(String paymentStatus) {
        if (paymentStatus == null) {
            return;
        }
        if (paymentStatus.equals(BillPaymentStatus.DIRECT_DEBIT_PAID.toString())) {
            paymentStatus = "已支付";
        } else {
            paymentStatus = "未支付";
        }
        this.paymentStatus = paymentStatus;
    }

    public String getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(String beginDate) {
        if (StringUtils.isNotEmpty(beginDate)) {
            beginDate = StringUtils.substring(beginDate, 0, 10);
        }
        this.beginDate = beginDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        if (StringUtils.isNotEmpty(endDate)) {
            endDate = StringUtils.substring(endDate, 0, 10);
        }
        this.endDate = endDate;
    }

}
