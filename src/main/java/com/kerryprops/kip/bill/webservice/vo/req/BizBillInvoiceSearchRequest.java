package com.kerryprops.kip.bill.webservice.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class BizBillInvoiceSearchRequest {

    @Schema(title = "楼盘ID", required = true)
    private String projectId;

    @Schema(title = "业务类型: LEA-租赁账单，UTI-电表系统")
    private String bizType;

    @Schema(title = "发票日期-搜索起始日期: yyyy-MM-dd")
    private String issuanceDateFrom;

    @Schema(title = "发票日期-搜索结束日期: yyyy-MM-dd")
    private String issuanceDateTo;

    @Schema(title = "bill invoice ID")
    private Long id;

    @Schema(title = "发票号")
    private String invoiceNo;

    @Schema(hidden = true)
    private List<String> mcus;

}
