package com.kerryprops.kip.bill.webservice.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
public class SmsStatusVo implements Serializable {

    @Schema(title = "手机号")
    private String phone;

    @Schema(title = "发送是否成功")
    private Boolean state;

    @Schema(title = "发送结果描述")
    private String message;

    @Schema(title = "发送时间")
    private String deliverTime;

}
