package com.kerryprops.kip.bill.webservice.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 针对用户进行查询账单信息 查询实体
 */
@Data
@Schema
public class BizBillSearchReqVo {

    private static final long serialVersionUID = 1L;

    @Schema(title = "账单开始年", example = "19")
    private Integer tpFyrStart;

    @Schema(title = "账单开始月", example = "12")
    private Integer tpPnStart;

    @Schema(title = "账单结束年", example = "20")
    private Integer tpFyrEnd;

    @Schema(title = "账单结束月", example = "12")
    private Integer tpPnEnd;

    @Schema(title = "用户名称列表")
    private List<String> tpAlphs;

    @Schema(title = "用户编号列表")
    private List<String> tpAn8s;

}
