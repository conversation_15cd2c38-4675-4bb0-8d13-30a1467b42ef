package com.kerryprops.kip.bill.webservice.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * BillConfigListDto.
 *
 * <AUTHOR> Yu
 */
@Data
public class BillConfigListDto {

    @Schema(title = "账单类型")
    private String sourceType;

    @Schema(title = "账单来源名称", description = "支持模糊查询")
    private String sourceName;

    @Schema(title = "是否删除", description = "默认查询未删除数据")
    private String delFlag = "0";

    @Schema(title = "服务地址", description = "支持模糊查询")
    private String httpService;

}