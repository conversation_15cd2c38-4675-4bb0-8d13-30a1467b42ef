package com.kerryprops.kip.bill.webservice.vo.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kerryprops.kip.bill.webservice.vo.resp.RoomAn8RespVo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class CashierFeeQRCodePayRequest {

    @NotBlank(message = "authCode is required")
    @Schema(title = "用户auth_code", required = true)
    private String authCode;

    @NotBlank(message = "payOption is required")
    @Schema(title = "pay option: WECHATPAY, ALIPAY", required = true)
    private String payOption;

    @NotBlank(message = "projectId不能为空")
    @Schema(title = "楼盘", required = true)
    private String projectId;

    @NotBlank(message = "building不能为空")
    @Schema(title = "楼栋", required = true)
    private String buildingId;

    @NotBlank(message = "roomId不能为空")
    @Schema(title = "户号", required = true)
    private String roomId;

    @Schema(title = "描述")
    private String description;

    @NotNull(message = "杂费配置'feedId'不能为空")
    @Schema(title = "杂费配置id", required = true)
    private Long feeId;

    @NotBlank(message = "杂费配置名（费项）'feeName'不能为空")
    @Schema(title = "杂费配置名（费项）", required = true)
    private String feeName;

    @NotNull(message = "收款金额'feeAmount'不能为空")
    @Digits(integer = 32, fraction = 2)
    @Schema(title = "收款金额", description = "订单应收金额||账单金额，还没计算税率", required = true)
    private BigDecimal feeAmount;

    @Schema(title = "付款人信息")
    private RoomAn8RespVo payerInfo;

    @Schema(title = "付款日期[yyyy-MM-dd]。如果不传，则为实际支付时间。")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date paymentTime;

}