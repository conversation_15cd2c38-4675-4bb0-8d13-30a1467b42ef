package com.kerryprops.kip.bill.webservice.scheduler;

import com.kerryprops.kip.bill.log4j.BSConversationFilter;
import com.kerryprops.kip.bill.webservice.impl.DirectDebitsBillBatchController;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@AllArgsConstructor
@ConditionalOnProperty(value = "distributed.jobs.enabled", havingValue = "true")
public class DirectDebitsScheduler {

    private DirectDebitsBillBatchController controller;

    @Scheduled(cron = "${scheduler.conductDirectPayment}")
    public void conductDirectPayment() {
        BSConversationFilter.setLoggingContext();
        log.info("scheduled_conductDirectPayment start");
        controller.conductDirectPayment();
        log.info("scheduled_conductDirectPayment done");
    }

}
