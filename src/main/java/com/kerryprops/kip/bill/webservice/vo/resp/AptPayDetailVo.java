package com.kerryprops.kip.bill.webservice.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * 名   称：apt_offline_pay
 * 描   述：
 * 作   者：<PERSON>
 * 时   间：2021/08/31 15:38:36
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AptPayDetailVo extends AptPayVo {

    @Schema(title = "支付账单列表")
    private List<StaffAptBillRespVo> bills;

}
