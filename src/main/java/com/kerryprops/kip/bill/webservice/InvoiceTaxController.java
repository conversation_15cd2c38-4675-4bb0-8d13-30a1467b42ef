package com.kerryprops.kip.bill.webservice;

import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.feign.clients.KipInvoiceClient;
import com.kerryprops.kip.bill.webservice.vo.resp.TaxClassifyCodeResponse;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@Slf4j
@RestController
@AllArgsConstructor
public class InvoiceTaxController {

    private final KipInvoiceClient kipInvoiceClient;

    @Operation(summary = "查询税收分类编码")
    @GetMapping(value = "/tax/co/{co}/fee_type/{feeType}/classifications", produces = MediaType.APPLICATION_JSON_VALUE)
    public TaxClassifyCodeResponse getClassifyCode(@PathVariable("co") String companyCode, @PathVariable("feeType") String feeType) {
        try {
            RespWrapVo<TaxClassifyCodeResponse> respWrapVo = kipInvoiceClient.getClassifyCode(companyCode, feeType);
            if (Optional.ofNullable(respWrapVo).map(RespWrapVo::getData)
                    .map(TaxClassifyCodeResponse::getClassifyCode).isPresent()) {
                return respWrapVo.getData();
            } else {
                log.info("empty tax clazz code");
                return null;
            }
        } catch (Exception e) {
            log.error("error query kss.getClassifyCode", e);
            return null;
        }
    }

}
