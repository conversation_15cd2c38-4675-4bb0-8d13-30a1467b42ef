package com.kerryprops.kip.bill.webservice;

import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptPaySaveVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptPaySearchVo;
import com.kerryprops.kip.bill.webservice.vo.resp.AptPayDetailVo;
import com.kerryprops.kip.bill.webservice.vo.resp.AptPayVo;
import com.kerryprops.kip.bill.webservice.vo.resp.RoomAn8RespVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/***********************************************************************************************************************
 * Project - accelerator
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - David Wei
 * Created Date - 06/03/2021 14:05
 **********************************************************************************************************************/

@Tag(name = "公寓小区-S端线下收款")
@RequestMapping(value = "", produces = MediaType.APPLICATION_JSON_VALUE)
public interface StaffAptPayFacade {

    @GetMapping("/s/apt/pay/queryPaymentCate")
    @Operation(summary = "获取预收科目字典项")
    RespWrapVo<List<Map<String, String>>> queryPaymentCate();

    @PostMapping("/s/apt/pay/save")
    @Operation(summary = "保存对象")
    RespWrapVo<Boolean> save(@Valid @RequestBody AptPaySaveVo saveReqVo);

    @PutMapping("/s/apt/pay/offline/verify/{id}")
    @Operation(summary = "审核")
    RespWrapVo<Boolean> verify(@PathVariable("id") Long id);

    @PostMapping("/s/apt/pay/batch_verify")
    @Operation(summary = "按条件批量审核")
    Integer batchVerify(@RequestBody AptPaySearchVo searchReqVo);

    @PostMapping("/s/apt/pay/{id}/switch_verify")
    @Operation(summary = "切换审核状态")
    Boolean switchVerifyById(@PathVariable("id") Long id);

    @DeleteMapping("/s/apt/pay/delete/{id}")
    @Operation(summary = "删除线下收款")
    RespWrapVo<Boolean> deleteOfflinePay(@PathVariable("id") Long id);

    @GetMapping("/s/apt/pay/export")
    @Operation(summary = "导出所有对象列表")
    void export(@ModelAttribute AptPaySearchVo searchReqVo, HttpServletResponse response);

    @GetMapping("/s/apt/pay/detail/{id}")
    @Operation(summary = "收款详情")
    RespWrapVo<AptPayDetailVo> detail(@PathVariable("id") Long id);

    @GetMapping("/s/apt/pay/search")
    @Operation(summary = "查询所有对象列表")
    RespWrapVo<Page<AptPayVo>> search(@SortDefault.SortDefaults(value = {
            @SortDefault(sort = "createTime", direction = Sort.Direction.DESC),
            @SortDefault(sort = "updateTime", direction = Sort.Direction.DESC)}) Pageable pageable,
                                      @ModelAttribute AptPaySearchVo searchReqVo);

    @GetMapping("/s/apt/pay/room/{roomId}/an8")
    @Operation(summary = "查询历史住户姓名列表，线下收款页面使用")
    RespWrapVo<List<RoomAn8RespVo>> roomAn8(@SortDefault.SortDefaults(value = {
            @SortDefault(sort = "createTime", direction = Sort.Direction.DESC),
            @SortDefault(sort = "updateTime", direction = Sort.Direction.DESC)}) Pageable pageable,
                                            @PathVariable("roomId") String roomId);

    @GetMapping("/s/apt/pay/payment/project_ids/{project_id}/toJDE")
    @Operation(summary = "付款信息回写JDE")
    RespWrapVo<String> writeBack(@PathVariable("project_id") String projectId);

    @Deprecated
    @GetMapping("/s/apt/pay/bill/confirmToJde")
    @Operation(summary = "线上支付账单匹配到JDE")
    RespWrapVo<String> confirmToJde();

    @GetMapping("/s/apt/pay/bill/autoConfirmJdePay")
    @Operation(summary = "线下账单匹配到KIP(自动)")
    RespWrapVo<String> autoConfirmJdePayStatus(@RequestParam(value = "auto", required = true) Boolean auto);

}
