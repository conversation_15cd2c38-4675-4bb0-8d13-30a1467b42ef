package com.kerryprops.kip.bill.webservice.vo.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kerryprops.kip.bill.common.enums.AptPayVerifyStatus;
import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import com.kerryprops.kip.bill.common.enums.BillPayModule;
import com.kerryprops.kip.bill.common.enums.PaymentCateEnum;
import com.kerryprops.kip.bill.common.enums.PaymentPayType;
import com.kerryprops.kip.bill.common.utils.IdWorker;
import com.kerryprops.kip.bill.common.utils.PrimaryKeyUtil;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPayConfig;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.feign.entity.FeeConfigVo;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.utils.BillUtil;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.apache.logging.log4j.util.Strings;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> 2023-12-15
 **/
@Getter
@Setter
public class CashierFeePayRequest {

    @NotBlank
    @Schema(title = "楼盘", required = true)
    private String projectId;

    @Schema(title = "楼栋")
    private String buildingId;

    @Schema(title = "户号")
    private String roomId;

    @NotNull
    @Schema(title = "订单日期[yyyy-MM-dd]", required = true)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date billDate;

    @NotNull
    @Schema(title = "支付方式", description = "具体支付方式，如：微信支付、现金支付...", required = true)
    private String payType;

    @Schema(title = "描述")
    private String description;

    @NotNull
    @Schema(title = "杂费配置id", required = true)
    private Long feeId;

    @NotBlank
    @Schema(title = "杂费配置名（费项）", required = true)
    private String feeName;

    @NotNull
    @Digits(integer = 32, fraction = 2)
    @Schema(title = "收款金额", description = "订单应收金额||账单金额，还没计算税率", required = true)
    private BigDecimal billAmount;

    @Schema(title = "流水号")
    private String pspTransNo;

    @Schema(title = "营业单位")
    private String bu;

    @Schema(title = "JDE单元号")
    private String unit;

    @Schema(title = "付款人")
    private String alph;

    @Schema(title = "an8，可代表楼栋、单元号等等")
    private String an8;

    @Schema(title = "JDE合同号")
    private String doco;

    public AptPaymentInfo toAptPaymentInfo(PositionItemResponse position, FeeConfigVo feeConfig) {
        Date now = new Date();
        String paymentId = PrimaryKeyUtil.createPaymentId();
        String payAct = IdWorker.getFlowIdWorkerInstance().nextStrId();
        String userProfileId = UserInfoUtils.getUserProfileId();
        String taxRate = feeConfig.getTaxRate();
        BigDecimal feeTax = new BigDecimal(taxRate);
        BigDecimal feeTaxAmount = BillUtil.calcFeeTaxAmount(billAmount, feeTax);

        AptPaymentInfo paymentInfo = new AptPaymentInfo();
        paymentInfo.setId(paymentId);
        paymentInfo.setCreateTime(now);
        paymentInfo.setUpdateTime(now);
        paymentInfo.setDeleted(0);
        paymentInfo.setPaymentTransNo(Strings.EMPTY);
        paymentInfo.setXmlUrl(Strings.EMPTY);
        paymentInfo.setOfdUrl(Strings.EMPTY);
        paymentInfo.setFailedReason(Strings.EMPTY);
        paymentInfo.setAgreementNo(Strings.EMPTY);
        paymentInfo.setAmt(billAmount.doubleValue());
        paymentInfo.setBindUserId(UserInfoUtils.getUserId());
        paymentInfo.setUserProfileId(userProfileId);
        paymentInfo.setAdvanceAmount(BigDecimal.ZERO);
        paymentInfo.setBillPayModule(BillPayModule.CASHIER_FEE);
        paymentInfo.setCreateBy(UserInfoUtils.getKerryAccount());
        paymentInfo.setPaymentCate(PaymentCateEnum.UNKNOWN);
        paymentInfo.setAppliedInvoice(1);
        paymentInfo.setPositionItem(position);
        paymentInfo.setFeeTax(feeTax);
        paymentInfo.setFeeTaxAmount(feeTaxAmount);
        paymentInfo.setPayAct(payAct);
        paymentInfo.setPayChannel(BillPayChannel.OFFLINE);
        paymentInfo.setPayType(PaymentPayType.fromInfo(getPayType()));

        paymentInfo.setPaymentTime(getBillDate());
        paymentInfo.setProjectId(getProjectId());
        paymentInfo.setBuildingId(getBuildingId());
        paymentInfo.setPspTransNo(getPspTransNo());
        paymentInfo.setRoomId(getRoomId());
        paymentInfo.setDescription(getDescription());
        paymentInfo.setFeeId(getFeeId());
        paymentInfo.setFeeName(getFeeName());
        paymentInfo.setUnit(getUnit());
        paymentInfo.setBu(getBu());
        paymentInfo.setAn8(getAn8());
        paymentInfo.setAlph(getAlph());
        paymentInfo.setDoco(getDoco());
        paymentInfo.setPayTypeInfo(getPayType());
        return paymentInfo;
    }

    public AptPay toAptPay(AptPaymentInfo payment, AptPayConfig payConfig) {
        Double tax = payConfig.getTax();
        double totalAmt = billAmount.doubleValue();
        BigDecimal feeTax = payment.getFeeTax();
        BigDecimal feeTaxAmount = payment.getFeeTaxAmount();

        AptPay aptPay = new AptPay();
        aptPay.setPayType(payConfig.getPaymentType());
        aptPay.setPayDetail(payConfig.getPaymentDetail());
        aptPay.setPayConfigId(payConfig.getId());
        aptPay.setTax(tax);
        aptPay.setTaxAmt(BillUtil.calcTaxAmount(totalAmt, tax, payConfig.getMax()));
        aptPay.setTotalAmt(totalAmt);
        aptPay.setAdvanceAmount(BigDecimal.ZERO);
        aptPay.setPayAct(payment.getPayAct());
        aptPay.setVerifyStatus(AptPayVerifyStatus.TO_BE_VERIFIED);
        aptPay.setSendJdeStatus(0);
        aptPay.setPaymentInfoId(payment.getId());
        aptPay.setVersionNum(1);
        aptPay.setPaymentCate(PaymentCateEnum.A003.name());
        aptPay.setBillPayModule(BillPayModule.CASHIER_FEE);
        aptPay.setCreateBy(UserInfoUtils.getKerryAccount());
        aptPay.setFloorId(payment.getFloorId());
        aptPay.setPositionItem(payment.getPositionItem());
        aptPay.setFeeTax(feeTax);
        aptPay.setFeeTaxAmount(feeTaxAmount);
        aptPay.setPayDesc(BillPayChannel.OFFLINE.getInfo());
        aptPay.setPayChannel(BillPayChannel.OFFLINE);

        aptPay.setPayDate(getBillDate());
        aptPay.setFeeId(getFeeId());
        aptPay.setFeeName(getFeeName());
        aptPay.setProjectId(getProjectId());
        aptPay.setBuildingId(getBuildingId());
        aptPay.setRoomId(getRoomId());
        aptPay.setPayTranx(getPspTransNo());
        aptPay.setComments(getDescription());
        aptPay.setUnit(getUnit());
        aptPay.setBu(getBu());
        aptPay.setAn8(getAn8());
        aptPay.setAlph(getAlph());
        aptPay.setDoco(getDoco());
        return aptPay;
    }

}
