package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.aop.RedisLock;
import com.kerryprops.kip.bill.config.EFapiaoConfig;
import com.kerryprops.kip.bill.dao.entity.EFapiaoJDEBill;
import com.kerryprops.kip.bill.feign.clients.MessageClient;
import com.kerryprops.kip.bill.service.EFapiaoBillService;
import com.kerryprops.kip.bill.service.EFapiaoJdeBillService;
import com.kerryprops.kip.bill.webservice.EFapiaoSyncBillFacade;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * e-fapiao同步任务controller类
 *
 * <AUTHOR> Yan
 * @Date 2023-3-18
 */
@Slf4j
@RestController
public class EFapiaoSyncBillController implements EFapiaoSyncBillFacade {

    @Autowired
    EFapiaoConfig eFapiaoConfig;

    @Autowired
    EFapiaoJdeBillService eFapiaoJdeBillService;

    @Autowired
    EFapiaoBillService eFapiaoBillService;

    @Value("${e-fapiao.upload-pause-short}")
    private int uploadPauseShort;

    @Value("${e-fapiao.upload-pause-long}")
    private int uploadPauseLong;

    @Autowired
    private MessageClient messageClient;

    @Override
    @RedisLock(key = "kip:billing:b:efapiao:syncjdebill", expire = 180L)
    public Boolean sync(@RequestBody EFapiaoConfig.Item eFapiaoItem
            , @RequestParam("isSchedule") boolean isSchedule) {
        log.info("sync_e_fapiao_bills_from_jde, isSchedule: [{}]", isSchedule);


        List<EFapiaoConfig.Item> coValidBuPeriods = new ArrayList<>();
        if (isSchedule) {
            coValidBuPeriods.addAll(eFapiaoConfig.getCoValidBuPeriod());
        } else {
            coValidBuPeriods.add(eFapiaoItem);
        }

        for (EFapiaoConfig.Item item : coValidBuPeriods) {
            String companyCode = item.getCompanyCode();
            String bus = item.getBus();
            String startDate = item.getStartDate();

            if (StringUtils.isEmpty(companyCode) || StringUtils.isEmpty(startDate) || StringUtils.isEmpty(bus)) {
                log.info("sync_e_fapiao_bills_from_jde param_is_empty.");
                continue;
            }

            String[] busArray = bus.split(",");

            for (String bu : busArray) {
                List<String> yearMonths = getYearMonths(companyCode, bu, startDate);
                for (String yearMonth : yearMonths) {
                    List<EFapiaoJDEBill> eFapiaoJDEBills = eFapiaoJdeBillService
                            .queryByCompanyCodeAndMonthAndBu(companyCode, yearMonth, bu);
                    log.info("sync_e_fapiao_bills_from_jde, companyCode:[{}], bu:[{}], startDate:[{}], jde_bill_amount:[{}]"
                            , companyCode, bu, yearMonth, Optional.ofNullable(eFapiaoJDEBills).map(List::size).orElse(-1));

                    AtomicInteger count = new AtomicInteger();
                    eFapiaoJDEBills.forEach(jdeBillInvoice -> {
                        try {
                            eFapiaoBillService.invoiceUpload(jdeBillInvoice); // 申请开票

                            // 限制请求票易通qps
                            Thread.sleep(uploadPauseShort);
                            count.getAndIncrement();
                            if (count.get() % 10 == 0) {
                                Thread.sleep(uploadPauseLong);
                            }
                        } catch (Exception e) {
                            String salesbillNo = jdeBillInvoice.getKco() + jdeBillInvoice.getBillType()
                                    + jdeBillInvoice.getDoc() + jdeBillInvoice.getPaymentItem();
                            log.error("sync_e_fapiao_bills_from_jde failed, sales_bill_no:[{}] ", salesbillNo, e);
                        }
                    });
                }
            }
        }
        return true;
    }

    @Hidden
    @PostMapping(value = "/s/bill/e_fapiao/sales_bill_no/{salesBillNo}/compensate_upload")
    public Object compensateUpload(@PathVariable("salesBillNo") String salesBillNo) {
        EFapiaoJDEBill eFapiaoJDEBill = eFapiaoJdeBillService.queryBySalesBillNo(salesBillNo);
        if (Objects.isNull(eFapiaoJDEBill)) {
            return "bizBill not found";
        }
        eFapiaoBillService.invoiceUpload(eFapiaoJDEBill); // 申请开票（补偿场景）
        return eFapiaoJDEBill;
    }

    private List<String> getYearMonths(String companyCode, String bus, String startDate) {
        if (Objects.isNull(startDate) || startDate.length() < 7) {
            throw new RuntimeException(String
                    .format("sync_e_fapiao_bills_from_jde start_date_error: %s", startDate));
        }

        String maxDate = eFapiaoJdeBillService.queryMaxDateByCompanyCodeAndBu(companyCode, bus);
        if (StringUtils.isNotEmpty(maxDate) && maxDate.length() >= 7) {
            maxDate = maxDate.substring(0, 7);
        } else {
            maxDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
        }

        int tempYear = Integer.parseInt(startDate.substring(0, 4));
        int tempMonth = Integer.parseInt(startDate.substring(5, 7));
        String tempYearMonth = startDate.substring(0, 7);

        List<String> yearMonths = new ArrayList<>();
        while (maxDate.compareTo(tempYearMonth) >= 0) {
            yearMonths.add(tempYearMonth);

            if (12 <= tempMonth) {
                tempMonth = 1;
                tempYear++;
            } else {
                tempMonth++;
            }

            tempYearMonth = tempMonth < 10 ? tempYear + "-0" + tempMonth : tempYear + "-" + tempMonth;
        }
        return yearMonths;
    }

}



