package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.aop.BillErrorEnum;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.exceptions.AppException;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.AptPayConfigRepository;
import com.kerryprops.kip.bill.dao.AptPayRepository;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPayConfig;
import com.kerryprops.kip.bill.dao.entity.QAptPay;
import com.kerryprops.kip.bill.dao.entity.QAptPayConfig;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.webservice.StaffAptPayConfigFacade;
import com.kerryprops.kip.bill.webservice.vo.req.AptOtherPayConfigSaveVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptOtherPayConfigUpdateVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptPayConfigSaveVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptPayConfigSearchVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptPayConfigUpdateVo;
import com.kerryprops.kip.bill.webservice.vo.resp.AptPayConfigVo;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import com.kerryprops.kip.hiveas.webservice.resource.resp.ProjectResp;
import com.querydsl.core.types.dsl.BooleanExpression;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 1. super admin: can do any thing;
 * 2. other: can do any thing under bu
 */
@Slf4j
@RestController
public class StaffAptPayConfigController implements StaffAptPayConfigFacade {

    @Autowired
    private AptPayConfigRepository aptPayConfigRepository;

    @Autowired
    private AptPayRepository payRepository;

    @Autowired
    private HiveAsClient hiveAsClient;

    @Override
    public RespWrapVo<Boolean> save(@Valid @RequestBody AptPayConfigSaveVo saveReqVo) {
        BooleanExpression expression = QAptPayConfig.aptPayConfig.deleteAt.eq(0L)
                .and(QAptPayConfig.aptPayConfig.projectId.eq(saveReqVo.getProjectId()))
                .and(QAptPayConfig.aptPayConfig.paymentType.eq(saveReqVo.getPaymentType()))
                .and(QAptPayConfig.aptPayConfig.companyCode.eq(saveReqVo.getCompanyCode()));

        Iterable<AptPayConfig> aptPayConfigIterable = aptPayConfigRepository.findAll(expression);
        if (aptPayConfigIterable != null && aptPayConfigIterable.iterator().hasNext()) {
            throw new AppException("510000", "该付费方式已存在");
        }
        // pay config not exists
        AptPayConfig aptPayConfig = BeanUtil.copy(saveReqVo, AptPayConfig.class);
        populateProject(saveReqVo.getProjectId(), aptPayConfig);
        aptPayConfigRepository.save(aptPayConfig);
        return new RespWrapVo<>(true);
    }

    @Override
    public RespWrapVo<Boolean> update(@Valid @RequestBody AptPayConfigUpdateVo updateReqVo) {
        AptPayConfig aptPayConfig = findOne(updateReqVo.getId());
        if (aptPayConfig == null) {
            throw new RuntimeException("configure not found.");
        }
        if (!Objects.equals(updateReqVo.getProjectId(), aptPayConfig.getProjectId())) {
            populateProject(updateReqVo.getProjectId(), aptPayConfig);
        }
        updateReqVo.toAptPayConfig(aptPayConfig);
        aptPayConfigRepository.save(aptPayConfig);
        return new RespWrapVo<>(true);
    }

    @Override
    public RespWrapVo<Boolean> saveOtherPay(@Valid @RequestBody AptOtherPayConfigSaveVo saveReqVo) {
        AptPayConfigSaveVo aptPayConfigSaveVo = BeanUtil.copy(saveReqVo, AptPayConfigSaveVo.class);
        aptPayConfigSaveVo.setTax(0d);
        aptPayConfigSaveVo.setMax(0d);
        return save(aptPayConfigSaveVo);
    }

    @Override
    public RespWrapVo<Boolean> updateOtherPay(@Valid @RequestBody AptOtherPayConfigUpdateVo updateReqVo) {
        AptPayConfigUpdateVo aptPayConfigUpdateVo = BeanUtil.copy(updateReqVo, AptPayConfigUpdateVo.class);
        return update(aptPayConfigUpdateVo);
    }

    @Override
    @Deprecated
    public RespWrapVo<Boolean> deleteById(@PathVariable("id") Long id) {
        AptPayConfig aptPayConfig = findOne(id);
        if (aptPayConfig == null) {
            return new RespWrapVo<>(false);
        }
        Iterable<AptPay> aptPayIterable = payRepository.findAll(QAptPay.aptPay.payConfigId.eq(id));
        if (aptPayIterable != null && aptPayIterable.iterator() != null && aptPayIterable.iterator().hasNext()) {
            throw new AppException("510000", "已有支付数据与当前配置绑定，不能删除");
        }
        aptPayConfigRepository.deleteById(id);
//        aptPayConfig.setDeleteAt(1l);
//        aptPayConfigRepository.save(aptPayConfig);
        return new RespWrapVo<>(true);
    }

    @Override
    public RespWrapVo<AptPayConfigVo> getById(@PathVariable("id") Long id) {
        AptPayConfig aptPayConfig = findOne(id);
        if (aptPayConfig == null) {
            return new RespWrapVo<>();
        }
        return new RespWrapVo<>(BeanUtil.copy(aptPayConfig, AptPayConfigVo.class));
    }

    @Override
    public AptPayConfigVo disableById(@PathVariable("id") Long id) {
        AptPayConfig aptPayConfig = findOne(id);
        if (Objects.isNull(aptPayConfig)) {
            throw AppException.error(BillErrorEnum.NOT_FOUND);
        }

        final Integer DISABLED_STATUS = 0;
        aptPayConfig.setEnabledStatus(DISABLED_STATUS);
        aptPayConfigRepository.save(aptPayConfig);

        return BeanUtil.copy(aptPayConfig, AptPayConfigVo.class);
    }

    @Override
    public AptPayConfigVo enableById(@PathVariable("id") Long id) {
        AptPayConfig aptPayConfig = findOne(id);
        if (Objects.isNull(aptPayConfig)) {
            throw AppException.error(BillErrorEnum.NOT_FOUND);
        }

        final Integer ENABLED_STATUS = 1;
        aptPayConfig.setEnabledStatus(ENABLED_STATUS);
        aptPayConfigRepository.save(aptPayConfig);

        return BeanUtil.copy(aptPayConfig, AptPayConfigVo.class);
    }

    @Override
    public RespWrapVo<Page<AptPayConfigVo>> search(@SortDefault.SortDefaults(value = {
            @SortDefault(sort = "createTime", direction = Sort.Direction.DESC),
            @SortDefault(sort = "updateTime", direction = Sort.Direction.DESC)}) Pageable pageable,
                                                   @ModelAttribute AptPayConfigSearchVo searchReqVo) {
        Page<AptPayConfig> dtoPage = aptPayConfigRepository.findAll(searchReqVo.toPredicates(), pageable);
        Page<AptPayConfigVo> voPage = dtoPage.map(e -> BeanUtil.copy(e, AptPayConfigVo.class));
        return new RespWrapVo<>(voPage);
    }

    @Override
    public RespWrapVo<List<String>> getPaymentType(@RequestParam(value = "projectId", required = false) String projectId) {
        BooleanExpression expression = QAptPayConfig.aptPayConfig.deleteAt.eq(0l);
        List<String> maxProjectIds = maxBindingScope();
        if (CollectionUtils.isNotEmpty(maxProjectIds)) {
            expression = expression.and(QAptPayConfig.aptPayConfig.projectId.in(maxProjectIds));
        }
        if (StringUtils.isNotEmpty(projectId)) {
            expression = expression.and(QAptPayConfig.aptPayConfig.projectId.eq(projectId));
        }
        List<String> paymentTypeList = aptPayConfigRepository.getJpaQueryFactory()
                .selectDistinct(QAptPayConfig.aptPayConfig.paymentType)
                .from(QAptPayConfig.aptPayConfig)
                .where(expression)
                .fetch();
        log.info("paymentTypeList: {}", paymentTypeList == null ? "" : Arrays.toString(paymentTypeList.toArray()));
        return new RespWrapVo<>(paymentTypeList);
    }

    public List<String> maxBindingScope() {
        LoginUser loginUser = UserInfoUtils.getUser();
        if (loginUser == null) {
            throw new RuntimeException("not login.");
        }
        if (loginUser.isSuperAdmin()) {
            return null;
        }
        return loginUser.toProjectIdList();
    }

    private void populateProject(String projectId, AptPayConfig aptPayConfig) {
        RespWrapVo<ProjectResp> centerRespVoRespWrapVo = hiveAsClient.getProjectById(projectId);
        if (!RespWrapVo.isResponseValidWithData(centerRespVoRespWrapVo)) {
            throw new RuntimeException("project not found.");
        }
        aptPayConfig.setPositionItem(PositionItemResponse.builder()
                .projectName(centerRespVoRespWrapVo.getData().getProject().getName()).build());
    }

    private AptPayConfig findOne(Long id) {
        AptPayConfigSearchVo searchVo = AptPayConfigSearchVo.builder().id(id).build();
        return aptPayConfigRepository.findOne(searchVo.toPredicates()).orElseGet(() -> null);
    }

}
