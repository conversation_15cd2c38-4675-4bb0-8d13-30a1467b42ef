package com.kerryprops.kip.bill.webservice.vo.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kerryprops.kip.bill.common.enums.RefLogType;
import com.kerryprops.kip.bill.dao.entity.AptBillOperationStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class BillRefLogSearchRequest {

    @Schema(title = "搜索开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date searchDateFrom;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "搜索结束日期")
    private Date searchDateTo;

    @Schema(title = "搜索楼盘")
    private String projectId;

    @Schema(title = "搜索楼栋")
    private String buildingId;

    @Schema(title = "搜索户号")
    private String roomId;

    @Schema(title = "账单号")
    private String billNo;

    @Schema(title = "日志类型, 同步：SYNC_LOG， 推送：PUSH_LOG")
    private RefLogType logType;

    @Schema(title = "当日志类型为推送：PUSH_LOG， operationStatus可传：PUSHED， PUSH_FAILED")
    private AptBillOperationStatus operationStatus;

}
