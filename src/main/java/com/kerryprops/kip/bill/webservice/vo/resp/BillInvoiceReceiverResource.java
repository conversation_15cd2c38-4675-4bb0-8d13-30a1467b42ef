package com.kerryprops.kip.bill.webservice.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 账单查询的 view
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class BillInvoiceReceiverResource {

    @Schema(title = "接收人ID")
    private String userId;

    @Schema(title = "接收人名称：当有财务时为KIP登陆账号，当无财务时为'租户默认邮箱'常量")
    private String userName;

    @Schema(title = "接收人手机号")
    private String phoneNumber;

    @Schema(title = "接收人企业端(B端)账号")
    private String loginAccount;

    @Schema(title = "用户邮箱")
    private String email;

    @Schema(title = "用户的备用邮箱，多个以逗号分隔")
    private String emailCc;

    @Schema(title = "发送批次号")
    private Long billReceiverConfigId;

}
