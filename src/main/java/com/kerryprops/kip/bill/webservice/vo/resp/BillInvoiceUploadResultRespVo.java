package com.kerryprops.kip.bill.webservice.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 手动推送JDE业务单至票易通执行结果-响应体Vo
 *
 * <AUTHOR>
 * @date 2024-8-7
 */
@Data
public class BillInvoiceUploadResultRespVo {

    @Schema(title = "单据公司", example = "31007")
    private String kco;

    @Schema(title = "单据类型", example = "RN")
    private String billType;

    @Schema(title = "单据号", example = "19004780")
    private int doc;

    @Schema(title = "付款项", example = "001")
    private String paymentItem;

    @Schema(title = "上传结果")
    private String invoiceResult;

    @Schema(title = "失败原因")
    private String failReason;

}
