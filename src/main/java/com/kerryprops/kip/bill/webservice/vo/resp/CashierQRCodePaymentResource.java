package com.kerryprops.kip.bill.webservice.vo.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.pmw.variables.PayOption;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

@Data
public class CashierQRCodePaymentResource {

    @Schema(title = "支付时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
    private ZonedDateTime paymentTime;

    @Schema(title = "支付金额")
    private BigDecimal paymentAmount;

    @Schema(title = "支付方式")
    private PayOption payOption;

    @Schema(title = "支付订单号")
    private String orderNo;

    @Schema(title = "支付状态")
    private BillPaymentStatus paymentStatus;

    @Schema(title = "付款描述")
    private String paymentDesc;

    @Schema(title = "收款号")
    private String payAct;

}
