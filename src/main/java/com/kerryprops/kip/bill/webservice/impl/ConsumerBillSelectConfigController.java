package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.enums.BillSelectMode;
import com.kerryprops.kip.bill.service.impl.BillSelectConfigService;
import com.kerryprops.kip.bill.webservice.vo.req.BillSelectModeDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * StaffBillSelectConfigController.
 *
 * <AUTHOR> Yu 2025-02-27 15:57:17
 **/
@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "c端账单选择配置")
@RequestMapping(value = "/c/bill/select_configs")
public class ConsumerBillSelectConfigController {

    private final BillSelectConfigService billSelectConfigService;

    @Operation(summary = "查询指定单元的账单选择规则")
    @GetMapping("/rooms/select_mode")
    public ModeVo getMode(@Valid BillSelectModeDto dto) {
        var mode = billSelectConfigService.getBillSelectMode(dto.getProjectId(), dto.getBuildingId(), dto.getRoomId());
        return new ModeVo(mode);
    }

    public record ModeVo(@Schema(title = "账单选择模式") BillSelectMode selectMode) {

    }

}
