package com.kerryprops.kip.bill.webservice.vo.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.ImmutableList;
import com.kerryprops.kip.bill.common.enums.AptPayDeleteStatusEnum;
import com.kerryprops.kip.bill.common.enums.AptPayVerifyStatus;
import com.kerryprops.kip.bill.common.jpa.QueryFilter;
import com.kerryprops.kip.bill.dao.entity.QAptPay;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.querydsl.core.types.Predicate;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.kerryprops.kip.bill.common.enums.BillPayModule.CASHIER_FEE;

/**
 * 杂费配置-按条件审核
 *
 * <AUTHOR>
 * Created Date - 2024-1-4 19:33:05
 */
@Data
public class CashierFeePaysVerifyRequest implements QueryFilter {

    @NotBlank(message = "projectId must not be blank")
    @Schema(title = "楼盘ID", required = true)
    private String projectId;

    @Schema(title = "楼栋ID")
    private String buildingId;

    @Schema(title = "单元ID")
    private String roomId;

    // 支付方式，比如："其他付费"
    @Schema(title = "支付方式")
    private String payType;

    @Schema(title = "支付开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date payDateStart;

    @Schema(title = "支付结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date payDateEnd;

    @Schema(title = "杂费配置id", required = true)
    private Long feeId;

    @Schema(title = "收款号")
    private String payAct;

    @Override
    public List<Optional<Predicate>> predicates() {
        ImmutableList.Builder<Optional<Predicate>> builder = ImmutableList.builder();
        builder.add(Optional.of(CASHIER_FEE).map(QAptPay.aptPay.billPayModule::eq))
                .add(Optional.of(projectId).map(QAptPay.aptPay.projectId::eq))
                .add(Optional.ofNullable(buildingId).map(QAptPay.aptPay.buildingId::eq))
                .add(Optional.ofNullable(UserInfoUtils.maxBindingScope()).map(QAptPay.aptPay.buildingId::in))
                .add(Optional.ofNullable(roomId).map(QAptPay.aptPay.roomId::eq))
                .add(Optional.ofNullable(feeId).map(QAptPay.aptPay.feeId::eq))
                .add(Optional.ofNullable(payDateStart).map(QAptPay.aptPay.payDate::goe))
                .add(Optional.ofNullable(payDateEnd).map(QAptPay.aptPay.payDate::loe))
                .add(Optional.ofNullable(payType).map(QAptPay.aptPay.payType::eq))
                .add(Optional.ofNullable(payAct).map(QAptPay.aptPay.payAct::eq))
                .add(Optional.of(AptPayDeleteStatusEnum.NOT_DELETED.getCode()).map(QAptPay.aptPay.deletedAt::eq))
                .add(Optional.of(AptPayVerifyStatus.TO_BE_VERIFIED).map(QAptPay.aptPay.verifyStatus::eq));

        return builder.build();
    }

}
