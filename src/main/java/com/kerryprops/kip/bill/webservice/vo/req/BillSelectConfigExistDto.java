package com.kerryprops.kip.bill.webservice.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * BillSelectConfigExistDto.
 *
 * <AUTHOR> 2025-02-27 17:06:21
 **/
@Data
public class BillSelectConfigExistDto {

    @NotBlank
    @Schema(title = "楼盘id")
    private String projectId;

    @Schema(title = "楼栋id")
    private String buildingId;

    @Schema(title = "单元id")
    private String roomId;

}
