package com.kerryprops.kip.bill.webservice.vo.req;

import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillPushStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 账单查询的 view
 */
@Data
@Schema(name = "AptBillSearchReqVo")
public class AptBillSearchReqVo extends BaseReqVo {

    @Schema(title = "账单ID")
    private Long id;

    @Schema(title = "账单号")
    private String billNo;

    @Schema(title = "账单年开始")
    private Integer beginYear;

    @Schema(title = "账单年结束")
    private Integer endYear;

    @Schema(title = "账单月开始")
    private Integer beginMonth;

    @Schema(title = "账单月结束")
    private Integer endMonth;

    @Schema(title = "系统状态")
    private BillStatus status;

    @Schema(title = "用户支付状态")
    private BillPaymentStatus paymentStatus;

    @Schema(title = "账单推送状态")
    private BillPushStatus pushStatus;

    @Schema(title = "楼盘ids")
    private List<String> projectIds;

    @Schema(title = "楼栋ID")
    private List<String> buildingIds;

    @Schema(title = "楼层ID")
    private List<String> floorIds;

    @Schema(title = "房间ID")
    private List<String> roomIds;

    @Schema(title = "代扣方案")
    private String pspName;

}
