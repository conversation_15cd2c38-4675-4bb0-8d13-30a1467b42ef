package com.kerryprops.kip.bill.webservice.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class DirectDebitsBatchesQueryRequest {

    @Schema(title = "project ID")
    private String projectId;

    @Schema(title = "代扣订单生成日期, pattern: 'yyyy-MM-dd'")
    private String generatedDate;

    @Schema(title = "代扣方案：目前只有'alipay'")
    private String pspName;

    @Schema(title = "所有状态：AWAIT(待发送)，SENT(已发送)，LAPSED(已失效)")
    private String status;

}
