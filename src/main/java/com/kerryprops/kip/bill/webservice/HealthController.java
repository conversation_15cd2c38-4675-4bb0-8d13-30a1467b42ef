package com.kerryprops.kip.bill.webservice;

import com.kerryprops.kip.bill.webservice.vo.HealthResource;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.health.CompositeHealthContributor;
import org.springframework.boot.actuate.health.HealthContributorRegistry;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.boot.actuate.health.Status;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;
import java.util.stream.Stream;

@Slf4j
@Hidden
@RestController
@AllArgsConstructor
public class HealthController {

    private final HealthContributorRegistry healthRegistry;

    @GetMapping(value = "/health/shallow", produces = "application/json")
    public HealthResource health() {
        try {
            HealthIndicator dbHealthIndicator = (HealthIndicator) ((CompositeHealthContributor) healthRegistry
                    .getContributor("db")).getContributor("dataSource");
            HealthIndicator jdeDBHealthIndicator = (HealthIndicator) ((CompositeHealthContributor) healthRegistry
                    .getContributor("db")).getContributor("jdeDataSource");
            HealthIndicator redisHealthIndicator = (HealthIndicator) healthRegistry.getContributor("redis");

            return Stream.of(dbHealthIndicator.health().getStatus()
                    , jdeDBHealthIndicator.health().getStatus()
                    , redisHealthIndicator.health().getStatus()
            ).allMatch(status -> Objects.equals(Status.UP, status))
                    ? HealthResource.SUCCESS_RESPONSE : HealthResource.FAIL_RESPONSE;
        } catch (Exception e) {
            log.error("health shallow check failed: ", e);
            return HealthResource.FAIL_RESPONSE;
        }
    }

}
