package com.kerryprops.kip.bill.webservice.vo.req;

import com.kerryprops.kip.bill.common.enums.AptPayVerifyStatus;
import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import com.kerryprops.kip.bill.common.enums.BillPayModule;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.PaymentCateEnum;
import com.kerryprops.kip.bill.common.enums.PaymentPayType;
import com.kerryprops.kip.bill.common.utils.IdWorker;
import com.kerryprops.kip.bill.common.utils.PrimaryKeyUtil;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPayConfig;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.utils.BillUtil;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.apache.logging.log4j.util.Strings;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 2023-11-22 17:02:59
 **/
@Getter
@Setter
public class CashierOfflinePayRequest {

    @NotBlank
    @Schema(title = "楼盘", required = true)
    private String projectId;

    @Schema(title = "楼栋")
    private String buildingId;

    @Schema(title = "流水号")
    private String pspTransNo;

    @Schema(title = "户号")
    private String roomId;

    @NotNull
    @Schema(title = "收款日期[yyyy-MM-dd]", required = true)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date paymentTime;

    @NotNull
    @Schema(title = "付费方式", required = true)
    private String payType;

    @Schema(title = "描述")
    private String description;

    @Schema(title = "实收金额")
    private BigDecimal actualAmount;

    @Schema(title = "预收金额")
    private BigDecimal advanceAmount;

    @Schema(title = "预收科目[A003 : 管理费预收 | A004 : 其他预收]，未勾选账单时填写")
    private PaymentCateEnum paymentCate;

    @Schema(title = "勾选的账单id")
    private List<Long> billIds;

    @Schema(title = "营业单位")
    private String bu;

    @Schema(title = "JDE单元号")
    private String unit;

    @Schema(title = "付款人")
    private String alph;

    @Schema(title = "an8，可代表楼栋、单元号等等")
    private String an8;

    @Schema(title = "JDE合同号")
    private String doco;

    public AptPaymentInfo toAptPaymentInfo(PositionItemResponse position) {
        Date now = new Date();
        String paymentId = PrimaryKeyUtil.primaryKey(PrimaryKeyUtil.PAYMENT, null);

        AptPaymentInfo paymentInfo = new AptPaymentInfo();
        paymentInfo.setId(paymentId);
        paymentInfo.setCreateTime(now);
        paymentInfo.setUpdateTime(now);
        paymentInfo.setDeleted(0);
        paymentInfo.setPaymentTransNo(Strings.EMPTY);
        paymentInfo.setXmlUrl(Strings.EMPTY);
        paymentInfo.setOfdUrl(Strings.EMPTY);
        paymentInfo.setFailedReason(Strings.EMPTY);
        paymentInfo.setAgreementNo(Strings.EMPTY);
        paymentInfo.setAmt(actualAmount.doubleValue());
        paymentInfo.setPaymentStatus(BillPaymentStatus.PAID);
        paymentInfo.setBindUserId(UserInfoUtils.getUserId());
        paymentInfo.setUserProfileId(UserInfoUtils.getUserProfileId());
        paymentInfo.setPositionItem(position);
        paymentInfo.setAppliedInvoice(1);
        paymentInfo.setPayType(PaymentPayType.fromInfo(getPayType()));
        paymentInfo.setBillPayModule(BillPayModule.CASHIER);
        paymentInfo.setCreateBy(UserInfoUtils.getKerryAccount());
        paymentInfo.setPayAct(IdWorker.getFlowIdWorkerInstance().nextStrId());
        paymentInfo.setPayChannel(BillPayChannel.OFFLINE);

        paymentInfo.setPaymentTime(getPaymentTime());
        paymentInfo.setProjectId(getProjectId());
        paymentInfo.setBuildingId(getBuildingId());
        paymentInfo.setPspTransNo(getPspTransNo());
        paymentInfo.setRoomId(getRoomId());
        paymentInfo.setDescription(getDescription());
        paymentInfo.setAdvanceAmount(getAdvanceAmount());
        paymentInfo.setPaymentCate(getPaymentCate());
        paymentInfo.setUnit(getUnit());
        paymentInfo.setBu(getBu());
        paymentInfo.setAn8(getAn8());
        paymentInfo.setAlph(getAlph());
        paymentInfo.setDoco(getDoco());
        paymentInfo.setPayTypeInfo(getPayType());
        return paymentInfo;
    }

    public AptPay toAptPay(AptBill bill, AptPaymentInfo payment, AptPayConfig payConfig, PositionItemResponse pos) {
        Double tax = payConfig.getTax();
        double totalAmt = actualAmount.doubleValue();

        AptPay aptPay = new AptPay();
        aptPay.setPayDesc(BillPayChannel.OFFLINE.getInfo());
        aptPay.setPayChannel(BillPayChannel.OFFLINE);
        aptPay.setPayType(payConfig.getPaymentType());
        aptPay.setPayConfigId(payConfig.getId());
        aptPay.setTax(tax);
        aptPay.setTaxAmt(BillUtil.calcTaxAmount(totalAmt, tax, payConfig.getMax()));
        aptPay.setTotalAmt(totalAmt);
        aptPay.setPayAct(payment.getPayAct());
        aptPay.setPayDetail(payConfig.getPaymentDetail());
        aptPay.setVerifyStatus(AptPayVerifyStatus.TO_BE_VERIFIED);
        aptPay.setSendJdeStatus(0);
        aptPay.setPaymentInfoId(payment.getId());
        aptPay.setVersionNum(1);
        aptPay.setPaymentCate(Objects.nonNull(paymentCate) ? paymentCate.name() : PaymentCateEnum.UNKNOWN.name());
        aptPay.setBillPayModule(BillPayModule.CASHIER);
        aptPay.setCreateBy(UserInfoUtils.getKerryAccount());
        aptPay.setPositionItem(pos);
        if (Objects.nonNull(bill)) {
            aptPay.setFloorId(bill.getFloorId());
            aptPay.setBu(bill.getBu());
            aptPay.setUnit(bill.getUnit());
            aptPay.setAn8(bill.getAn8());
            aptPay.setAlph(bill.getAlph());
            aptPay.setDoco(bill.getDoco());
        } else {
            aptPay.setUnit(getUnit());
            aptPay.setBu(getBu());
            aptPay.setAn8(getAn8());
            aptPay.setAlph(getAlph());
            aptPay.setDoco(getDoco());
        }

        aptPay.setAdvanceAmount(getAdvanceAmount());
        aptPay.setComments(getDescription());
        aptPay.setPayTranx(getPspTransNo());
        aptPay.setPayDate(getPaymentTime());
        aptPay.setProjectId(getProjectId());
        aptPay.setBuildingId(getBuildingId());
        aptPay.setRoomId(getRoomId());
        return aptPay;
    }

}
