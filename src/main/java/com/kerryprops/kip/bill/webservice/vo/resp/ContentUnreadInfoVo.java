package com.kerryprops.kip.bill.webservice.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * kerry bill B端App 未读信息VO类
 *
 * <AUTHOR>
 * @Date 2023-06-08
 */
@Data
@Schema
public class ContentUnreadInfoVo {

    @Schema(title = "是否有未读的信息。true：有未读信息；false：无未读信息")
    private boolean hasUnreadContent;

    @Schema(title = "此用户未读信息的个数")
    private long unreadContentCount;

}
