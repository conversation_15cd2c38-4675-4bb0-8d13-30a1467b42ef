package com.kerryprops.kip.bill.webservice.vo.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import com.kerryprops.kip.bill.common.enums.CashierPayStatus;
import com.kerryprops.kip.bill.common.enums.InvoicedStatus;
import com.kerryprops.kip.bill.common.enums.PaymentCateEnum;
import com.kerryprops.kip.bill.common.enums.PaymentPayType;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

@Data
public class CashierFeePaysResource {

    private long id;

    @Schema(title = "楼盘ID")
    private String projectId;

    @Schema(title = "楼栋ID")
    private String buildingId;

    @Schema(title = "楼层ID")
    private String floorId;

    @Schema(title = "单元ID")
    private String roomId;

    @Schema(title = "名称信息")
    private PositionItemResponse positionItem;

    @Schema(title = "收款号")
    private String payAct;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(title = "支付时间")
    private Date payDate;

    @Schema(title = "描述")
    private String comment;

    @Schema(title = "支付方式")
    private String payType;

    @Schema(title = "订单金额(实收金额)")
    private BigDecimal totalAmt;

    @Schema(title = "手续费")
    private double taxAmt;

    @Schema(title = "预收金额")
    private BigDecimal advanceAmt;

    @Schema(title = "预收科目")
    private String paymentCate;

    @Schema(title = "状态")
    private CashierPayStatus status;

    @Schema(title = "关联paymentInfo ID")
    private String paymentInfoId;

    @Schema(title = "操作人")
    private String accountFromType;

    @Schema(title = "收款记录生成时间")
    private Date createTime;

    @Schema(title = "收款记录更新时间")
    private Date updateTime;

    @Schema(title = "杂费配置id")
    private Long feeId;

    @Schema(title = "杂费名")
    private String feeName;

    @Schema(title = "是否写入JDE")
    private Integer sendJdeStatus;

    @Schema(title = "审核状态")
    private Integer verifyStatus;

    @Schema(title = "费用类型说明")
    private BillPayChannel payChannel;

    @Schema(title = "开票金额")
    private BigDecimal canInvoiceBillAmount;

    @Schema(title = "0-未取消, 1-已取消")
    private Integer deletedAt;

    @Schema(title = "预收账单能否开票标识")
    private String isAdvanceBilling = "0";

    @Schema(title = "是否已开票状态")
    private InvoicedStatus invoicedStatus;

    public static CashierFeePaysResource of(AptPay vo) {
        CashierPayStatus status = CashierPayStatus.of(vo);

        CashierFeePaysResource resource = new CashierFeePaysResource();
        resource.setTotalAmt(BigDecimal.valueOf(vo.getTotalAmt()));
        resource.setStatus(status);

        resource.setId(vo.getId());
        resource.setProjectId(vo.getProjectId());
        resource.setBuildingId(vo.getBuildingId());
        resource.setFloorId(vo.getFloorId());
        resource.setRoomId(vo.getRoomId());
        resource.setPositionItem(vo.getPositionItem());
        resource.setPayAct(vo.getPayAct());
        resource.setPayDate(vo.getPayDate());
        resource.setComment(vo.getComments());
        resource.setPayType(vo.getPayType());
        resource.setAdvanceAmt(vo.getAdvanceAmount());
        resource.setPaymentCate(vo.getPaymentCate());
        resource.setPaymentInfoId(vo.getPaymentInfoId());
        resource.setCreateTime(vo.getCreateTime());
        resource.setUpdateTime(vo.getUpdateTime());
        resource.setAccountFromType(vo.getCreateBy());
        resource.setFeeId(vo.getFeeId());
        resource.setFeeName(vo.getFeeName());
        resource.setSendJdeStatus(vo.getSendJdeStatus());
        resource.setVerifyStatus(vo.getVerifyStatus().getCode());
        resource.setPayChannel(vo.getPayChannel());
        resource.setDeletedAt(vo.getDeletedAt());
        resource.setInvoicedStatus(vo.getInvoicedStatus());
        resource.setTaxAmt(vo.getTaxAmt());
        return resource;
    }

    public static CashierFeePaysResource of(AptPaymentInfo vo) {
        CashierPayStatus status = CashierPayStatus.of(vo);
        String payType = vo.getPayTypeInfo();
        if (StringUtils.isBlank(payType)) {
            payType = Optional.ofNullable(vo.getPayType()).map(v -> v.getInfo()).orElse(null);
        }

        CashierFeePaysResource resource = new CashierFeePaysResource();
        resource.setProjectId(vo.getProjectId());
        resource.setBuildingId(vo.getBuildingId());
        resource.setFloorId(vo.getFloorId());
        resource.setRoomId(vo.getRoomId());
        resource.setPositionItem(vo.getPositionItem());
        resource.setPayAct(vo.getPayAct());
        resource.setPayDate(vo.getPaymentTime());
        resource.setComment(vo.getDescription());
        resource.setAdvanceAmt(vo.getAdvanceAmount());
        resource.setPaymentCate(vo.getPaymentCate().name());
        resource.setPaymentInfoId(vo.getId());
        resource.setCreateTime(vo.getCreateTime());
        resource.setUpdateTime(vo.getUpdateTime());
        resource.setAccountFromType(vo.getCreateBy());
        resource.setFeeId(vo.getFeeId());
        resource.setFeeName(vo.getFeeName());
        resource.setInvoicedStatus(vo.getInvoicedStatus());

        resource.setTotalAmt(BigDecimal.valueOf(vo.getAmt()));
        resource.setStatus(status);
        resource.setPayType(payType);
        return resource;
    }

    public String getPaymentCate() {
        if (PaymentCateEnum.UNKNOWN.name().equals(paymentCate)) {
            return null;
        }
        return paymentCate;
    }

    public String getPayType() {
        if (PaymentPayType.UNKNOWN.getInfo().equals(payType)) {
            return null;
        }
        return payType;
    }

}
