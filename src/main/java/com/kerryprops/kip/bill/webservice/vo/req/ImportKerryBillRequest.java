package com.kerryprops.kip.bill.webservice.vo.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ImportKerryBillRequest {

    // 账单列表. 每次最多处理100条账单
    @Valid
    @NotEmpty(message = "bills不能为空")
    @Size(max = 100, message = "bills每次最多处理100条账单")
    List<ImportKerryBill> bills;

    @Data
    public static class ImportKerryBill {

        // UK
        @NotBlank(message = "documentType must not be blank")
        @Schema(title = "账单类型, eg: VC", required = true)
        private String documentType;

        @NotBlank(message = "documentTextDesc must not be blank")
        @Schema(title = "账单的中文描述, eg: 储值卡账单", required = true)
        private String documentTextDesc;

        @NotBlank(message = "documentEngDesc must not be blank")
        @Schema(title = "账单的英文描述, eg: ValueCardStatement", required = true)
        private String documentEngDesc;

        @NotBlank(message = "billFileUrl must not be blank")
        @Schema(title = "账单文件完整路径, eg: http//xx.yy.zz/***_263844200871690_20200511172542.pdf", required = true)
        private String billFileUrl;

        @NotBlank(message = "billFilename must not be blank")
        @Schema(title = "账单文件名称, eg: 263844200871690_20200511172542.pdf", required = true)
        private String billFilename;

        @NotBlank(message = "displayWebsite must not be blank")
        @Schema(title = "网站是否显示，Y为显示, eg: Y", required = true)
        private String displayWebsite;

        @NotBlank(message = "billSource must not be blank")
        @Schema(title = "沈阳储值卡系统标记，Y为显示, eg: C", required = true)
        private String billSource;

        // UK
        @NotBlank(message = "companyCode must not be blank")
        @Schema(title = "公司编码(company code), eg: 31019", required = true)
        private String companyCode;

        @NotBlank(message = "companyName must not be blank")
        @Schema(title = "公司名称, eg: 嘉里(沈阳)房地产开发有限公司", required = true)
        private String companyName;

        // UK
        @NotBlank(message = "an8 must not be blank")
        @Schema(title = "财务的AN8地址, eg: 20335857", required = true)
        private String an8;

        @NotBlank(message = "alph must not be blank")
        @Schema(title = "合同主体名称, eg: 上海办伴科技发展有限公司", required = true)
        private String alph;

        @NotBlank(message = "doco must not be blank")
        @Schema(title = "合同编码, eg: 279684", required = true)
        private String doco;

        // UK
        @NotBlank(message = "mcu must not be blank")
        @Schema(title = "建筑物编号, eg: 43255201", required = true)
        private String mcu;

        @NotBlank(message = "mcuDesc must not be blank")
        @Schema(title = "建筑物描述, eg: 嘉里沈阳，租赁，商场", required = true)
        private String mcuDesc;

        @NotBlank(message = "unit must not be blank")
        @Schema(title = "单元号, eg: T1-1501", required = true)
        private String unit;

        @NotBlank(message = "unitDesc must not be blank")
        @Schema(title = "单元号描述, eg: 沈阳嘉里中心T1号楼1501室", required = true)
        private String unitDesc;

        // UK
        @NotNull(message = "billYear must not be blank")
        @Schema(title = "账单打印时间, eg: 22", required = true)
        private Integer billYear;

        // UK
        @NotNull(message = "billMonth must not be blank")
        @Schema(title = "账单年(公历年的后2位), eg: 6", required = true)
        private Integer billMonth;

        @NotBlank(message = "printTime must not be blank")
        @Schema(title = "账单月(1~12）, eg: 2022-05-09 14:06:45", required = true)
        private String printTime;

        // UK
//        @NotBlank(message = "generateTime must not be blank")
        @Schema(title = "账单生成时间, eg: 2022-05-09 14:06:45", required = true)
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date generateTime;

    }

}
