package com.kerryprops.kip.bill.webservice;


import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.webservice.vo.resp.AptPaymentInfoVo;
import com.kerryprops.kip.bill.webservice.vo.resp.PayableStaffAptBillRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.RoomAndUserSignResource;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffAptBillRespVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/***********************************************************************************************************************
 * Project - accelerator
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - David Wei
 * Created Date - 06/03/2021 14:05
 **********************************************************************************************************************/

@Tag(name = "公寓小区-C端账单管理")
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE)
public interface ConsumerAptBillFacade {

    /**
     * C端: 【账单列表->未支付账单】页面接口
     */
    @GetMapping("/c/bill/payable/{buildingId}/{roomId}")
    @Operation(summary = "查询待缴费账单")
    RespWrapVo<List<PayableStaffAptBillRespVo>> userBillInfoList(
            @SortDefault.SortDefaults(value = {@SortDefault(sort = "beginDate", direction = Sort.Direction.ASC),
                    @SortDefault(sort = "endDate", direction = Sort.Direction.ASC)}) Pageable pageable,
            @PathVariable("buildingId") String buildingId, @PathVariable("roomId") String roomId);

    @GetMapping("/c/bill/payable/lbs")
    @Operation(summary = "查询lbs下待缴费账单")
    RespWrapVo<List<StaffAptBillRespVo>> getLbsPayableBill(
            @SortDefault.SortDefaults(value = {@SortDefault(sort = "beginDate", direction = Sort.Direction.DESC),
                    @SortDefault(sort = "endDate", direction = Sort.Direction.DESC)}) Pageable pageable,
            @RequestParam(value = "lbsId", required = true) String lbsId);

    @GetMapping("/c/room/{roomId}/sign_info")
    @Operation(summary = "查询待缴费账单")
    RoomAndUserSignResource userAndSignInfo(@PathVariable("roomId") String roomId);

    /**
     * C端: 【账单列表->已支付账单】页面接口
     */
    @GetMapping("/c/bill/history/{buildingId}/{roomId}")
    @Operation(summary = "查询历史账单")
    RespWrapVo<List<StaffAptBillRespVo>> historyBill(
            @SortDefault.SortDefaults(value = {@SortDefault(sort = "beginDate", direction = Sort.Direction.ASC),
                    @SortDefault(sort = "endDate", direction = Sort.Direction.ASC)}) Pageable pageable,
            @PathVariable("buildingId") String buildingId, @PathVariable("roomId") String roomId);

    /**
     * C端：【账单列表->缴费记录】页面接口
     */
    @Operation(summary = "查询账单列表")
    @GetMapping("/c/bill/queryPaymentPage/{buildingId}/{roomId}")
    RespWrapVo<List<AptPaymentInfoVo>> queryPaymentPage(@PathVariable("buildingId") String buildingId
            , @PathVariable("roomId") String roomId);

    @Operation(summary = "查询账单详情")
    @GetMapping("/c/bill/queryPaymentInfo/{orderNo}")
    RespWrapVo<AptPaymentInfoVo> queryPaymentInfo(@PathVariable("orderNo") String orderNo);

}
