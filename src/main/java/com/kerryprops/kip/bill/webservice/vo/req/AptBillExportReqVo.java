package com.kerryprops.kip.bill.webservice.vo.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.ImmutableList;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.jpa.QueryFilter;
import com.kerryprops.kip.bill.dao.entity.QAptBill;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.querydsl.core.types.Predicate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.kerryprops.kip.bill.common.utils.PayStatusUtils.getPayStatusPredicate;

/**
 * 账单查询的 view
 */
@Data
@Schema
public class AptBillExportReqVo implements QueryFilter {


    @Schema(title = "付款人")
    private String alph;


    @Schema(title = "费项")
    private List<String> categorys;

    @Schema(title = "楼盘CODE")
    private List<String> projectIds;

    @Schema(title = "楼栋ID列表")
    private List<String> buildingIds;

    @Schema(title = "楼层ID")
    private List<String> floorIds;

    @Schema(title = "房间ID")
    private List<String> roomIds;


    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    @Schema(title = "账单开始")
    private Date beginDate;

    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    @Schema(title = "账单结束")
    private Date endDate;

    @Schema(title = "账单年")
    private Integer year;

    @Schema(title = "账单月")
    private Integer month;

    @Schema(title = "用户支付状态")
    private BillPaymentStatus paymentStatus;

    @Override
    public List<Optional<Predicate>> predicates() {
        Integer beginBillMonth = null;
        Integer endBillMonth = null;
        Optional<Predicate> paymentStatusOptional = Optional.empty();

        if (beginDate != null) {
            Calendar c = Calendar.getInstance();
            c.setTime(beginDate);
            beginBillMonth = c.get(Calendar.YEAR) * 100 + c.get(Calendar.MONTH) + 1;
        }

        if (endDate != null) {
            Calendar c = Calendar.getInstance();
            c.setTime(endDate);
            endBillMonth = c.get(Calendar.YEAR) * 100 + c.get(Calendar.MONTH) + 1;
        }

        if (null != paymentStatus) {
            paymentStatusOptional = getPayStatusPredicate(paymentStatus);
        }

        return ImmutableList.<Optional<Predicate>>builder()
                .add(Optional.ofNullable(0).map(QAptBill.aptBill.deletedAt::eq))
                .add(Optional.ofNullable(maxBindingScope()).map(QAptBill.aptBill.buildingId::in))
                .add(Optional.ofNullable(projectIds).map(QAptBill.aptBill.projectId::in))
                .add(Optional.ofNullable(buildingIds).map(QAptBill.aptBill.buildingId::in))
                .add(Optional.ofNullable(floorIds).map(QAptBill.aptBill.floorId::in))
                .add(Optional.ofNullable(roomIds).map(QAptBill.aptBill.roomId::in))
                .add(Optional.ofNullable(year).map(QAptBill.aptBill.year::eq))
                .add(Optional.ofNullable(month).map(QAptBill.aptBill.month::eq))
                .add(Optional.ofNullable(beginBillMonth).map(QAptBill.aptBill.billMonth::goe))
                .add(Optional.ofNullable(endBillMonth).map(QAptBill.aptBill.billMonth::loe))
                .add(Optional.ofNullable(alph).map(e -> ('%' + e + '%')).map(QAptBill.aptBill.alph::likeIgnoreCase))
                .add(Optional.ofNullable(categorys).map(QAptBill.aptBill.category::in))
                .add(paymentStatusOptional)
                .build();
    }

    public List<String> maxBindingScope() {
        LoginUser loginUser = UserInfoUtils.getUser();
        if (loginUser == null) {
            throw new RuntimeException("not login.");
        }
        if (loginUser.isSuperAdmin()) {
            return null;
        }
        return loginUser.toBuildingIdList();
    }

}
