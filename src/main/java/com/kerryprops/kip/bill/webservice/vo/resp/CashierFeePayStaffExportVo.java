package com.kerryprops.kip.bill.webservice.vo.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 杂费单列表-财务人员-导出Vo
 *
 * <AUTHOR>
 * @date 2024-7-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CashierFeePayStaffExportVo extends CashierFeePayReceptionExportVo {

    // 财务人员能够查询/导出手续费字段
    @ExcelProperty(value = "手续费", index = 9)
    @Schema(title = "手续费")
    private Double taxAmt;

}
