package com.kerryprops.kip.bill.webservice.vo.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 账单查询的 view
 */
@Data
@Schema
public class BizBillRespVo {

    @Schema(title = "账单在KIP平台ID")
    private Long id;

    @Schema(title = "合同号")
    private String tpDoco;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(title = "账单生成日期")
    private Date tpCrtutime;

    @Schema(title = "发单公司")
    private String tpDl03;

    @Schema(title = "用户地址号")
    private String tpAn8;

    @Schema(title = "用户名称")
    private String tpAlph;

    @Schema(title = "账单类型")
    private String tpDct;

    @Schema(title = "账单类型名称")
    private String tpDl01;

    @Schema(title = "账单名称")
    private String tpGtitnm;

    @Schema(title = "账单年")
    private Integer tpFyr;

    @Schema(title = "账单月")
    private Integer tpPn;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(title = "账单发送时间")
    private Date sendDate;

    @Schema(title = "账单文件的链接地址")
    private String fileUrl;

    @Schema(title = "账单文件名")
    private String tpGtfilenm;

    @Schema(title = "查看状态：0未读 1已读")
    private Integer readStatus;

}
