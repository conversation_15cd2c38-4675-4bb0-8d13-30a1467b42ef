package com.kerryprops.kip.bill.webservice;

import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.webservice.vo.resp.EnumResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@Tag(name = "公寓小区-数据字典")
@RequestMapping(value = "", produces = MediaType.APPLICATION_JSON_VALUE)
public interface AptDicFacade {

    @Operation(summary = "C端枚举类型")
    @GetMapping("/c/apt/pay/enums")
    RespWrapVo<Map<String, List<EnumResponse>>> getCEnums(@RequestParam(value = "name", required = false) String name);

    @Operation(summary = "S端枚举类型")
    @GetMapping("/s/apt/pay/enums")
    RespWrapVo<Map<String, List<EnumResponse>>> getSEnums(@RequestParam(value = "name", required = false) String name);

}
