package com.kerryprops.kip.bill.webservice.vo.resp;

import com.kerryprops.kip.bill.dao.entity.BillSendConfigAn8Link;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

@Data
public class BillSendConfigResource {

    private Long id;

    private String doco;

    private String phoneNumber;

    private String tenantManagerId;

    private String emailUsername;

    private String email;

    private String loginNo;

    /* 建筑物 */
    private String mcu;

    /* JDE单元 */
    private String unit;

    private Integer isDel;

    private String manualUpdateOperator;

    private String manualUpdateTime;

    private ZonedDateTime createdTime;

    private ZonedDateTime updatedTime;

    private String buildingId;

    private String buildingName;

    private String projectId;

    private List<BillSendConfigAn8Link> an8LinkList;

}
