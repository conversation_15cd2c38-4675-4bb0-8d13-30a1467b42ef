package com.kerryprops.kip.bill.webservice.impl;

import com.google.common.collect.Maps;
import com.kerryprops.kip.bill.common.aop.BillErrorEnum;
import com.kerryprops.kip.bill.common.constants.AppConstants;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.exceptions.AppException;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.utils.DataScopeUtils;
import com.kerryprops.kip.bill.common.utils.DateUtils;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.clients.HiveClient;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.IBillService;
import com.kerryprops.kip.bill.service.model.leg.Bill;
import com.kerryprops.kip.bill.service.model.leg.BillSelectVo;
import com.kerryprops.kip.bill.service.model.leg.BillUserSelectVo;
import com.kerryprops.kip.bill.service.model.s.BillSearchReqBo;
import com.kerryprops.kip.bill.service.model.s.BillSendHistoryReqDto;
import com.kerryprops.kip.bill.webservice.StaffBillFacade;
import com.kerryprops.kip.bill.webservice.vo.req.BillSearchReqVo;
import com.kerryprops.kip.bill.webservice.vo.req.BillSendHistoryReqVo;
import com.kerryprops.kip.bill.webservice.vo.req.BillSendReqVo;
import com.kerryprops.kip.bill.webservice.vo.req.DocoBillPayer;
import com.kerryprops.kip.bill.webservice.vo.req.EmailResultVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillDetailRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillPayer;
import com.kerryprops.kip.bill.webservice.vo.resp.ExportStaffBillRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffBillHistoryRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffBillReceiverRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffBillRespVo;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.web.SortDefault;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.common.constants.AppConstants.STAFF_BILL_SEND_BILL_LIST_REDIS_KEY;
import static com.kerryprops.kip.bill.common.utils.BillingFun.exportByEasyExcel;

/**
 * 1. super admin: can do any thing;
 * 2. other: can do any thing under bu
 */
@Slf4j
@RestController
public class StaffBillController implements StaffBillFacade {

    @Autowired
    private IBillService billService;

    @Autowired
    private HiveAsClient hiveAsClient;

    @Autowired
    private HiveClient hiveService;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Override
    public RespWrapVo<Page<StaffBillRespVo>> list(@SortDefault.SortDefaults(value = {
            @SortDefault(sort = "tpDoco", direction = Sort.Direction.DESC),
            @SortDefault(sort = "tpFyr", direction = Sort.Direction.DESC),
            @SortDefault(sort = "tpPn", direction = Sort.Direction.DESC),
            @SortDefault(sort = "formatDate", direction = Sort.Direction.DESC)}) Pageable pageable,
                                                  @ModelAttribute BillSearchReqVo billSearchReqVo) {
        BillSearchReqBo billSearchReqBo = BeanUtil.copy(billSearchReqVo, BillSearchReqBo.class);
        populateDataFields(billSearchReqBo, billSearchReqVo.getBuildingIds());

        Page<BillSelectVo> list = billService.selectBillVoList(pageable, billSearchReqBo.toPredicates());
        Page<StaffBillRespVo> staffBillRespVoPage = list.map(e -> BeanUtil.copy(e, StaffBillRespVo.class));

        Map<DocoBillPayer, List<StaffBillRespVo>> an8Map = Maps.newLinkedHashMap();
        for (StaffBillRespVo aBill : staffBillRespVoPage) {
            String doco = aBill.getTpDoco();
            String an8 = aBill.getTpAn8();
            String alph = aBill.getTpAlph();

            if (StringUtils.isAnyEmpty(doco, an8)) {
                aBill.setCheckedFlag(false);
                continue;
            }
            aBill.setCheckedFlag(true);

            DocoBillPayer key = DocoBillPayer.builder().doco(doco).an8(an8).alph(alph).build();
            if (!an8Map.containsKey(key)) {
                an8Map.put(key, new ArrayList<>());
            }
            an8Map.get(key).add(aBill);
        }

        Map<DocoBillPayer, Set<StaffBillReceiverRespVo>> docoAn8SetMap = billService.selectBatchBillReceivers(an8Map.keySet());
        if (docoAn8SetMap != null && !docoAn8SetMap.isEmpty()) {
            for (Map.Entry<DocoBillPayer, Set<StaffBillReceiverRespVo>> docoAn8SetEntry : docoAn8SetMap.entrySet()) {
                String recivers = null;
                if (CollectionUtils.isNotEmpty(docoAn8SetEntry.getValue())) {
                    recivers = Arrays.toString(docoAn8SetEntry.getValue().stream().map(e -> e.toString()).toArray());
                }
                log.info("docoAn8: {}, receivers: {}", docoAn8SetEntry.getKey(), recivers);
            }
            for (Map.Entry<DocoBillPayer, List<StaffBillRespVo>> docoAn8ListEntry : an8Map.entrySet()) {
                DocoBillPayer docoAn8 = docoAn8ListEntry.getKey();
                List<StaffBillRespVo> staffBillRespVos = docoAn8ListEntry.getValue();
                Set<StaffBillReceiverRespVo> userInfoList = docoAn8SetMap.get(docoAn8);
                String recivers = null;
                if (CollectionUtils.isNotEmpty(userInfoList)) {
                    for (StaffBillRespVo staffBillRespVo : staffBillRespVos) {
                        staffBillRespVo.setUserInfoList(userInfoList);
                    }
                    recivers = Arrays.toString(staffBillRespVos.stream().map(StaffBillRespVo::toString).toArray());
                }
                log.info("respond docoAn8: {}, receivers: {}", docoAn8, recivers);
            }
        }
        // query building info from hive
        Map<String, List<HiveClient.HiveBuBuildingResponse>> hiveRespCache = new HashMap<>();
        staffBillRespVoPage.forEach(res -> {
            if (StringUtils.isNotBlank(res.getTpMcu()) && StringUtils.isNotBlank(res.getTpUnit())) {
                List<String> bus = new ArrayList<>(List.of(res.getTpMcu().split(",")));
                List<String> jdeRoomNos = new ArrayList<>(List.of(res.getTpUnit().split(",")));

                bus.sort(String::compareTo);
                jdeRoomNos.sort(String::compareTo);

                final String SPLIT_FLAG_IN_KEY = "_";
                String busJdeRoomNosSorted = StringUtils.join(bus.toArray(), SPLIT_FLAG_IN_KEY)
                        + SPLIT_FLAG_IN_KEY + StringUtils.join(jdeRoomNos.toArray(), SPLIT_FLAG_IN_KEY);
                List<HiveClient.HiveBuBuildingResponse> hiveResp = hiveRespCache.get(busJdeRoomNosSorted);

                if (CollectionUtils.isEmpty(hiveResp)) {
                    HiveClient.HiveBuBuildingRequest hiveReq = new HiveClient.HiveBuBuildingRequest();
                    hiveReq.setBu(bus);
                    hiveReq.setJdeRoomNo(jdeRoomNos);
                    hiveResp = hiveService.getBuildingByBuAndJdeRoomNo(List.of(hiveReq));

                    hiveRespCache.put(busJdeRoomNosSorted, hiveResp);
                }

                if (CollectionUtils.isNotEmpty(hiveResp)) {
                    res.setBuildingId(hiveResp.stream().map(HiveClient.HiveBuBuildingResponse::getId)
                            .filter(StringUtils::isNotBlank).collect(Collectors.joining(",")));
                    res.setBuildingName(hiveResp.stream().map(HiveClient.HiveBuBuildingResponse::getName)
                            .filter(StringUtils::isNotBlank).collect(Collectors.joining(",")));
                }
            }
        });
        return new RespWrapVo<>(staffBillRespVoPage);
    }

    @Override
    public RespWrapVo<List<String>> fuzzyQueryDocos(String projectId, String query) {
        List<String> bus = DataScopeUtils.getBusByProjectId(hiveAsClient, projectId);
        List<String> docos = billService.fuzzySelectDocos(bus, query);
        return new RespWrapVo<>(docos);
    }

    @Override
    public RespWrapVo<List<String>> queryDcts(String projectId) {
        List<String> bus = DataScopeUtils.getBusByProjectId(hiveAsClient, projectId);
        List<String> dcts = billService.selectDcts(bus);
        return new RespWrapVo<>(dcts);
    }

    @Override
    public List<BillPayer> fuzzyQueryPayersByProjectId(String projectId, String delFlag, String query) {
        List<String> bus = DataScopeUtils.getBusByProjectId(hiveAsClient, projectId);
        return billService.fuzzySelectPayers(bus, delFlag, query);
    }

    @Override
    public RespWrapVo<List<String>> queryDocos(String projectId) {
        List<String> bus = DataScopeUtils.getBusByProjectId(hiveAsClient, projectId);
        List<String> docos = billService.selectDocos(bus);
        return new RespWrapVo<>(docos);
    }

    @Override
    public void exportList(@ModelAttribute BillSearchReqVo billSearchReqVo, HttpServletResponse response) {

        BillSearchReqBo billSearchReqBo = BeanUtil.copy(billSearchReqVo, BillSearchReqBo.class);
        populateDataFields(billSearchReqBo, billSearchReqVo.getBuildingIds());
        List<Bill> billUserSelectVos = billService.selectBillVoList(billSearchReqBo.toPredicates());
        List<ExportStaffBillRespVo> staffBillRespVoList = billUserSelectVos.stream().map(e -> BeanUtil.copy(e, ExportStaffBillRespVo.class)).collect(Collectors.toList());

        final String CONTENT_NAME = "bill_list";
        exportByEasyExcel(response, staffBillRespVoList, ExportStaffBillRespVo.class
                , CONTENT_NAME, CONTENT_NAME);
    }

    @Override
    public RespWrapVo<Set<BillPayer>> queryPayers(String doco) {
        Set<BillPayer> billPayers = billService.selectBillPayers(doco);
        RespWrapVo<Set<BillPayer>> respWrapVo = new RespWrapVo<>(billPayers);
        return respWrapVo;
    }

    @Override
    public RespWrapVo<BillDetailRespVo> getInfo(@PathVariable("id") Long id) {
        BillSearchReqBo billSearchReqBo = BillSearchReqBo.builder().id(id).build();
        populateDataFields(billSearchReqBo, null);
        Bill bill = billService.selectBillVo(billSearchReqBo.toPredicates());
        if (bill == null) {
            return new RespWrapVo<>();
        }
        return new RespWrapVo<>(BeanUtil.copy(bill, BillDetailRespVo.class));
    }

    @Override
    public RespWrapVo<Boolean> deleteBill(@RequestParam("ids") List<Long> ids) {
        BillSearchReqBo billSearchReqBo = BillSearchReqBo.builder().tpStatus(0).ids(ids).build();
        populateDataFields(billSearchReqBo, null);
        Boolean result = billService.deleteBill(billSearchReqBo.toPredicates());
        return new RespWrapVo<>(result);
    }

    @Override
    public RespWrapVo<Integer> sendBillList(@RequestBody BillSendReqVo billSendReqVo) {
        if (billSendReqVo == null || CollectionUtils.isEmpty(billSendReqVo.getBillIds())) {
            return new RespWrapVo<>("200010", "没有选择账单无法发送");
        }
        if (!List.of(1, 2).contains(billSendReqVo.getSendType())) {
            return new RespWrapVo<>("200015", "发送类型选择错误");
        }

        LoginUser loginUser = UserInfoUtils.getUser();
        String redisLockKeySuffix = Optional.ofNullable(loginUser).map(user ->
                StringUtils.isEmpty(user.getProjectIds())
                        ? user.getUniqueUserId() : user.getProjectIds()
        ).orElse(StringUtils.EMPTY);

        Boolean hasKey = redisTemplate.hasKey(STAFF_BILL_SEND_BILL_LIST_REDIS_KEY + ':' + redisLockKeySuffix);
        if (Boolean.TRUE.equals(hasKey)) {
            throw AppException.error(BillErrorEnum.DRAW_CONCURRENT_OPERATION);
        }

        BillSearchReqBo billSearchReqBo = BillSearchReqBo.builder().ids(billSendReqVo.getBillIds()).build();
        populateDataFields(billSearchReqBo, null);
        List<Bill> billUserSelectVos = billService.selectBillVoList(billSearchReqBo.toPredicates());
        if (CollectionUtils.isEmpty(billUserSelectVos)) {
            return new RespWrapVo<>(0);
        }

        billService.sendBillList(billUserSelectVos, billSendReqVo.getSendType(), redisLockKeySuffix
                , UserInfoUtils.getUser().getNickName());
        return new RespWrapVo<>(billUserSelectVos.size());
    }

    @Override
    public RespWrapVo<Page<StaffBillHistoryRespVo>> sendHistory(Pageable pageable,
                                                                @ModelAttribute BillSendHistoryReqVo billSendHistoryReqVo) {

        BillSendHistoryReqDto billSendHistoryReqDto = new BillSendHistoryReqDto();

        setBillSendHistoryReqDto(billSendHistoryReqVo, billSendHistoryReqDto);

        if (billSendHistoryReqVo.getEmailDateStart() != null) {
            var dateStart = DateUtils.dateTime(billSendHistoryReqVo.getEmailDateStart());
            billSendHistoryReqDto.setEmailDateStart(dateStart);
        }

        if (billSendHistoryReqVo.getEmailDateEnd() != null) {
            var dateEnd = DateUtils.dateTime(billSendHistoryReqVo.getEmailDateEnd());
            billSendHistoryReqDto.setEmailDateEnd(dateEnd);
        }

        List<String> bus = buildBusToQueryHistoryBill(billSendHistoryReqVo);
        if (CollectionUtils.isEmpty(bus)) {
            billSendHistoryReqDto.setBus("");
        } else {
            billSendHistoryReqDto.setBus(bus.stream().collect(Collectors.joining(AppConstants.COMMA)));
        }

        Page<BillUserSelectVo> list = billService.searchPagedBill(pageable, billSendHistoryReqDto);
        Page<StaffBillHistoryRespVo> staffBillHistoryRespVos = list.map(e -> {
            if (Objects.isNull(e.getEmailDate())) {
                e.setEmailDate(e.getMailDate());
            }

            return BeanUtil.copy(e, StaffBillHistoryRespVo.class);
        });

        staffBillHistoryRespVos.stream().map(this::jointAlphWithAn8).collect(Collectors.toList());
        return new RespWrapVo<>(staffBillHistoryRespVos);
    }

    @Override
    public void exportHistory(@ModelAttribute BillSendHistoryReqVo billSendHistoryReqVo,
                              HttpServletResponse response) {
        var billSendHistoryReqDto = new BillSendHistoryReqDto();

        setBillSendHistoryReqDto(billSendHistoryReqVo, billSendHistoryReqDto);

        if (billSendHistoryReqVo.getEmailDateStart() != null) {
            var dateStart = DateUtils.dateTime(billSendHistoryReqVo.getEmailDateStart());
            billSendHistoryReqDto.setEmailDateStart(dateStart);
        }

        if (billSendHistoryReqVo.getEmailDateEnd() != null) {
            var dateEnd = DateUtils.dateTime(billSendHistoryReqVo.getEmailDateEnd());
            billSendHistoryReqDto.setEmailDateEnd(dateEnd);
        }

        List<String> bus = buildBusToQueryHistoryBill(billSendHistoryReqVo);
        if (CollectionUtils.isEmpty(bus)) {
            billSendHistoryReqDto.setBus("");
        } else {
            billSendHistoryReqDto.setBus(bus.stream().collect(Collectors.joining(AppConstants.COMMA)));
        }
        List<BillUserSelectVo> list = billService.searchPagedBill(billSendHistoryReqDto);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<StaffBillHistoryRespVo> staffBillHistoryRespVos = list.stream()
                .map(e -> {
                    if (Objects.isNull(e.getEmailDate())) {
                        e.setEmailDate(e.getMailDate());
                    }
                    return BeanUtil.copy(e, StaffBillHistoryRespVo.class);
                })
                .collect(Collectors.toList());
        staffBillHistoryRespVos.stream().map(this::jointAlphWithAn8).collect(Collectors.toList());

        final String CONTENT_NAME = "bill_history";
        exportByEasyExcel(response, staffBillHistoryRespVos, StaffBillHistoryRespVo.class
                , CONTENT_NAME, CONTENT_NAME);
    }

    @Override
    public RespWrapVo<Boolean> emailCallBack(@Valid @RequestBody EmailResultVo emailResultVo) {
        return new RespWrapVo<>(billService.emailCallBack(emailResultVo));
    }

    private void populateDataFields(BillSearchReqBo billSearchReqBo, List<String> searchedBuildingIds) {
        List<String> searchedBus;
        List<String> maxBus;
        List<String> busRelatedToProjectId;
        LoginUser loginUser = UserInfoUtils.getUser();
        if (loginUser == null) {
            log.info("User not login.");
            throw new RuntimeException("User not login.");
        }
        if (CollectionUtils.isEmpty(searchedBuildingIds)) {
            searchedBus = null;
        } else {
            searchedBus = hiveAsClient.convertToJdeBus(searchedBuildingIds);
        }

        if (loginUser.isSuperAdmin()) {
            maxBus = null;
        } else {
            maxBus = hiveAsClient.convertToJdeBus(loginUser.toBuildingIdList());
        }

        if (StringUtils.isEmpty(billSearchReqBo.getProjectId())) {
            busRelatedToProjectId = null;
        } else {
            String searchedProjectId = billSearchReqBo.getProjectId();
            String[] projectIdArr = StringUtils.split(searchedProjectId, AppConstants.COMMA);
            busRelatedToProjectId = hiveAsClient.convertToJdeBus(projectIdArr);
        }

        billSearchReqBo.setSearchedBus(searchedBus);
        billSearchReqBo.setMaxBus(maxBus);
        billSearchReqBo.setBusRelatedToProjectId(busRelatedToProjectId);
    }

    private void setBillSendHistoryReqDto(BillSendHistoryReqVo billSendHistoryReqVo,
                                          BillSendHistoryReqDto billSendHistoryReqDto) {

        if (StringUtils.isNotEmpty(billSendHistoryReqVo.getTpAlph())) {
            billSendHistoryReqDto.setTpAlph(billSendHistoryReqVo.getTpAlph());
        }

        if (StringUtils.isNotEmpty(billSendHistoryReqVo.getTpMcu())) {
            billSendHistoryReqDto.setTpMcu(billSendHistoryReqVo.getTpMcu());
        }

        if (StringUtils.isNotEmpty(billSendHistoryReqVo.getTpDoco())) {
            billSendHistoryReqDto.setTpDoco(billSendHistoryReqVo.getTpDoco());
        }

        if (StringUtils.isNotEmpty(billSendHistoryReqVo.getTpDct())) {
            billSendHistoryReqDto.setTpDct(billSendHistoryReqVo.getTpDct());
        }

        if (null != billSendHistoryReqVo.getTpFyr()) {
            billSendHistoryReqDto.setTpFyr(billSendHistoryReqVo.getTpFyr());
        }

        if (null != billSendHistoryReqVo.getTpPn()) {
            billSendHistoryReqDto.setTpPn(billSendHistoryReqVo.getTpPn());
        }
    }

    private StaffBillHistoryRespVo jointAlphWithAn8(StaffBillHistoryRespVo vos) {
        if (null != vos) {
            vos.setTpAlph(vos.getTpAlph() + '-' + vos.getTpAn8());
        }
        return vos;
    }

    private List<String> buildBusToQueryHistoryBill(BillSendHistoryReqVo billSendHistoryReqVo) {
        LoginUser loginUser = UserInfoUtils.getUser();
        if (loginUser == null) {
            throw new RuntimeException("User not login.");
        }

        List<String> searchedBu = null;
        List<String> searchedBuildingBus = hiveAsClient.convertToJdeBus(billSendHistoryReqVo.getBuildingIds());
        List<String> searchedProjectBus = hiveAsClient.convertToJdeBus(StringUtils.split(billSendHistoryReqVo.getProjectId()
                , AppConstants.COMMA));
        if (CollectionUtils.isNotEmpty(searchedProjectBus) && CollectionUtils.isEmpty(searchedBuildingBus)) {
            searchedBu = searchedProjectBus;
        } else if (CollectionUtils.isEmpty(searchedProjectBus) && CollectionUtils.isNotEmpty(searchedBuildingBus)) {
            searchedBu = searchedBuildingBus;
        } else if (CollectionUtils.isNotEmpty(searchedProjectBus) && CollectionUtils.isNotEmpty(searchedBuildingBus)) {
            searchedBu = searchedProjectBus.stream().filter(s -> searchedBuildingBus.contains(s)).collect(Collectors.toList());
        }

        List<String> bus;
        List<String> maxBus = hiveAsClient.convertToJdeBus(loginUser.toBuildingIdList());
        if (loginUser.isSuperAdmin()) {
            bus = searchedBu;
        } else {
            if (CollectionUtils.isEmpty(maxBus)) {
                throw new RuntimeException("Invalid user.");
            }
            if (CollectionUtils.isEmpty(searchedBu)) {
                bus = maxBus;
            } else {
                bus = searchedBu.stream().filter(e -> maxBus.contains(e)).collect(Collectors.toList());
            }
        }
        return bus;
    }

    private RespWrapVo checkPermission(List<String> bus, List<String> searchProjectIds, List<Long> searchedBuildingIds) {
//        LoginUser loginUser = UserInfoUtils.getUser();
//        List<String> maxProjectCodeScope = loginUser.toProjectCodeList();
//        List<Long> maxBuildingIdScope = loginUser.toBuildingIdList();
//        Boolean isSuperAdmin = loginUser.isSuperAdmin();
//
//        if(!isSuperAdmin && CollectionUtils.isEmpty(searchProjectCodes) && CollectionUtils.isEmpty(searchedBuildingIds)){
//            log.debug("Initial current user max query scope to : {}", maxBuildingIdScope);
//            searchedBuildingIds = maxBuildingIdScope;
//        }
//
//        if(CollectionUtils.isNotEmpty(searchedBuildingIds)){
//            List<Long> unAuthedBuildingIds = searchedBuildingIds.stream().filter(e -> !maxBuildingIdScope.contains(e)).collect(Collectors.toList());
//            if(CollectionUtils.isNotEmpty(unAuthedBuildingIds)){
//                log.error("权限不足：无权查看未授权bu账单。 当前用户授权bu：{}， 准备查看的bu：{}", Arrays.toString(maxBuildingIdScope.toArray()), Arrays.toString(searchedBuildingIds.toArray()));
//                return new RespWrapVo<>("200011", "权限不足：无权查看未授权账单");
//            }
//
//            RespWrapVo<Set<BuildingRespVo>> buildingRespWrapVo = hiveAsClient.getBuildingByIds(searchedBuildingIds);
//            if(!RespWrapVo.isResponseValid(buildingRespWrapVo)){
//                log.error("find building by building ids failed. {}", buildingRespWrapVo);
//                throw new RuntimeException("find bu by building ids failed");
//            }
//
//            Set<BuildingRespVo> buildingRespVos = buildingRespWrapVo.getData();
//            if(CollectionUtils.isEmpty(buildingRespVos)){
//                log.debug("No building found by building Id. {}", Arrays.toString(searchedBuildingIds.toArray()));
//            }
//            for (BuildingRespVo buildingRespVo : buildingRespVos) {
//                if(Objects.isNull(buildingRespVo) || Objects.isNull(buildingRespVo.getBuilding()) || StringUtils.isEmpty(buildingRespVo.getBuilding().getPropertyManagementBU())){
//                    log.debug("Kip building is not binding with jde bu: {}", buildingRespVo);
//                }
//                bus.add(buildingRespVo.getBuilding().getPropertyManagementBU());
//            }
//
//            if(CollectionUtils.isEmpty(bus)){
//                log.warn("No bu found by buildIds: {}", searchedBuildingIds);
//                return new RespWrapVo<>();
//            }
//        } else if(CollectionUtils.isNotEmpty(searchProjectCodes)){
//            List<String> unAuthedProjectCodes = searchProjectCodes.stream().filter(e -> !maxProjectCodeScope.contains(e)).collect(Collectors.toList());
//            if(CollectionUtils.isNotEmpty(unAuthedProjectCodes)){
//                log.error("权限不足：无权查看未授权project账单。 当前用户授权project：{}， 准备查看的project：{}", Arrays.toString(maxProjectCodeScope.toArray()), Arrays.toString(searchProjectCodes.toArray()));
//                return new RespWrapVo<>("200011", "权限不足：无权查看未授权账单");
//            }
//            RespWrapVo<List<CenterRespVo>> projectRespWrapVo = hiveAsClient.getCenterByIds((String[])searchProjectCodes.toArray());
//            if(!RespWrapVo.isResponseValid(projectRespWrapVo)){
//                log.error("find project by project codes failed.", projectRespWrapVo);
//                throw new RuntimeException("find project by project codes failed");
//            }
//
//            List<CenterRespVo> centerRespVos = projectRespWrapVo.getData();
//            if(CollectionUtils.isEmpty(centerRespVos)){
//                log.debug("No project found by project codes. {}", Arrays.toString(searchProjectCodes.toArray()));
//            }
//            for (CenterRespVo centerRespVo : centerRespVos) {
//                List<BuildingVo> buildingVos = centerRespVo.getBuildings();
//                if(CollectionUtils.isEmpty(buildingVos)){
//                    log.debug("Project is not binding building.", centerRespVo);
//                }
//                for (BuildingVo building : buildingVos) {
//                    BuildingRespDto buildingRespDto = building.getBuilding();
//                    if(Objects.isNull(buildingRespDto) || StringUtils.isEmpty(buildingRespDto.getPropertyManagementBU())){
//                        log.debug("Kip building is not binding with jde bu: {}", buildingRespDto);
//                    }
//                    bus.add(buildingRespDto.getPropertyManagementBU());
//                }
//            }
//            if(CollectionUtils.isEmpty(bus)){
//                log.warn("No bu found by projectCodes: {}", searchProjectCodes);
//                return new RespWrapVo<>();
//            }
//        }
//
//        if(!isSuperAdmin && CollectionUtils.isEmpty(bus)){
//            log.warn("No bu found: {}", loginUser);
//            return new RespWrapVo<>();
//        }
        return null;
    }

//    @Override
//    public RespWrapVo<Page<BillUserSelectVo>> readInfoList(@SortDefault.SortDefaults(value = {
//            @SortDefault(sort = "tpCrtutime", direction = Sort.Direction.DESC),
//            @SortDefault(sort = "createTime", direction = Sort.Direction.DESC),
//            @SortDefault(sort = "updateTime", direction = Sort.Direction.DESC)}) Pageable pageable,
//                                                           @ModelAttribute BillReadSearchReqVo billReadSearchReqVo){
//
//        RespWrapVo validateResult = validateSPermission(billReadSearchReqVo);
//        if(validateResult != null){
//            return validateResult;
//        }
//
//        Page<BillUserSelectVo> list = billService.searchPagedBill(pageable, billReadSearchReqVo.toPredicates());
//        return new RespWrapVo<>(list);
//    }
}
