package com.kerryprops.kip.bill.webservice.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Auther: zjy
 * @Date: 2021/7/7 17:51
 * @Description:
 */
@Data
public class CallBackKerryInvoiceMainVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 业务单号
     */
    @Schema(title = "业务单号")
    private String salesbillNo;

    /**
     * 单据类型(公寓HMS、停车场CARPARK等)
     */
    @Schema(title = "单据类型(公寓HMS、停车场CARPARK等)")
    private String salesbillType;

    /**
     * 业务单金额
     */
    @Schema(title = "业务单含税金额（已开部分）")
    private BigDecimal salesbillAmountWithTax;

    @Schema(title = "业务单关联的已开不含税金额（已开部分）")
    private BigDecimal salesbillAmountWithoutTax;

    @Schema(title = "业务单关联的税金（已开部分）")
    private BigDecimal salesbillTaxAmount;

    /**
     * 业务单次序：记录同一发票下，业务单的次序，从1开始
     */
    @Schema(title = "业务单次序：记录同一发票下，业务单的次序")
    private int salesbillNumber;

    @Schema(title = "发票类型：gvat-增值税普通发票 svat-增值税专⽤发票 gvate-增值税电⼦普票发票 svate-增值税电⼦专⽤发票" +
            " gvatq-数电普通发票 svatq-数电专用发票 gvatz-数电纸质普通发票 svatz-数电纸质专用发票")
    private String invoiceType;

    /**
     * 发票备注
     */
    @Schema(title = "发票备注")
    private String remark;

    /**
     * 销方统一信用代码
     */
    @Schema(title = "销方统一信用代码 ")
    private String sellerNo;

    /**
     * 销方银行账号
     */
    @Schema(title = "销方银行账号")
    private String sellerBankAccount;

    /**
     * 销方地址
     */
    @Schema(title = "销方地址")
    private String sellerAddress;

    /**
     * 销方银行名称
     */
    @Schema(title = "销方银行名称")
    private String sellerBankName;

    /**
     * 销方名称
     */
    @Schema(title = "销方名称")
    private String sellerName;

    /**
     * 销方税号
     */
    @Schema(title = "销方税号")
    private String sellerTaxNo;

    /**
     * 销方电话
     */
    @Schema(title = "销方电话")
    private String sellerTel;

    /**
     * 接收邮箱 邮箱票易通发
     */
    @Schema(title = "接收邮箱 邮箱票易通发")
    private String receiveUserEmail;

    /**
     * 接收方电话 短信由各平台发
     */
    @Schema(title = "接收方电话 短信由各平台发")
    private String receiveUserTel;

    /**
     * 购方编号
     */
    @Schema(title = "购方编号")
    private String purchaserNo;

    /**
     * 购方抬头（专票必填、禁止特殊字符）
     */
    @Schema(title = "购方抬头（专票必填、禁止特殊字符）")
    private String purchaserName;

    /**
     * 购方税号
     */
    @Schema(title = "购方税号")
    private String purchaserTaxNo;

    /**
     * 公司地址 （专票必填、地址电话总长不能超过100字节，一个中文占两个字节，一个英文或数字占一个字节，禁止特殊字符）
     */
    @Schema(title = "公司地址 （专票必填、地址电话总长不能超过100字节，一个中文占两个字节，一个英文或数字占一个字节，禁止特殊字符）")
    private String purchaserAddress;

    /**
     * 公司电话（专票必填）
     */
    @Schema(title = "公司电话（专票必填）")
    private String purchaserTel;

    /**
     * 银行名称  （专票必填、银行名称账号总长不能超过100字节，一个中文占两个字节，一个英文或数字占一个字节 ，禁止特殊字符）
     */
    @Schema(title = "银行名称  （专票必填、银行名称账号总长不能超过100字节，一个中文占两个字节，一个英文或数字占一个字节 ，禁止特殊字符）")
    private String purchaserBankName;

    /**
     * 银行账号  （专票必填、银行名称账号总长不能超过100字节，一个中文占两个字节，一个英文或数字占一个字节 ，禁止特殊字符）
     */
    @Schema(title = "银行账号  （专票必填、银行名称账号总长不能超过100字节，一个中文占两个字节，一个英文或数字占一个字节 ，禁止特殊字符）")
    private String purchaserBankAccount;

    /**
     * 计价方式： 0 不含税单价、1.含税单价 值为1 只传含税金额
     */
    @Schema(title = "计价方式： 0 不含税单价、1.含税单价 值为1 只传含税金额")
    private Integer priceMethod;

    /**
     * 含税金额 ，保留2位小数
     */
    @Schema(title = "含税金额 ，保留2位小数")
    private BigDecimal amountWithTax;

    /**
     * 不含税金额 ，保留2位小数
     */
    @Schema(title = "不含税金额 ，保留2位小数")
    private BigDecimal amountWithoutTax;

    /**
     * 税额 ，保留2位小数
     */
    @Schema(title = "税额 ，保留2位小数")
    private BigDecimal taxAmount;

    @Schema(title = "税率，如：0.090")
    private String taxRate;

    /**
     * 收款人姓名（优先使用该参数）如果没有则可以从发票管理平台
     */
    @Schema(title = "收款人姓名（优先使用该参数）如果没有则可以从发票管理平台")
    private String cashierName;

    /**
     * 复核人姓名（优先使用该参数）如果没有则可以从发票管理平台取
     */
    @Schema(title = "复核人姓名（优先使用该参数）如果没有则可以从发票管理平台取")
    private String checkerName;

    /**
     * 开票人姓名（优先使用该参数）如果没有则可以从发票管理平台取
     */
    @Schema(title = "开票人姓名（优先使用该参数）如果没有则可以从发票管理平台取")
    private String invoicerName;

    /**
     * 业态：31074300、31074400 票易通 ext1
     */
    @Schema(title = "业态：31074300、31074400 票易通 ext1 ")
    private String businessType;

    /**
     * 扩展字段1 北京特殊情况，枚举A/R、BJKR
     */
    @Schema(title = "扩展字段1 北京特殊情况，枚举A/R、BJKR 票易通 ext2")
    private String ext1;

    /**
     * 扩展字段2  票易通 ext3
     */
    @Schema(title = "扩展字段2  票易通 ext3")
    private String ext2;

    @Schema(title = "扩展字段3  票易通 ext4")
    private String ext3;

    @Schema(title = "发票号码")
    private String invoiceNo;

    @Schema(title = "发票代码")
    private String invoiceCode;

    @Schema(title = "开票日期")
    private String paperDrewDate;

    @Schema(title = "校验码")
    private String checkCode;

    /**
     * 发票路径
     */
    @Schema(title = "发票路径")
    private String pdfUrl;

    @Schema(title = "数电xml地址")
    private String xmlUrl;

    @Schema(title = "发票ofd地址")
    private String ofdUrl;

    @Schema(title = "数电：红冲场景回调字段：开具原因")
    private String makingReason;

    @Schema(title = "开票状态 1-成功 0-失败，包括数据校验和自动开具反馈失败")
    private int invoiceStatus = 1;

    @Schema(title = "发票状态，1-正常，0-作废")
    private int status = 1;

    @Schema(title = "红冲状态：0-默认，1-待红冲，2-部分待红冲，3-红冲，4-部分红冲，5-红冲发票")
    private String redFlag;

    @Schema(title = "失败信息")
    private String message;

    @Schema(title = "系统来源类型: 0接口传入，1页面导入，2手工开票")
    private String systemOrigType;

    @Schema(title = "系统来源，如：JDE，KIP-BILLING等")
    private String systemOrig;

    @Schema(title = "明细")
    private List<CallBackKerryInvoiceDetailVo> detailVos;

}