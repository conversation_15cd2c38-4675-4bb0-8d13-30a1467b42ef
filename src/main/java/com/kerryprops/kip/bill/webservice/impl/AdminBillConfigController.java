package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.exceptions.AppException;
import com.kerryprops.kip.bill.dao.entity.BillConfigEntity;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.IBillConfigService;
import com.kerryprops.kip.bill.webservice.vo.req.BillConfigAddDto;
import com.kerryprops.kip.bill.webservice.vo.req.BillConfigListDto;
import com.kerryprops.kip.bill.webservice.vo.req.BillConfigUpdateDto;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * AdminBillConfigController.
 *
 * <AUTHOR> Yu 2025-03-10 10:44:20
 **/
@Hidden
@RestController
@RequiredArgsConstructor
@RequestMapping("/admin/bill/configs")
public class AdminBillConfigController {

    private final IBillConfigService billConfigService;

    @Operation(summary = "获取账单配置详情")
    @GetMapping("/{id}")
    public BillConfigEntity getBillConfig(@PathVariable Long id) {
        validAdmin();
        return billConfigService.getBillConfig(id);
    }

    @Operation(summary = "查询账单配置列表")
    @GetMapping
    public List<BillConfigEntity> listBillConfigs(BillConfigListDto queryDto) {
        validAdmin();
        return billConfigService.listBillConfigs(queryDto);
    }

    @Operation(summary = "新增账单配置")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public BillConfigEntity addBillConfig(@Valid @RequestBody BillConfigAddDto dto) {
        validAdmin();
        return billConfigService.addBillConfig(dto);
    }

    @Operation(summary = "更新账单配置")
    @PutMapping("/{id}")
    public BillConfigEntity updateBillConfig(@PathVariable Long id,
                                             @RequestBody BillConfigUpdateDto dto) {
        validAdmin();
        return billConfigService.updateBillConfig(id, dto);
    }

    @Operation(summary = "删除账单配置")
    @DeleteMapping("/{id}")
    public BillConfigEntity deleteBillConfig(@PathVariable Long id) {
        validAdmin();
        return billConfigService.deleteBillConfigById(id);
    }

    private void validAdmin() {
        if (Boolean.TRUE.equals(UserInfoUtils.isSuperAdmin())) {
            return;
        }
        throw new AppException("403001", "必须是管理员才能访问该接口");
    }

}
