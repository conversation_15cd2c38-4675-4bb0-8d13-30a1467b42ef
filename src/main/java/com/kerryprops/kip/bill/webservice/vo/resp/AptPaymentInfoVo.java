package com.kerryprops.kip.bill.webservice.vo.resp;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.kerryprops.kip.bill.common.enums.BillPayModule;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.PaymentCateEnum;
import com.kerryprops.kip.bill.common.enums.PaymentPayType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;


@Data
public class AptPaymentInfoVo {

    @Schema(title = "ID")
    private String id;

    @Schema(title = "金额")
    private Double amt;

    @Schema(title = "支付状态")
    private BillPaymentStatus paymentStatus;

    @Schema(title = "支付session")
    private String paySession;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(title = "创建时间")
    private Timestamp createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(title = "更新时间")
    private Timestamp updateTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(title = "支付时间")
    private Timestamp paymentTime;

    @Schema(title = "楼栋信息")
    private PositionItemResponse positionItem;

    private String billingStatus = "0";

    private List<AptBillVo> aptBillList;

    @Schema(title = "收款号")
    private String payAct;

    /**
     * 后端处理逻辑： -1，取消不可开票，0 - 初始值，1 - 未开票，2 - 开票成功，3 - 开票失败
     */
    @Schema(title = "是否可以开票")
    private Integer appliedInvoice;

    @Schema(title = "支付方式")
    private PaymentPayType payType;

    @Schema(title = "取消类型")
    private String cancelType;

    @Schema(title = "开票地址")
    private String invoiceUrl;

    @Schema(title = "发票地址，xml格式")
    private String xmlUrl;

    @Schema(title = "发票地址，ofd格式")
    private String ofdUrl;

    @Schema(title = "代扣失败原因")
    private String failedReason;

    @Schema(title = "开票金额")
    private BigDecimal canInvoiceBillAmount;

    @Schema(title = "预收金额")
    private BigDecimal advanceAmount;

    @Schema(title = "预收科目")
    private PaymentCateEnum paymentCate;

    @Schema(title = "是否预收账单（给前端用于能否开票判断)[1:可开票|其他:不可开票]")
    private String isAdvanceBilling = "0";

    @Schema(title = "录入收款的前端模块")
    private BillPayModule billPayModule;

    @Schema(title = "杂费名")
    private String feeName;

    @Schema(title = "杂费id")
    private Long feeId;

    @Schema(title = "楼栋id", description = "前端在没有bills时，计算支付方式时所需")
    private String buildingId;

    @Schema(title = "楼盘id")
    private String projectId;

    @Schema(title = "楼层id")
    private String floorId;

    @Schema(title = "房间id")
    private String roomId;

}
