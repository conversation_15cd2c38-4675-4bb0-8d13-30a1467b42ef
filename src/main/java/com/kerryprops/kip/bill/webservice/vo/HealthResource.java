package com.kerryprops.kip.bill.webservice.vo;

import lombok.Data;

import java.util.Objects;

@Data
public class HealthResource {

    public static final int SUCCESS = 0;

    public static final HealthResource SUCCESS_RESPONSE = new HealthResource() {

        @Override
        public int getHealth() {
            return SUCCESS;
        }
    };

    public static final HealthResource FAIL_RESPONSE = new HealthResource();

    private int health = Integer.MIN_VALUE;

    public static boolean isSuccess(HealthResource healthResponse) {
        return Objects.nonNull(healthResponse) && SUCCESS == healthResponse.health;
    }

}
