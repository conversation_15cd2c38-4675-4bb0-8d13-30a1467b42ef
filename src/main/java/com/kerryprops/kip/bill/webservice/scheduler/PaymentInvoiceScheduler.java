package com.kerryprops.kip.bill.webservice.scheduler;

import com.kerryprops.kip.bill.common.enums.InvoicedStatus;
import com.kerryprops.kip.bill.dao.AptPayRepository;
import com.kerryprops.kip.bill.dao.AptPaymentInfoRepository;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.feign.clients.KipInvoiceClient;
import com.kerryprops.kip.bill.feign.entity.InvoiceVo;
import com.kerryprops.kip.bill.log4j.BSConversationFilter;
import com.kerryprops.kip.bill.service.impl.InvoiceApplicationService;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Stream;

/**
 * <AUTHOR> 2024-03-14 15:03:38
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class PaymentInvoiceScheduler {

    private final AptPaymentInfoRepository aptPaymentInfoRepository;

    private final AptPayRepository aptPayRepository;

    private final EntityManager entityManager;

    private final KipInvoiceClient kipInvoiceClient;

    private final InvoiceApplicationService invoiceApplicationService;

    @Scheduled(cron = "${scheduler.pullInvoicedStatus:0 0 1 * * ?}")
    @SchedulerLock(name = "kip:billing:pullInvoicedStatus:task")
    @Transactional
    public void pullInvoicedStatus() {
        BSConversationFilter.setLoggingContext();
        log.info("start_task_pullInvoicedStatus");
        AtomicInteger syncSum = new AtomicInteger();
        long start = System.currentTimeMillis();
        try (Stream<AptPaymentInfo> stream = aptPaymentInfoRepository.streamAllByInvoicedStatus(InvoicedStatus.TO_BE_SYNC)) {
            stream.forEach(v -> {
                entityManager.detach(v);

                String paymentId = v.getId();
                log.info("pull invoiced status. paymentId : {}", paymentId);
                List<InvoiceVo> vos = StringUtils.isBlank(paymentId) ?
                        Collections.emptyList() :
                        kipInvoiceClient.listBillingInvoice(paymentId);
                InvoicedStatus invoicedStatus = vos.isEmpty() ? InvoicedStatus.NO_INVOICED : InvoicedStatus.HAS_INVOICED;

                invoiceApplicationService.updateInvoicedStatus(paymentId, invoicedStatus);

                syncSum.getAndIncrement();
            });
        }
        try (Stream<AptPay> stream = aptPayRepository.streamAllByInvoicedStatus(InvoicedStatus.TO_BE_SYNC)) {
            stream.forEach(v -> {
                entityManager.detach(v);

                String paymentId = v.getPaymentInfoId();
                log.info("pull invoiced status. paymentId : {}", paymentId);
                List<InvoiceVo> vos = StringUtils.isBlank(paymentId) ?
                        Collections.emptyList() :
                        kipInvoiceClient.listBillingInvoice(paymentId);
                InvoicedStatus invoicedStatus = vos.isEmpty() ? InvoicedStatus.NO_INVOICED : InvoicedStatus.HAS_INVOICED;

                invoiceApplicationService.updateInvoicedStatus(paymentId, invoicedStatus);

                syncSum.getAndIncrement();
            });
        }
        long cost = System.currentTimeMillis() - start;
        log.info("start_task_pullInvoicedStatus_done. syncSum: {} || cost : {} ms ", syncSum, cost);
    }

}
