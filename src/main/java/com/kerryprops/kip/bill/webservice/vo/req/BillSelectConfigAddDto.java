package com.kerryprops.kip.bill.webservice.vo.req;

import com.kerryprops.kip.bill.common.enums.BillSelectMode;
import com.kerryprops.kip.bill.dao.entity.BillSelectConfig;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * BillSelectConfigAddDto.
 *
 * <AUTHOR> 2025-02-27 17:08:47
 **/
@Data
public class BillSelectConfigAddDto {

    @NotBlank
    @Schema(title = "项目唯一标识")
    private String projectId;

    @Schema(title = "楼栋id")
    private String buildingId;

    @Schema(title = "单元id")
    private String roomId;

    @Schema(title = "楼栋名")
    private String buildingName;

    @Schema(title = "单元名")
    private String roomName;

    @NotNull
    @Schema(title = "账单选择模式")
    private BillSelectMode billSelectMode;

    public BillSelectConfig toBillSelectConfig() {
        BillSelectConfig config = new BillSelectConfig();
        config.setProjectId(this.projectId);
        config.setBuildingId(this.buildingId);
        config.setRoomId(this.roomId);
        config.setBillSelectMode(this.billSelectMode);
        config.setRoomName(this.roomName);
        config.setBuildingName(this.buildingName);
        return config;
    }

}
