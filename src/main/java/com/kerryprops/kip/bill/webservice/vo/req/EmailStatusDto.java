package com.kerryprops.kip.bill.webservice.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
public class EmailStatusDto implements Serializable {

    @Schema(title = "接收邮箱")
    private String email;

    @Schema(title = "邮件发送是否成功")
    private Boolean state;

    @Schema(title = "邮件发送结果描述")
    private String message;

    private String deliverTime;

}
