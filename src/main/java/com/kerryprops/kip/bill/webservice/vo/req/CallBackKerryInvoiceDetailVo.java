package com.kerryprops.kip.bill.webservice.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Auther: zjy
 * @Date: 2021/7/7 17:54
 * @Description:
 */
@Data
public class CallBackKerryInvoiceDetailVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 明细编号
     */
    @Schema(title = "明细编号")
    private String salesbillItemNo;

    /**
     * 货物或应税劳务名称 ，禁止特殊字符
     */
    @Schema(title = "货物或应税劳务名称 ，禁止特殊字符")
    private String itemName;

    /**
     * 规格型号， 禁止特殊字符，如果对方填写则传过来
     */
    @Schema(title = "规格型号， 禁止特殊字符，如果对方填写则传过来")
    private String itemSpec;

    /**
     * 单位， 禁止特殊字符，如果对方填写则传过来
     */
    @Schema(title = "单位， 禁止特殊字符，如果对方填写则传过来")
    private String quantityUnit;

    /**
     * 数量
     */
    @Schema(title = "数量")
    private BigDecimal quantity;

    /**
     * 商品货物税收分类编码（19位税编）
     */
    @Schema(title = "商品货物税收分类编码（19位税编）")
    private String goodsTaxNo;

    /**
     * 含税折扣行（传正数）
     */
    @Schema(title = "value = 含税折扣行（传正数）")
    private BigDecimal outterDiscountWithTax;

    /**
     * 税率 小数（0.03、0.06....）
     */
    @Schema(title = "税率 小数（0.03、0.06....）")
    private BigDecimal taxRate;

    /**
     * 含税金额（如果priceMethod 为1 则传该字段必传） ：数量*单价（amountwithtax-outerdiscountwithtax > 0）
     */
    @Schema(title = "含税金额（如果priceMethod 为1 则传该字段必传） ：数量*单价（amountwithtax-outerdiscountwithtax > 0）")
    private BigDecimal amountWithTax;

    /**
     * 不含金额（如果priceMethod 为0 则传该字段必传）
     */
    @Schema(title = "不含金额（如果priceMethod 为0 则传该字段必传）")
    private BigDecimal amountWithoutTax;

    /**
     * 税额
     */
    @Schema(title = "税额")
    private BigDecimal taxAmount;

    /**
     * 含税单价（如果priceMethod 为1 则传该字段必传）
     */
    @Schema(title = "含税单价（如果priceMethod 为1 则传该字段必传）")
    private BigDecimal unitPriceWithTax;

    /**
     * 单价（如果priceMethod 为0 则传该字段必传）
     */
    @Schema(title = "单价（如果priceMethod 为0 则传该字段必传）")
    private BigDecimal unitPrice;

    /**
     * 是否享受税收优惠政策 0-不1-享受
     */
    @Schema(title = "是否享受税收优惠政策 0-不1-享受")
    private String taxPre;

    /**
     * 税收优惠
     */
    @Schema(title = "税收优惠")
    private String taxPreCon;

    /**
     * 零税率标志；标识进行注释例如非0税率；0-出口退税 1-免税 2-不征税 3-普通0税率
     */
    @Schema(title = "零税率标志；标识进行注释例如非0税率；0-出口退税 1-免税 2-不征税 3-普通0税率")
    private String zeroTax;

    @Schema(title = "数电，行业特殊票种类型：不动产销售-05，不动产经营租赁服务-06")
    private String itemTypeCode;

    /**
     * 数电，特殊票种：不动产销售类/不动产租赁类
     */
    @Schema(title = "不动产详细地址")
    private String realEstateAddress;

    @Schema(title = "房屋产权证书/不动产权证号码")
    private String realEstateNo;

    @Schema(title = "跨地市标志，字符 false-否, true-是")
    private String crossCitySign;

    @Schema(title = "面积单位,平方米-01, 亩-02, ㎡-03, 平方千米-04, 公顷-05, h㎡-06, k㎡-07")
    private String areaUnit;

    @Schema(title = "不动产地址（省市区）")
    private String realEstatePlace;

    /**
     * 数电，特殊票种：不动产销售类
     */
    @Schema(title = "土地增值税项目编号")
    private String landVatItemNo;

    @Schema(title = "不动产单元代码/网签合同备案编码")
    private String realEstateCode;

    @Schema(title = "核定计税价格")
    private String taxablePrice;

    @Schema(title = "实际成交含税金额")
    private String transactionPrice;

    /**
     * 数电，特殊票种：不动产租赁类
     */
    @Schema(title = "租赁期起，yyyyMMdd")
    private String leaseTermStart;

    @Schema(title = "租赁期止，yyyyMMdd")
    private String leaseTermEnd;

    /**
     * 备注
     */
    @Schema(title = "备注")
    private String remark;

    /**
     * 扩展字段
     */
    @Schema(title = "扩展字段")
    private String ext1;

    /**
     * 扩展字段
     */
    @Schema(title = "扩展字段")
    private String ext2;


}