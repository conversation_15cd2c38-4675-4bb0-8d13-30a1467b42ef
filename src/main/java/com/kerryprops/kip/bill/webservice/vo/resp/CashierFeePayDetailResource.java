package com.kerryprops.kip.bill.webservice.vo.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kerryprops.kip.bill.common.enums.CashierPayStatus;
import com.kerryprops.kip.bill.common.enums.PaymentPayType;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

@Data
public class CashierFeePayDetailResource {

    private long id;

    @Schema(title = "楼盘ID")
    private String projectId;

    @Schema(title = "楼栋ID")
    private String buildingId;

    @Schema(title = "楼层ID")
    private String floorId;

    @Schema(title = "单元ID")
    private String roomId;

    @Schema(title = "名称信息")
    private PositionItemResponse positionItem;

    @Schema(title = "收款号")
    private String payAct;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(title = "支付时间")
    private Date payDate;

    @Schema(title = "描述")
    private String comment;

    @Schema(title = "支付方式")
    private String payType;

    @Schema(title = "订单金额(实收金额)")
    private BigDecimal totalAmt;

    @Schema(title = "预收金额")
    private BigDecimal advanceAmt;

    @Schema(title = "预收科目")
    private String paymentCate;

    @Schema(title = "状态")
    private CashierPayStatus status;

    @Schema(title = "关联paymentInfo ID")
    private String paymentInfoId;

    @Schema(title = "操作人(账号登录来源)[S|C|B]")
    private String accountFromType;

    @Schema(title = "收款记录生成时间")
    private Date createTime;

    @Schema(title = "收款记录更新时间")
    private Date updateTime;

    @Schema(title = "杂费配置id")
    private Long feeId;

    @Schema(title = "杂费名")
    private String feeName;

    @Schema(title = "杂费税率")
    private BigDecimal feeTax;

    @Schema(title = "杂费税额")
    private BigDecimal feeTaxAmount;

    @Schema(title = "手续费率")
    private BigDecimal tax;

    @Schema(title = "手续费")
    private BigDecimal taxAmount;

    @Schema(title = "流水号")
    private String pspTransNo;

    @Schema(title = "是否写入JDE,0未写入 1已写入")
    private Integer sendJdeStatus;

    @Schema(title = "是否已复核")
    private Integer verifyStatus;

    @Schema(title = "是否已取消")
    private Integer deleteAt;

    public static CashierFeePayDetailResource of(AptPaymentInfo paymentInfo) {
        String payType = paymentInfo.getPayTypeInfo();
        if (StringUtils.isBlank(payType)) {
            payType = Optional.ofNullable(paymentInfo.getPayType()).map(v -> v.getInfo()).orElse(null);
        }

        CashierFeePayDetailResource resource = new CashierFeePayDetailResource();
        resource.setProjectId(paymentInfo.getProjectId());
        resource.setBuildingId(paymentInfo.getBuildingId());
        resource.setFloorId(paymentInfo.getFloorId());
        resource.setRoomId(paymentInfo.getRoomId());
        resource.setPositionItem(paymentInfo.getPositionItem());
        resource.setAdvanceAmt(paymentInfo.getAdvanceAmount());
        resource.setPaymentCate(paymentInfo.getPaymentCate().name());
        resource.setPaymentInfoId(paymentInfo.getId());
        resource.setCreateTime(paymentInfo.getCreateTime());
        resource.setUpdateTime(paymentInfo.getUpdateTime());
        resource.setAccountFromType(paymentInfo.getCreateBy());
        resource.setFeeId(paymentInfo.getFeeId());
        resource.setFeeName(paymentInfo.getFeeName());
        resource.setFeeTax(paymentInfo.getFeeTax());
        resource.setFeeTaxAmount(paymentInfo.getFeeTaxAmount());
        resource.setPspTransNo(paymentInfo.getPspTransNo());
        resource.setPayDate(paymentInfo.getPaymentTime());
        resource.setComment(paymentInfo.getDescription());
        resource.setDeleteAt(paymentInfo.getDeleted());

        resource.setTotalAmt(BigDecimal.valueOf(paymentInfo.getAmt()));
        resource.setStatus(CashierPayStatus.of(paymentInfo));
        resource.setPayType(payType);
        return resource;
    }

    public static CashierFeePayDetailResource of(AptPay vo) {
        CashierFeePayDetailResource resource = new CashierFeePayDetailResource();
        resource.setId(vo.getId());
        resource.setProjectId(vo.getProjectId());
        resource.setBuildingId(vo.getBuildingId());
        resource.setFloorId(vo.getFloorId());
        resource.setRoomId(vo.getRoomId());
        resource.setPositionItem(vo.getPositionItem());
        resource.setPayAct(vo.getPayAct());
        resource.setPayDate(vo.getPayDate());
        resource.setComment(vo.getComments());
        resource.setPayType(vo.getPayType());
        resource.setAdvanceAmt(vo.getAdvanceAmount());
        resource.setPaymentCate(vo.getPaymentCate());
        resource.setPaymentInfoId(vo.getPaymentInfoId());
        resource.setCreateTime(vo.getCreateTime());
        resource.setUpdateTime(vo.getUpdateTime());
        resource.setAccountFromType(vo.getCreateBy());
        resource.setFeeId(vo.getFeeId());
        resource.setFeeName(vo.getFeeName());
        resource.setFeeTax(vo.getFeeTax());
        resource.setFeeTaxAmount(vo.getFeeTaxAmount());
        resource.setPspTransNo(vo.getPayTranx());
        resource.setSendJdeStatus(vo.getSendJdeStatus());
        resource.setDeleteAt(vo.getDeletedAt());

        resource.setTax(BigDecimal.valueOf(vo.getTax()));
        resource.setTaxAmount(BigDecimal.valueOf(vo.getTaxAmt()));
        resource.setVerifyStatus(vo.getVerifyStatus().getCode());
        resource.setTotalAmt(BigDecimal.valueOf(vo.getTotalAmt()));
        resource.setStatus(CashierPayStatus.of(vo));
        return resource;
    }

    public String getPayType() {
        if (PaymentPayType.UNKNOWN.getInfo().equals(payType)) {
            return null;
        }
        if (PaymentPayType.UNKNOWN.name().equals(payType)) {
            return null;
        }
        return payType;
    }

}
