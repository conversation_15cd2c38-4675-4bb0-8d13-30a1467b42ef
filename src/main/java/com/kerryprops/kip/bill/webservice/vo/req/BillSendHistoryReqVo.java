package com.kerryprops.kip.bill.webservice.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 账单查询的 view
 */
@Data
@Schema
public class BillSendHistoryReqVo extends BaseReqVo {

    private static final long serialVersionUID = 1L;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(title = "账单发送时间起始")
    private Date emailDateStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(title = "账单发送时间末尾")
    private Date emailDateEnd;

    @Schema(title = "公司名称")
    private String tpAlph;

    @Schema(title = "楼盘Code")
    private String projectId;

    @Schema(title = "建筑物")
    private String tpMcu;

    @Schema(title = "jde合同号")
    private String tpDoco;

    @Schema(title = "账单类型")
    private String tpDct;

    // 账单年
    @Schema(title = "账单年")
    private Integer tpFyr;

    // 账单月
    @Schema(title = "账单月")
    private Integer tpPn;

}
