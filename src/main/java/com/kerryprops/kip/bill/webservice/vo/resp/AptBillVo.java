package com.kerryprops.kip.bill.webservice.vo.resp;

import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillPushStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@Data
@Schema
public class AptBillVo {

    @Schema(title = "KEY")
    private Long id;

    /*	len: 32*/
    @Schema(title = "账单号")
    private String billNo;

    private String category;

    /*	len: 32*/
    private String bu;

    /*	len: 32*/
    private String unit;

    /*	len: 32*/
    private String an8;

    private String alph;

    private String doco;

    /*	len: 19*/
    private Date beginDate;

    /*	len: 19*/
    private Date endDate;

    private Integer year;

    private Integer month;

    private Integer billMonth;

    private double amt;

    /*	len: 10*/
    private BillStatus status;

    private BillPaymentStatus paymentStatus;

    private String paymentResult;

    private BillPushStatus pushStatus;

    /*	len: 10*/
    private String projectId;

    /*	len: 10*/
    private String buildingId;

    /*	len: 10*/
    private String floorId;

    /*	len: 10*/
    private String roomId;

    private String rdGlc;

    private String rdGlcName;

    private String paySession;

    private String serviceName;

    private BigDecimal rate;

    private String classifyCode;

    private Integer blockChain;

    private String isBilling = "0";

}
