package com.kerryprops.kip.bill.webservice.vo.resp;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 账单查询的 view
 */
@Data
@Schema
public class StaffBillHistoryRespVo {

    @ExcelIgnore
    @Schema(title = "账单在KIP平台ID")
    private Long id;

    @ExcelIgnore
    @Schema(title = "公司编号")
    private String tpCo;

    @ExcelIgnore
    @Schema(title = "用户编号")
    private String tpAn8;

    @ExcelProperty("付款人")
    @Schema(title = "公司名称")
    private String tpAlph;

    @ExcelProperty("账单类型")
    @Schema(title = "账单类型")
    private String tpDct;

    @ExcelProperty("账单名称")
    @Schema(title = "账单类型名称")
    private String tpDl01;

    @ExcelProperty("账单打印时间")
    @Schema(title = "账单打印时间")
    private String formatDate;

    @ExcelProperty("账单年份")
    @Schema(title = "账单年")
    private Integer tpFyr;

    @ExcelProperty("账单月份")
    @Schema(title = "账单月")
    private Integer tpPn;

    @ExcelProperty("发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(title = "账单发送时间")
    private String emailDate;

    @ExcelIgnore
    @Schema(title = "账单文件的链接地址")
    private String fileUrl;

    @ExcelIgnore
    @Schema(title = "账单文件名")
    private String tpGtfilenm;

    @ExcelProperty("发送人")
    @Schema(title = "账单发送人姓名")
    private String updateBy;

    @ExcelProperty("建筑物")
    @Schema(title = "建筑物")
    private String tpMcu;

    @ExcelProperty("合同号")
    @Schema(title = "jde合同号")
    private String tpDoco;

}
