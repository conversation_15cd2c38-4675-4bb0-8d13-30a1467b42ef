package com.kerryprops.kip.bill.webservice.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kerryprops.kip.bill.common.aop.RedisLock;
import com.kerryprops.kip.bill.service.CounterCashierFeeService;
import com.kerryprops.kip.bill.webservice.vo.req.CashierFeePayRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierFeePaysRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierFeePaysVerifyRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierFeePrepayRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierFeeQRCodePayRequest;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierFeePayDetailResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierFeePayResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierFeePaysResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierQRCodePaymentResource;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.kerryprops.kip.bill.common.utils.BillingFun.exceptionToNull;

/**
 * <AUTHOR> 2023-12-15 09:52:34
 **/
@Tag(name = "S端-物业前台-杂费管理")
@Slf4j
@RestController
@RequiredArgsConstructor
@Validated
public class CounterCashierFeeController {

    private final ObjectMapper objectMapper;

    private final CounterCashierFeeService counterCashierFeeService;

    @Operation(summary = "杂费单-离线支付")
    @PostMapping("/cashier/apt/bill/fee/offline/pay")
    public CashierFeePayResource feeOfflinePay(@RequestBody @Valid CashierFeePayRequest request) {
        return counterCashierFeeService.feeOfflinePay(request);
    }

    @Operation(summary = "杂费单-预付款", description = "对应新建杂费单中的发送付款码操作")
    @PostMapping("/cashier/apt/bill/fee/prepay")
    public CashierFeePayResource prepay(@RequestBody @Valid CashierFeePrepayRequest request) {
        return counterCashierFeeService.prepay(request);
    }

    @Operation(summary = "杂费单列表")
    @GetMapping(value = "/cashier/apt/bill/fee/pays", produces = {"application/json"})
    public Page<CashierFeePaysResource> queryCashierAptPays(@ModelAttribute CashierFeePaysRequest req
            , @SortDefault.SortDefaults(value = {
            @SortDefault(sort = "createTime", direction = Sort.Direction.DESC),
            @SortDefault(sort = "updateTime", direction = Sort.Direction.DESC)}) Pageable pageable) {

        return counterCashierFeeService.queryFeePays(req, pageable);
    }

    @Operation(summary = "杂费单列表-导出")
    @GetMapping(value = "/cashier/apt/bill/fee/pays/export")
    public void exportCashierAptPays(@ModelAttribute CashierFeePaysRequest req, HttpServletResponse response) {
        counterCashierFeeService.exportFeePays(req, response);
    }

    @Operation(summary = "杂费单详情")
    @GetMapping(value = "/cashier/apt/bill/fee/pay/detail", produces = {"application/json"})
    public CashierFeePayDetailResource queryFeePayDetail(@NotBlank String paymentInfoId) {
        return counterCashierFeeService.queryFeePayDetail(paymentInfoId);
    }

    @Operation(summary = "杂费账单-审核/取消审核")
    @PutMapping(value = "/cashier/apt/bill/fee/pays/ids/{id}/switch_verify_status", produces = {"application/json"})
    public CashierFeePayDetailResource switchFeePayVerifyStatus(@PathVariable("id") Long id) {
        return counterCashierFeeService.switchFeePayVerifyStatus(id);
    }

    @Operation(summary = "杂费账单-按条件审核")
    @PutMapping(value = "/cashier/apt/bill/fee/pays/conditional_verify", produces = {"application/json"})
    public List<CashierFeePayDetailResource> conditionalVerifyFeePay(@Valid @RequestBody CashierFeePaysVerifyRequest req) {
        return counterCashierFeeService.conditionalVerifyFeePay(req);
    }

    @Operation(summary = "杂费账单-取消")
    @PutMapping(value = "/cashier/apt/bill/fee/pays/ids/{id}/cancel", produces = {"application/json"})
    public CashierFeePayDetailResource cancelFeePay(@PathVariable("id") Long id) {
        return counterCashierFeeService.cancelFeePay(id);
    }

    @Operation(summary = "杂费账单-回写JDE")
    @PostMapping(value = "/cashier/apt/bill/fee/pays/project_ids/{project_id}/write_back_jde", produces = {"application/json"})
    @RedisLock(key = "kip:billing:s:writeBackFee2JDE", expire = 180L)
    public Integer writeBackFee2JDE(@PathVariable("project_id") String projectId) {
        return counterCashierFeeService.writeBackFee2JDE(projectId);
    }

    @Operation(summary = "杂费单-扫码缴费")
    @PostMapping("/cashier/apt/bill/fee/qrcode/pay")
    public CashierQRCodePaymentResource counterCashierFeeQRCodePayment(@Valid @RequestBody CashierFeeQRCodePayRequest request) {
        log.info("received_cashier_fee_qrcode_pay_req: {}"
                , exceptionToNull(() -> objectMapper.writeValueAsString(request)));
        return counterCashierFeeService.counterCashierQRCodePayment(request);
    }

}
