package com.kerryprops.kip.bill.webservice.impl;

import com.google.common.collect.Lists;
import com.kerryprops.kip.bill.common.aop.BillErrorEnum;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.exceptions.AppException;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.utils.CommonUtil;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptJdeBillRepository;
import com.kerryprops.kip.bill.dao.AptPayBillRepository;
import com.kerryprops.kip.bill.dao.AptPayRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptBillOperator;
import com.kerryprops.kip.bill.dao.entity.AptJdeBill;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPayBill;
import com.kerryprops.kip.bill.dao.entity.QAptJdeBill;
import com.kerryprops.kip.bill.dao.entity.QAptPay;
import com.kerryprops.kip.bill.dao.entity.QAptPayBill;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.impl.AptBillAsyncPushServiceImpl;
import com.kerryprops.kip.bill.service.model.s.AptBillManageSearchReqBo;
import com.kerryprops.kip.bill.service.model.s.AptBillPushReqBo;
import com.kerryprops.kip.bill.webservice.StaffAptBillFacade;
import com.kerryprops.kip.bill.webservice.vo.req.AptBillExportReqVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptBillManageSearchReqVo;
import com.kerryprops.kip.bill.webservice.vo.resp.AptBillExportRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffAptBillManageRespVo;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IteratorUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.web.SortDefault;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.common.constants.AppConstants.APT_BILL_PUSH_REDIS_KEY_PREFIX;
import static com.kerryprops.kip.bill.common.utils.BillingFun.exportByEasyExcel;
import static com.kerryprops.kip.bill.log4j.BSConversationFilter.getConversationId;

/**
 * 1. super admin: can do any thing;
 * 2. other: can do any thing under bu
 */
@Slf4j
@RestController
public class StaffAptBillController implements StaffAptBillFacade {

    private static final String BILL_PUSH_TIPS = "账单开始推送，推送结果稍后查看日志";

    @Autowired
    private AptBillRepository billRepository;

    @Autowired
    private AptPayBillRepository payBillRepository;

    @Autowired
    private AptPayRepository payRepository;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private AptJdeBillRepository aptJdeBillRepository;

    @Autowired
    private AptBillAsyncPushServiceImpl aptBillAsyncPushService;

    @Override
    public RespWrapVo<Map<String, String>> queryPaymentStatus() {
        Map<String, String> res = new HashMap<>();
        res.put(BillPaymentStatus.PAID.name(), BillPaymentStatus.PAYMENT_STATUS_PAID);
        res.put(BillPaymentStatus.TO_BE_PAID.name(), BillPaymentStatus.PAYMENT_STATUS_TO_BE_PAID);
        res.put(BillPaymentStatus.PAYING.name(), BillPaymentStatus.PAYMENT_STATUS_PAYING);
        return new RespWrapVo<>(res);
    }

    @Override
    public RespWrapVo<List<String>> queryAlphs(@PathVariable("projectId") String projectId, String roomId) {
        List<String> maxBindingScope = UserInfoUtils.maxBindingScope();
        boolean isMaxScopeNotNull = null != maxBindingScope;

        List<String> res = billRepository.queryAlphs(isMaxScopeNotNull, maxBindingScope, projectId, roomId);
        return new RespWrapVo<>(res);
    }

    @Override
    public RespWrapVo<List<String>> queryCategorys(@PathVariable("projectId") String projectId, String roomId) {
        List<String> maxBindingScope = UserInfoUtils.maxBindingScope();
        boolean isMaxScopeNotNull = null != maxBindingScope;

        List<String> res = billRepository.queryCategorys(isMaxScopeNotNull, maxBindingScope, projectId, roomId);
        return new RespWrapVo<>(res);
    }

    @Override
    public RespWrapVo<Page<StaffAptBillManageRespVo>> list(@SortDefault.SortDefaults(value = {
            @SortDefault(sort = "year", direction = Sort.Direction.ASC)
            , @SortDefault(sort = "month", direction = Sort.Direction.ASC)
            , @SortDefault(sort = "beginDate", direction = Sort.Direction.ASC)
            , @SortDefault(sort = "endDate", direction = Sort.Direction.ASC)}) Pageable pageable
            , @ModelAttribute AptBillManageSearchReqVo vo) {

        AptBillManageSearchReqBo searchReqBo = BeanUtil.copy(vo, AptBillManageSearchReqBo.class);
        Page<AptBill> aptBills = billRepository.findAll(searchReqBo.toPredicates(), pageable);
        if (aptBills.isEmpty()) {
            return new RespWrapVo<>();
        }
        Page<StaffAptBillManageRespVo> staffAptBillManageRespVo
                = aptBills.map(e -> BeanUtil.copy(e, StaffAptBillManageRespVo.class));
        return new RespWrapVo<>(staffAptBillManageRespVo);
    }

    @Override
    public RespWrapVo<Boolean> pushAll(@PathVariable("projectId") String projectId) {
        AptBillPushReqBo pushReqBo = AptBillPushReqBo.builder().build();
        Calendar c = Calendar.getInstance();
        c.add(Calendar.MONTH, 1);

        pushReqBo.setPaymentStatus(List.of(BillPaymentStatus.PAID, BillPaymentStatus.DIRECT_DEBIT_PAID));
        pushReqBo.setMaxYear(c.get(Calendar.YEAR));
        pushReqBo.setMaxMonth(c.get(Calendar.MONTH) + 1);
        pushReqBo.setProjectId(projectId);

        Iterable<AptBill> aptBillIterable = billRepository.findAll(pushReqBo.toPredicates());
        return execPushAsync(aptBillIterable, AptBillOperator.PUSH_ALL);
    }

    @Override
    @Transactional
    public RespWrapVo<Boolean> pushConditional(@RequestBody AptBillManageSearchReqVo vo) {
        AptBillManageSearchReqBo searchReqBo = BeanUtil.copy(vo, AptBillManageSearchReqBo.class);
        Iterable<AptBill> aptBillIterable = billRepository.findAll(searchReqBo.toPredicates());

        Iterator<AptBill> iterator = aptBillIterable.iterator();
        while (iterator.hasNext()) {
            AptBill aptBill = iterator.next();
            if (null != aptBill && List.of(BillPaymentStatus.PAID
                    , BillPaymentStatus.DIRECT_DEBIT_PAID).contains(aptBill.getPaymentStatus())) {
                iterator.remove();
            }
        }

        return execPushAsync(aptBillIterable, AptBillOperator.PUSH_CONDITIONAL);
    }

    @Override
    @Transactional
    public RespWrapVo<Boolean> pushSelected(@RequestBody List<Long> billIds) {
        if (null == billIds || billIds.isEmpty()) {
            final String ERRPR_TIP = "selected billIds is null or empty";
            log.info(ERRPR_TIP);
            return new RespWrapVo<>(RespCodeEnum.BAD_REQUEST.getCode(), ERRPR_TIP);
        }
        AptBillPushReqBo pushReqBo = AptBillPushReqBo.builder()
                .billIds(billIds).paymentStatus(List.of(BillPaymentStatus.PAID, BillPaymentStatus.DIRECT_DEBIT_PAID))
                .build();
        Iterable<AptBill> aptBillIterable = billRepository.findAll(pushReqBo.toPredicates());

        return execPushAsync(aptBillIterable, AptBillOperator.PUSH_SELECTED);
    }

    @Override
    public void export(@RequestBody AptBillExportReqVo exportReqVo, HttpServletResponse response) {
        Iterable<AptBill> billIterable = billRepository.findAll(exportReqVo.toPredicates());
        List<AptBill> bills = IteratorUtils.toList(billIterable.iterator());
        if (CollectionUtils.isEmpty(bills)) {
            return;
        }
        List<AptBillExportRespVo> billRespVos = bills.stream().map(e -> {
            AptBillExportRespVo billExportRespVo = BeanUtil.copy(e, AptBillExportRespVo.class);
            billExportRespVo.setProjectName(e.getPositionItem().getProjectName());
            billExportRespVo.setBuildingName(e.getPositionItem().getBuildingName());
            billExportRespVo.setPaymentResult(e.getPaymentResult());
            return billExportRespVo;
        }).collect(Collectors.toList());
        billRespVos.sort((o1, o2) -> {
            int a = o1.getYear() * 100 + o1.getMonth();
            int b = o2.getYear() * 100 + o2.getMonth();
            return b - a;
        });

        //handle offline payment
        List<AptBill> offlineAptBills = bills.stream()
                .filter(bill -> BillStatus.JDE_VERIFIED.equals(bill.getStatus())
                        && BillPaymentStatus.PAID.equals(bill.getPaymentStatus()))
                .collect(Collectors.toList());
        List<String> offlineBillNos = offlineAptBills.stream().map(AptBill::getBillNo).collect(Collectors.toList());
        Iterable<AptJdeBill> bills1 = aptJdeBillRepository.findAll(QAptJdeBill.aptJdeBill.billNumber.in(offlineBillNos));
        List<AptJdeBill> offlineAptJdeBillList = Lists.newArrayList(bills1);
        Map<String, List<AptJdeBill>> aptJdeBillMap = offlineAptJdeBillList.stream()
                .collect(Collectors.groupingBy(AptJdeBill::getBillNumber));
        billRespVos.forEach(vo -> {
            String billNo = vo.getBillNo();
            if (!offlineBillNos.contains(billNo)) {
                return;
            }

            Optional<AptJdeBill> maxJdeVerificationTime = aptJdeBillMap.get(billNo).stream()
                    .filter(bill -> bill.getJdeVerificationTime() != null)
                    .max(Comparator.comparing(AptJdeBill::getJdeVerificationTime));
            if (maxJdeVerificationTime.isPresent()) {
                vo.setPayDesc("线下缴费");
                vo.setCreateBy("线下核销");

                AptJdeBill aptJdeBill = maxJdeVerificationTime.get();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                vo.setPayDate(sdf.format(aptJdeBill.getJdeVerificationTime()));
            }
        });

        Map<Long, AptBillExportRespVo> exportMap = billRespVos.stream().collect(Collectors.toMap(AptBillExportRespVo::getId, Function.identity()));
        Iterable<AptPayBill> payBills = payBillRepository.findAll(QAptPayBill.aptPayBill.billId.in(billRespVos.stream().map(e -> e.getId()).collect(Collectors.toList())));
        List<AptPayBill> payBillList = IteratorUtils.toList(payBills.iterator());
        Map<Long, AptPayBill> payBillMap = payBillList.stream().collect(Collectors.toMap(AptPayBill::getBillId, Function.identity(), (k, v) -> v));
        List<Long> payIds = payBillList.stream().map(e -> e.getPayId()).collect(Collectors.toList());
        Iterator<AptPay> payBillIterator = payRepository.findAll(QAptPay.aptPay.id.in(payIds)).iterator();
        List<AptPay> payList = IteratorUtils.toList(payBillIterator);
        Map<Long, AptPay> payMap = payList.stream().collect(Collectors.toMap(AptPay::getId, Function.identity()));
        payBillList.stream().map(e -> e.getBillId()).forEach(billId -> {
            AptBillExportRespVo vo = exportMap.get(billId);
            Long payId = payBillMap.get(billId).getPayId();
            AptPay pay = payMap.get(payId);
            CommonUtil.copyPropertiesIgnoreNull(pay, vo);
            if (pay.getPayDate() != null) {
                vo.setPayDate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(pay.getPayDate()));
            }
        });

        final String CONTENT_NAME = "apartment_bill_list";
        exportByEasyExcel(response, billRespVos, AptBillExportRespVo.class, CONTENT_NAME, CONTENT_NAME);
    }

    private RespWrapVo<Boolean> execPushAsync(Iterable<AptBill> aptBillIterable, AptBillOperator aptBillOperator) {
        List<AptBill> aptBillList = Lists.newArrayList(aptBillIterable);
        if (CollectionUtils.isEmpty(aptBillList)) {
            log.info("no room to push");
            // data为false代表过滤后无可推送账单
            return new RespWrapVo<>(RespCodeEnum.SUCCESS, BILL_PUSH_TIPS, false);
        }
        String projectId = aptBillList.stream().map(AptBill::getProjectId)
                .filter(StringUtils::isNotBlank).findAny().orElse(null);
        if (Objects.nonNull(projectId)) {
            Object obj = null;
            if (AptBillOperator.PUSH_ALL.equals(aptBillOperator)) {
                obj = redisTemplate.opsForValue().get(APT_BILL_PUSH_REDIS_KEY_PREFIX
                        + ":" + projectId + ":" + AptBillOperator.PUSH_ALL);
            } else if (AptBillOperator.PUSH_CONDITIONAL.equals(aptBillOperator)) {
                obj = redisTemplate.opsForValue().get(APT_BILL_PUSH_REDIS_KEY_PREFIX
                        + ":" + projectId + ":" + AptBillOperator.PUSH_CONDITIONAL);
            }
            if (Objects.nonNull(obj)) {
                throw AppException.error(BillErrorEnum.DRAW_CONCURRENT_OPERATION);
            }
        }
        aptBillAsyncPushService.asyncPushAptBill(aptBillList, aptBillOperator
                , getConversationId(), UserInfoUtils.getUser());
        return new RespWrapVo<>(RespCodeEnum.SUCCESS.getCode(), BILL_PUSH_TIPS, true);
    }

}