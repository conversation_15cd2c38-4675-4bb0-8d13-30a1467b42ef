package com.kerryprops.kip.bill.webservice.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * BillConfigUpdateDto.
 *
 * <AUTHOR>
 */
@Data
public class BillConfigUpdateDto {

    @Schema(title = "账单类型", maxLength = 2)
    private String sourceType;

    @Schema(title = "账单来源名称", maxLength = 100)
    private String sourceName;

    @Schema(title = "账单获取服务地址", maxLength = 255)
    private String httpService;

    @Schema(title = "是否删除", description = "0:不删除, 1:删除")
    private String delFlag;

}