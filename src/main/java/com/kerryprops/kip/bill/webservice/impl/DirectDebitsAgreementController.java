package com.kerryprops.kip.bill.webservice.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kerryprops.kip.bill.common.exceptions.RestInvalidParamException;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.entity.AptBillDirectDebitsAgreement;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.service.AptBillAgreementService;
import com.kerryprops.kip.bill.service.model.s.AptBillAgreementBo;
import com.kerryprops.kip.bill.webservice.vo.req.DirectDebitsAgreementsQueryRequest;
import com.kerryprops.kip.bill.webservice.vo.resp.DirectDebitsAgreementResource;
import com.kerryprops.kip.hiveas.webservice.resource.resp.RoomResp;
import com.kerryprops.kip.pmw.client.resource.AsyncAgreementStatusChangedResource;
import com.kerryprops.kip.pmw.client.resource.AsyncAgreementStatusChangedResource.AsyncAgreementStatusChangedBodyResource;
import com.kerryprops.kip.pmw.client.resource.HeaderResource;
import com.kerryprops.kip.pmw.client.resource.QueryAgreementInputResource;
import com.kerryprops.kip.pmw.client.resource.QueryAgreementOutputResource;
import com.kerryprops.kip.pmw.client.resource.QueryAgreementOutputResource.PmwAgreementResource;
import com.kerryprops.kip.pmw.client.resource.TerminateAgreementInputResource;
import com.kerryprops.kip.pmw.client.resource.TerminateAgreementOutputResource;
import com.kerryprops.kip.pmw.client.resource.TerminateAgreementOutputResource.TerminateAgreementOutputBodyResource;
import com.kerryprops.kip.pmw.client.service.PaymentClientService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.AGREEMENT_NOT_EXISTS;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.AGREEMENT_NO_REQUIRED;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.PROJECT_ID_REQUIRED;
import static com.kerryprops.kip.bill.common.utils.BillingFun.exceptionToNull;

@Tag(name = "公寓小区-物业费代扣-协议管理")
@Slf4j
@RestController
@AllArgsConstructor
public class DirectDebitsAgreementController {

    private final ObjectMapper objectMapper;

    private final HiveAsClient hiveAsClient;

    private final PaymentClientService paymentClient;

    private final AptBillAgreementService aptBillAgreementService;

    @Operation(summary = "代扣协议列表查询")
    @GetMapping(value = "/s/apt/bill/direct_debits/agreements", produces = MediaType.APPLICATION_JSON_VALUE)
    public Page<DirectDebitsAgreementResource> directDebitAgreements(@SortDefault.SortDefaults(value = {
            @SortDefault(sort = "updatedTime", direction = Sort.Direction.DESC)}) Pageable pageable
            , DirectDebitsAgreementsQueryRequest request) {
        if (StringUtils.isEmpty(request.getProjectId())) {
            throw new RestInvalidParamException(PROJECT_ID_REQUIRED);
        }
        AptBillAgreementBo bo = BeanUtil.copy(request, AptBillAgreementBo.class);
        return aptBillAgreementService.queryAgreements(bo, pageable);
    }

    @Operation(summary = "商户端解约代扣协议")
    @PostMapping(value = "/s/apt/bill/direct_debits/agreement/{agreementNo}/terminate"
            , produces = MediaType.APPLICATION_JSON_VALUE)
    public boolean terminateAptBillAgreement(@PathVariable("agreementNo") String agreementNo) {
        List<AptBillDirectDebitsAgreement> dbAgmts = aptBillAgreementService.findAllByAgreementNos(List.of(agreementNo));
        if (CollectionUtils.isEmpty(dbAgmts)) {
            log.info("agreement_not_exists_to_terminate: {}", agreementNo);
            return true;
        }
        TerminateAgreementInputResource.TerminateAgreementInputBodyResource bodyResource =
                new TerminateAgreementInputResource.TerminateAgreementInputBodyResource();
        bodyResource.setAgreementNo(agreementNo);
        bodyResource.setOrderSource("KIP_BILLING");
        bodyResource.setProductType("BILLING");
        bodyResource.setRoomId(dbAgmts.get(0).getRoomId());

        HeaderResource.Builder builder = new HeaderResource.Builder();
        builder.setSub("kip");

        TerminateAgreementInputResource inputResource = new TerminateAgreementInputResource(builder.build()
                , bodyResource, null);
        TerminateAgreementOutputResource outputResource = paymentClient.terminateAgreement(inputResource);
        TerminateAgreementOutputBodyResource outputBodyResource = outputResource.getBody();
        aptBillAgreementService.agreementTerminated(outputBodyResource);
        return Boolean.TRUE;
    }

    /**
     * agreement callback API
     * very sensitive
     * don't alter it unless BUG or Incident
     */
    @Hidden
    @PostMapping(value = "s/apt/bill/direct_debits/agreement/{agreementNo}/callback"
            , produces = MediaType.APPLICATION_JSON_VALUE)
    public String agreementCallback(@PathVariable("agreementNo") String agreementNo
            , @RequestBody AsyncAgreementStatusChangedResource request) {
        AsyncAgreementStatusChangedBodyResource bodyResource = request.getBody();
        log.info("received_direct_debits_callback: {}"
                , exceptionToNull(() -> objectMapper.writeValueAsString(bodyResource)));
        aptBillAgreementService.saveOrUpdateAgreement(bodyResource);
        return "accepted";
    }

    @Hidden
    @PostMapping(value = "s/apt/bill/direct_debits/agreement/{agreementNo}/sync"
            , produces = MediaType.APPLICATION_JSON_VALUE)
    public boolean syncAgreement(@PathVariable("agreementNo") String agreementNo) {
        QueryAgreementInputResource.QueryAgreementInputBodyResource body
                = new QueryAgreementInputResource.QueryAgreementInputBodyResource();
        body.setOrderSource("KIP_BILLING");
        body.setProductType("BILLING");
//        body.setPspName(PspName.ALIPAY.name());
        body.setAgreementNos(List.of(agreementNo));

        HeaderResource.Builder builder = new HeaderResource.Builder();
        builder.setSub("kip");

        QueryAgreementInputResource inputResource
                = new QueryAgreementInputResource(builder.build(), body, null);

        QueryAgreementOutputResource outputResource = paymentClient.queryAgreement(inputResource);
        List<PmwAgreementResource> pmwAgreementResources
                = outputResource.getBody().getAgreementList();
        if (CollectionUtils.isEmpty(pmwAgreementResources)) {
            throw new RestInvalidParamException(AGREEMENT_NO_REQUIRED);
        }
        var op = pmwAgreementResources.stream().filter(r -> Objects.equals(agreementNo
                , r.getAgreementNo())).findFirst();
        if (op.isEmpty()) {
            throw new RestInvalidParamException(AGREEMENT_NOT_EXISTS);
        }
        PmwAgreementResource pmwAgreementResource = op.get();
        aptBillAgreementService.syncAgreement(pmwAgreementResource);
        return Boolean.TRUE;
    }

// ---------------------------------------------------------------------------------------------------------
    // 1、查询代扣配置接口, 应该在billing-service实现，临时放pmw

    /**
     * {
     * "bill_key": "12345678",
     * "company_id": "31000002401701",
     * "serial_no": *********,
     * "openid": "oxBHq0MS9oW-wTiHWO2rL-sH27So",
     * "merchant_id": "1614886487"
     * }
     */
    @Hidden
    @PostMapping(value = "/s/apt/direct_debits/psp/wechatpay/agreements/pre_verify"
            , produces = MediaType.APPLICATION_JSON_VALUE)
    public Object verifyDirectDebit(@RequestBody WxLifeDirectDebitPreVerifyCommand command) {
        log.info("received_wx_debit_gmt_verify request with body: {}"
                , exceptionToNull(() -> objectMapper.writeValueAsString(command)));
        // response
        DirectDebitConfigVerifyResponse verifyResponse = new DirectDebitConfigVerifyResponse();
        verifyResponse.setSerialNo(command.getSerialNo());
        verifyResponse.setBillKey(command.getBillKey());
        verifyResponse.setCompanyId(command.getCompanyId());
        verifyResponse.setOpenId(command.getOpenId());
        String roomId = command.getBillKey();
        // check room ID validity
        RespWrapVo<RoomResp> roomRespRespWrapVo = hiveAsClient.getRoomById(roomId);
        if (!RespWrapVo.isResponseValidWithData(roomRespRespWrapVo)) {
            log.error("verify_wx_direct_debit: not valid roomId: {}", roomId);
            verifyResponse.setReturnCode("NO_EXIST");
            verifyResponse.setReturnMsg("户号不存在");
            return verifyResponse;
        }
        // check duplicate agreement
        List<AptBillDirectDebitsAgreement> agmts = aptBillAgreementService.findAllActiveByRoomIdIn(List.of(roomId));
        if (CollectionUtils.isNotEmpty(agmts)) {
            if (agmts.stream().anyMatch(agmt -> Objects.equals(agmt.getPspLogonId(), command.getOpenId()))) {
                verifyResponse.setReturnCode("SIGNED_CHANNEL");
                verifyResponse.setReturnMsg("该户号已通过其他渠道签约（不支持签约）");
            } else {
                verifyResponse.setReturnCode("SIGNED_OTHER");
                verifyResponse.setReturnMsg("该户号已被其他用户签约（不支持签约）");
            }
            return verifyResponse;
        }
        RoomResp roomResp = roomRespRespWrapVo.getData();
        verifyResponse.setCustomerName(roomResp.getBuilding().getName() + "-" + roomResp.getRoom().getRoomNo());
        verifyResponse.setCustomerAddress(roomResp.getProject().getName()
                + roomResp.getBuilding().getName() + "-" + roomResp.getRoom().getRoomNo());
        verifyResponse.setReturnCode("SUCCESS");
        verifyResponse.setReturnMsg("成功");
        String respContent = exceptionToNull(() -> objectMapper.writeValueAsString(verifyResponse));
        log.info("verify_resp: {}", respContent);
        return verifyResponse;
    }

    @Data
    public static class DirectDebitConfigVerifyResponse {

        private String merchantId;

        private Long serialNo;

        private String billKey;

        private String companyId;

        private String openId;

        private String customerName;

        private String customerAddress;

        private String accountType; // postpaid

        private String returnCode;

        private String returnMsg;

    }

/*
    @Hidden
    @PostMapping(value = "/s/apt/direct_debits/psp/wechatpay/agreements/confirm"
            , produces = MediaType.APPLICATION_JSON_VALUE)
    public DirectDebitAgreementConfirmRequest agreements(@RequestBody WxLifeDirectDebitConfirmAgreementCommand request) {
        log.info("received_wx_debit_gmt_req with body: {}"
                , exceptionToNull(() -> objectMapper.writeValueAsString(request)));
        DirectDebitAgreementConfirmRequest confirmRequest = new DirectDebitAgreementConfirmRequest();
        confirmRequest.setSerialNo(request.getSerialNo());
        confirmRequest.setRoomId(request.getBillKey());
        confirmRequest.setCompanyId(request.getCompanyId());
        confirmRequest.setOpenId(request.getOpenId());
        confirmRequest.setChangeType(request.getChangeType());
        confirmRequest.setReturnCode("SUCCESS");
        confirmRequest.setReturnMsg("成功");

//        confirmRequest.setProjectId();
//        confirmRequest.setUserProfileId();
//        confirmRequest.setUserPhoneNumber();
        return confirmRequest;
    }

*/


    @Data
    public static class WxLifeDirectDebitPreVerifyCommand {

        private Long serialNo;

        private String billKey;

        private String companyId;

        private String openId;

        private String merchantId;

        // 签约时，选填，AES加密，需机构解密后获取
        private String phoneNo;

        // 身份证号码
        // 签约时，选填，AES加密，需机构解密后获取
        private String govId;

        // 姓名
        // 签约时，选填，AES加密，需机构解密后获取
        private String govName;

    }

    @Data
    public static class WxLifeDirectDebitConfirmAgreementCommand {

        private Long serialNo;

        private String billKey;

        private String companyId;

        private String openId;

        private String merchantId;

        private String changeType;

        private String accountType;

        private Integer contractTerminationMod;

        private Integer chargeDayOfMonth;

        private Integer chargeLimitAmount;

        private Integer chargeAmount;

        private String phoneNo;

        private String govId;

        private String govName;

        private String extra1;

        private String extra2;

    }


}
