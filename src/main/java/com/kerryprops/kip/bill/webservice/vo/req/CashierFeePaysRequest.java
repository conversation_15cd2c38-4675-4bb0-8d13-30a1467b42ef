package com.kerryprops.kip.bill.webservice.vo.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.ImmutableList;
import com.kerryprops.kip.bill.common.enums.BillPayModule;
import com.kerryprops.kip.bill.common.enums.InvoicedStatus;
import com.kerryprops.kip.bill.common.jpa.QueryFilter;
import com.kerryprops.kip.bill.dao.entity.QAptPay;
import com.kerryprops.kip.bill.dao.entity.QAptPaymentInfo;
import com.querydsl.core.types.Predicate;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Data
public class CashierFeePaysRequest implements QueryFilter {

    @Schema(title = "楼盘ID", required = true)
    @NotBlank
    private String projectId;

    @Schema(title = "楼栋ID")
    private String buildingId;

    @Schema(title = "单元ID")
    private String roomId;

    // 支付方式，比如："其他付费"
    @Schema(title = "支付方式")
    private List<String> payType;

    @Schema(title = "支付开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payDateStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "支付结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payDateEnd;

    @Schema(title = "杂费配置id")
    private List<@NotNull Long> feeIds;

    @Schema(title = "收款号")
    private String payAct;

    @Schema(title = "是否写入JDE")
    private Integer sendJdeStatus;

    @Schema(title = "是否为财务人员操作[0:否|1:是]", description = "财务模块不显示待支付记录，其他模块显示")
    private Integer isFinanceStaff;

    @Schema(title = "是否已取消")
    private Integer deleteAt;

    @Schema(title = "是否已开票状态")
    private InvoicedStatus invoicedStatus;

    @Override
    public List<Optional<Predicate>> predicates() {
        ImmutableList.Builder<Optional<Predicate>> builder = ImmutableList.builder();
        if (Objects.equals(1, isFinanceStaff)) {
            // 财务人员操作
            builder.add(Optional.of(projectId).map(QAptPay.aptPay.projectId::eq))
                    .add(Optional.ofNullable(buildingId).map(QAptPay.aptPay.buildingId::eq))
                    .add(Optional.ofNullable(roomId).map(QAptPay.aptPay.roomId::eq))
                    .add(Optional.ofNullable(payDateStart).map(QAptPay.aptPay.payDate::goe))
                    .add(Optional.ofNullable(payDateEnd).map(QAptPay.aptPay.payDate::loe))
                    .add(Optional.ofNullable(payType).map(QAptPay.aptPay.payType::in))
                    .add(Optional.ofNullable(payAct).map(e -> "%" + e + "%").map(QAptPay.aptPay.payAct::like))
                    .add(Optional.ofNullable(sendJdeStatus).map(QAptPay.aptPay.sendJdeStatus::eq))
                    .add(Optional.ofNullable(invoicedStatus).map(QAptPay.aptPay.invoicedStatus::eq))
            ;
            if (CollectionUtils.isNotEmpty(feeIds)) {
                builder.add(Optional.of(feeIds).map(QAptPay.aptPay.feeId::in));
            }
            builder.add(Optional.of(BillPayModule.CASHIER_FEE).map(QAptPay.aptPay.billPayModule::eq));
            builder.add(Optional.ofNullable(deleteAt).map(QAptPay.aptPay.deletedAt::eq));
        } else {
            //  收银台||物业前台
            if (Objects.isNull(deleteAt)) {
                deleteAt = 0;
            }

            builder.add(Optional.of(projectId).map(QAptPaymentInfo.aptPaymentInfo.projectId::eq))
                    .add(Optional.ofNullable(buildingId).map(QAptPaymentInfo.aptPaymentInfo.buildingId::eq))
                    .add(Optional.ofNullable(roomId).map(QAptPaymentInfo.aptPaymentInfo.roomId::eq))
                    .add(Optional.ofNullable(payDateStart).map(QAptPaymentInfo.aptPaymentInfo.paymentTime::goe))
                    .add(Optional.ofNullable(payDateEnd).map(QAptPaymentInfo.aptPaymentInfo.paymentTime::loe))
                    .add(Optional.ofNullable(payAct).map(e -> "%" + e + "%").map(QAptPaymentInfo.aptPaymentInfo.payAct::like))
                    .add(Optional.ofNullable(invoicedStatus).map(QAptPaymentInfo.aptPaymentInfo.invoicedStatus::eq))
            ;
            if (CollectionUtils.isNotEmpty(payType)) {
                builder.add(Optional.of(payType).map(QAptPaymentInfo.aptPaymentInfo.payTypeInfo::in));
            }
            if (CollectionUtils.isNotEmpty(feeIds)) {
                builder.add(Optional.of(feeIds).map(QAptPaymentInfo.aptPaymentInfo.feeId::in));
            }
            builder.add(Optional.of(BillPayModule.CASHIER_FEE).map(QAptPaymentInfo.aptPaymentInfo.billPayModule::eq));
            builder.add(Optional.of(deleteAt).map(QAptPaymentInfo.aptPaymentInfo.deleted::eq));
        }
        return builder.build();
    }

}
