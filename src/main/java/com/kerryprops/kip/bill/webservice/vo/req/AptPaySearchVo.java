package com.kerryprops.kip.bill.webservice.vo.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kerryprops.kip.bill.common.enums.InvoicedStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 名   称：apt_pay_config
 * 描   述：
 * 作   者：<PERSON>
 * 时   间：2021/08/30 15:03:37
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Data
@Schema
public class AptPaySearchVo {

    @Schema(title = "楼盘ID")
    private String projectId;

    @Schema(title = "楼栋ID")
    private String buildingId;

    @Schema(title = "单元ID")
    private String roomId;

    @Schema(title = "支付开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payDateStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "支付结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payDateEnd;

    @Schema(title = "支付配置ID")
    private Long payConfigId;

    @Schema(title = "支付方式")
    private String payType;

    @Schema(title = "是否写入JDE,0未写入 1已写入")
    private Integer sendJdeStatus;

    @Schema(title = "是否已复核")
    private Integer verifyStatus;


    @Schema(title = "是否移除 0 正常 1 已移除")
    private Integer deletedAt;

    @Schema(title = "收款号")
    private String payAct;

    @Schema(title = "是否已开票状态")
    private InvoicedStatus invoicedStatus;

}
