package com.kerryprops.kip.bill.webservice.vo.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kerryprops.kip.bill.common.enums.PaymentCateEnum;
import com.kerryprops.kip.bill.webservice.vo.resp.RoomAn8RespVo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class CashierQRCodePaymentRequest {

    @NotBlank(message = "projectId is required")
    @Schema(title = "楼盘ID", required = true)
    private String projectId;

    @NotBlank(message = "authCode is required")
    @Schema(title = "用户auth_code", required = true)
    private String authCode;

    @NotBlank(message = "payOption is required")
    @Schema(title = "pay option: WECHATPAY, ALIPAY", required = true)
    private String payOption;

    // @NotEmpty(message = "账单不能为空")
    @Schema(title = "发起支付的账单列表")
    private List<AptBillPaymentRequest> bills;

    @Schema(title = "支付描述", required = false)
    private String paymentDesc;

    // 无预收时可为空
    @Schema(title = "实收金额，有预收时必填")
    private BigDecimal actualAmount;

    // 无预收时可为空
    @Schema(title = "预收金额，有预收时必填")
    private BigDecimal advanceAmount;

    // 无预收时可为空
    @Schema(title = "预收科目[A003 : 管理费预收 | A004 : 其他预收]，未勾选账单时填写，有预收时必填")
    private PaymentCateEnum paymentCate;

    @Schema(title = "户号", required = true)
    private String roomId;

    @Schema(title = "付款人信息")
    private RoomAn8RespVo payerInfo;

    @Schema(title = "付款日期[yyyy-MM-dd]。如果不传，则为实际支付时间。")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date paymentTime;

}
