package com.kerryprops.kip.bill.webservice.vo.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import static com.kerryprops.kip.bill.common.utils.PayStatusUtils.transPaymentStatus;

/**
 * 账单查询的 view
 */
@Data
@Schema
public class AptBillExportRespVo {

    @ExcelProperty("楼栋")
    @Schema(title = "楼栋")
    private String buildingName;

    @ExcelProperty("户号")
    @Schema(title = "JDE单元号")
    private String unit;

    @ExcelProperty("付款人")
    @Schema(title = "付款人")
    private String alph;

    @ExcelProperty("账单号")
    @Schema(title = "账单号")
    private String billNo;

    @ExcelProperty("账单行ID")
    @Schema(title = "账单在KIP平台ID")
    private Long id;

    @ExcelProperty("费项")
    @Schema(title = "费项")
    private String category;

    @ExcelProperty("账单开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(title = "账单周期开始")
    private String beginDate;

    @ExcelProperty("账单结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(title = "账单周期结束")
    private String endDate;

    @ExcelProperty("账单年")
    @Schema(title = "账单年")
    private Integer year;

    @ExcelProperty("账单月")
    @Schema(title = "账单月")
    private Integer month;

    @ExcelProperty("金额")
    @Schema(title = "金额")
    private Double amt;

    @ExcelProperty("支付状态")
    @Schema(title = "用户支付状态")
    private String paymentStatus;

    @ExcelProperty("支付详情")
    @Schema(title = "支付详情")
    private String paymentResult;

    @ExcelProperty("楼盘")
    @Schema(title = "楼盘")
    private String projectName;

    @ExcelProperty("微信交易号")
    @Schema(title = "微信交易号")
    private String payTranx;

    @ExcelProperty("备注")
    @Schema(title = "备注")
    private String payDesc;

    @ExcelProperty("支付日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(title = "支付日期")
    private String payDate;

    @ExcelProperty("操作人")
    @Schema(title = "操作人")
    private String createBy;

    @ExcelProperty("创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(title = "创建日期")
    private String createTime;

    public void setPaymentStatus(String paymentStatus) {
        if (paymentStatus == null) {
            return;
        }
        this.paymentStatus = transPaymentStatus(paymentStatus);
    }

    public String getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(String beginDate) {
        if (StringUtils.isNotEmpty(beginDate)) {
            beginDate = StringUtils.substring(beginDate, 0, 10);
        }
        this.beginDate = beginDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        if (StringUtils.isNotEmpty(endDate)) {
            endDate = StringUtils.substring(endDate, 0, 10);
        }
        this.endDate = endDate;
    }

}
