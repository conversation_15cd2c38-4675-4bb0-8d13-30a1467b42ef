package com.kerryprops.kip.bill.webservice.vo.resp;

import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema
public class RoomAn8RespVo {

    @Schema(title = "JDE BU")
    private String bu;

    @Schema(title = "JDE单元ID")
    private String unit;

    @Schema(title = "JDE地址号")
    private String an8;

    @Schema(title = "JDE姓名")
    private String alph;

    @Schema(title = "JDE合同号")
    private String doco;

    public void fillRoomAn8(AptPaymentInfo paymentInfo) {
        String an8 = paymentInfo.getAn8();
        if (StringUtils.isNotBlank(an8)) {
            return;
        }
        paymentInfo.setBu(getBu());
        paymentInfo.setUnit(getUnit());
        paymentInfo.setAn8(getAn8());
        paymentInfo.setAlph(getAlph());
        paymentInfo.setDoco(getDoco());
    }

    public void fillRoomAn8(AptPay pay) {
        String an8 = pay.getAn8();
        if (StringUtils.isNotBlank(an8)) {
            return;
        }
        pay.setBu(getBu());
        pay.setUnit(getUnit());
        pay.setAn8(getAn8());
        pay.setAlph(getAlph());
        pay.setDoco(getDoco());
    }

}
