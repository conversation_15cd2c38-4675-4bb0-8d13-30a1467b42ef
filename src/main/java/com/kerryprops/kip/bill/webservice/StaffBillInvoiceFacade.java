package com.kerryprops.kip.bill.webservice;

import com.kerryprops.kip.bill.webservice.vo.req.BillInvoiceSearchRequest;
import com.kerryprops.kip.bill.webservice.vo.req.BillInvoiceSendRequest;
import com.kerryprops.kip.bill.webservice.vo.req.BillInvoiceUploadImportVo;
import com.kerryprops.kip.bill.webservice.vo.req.BillInvoiceUploadVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillInvoiceResource;
import com.kerryprops.kip.bill.webservice.vo.resp.BillInvoiceUploadResultExportVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillInvoiceUploadResultRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillPayer;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Tag(name = "办公商场-S端发票管理")
public interface StaffBillInvoiceFacade {

    @GetMapping(value = "/s/bill/invoice/list", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "查询电子发票列表及阅读情况")
    Page<BillInvoiceResource> billInvoiceList(@SortDefault.SortDefaults(value = {
            @SortDefault(sort = "paperDrewDate", direction = Sort.Direction.DESC)
            , @SortDefault(sort = "createdTime", direction = Sort.Direction.DESC)
    }) Pageable pageable, BillInvoiceSearchRequest request);

    @GetMapping("/s/bill/invoice/project_id/{projectId}/purchaser_names")
    @Operation(summary = "模糊查询购方公司列表")
    List<BillPayer> fuzzyQueryPurchaserNames(@PathVariable("projectId") String projectId, @RequestParam("query") String query);

    @PostMapping(value = "/s/bill/invoice/sms", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "发送发票短信通知")
    Integer sendBillInvoiceSms(@RequestBody BillInvoiceSendRequest request);

    @PostMapping(value = "/s/bill/invoice/email", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "发送发票邮件通知")
    Integer sendBillInvoiceEmail(@RequestBody BillInvoiceSendRequest request);

    @PostMapping(value = "/s/bill/invoice/message", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "发送发票站内信通知")
    Integer sendBillInvoiceMessage(@RequestBody BillInvoiceSendRequest request);

    @PostMapping(value = "/s/bill/invoice/manual_import")
    @Operation(summary = "解析excel文档，获取JDE业务单")
    List<BillInvoiceUploadImportVo> getImportBillInvoiceList(@RequestParam("file") MultipartFile file
            , HttpServletResponse response);

    @PostMapping(value = "/s/bill/invoice/manual_upload")
    @Operation(summary = "推送JDE业务单到票易通")
    @ApiResponse(description = "推送结果描述，如：申请开票异常")
    BillInvoiceUploadResultRespVo uploadBillInvoice(@RequestBody BillInvoiceUploadVo billInvoiceUploadVo);

    @PostMapping(value = "/s/bill/invoice/upload_results/export")
    @Operation(summary = "导出JDE业务单上传票易通的执行结果")
    void exportBillInvoiceResultList(@RequestBody List<BillInvoiceUploadResultExportVo> billInvoiceUploadResultVos, HttpServletResponse response);

}
