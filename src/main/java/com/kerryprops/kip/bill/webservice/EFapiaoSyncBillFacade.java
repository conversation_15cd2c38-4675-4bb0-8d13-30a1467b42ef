package com.kerryprops.kip.bill.webservice;

import com.kerryprops.kip.bill.config.EFapiaoConfig;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * e-fapiao同步任务接口类
 *
 * <AUTHOR>
 * @Date 2023-3-18
 */
@Tag(name = "e发票-账单同步")
@RequestMapping(value = "", produces = MediaType.APPLICATION_JSON_VALUE)
public interface EFapiaoSyncBillFacade {

    @PostMapping(value = "/s/apt/bill/e_fapiao/sync")
    @Operation(summary = "e发票-同步JDE账单")
    Boolean sync(@RequestBody EFapiaoConfig.Item item, @RequestParam("isSchedule") boolean isSchedule);

}
