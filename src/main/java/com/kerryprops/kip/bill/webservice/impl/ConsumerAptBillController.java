package com.kerryprops.kip.bill.webservice.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.enums.BillPayModule;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.PayCancelTypeEnum;
import com.kerryprops.kip.bill.common.enums.PaymentPayType;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.feign.clients.CUserClient;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.entity.TenantStaffResource;
import com.kerryprops.kip.bill.feign.entity.UserWithRoomsResponse;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.AptBillAgreementService;
import com.kerryprops.kip.bill.service.PaymentBillService;
import com.kerryprops.kip.bill.service.model.c.ConsumerAptBillSearchReqBo;
import com.kerryprops.kip.bill.webservice.ConsumerAptBillFacade;
import com.kerryprops.kip.bill.webservice.vo.resp.AptPaymentInfoVo;
import com.kerryprops.kip.bill.webservice.vo.resp.PayableStaffAptBillRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.RoomAndUserSignResource;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffAptBillRespVo;
import com.kerryprops.kip.hiveas.feign.dto.resp.BuildingRespDto;
import com.kerryprops.kip.hiveas.webservice.vo.resp.BuildingResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IteratorUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.sql.Timestamp;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@RestController
public class ConsumerAptBillController implements ConsumerAptBillFacade {

    @Autowired
    private CUserClient cUserClient;

    @Autowired
    private HiveAsClient hiveAsClient;

    @Autowired
    private AptBillRepository billRepository;

    @Autowired
    private PaymentBillService paymentBillService;

    @Autowired
    private AptBillAgreementService billAgreementService;

    @Value("${kip.payment.feePayTtl:48h}")
    private Duration payTimeout;

    @Override
    public RespWrapVo<List<StaffAptBillRespVo>> getLbsPayableBill(@SortDefault.SortDefaults(value = {
                                                                          @SortDefault(sort = "beginDate", direction = Sort.Direction.DESC),
                                                                          @SortDefault(sort = "endDate", direction = Sort.Direction.DESC)}) Pageable pageable,
                                                                  @RequestParam(value = "lbsId", required = true) String lbsId) {
        RespWrapVo<List<StaffAptBillRespVo>> listRespWrapVo = null;
        try {
            List<BillPaymentStatus> statusList = Lists.newArrayList(BillPaymentStatus.TO_BE_PAID, BillPaymentStatus.PAYING, BillPaymentStatus.PART_PAID);
            listRespWrapVo = search(null, null, lbsId, statusList);
        } catch (Exception e) {
            log.error("fail to query bill based on lbs[id={}, error={}]", lbsId, e.getMessage());
            List<StaffAptBillRespVo> staffAptBillRespVoList = new LinkedList<>();
            listRespWrapVo = new RespWrapVo<>(staffAptBillRespVoList);
            listRespWrapVo.setMessage(e.getMessage());
        }
        return listRespWrapVo;
    }

    @Override
    public RespWrapVo<List<PayableStaffAptBillRespVo>> userBillInfoList(@SortDefault.SortDefaults(value = {
                                                                                @SortDefault(sort = "beginDate", direction = Sort.Direction.ASC),
                                                                                @SortDefault(sort = "endDate", direction = Sort.Direction.ASC)}) Pageable pageable
            , @PathVariable("buildingId") String buildingId, @PathVariable("roomId") String roomId) {
        // 增加 是否签约代扣字段
        List<StaffAptBillRespVo> respVos = search(buildingId, roomId, null
                , Lists.newArrayList(BillPaymentStatus.TO_BE_PAID, BillPaymentStatus.PART_PAID)).getData();
        List<PayableStaffAptBillRespVo> payableRespVos = billAgreementService.addAgreementInfo(respVos, roomId);
        return new RespWrapVo<>(payableRespVos);
    }

    @Override
    public RoomAndUserSignResource userAndSignInfo(@PathVariable("roomId") String roomId) {
        Optional<RoomAndUserSignResource> op = billAgreementService.getUserAndRoomSignInfo(roomId);
        return op.orElse(new RoomAndUserSignResource());
    }

    @Override
    public RespWrapVo<List<StaffAptBillRespVo>> historyBill(@SortDefault.SortDefaults(value = {
                                                                    @SortDefault(sort = "beginDate", direction = Sort.Direction.ASC),
                                                                    @SortDefault(sort = "endDate", direction = Sort.Direction.ASC)}) Pageable pageable
            , @PathVariable("buildingId") String buildingId, @PathVariable("roomId") String roomId) {
        var search = search(buildingId, roomId, null, List.of(BillPaymentStatus.PAID
                , BillPaymentStatus.DIRECT_DEBIT_PAID));
        return new RespWrapVo<>(Optional.ofNullable(search.getData()).orElse(new ArrayList<>()).stream().map(s -> {
            if ("JDE核销".equals(s.getPaymentResult())) {
                s.setPaymentResult("线下冲销");
            }
            return s;
        }).collect(Collectors.toList()));
    }

    /**
     * @Method: queryPaymentPage
     * @Description: 查询个人账单
     * @Date: 2022/2/6 14:18
     * @Author: Kanon
     * @param: pageable
     * @return: com.kerryprops.kip.bill.common.vo.RespData
     */
    @Override
    public RespWrapVo<List<AptPaymentInfoVo>> queryPaymentPage(@PathVariable("buildingId") String buildingId
            , @PathVariable("roomId") String roomId) {
        var userId = UserInfoUtils.getUserId();

        List<AptPaymentInfo> aptPaymentInfos = paymentBillService.queryMyPaymentInfoList(userId, buildingId, roomId);
        List<AptPaymentInfoVo> aptPaymentInfoVos = aptPaymentInfos.stream()
                .map(a -> BeanUtil.copy(a, AptPaymentInfoVo.class)).map(a -> {
                    if (PaymentPayType.UNKNOWN.equals(a.getPayType())) {
                        a.setPayType(null);
                    }
                    return a;
                }).collect(Collectors.toList());
        return new RespWrapVo<>(aptPaymentInfoVos);
    }

    @Override
    public RespWrapVo<AptPaymentInfoVo> queryPaymentInfo(String orderNo) {
        AptPaymentInfoVo aptPaymentInfoVo = paymentBillService.queryPaymentInfoById(orderNo);
        log.info("aptPaymentInfoVo: {}", JSON.toJSONString(aptPaymentInfoVo));
        var collect = aptPaymentInfoVo.getAptBillList().stream().filter(b -> "1".equals(b.getIsBilling()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect) && !Objects.equals("1", aptPaymentInfoVo.getIsAdvanceBilling())) {
            aptPaymentInfoVo.setAppliedInvoice(0);
        }

        //判断超时
        boolean isFeePayModule = BillPayModule.CASHIER_FEE.equals(aptPaymentInfoVo.getBillPayModule());
        long timeout = isFeePayModule ? payTimeout.toSeconds() : Duration.ofMinutes(15).toSeconds();
        long elapse = (System.currentTimeMillis() - aptPaymentInfoVo.getCreateTime().getTime()) / 1000;
        if (elapse > timeout && aptPaymentInfoVo.getPaymentStatus().name().equals(BillPaymentStatus.TO_BE_PAID.name())) {
            aptPaymentInfoVo.setPaymentStatus(BillPaymentStatus.CANCEL);
            aptPaymentInfoVo.setCancelType(PayCancelTypeEnum.TIMEOUT_CANCELLED.name());
            aptPaymentInfoVo.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        }
        return new RespWrapVo<>(aptPaymentInfoVo);
    }

    public List<String> parseUserWithRoomResponse(RespWrapVo<UserWithRoomsResponse> userWithRoomsResponseRespWrapVo) {
        List<TenantStaffResource> tenantStaffResources = Optional.ofNullable(userWithRoomsResponseRespWrapVo)
                .map(RespWrapVo::getData)
                .map(UserWithRoomsResponse::getRooms)
                .orElse(Collections.emptyList());
        return tenantStaffResources
                .stream()
                .filter(e -> e.getAuthorizer() != null && (e.getAuthorizer() == 1 || e.getAuthorizer() == 2))
                .map(e -> e.getRoomNumber())
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
    }

    @SuppressWarnings("unchecked")
    private RespWrapVo<List<StaffAptBillRespVo>> search(String buildingId,
                                                        String roomId,
                                                        String lbsId,
                                                        List<BillPaymentStatus> paymentStatusList) {

        List<String> roomIds = populateRoomIds(roomId);
        List<String> buildingIds = null;
        if (StringUtils.isNotEmpty(buildingId)) {
            buildingIds = Lists.newArrayList(buildingId);
        }
        List<String> lbsBuildingIds = null;
        if (StringUtils.isNotEmpty(lbsId)) {
            List<BuildingResponseVo> buildingRespList = hiveAsClient.getLbs(lbsId);
            if (CollectionUtils.isNotEmpty(buildingRespList)) {
                lbsBuildingIds = buildingRespList.stream()
                        .map(BuildingResponseVo::getBuilding)
                        .filter(e -> Objects.nonNull(e) && StringUtils.isNotEmpty(e.getId()))
                        .map(BuildingRespDto::getId)
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(lbsBuildingIds)) {
                    lbsBuildingIds = null;
                }

            }
        }
        ConsumerAptBillSearchReqBo searchReqBo = ConsumerAptBillSearchReqBo.builder()
                .paymentStatusList(paymentStatusList)
                .lbsBuildingIds(lbsBuildingIds)
                .buildingIds(buildingIds)
                .roomIds(roomIds)
                .build();
        Iterable<AptBill> aptBillIterable = billRepository.findAll(searchReqBo.toPredicates());
        List<AptBill> myList = IteratorUtils.toList(aptBillIterable.iterator());
        if (CollectionUtils.isEmpty(myList)) {
            return new RespWrapVo<>();
        }
        List<StaffAptBillRespVo> staffAptBillRespVos = myList.stream()
                .map(e -> BeanUtil.copy(e, StaffAptBillRespVo.class))
                .sorted((o1, o2) -> {
                    int a = o1.getYear() * 100 + o1.getMonth();
                    int b = o2.getYear() * 100 + o2.getMonth();
                    return a - b;
                }).collect(Collectors.toList());
        return new RespWrapVo<>(staffAptBillRespVos);
    }

    private List<String> populateRoomIds(String roomId) {
        if (StringUtils.isNotBlank(roomId)) {
            return List.of(roomId);
        }
        List<String> roomIds = null;
        try {
            LoginUser loginUser = UserInfoUtils.getUser();
            log.info("loginUser : [{}]", JSON.toJSONString(loginUser));
            if ("CASHIER_DESK".equalsIgnoreCase(loginUser.getFromType())) {
                return List.of(roomId);
            }
            RespWrapVo<UserWithRoomsResponse> userWithRoomsResponseRespWrapVo = cUserClient.userAssociateRooms(loginUser.getUniqueUserId());
            log.info("userWithRoomsResponseRespWrapVo : [{}]", JSON.toJSONString(userWithRoomsResponseRespWrapVo));

            roomIds = parseUserWithRoomResponse(userWithRoomsResponseRespWrapVo);
        } catch (Exception e) {
            log.error("query room user failed.", e);
        }
        if (CollectionUtils.isEmpty(roomIds)) {
            if (StringUtils.isBlank(roomId)) {
                throw new RuntimeException("room user not found");
            } else {
                // 兼容counter cashier 扫码进入页面场景
                return List.of(roomId);
            }
        }
        return roomIds;
    }

}
