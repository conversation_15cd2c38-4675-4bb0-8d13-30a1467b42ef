package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.AptPayConfigRepository;
import com.kerryprops.kip.bill.dao.entity.QAptPayConfig;
import com.kerryprops.kip.bill.webservice.ConsumerAptPayConfigFacade;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@AllArgsConstructor
public class ConsumerAptPayConfigController implements ConsumerAptPayConfigFacade {

    private final AptPayConfigRepository aptPayConfigRepository;

    @Override
    public RespWrapVo<List<String>> queryPaymentType(String projectId, String companyCode) {
        final Integer ENABLED_STATUS = 1;
        BooleanExpression expression = QAptPayConfig.aptPayConfig.deleteAt.eq(0L)
                .and(QAptPayConfig.aptPayConfig.projectId.eq(projectId))
                .and(QAptPayConfig.aptPayConfig.companyCode.eq(companyCode))
                .and(QAptPayConfig.aptPayConfig.enabledStatus.eq(ENABLED_STATUS));

        List<String> paymentTypeList = aptPayConfigRepository.getJpaQueryFactory()
                .selectDistinct(QAptPayConfig.aptPayConfig.paymentType)
                .from(QAptPayConfig.aptPayConfig)
                .where(expression)
                .fetch();
        log.info("queryPaymentTypeList: {}", paymentTypeList == null ? "empty" : paymentTypeList);
        return new RespWrapVo<>(paymentTypeList);
    }

}
