package com.kerryprops.kip.bill.webservice;

import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

@Tag(name = "公寓小区-C端付费配置管理")
public interface ConsumerAptPayConfigFacade {

    @GetMapping("/c/apt/pay_config/payment_type/project/{projectId}/company/{companyCode}")
    @Operation(summary = "查询支付方式")
    RespWrapVo<List<String>> queryPaymentType(@PathVariable(value = "projectId") String projectId
            , @PathVariable(value = "companyCode") String companyCode);

}
