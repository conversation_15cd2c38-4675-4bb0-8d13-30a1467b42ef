package com.kerryprops.kip.bill.webservice.vo.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.kerryprops.kip.bill.common.enums.BillSelectMode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.AssertTrue;
import lombok.Data;

/**
 * BillSelectConfigPutDto.
 *
 * <AUTHOR> Yu 2025-02-27 17:08:47
 **/
@Data
public class BillSelectConfigPutDto {

    @Schema(title = "楼栋id")
    private String buildingId;

    @Schema(title = "单元id")
    private String roomId;

    @Schema(title = "账单选择模式")
    private BillSelectMode billSelectMode;

    @Schema(title = "楼栋名")
    private String buildingName;

    @Schema(title = "单元名")
    private String roomName;

    @JsonIgnore
    @SuppressWarnings("unused")
    @AssertTrue(message = "必须输入内容")
    boolean isValidSelection() {
        return this.buildingId != null || this.roomId != null || billSelectMode != null;
    }

}
