package com.kerryprops.kip.bill.webservice.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * BillSelectModeDto.
 *
 * <AUTHOR> 2025-02-28 11:29:24
 **/
@Setter
@Getter
@ToString
public class BillSelectModeDto {

    @NotBlank
    @Schema(title = "楼盘id")
    private String projectId;

    @NotBlank
    @Schema(title = "楼栋id")
    private String buildingId;

    @NotBlank
    @Schema(title = "单元id")
    private String roomId;

}
