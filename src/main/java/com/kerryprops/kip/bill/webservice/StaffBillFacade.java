package com.kerryprops.kip.bill.webservice;

import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.webservice.vo.req.BillSearchReqVo;
import com.kerryprops.kip.bill.webservice.vo.req.BillSendHistoryReqVo;
import com.kerryprops.kip.bill.webservice.vo.req.BillSendReqVo;
import com.kerryprops.kip.bill.webservice.vo.req.EmailResultVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillDetailRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillPayer;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffBillHistoryRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffBillRespVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Set;

/***********************************************************************************************************************
 * Project - accelerator
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - David Wei
 * Created Date - 06/03/2021 14:05
 **********************************************************************************************************************/

@Tag(name = "办公商场-S端账单管理")
@RequestMapping(value = "/s/bill", produces = MediaType.APPLICATION_JSON_VALUE)
public interface StaffBillFacade {

    @GetMapping("/list")
    @Operation(summary = "查询电子账单列表及阅读情况")
    RespWrapVo<Page<StaffBillRespVo>> list(@SortDefault.SortDefaults(value = {
            @SortDefault(sort = "tpDoco", direction = Sort.Direction.DESC)
            , @SortDefault(sort = "tpFyr", direction = Sort.Direction.DESC)
            , @SortDefault(sort = "tpPn", direction = Sort.Direction.DESC)
            , @SortDefault(sort = "formatDate", direction = Sort.Direction.DESC)}) Pageable pageable
            , @ModelAttribute BillSearchReqVo vo);

    @GetMapping("/{projectId}/docos")
    @Operation(summary = "模糊查询当前楼盘下的合同列表")
    RespWrapVo<List<String>> fuzzyQueryDocos(@PathVariable("projectId") String projectId
            , @RequestParam(value = "query", required = false) String query);

    @GetMapping("/dcts/{projectId}")
    @Operation(summary = "根据楼盘查询账单类型列表")
    RespWrapVo<List<String>> queryDcts(@PathVariable("projectId") String projectId);

    @GetMapping("/project_id/{projectId}/del_flag/{delFlag}/payers")
    @Operation(summary = "根据楼盘查询付款人列表（模糊搜索）")
    @Parameters({
            @Parameter(name = "projectId", description = "楼盘Id", required = true),
            @Parameter(name = "delFlag", description = "移除标记，1：已移除，0：未移除", required = true),
            @Parameter(name = "query", description = "查询输入值", required = false)
    })
    List<BillPayer> fuzzyQueryPayersByProjectId(@PathVariable("projectId") String projectId
            , @PathVariable("delFlag") String delFlag, @RequestParam("query") String query);

    @GetMapping("/docos/{projectId}")
    @Operation(summary = "根据楼盘查询合同列表")
    RespWrapVo<List<String>> queryDocos(@PathVariable("projectId") String projectId);

    @GetMapping("/list/export")
    @Operation(summary = "导出电子账单列表及阅读情况")
    void exportList(@ModelAttribute BillSearchReqVo vo, HttpServletResponse response);

    @GetMapping("/payers/{doco}")
    @Operation(summary = "根据合同号查询付款人列表")
    RespWrapVo<Set<BillPayer>> queryPayers(@PathVariable("doco") String doco);

    @GetMapping(value = "/{id}")
    @Operation(summary = "获取电子账单详细信息")
    RespWrapVo<BillDetailRespVo> getInfo(@PathVariable("id") Long id);

    @DeleteMapping
    @Operation(summary = "移除账单")
    RespWrapVo<Boolean> deleteBill(@RequestParam("ids") List<Long> ids);

    @PostMapping("/sendBillList")
    @Operation(summary = "发送账单")
    RespWrapVo<Integer> sendBillList(@RequestBody BillSendReqVo inputVo);

    @GetMapping("/history")
    @Operation(summary = "查询账单发送历史")
    RespWrapVo<Page<StaffBillHistoryRespVo>> sendHistory(Pageable pageable
            , @ModelAttribute BillSendHistoryReqVo billSendHistoryReqVo);

    @GetMapping("/history/export")
    @Operation(summary = "导出账单发送历史")
    void exportHistory(@ModelAttribute BillSendHistoryReqVo billSendHistoryReqVo
            , HttpServletResponse response);

    @PostMapping("/email/callback")
    @Operation(summary = "邮件发送状态回调")
    RespWrapVo<Boolean> emailCallBack(@Valid @RequestBody EmailResultVo emailResultVo);

}
