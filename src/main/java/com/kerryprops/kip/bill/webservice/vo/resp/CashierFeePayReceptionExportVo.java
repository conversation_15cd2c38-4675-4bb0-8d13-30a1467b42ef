package com.kerryprops.kip.bill.webservice.vo.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 杂费单列表-前台人员-导出Vo
 *
 * <AUTHOR>
 * @date 2024-7-18
 */
@Data
public class CashierFeePayReceptionExportVo {

    @ExcelProperty("楼盘")
    @Schema(title = "楼盘")
    private String projectName;

    @ExcelProperty("楼栋")
    @Schema(title = "楼栋")
    private String buildingName;

    @ExcelProperty("户号")
    @Schema(title = "户号")
    private String roomNo;

    @ExcelProperty("订单日期")
    @Schema(title = "订单日期")
    @DateTimeFormat("yyyy-MM-dd")
    private Date payDate;

    @ExcelProperty("收款号")
    @Schema(title = "收款号")
    private String payAct;

    @ExcelProperty("费项")
    @Schema(title = "费项")
    private String feeName;

    @ExcelProperty("描述")
    @Schema(title = "描述")
    private String comment;

    @ExcelProperty("支付方式")
    @Schema(title = "支付方式")
    private String payType;

    @ExcelProperty("订单金额")
    @Schema(title = "订单金额(实收金额)")
    private BigDecimal totalAmt;

}
