package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.service.KerryBillEFapiaoService;
import com.kerryprops.kip.bill.service.model.s.BizBillEFapiaoSearchReqBo;
import com.kerryprops.kip.bill.webservice.vo.req.BizBillInvoiceSearchRequest;
import com.kerryprops.kip.bill.webservice.vo.resp.BizBillInvoiceResource;
import com.kerryprops.kip.bill.webservice.vo.resp.ContentUnreadInfoVo;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@AllArgsConstructor
@Tag(name = "办公商场-B端发票管理")
public class BizBillInvoiceController {

    private final KerryBillEFapiaoService kerryBillEFapiaoService;

    @Operation(summary = "按条件查询")
    @GetMapping(value = "/b/bill/invoice/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public Page<BizBillInvoiceResource> userInvoiceList(@SortDefault.SortDefaults(value = {
            @SortDefault(sort = "paperDrewDate", direction = Sort.Direction.DESC)
            , @SortDefault(sort = "createdTime", direction = Sort.Direction.DESC)
    }) Pageable pageable, BizBillInvoiceSearchRequest request) {
        BizBillEFapiaoSearchReqBo searchBo = BeanUtil.copy(request, BizBillEFapiaoSearchReqBo.class);
//        List<String> mcus = hiveAsClient.populateDataFields(searchBo.getProjectId(), searchBo.getBuildingIds());
//        searchBo.setMcus(mcus);
        return kerryBillEFapiaoService.userEFapiaoList(searchBo, pageable);
    }

    @Hidden
    @GetMapping(value = "/b/bill/invoice/{id}/url", produces = MediaType.APPLICATION_JSON_VALUE)
    public String invoiceUrl(@PathVariable(name = "id") long id) {
        return kerryBillEFapiaoService.queryUrlsByBillInvoiceId(id);
    }

    @Operation(summary = "当前用户是否全部已读")
    @GetMapping(value = "/b/bill/invoice/read_status", produces = MediaType.APPLICATION_JSON_VALUE)
    public ContentUnreadInfoVo userInvoiceReadStatus() {
        return kerryBillEFapiaoService.userInvoiceReadStatus();
    }

    @Operation(summary = "标记发票信息当前用户已读")
    @PostMapping(value = "/b/bill/invoice/id/{id}/set_readed", produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean setUserInvoiceReaded(@PathVariable(name = "id") Long id) {
        return kerryBillEFapiaoService.setUserInvoiceReaded(id);
    }

    @Operation(summary = "标记发票信息当前用户全部已读")
    @PostMapping(value = "/b/bill/invoice/all_readed", produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean setUserInvoiceAllReaded() {
        return kerryBillEFapiaoService.setUserInvoiceAllReaded();
    }

}
