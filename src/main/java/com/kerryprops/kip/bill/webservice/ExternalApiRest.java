package com.kerryprops.kip.bill.webservice;


import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@Tag(name = "外部接口")
@RequestMapping(value = "/s/external", produces = MediaType.APPLICATION_JSON_VALUE)
public interface ExternalApiRest {

    @Operation(summary = "查询票据码")
    @GetMapping("/queryGlcGroup")
    RespWrapVo<List<String>> queryGlcGroup();

}
