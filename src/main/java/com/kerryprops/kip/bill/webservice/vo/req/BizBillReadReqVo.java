package com.kerryprops.kip.bill.webservice.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class BizBillReadReqVo {

    private static final long serialVersionUID = 1L;

    @Schema(title = "账单ID")
    private Long id;

    @Schema(title = "更新阅读时间类型：0 站内信阅读 1 C端阅读")
    private Integer mailOrMobileSource;

}
