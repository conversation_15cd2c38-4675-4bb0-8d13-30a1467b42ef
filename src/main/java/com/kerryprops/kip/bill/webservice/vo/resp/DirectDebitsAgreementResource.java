package com.kerryprops.kip.bill.webservice.vo.resp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.kerryprops.kip.bill.common.enums.DirectDebitAgreementSignType;
import com.kerryprops.kip.bill.common.enums.DirectDebitAgreementStatus;
import com.kerryprops.kip.bill.config.ZonedDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.ZonedDateTime;

@Data
public class DirectDebitsAgreementResource {

    private String userProfileId;

    private String projectId;

    private String projectName;

    private String buildingId;

    private String buildingName;

    private String floorId;

    private String floorName;

    private String roomId;

    private String roomName;

    @Schema(title = "所有状态：INACTIVE(未激活)，ACTIVE(有效)，TERMINATED(失效)")
    private DirectDebitAgreementStatus agreementStatus;

    private String pspName;

    private String pspLogonId;

    private String agreementNo;

    @Schema(title = "两种签约类型：PAY_AND_SIGN，DIRECT_SIGN")
    private DirectDebitAgreementSignType signType;

    @JsonSerialize(using = ZonedDateTimeSerializer.class)
    private ZonedDateTime invalidTime;

    @JsonSerialize(using = ZonedDateTimeSerializer.class)
    private ZonedDateTime signedTime;

    private String userPhoneNumber;

    private String personalProductCode;

}
