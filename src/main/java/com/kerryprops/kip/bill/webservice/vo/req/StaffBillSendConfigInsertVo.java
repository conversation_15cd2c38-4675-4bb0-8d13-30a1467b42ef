package com.kerryprops.kip.bill.webservice.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.List;

@Data
@Schema
public class StaffBillSendConfigInsertVo {

    @Schema(title = "JDE合同号")
    @NotBlank(message = "doco can not be null")
    @Length(max = 32, message = "doco must be within 32 characters in length")
    private String doco;

    @Valid
    @Schema(title = "付款人")
    @NotEmpty(message = "payer is required")
    private List<BillPayerVo> billPayerVos;

    @Schema(title = "楼盘id")
    @NotBlank(message = "projectId can not be null")
    @Length(max = 32, message = "projectId must be within 32 characters in length")
    private String projectId;

    @Schema(title = "楼栋id")
    @NotBlank(message = "buildingId can not be null")
    @Length(max = 128, message = "buildingId must be within 128 characters in length")
    private String buildingId;

    @Schema(title = "楼栋Name")
    @NotBlank(message = "buildingName can not be null")
    @Length(max = 128, message = "buildingName must be within 128 characters in length")
    private String buildingName;

    @Schema(title = "账单接收人电话")
    @Length(max = 32, message = "phoneNumber must be within 32 characters in length")
    private String phoneNumber;

    @Schema(title = "账单接收人邮箱")
    @NotBlank(message = "email address can not be null")
    @Length(max = 128, message = "email address must be within 128 characters in length")
    private String email;

    @Schema(title = "B端账号")
    @Length(max = 64, message = "loginNo must be within 64 characters in length")
    private String loginNo;

}
