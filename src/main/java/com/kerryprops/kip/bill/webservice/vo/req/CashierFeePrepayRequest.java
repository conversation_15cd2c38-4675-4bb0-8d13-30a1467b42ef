package com.kerryprops.kip.bill.webservice.vo.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import com.kerryprops.kip.bill.common.enums.BillPayModule;
import com.kerryprops.kip.bill.common.enums.PaymentCateEnum;
import com.kerryprops.kip.bill.common.enums.PaymentPayType;
import com.kerryprops.kip.bill.common.utils.IdWorker;
import com.kerryprops.kip.bill.common.utils.PrimaryKeyUtil;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.feign.entity.FeeConfigVo;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.utils.BillUtil;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.apache.logging.log4j.util.Strings;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> 2023-12-15
 **/
@Getter
@Setter
public class CashierFeePrepayRequest {

    @NotBlank
    @Schema(title = "楼盘", required = true)
    private String projectId;

    @Schema(title = "楼栋")
    private String buildingId;

    @Schema(title = "户号")
    private String roomId;

    @NotNull
    @Schema(title = "订单日期[yyyy-MM-dd]", required = true)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date billDate;

    @Schema(title = "描述")
    private String description;

    @NotNull
    @Schema(title = "杂费配置id", required = true)
    private Long feeId;

    @NotBlank
    @Schema(title = "杂费配置名（费项）", required = true)
    private String feeName;

    @NotNull
    @Digits(integer = 32, fraction = 2)
    @Schema(title = "收款金额", description = "订单应收金额||账单金额，还没计算税率", required = true)
    private BigDecimal billAmount;

    @Schema(title = "流水号")
    private String pspTransNo;

    public AptPaymentInfo toAptPaymentInfo(PositionItemResponse position, FeeConfigVo feeConfig) {
        Date now = new Date();
        String paymentId = PrimaryKeyUtil.createPaymentId();
        String taxRate = feeConfig.getTaxRate();
        BigDecimal feeTax = new BigDecimal(taxRate);
        BigDecimal feeTaxAmount = BillUtil.calcFeeTaxAmount(billAmount, feeTax);
        String payAct = String.valueOf(IdWorker.getFlowIdWorkerInstance().nextId());

        AptPaymentInfo paymentInfo = new AptPaymentInfo();
        paymentInfo.setId(paymentId);
        paymentInfo.setCreateTime(now);
        paymentInfo.setUpdateTime(now);
        paymentInfo.setDeleted(0);
        paymentInfo.setPaymentTransNo(Strings.EMPTY);
        paymentInfo.setXmlUrl(Strings.EMPTY);
        paymentInfo.setOfdUrl(Strings.EMPTY);
        paymentInfo.setFailedReason(Strings.EMPTY);
        paymentInfo.setAgreementNo(Strings.EMPTY);
        paymentInfo.setAmt(billAmount.doubleValue());
        paymentInfo.setBindUserId(UserInfoUtils.getUserId());
        paymentInfo.setUserProfileId(UserInfoUtils.getUserProfileId());
        paymentInfo.setAdvanceAmount(BigDecimal.ZERO);
        paymentInfo.setBillPayModule(BillPayModule.CASHIER_FEE);
        paymentInfo.setCreateBy(UserInfoUtils.getKerryAccount());
        paymentInfo.setPaymentCate(PaymentCateEnum.UNKNOWN);
        paymentInfo.setPayType(PaymentPayType.UNKNOWN);
        paymentInfo.setPositionItem(position);
        paymentInfo.setFeeTax(feeTax);
        paymentInfo.setFeeTaxAmount(feeTaxAmount);
        paymentInfo.setPayAct(payAct);
        paymentInfo.setPayChannel(BillPayChannel.ONLINE);

        paymentInfo.setPaymentTime(getBillDate());
        paymentInfo.setProjectId(getProjectId());
        paymentInfo.setBuildingId(getBuildingId());
        paymentInfo.setPspTransNo(getPspTransNo());
        paymentInfo.setRoomId(getRoomId());
        paymentInfo.setDescription(getDescription());
        paymentInfo.setFeeId(getFeeId());
        paymentInfo.setFeeName(getFeeName());
        return paymentInfo;
    }

}
