package com.kerryprops.kip.bill.webservice.vo.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 手动推送JDE业务单至票易通执行结果-导出Vo
 *
 * <AUTHOR>
 * @date 2024-7-26
 */
@Data
public class BillInvoiceUploadResultExportVo {

    @Schema(title = "单据公司", example = "31007")
    @ExcelProperty("单据公司")
    private String kco;

    @Schema(title = "单据类型", example = "RN")
    @ExcelProperty("单据类型")
    private String billType;

    @Schema(title = "单据号", example = "19004780")
    @ExcelProperty("单据号")
    private int doc;

    @Schema(title = "付款项", example = "001")
    @ExcelProperty("付款项")
    private String paymentItem;

    @Schema(title = "上传结果")
    @ExcelProperty("上传结果")
    private String invoiceResult;

    @Schema(title = "失败原因")
    @ExcelProperty("失败原因")
    private String failReason;

}
