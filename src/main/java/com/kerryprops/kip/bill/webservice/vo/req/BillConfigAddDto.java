package com.kerryprops.kip.bill.webservice.vo.req;

import com.kerryprops.kip.bill.dao.entity.BillConfigEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * BillConfigAddDto.
 *
 * <AUTHOR> Yu
 */
@Data
public class BillConfigAddDto {

    @NotBlank
    @Schema(title = "账单类型")
    private String sourceType;

    @NotBlank
    @Schema(title = "账单来源名称")
    private String sourceName;

    @NotBlank
    @Schema(title = "账单获取服务地址")
    private String httpService;

    public BillConfigEntity toBillConfigEntity() {
        BillConfigEntity billConfigEntity = new BillConfigEntity();
        billConfigEntity.setSourceType(sourceType);
        billConfigEntity.setSourceName(sourceName);
        billConfigEntity.setHttpService(httpService);
        return billConfigEntity;
    }

}