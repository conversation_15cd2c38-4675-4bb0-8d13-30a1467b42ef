package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.dao.entity.BillSelectConfig;
import com.kerryprops.kip.bill.service.impl.BillSelectConfigService;
import com.kerryprops.kip.bill.webservice.vo.req.BillSelectConfigAddDto;
import com.kerryprops.kip.bill.webservice.vo.req.BillSelectConfigExistDto;
import com.kerryprops.kip.bill.webservice.vo.req.BillSelectConfigListDto;
import com.kerryprops.kip.bill.webservice.vo.req.BillSelectConfigPutDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * StaffBillSelectConfigController.
 *
 * <AUTHOR> Yu 2025-02-27 15:57:17
 **/
@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "S端账单选择配置")
@RequestMapping(value = "/s/bill/select_configs")
public class StaffBillSelectConfigController {

    private final BillSelectConfigService billSelectConfigService;

    @Operation(summary = "是否已存在配置", description = "前端新增或更新配置前，查询该接口")
    @GetMapping("/exist")
    public ExistVo exist(BillSelectConfigExistDto dto) {
        return new ExistVo(billSelectConfigService.existConfig(dto));
    }

    @Operation(summary = "账单选择配置详情")
    @GetMapping("/{id}")
    public BillSelectConfig getDetail(@PathVariable Long id) {
        return billSelectConfigService.getConfig(id);
    }

    @Operation(summary = "账单选择配置列表")
    @GetMapping
    public Page<BillSelectConfig> list(@SortDefault(sort = "createTime", direction = Sort.Direction.DESC)
                                       Pageable pageable,
                                       BillSelectConfigListDto dto) {
        return billSelectConfigService.listConfig(dto, pageable);
    }

    @Operation(summary = "新增账单选择配置")
    @PostMapping
    public BillSelectConfig add(@RequestBody BillSelectConfigAddDto dto) {
        return billSelectConfigService.addConfig(dto);
    }

    @Operation(summary = "修改账单选择配置")
    @PutMapping("/{id}")
    public BillSelectConfig put(@PathVariable Long id, @RequestBody BillSelectConfigPutDto dto) {
        return billSelectConfigService.updateConfig(id, dto);
    }

    @Operation(summary = "删除账单选择配置")
    @DeleteMapping("/{id}")
    public BillSelectConfig delete(@PathVariable Long id) {
        return billSelectConfigService.deleteConfig(id);
    }

    public record ExistVo(Boolean exist) {

    }

}
