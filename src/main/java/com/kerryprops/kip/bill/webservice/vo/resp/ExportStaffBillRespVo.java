package com.kerryprops.kip.bill.webservice.vo.resp;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 账单查询的 view
 */
@Schema
public class ExportStaffBillRespVo {

    @ExcelProperty("合同编号")
    @Schema(title = "jde合同号")
    private String tpDoco;

    @ExcelProperty("用户编号")
    @Schema(title = "用户编号")
    private String tpAn8;

    @ExcelProperty("用户名称")
    @Schema(title = "用户名称")
    private String tpAlph;

    @ExcelProperty("账单类型")
    @Schema(title = "账单类型")
    private String tpDct;

    @ExcelProperty("账单打印时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(title = "账单打印时间")
    private String formatDate;

    @ExcelProperty("MCU")
    @Schema(title = "建筑物编号")
    private String tpMcu;

    @ExcelProperty("单元号")
    @Schema(title = "账单单元")
    private String tpUnit;

    @ExcelProperty("账单年份")
    @Schema(title = "账单年")
    private Integer tpFyr;

    @ExcelProperty("账单月份")
    @Schema(title = "账单月")
    private Integer tpPn;

    @ExcelIgnore
    private Integer mailStatus;

    @ExcelIgnore
    private Integer emailStatus;

    @ExcelProperty("站内信发送状态")
    @Schema(title = "账单站内信发送状态")
    private String mailStatusString;

    @ExcelProperty("邮件发送状态")
    @Schema(title = "账单邮件发送状态")
    private String emailStatusString;

    public String getMailStatusString() {
        return mailStatusString;
    }

    public void setMailStatusString(String mailStatusString) {
        this.mailStatusString = mailStatusString;
    }

    public String getEmailStatusString() {
        return emailStatusString;
    }

    public void setEmailStatusString(String emailStatusString) {

        this.emailStatusString = emailStatusString;
    }

    public String getTpDoco() {
        return tpDoco;
    }

    public void setTpDoco(String tpDoco) {
        this.tpDoco = tpDoco;
    }

    public String getTpAn8() {
        return tpAn8;
    }

    public void setTpAn8(String tpAn8) {
        this.tpAn8 = tpAn8;
    }

    public String getTpAlph() {
        return tpAlph;
    }

    public void setTpAlph(String tpAlph) {
        this.tpAlph = tpAlph;
    }

    public String getTpDct() {
        return tpDct;
    }

    public void setTpDct(String tpDct) {
        this.tpDct = tpDct;
    }

    public String getFormatDate() {
        return formatDate;
    }

    public void setFormatDate(String formatDate) {
        this.formatDate = formatDate;
    }

    public String getTpMcu() {
        return tpMcu;
    }

    public void setTpMcu(String tpMcu) {
        this.tpMcu = tpMcu;
    }

    public String getTpUnit() {
        return tpUnit;
    }

    public void setTpUnit(String tpUnit) {
        this.tpUnit = tpUnit;
    }

    public Integer getTpFyr() {
        return tpFyr;
    }

    public void setTpFyr(Integer tpFyr) {
        this.tpFyr = tpFyr;
    }

    public Integer getTpPn() {
        return tpPn;
    }

    public void setTpPn(Integer tpPn) {
        this.tpPn = tpPn;
    }

    public Integer getMailStatus() {
        return mailStatus;
    }

    public void setMailStatus(Integer mailStatus) {
        if (mailStatus == null || mailStatus == 0) {
            mailStatusString = "未发送";
        } else if (mailStatus == 3) {
            mailStatusString = "发送中";
        } else if (mailStatus == 5) {
            mailStatusString = "已发送";
        } else if (mailStatus == 10) {
            mailStatusString = "发送失败";
        } else if (mailStatus == 15) {
            mailStatusString = "部分发送成功";
        } else if (mailStatus == 30) {
            mailStatusString = "发送成功";
        } else {
            mailStatusString = "未知";
        }
        this.mailStatus = mailStatus;
    }

    public Integer getEmailStatus() {
        return emailStatus;
    }

    public void setEmailStatus(Integer emailStatus) {
        if (emailStatus == null || emailStatus == 0) {
            emailStatusString = "未发送";
        } else if (emailStatus == 3) {
            emailStatusString = "发送中";
        } else if (emailStatus == 5) {
            emailStatusString = "已发送";
        } else if (emailStatus == 10) {
            emailStatusString = "发送失败";
        } else if (emailStatus == 15) {
            emailStatusString = "部分发送成功";
        } else if (emailStatus == 30) {
            emailStatusString = "发送成功";
        } else {
            emailStatusString = "未知";
        }
        this.emailStatus = emailStatus;
    }

}
