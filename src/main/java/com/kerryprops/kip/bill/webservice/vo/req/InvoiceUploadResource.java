package com.kerryprops.kip.bill.webservice.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - repair-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 08/30/2021 14:05
 **********************************************************************************************************************/

@Getter
@Setter
@Builder
@Schema
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceUploadResource implements Serializable {

    @NotBlank(message = "账单号不能为空")
    @Schema(title = "账单号")
    private String orderNo;

    @NotBlank(message = "发票类型不能为空")
    @Schema(title = "发票类型")
    private String invoiceType;

    @NotNull(message = "抬头类型不能为空")
    @Schema(title = "抬头类型，1：个人， 2：企业")
    private Integer titleType;

    @NotBlank(message = "发票抬头不能为空")
    @Schema(title = "发票抬头")
    private String invoiceTitle;

    @Schema(title = "税号")
    private String taxNo;

    @Schema(title = "开户银行")
    private String bankName;

    @Schema(title = "银行账号")
    private String bankAccount;

    @Schema(title = "银行地址")
    private String address;

    @Schema(title = "国际电话区号，推荐携带 +", example = "+44")
    private String areaCode;

    @Schema(title = "企业电话")
    private String tel;

    @NotBlank(message = "邮箱不能为空")
    @Schema(title = "收票邮箱")
    private String email;

    @Schema(title = "备注")
    private String remark;

}
