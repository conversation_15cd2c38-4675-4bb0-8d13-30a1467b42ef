package com.kerryprops.kip.bill.webservice.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

@Data
@Schema
public class BillSendConfigReqVo {

    private static final long serialVersionUID = 1L;

    /**
     * 建筑物
     */
    @Schema(title = "bu")
    private String mcu;

    /**
     * 账单单元
     */
    @Schema(title = "jde单元", example = "B39")
    private String unit;

    /**
     * jde合同号
     */
    @Schema(title = "jde合同号", example = "320021")
    private String doco;

    /**
     * 合同主体名称
     */
    @Schema(title = "公司名称")
    private String alph;

    /**
     * 用户编号
     */
    @Schema(title = "用户编号", example = "20275864")
    private String an8;

    @Schema(title = "建筑物编号", example = "[\"42105100\", \"32014100\"]")
    private List<String> buildingIds;

    @Schema(title = "楼盘ID")
    @NotEmpty(message = "楼盘ID不能为空")
    private String projectId;

}
