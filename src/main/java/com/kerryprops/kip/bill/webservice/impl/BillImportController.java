package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.aop.SignValid;
import com.kerryprops.kip.bill.service.KerryBillImportService;
import com.kerryprops.kip.bill.webservice.vo.req.ImportKerryBillRequest;
import com.kerryprops.kip.bill.webservice.vo.resp.ImportedBillResource;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@AllArgsConstructor
public class BillImportController {

    private final KerryBillImportService billImportService;

    /**
     * 目前仅用于沈阳储值卡电子对账单KIP-9949
     */
    @SignValid(value = "skcVc")
    @PostMapping(value = "/s/tenants/bills/import", consumes = MediaType.APPLICATION_JSON_VALUE
            , produces = MediaType.APPLICATION_JSON_VALUE)
    public ImportedBillResource billImport(@RequestBody @Valid ImportKerryBillRequest request) {
        int count = billImportService.importBill(request);
        ImportedBillResource response = new ImportedBillResource();
        response.setImportedBillCount(count);
        return response;
    }

}
