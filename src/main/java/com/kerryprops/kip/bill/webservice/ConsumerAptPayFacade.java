package com.kerryprops.kip.bill.webservice;

import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptPaymentVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillSessionResult;
import com.kerryprops.kip.pmw.client.resource.AsynPaymentResultResource;
import com.kerryprops.kip.pmw.client.resource.AsyncPaymentFailedResource;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/***********************************************************************************************************************
 * Project - accelerator
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - David Wei
 * Created Date - 06/03/2021 14:05
 **********************************************************************************************************************/

@Tag(name = "公寓小区-C端支付")
@RequestMapping(value = "", produces = MediaType.APPLICATION_JSON_VALUE)
public interface ConsumerAptPayFacade {

    @PostMapping("/c/apt/pay/callback")
    @Operation(summary = "支付回调")
    String callback(@RequestBody AsynPaymentResultResource paymentResult);

    @Hidden
    @PostMapping("/payment/failed/callback")
    String payFailedCallback(@RequestBody AsyncPaymentFailedResource paymentFailedResource);

    @Operation(summary = "用户主动取消支付")
    @PostMapping("/c/apt/pay/cancelPayment/{paymentId}")
    RespWrapVo cancelPayment(@PathVariable("paymentId") String paymentId);

    @Operation(summary = "账单支付（合并支付） v2")
    @PostMapping("/c/apt/pay/v2/session")
    RespWrapVo<BillSessionResult> v2Pay(@RequestBody List<AptPaymentVo> aptPayVos);

}
