package com.kerryprops.kip.bill.webservice.impl;

import com.google.common.collect.Lists;
import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.webservice.AptDicFacade;
import com.kerryprops.kip.bill.webservice.vo.resp.EnumResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 1. super admin: can do any thing;
 * 2. other: can do any thing under bu
 */
@Slf4j
@RestController
public class AptDicController implements AptDicFacade {


    @Override
    public RespWrapVo<Map<String, List<EnumResponse>>> getCEnums(@RequestParam(value = "name", required = false) String name) {
        return new RespWrapVo<>(populateEnum(name));
    }

    @Override
    public RespWrapVo<Map<String, List<EnumResponse>>> getSEnums(@RequestParam(value = "name", required = false) String name) {
        return new RespWrapVo<>(populateEnum(name));
    }

    private Map<String, List<EnumResponse>> populateEnum(String name) {
        Map<String, List<EnumResponse>> resp = new HashMap<>();

        List<EnumResponse> payChannelList = Lists.newArrayList();
        resp.put("payChannel", payChannelList);
        for (BillPayChannel enumEnitity : BillPayChannel.values()) {
            EnumResponse enumResponse = new EnumResponse();
            enumResponse.setCode(enumEnitity.toString());
            enumResponse.setDisplayName(enumEnitity.getInfo());
            payChannelList.add(enumResponse);
        }

//        List<EnumResponse> payTypeList = Lists.newArrayList();
//        resp.put("payType", payTypeList);
//        for (BillPayType enumEnitity : BillPayType.values()) {
//            EnumResponse enumResponse = new EnumResponse();
//            enumResponse.setCode(enumEnitity.toString());
//            enumResponse.setDisplayName(enumEnitity.getInfo());
//            payTypeList.add(enumResponse);
//        }

        if (StringUtils.isEmpty(name)) {
            return resp;
        }
        Map<String, List<EnumResponse>> oneResp = new HashMap<>();
        oneResp.put(name, resp.get(name));
        return oneResp;
    }

}
