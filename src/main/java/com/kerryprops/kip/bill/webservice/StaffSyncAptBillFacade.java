package com.kerryprops.kip.bill.webservice;

import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/***********************************************************************************************************************
 * Project - accelerator
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - <PERSON>
 * Created Date - 06/03/2021 14:05
 **********************************************************************************************************************/

@Tag(name = "公寓小区-账单同步")
@RequestMapping(value = "/s/apt/bill", produces = MediaType.APPLICATION_JSON_VALUE)
public interface StaffSyncAptBillFacade {

    @GetMapping("/{projectId}/sync")
    @Operation(summary = "同步JDE账单")
    RespWrapVo<Boolean> sync(@PathVariable("projectId") String projectId);

    @GetMapping("/sync/hive")
    @Operation(summary = "补充JDE账单的hive信息")
    RespWrapVo<Boolean> syncHive();

    @GetMapping("/verify")
    @Operation(summary = "验证用户及账单是否存在")
    RespWrapVo<Boolean> verifyUser(@RequestParam("roomId") String roomId, @RequestParam("billNo") String billNo);

}
