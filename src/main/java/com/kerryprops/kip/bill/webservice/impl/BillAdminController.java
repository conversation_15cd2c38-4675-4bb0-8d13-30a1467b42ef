package com.kerryprops.kip.bill.webservice.impl;

import com.google.common.collect.Maps;
import com.kerryprops.kip.bill.dao.AptErrJdeBillRepository;
import com.kerryprops.kip.bill.dao.AptPaymentInfoRepository;
import com.kerryprops.kip.bill.dao.AptSyncJdeJobRepository;
import com.kerryprops.kip.bill.dao.AptSyncPaidBillToJdeRepository;
import com.kerryprops.kip.bill.dao.AptSyncPayToJdeRepository;
import com.kerryprops.kip.bill.dao.BillEmailTraceRepository;
import com.kerryprops.kip.bill.dao.EFapiaoBillInvoiceRepository;
import com.kerryprops.kip.bill.dao.EFapiaoSendRecordRepository;
import com.kerryprops.kip.bill.dao.EFapiaoSyncRepository;
import com.kerryprops.kip.bill.dao.entity.QAptSyncPaidBillToJde;
import com.kerryprops.kip.bill.dao.entity.QAptSyncPayToJde;
import com.kerryprops.kip.bill.dao.entity.QEFapiaoSyncBill;
import com.kerryprops.kip.bill.webservice.scheduler.PaymentInvoiceScheduler;
import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.HikariPoolMXBean;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Hidden
@RestController
public class BillAdminController {

    @Autowired
    @Qualifier("jdeJdbcTemplate")
    private JdbcTemplate jdeJdbcTemplate;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private AptErrJdeBillRepository aptErrJdeBillRepository;

    @Autowired
    private AptSyncJdeJobRepository aptSyncJdeJobRepository;

    @Autowired
    private AptSyncPayToJdeRepository aptSyncPayToJdeRepository;

    @Autowired
    private AptSyncPaidBillToJdeRepository aptSyncPaidBillToJdeRepository;

    @Autowired
    private AptPaymentInfoRepository aptPaymentInfoRepository;

    @Autowired
    private BillEmailTraceRepository emailTraceRepository;

    @Autowired
    private EFapiaoSendRecordRepository eFapiaoSendRecordRepository;

    @Autowired
    private EFapiaoBillInvoiceRepository eFapiaoBillInvoiceRepository;

    @Autowired
    private EFapiaoSyncRepository eFapiaoSyncRepository;

    @Autowired
    private PaymentInvoiceScheduler paymentInvoiceScheduler;

    @GetMapping("/admin/metrics/jdbc/info")
    public Object jdbcInfo() {
        Map<String, Object> map = Maps.newHashMap();
        HikariPoolMXBean jdePool = ((HikariDataSource) jdeJdbcTemplate.getDataSource()).getHikariPoolMXBean();
        map.put("jdeJdbc", PoolInfo.info(jdePool));
        HikariPoolMXBean defaultPool = ((HikariDataSource) jdbcTemplate.getDataSource()).getHikariPoolMXBean();
        map.put("commonJdbc", PoolInfo.info(defaultPool));
        return map;
    }

    /**
     * apt_sync_bills_error
     */
    @PostMapping("/admin/apt/jde/bill/sync/error/log")
    public Object aptSyncBillsError(@SortDefault.SortDefaults(value = {
            @SortDefault(sort = "updateTime", direction = Sort.Direction.DESC)}) Pageable pageable) {
        return aptErrJdeBillRepository.findAll(pageable);
    }

    /**
     * apt_sync_jde_job
     */
    @PostMapping("/admin/apt/jde/bill/sync/job/log")
    public Object aptSyncJdeJob(@SortDefault.SortDefaults(value = {
            @SortDefault(sort = "updateTime", direction = Sort.Direction.DESC)}) Pageable pageable) {
        return aptSyncJdeJobRepository.findAll(pageable);
    }

    /**
     * apt_sync_pay_to_jde
     */
    @PostMapping("/admin/apt/sync/bill/{billNo}/pay/jde")
    public Object syncPayToJde(@SortDefault.SortDefaults(value = {
                                       @SortDefault(sort = "updateTime", direction = Sort.Direction.DESC)}) Pageable pageable
            , @PathVariable("billNo") String billNo) {
        return aptSyncPayToJdeRepository.findAll(QAptSyncPayToJde.aptSyncPayToJde.rccknu.eq(billNo)
                , pageable);
    }

    @PostMapping("/admin/apt/sync/bill/pay/jde/{docNo}")
    public Object syncPayToJde2(@SortDefault.SortDefaults(value = {
                                        @SortDefault(sort = "updateTime", direction = Sort.Direction.DESC)}) Pageable pageable
            , @PathVariable("docNo") Long docNo) {
        return aptSyncPayToJdeRepository.findAll(QAptSyncPayToJde.aptSyncPayToJde.rcdoc.eq(docNo)
                , pageable);
    }

    /**
     * apt_sync_paid_bill_to_jde
     */
    @PostMapping("/admin/apt/sync/bill/{billNo}/paid/jde")
    public Object paidBillToJde(@SortDefault.SortDefaults(value = {
                                        @SortDefault(sort = "updateTime", direction = Sort.Direction.DESC)}) Pageable pageable
            , @PathVariable("billNo") String billNo) {
        return aptSyncPaidBillToJdeRepository.findAll(QAptSyncPaidBillToJde.aptSyncPaidBillToJde.rbcknu.eq(billNo)
                , pageable);
    }

    @PostMapping("/admin/apt/sync/bill/paid/jde/{docNo}")
    public Object paidBillToJde2(@SortDefault.SortDefaults(value = {
                                         @SortDefault(sort = "updateTime", direction = Sort.Direction.DESC)}) Pageable pageable
            , @PathVariable("docNo") Long docNo) {
        return aptSyncPaidBillToJdeRepository.findAll(QAptSyncPaidBillToJde.aptSyncPaidBillToJde.rcdoc.eq(docNo)
                , pageable);
    }

    @PostMapping("/admin/apt/payment/info/{id}")
    public Object paymentInfo(@PathVariable("id") String id) {
        return aptPaymentInfoRepository.findById(id);
    }

    @PostMapping("/admin/bill/email/trace/batch/{batchNo}")
    public Object billEmailTrace(@PathVariable("batchNo") String batchNo) {
        return emailTraceRepository.findAllByBatchNo(batchNo);
    }

    @PostMapping("/admin/bill/invoice/{id}")
    public Object billInvoice(@PathVariable("id") Long id) {
        return eFapiaoBillInvoiceRepository.findById(id);
    }

    @PostMapping("/admin/bill/invoice/salesBillNo/{no}")
    public Object billInvoice(@PathVariable("no") String no) {
        return eFapiaoBillInvoiceRepository.findAllBySalesBillNo(no);
    }


    @PostMapping("admin/bill/invoice/{id}/sendlog")
    public Object billInvoiceSend(@PathVariable("id") Long id) {
        return eFapiaoSendRecordRepository.findAllByBillFapiaoIdIn(List.of(id));
    }

    @PostMapping("admin/bill/invoice/sync/salesBillNo/{no}")
    public Object billInvoiceSync(@PathVariable("no") String no) {
        return eFapiaoSyncRepository.getJpaQueryFactory().select(QEFapiaoSyncBill.eFapiaoSyncBill)
                .from(QEFapiaoSyncBill.eFapiaoSyncBill)
                .where(QEFapiaoSyncBill.eFapiaoSyncBill.salesBillNo.eq(no)).fetch();
    }

    @PostMapping("/admin/apt/sync/bill/invoiced_status")
    public Object pullInvoicedStatus() {
        paymentInvoiceScheduler.pullInvoicedStatus();
        return "success";
    }

    @Data
    private static class PoolInfo {

        int idleConnections;

        int activeConnections;

        int totalConnections;

        int threadsAwaitingConnection;

        static PoolInfo info(HikariPoolMXBean p) {
            PoolInfo info = new PoolInfo();
            info.idleConnections = p.getIdleConnections();
            info.activeConnections = p.getActiveConnections();
            info.totalConnections = p.getTotalConnections();
            info.threadsAwaitingConnection = p.getThreadsAwaitingConnection();
            return info;
        }

    }

}
