package com.kerryprops.kip.bill.webservice.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class DirectDebitsAgreementsQueryRequest {

    @Schema(title = "project ID")
    private String projectId;

    @Schema(title = "building ID")
    private List<String> buildingIds;

    @Schema(title = "room ID")
    private List<String> roomIds;

    @Schema(title = "代扣方案：alipay、wechatpay")
    private String pspName;

    @Schema(title = "所有状态：INACTIVE(未激活)，ACTIVE(有效)，TERMINATED(失效)")
    private String agreementStatus;

}
