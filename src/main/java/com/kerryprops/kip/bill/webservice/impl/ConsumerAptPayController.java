package com.kerryprops.kip.bill.webservice.impl;

import com.alibaba.fastjson.JSON;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.service.AptBillPaymentCallbackService;
import com.kerryprops.kip.bill.service.PaymentBillService;
import com.kerryprops.kip.bill.webservice.ConsumerAptPayFacade;
import com.kerryprops.kip.bill.webservice.vo.req.AptPaymentVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillSessionResult;
import com.kerryprops.kip.pmw.client.resource.AsynPaymentResultResource;
import com.kerryprops.kip.pmw.client.resource.AsyncPaymentFailedResource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 1. super admin: can do any thing;
 * 2. other: can do any thing under bu
 */
@Slf4j
@RestController
public class ConsumerAptPayController implements ConsumerAptPayFacade {


    @Autowired
    private PaymentBillService paymentBillService;

    @Autowired
    private AptBillPaymentCallbackService paymentCallbackService;

/*    @Autowired
    private AptPayBillRepository payBillRepository;

    @Autowired
    private AptBillRepository billRepository;

    @Autowired
    private AptPayConfigRepository payConfigRepository;

    @Autowired
    private AptPayRepository payRepository;


    @Autowired
    private AptJdeBillRepository jdeBillRepository;

    @Autowired
    private HiveAsClient hiveAsClient;

    @Autowired
    private CUserClient cUserClient;

    @Autowired
    private PaymentClientService paymentClientService;

    @Autowired
    private PaymentConfigProps paymentConfigProps;

    private static final String BILL_REDIS_KEY = "BKOLD:";*/

    /**
     * @Method: pay
     * @Description: 账单支付（合并支付）
     * @Date: 2022/1/17 11:49
     * @Author: Kanon
     * @param: billNos
     * @return: com.kerryprops.kip.bill.common.vo.RespWrapVo<com.kerryprops.kip.bill.webservice.vo.resp.BillSessionResult>
     */
/*    @Override
    @Transactional
    public RespWrapVo<BillSessionResult> pay(List<String> billNos) {
        log.info("创建账单合并billNos: {}", billNos);
        var billSessionResult = paymentBillService.billPayment(billNos);
        log.info("账单合并创建完成: {}", JSON.toJSONString(billSessionResult));
        return new RespWrapVo<>(billSessionResult);
    }*/

    /**
     * @Method: pay
     * @Description: 账单支付（合并支付）
     * @Date: 2022/5/27 20:27
     * @Author: Jane Dong
     * @param: billNos, billExtras
     * @return: com.kerryprops.kip.bill.common.vo.RespWrapVo<com.kerryprops.kip.bill.webservice.vo.resp.BillSessionResult>
     */
    @Override
    @Transactional
    public RespWrapVo<BillSessionResult> v2Pay(@RequestBody List<AptPaymentVo> aptPayVos) {
        log.info("创建账单合并aptPayVos: {}", JSON.toJSONString(aptPayVos));
        BillSessionResult billSessionResult = paymentBillService.billPayment(aptPayVos, true);
        log.info("账单合并创建完成: {}", JSON.toJSONString(billSessionResult));
        return new RespWrapVo<>(billSessionResult);
    }

    /**
     * @Method: callback
     * @Description: 支付回调
     * @Date: 2022/2/6 12:24
     * @Author: Kanon
     * @param: AsynPaymentResultResource 支付成功/取消 数据
     * @return: String： 回调成功信息
     */
    @Override
    public String callback(@RequestBody AsynPaymentResultResource paymentResult) {
        var body = paymentResult.getBody();
        log.info("账单支付回调: {}", JSON.toJSONString(body));
        try {
            paymentCallbackService.handlePaymentCallback(body);
        } catch (Exception e) {
            log.error("payment_callback_error detail: ", e);
            return e.getMessage();
        }
        log.info("账单支付回调:结束");
        return "accepted";
    }

    /**
     * @Method: cancelPayment
     * @Description: 用户主动取消支付
     * @Date: 2022/2/6 13:48
     * @Author: Kanon
     * @param: paymentId
     * @return: com.kerryprops.kip.bill.common.vo.RespData
     */
    @Override
    public RespWrapVo cancelPayment(String paymentId) {
        paymentBillService.cancelPayment(paymentId);
        return new RespWrapVo();
    }

    @Override
    public String payFailedCallback(@RequestBody AsyncPaymentFailedResource paymentFailedResource) {
        try {
            paymentCallbackService.handlePaymentFailedCallback(paymentFailedResource);
        } catch (Exception e) {
            log.error("payment_failed_callback_error detail: ", e);
            return e.getMessage();
        }
        log.info("账单failed支付回调:结束");
        return "accepted";
    }

}
