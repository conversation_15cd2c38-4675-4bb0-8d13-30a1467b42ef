package com.kerryprops.kip.bill.webservice.vo.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.kerryprops.kip.bill.common.enums.BillSelectMode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * BillSelectConfigListDto.
 *
 * <AUTHOR> Yu 2025-02-27 17:10:50
 **/
@Data
public class BillSelectConfigListDto {

    @NotBlank
    @Schema(title = "项目唯一标识")
    private String projectId;

    @Schema(title = "楼栋id")
    private String buildingId;

    @Schema(title = "单元id")
    private String roomId;

    @Schema(title = "账单选择模式")
    private BillSelectMode billSelectMode;

    @JsonIgnore
    @SuppressWarnings("unused")
    @AssertTrue(message = "roomId 存在，buildingId 不能为空")
    boolean isBuildingIdRequired() {
        if (roomId == null) {
            return true;
        }
        return buildingId != null;
    }

}
