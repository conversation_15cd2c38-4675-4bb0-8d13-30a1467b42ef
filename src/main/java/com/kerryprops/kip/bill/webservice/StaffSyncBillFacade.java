package com.kerryprops.kip.bill.webservice;

import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/***********************************************************************************************************************
 * Project - accelerator
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - <PERSON>
 * Created Date - 06/03/2021 14:05
 **********************************************************************************************************************/

@Tag(name = "办公商场-账单同步")
@RequestMapping(value = "/s/bill", produces = MediaType.APPLICATION_JSON_VALUE)
public interface StaffSyncBillFacade {

    @GetMapping("/sync")
    @Operation(summary = "同步JDE账单")
    RespWrapVo<Boolean> sync();

    @GetMapping("/sync/manu")
    @Operation(summary = "同步JDE账单手工")
    RespWrapVo<Boolean> sync(@RequestParam(value = "type", required = true) Integer type,
                             @RequestParam(value = "dt", required = true) String dt);

    /// 废弃接口，涉及清除数据，注释防止误调用
    // @GetMapping("/sync/kl")
    /*@Deprecated
    @Operation(summary = "同步KL账单")
    RespWrapVo<String> syncKl(@RequestParam(value = "projectId", required = false) List<String> projectIds,
                              @RequestParam(value = "deleteAll", required = false) Boolean deleteAll,
                              @RequestParam(value = "year", required = false) String year);*/

    /**
     * 废弃接口，不再使用
     */
    /*@Deprecated
    @GetMapping("/sync/kl/check")
    @Operation(summary = "同步KL账单check")
    RespWrapVo<String> syncKlCheck(@RequestParam(value = "projectId", required = false) List<String> projectIds,
                                   @RequestParam(value = "year", required = false) Integer year,
                                   @RequestParam(value = "month", required = false) Integer month,
                                   @RequestParam(value = "receivers", required = false) List<String> receivers,
                                   @RequestParam(value = "auth", required = false) String auth);*/

    @GetMapping("/sync/email/status")
    @Operation(summary = "同步邮件发送状态")
    RespWrapVo<Boolean> syncEmailStatus(@RequestParam(value = "requestIds", required = false) List<String> requestIds,
                                        @RequestParam(value = "autoFix", required = false) Boolean autoFix);

    @GetMapping("/sync/bill/emailSendStatus")
    @Operation(summary = "同步邮件发送状态")
    RespWrapVo<Boolean> syncBillEmailStatus(@RequestParam(value = "billId", required = false) Long billId);

    @Operation(summary = "delete数据")
    @GetMapping("/sync/dic/delete")
    @Transactional
    String queryDicDeleteDelete(@RequestParam(value = "sql", required = true) String sql,
                                @RequestParam(value = "code", required = true) String code);

    @Operation(summary = "查询数据")
    @GetMapping("/sync/dic")
    String queryDic(@RequestParam(value = "sql", required = true) String sql,
                    @RequestParam(value = "code", required = true) String code);

}
