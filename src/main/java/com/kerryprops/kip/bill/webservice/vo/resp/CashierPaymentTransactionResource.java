package com.kerryprops.kip.bill.webservice.vo.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import com.kerryprops.kip.bill.common.enums.CashierPayStatus;
import com.kerryprops.kip.bill.common.enums.InvoicedStatus;
import com.kerryprops.kip.bill.common.enums.PaymentCateEnum;
import com.kerryprops.kip.bill.common.enums.PaymentPayType;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Schema
@Data
public class CashierPaymentTransactionResource {

    private long id;

    @Schema(title = "楼盘ID")
    private String projectId;

    @Schema(title = "楼栋ID")
    private String buildingId;

    @Schema(title = "楼层ID")
    private String floorId;

    @Schema(title = "单元ID")
    private String roomId;

    @Schema(title = "名称信息")
    private PositionItemResponse positionItem;

    @Schema(title = "收款号")
    private String payAct;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(title = "支付时间")
    private Date payDate;

    @Schema(title = "描述")
    private String comment;

    @Schema(title = "支付方式")
    private String payType;

    @Schema(title = "实收总额")
    private BigDecimal totalAmt;

    @Schema(title = "预收总额")
    private BigDecimal advanceAmt;

    @Schema(title = "预收科目")
    private String paymentCate;

    @Schema(title = "状态")
    private CashierPayStatus status;

    @Schema(title = "关联paymentInfo ID")
    private String paymentInfoId;

    @Schema(title = "操作人")
    private String accountFromType;

    @Schema(title = "收款记录生成时间")
    private Date createTime;

    @Schema(title = "收款记录更新时间")
    private Date updateTime;

    @Schema(title = "开票金额")
    private BigDecimal canInvoiceBillAmount;

    @Schema(title = "预收账单能否开票标识")
    private String isAdvanceBilling = "0";

    @Schema(title = "是否已开票状态")
    private InvoicedStatus invoicedStatus;

    public static CashierPaymentTransactionResource of(AptPay vo) {
        CashierPaymentTransactionResource resource = new CashierPaymentTransactionResource();
        resource.setTotalAmt(BigDecimal.valueOf(vo.getTotalAmt()));
        resource.setId(vo.getId());
        resource.setProjectId(vo.getProjectId());
        resource.setBuildingId(vo.getBuildingId());
        resource.setFloorId(vo.getFloorId());
        resource.setRoomId(vo.getRoomId());
        resource.setPositionItem(vo.getPositionItem());
        resource.setPayAct(vo.getPayAct());
        resource.setPayDate(vo.getPayDate());
        resource.setComment(extractDescription(vo));
        resource.setPayType(vo.getPayType());
        resource.setAdvanceAmt(vo.getAdvanceAmount());
        resource.setPaymentCate(vo.getPaymentCate());
        resource.setPaymentInfoId(vo.getPaymentInfoId());
        resource.setCreateTime(vo.getCreateTime());
        resource.setUpdateTime(vo.getUpdateTime());
        resource.setAccountFromType(vo.getCreateBy());
        resource.setInvoicedStatus(vo.getInvoicedStatus());
        return resource;
    }

    public String getPaymentCate() {
        if (PaymentCateEnum.UNKNOWN.name().equals(paymentCate)) {
            return null;
        }
        return paymentCate;
    }

    public String getPayType() {
        if (PaymentPayType.UNKNOWN.getInfo().equals(payType)) {
            return null;
        }
        return payType;
    }

    private static String extractDescription(AptPay vo) {
        var payChannel = vo.getPayChannel();
        if (BillPayChannel.DIRECT_DEBITS.equals(payChannel)
                || BillPayChannel.ONLINE.equals(payChannel)) {
            return vo.getPayDesc();
        }
        if (BillPayChannel.OFFLINE.equals(payChannel)) {
            return vo.getComments();
        }
        return null;
    }

}
