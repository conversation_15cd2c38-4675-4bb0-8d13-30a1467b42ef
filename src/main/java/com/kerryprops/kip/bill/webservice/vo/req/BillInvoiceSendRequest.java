package com.kerryprops.kip.bill.webservice.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.Set;

@Data
@Schema
public class BillInvoiceSendRequest {

    @Schema(title = "ID列表")
    @NotEmpty(message = "发票ids不能为空")
    private Set<Long> invoiceIds;

    @Schema(title = "楼盘ID")
    @NotBlank(message = "projectId不能为空")
    private String projectId;

}
