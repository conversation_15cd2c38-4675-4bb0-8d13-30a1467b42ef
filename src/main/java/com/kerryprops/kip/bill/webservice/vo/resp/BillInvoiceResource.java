package com.kerryprops.kip.bill.webservice.vo.resp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Set;

@Data
public class BillInvoiceResource {

    @Schema(title = "发票类型")
    public String invoiceType;

    @Schema(title = "发票类型名称")
    public String invoiceTypeName;

    @Schema(title = "发票状态")
    public String invoiceStatus;

    @Schema(title = "Bill invoice ID")
    private Long id;

    @Schema(title = "业务类型")
    private String bizType;

    @Schema(title = "购方公司")
    private String purchaserName;

    @Schema(title = "销方公司")
    private String sellerName;

    @Schema(title = "建筑物")
    private String mcu;

    @Schema(title = "楼栋ID")
    private String buildingId;

    @Schema(title = "楼栋名称")
    private String buildingName;

    @Schema(title = "发票日期")
    private String issuanceDate;

    @Schema(title = "发票号码")
    private String invoiceNo;

    @Schema(title = "发票金额")
    private BigDecimal invoiceAmt;

    @Schema(title = "合同号")
    private String doco;

    @Schema(title ="数电票-行业特殊票种-不动产租赁场景：租赁期起，格式：yyyyMMdd ")
    private String leaseTermStart;

    @Schema(title ="数电票-行业特殊票种-不动产租赁场景：租赁期止，格式：yyyyMMdd")
    private String leaseTermEnd;

    @Schema(title = "付款人an8")
    private String payerAn8;

    @Schema(title = "付款人alph")
    private String payerAlph;

    @Schema(title = "楼盘ID")
    private String projectId;

    @Schema(title = "收件人列表")
    private Set<BillInvoiceReceiverResource> userInfoList;

    @Schema(title = "发票pdf链接")
    private String invoiceUrl;

    @Schema(title = "发票xml链接")
    private String xmlUrl;

    @Schema(title = "发票ofd链接")
    private String ofdUrl;

    @Schema(title = "邮件发送状态")
    private SendStatusResource emailSendStatus;

    @Schema(title = "消息发送状态")
    private SendStatusResource messageSendStatus;

    @Schema(title = "短信发送状态")
    private SendStatusResource smsSendStatus;

    @Schema(title = "JDE单元号")
    private String jdeUnit;

    @JsonIgnore
    private String salesBillNo;

}
