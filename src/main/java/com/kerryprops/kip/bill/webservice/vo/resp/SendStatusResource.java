package com.kerryprops.kip.bill.webservice.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SendStatusResource {

    @Schema(title = "当前状态")
    private Integer status;

    @Schema(title = "发送历史, 降序排列")
    private Collection<SendStatusResourceSpec> sendSpecs;

}
