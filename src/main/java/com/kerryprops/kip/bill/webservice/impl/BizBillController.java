package com.kerryprops.kip.bill.webservice.impl;

import com.google.common.collect.Sets;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.IBillSendConfigService;
import com.kerryprops.kip.bill.service.IBillService;
import com.kerryprops.kip.bill.service.model.b.BizBillSearchBo;
import com.kerryprops.kip.bill.service.model.leg.Bill;
import com.kerryprops.kip.bill.service.model.leg.BillUserSelectVo;
import com.kerryprops.kip.bill.webservice.BizBillFacade;
import com.kerryprops.kip.bill.webservice.vo.req.BizBillReadReqVo;
import com.kerryprops.kip.bill.webservice.vo.req.BizBillSearchReqVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillPayer;
import com.kerryprops.kip.bill.webservice.vo.resp.BillUnreadInfoVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BizBillDetailRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BizBillRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.ContentUnreadInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * can do any thing under doco + an8
 */
@Slf4j
@RestController
public class BizBillController implements BizBillFacade {

    @Autowired
    private IBillService billService;

    @Autowired
    private IBillSendConfigService billSendConfigService;

    @Override
    public RespWrapVo<List<String>> queryBillPayers() {
        /*LoginUser loginUser = UserInfoUtils.getUser();
        String loginAccount = loginUser.getLoginAccount();
        List<BillPayer> billPayers = billSendConfigService.queryBillPayers(loginAccount);
        List<String> payers = billPayers.stream().filter(e -> StringUtils.isNotBlank(e.getTpAlph()))
                .map(billPayer -> {
            if (StringUtils.isBlank(billPayer.getTpAlph())) {
                return billPayer.getTpAn8();
            } else {
                return billPayer.getTpAlph() + "-" + billPayer.getTpAn8();
            }
        }).collect(Collectors.toList());
        RespWrapVo<List<String>> respWrapVo = new RespWrapVo<>(payers);
        return respWrapVo;*/
        Set<String> docos = null;
        try {
            docos = billService.getDocos();
        } catch (Exception e) {
            return new RespWrapVo<>(Collections.emptyList());
        }
        LoginUser loginUser = UserInfoUtils.getUser();
        String loginAccount = loginUser.getLoginAccount();
        List<BillPayer> billPayers = billSendConfigService.queryBillPayers(loginAccount);
        Set<String> an8s = billPayers.stream().map(BillPayer::getTpAn8).collect(Collectors.toSet());
        Set<BillPayer> payers = billService.selectBillTenants(docos, an8s);
        return new RespWrapVo<>(payers.stream().map(e -> e.getTpAlph() + "-" + e.getTpAn8()).collect(Collectors.toList()));
    }

    @Override
    public RespWrapVo<Page<BizBillRespVo>> userBillInfoList(@SortDefault.SortDefaults(value = {
            @SortDefault(sort = "tpCrtutime", direction = Sort.Direction.DESC)
            , @SortDefault(sort = "createTime", direction = Sort.Direction.DESC)
            , @SortDefault(sort = "updateTime", direction = Sort.Direction.DESC)}) Pageable pageable
            , @ModelAttribute BizBillSearchReqVo bizBillSearchReqVo) {
        List<String> inputAlphs = bizBillSearchReqVo.getTpAlphs();
        /*if (CollectionUtils.isNotEmpty(inputAn8s) && inputAn8s.size()
                > (CollectionUtils.isEmpty(inputAlphs) ? 0 : inputAlphs.size())) {
            if (Objects.isNull(inputAlphs)) {
                inputAlphs = Lists.newArrayList();
            }
            inputAlphs.add(Strings.EMPTY);
        }*/
        List<String> inputAn8s = bizBillSearchReqVo.getTpAn8s();
        Set<BillPayer> billPayers = billService.getBillPayers();
//        Set<String> tenantAlphs = billPayers.stream().map(BillPayer::getTpAlph)
//                .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        Set<String> tenantAn8s = billPayers.stream().map(BillPayer::getTpAn8).collect(Collectors.toSet());
        BizBillSearchBo searchBo = BeanUtil.copy(bizBillSearchReqVo, BizBillSearchBo.class);
        searchBo.setDocos(billService.getDocos());
        Set<String> an8s = CollectionUtils.isEmpty(inputAn8s) ? tenantAn8s :
                tenantAn8s.stream().filter(inputAn8s::contains).collect(Collectors.toSet());
        searchBo.setAn8s(an8s);
        if (!CollectionUtils.isEmpty(inputAlphs)) {
            searchBo.setAlphs(Sets.newHashSet(inputAlphs));
        }
//        Set<String> alphs = CollectionUtils.isEmpty(inputAlphs) ? tenantAlphs :
//                tenantAlphs.stream().filter(inputAlphs::contains).collect(Collectors.toSet());
//        searchBo.setAlphs(alphs);

        Page<BillUserSelectVo> list = billService.searchPagedBill(pageable, searchBo.toPredicates());

        Collection<Long> billIds = list.stream().map(BillUserSelectVo::getId).collect(Collectors.toSet());
        Collection<Long> userReadBillIds = billService.getUserReadBillIds(billIds);
        Page<BizBillRespVo> billRespVoPage = list.map(e -> {
            BizBillRespVo bizBillRespVo = BeanUtil.copy(e, BizBillRespVo.class);
            bizBillRespVo.setSendDate(getSendDate(e.getMailDate(), e.getEmailDate()));
            bizBillRespVo.setReadStatus(userReadBillIds.contains(e.getId()) ?
                    BillReadStatus.READED.code : BillReadStatus.UNREAD.code);
            return bizBillRespVo;
        });

        return new RespWrapVo<>(billRespVoPage);
    }

    @Override
    public RespWrapVo<BizBillDetailRespVo> getInfo(@PathVariable("id") Long id) {
        Set<BillPayer> billPayers = billService.getBillPayers();
//        Set<String> alphs = billPayers.stream().map(BillPayer::getTpAlph).distinct().collect(Collectors.toSet());
        Set<String> an8s = billPayers.stream().map(BillPayer::getTpAn8).distinct().collect(Collectors.toSet());

        BizBillSearchBo searchBo = new BizBillSearchBo();
        searchBo.setId(id);
        searchBo.setDocos(billService.getDocos());
//        searchBo.setAlphs(alphs);
        searchBo.setAn8s(an8s);
        Bill bill = billService.selectBillVo(searchBo.toPredicates());
        if (bill == null) {
            return new RespWrapVo<>();
        }
        //更新已读
        if (bill.getReadStatus() == null) {
            updateBillReadStatus(BizBillReadReqVo.builder().id(bill.getId()).build());
        }

        BizBillDetailRespVo billDetailRespVo = BeanUtil.copy(bill, BizBillDetailRespVo.class);
        billDetailRespVo.setSendDate(getSendDate(bill.getMailDate(), bill.getEmailDate()));
        return new RespWrapVo<>(billDetailRespVo);
    }

    @Override
    public RespWrapVo<List<Integer>> selectBillDistinctYears() {
        List<Integer> allYears = billService.selectBillDistinctYears();
        allYears = allYears.stream().filter(e -> e != null).map(e -> 2000 + e).collect(Collectors.toList());
        return new RespWrapVo<>(allYears);
    }

    @Override
    public RespWrapVo<Integer> updateBillReadStatus(BizBillReadReqVo billReadReqVo) {
        Integer cnt = billService.updateBillReadStatus(billReadReqVo);
        return new RespWrapVo<>(cnt);
    }

    @Override
    public RespWrapVo<Integer> updateReadTime(@PathVariable("id") Long id) {
        return new RespWrapVo<>(billService.updateReadTime(id));
    }

    @Override
    public RespWrapVo<BillUnreadInfoVo> userHasUnreadBill() {
        Set<BillPayer> billPayers = billService.getBillPayers();
//        Set<String> alphs = billPayers.stream().map(BillPayer::getTpAlph).collect(Collectors.toSet());
        Set<String> an8s = billPayers.stream().map(BillPayer::getTpAn8).collect(Collectors.toSet());

        BizBillSearchBo searchBo = new BizBillSearchBo();
        searchBo.setDocos(billService.getDocos());
//        searchBo.setAlphs(alphs);
        searchBo.setAn8s(an8s);
        // 阅读状态 0 未读、1已读
        searchBo.setIsNotRead(true);
        long unreadBillCount = billService.countByPredicate(searchBo.toPredicates());
        BillUnreadInfoVo vo = new BillUnreadInfoVo();
        vo.setUnreadBillCount(unreadBillCount);
        vo.setHasUnreadBill(unreadBillCount > 0);
        return new RespWrapVo<>(vo);
    }

    @Override
    public ContentUnreadInfoVo userBillReadStatus() {
        BizBillSearchBo searchBo = getUserBizBillSearchBo();
        return billService.userBillReadStatus(searchBo.toPredicates());
    }

    @Override
    public Boolean setUserBillReaded(@PathVariable(name = "id") Long id) {
        return billService.setUserBillRead(id);
    }

    @Override
    public Boolean setUserBillAllReaded() {
        BizBillSearchBo searchBo = getUserBizBillSearchBo();
        return billService.setUserBillAllReaded(searchBo.toPredicates());
    }

    // 获取账单发送时间
    private Date getSendDate(Date mailDate, Date emailDate) {
        if (!Objects.isNull(mailDate) && !Objects.isNull(emailDate)) {
            return mailDate.compareTo(emailDate) < 0 ? mailDate : emailDate;
        } else if (Objects.isNull(mailDate) && !Objects.isNull(emailDate)) {
            return emailDate;
        } else if (!Objects.isNull(mailDate) && Objects.isNull(emailDate)) {
            return mailDate;
        }
        return null;
    }

    private BizBillSearchBo getUserBizBillSearchBo() {
        Set<BillPayer> billPayers = billService.getBillPayers();
        Set<String> an8s = billPayers.stream().map(BillPayer::getTpAn8).collect(Collectors.toSet());

        BizBillSearchBo searchBo = new BizBillSearchBo();
        searchBo.setDocos(billService.getDocos());
        searchBo.setAn8s(an8s);
        return searchBo;
    }

    private enum BillReadStatus {
        UNREAD(0),
        READED(1);

        private final int code;

        BillReadStatus(int code) {
            this.code = code;
        }
    }

}
