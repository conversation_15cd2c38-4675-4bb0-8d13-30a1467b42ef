package com.kerryprops.kip.bill.webservice.impl;

import com.google.common.collect.Lists;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.exceptions.RestInternalServiceException;
import com.kerryprops.kip.bill.common.exceptions.RestInvalidParamException;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.utils.DiffFieldUtils;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptJdeBillRepository;
import com.kerryprops.kip.bill.dao.AptPayBillRepository;
import com.kerryprops.kip.bill.dao.AptPayRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptBillOperator;
import com.kerryprops.kip.bill.dao.entity.AptJdeBill;
import com.kerryprops.kip.bill.dao.entity.QAptBill;
import com.kerryprops.kip.bill.dao.entity.QAptJdeBill;
import com.kerryprops.kip.bill.service.AptBillOperationService;
import com.kerryprops.kip.bill.service.StaffManualSyncAptBillService;
import com.kerryprops.kip.bill.webservice.vo.resp.OperationChangedFiledRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.SyncStaffAptBillRespVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.NOT_FOUND;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.SYNC_PAID_AND_NOT_JDE_VERIFIED_BILL_ERROR;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.SYNC_PAYING_BILL_ERROR;

@Tag(name = "物业缴费 - 账单手动同步优化")
@RestController
@Slf4j
@AllArgsConstructor
public class StaffManualSyncAptBillController {

    private final AptPayRepository aptPayRepository;

    private final AptBillRepository aptBillRepository;

    private final AptPayBillRepository payBillRepository;

    private final AptBillOperationService operationService;

    private final AptJdeBillRepository aptJdeBillRepository;

    private final StaffManualSyncAptBillService staffManualSyncAptBillService;

    @Operation(summary = "手动同步物业账单")
    @PostMapping(value = "s/apt/pay/bill/{billNo}/sync", produces = MediaType.APPLICATION_JSON_VALUE)
    public SyncStaffAptBillRespVo manualSync(@PathVariable("billNo") String billNo) {
        Optional<AptBill> billOp = aptBillRepository.findOne(QAptBill.aptBill.billNo.eq(billNo));
        if (billOp.isEmpty()) {
            throw new RestInvalidParamException(NOT_FOUND);
        }
        AptBill aptBill = billOp.get();
        // not allow sync the bill on PAYING status
        if (BillPaymentStatus.PAYING.equals(aptBill.getPaymentStatus())) {
            throw new RestInvalidParamException(SYNC_PAYING_BILL_ERROR);
        }

        if (List.of(BillPaymentStatus.PAID, BillPaymentStatus.DIRECT_DEBIT_PAID).contains(aptBill.getPaymentStatus())
                && !"JDE核销".equals(aptBill.getPaymentResult())) {
            throw new RestInvalidParamException(SYNC_PAID_AND_NOT_JDE_VERIFIED_BILL_ERROR);
        }

        SyncStaffAptBillRespVo previousVo = BeanUtil.copy(aptBill, SyncStaffAptBillRespVo.class);
        previousVo.setChangedFields(Collections.emptyList());
        // query apt_sync_bills records
        Iterable<AptJdeBill> jdeBillIterable = aptJdeBillRepository
                .findAll(QAptJdeBill.aptJdeBill.billNumber.eq(billNo));
        List<AptJdeBill> aptJdeBills = Lists.newLinkedList(jdeBillIterable);
        if (CollectionUtils.isEmpty(aptJdeBills)) {
            log.info("no target jde bills are found");
            return previousVo;
        }

        // do sync
        try {
            aptBill = staffManualSyncAptBillService.manualSync(aptBill, aptJdeBills);
        } catch (Exception e) {
            log.error("manual_sync_apt_bill error:", e);
            throw new RestInternalServiceException();
        }
        SyncStaffAptBillRespVo newVo = BeanUtil.copy(aptBill, SyncStaffAptBillRespVo.class);
        List<OperationChangedFiledRespVo> changedFields = DiffFieldUtils.diffFields(previousVo, newVo);
        operationService.saveOperationLog(aptBill, changedFields, AptBillOperator.MANUAL_SYNC, StringUtils.EMPTY);
        newVo.setChangedFields(changedFields);
        return newVo;
    }

}
