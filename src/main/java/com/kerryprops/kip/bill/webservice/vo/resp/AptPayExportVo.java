package com.kerryprops.kip.bill.webservice.vo.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 名   称：apt_offline_pay
 * 描   述：
 * 作   者：<PERSON>
 * 时   间：2021/08/31 15:38:36
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AptPayExportVo {

    @ExcelProperty("楼盘")
    @Schema(title = "楼盘")
    private String projectName;

    @ExcelProperty("楼栋")
    @Schema(title = "楼栋")
    private String buildingName;

    @ExcelProperty("户号")
    @Schema(title = "户号")
    private String roomNo;

    @ExcelProperty("收款号")
    @Schema(title = "收款号")
    private String payAct;

    @ExcelProperty("收款日期")
    @Schema(title = "收款日期")
    @DateTimeFormat("yyyy-MM-dd")
    private Date payDate;

    @ExcelProperty("描述")
    @Schema(title = "描述")
    private String comments;

    @ExcelProperty("收费类型")
    @Schema(title = "收费类型")
    private String payChannel;

    @ExcelProperty("支付方式")
    @Schema(title = "支付方式")
    private String payType;

    @ExcelProperty("实收金额")
    @Schema(title = "实收金额")
    private Double totalAmt;

    @ExcelProperty("手续费")
    @Schema(title = "手续费")
    private double taxAmt;

    @ExcelProperty("预收金额")
    @Schema(title = "预收金额")
    private BigDecimal advanceAmount;

    @ExcelProperty("预收科目")
    @Schema(title = "预收科目")
    private String paymentCate;


}
