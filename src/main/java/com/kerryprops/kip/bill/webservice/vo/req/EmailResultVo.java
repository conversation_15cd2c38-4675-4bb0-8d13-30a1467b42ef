package com.kerryprops.kip.bill.webservice.vo.req;

import com.kerryprops.kip.bill.common.enums.EmailStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/*****************************************************************************
 * Project - unified-messaging-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - <PERSON>
 * Created Date - 07/05/2021 15:34
 *****************************************************************************/

@Data
@Schema
public class EmailResultVo implements Serializable {

    @Schema(title = "发送邮件请求ID")
    @NotBlank
    private String requestId;

    @Schema(title = "发送邮件状态")
    private EmailStatus sendStatus;

    @Schema(title = "发送结果")
    private List<EmailStatusDto> results;

}
