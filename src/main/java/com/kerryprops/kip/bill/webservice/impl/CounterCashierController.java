package com.kerryprops.kip.bill.webservice.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kerryprops.kip.bill.service.CounterCashierService;
import com.kerryprops.kip.bill.webservice.vo.req.CashierAptPaySearchRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierOfflinePayRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierQRCodePaymentRequest;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierAptPaymentInfoResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierOfflinePayResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierPaymentReceiptResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierPaymentTransactionResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierQRCodePaymentResource;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import static com.kerryprops.kip.bill.common.utils.BillingFun.exceptionToNull;

/**
 * <AUTHOR> 2023-11-16 17:47:34
 **/
@Slf4j
@Tag(name = "S端-收银台")
@RestController
@AllArgsConstructor
public class CounterCashierController {

    private final ObjectMapper objectMapper;

    private final CounterCashierService counterCashierService;

    /**
     * 【收银台】-【账单收款】-【离线支付】
     */
    @Operation(summary = "离线支付-录入收款单",
            description = "消费者[业主]在线下通过刷卡、现金等方式支付后，物业前台收费人员在S端系统录入这笔账单信息")
    @PostMapping("/cashier/apt/bill/offline/pay")
    public CashierOfflinePayResource offlinePay4Cashier(@Valid @RequestBody CashierOfflinePayRequest request) {
        log.info("received_cashier_offline_pay_req: {}", exceptionToNull(() -> objectMapper.writeValueAsString(request)));
        return counterCashierService.offlinePay(request);
    }

    /**
     * 【收银台】-【账单收款】-【扫码支付】
     */
    @Operation(summary = "账单收款-扫码支付")
    @PostMapping("/cashier/apt/bill/qrcode/pay")
    public CashierQRCodePaymentResource counterCashierQRCodePayment(@Valid @RequestBody CashierQRCodePaymentRequest request) {
        log.info("received_cashier_qrcode_pay_req: {}", exceptionToNull(() -> objectMapper.writeValueAsString(request)));
        // conduct qrcode payment:
        return counterCashierService.counterCashierQRCodePayment(request);
    }

    /**
     * 物业前台扫码后查询交易状态，如果超时未支付，则调用支付供应商的取消支付接口，
     * 并将交易状态置为CANCEL状态。物业前台需要重新扫码收款
     */
    @Operation(summary = "收银台-查询支付详情", description = "物业前台扫码后查询交易状态，如果超时未支付，则调用支付供应商的取消支付接口" +
            "，并将交易状态置为CANCEL状态。物业前台需要重新扫码收款")
    @GetMapping("/cashier/apt/bill/payment/{paymentInfoId}/info")
    public CashierAptPaymentInfoResource queryPaymentInfo(@PathVariable("paymentInfoId") String paymentInfoId) {
        return counterCashierService.queryCashierPaymentInfo(paymentInfoId);
    }

    /**
     * KIP-12200
     * 【收银台】-【账单收款】-【收据】
     * 表头：楼栋对应的物业公司名称：从HIVE取值，如HIVE中未填写物业公司名称，则该行为空
     * 表体：楼盘名称， 楼栋/户号， 订单号，收款日期， 支付方式，账单明细，小计，预收金额
     */
    @Operation(summary = "缴费记录详情||收据", description = "【收银台】-【账单收款】-【收据】")
    @GetMapping("/cashier/apt/bill/payment/{paymentInfoId}/receipt")
    public CashierPaymentReceiptResource acquirePaymentReceipt(@PathVariable("paymentInfoId") String paymentInfoId) {
        return counterCashierService.acquirePaymentReceipt(paymentInfoId);
    }

    /**
     * 【收银台】-【账单收款】-【缴费记录】 KIP-12200
     * 此界面可查询所有收款（从收银台录入的收款、代扣、移动端完成的缴费）
     */
    @Operation(summary = "账单收款-缴费记录")
    @GetMapping(value = "/cashier/apt/bill/pays", produces = MediaType.APPLICATION_JSON_VALUE)
    public Page<CashierPaymentTransactionResource> queryCashierAptPays(@ModelAttribute CashierAptPaySearchRequest req
            , @SortDefault.SortDefaults(value = {
                    @SortDefault(sort = "createTime", direction = Sort.Direction.DESC),
                    @SortDefault(sort = "updateTime", direction = Sort.Direction.DESC)}) Pageable pageable) {

        return counterCashierService.queryCashierAptPays(req, pageable);
    }

    @Operation(summary = "账单收款-缴费记录-导出")
    @GetMapping(value = "/cashier/apt/bill/pays/export")
    public void exportCashierAptPays(@ModelAttribute CashierAptPaySearchRequest req, HttpServletResponse response) {
        counterCashierService.exportCashierAptPays(req, response);
    }

}
