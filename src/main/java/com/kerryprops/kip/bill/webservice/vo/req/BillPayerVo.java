package com.kerryprops.kip.bill.webservice.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BillPayerVo {

    @Schema(title = "用户名称")
//    @NotBlank(message = "alph can not be null")
    @Length(max = 255, message = "alph must be within 255 characters in length")
    private String alph;

    @Schema(title = "用户编号")
    @NotBlank(message = "an8 can not be null")
    @Length(max = 32, message = "an8 must be within 32 characters in length")
    private String an8;

    public String groupByKey() {
        StringBuffer sb = new StringBuffer();
        sb.append(an8).append("_").append(alph).append("_");
        return sb.toString();
    }

}
