package com.kerryprops.kip.bill.webservice.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class AptBillPaymentRequest {

    @NotBlank(message = "bills[*].billNo is required")
    @Schema(description = "账单号", required = true)
    private String billNo;

    @NotBlank(message = "bills[*].amount is required")
    @Schema(description = "账单金额", required = true)
    private BigDecimal amount;

}
