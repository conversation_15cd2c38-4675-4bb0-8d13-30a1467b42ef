package com.kerryprops.kip.bill.webservice.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.kerryprops.kip.bill.common.aop.RedisLock;
import com.kerryprops.kip.bill.common.enums.DirectDebitsBatchStatus;
import com.kerryprops.kip.bill.common.exceptions.RestInvalidParamException;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.dao.AptBillDirectDebitsBatchRepository;
import com.kerryprops.kip.bill.dao.entity.AptBillDirectDebitsBatch;
import com.kerryprops.kip.bill.dao.entity.AptDirectDebitsBatchBill;
import com.kerryprops.kip.bill.service.AptBillDirectDebitsBatchBillService;
import com.kerryprops.kip.bill.service.AptBillDirectDebitsBatchService;
import com.kerryprops.kip.bill.service.model.s.AptBillDirectDebitsBatchBo;
import com.kerryprops.kip.bill.service.model.s.AptBillSearchReqBo;
import com.kerryprops.kip.bill.webservice.vo.req.AptBillSearchReqVo;
import com.kerryprops.kip.bill.webservice.vo.req.DirectDebitsBatchesCreationRequest;
import com.kerryprops.kip.bill.webservice.vo.req.DirectDebitsBatchesQueryRequest;
import com.kerryprops.kip.bill.webservice.vo.req.DirectDebitsDeleteBatchBillsRequest;
import com.kerryprops.kip.bill.webservice.vo.resp.AptDirectDebitsBatchBillExportVo;
import com.kerryprops.kip.bill.webservice.vo.resp.DirectDebitsBatchBillAmountResource;
import com.kerryprops.kip.bill.webservice.vo.resp.DirectDebitsBatchBillDetailResource;
import com.kerryprops.kip.bill.webservice.vo.resp.DirectDebitsBillBatchQueryResource;
import com.querydsl.core.types.Predicate;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.BILL_CLOSING_MONTH_REQUIRED;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.DIRECT_DEBITS_BILL_IS_REQUIRED;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.EMPTY_CONTENT_EXPORT_EXCEL;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.PROJECT_ID_REQUIRED;
import static com.kerryprops.kip.bill.common.utils.BillingFun.exceptionToNull;
import static com.kerryprops.kip.bill.common.utils.BillingFun.exportByEasyExcel;
import static com.kerryprops.kip.bill.dao.entity.QAptBillDirectDebitsBatch.aptBillDirectDebitsBatch;
import static com.kerryprops.kip.bill.log4j.BSConversationFilter.setLoggingContext;

@Tag(name = "公寓小区-物业费代扣-批次 & 账单")
@Slf4j
@RestController
@AllArgsConstructor
public class DirectDebitsBillBatchController {

    private final ObjectMapper objectMapper;

    private final AptBillDirectDebitsBatchRepository batchRepository;

    private final AptBillDirectDebitsBatchBillService batchBillService;

    private final AptBillDirectDebitsBatchService directDebitsBatchService;

    @Operation(summary = "物业费代扣的批头列表")
    @GetMapping(value = "/s/apt/bill/direct_debits/batches", produces = MediaType.APPLICATION_JSON_VALUE)
    public Page<DirectDebitsBillBatchQueryResource> getDirectDebitsBatches(@SortDefault.SortDefaults(value = {
            @SortDefault(sort = "createdTime", direction = Sort.Direction.DESC)}) Pageable pageable
            , DirectDebitsBatchesQueryRequest request) {

        if (StringUtils.isEmpty(request.getProjectId())) {
            throw new RestInvalidParamException(PROJECT_ID_REQUIRED);
        }

        AptBillDirectDebitsBatchBo bo = BeanUtil.copy(request, AptBillDirectDebitsBatchBo.class);
        return directDebitsBatchService.queryDirectDebitsBatches(bo, pageable);
    }

    @Operation(summary = "创建物业费代扣的批头")
    @PostMapping(value = "/s/apt/bill/direct_debits/batches", produces = MediaType.APPLICATION_JSON_VALUE)
    public DirectDebitsBillBatchQueryResource createDirectDebitsBatch(
            @RequestBody DirectDebitsBatchesCreationRequest request) {
        log.info("received_create_bill_batch_req: {}"
                , exceptionToNull(() -> objectMapper.writeValueAsString(request)));
        if (StringUtils.isEmpty(request.getProjectId())) {
            throw new RestInvalidParamException(PROJECT_ID_REQUIRED);
        }
        if (StringUtils.isEmpty(request.getClosingMonth())) {
            throw new RestInvalidParamException(BILL_CLOSING_MONTH_REQUIRED);
        }
        directDebitsBatchService.validateDirectDebitsBatchCreation(request);
        return directDebitsBatchService.createDirectDebitsBatch(request);
    }

    @Operation(summary = "删除物业费代扣的批头")
    @DeleteMapping(value = "/s/apt/bill/direct_debits/batches/{batchNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public DirectDebitsBillBatchQueryResource deleteDirectDebitsBatches(@PathVariable("batchNo") String batchNo) {
        return directDebitsBatchService.deleteDirectDebitsBatch(batchNo);
    }

    /**
     * 1、在C端产生缴费订单，按楼栋+单元合并产生一个订单
     * 2、按订单汇总后的金额发送至支付宝后缴费
     * 3、代扣批头状态更新为“已发送”
     */
    @Operation(summary = "推送指定批次物业费代扣账单")
    @PostMapping(value = "/s/apt/bill/direct_debits/batches/{batchNo}/push", produces = MediaType.APPLICATION_JSON_VALUE)
    public DirectDebitsBillBatchQueryResource pushDirectDebitsBatchBills(@PathVariable("batchNo") String batchNo) {
        return directDebitsBatchService.pushDirectDebitsBatchBills(batchNo);
    }

    @Operation(summary = "获取批次的所有代扣账单列表")
    @GetMapping(value = "/s/apt/bill/direct_debits/batches/{batchNo}/bills", produces = MediaType.APPLICATION_JSON_VALUE)
    public Page<DirectDebitsBatchBillDetailResource> getBillsInBatch(@SortDefault.SortDefaults(value = {
            @SortDefault(sort = "beginDate", direction = Sort.Direction.ASC)
            , @SortDefault(sort = "endDate", direction = Sort.Direction.ASC)
            , @SortDefault(sort = "buildingId", direction = Sort.Direction.ASC)
            , @SortDefault(sort = "roomId", direction = Sort.Direction.ASC)}) Pageable pageable
            , @PathVariable("batchNo") String batchNo, AptBillSearchReqVo req) {
        AptBillSearchReqBo searchReqBo = BeanUtil.copy(req, AptBillSearchReqBo.class);
        return directDebitsBatchService.queryBillsInBatch(batchNo, pageable, searchReqBo);
    }

    @Operation(summary = "删除指定的物业费代扣账单")
    @DeleteMapping(value = "/s/apt/bill/direct_debits/batches/{batchNo}/bills", produces = MediaType.APPLICATION_JSON_VALUE)
    public boolean deleteBillsInBatch(@PathVariable("batchNo") String batchNo
            , @RequestBody DirectDebitsDeleteBatchBillsRequest request) {
        if (CollectionUtils.isEmpty(request.getBillNos())) {
            throw new RestInvalidParamException(DIRECT_DEBITS_BILL_IS_REQUIRED);
        }
        return directDebitsBatchService.deleteDirectDebitsBatchBills(batchNo, request.getBillNos());
    }

    @Operation(summary = "导出指定批次的代扣账单列表")
    @GetMapping(value = "/s/apt/bill/direct_debits/batches/{batchNo}/bills/export"
            , produces = MediaType.APPLICATION_JSON_VALUE)
    public void exportBillsInBatch(@PathVariable("batchNo") String batchNo, AptBillSearchReqVo req
            , HttpServletResponse response) {
        AptBillSearchReqBo searchReqBo = BeanUtil.copy(req, AptBillSearchReqBo.class);
        List<DirectDebitsBatchBillDetailResource> lst = directDebitsBatchService.queryBillsInBatch(batchNo, searchReqBo);
        if (CollectionUtils.isEmpty(lst)) {
            throw new RestInvalidParamException(EMPTY_CONTENT_EXPORT_EXCEL);
        }
        var resp = lst.stream().map(r -> {
            var tmp = BeanUtil.copy(r, AptDirectDebitsBatchBillExportVo.class);
            tmp.setPositionItem(r.getPositionItem());
            return tmp;
        }).collect(Collectors.toList());

        final String CONTENT_NAME = "apt_direct_debits_batch_bill";
        exportByEasyExcel(response, resp, AptDirectDebitsBatchBillExportVo.class
                , CONTENT_NAME, CONTENT_NAME);
    }

    @Operation(summary = "指定批次的代扣账单总金额")
    @GetMapping(value = "/s/apt/bill/direct_debits/batches/{batchNo}/bills/amount"
            , produces = MediaType.APPLICATION_JSON_VALUE)
    public DirectDebitsBatchBillAmountResource getBillAmount(@PathVariable("batchNo") String batchNo
            , AptBillSearchReqVo req) {
        AptBillSearchReqBo searchReqBo = BeanUtil.copy(req, AptBillSearchReqBo.class);
        return directDebitsBatchService.calculateBatchBillAmount(batchNo, searchReqBo);
    }

    @Hidden
    @RedisLock(key = "kip:billing:s:aptBillLapsedCheck", expire = 1L)
    @PostMapping(value = "/s/apt/bill/direct_debits/batches/lapsed_check")
    public List<DirectDebitsBillBatchQueryResource> lapsedCheck() {
        log.info("start direct_debits_batches_lapsed_check");
        return directDebitsBatchService.lapsedCheck();
    }

    @Hidden
    @PostMapping(value = "/s/apt/bill/direct_debits/batches/conduct_direct_payment")
    @SchedulerLock(name = "kip:billing:conductDirectPayment:task", lockAtLeastFor = "PT1M", lockAtMostFor = "PT2H")
    public void conductDirectPayment() {
        setLoggingContext();
        Predicate predicate = aptBillDirectDebitsBatch.isDel.eq(0)
                .and(aptBillDirectDebitsBatch.status.eq(DirectDebitsBatchStatus.SENT))
                .and(aptBillDirectDebitsBatch.billCount.ne(aptBillDirectDebitsBatch.billSentCount));
        List<AptBillDirectDebitsBatch> batches = Lists.newLinkedList(batchRepository.findAll(predicate));
        if (CollectionUtils.isEmpty(batches)) {
            log.info("apt_conductDirectPayment: no batch met condition");
            return;
        }
        for (AptBillDirectDebitsBatch batch : batches) {
            Map<String, List<AptDirectDebitsBatchBill>> paymentInfoIdAndBatchBillMap
                    = batchBillService.getRoomBillsOfBatch(batch);
            // handle sequentially by room (1 payment_info per room)
            for (Map.Entry<String, List<AptDirectDebitsBatchBill>> entry : paymentInfoIdAndBatchBillMap.entrySet()) {
                String paymentInfoId = entry.getKey();
                List<AptDirectDebitsBatchBill> roomBatchBills = entry.getValue();
                if (CollectionUtils.isNotEmpty(roomBatchBills)) {
                    batchBillService.conductRoomDirectPaymentHandler(paymentInfoId, roomBatchBills);
                }
            }
            batch.setBillSentCount(batch.getBillCount());
            batchRepository.save(batch);
        }
    }

}
