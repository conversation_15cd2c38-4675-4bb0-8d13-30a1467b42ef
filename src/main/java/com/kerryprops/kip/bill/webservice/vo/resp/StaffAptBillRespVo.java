package com.kerryprops.kip.bill.webservice.vo.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillPushStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 账单查询的 view
 */
@Data
@Schema
public class StaffAptBillRespVo {

    @Schema(title = "账单在KIP平台ID")
    private Long id;

    @Schema(title = "账单号")
    private String billNo;

    @Schema(title = "费项")
    private String category;

    @Schema(title = "票据码")
    private String rdGlc;

    @Schema(title = "JDE bu")
    private String bu;

    @Schema(title = "JDE单元号")
    private String unit;

    @Schema(title = "JDE地址号")
    private String an8;

    @Schema(title = "付款人")
    private String alph;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(title = "账单周期开始")
    private Date beginDate;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(title = "账单周期结束")
    private Date endDate;

    @Schema(title = "账单年")
    private Integer year;

    @Schema(title = "账单月")
    private Integer month;

    @Schema(title = "金额")
    private Double amt;

    @Schema(title = "系统状态")
    private BillStatus status;

    @Schema(title = "用户支付状态")
    private BillPaymentStatus paymentStatus;

    @Schema(title = "支付结果")
    private String paymentResult;

    @Schema(title = "账单推送状态")
    private BillPushStatus pushStatus;

    @Schema(title = "楼盘CODE")
    private String projectId;

    @Schema(title = "楼栋ID")
    private String buildingId;

    @Schema(title = "楼层ID")
    private String floorId;

    @Schema(title = "房间ID")
    private String roomId;

    @Schema(title = "地址明细")
    private PositionItemResponse positionItem;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(title = "创建时间")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(title = "更新时间")
    private Date updateTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @Schema(title = "支付时间")
    private Date payTime;

}
