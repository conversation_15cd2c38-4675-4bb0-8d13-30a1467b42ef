package com.kerryprops.kip.bill.webservice.vo.req;

import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 名   称：apt_pay_config
 * 描   述：
 * 作   者：David <PERSON>
 * 时   间：2021/08/30 15:03:37
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Data
@Schema
public class AptPayConfigSaveVo {

    @Schema(title = "楼盘Id")
    @NotNull
    private String projectId;

    @Schema(title = "付费渠道类型")
    @Enumerated(value = EnumType.STRING)
    @NotNull
    private BillPayChannel channel;

//	@Schema(title = "付费配置类型")
//	@Enumerated(value = EnumType.STRING)
//	@NotNull
//	private BillPayCategory category;

    @Schema(title = "付费方式")
    @NotNull
    private String paymentType;

    @Schema(title = "科目账")
    @NotNull
    private String paymentCate;

    @Schema(title = "明细账")
    @NotNull
    private String paymentDetail;

    @Schema(title = "总账银行账户")
    @NotNull
    private String bankAccount;

    @Schema(title = "银行科目MCU")
    @NotNull
    private String mcu;

    @Schema(title = "手续费率")
    @NotNull
    private Double tax;

    @Schema(title = "封顶")
    @NotNull
    private Double max;

    @Schema(title = "备注")
    private String comments;

    @Schema(title = "公司")
    @NotEmpty(message = "【付费配置·公司】为必填字段")
    private String companyCode;

}
