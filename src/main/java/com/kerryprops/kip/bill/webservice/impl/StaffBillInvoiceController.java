package com.kerryprops.kip.bill.webservice.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.kerryprops.kip.bill.common.enums.SendStatus;
import com.kerryprops.kip.bill.common.exceptions.AppException;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.utils.DataScopeUtils;
import com.kerryprops.kip.bill.dao.entity.EFapiaoBillInvoice;
import com.kerryprops.kip.bill.dao.entity.EFapiaoJDEBill;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.service.EFapiaoBillService;
import com.kerryprops.kip.bill.service.EFapiaoJdeBillService;
import com.kerryprops.kip.bill.service.EFapiaoSendService;
import com.kerryprops.kip.bill.service.KerryBillEFapiaoService;
import com.kerryprops.kip.bill.service.model.s.BillEFapiaoSearchReqBo;
import com.kerryprops.kip.bill.webservice.StaffBillInvoiceFacade;
import com.kerryprops.kip.bill.webservice.vo.req.BillInvoiceSearchRequest;
import com.kerryprops.kip.bill.webservice.vo.req.BillInvoiceSendRequest;
import com.kerryprops.kip.bill.webservice.vo.req.BillInvoiceUploadImportVo;
import com.kerryprops.kip.bill.webservice.vo.req.BillInvoiceUploadVo;
import com.kerryprops.kip.bill.webservice.vo.req.BizBillInvoiceSearchRequest;
import com.kerryprops.kip.bill.webservice.vo.req.EmailResultVo;
import com.kerryprops.kip.bill.webservice.vo.req.SmsResultCallbackVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillInvoiceResource;
import com.kerryprops.kip.bill.webservice.vo.resp.BillInvoiceUploadResultExportVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillInvoiceUploadResultRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillPayer;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffBillReceiverRespVo;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Stream;

import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.BAD_REQUEST;
import static com.kerryprops.kip.bill.common.utils.BillingFun.exceptionToNull;
import static com.kerryprops.kip.bill.common.utils.BillingFun.exportByEasyExcel;

@Slf4j
@AllArgsConstructor
@RestController
public class StaffBillInvoiceController implements StaffBillInvoiceFacade {

    private final ObjectMapper objectMapper;

    private final HiveAsClient hiveAsClient;

    private final EFapiaoSendService eFapiaoSendService;

    private final KerryBillEFapiaoService kerryBillEFapiaoService;

    private final EFapiaoJdeBillService eFapiaoJdeBillService;

    private final EFapiaoBillService eFapiaoBillService;

    @Override
    public Page<BillInvoiceResource> billInvoiceList(Pageable pageable, BillInvoiceSearchRequest request) {
        BillEFapiaoSearchReqBo reqBo = BeanUtil.copy(request, BillEFapiaoSearchReqBo.class);
        reqBo.setInvoiceState(request.getInvoiceStatus());
        return kerryBillEFapiaoService.eFapiaoList(reqBo, pageable);
    }

    @Override
    public List<BillPayer> fuzzyQueryPurchaserNames(String projectId, String query) {
        List<String> bus = DataScopeUtils.getBusByProjectId(hiveAsClient, projectId);
        return kerryBillEFapiaoService.fuzzyQueryPurchaserNames(bus, query);
    }

    @Override
    public Integer sendBillInvoiceSms(@Valid BillInvoiceSendRequest request) {
        String projectId = request.getProjectId();
        Set<Long> billFapiaoIds = request.getInvoiceIds();
        Map<StaffBillReceiverRespVo, Set<EFapiaoBillInvoice>> receiverInvoiceMap = eFapiaoSendService
                .invoiceReceivers(projectId, billFapiaoIds);
        Map<Long, String> batchNoMap = Maps.newLinkedHashMap();
        receiverInvoiceMap.values().stream().flatMap(Collection::stream).forEach(bi -> batchNoMap.put(bi.getId()
                , String.valueOf(System.currentTimeMillis())));
        for (Map.Entry<StaffBillReceiverRespVo, Set<EFapiaoBillInvoice>> entry : receiverInvoiceMap.entrySet()) {
            StaffBillReceiverRespVo receiver = entry.getKey();
            if (StringUtils.isBlank(receiver.getPhoneNumber())) {
                continue;
            }
            eFapiaoSendService.notifyBySms(projectId, receiver, entry.getValue(), batchNoMap);
        }
        return receiverInvoiceMap.size();
    }

    @Override
    public Integer sendBillInvoiceEmail(@Valid BillInvoiceSendRequest request) {
        String projectId = request.getProjectId();
        Set<Long> billFapiaoIds = request.getInvoiceIds();
        Map<StaffBillReceiverRespVo, Set<EFapiaoBillInvoice>> receiverInvoiceMap = eFapiaoSendService
                .invoiceReceivers(projectId, billFapiaoIds);
        Map<Long, String> batchNoMap = Maps.newLinkedHashMap();
        receiverInvoiceMap.values().stream().flatMap(Collection::stream).forEach(bi -> batchNoMap.put(bi.getId()
                , String.valueOf(System.currentTimeMillis())));
        for (Map.Entry<StaffBillReceiverRespVo, Set<EFapiaoBillInvoice>> entry : receiverInvoiceMap.entrySet()) {
            StaffBillReceiverRespVo receiver = entry.getKey();
            if (StringUtils.isBlank(receiver.getEmail())) {
                continue;
            }
            Set<EFapiaoBillInvoice> billInvoices = entry.getValue();
            billInvoices.stream().filter(e -> Objects.nonNull(e.getAmountWithTax()))
                    .forEach(e -> e.setAmountWithTax(e.getAmountWithTax()
                            .setScale(2, RoundingMode.HALF_UP)));
            eFapiaoSendService.notifyByEmail(projectId, receiver, billInvoices, batchNoMap);
        }
        return receiverInvoiceMap.size();
    }

    /**
     * 发站内信，message_type为"BILL_INVOICE"
     * 如果选中的发票没有对应的接收人，此接口也要将{@link EFapiaoBillInvoice#getMessageSendStatus()}
     * 置为{@linkplain SendStatus#MSG_SUCCESS}，当用户创建了B端账号后，用户可以直接通过
     * 接口 {@linkplain BizBillInvoiceController#userInvoiceList(Pageable, BizBillInvoiceSearchRequest)}
     * 查看所有有权限的发票
     */
    @Override
    public Integer sendBillInvoiceMessage(@Valid BillInvoiceSendRequest request) {
        Set<Long> billFapiaoIds = request.getInvoiceIds();
        return eFapiaoSendService.sendInvoiceMessage(request.getProjectId(), billFapiaoIds);
    }

    @Override
    public List<BillInvoiceUploadImportVo> getImportBillInvoiceList(@RequestParam("file") MultipartFile file
            , HttpServletResponse response) {
        List<String> excelHeads = new ArrayList<>();
        List<BillInvoiceUploadImportVo> billUploadImportVos = new ArrayList<>();

        try {
            EasyExcel.read(file.getInputStream(), BillInvoiceUploadImportVo.class
                    , new ImportBillInvoiceExcelListener(billUploadImportVos, excelHeads))
                    .sheet(0).doRead();
        } catch (Exception e) {
            log.error("import_bill_invoice import_error:", e);
            throw new AppException(BAD_REQUEST.getCode(), "导入数据异常，请检查数据");
        }

        if (CollectionUtils.isEmpty(excelHeads)
                || 4 > Stream.of("单据公司", "单据类型", "单据号", "付款项")
                .filter(excelHeads::contains).count()) {
            final String FORMAT_ERROR_TIP = "上传文件格式错误";
            BillInvoiceUploadResultRespVo errorTip = new BillInvoiceUploadResultRespVo();
            errorTip.setInvoiceResult("失败");
            errorTip.setFailReason(FORMAT_ERROR_TIP);

            String FILE_NAME = "jde_upload_xforce_error_tip_" + System.currentTimeMillis();
            final String SHEET_NAME = "开票请求上传错误报告";
            exportByEasyExcel(response, List.of(errorTip)
                    , BillInvoiceUploadResultExportVo.class, FILE_NAME, SHEET_NAME);
            throw new AppException(BAD_REQUEST.getCode(), FORMAT_ERROR_TIP);
        }

        if (CollectionUtils.isEmpty(billUploadImportVos)) {
            throw new AppException(BAD_REQUEST.getCode(), "导入数据为空！");
        }

        return billUploadImportVos;
    }

    @Override
    public BillInvoiceUploadResultRespVo uploadBillInvoice(BillInvoiceUploadVo billInvoiceUploadVo) {
        final String EXPORT_RESULT_SUCCESS = "成功";
        final String EXPORT_RESULT_FAIL = "失败";

        String salesBillNo = StringUtils.EMPTY;
        BillInvoiceUploadResultRespVo uploadResultRespVo;
        try {
            salesBillNo = billInvoiceUploadVo.getKco() +
                    billInvoiceUploadVo.getBillType() +
                    billInvoiceUploadVo.getDoc() +
                    billInvoiceUploadVo.getPaymentItem();
            uploadResultRespVo = BeanUtil.copy(billInvoiceUploadVo, BillInvoiceUploadResultRespVo.class);
        } catch (Exception e) {
            log.error("import_bill_invoice generate_bill_info_error, bill_no:[{}]", salesBillNo, e);
            BillInvoiceUploadResultRespVo errorMsgVo = new BillInvoiceUploadResultRespVo();
            errorMsgVo.setInvoiceResult(EXPORT_RESULT_FAIL);
            errorMsgVo.setFailReason("读取业务单信息异常：" + e.getMessage());
            return errorMsgVo;
        }

        EFapiaoJDEBill eFapiaoJDEBill;
        try {
            eFapiaoJDEBill = eFapiaoJdeBillService.queryBySalesBillNo(salesBillNo);
        } catch (Exception e) {
            log.error("import_bill_invoice query_bill_error, bill_no:[{}]", salesBillNo, e);
            uploadResultRespVo.setInvoiceResult(EXPORT_RESULT_FAIL);
            uploadResultRespVo.setFailReason("查询业务单异常：" + e.getMessage());
            return uploadResultRespVo;
        }

        if (Objects.isNull(eFapiaoJDEBill)) {
            log.error("import_bill_invoice jde_bill_not_found sales_bill_no:[{}]", salesBillNo);
            uploadResultRespVo.setInvoiceResult(EXPORT_RESULT_FAIL);
            uploadResultRespVo.setFailReason("JDE中无此账单数据：" + salesBillNo);
            return uploadResultRespVo;
        }

        try {
            eFapiaoBillService.invoiceUpload(eFapiaoJDEBill); // 申请开票（导入场景）
        } catch (Exception e) {
            log.error("import_bill_invoice invoice_upload_error bill_no:[{}]", salesBillNo, e);
            uploadResultRespVo.setInvoiceResult(EXPORT_RESULT_FAIL);
            uploadResultRespVo.setFailReason("申请开票异常：" + e.getMessage());
            return uploadResultRespVo;
        }

        uploadResultRespVo.setInvoiceResult(EXPORT_RESULT_SUCCESS);
        return uploadResultRespVo;
    }

    @Override
    public void exportBillInvoiceResultList(List<BillInvoiceUploadResultExportVo> billInvoiceUploadResultVos
            , HttpServletResponse response) {
        String FILE_NAME = "jde_upload_xforce_result_list_" + System.currentTimeMillis();
        final String SHEET_NAME = "开票请求上传报告";
        exportByEasyExcel(response, billInvoiceUploadResultVos
                , BillInvoiceUploadResultExportVo.class, FILE_NAME, SHEET_NAME);
    }

    @Hidden
    @PostMapping("/bill/invoice/sms/callback")
    public String handleSmsCallback(@RequestBody SmsResultCallbackVo smsResultCallbackVo) {
        log.info("received_sms_callback_req: {}", exceptionToNull(() -> objectMapper.writeValueAsString(smsResultCallbackVo)));
        kerryBillEFapiaoService.handleSmsCallback(smsResultCallbackVo);
        return "accepted";
    }

    @Hidden
    @PostMapping("/bill/invoice/email/callback")
    public String handleEmailCallback(@RequestBody EmailResultVo emailResultVo) {
        log.info("received_email_callback_req: {}", exceptionToNull(() -> objectMapper.writeValueAsString(emailResultVo)));
        kerryBillEFapiaoService.handleEmailCallback(emailResultVo);
        return "accepted";
    }

    private static class ImportBillInvoiceExcelListener extends AnalysisEventListener<BillInvoiceUploadImportVo> {

        List<String> excelHeads;

        List<BillInvoiceUploadImportVo> billUploadImportVos;

        public ImportBillInvoiceExcelListener(List<BillInvoiceUploadImportVo> billUploadImportVos, List<String> excelHeads) {
            this.excelHeads = excelHeads;
            this.billUploadImportVos = billUploadImportVos;
        }

        @Override
        public void invoke(BillInvoiceUploadImportVo data, AnalysisContext context) {
            this.billUploadImportVos.add(data);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
        }

        @Override
        public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
            log.info("import_bill_invoice excel_head= [{}]", headMap);
            if (Objects.nonNull(headMap) && !headMap.isEmpty()) {
                this.excelHeads.addAll(headMap.values().stream().toList());
            }
        }

    }

}

