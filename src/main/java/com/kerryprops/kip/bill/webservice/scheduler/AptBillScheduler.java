package com.kerryprops.kip.bill.webservice.scheduler;

import com.kerryprops.kip.bill.log4j.BSConversationFilter;
import com.kerryprops.kip.bill.webservice.impl.DirectDebitsBillBatchController;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@AllArgsConstructor
@ConditionalOnProperty(value = "distributed.jobs.enabled", havingValue = "true")
public class AptBillScheduler {

    private DirectDebitsBillBatchController batchController;

    @Scheduled(cron = "0 0 1,3 * * ?")
    public void scheduleBBill() {
        BSConversationFilter.setLoggingContext();
        log.info("scheduled direct_debits_batches_lapsed_check");
        var result = batchController.lapsedCheck();
        log.info("scheduled direct_debits_batches_lapsed_check done, result: {}", result);
    }

}
