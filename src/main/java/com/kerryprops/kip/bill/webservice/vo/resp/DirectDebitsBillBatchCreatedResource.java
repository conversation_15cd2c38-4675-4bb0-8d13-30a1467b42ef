package com.kerryprops.kip.bill.webservice.vo.resp;

import com.kerryprops.kip.bill.common.enums.DirectDebitsBatchStatus;
import lombok.Data;

import java.time.ZonedDateTime;

@Data
public class DirectDebitsBillBatchCreatedResource {

    private Long id;

    private String createdBy;

    private String lastModifiedBy;

    private DirectDebitsBatchStatus status;

    private String pspName;

    private String projectId;

    private String projectName;

    private Integer billCount;

    private Integer paymentConfirmedCount;

    private Integer billSentCount;

    private String closingMonth;

    private ZonedDateTime createdTime;

    private ZonedDateTime updatedTime;

}
