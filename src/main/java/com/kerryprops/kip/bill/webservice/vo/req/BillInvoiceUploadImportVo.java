package com.kerryprops.kip.bill.webservice.vo.req;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 手动推送JDE业务单至票易通-导入Vo
 *
 * <AUTHOR>
 * @date 2024-7-26
 */
@Data
public class BillInvoiceUploadImportVo {

    @ExcelProperty("单据公司")
    @Schema(title = "单据公司", example = "31007")
    private String kco;

    @ExcelProperty("单据类型")
    @Schema(title = "单据类型", example = "RN")
    private String billType;

    @ExcelProperty("单据号")
    @Schema(title = "单据号", example = "19004780")
    private int doc;

    @ExcelProperty("付款项")
    @Schema(title = "付款项", example = "001")
    private String paymentItem;

}
