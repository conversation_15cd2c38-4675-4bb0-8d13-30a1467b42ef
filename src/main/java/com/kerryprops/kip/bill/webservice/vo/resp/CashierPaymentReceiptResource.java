package com.kerryprops.kip.bill.webservice.vo.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import com.kerryprops.kip.bill.common.enums.BillPayModule;
import com.kerryprops.kip.bill.common.enums.CashierPayStatus;
import com.kerryprops.kip.bill.common.enums.PaymentPayType;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Setter
@Getter
public class CashierPaymentReceiptResource {

    @Schema(title = "关联paymentInfo ID || 订单号")
    private String paymentInfoId;

    @Schema(title = "楼盘ID")
    private String projectId;

    @Schema(title = "楼栋ID")
    private String buildingId;

    @Schema(title = "楼层ID")
    private String floorId;

    @Schema(title = "单元ID")
    private String roomId;

    @Schema(title = "名称信息")
    private PositionItemResponse positionItem;

    @Schema(title = "收款号")
    private String payAct;

    @Schema(title = "收款日期[yyyy-MM-dd]")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private ZonedDateTime payDate;

    @Schema(title = "描述")
    private String comment;

    @Schema(title = "支付方式")
    private String payType;

    @Schema(title = "流水号")
    private String pspTransNo;

    @Schema(title = "实收金额")
    private BigDecimal totalAmt;

    @Schema(title = "账单金额")
    private BigDecimal billAmount;

    @Schema(title = "预收金额")
    private BigDecimal advanceAmt;

    @Schema(title = "预收科目")
    private String paymentCate;

    @Schema(title = "状态")
    private CashierPayStatus status;

    @Schema(title = "操作人")
    private String accountFromType;

    // alph
    @Schema(title = "付款人")
    private String payer;

    @Schema(title = "账单")
    private List<Bill> bills;

    @Schema(title = "开票金额")
    private BigDecimal canInvoiceBillAmount;

    @Schema(title = "预收账单能否开票标识")
    private String isAdvanceBilling = "0";

    @Schema(title = "支付来源的前端模块")
    private BillPayModule billPayModule;

    @Schema(title = "是否已复核")
    private Integer verifyStatus;

    @Schema(title = "是否已删除 0 没有 1 已删除")
    private Integer deletedAt;

    @Schema(title = "费项")
    private String feeName;

    public static CashierPaymentReceiptResource of(AptPaymentInfo paymentInfo, AptPay aptPay) {
        Date payDate = paymentInfo.getPaymentTime();
        ZonedDateTime payDate1 = null;
        if (Objects.nonNull(payDate)) {
            payDate1 = payDate.toInstant()
                              .atZone(ZoneId.systemDefault());
        }
        String payType = paymentInfo.getPayTypeInfo();
        if (StringUtils.isBlank(payType)) {
            payType = Optional.ofNullable(paymentInfo.getPayType())
                              .map(v -> v.getInfo())
                              .orElse(null);
        }

        CashierPaymentReceiptResource resource = new CashierPaymentReceiptResource();
        resource.setPaymentInfoId(paymentInfo.getId());
        resource.setProjectId(paymentInfo.getProjectId());
        resource.setBuildingId(paymentInfo.getBuildingId());
        resource.setFloorId(paymentInfo.getFloorId());
        resource.setRoomId(paymentInfo.getRoomId());
        resource.setPositionItem(paymentInfo.getPositionItem());
        resource.setPayAct(paymentInfo.getPayAct());
        resource.setComment(extractDescription(paymentInfo, aptPay));
        resource.setPspTransNo(paymentInfo.getPspTransNo());
        resource.setAdvanceAmt(paymentInfo.getAdvanceAmount());
        resource.setAccountFromType(paymentInfo.getCreateBy());
        resource.setBillPayModule(paymentInfo.getBillPayModule());
        resource.setDeletedAt(paymentInfo.getDeleted());

        resource.setPaymentCate(paymentInfo.getPaymentCate()
                                           .name());
        resource.setTotalAmt(BigDecimal.valueOf(paymentInfo.getAmt()));
        resource.setStatus(CashierPayStatus.of(paymentInfo, aptPay));
        resource.setPayDate(payDate1);
        resource.setPayType(payType);
        resource.setVerifyStatus(Optional.ofNullable(aptPay)
                                         .map(AptPay::getVerifyStatus)
                                         .map(v -> v.getCode())
                                         .orElse(null));
        resource.setFeeName(paymentInfo.getFeeName());
        return resource;
    }

    public String getPayType() {
        if (PaymentPayType.UNKNOWN.getInfo()
                                  .equals(payType)) {
            return null;
        }
        return payType;
    }

    private static String extractDescription(AptPaymentInfo aptPaymentInfo, AptPay aptPay) {
        if (Objects.isNull(aptPay)) {
            return aptPaymentInfo.getDescription();
        }
        var payChannel = aptPay.getPayChannel();
        if (BillPayChannel.DIRECT_DEBITS.equals(payChannel) || BillPayChannel.ONLINE.equals(payChannel)) {
            return aptPay.getPayDesc();
        }
        if (BillPayChannel.OFFLINE.equals(payChannel)) {
            return aptPay.getComments();
        }
        return null;
    }

    @Setter
    @Getter
    public static class Bill {

        @Schema(title = "账单id")
        private Long billId;

        @Schema(title = "账单号")
        private String billNo;

        @Schema(title = "费项名")
        private String category;

        @Schema(title = "账单开始时间 [yyyy-MM-dd'T'HH:mm:ss.SSSXXX]")
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX", timezone = "GMT+8")
        private ZonedDateTime startTime;

        @Schema(title = "账单结束时间 [yyyy-MM-dd'T'HH:mm:ss.SSSXXX]")
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX", timezone = "GMT+8")
        private ZonedDateTime endTime;

        @Schema(title = "发票金额")
        private BigDecimal amt;

        @Schema(title = "能否开票标识[1:可开票|其他:不可开票]")
        private String isBilling = "0";

        public static Bill of(AptBill aptBill) {
            Bill bill = new Bill();
            bill.setBillId(aptBill.getId());
            bill.setBillNo(aptBill.getBillNo());
            bill.setCategory(aptBill.getCategory());
            bill.setStartTime(aptBill.getBeginDate()
                                     .toInstant()
                                     .atZone(ZoneId.systemDefault()));
            bill.setEndTime(aptBill.getEndDate()
                                   .toInstant()
                                   .atZone(ZoneId.systemDefault()));
            bill.setAmt(BigDecimal.valueOf(aptBill.getAmt()));
            return bill;
        }

    }

}
