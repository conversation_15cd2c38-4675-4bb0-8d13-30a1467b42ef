package com.kerryprops.kip.bill.webservice.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/***********************************************************************************************************************
 * Project - user-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/06/2021 16:22
 **********************************************************************************************************************/

@Getter
@Setter
@Schema
@NoArgsConstructor
@AllArgsConstructor
public class TaxClassifyCodeResponse implements Serializable {

    @Schema(title = "主键id")
    private Long id;

    @Schema(title = "收费类型")
    private String feeType;

    @Schema(title = "收费类型名称")
    private String feeTypeName;

    @Schema(title = "公司co")
    private String companyCode;

    @Schema(title = "税收分类编码")
    private String classifyCode;

    @Schema(title = "开票服务名称")
    private String serviceName;

    @Schema(title = "税率")
    private BigDecimal rate;

    @Schema(title = "业务单类型")
    private String billType;

    @Schema(title = "创建时间")
    private Date createTime;

    /**
     * 是否使用区块链电子发票
     */
    private Integer blockChain;

}
