package com.kerryprops.kip.bill.webservice.vo.resp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.kerryprops.kip.bill.config.ZonedDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.ZonedDateTime;

@Data
public class SendStatusResourceSpec {

    @Schema(title = "用户发送时间")
    @JsonSerialize(using = ZonedDateTimeSerializer.class)
    private ZonedDateTime sendTime;

    @Schema(title = "消息送达时间")
    @JsonSerialize(using = ZonedDateTimeSerializer.class)
    private ZonedDateTime deliveryTime;

    @Schema(title = "发送状态")
    private Integer sendStatus;

    @Schema(title = "发送ID")
    private String requestId;

    @Schema(title = "发送批次号")
    private String batchNo;

    @Schema(title = "邮箱地址")
    private String email;

    @Schema(title = "sms")
    private String sms;

    @Schema(title = "用户ID")
    private String userId;

    @Schema(title = "用户名")
    private String userName;

    @Schema(title = "发送情况")
    private String sendDesc;

}
