package com.kerryprops.kip.bill.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.ZonedDateTime;

import static com.kerryprops.kip.bill.common.utils.BillingFun.zonedDatetimeStrFun;

@Component
public class ZonedDateTimeSerializer extends StdSerializer<ZonedDateTime> {


    public ZonedDateTimeSerializer() {
        this(null);
    }

    protected ZonedDateTimeSerializer(Class<ZonedDateTime> t) {
        super(t);
    }

    @Override
    public void serialize(ZonedDateTime value, JsonGenerator jgen, SerializerProvider provider) throws IOException {
        if (value != null) {
            // eg. 2020-04-04T23:59:00+08:00
            jgen.writeString(zonedDatetimeStrFun.apply(value));
        }
    }

}
