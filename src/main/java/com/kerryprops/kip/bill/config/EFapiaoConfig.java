package com.kerryprops.kip.bill.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * e-fapiao Config类，存储公司code-MCU，有效期日期映射
 *
 * <AUTHOR>
 * @Date 2023-3-18
 */
@Data
@Component
@ConfigurationProperties(prefix = "e-fapiao")
public class EFapiaoConfig {

    private List<Item> coValidBuPeriod;

    private String blanks;

    @Data
    public static class Item {

        private String companyCode;

        private String bus;

        private String startDate;

    }

}



