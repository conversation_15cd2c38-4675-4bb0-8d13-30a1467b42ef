package com.kerryprops.kip.bill.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/***
 * 2020/9/27
 * 联客移动端小程序相关的配置信息
 */
@Component
@ConfigurationProperties(prefix = "miniprogram")
public class KerryMIniProgramConfig {

    /**
     * 基础url地址
     */
    private static String mpUrl;

    /**
     * 发送消息
     */
    private static String mpMessageSend;

    /**
     * 消息更新
     */
    private static String mpMessageUpdate;

    /**
     * 联客用户与小程序解绑
     */
    private static String mpRemoveUser;

    /**
     * 获取快速注册的二维码接口
     */
    private static String mpGetRegisterQrcode;

    /**
     * 调用小程序端 设置签名需要的信息
     */
    private static String mpApiSign;

    /**
     * 发送账单信息
     */
    private static String mpMessageSendFinanceMessage;

    /**
     * 临时访客信息更新
     */
    private static String mpTemporaryCustomUpdate;

    /**
     * 报修状态更新
     */
    private static String mpWarrantyInfoUpdate;

    /**
     * 访客邀请更新
     */
    private static String mpInviteCustomUpdate;

    /**
     * 获取大楼的小程序二维码
     */
    private static String mpQrGetnew;

    /**
     * 获取账号绑定所需的二维码
     */
    private static String mpQrGetAccountBindQr;

    /**
     * 获取场地/设备二维码
     */
    private static String siteEquipmentQrcode;

    /**
     * 发送消息
     */
    public static String getMpMessageSend() {
        return mpUrl + mpMessageSend;
    }

    public void setMpMessageSend(String mpMessageSend) {
        KerryMIniProgramConfig.mpMessageSend = mpMessageSend;
    }

    /**
     * 消息更新
     */
    public static String getMpMessageUpdate() {
        return mpUrl + mpMessageUpdate;
    }

    public void setMpMessageUpdate(String mpMessageUpdate) {
        KerryMIniProgramConfig.mpMessageUpdate = mpMessageUpdate;
    }

    /**
     * 联客用户与小程序解绑
     */
    public static String getMpRemoveUser() {
        return mpUrl + mpRemoveUser;
    }

    public void setMpRemoveUser(String mpRemoveUser) {
        KerryMIniProgramConfig.mpRemoveUser = mpRemoveUser;
    }

    /**
     * 获取快速注册的二维码接口
     */
    public static String getMpGetRegisterQrcode() {
        return mpUrl + mpGetRegisterQrcode;
    }

    public void setMpGetRegisterQrcode(String mpGetRegisterQrcode) {
        KerryMIniProgramConfig.mpGetRegisterQrcode = mpGetRegisterQrcode;
    }

    /**
     * 调用小程序端 设置签名需要的信息
     */
    public static String getMpApiSign() {
        return mpApiSign;
    }

    public void setMpApiSign(String mpApiSign) {
        KerryMIniProgramConfig.mpApiSign = mpApiSign;
    }

    public static String getMpUrl() {
        return mpUrl;
    }

    public void setMpUrl(String mpUrl) {
        KerryMIniProgramConfig.mpUrl = mpUrl;
    }

    /**
     * 发送账单信息
     */
    public static String getMpMessageSendFinanceMessage() {
        return mpUrl + mpMessageSendFinanceMessage;
    }

    public void setMpMessageSendFinanceMessage(String mpMessageSendFinanceMessage) {
        KerryMIniProgramConfig.mpMessageSendFinanceMessage = mpMessageSendFinanceMessage;
    }

    /**
     * 临时访客信息更新
     */
    public static String getMpTemporaryCustomUpdate() {
        return mpUrl + mpTemporaryCustomUpdate;
    }

    public void setMpTemporaryCustomUpdate(String mpTemporaryCustomUpdate) {
        KerryMIniProgramConfig.mpTemporaryCustomUpdate = mpTemporaryCustomUpdate;
    }

    /**
     * 报修状态更新
     */
    public static String getMpWarrantyInfoUpdate() {
        return mpUrl + mpWarrantyInfoUpdate;
    }

    public void setMpWarrantyInfoUpdate(String mpWarrantyInfoUpdate) {
        KerryMIniProgramConfig.mpWarrantyInfoUpdate = mpWarrantyInfoUpdate;
    }

    /**
     * 访客邀请更新
     */
    public static String getMpInviteCustomUpdate() {
        return mpUrl + mpInviteCustomUpdate;
    }

    public void setMpInviteCustomUpdate(String mpInviteCustomUpdate) {
        KerryMIniProgramConfig.mpInviteCustomUpdate = mpInviteCustomUpdate;
    }

    /**
     * 获取大楼的小程序二维码
     */
    public static String getMpQrGetnew() {
        return mpUrl + mpQrGetnew;
    }

    public void setMpQrGetnew(String mpQrGetnew) {
        KerryMIniProgramConfig.mpQrGetnew = mpQrGetnew;
    }

    /**
     * 获取账号绑定所需的二维码
     */
    public static String getMpQrGetAccountBindQr() {
        return mpUrl + mpQrGetAccountBindQr;
    }

    public void setMpQrGetAccountBindQr(String mpQrGetAccountBindQr) {
        KerryMIniProgramConfig.mpQrGetAccountBindQr = mpQrGetAccountBindQr;
    }

    /**
     * 获取场地/设备二维码
     *
     * @return
     */
    public static String getSiteEquipmentQrcode() {
        return mpUrl + siteEquipmentQrcode;
    }

    public void setSiteEquipmentQrcode(String siteEquipmentQrcode) {
        KerryMIniProgramConfig.siteEquipmentQrcode = siteEquipmentQrcode;
    }


}
