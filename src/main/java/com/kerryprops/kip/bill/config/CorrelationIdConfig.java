package com.kerryprops.kip.bill.config;

import org.apache.logging.log4j.ThreadContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.zalando.logbook.CorrelationId;

import static com.kerryprops.kip.bill.log4j.BSConversationFilter.MDC_CORRELATION_ID_KEY;

/**
 * CorrelationIdConfig
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> <PERSON><PERSON>Zhang
 * Created Date - 2024/9/20 下午1:40
 */
@Configuration(proxyBeanMethods = false)
public class CorrelationIdConfig {

    @Bean
    public CorrelationId correlationId() {
        return r -> ThreadContext.get(MDC_CORRELATION_ID_KEY);
    }

}