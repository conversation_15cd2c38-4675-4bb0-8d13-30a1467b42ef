package com.kerryprops.kip.bill.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataMigrationConfig {

    @Value("${dm.projects}")
    private String projects;

    @Value("${dm.cBillProjects}")
    private String cBillProjects;

    @Value("${dm.kl.url}")
    private String url;

    @Value("${dm.kl.username}")
    private String userName;

    @Value("${dm.kl.password}")
    private String password;

    @Value("${dm.wx.msgUrl}")
    private String wxMsgUrl;

    @Value("${dm.wx.paidMsgUrl}")
    private String wxPaymentConfirmedMsgUrl;

    @Value("${dm.wx.payFailedMsgUrl}")
    private String payFailedMsgUrl;

}
