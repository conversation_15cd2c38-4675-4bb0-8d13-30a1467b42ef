package com.kerryprops.kip.bill.config;

import com.google.common.collect.Lists;
import com.kerryprops.kip.bill.common.current.TokenArgumentResolver;
import com.kerryprops.kip.bill.interceptors.BizAuthInterceptor;
import com.kerryprops.kip.bill.interceptors.UserInfoInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.web.PageableHandlerMethodArgumentResolver;
import org.springframework.format.FormatterRegistry;
import org.springframework.format.datetime.standard.DateTimeFormatterRegistrar;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.List;

import static java.time.format.DateTimeFormatter.ISO_LOCAL_DATE;
import static java.time.temporal.ChronoField.HOUR_OF_DAY;
import static java.time.temporal.ChronoField.MINUTE_OF_HOUR;
import static java.time.temporal.ChronoField.SECOND_OF_MINUTE;

@Configuration
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
public class DefaultMvcConfiguration implements WebMvcConfigurer {

    public static final DateTimeFormatter CUSTOM_ISO_LOCAL_DATE_TIME;

    public static final DateTimeFormatter CUSTOM_ISO_LOCAL_TIME;

    // 每页最大数量
    private static final int MAX_PAGE_SIZE = 500;

    static {
        CUSTOM_ISO_LOCAL_DATE_TIME = new DateTimeFormatterBuilder().parseCaseInsensitive()
                .append(ISO_LOCAL_DATE).appendLiteral(' ').appendValue(HOUR_OF_DAY, 2)
                .appendLiteral(':').appendValue(MINUTE_OF_HOUR, 2).optionalStart()
                .appendLiteral(':').appendValue(SECOND_OF_MINUTE, 2).toFormatter();

        CUSTOM_ISO_LOCAL_TIME = new DateTimeFormatterBuilder().parseCaseInsensitive()
                .appendValue(HOUR_OF_DAY, 2)
                .appendLiteral(":").appendValue(MINUTE_OF_HOUR, 2).optionalStart()
                .appendLiteral(":").appendValue(SECOND_OF_MINUTE, 2).toFormatter();
    }

    @Autowired
    private UserInfoInterceptor userInfoInterceptor;

    @Autowired
    private TokenArgumentResolver tokenArgumentResolver;

    @Autowired
    private BizAuthInterceptor bizAuthInterceptor;

    @Override
    public void addFormatters(FormatterRegistry registry) {
        DateTimeFormatterRegistrar registrar = new DateTimeFormatterRegistrar();
        registrar.setDateTimeFormatter(CUSTOM_ISO_LOCAL_DATE_TIME);
        registrar.setDateFormatter(ISO_LOCAL_DATE);
        registrar.setTimeFormatter(CUSTOM_ISO_LOCAL_TIME);
        registrar.registerFormatters(registry);
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        List<String> commonExcludePath = Lists.newArrayList(
                "/actuator/**",
                "/v2/api-docs**",
                "/swagger-resources/**",
                "/webjars/**",
                "/swagger-ui.html/**",
                "/error");

        registry.addInterceptor(userInfoInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(commonExcludePath);

        registry.addInterceptor(bizAuthInterceptor)
                .addPathPatterns("/b/bill/**")
                .excludePathPatterns(commonExcludePath);
    }


    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(tokenArgumentResolver);

        PageableHandlerMethodArgumentResolver pageableResolver = new PageableHandlerMethodArgumentResolver();
        pageableResolver.setMaxPageSize(MAX_PAGE_SIZE);
        resolvers.add(pageableResolver);
    }

}
