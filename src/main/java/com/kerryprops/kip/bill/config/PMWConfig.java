package com.kerryprops.kip.bill.config;

import com.kerryprops.kip.pmw.client.config.PaymentConfigBean;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

@Data
@Slf4j
@Configuration
@ComponentScan(basePackages = "com.kerryprops.kip.pmw.client")
public class PMWConfig {

    @Bean
    public PaymentConfigBean paymentConfigBean(PaymentConfigProps props) {
        PaymentConfigBean configBean = new PaymentConfigBean();
        configBean.setConnectionTimeout(props.getConnectionTimeout());
        configBean.setPmwHost(props.getHost());
        configBean.setKeystorePath(props.getKeystorePath());
        configBean.setKeystorePassword(props.getKeystorePassword());
        configBean.setAliasName(props.getAliasName());
        configBean.setKeyPassword(props.getKeyPassword());
        configBean.setClientPublicKey(props.getPmwPublicKey());
        return configBean;
    }

}
