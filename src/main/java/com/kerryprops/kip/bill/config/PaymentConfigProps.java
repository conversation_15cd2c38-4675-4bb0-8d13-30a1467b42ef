package com.kerryprops.kip.bill.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "kip.payment")
public class PaymentConfigProps {

    private String keystorePath;

    private String keystorePassword;

    private String keyPassword;

    private String aliasName;

    private String pmwPublicKey;

    private String host;

    private String connectionTimeout;

    private String ttl;

    private String notifyUrl;

    private double directDebitsAmountLimit = 0.3;

    private String directDebitsPayTtl = "30";

    private boolean directDebitsDailyUniqBatch = true;

    // QR Code scan limit
    private int userQRCodePaymentTtlSeconds = 60;

}
