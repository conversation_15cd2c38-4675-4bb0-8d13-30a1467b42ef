package com.kerryprops.kip.bill.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 读取项目相关配置
 *
 * <AUTHOR>
@Component
@ConfigurationProperties(prefix = "kerry")
public class KerryConfig {

    /**
     * 上传路径
     */
    private static String profile;

    /**
     * 获取地址开关
     */
    private static boolean addressEnabled;

    /**
     * 进行发送账号密码的方式 0 按照已创建就进行发送，1按照task任务进行发送
     */
    private static String userNameEmailSendMethod;

    /**
     * 要重定向的项目根路径
     */
    private static String projectRootUrl;

    /**
     * 关于空气质量需要进行重定向的 链接地址
     */
    private static String airReturnUrl;

    /**
     * 当前环境项目求情Url
     */
    private static String projectUrl;

    /**
     * 项目名称
     */
    private String name;

    /**
     * 版本
     */
    private String version;

    /**
     * 版权年份
     */
    private String copyrightYear;

    /**
     * 实例演示开关
     */
    private boolean demoEnabled;

    public static String getProjectUrl() {
        return projectUrl;
    }

    public void setProjectUrl(String projectUrl) {
        KerryConfig.projectUrl = projectUrl;
    }

    public static String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        KerryConfig.profile = profile;
    }

    public static boolean isAddressEnabled() {
        return addressEnabled;
    }

    public void setAddressEnabled(boolean addressEnabled) {
        KerryConfig.addressEnabled = addressEnabled;
    }

    /**
     * 获取头像上传路径
     */
    public static String getAvatarPath() {
        return getProfile() + "/avatar";
    }

    /**
     * 获取下载路径
     */
    public static String getDownloadPath() {
        return getProfile() + "/download/";
    }

    /**
     * 获取上传路径
     */
    public static String getUploadPath() {
        return getProfile() + "/upload";
    }

    public static String getUserNameEmailSendMethod() {
        return userNameEmailSendMethod;
    }

    public void setUserNameEmailSendMethod(String userNameEmailSendMethod) {
        KerryConfig.userNameEmailSendMethod = userNameEmailSendMethod;
    }

    public static String getProjectRootUrl() {
        return projectRootUrl;
    }

    public void setProjectRootUrl(String projectRootUrl) {
        KerryConfig.projectRootUrl = projectRootUrl;
    }

    public static String getAirReturnUrl() {
        return airReturnUrl;
    }

    public void setAirReturnUrl(String airReturnUrl) {
        KerryConfig.airReturnUrl = airReturnUrl;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getCopyrightYear() {
        return copyrightYear;
    }

    public void setCopyrightYear(String copyrightYear) {
        this.copyrightYear = copyrightYear;
    }

    public boolean isDemoEnabled() {
        return demoEnabled;
    }

    public void setDemoEnabled(boolean demoEnabled) {
        this.demoEnabled = demoEnabled;
    }

}
