package com.kerryprops.kip.bill.config;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/***********************************************************************************************************************
 * Project - repair-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 08/30/2021 16:34
 **********************************************************************************************************************/

@Get<PERSON>
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Component
@ConfigurationProperties(prefix = "kip.invoice.msg-callback")
public class MsgCallbackProperties {

    private String sms;

    private String email;

}
