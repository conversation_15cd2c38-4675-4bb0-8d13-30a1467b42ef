package com.kerryprops.kip.bill.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;

/***
 * 同步jde读取数据库的配置信息
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "jde")
public class SyncJdeConfig {

    public static String viewName;

    public static String dataBase;

    /**
     * 获取用户地址号的表名
     */
    public static String an8Table;

    /**
     * 电子账单获取的表名
     */
    public static String billTable;

    /**
     * jde场地视图
     */
    public static String siteView;

    /**
     * jde同步公共报修 派单接口
     */
    public static String publicDispatch;

    /**
     * jde同步公司报修 派单接口
     */
    public static String companyDispatch;

    /**
     * 同步jde评价的表名
     */
    public static String F00022;

    /**
     * 同步jde评价的表名
     */
    public static String F00092;

    /**
     * jde数据库名
     */
    public static String jdeDbPrefix;

    /**
     * jde工单费用表
     */
    public static String jdeRepairBillingFee;

    /**
     * JDE 的故障类型 表
     */
    public static String vwoFailtype;

    /**
     * 同步jeb设备表名
     */
    public static String F1201;

    /**
     * 同步jeb设备表名
     */
    public static String F1217;

    public static String billPay;

    /**
     * e-fapiao：jde源数据库名
     */
    private static String jdeBillInvoiceOrigin;

    /**
     * e-fapiao：jde已读标记数据库名，记录已完成开票请求请教，但是不一定开票完成的数据
     */
    private static String jdeBillInvoiceRead;

    /**
     * e-fapiao：jde回写数据库名，记录开票完成的数据
     */
    private static String jdeBillInvoiceWriteBack;

    // JDE杂费信息相关表
    private static String jdeCashierFee;

    public static String getBillPay() {
        return billPay;
    }

    public static void setBillPay(String billPay) {
        SyncJdeConfig.billPay = billPay;
    }

    public static String getF1201() {
        return F1201;
    }

    public void setF1201(String f1201) {
        F1201 = f1201;
    }

    public static String getF1217() {
        return F1217;
    }

    public void setF1217(String f1217) {
        F1217 = f1217;
    }


    public static String getJdeBillInvoiceOrigin() {
        return jdeBillInvoiceOrigin;
    }

    public void setJdeBillInvoiceOrigin(String jdeBillInvoiceOrigin) {
        SyncJdeConfig.jdeBillInvoiceOrigin = jdeBillInvoiceOrigin;
    }

    public static String getJdeBillInvoiceRead() {
        return jdeBillInvoiceRead;
    }

    public void setJdeBillInvoiceRead(String jdeBillInvoiceRead) {
        SyncJdeConfig.jdeBillInvoiceRead = jdeBillInvoiceRead;
    }

    public static String getJdeBillInvoiceWriteBack() {
        return jdeBillInvoiceWriteBack;
    }

    public void setJdeBillInvoiceWriteBack(String jdeBillInvoiceWriteBack) {
        SyncJdeConfig.jdeBillInvoiceWriteBack = jdeBillInvoiceWriteBack;
    }

    public static String getBillTable() {
        return billTable;
    }

    public void setBillTable(String billTable) {
        SyncJdeConfig.billTable = billTable;
    }

    public static String getAn8Table() {
        return an8Table;
    }

    public void setAn8Table(String an8Table) {
        SyncJdeConfig.an8Table = an8Table;
    }

    public static String getDataBase() {
        return dataBase;
    }

    public void setDataBase(String dataBase) {
        SyncJdeConfig.dataBase = dataBase;
    }

    public static String getViewName() {
        return viewName;
    }

    public void setViewName(String viewName) {
        SyncJdeConfig.viewName = viewName;
    }

    public static String getSiteView() {
        return siteView;
    }

    public void setSiteView(String siteView) {
        SyncJdeConfig.siteView = siteView;
    }

    public static String getPublicDispatch() {
        return publicDispatch;
    }

    public void setPublicDispatch(String publicDispatch) {
        SyncJdeConfig.publicDispatch = publicDispatch;
    }

    public static String getCompanyDispatch() {
        return companyDispatch;
    }

    public void setCompanyDispatch(String companyDispatch) {
        SyncJdeConfig.companyDispatch = companyDispatch;
    }

    public static String getF00022() {
        return F00022;
    }

    public void setF00022(String f00022) {
        F00022 = f00022;
    }

    public static String getF00092() {
        return F00092;
    }

    public void setF00092(String f00092) {
        F00092 = f00092;
    }

    public static String getJdeDbPrefix() {
        return jdeDbPrefix;
    }

    public void setJdeDbPrefix(String jdeDbPrefix) {
        SyncJdeConfig.jdeDbPrefix = jdeDbPrefix;
    }

    public static String getJdeRepairBillingFee() {
        return jdeRepairBillingFee;
    }

    public void setJdeRepairBillingFee(String jdeRepairBillingFee) {
        SyncJdeConfig.jdeRepairBillingFee = jdeRepairBillingFee;
    }

    public static String getVwoFailtype() {
        return vwoFailtype;
    }

    public void setVwoFailtype(String vwoFailtype) {
        SyncJdeConfig.vwoFailtype = vwoFailtype;
    }

    public static String getJdeCashierFee() {
        return jdeCashierFee;
    }

    public void setJdeCashierFee(String jdeCashierFee) {
        SyncJdeConfig.jdeCashierFee = jdeCashierFee;
    }

    /**
     * 获取当前日期 儒历 年月日
     */
    public static String getConfucianCalendar() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        int year = calendar.get(Calendar.YEAR) - 1900;
        int days = calendar.get(Calendar.DAY_OF_YEAR);
        String strDays = "" + days;
        if (days < 100) {
            strDays = String.format("%03d", days);
        }
        return "" + year + strDays;
    }


    /**
     * 将儒日历转成date 年月日
     * 实体类里已用注解实现格式化
     */
    public static Date transferConfucianCalendar(String confucianCalendar, Calendar calendar) {
        int num = Integer.parseInt(confucianCalendar);
        if (confucianCalendar.length() < 6) {//在不够6位的情况下 前补零 成6位 字符串
            confucianCalendar = String.format("%06d", num);
        }
        int year = (Integer.parseInt(confucianCalendar.substring(0, 3))) + 1900;
        int dy = Integer.parseInt(confucianCalendar.substring(3, 6));
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.DAY_OF_YEAR, dy);

        return calendar.getTime();
    }

}
