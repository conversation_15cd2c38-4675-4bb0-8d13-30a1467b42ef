package com.kerryprops.kip.bill.config;

import com.kerryprops.kip.bill.common.aop.BillErrorEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.lang.reflect.Method;
import java.util.concurrent.Executor;

/***********************************************************************************************************************
 * Project - hive-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> kevin
 * 异步任务配置
 * Created Date - 08/12/2021 14:27
 **********************************************************************************************************************/
@Configuration
@Slf4j
public class AsyncConfig implements AsyncConfigurer {

    @Override
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor threadPool = new ThreadPoolTaskExecutor();
        // 当前线程数
        threadPool.setCorePoolSize(20);
        // 最大线程数
        threadPool.setMaxPoolSize(120);
        // 线程池所使用的缓冲队列
        threadPool.setQueueCapacity(10);
        // 等待任务在关机时完成--表明等待所有线程执行完
        threadPool.setWaitForTasksToCompleteOnShutdown(true);
        // 等待时间 （默认为0，此时立即停止），并没等待xx秒后强制停止
        threadPool.setAwaitTerminationSeconds(60);
        // 线程名称前缀
        threadPool.setThreadNamePrefix("InterAsync-");
        threadPool.initialize(); // 初始化
        log.info("===== [ 开启异步线程池 ] =====");
        return threadPool;
    }

    //    @Bean
    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return new MyAsyncExceptionHandler();
    }


    /**
     * 自定义异常处理类
     */
    static class MyAsyncExceptionHandler implements AsyncUncaughtExceptionHandler {

        // 手动处理捕获的异常
        @Override
        public void handleUncaughtException(Throwable throwable, Method method, Object... obj) {
            if (BillErrorEnum.DRAW_CONCURRENT_OPERATION.getMessage().equals(throwable.getMessage())) {
                log.info(BillErrorEnum.DRAW_CONCURRENT_OPERATION.getMessage());
                return;
            }
            log.error("===== [ 捕获线程异常信息 ] =====", throwable);
            log.error("Exception message - " + throwable.getMessage());
        }

    }

}
