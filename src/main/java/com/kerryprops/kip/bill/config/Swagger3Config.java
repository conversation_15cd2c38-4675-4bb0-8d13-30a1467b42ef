package com.kerryprops.kip.bill.config;

import io.swagger.v3.oas.models.ExternalDocumentation;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * swagger-ui配置
 */
@Configuration
@Profile({"local", "dev"})
public class Swagger3Config {

    @Value("${spring.application.name:kip-billing}")
    private String appName;

    @Bean
    public OpenAPI restfulOpenApis() {
        String desc = appName + " swagger api文档\n\n" +
                "dev环境地址：API URL: https://dev.kerryprops.com; Gateway URL: https://dev-api-kip.kerryprops.com.cn" +
                "\n\n" +
                "qa环境地址：API URL: https://qa.kerryprops.com; Gateway URL: https://qa-api-kip.kerryprops.com.cn" +
                "\n\n" +
                "prod环境地址：API URL: https://prod.kerryprops.com; Gateway URL: https://api-kip.kerryprops.com.cn";
        return new OpenAPI()
                .info(new Info()
                        .title("Template - RESTFUL API")
                        .description(desc)
                        .version("1.0")
                )
                .externalDocs(new ExternalDocumentation()
                        .description("The Open API"));
    }

}