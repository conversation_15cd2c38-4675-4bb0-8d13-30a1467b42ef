package com.kerryprops.kip.bill.config;

import com.kerryprops.kip.bill.common.exceptions.ImportBillException;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/***********************************************************************************************************************
 * Project - profile-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/27/2021 09:14
 **********************************************************************************************************************/

@Data
@Component
@ConfigurationProperties(prefix = "kip.signature")
public class BsSignProperties {

    private SignConfig skcVC;

    public SignConfig getByType(String type) {
        if ("skcVC".equalsIgnoreCase(type)) {
            return this.skcVC;
        }
        throw new ImportBillException("120001", "签名验证错误");
    }

    @Getter
    @Setter
    public static class SignConfig {

        private String systemId;

        private String systemSecret;

    }

}
