package com.kerryprops.kip.bill.annotation;

public interface LogOperationSerializer<T> {

    LogOperationSerializer DEFAULT = new Default();

    String serialize(T obj);

    abstract class None implements LogOperationSerializer<Object> {

    }

    class Default implements LogOperationSerializer<Object> {

        @Override
        public String serialize(Object obj) {
            return String.valueOf(obj);
        }

    }

}
