package com.kerryprops.kip.bill.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface LogOperation {

    boolean deepLog() default false;

    String fieldAlias() default "";

    Class<? extends LogOperationSerializer> using() default LogOperationSerializer.None.class;

}
