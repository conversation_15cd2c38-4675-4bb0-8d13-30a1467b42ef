package com.kerryprops.kip.bill.service;

import com.kerryprops.kip.bill.service.model.s.BillEFapiaoSearchReqBo;
import com.kerryprops.kip.bill.service.model.s.BizBillEFapiaoSearchReqBo;
import com.kerryprops.kip.bill.webservice.vo.req.EmailResultVo;
import com.kerryprops.kip.bill.webservice.vo.req.SmsResultCallbackVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillInvoiceResource;
import com.kerryprops.kip.bill.webservice.vo.resp.BillPayer;
import com.kerryprops.kip.bill.webservice.vo.resp.BizBillInvoiceResource;
import com.kerryprops.kip.bill.webservice.vo.resp.ContentUnreadInfoVo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface KerryBillEFapiaoService {

    List<BillInvoiceResource> eFapiaoList(BillEFapiaoSearchReqBo reqBo);

    Page<BillInvoiceResource> eFapiaoList(BillEFapiaoSearchReqBo reqBo, Pageable pageable);

    Page<BizBillInvoiceResource> userEFapiaoList(BizBillEFapiaoSearchReqBo reqBo, Pageable pageable);

    ContentUnreadInfoVo userInvoiceReadStatus();

    Boolean setUserInvoiceReaded(Long id);

    Boolean setUserInvoiceAllReaded();

    List<BillPayer> fuzzyQueryPurchaserNames(List<String> bus, String query);

    String queryUrlsByBillInvoiceId(long id);

    void handleSmsCallback(SmsResultCallbackVo smsResultCallbackVo);

    void handleEmailCallback(EmailResultVo emailResultVo);

}
