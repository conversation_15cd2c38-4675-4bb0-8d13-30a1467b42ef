package com.kerryprops.kip.bill.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.kerryprops.kip.bill.common.enums.InvoiceIssueStatus;
import com.kerryprops.kip.bill.common.enums.InvoiceRecordStatus;
import com.kerryprops.kip.bill.common.enums.InvoiceRedflagStatusEnum;
import com.kerryprops.kip.bill.common.enums.InvoiceState;
import com.kerryprops.kip.bill.common.enums.InvoiceTypeEnum;
import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.utils.InvoiceUtils;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.config.KerryInvoiceProperties;
import com.kerryprops.kip.bill.dao.EFapiaoBillInvoiceRepository;
import com.kerryprops.kip.bill.dao.EFapiaoSyncRepository;
import com.kerryprops.kip.bill.dao.entity.EFapiaoBillInvoice;
import com.kerryprops.kip.bill.dao.entity.EFapiaoJDEBill;
import com.kerryprops.kip.bill.dao.entity.EFapiaoSyncBill;
import com.kerryprops.kip.bill.dao.entity.QEFapiaoBillInvoice;
import com.kerryprops.kip.bill.feign.clients.KipInvoiceClient;
import com.kerryprops.kip.bill.feign.entity.UploadInvoiceDetailVo;
import com.kerryprops.kip.bill.feign.entity.UploadInvoiceMainVo;
import com.kerryprops.kip.bill.service.EFapiaoBillService;
import com.kerryprops.kip.bill.service.EFapiaoSendService;
import com.kerryprops.kip.bill.webservice.vo.req.CallBackKerryInvoiceMainVo;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

import static com.kerryprops.kip.bill.common.constants.AppConstants.COMMA;
import static com.kerryprops.kip.bill.common.enums.BillInvoiceBizTypeEnum.SY_UTILITY;
import static com.kerryprops.kip.bill.common.enums.InvoiceTypeEnum.CZ;
import static com.kerryprops.kip.bill.common.enums.InvoiceTypeEnum.QC;
import static com.kerryprops.kip.bill.common.enums.InvoiceTypeEnum.QS;
import static com.kerryprops.kip.bill.common.enums.InvoiceTypeEnum.SZ;
import static com.kerryprops.kip.bill.common.utils.InvoiceUtils.INVOICE_STATUS_FAILED;
import static com.kerryprops.kip.bill.common.utils.InvoiceUtils.INVOICE_STATUS_SUCCESS;
import static com.kerryprops.kip.bill.common.utils.InvoiceUtils.STATUS_CANCEL;
import static com.kerryprops.kip.bill.common.utils.InvoiceUtils.STATUS_NORMAL;
import static com.kerryprops.kip.bill.common.utils.InvoiceUtils.splitJdeSalesbillNo;
import static com.kerryprops.kip.bill.common.utils.NonNullUtils.nonNullStringFunction;


/**
 * e-fapiao：本地侧数据Service接口实现类
 *
 * <AUTHOR> Yan
 * @Date 2023-3-28
 */
@Service
@Slf4j
public class EFapiaoBillServiceImpl implements EFapiaoBillService {

    private static final BigDecimal BIG_DECIMAL_100 = BigDecimal.valueOf(100);

    @Value("${e-fapiao.sales-bill-type}")
    private String eFapiaoSalesBillType;

    @Value("${full-digital.special-invoice-lease-goods-tax-prefix}")
    private String specialInvoiceLeaseGoodsTaxPrefix;

    @Autowired
    private KipInvoiceClient kipInvoiceClient;

    @Autowired
    private KerryInvoiceProperties kerryInvoiceProperties;

    @Autowired
    private EFapiaoBillInvoiceRepository eFapiaoBillInvoiceRepository;

    @Autowired
    private EFapiaoSyncRepository eFapiaoSyncRepository;

    @Autowired
    private EFapiaoJdeBillServiceImpl eFapiaoJdeBillService;

    @Autowired
    private EFapiaoSendService eFapiaoSendService;

    @Override
    public void invoiceWriteBack(CallBackKerryInvoiceMainVo mainVo) {
        mainVo.setTaxRate(transTaxRate2Num(mainVo.getTaxRate()).toString());

        EFapiaoBillInvoice eFapiaoBillInvoice = BeanUtil.copy(mainVo, EFapiaoBillInvoice.class);
        invoiceMainCallBack2KerryWriteBack(mainVo, eFapiaoBillInvoice);

        String[] jdeSalesbillNoArray = splitJdeSalesbillNo(mainVo.getSalesbillNo());
        eFapiaoBillInvoice.setKco(jdeSalesbillNoArray[0]);
        eFapiaoBillInvoice.setBillType(jdeSalesbillNoArray[1]);
        eFapiaoBillInvoice.setDoc(Integer.parseInt(jdeSalesbillNoArray[2]));
        eFapiaoBillInvoice.setPaymentItem(jdeSalesbillNoArray[3]);
        eFapiaoBillInvoice.setPurchaserAddress(nonNullStringFunction.apply(mainVo.getPurchaserAddress()));
        eFapiaoBillInvoice.setPurchaserBankName(nonNullStringFunction.apply(mainVo.getPurchaserBankName()));
        eFapiaoBillInvoice.setJdeUnit(nonNullStringFunction.apply(mainVo.getExt1()));
        eFapiaoBillInvoice.setBizType("JDE");

        if (CollectionUtils.isNotEmpty(mainVo.getDetailVos())) {
            mainVo.getDetailVos().forEach(detailVo -> {
                eFapiaoBillInvoice.setGoodsTaxNo(nonNullStringFunction.apply(detailVo.getGoodsTaxNo()));
                eFapiaoBillInvoice.setTaxDiscount(nonNullStringFunction.apply(detailVo.getTaxPre()));
                eFapiaoBillInvoice.setTaxDiscountDesc(nonNullStringFunction.apply(detailVo.getTaxPreCon()));
                eFapiaoBillInvoice.setItemName(nonNullStringFunction.apply(detailVo.getItemName()));
                eFapiaoBillInvoice.setItemSpec(nonNullStringFunction.apply(detailVo.getItemSpec()));
            });
        }

        if (INVOICE_STATUS_FAILED == mainVo.getInvoiceStatus()) {
            //处理开票失败通知
            log.warn("[EFapiao][InvoiceFailed] - Invoice creation failed for salesBillNo={} with error={}", mainVo.getSalesbillNo(), mainVo.getMessage());

            // 更新Kerry+同步表状态
            eFapiaoSyncRepository.updateStateBySalesBillNo(mainVo.getSalesbillNo()
                    , InvoiceIssueStatus.INVOICE_FAILED.getIndex());
        } else if (INVOICE_STATUS_SUCCESS == mainVo.getInvoiceStatus() && STATUS_NORMAL == mainVo.getStatus()) {
            if (InvoiceRedflagStatusEnum.RED_FLAG_DEFAULT.getIndexStr().equals(mainVo.getRedFlag())) {
                // 开票成功，发票状态正常
                log.info("[EFapiao][InvoiceNormal] - Invoice successfully created in normal state for salesBillNo={}", mainVo.getSalesbillNo());
                InvoiceRecordStatus invoiceRecordStatus = mainVo.getSalesbillNumber() > 1 ?
                        InvoiceRecordStatus.ENTOURAGE_COMPLETED : InvoiceRecordStatus.COMPLETED;
                eFapiaoBillInvoice.setInvoiceRecordStatus(invoiceRecordStatus);
                eFapiaoBillInvoice.setState(InvoiceState.NORMAL);
                eFapiaoBillInvoiceRepository.save(eFapiaoBillInvoice);


                if (mainVo.getSalesbillNumber() > 1) {
                    // 合并/组合场景下，合并展示用发票信息的mcu字段、jdeUnit字段
                    mergeBuildingInfo(mainVo.getBusinessType(), mainVo.getExt1(), mainVo);
                }

                // 更新Kerry+同步表状态
                eFapiaoSyncRepository.updateStateBySalesBillNo(mainVo.getSalesbillNo()
                        , InvoiceIssueStatus.INVOICE_SUCCESS.getIndex());
            } else if (InvoiceRedflagStatusEnum.RED_FLAG_RED_STATUS.getIndexStr().equals(mainVo.getRedFlag())) {
                // 开票成功，标记原蓝票为“已红冲”
                log.info("[EFapiao][InvoiceRedStatus] - Invoice marked as red reversed for salesBillNo={}", mainVo.getSalesbillNo());
                eFapiaoBillInvoiceRepository.updateStateBySalesBillNoAndInvoiceNo(mainVo.getSalesbillNo()
                        , mainVo.getInvoiceNo(), InvoiceState.RED_STATUS.getIndex());

                // 更新Kerry+同步表状态
                eFapiaoSyncRepository.updateStateBySalesBillNo(mainVo.getSalesbillNo()
                        , InvoiceIssueStatus.INVOICE_SUCCESS_RED_STATUS.getIndex());
            } else if (InvoiceRedflagStatusEnum.RED_FLAG_RED_INVOICE.getIndexStr().equals(mainVo.getRedFlag())) {
                // 开票成功，发票为红冲发票
                log.info("[EFapiao][InvoiceRedFlag] - Red invoice successfully created for salesBillNo={}", mainVo.getSalesbillNo());
                InvoiceRecordStatus invoiceRecordStatus = mainVo.getSalesbillNumber() > 1 ?
                        InvoiceRecordStatus.ENTOURAGE_COMPLETED : InvoiceRecordStatus.COMPLETED;
                eFapiaoBillInvoice.setInvoiceRecordStatus(invoiceRecordStatus);
                eFapiaoBillInvoice.setState(InvoiceState.RED_LETTER);
                eFapiaoBillInvoiceRepository.save(eFapiaoBillInvoice);

                if (mainVo.getSalesbillNumber() > 1) {
                    // 合并/组合场景下，合并展示用发票信息的mcu字段、jdeUnit字段
                    mergeBuildingInfo(mainVo.getBusinessType(), mainVo.getExt1(), mainVo);
                }

                // 更新Kerry+同步表状态
                eFapiaoSyncRepository.updateStateBySalesBillNo(mainVo.getSalesbillNo()
                        , InvoiceIssueStatus.INVOICE_SUCCESS_RED_FLAG.getIndex());
            }
        } else if (INVOICE_STATUS_SUCCESS == mainVo.getInvoiceStatus() && STATUS_CANCEL == mainVo.getStatus()) {
            // 开票成功，发票状态作废
            log.info("[EFapiao][InvoiceCancelled] - Invoice has been cancelled for salesBillNo={}", mainVo.getSalesbillNo());
            eFapiaoBillInvoiceRepository.updateStateBySalesBillNoAndInvoiceNo(mainVo.getSalesbillNo()
                    , mainVo.getInvoiceNo(), InvoiceState.CANCELLED.getIndex());

            // 更新Kerry+同步表状态
            eFapiaoSyncRepository.updateStateBySalesBillNo(mainVo.getSalesbillNo()
                    , InvoiceIssueStatus.INVOICE_CANCEL.getIndex());
        }
    }

    @Override
    public void excelImportInvoiceWriteBack(CallBackKerryInvoiceMainVo mainVo) {
        if (Objects.isNull(mainVo)) {
            log.error("[EFapiao][NullData] - Cannot process excel import callback due to missing invoice data");
            return;
        }

        String salesbillNo = mainVo.getSalesbillNo();

        // 票易通ex1是bu，票易通ex3是合同号，票易通ex4是付款人
        if (StringUtils.isAnyBlank(mainVo.getBusinessType(), mainVo.getExt2(), mainVo.getExt3())) {
            log.warn("[EFapiao][MissingData] - Critical data missing for invoice import: bu={}, doco={}, an8={}", 
                    mainVo.getBusinessType(), mainVo.getExt2(), mainVo.getExt3());
            return;
        }

        mainVo.setTaxRate(transTaxRate2Num(mainVo.getTaxRate()).toString());

        EFapiaoBillInvoice eFapiaoBillInvoice = BeanUtil.copy(mainVo, EFapiaoBillInvoice.class);
        invoiceMainCallBack2KerryWriteBack(mainVo, eFapiaoBillInvoice);

        eFapiaoBillInvoice.setBizType(SY_UTILITY.name()
                .equalsIgnoreCase(mainVo.getSystemOrig()) ? SY_UTILITY.getBizCode() : "IMP");

        if (INVOICE_STATUS_FAILED == mainVo.getInvoiceStatus()) {
            //处理开票失败通知
            log.warn("[EFapiao][ImportFailed] - Invoice import failed for salesBillNo={} with error={}", salesbillNo, mainVo.getMessage());
        } else if (INVOICE_STATUS_SUCCESS == mainVo.getInvoiceStatus() && STATUS_NORMAL == mainVo.getStatus()) {
            if (InvoiceRedflagStatusEnum.RED_FLAG_DEFAULT.getIndexStr().equals(mainVo.getRedFlag())) {
                // 开票成功，发票状态正常
                log.info("[EFapiao][ImportSuccess] - Invoice imported successfully in normal state for salesBillNo={}", salesbillNo);
                InvoiceRecordStatus invoiceRecordStatus = mainVo.getSalesbillNumber() > 1 ?
                        InvoiceRecordStatus.ENTOURAGE_COMPLETED : InvoiceRecordStatus.COMPLETED;
                eFapiaoBillInvoice.setInvoiceRecordStatus(invoiceRecordStatus);
                eFapiaoBillInvoice.setState(InvoiceState.NORMAL);
                eFapiaoBillInvoiceRepository.save(eFapiaoBillInvoice);

                if (mainVo.getSalesbillNumber() > 1) {
                    // 合并/组合场景下，合并展示用发票信息的mcu字段、jdeUnit字段
                    mergeBuildingInfo(eFapiaoBillInvoice.getMcu(), eFapiaoBillInvoice.getJdeUnit(), mainVo);
                }
                if (mainVo.getSalesbillNumber() == 1
                        && SY_UTILITY.name().equalsIgnoreCase(mainVo.getSystemOrig())) {
                    eFapiaoSendService.sendInvoiceMessage(StringUtils.EMPTY, Set.of(eFapiaoBillInvoice.getId()));
                }
            } else if (InvoiceRedflagStatusEnum.RED_FLAG_RED_STATUS.getIndexStr().equals(mainVo.getRedFlag())) {
                // 开票成功，标记原蓝票为“已红冲”
                log.info("[EFapiao][ImportRedStatus] - Invoice marked as red reversed for salesBillNo={}", salesbillNo);
                eFapiaoBillInvoiceRepository.updateStateBySalesBillNoAndInvoiceNo(mainVo.getSalesbillNo()
                        , mainVo.getInvoiceNo(), InvoiceState.RED_STATUS.getIndex());
            } else if (InvoiceRedflagStatusEnum.RED_FLAG_RED_INVOICE.getIndexStr().equals(mainVo.getRedFlag())) {
                // 开票成功，发票为红冲发票
                log.info("[EFapiao][ImportRedFlag] - Red invoice successfully created for salesBillNo={}", salesbillNo);
                InvoiceRecordStatus invoiceRecordStatus = mainVo.getSalesbillNumber() > 1 ?
                        InvoiceRecordStatus.ENTOURAGE_COMPLETED : InvoiceRecordStatus.COMPLETED;
                eFapiaoBillInvoice.setInvoiceRecordStatus(invoiceRecordStatus);
                eFapiaoBillInvoice.setState(InvoiceState.RED_LETTER);
                eFapiaoBillInvoiceRepository.save(eFapiaoBillInvoice);

                if (mainVo.getSalesbillNumber() > 1) {
                    // 合并/组合场景下，合并展示用发票信息的mcu字段、jdeUnit字段
                    mergeBuildingInfo(eFapiaoBillInvoice.getMcu(), eFapiaoBillInvoice.getJdeUnit(), mainVo);
                }
            }
        } else if (INVOICE_STATUS_SUCCESS == mainVo.getInvoiceStatus() && STATUS_CANCEL == mainVo.getStatus()) {
            // 开票成功，发票状态作废
            log.info("[EFapiao][ImportCancelled] - Invoice has been cancelled for salesBillNo={}", salesbillNo);
            eFapiaoBillInvoiceRepository.updateStateBySalesBillNoAndInvoiceNo(mainVo.getSalesbillNo()
                    , mainVo.getInvoiceNo(), InvoiceState.CANCELLED.getIndex());
        }
    }

    @Override
    @Transactional(noRollbackFor = RuntimeException.class)
    public void invoiceUpload(EFapiaoJDEBill eFapiaoJDEBill) {
        if (Objects.isNull(eFapiaoJDEBill)) {
            log.error("[EFapiao][NullData] - Invoice upload failed due to null invoice information");
            throw new RuntimeException("invoice_service_uploaded invoice_info_is_null");
        }

        UploadInvoiceMainVo mainVo = getInvoiceMainVo(eFapiaoJDEBill);

        log.info("[EFapiao][UploadStart] - Beginning invoice upload for salesBillNo={} with requestParam={}", mainVo.getSalesbillNo(), JSONObject.toJSONString(mainVo));

        EFapiaoSyncBill eFapiaoSyncBill = BeanUtil.copy(eFapiaoJDEBill, EFapiaoSyncBill.class);
        eFapiaoSyncBill.setInvoiceType(mainVo.getInvoiceType());
        eFapiaoSyncBill.setSalesBillNo(mainVo.getSalesbillNo());
        eFapiaoSyncRepository.save(eFapiaoSyncBill);

        RespWrapVo respWrapVo = kipInvoiceClient.invoiceUpload(mainVo);

        if (!Objects.equals(respWrapVo.getCode(), RespCodeEnum.SUCCESS.getCode())) {
            eFapiaoSyncRepository.updateStateBySalesBillNo(mainVo.getSalesbillNo()
                    , InvoiceIssueStatus.UPLOAD_FAILED.getIndex());

            throw new RuntimeException(respWrapVo.getMessage());
        } else {
            log.info("[EFapiao][UploadSuccess] - Invoice successfully uploaded, response={}", respWrapVo);

            // Kerry+已读表，标记已读
            eFapiaoSyncRepository.updateStateBySalesBillNo(mainVo.getSalesbillNo()
                    , InvoiceIssueStatus.UPLOAD_SUCCESS.getIndex());

            // JDE已读表，标记已读
            eFapiaoJdeBillService.writeBackUploaded(eFapiaoJDEBill);
        }
        log.info("[EFapiao][UploadComplete] - Invoice upload process completed");
    }

    private void mergeBuildingInfo(String mcu, String jdeUnit, CallBackKerryInvoiceMainVo mainVo) {
        EFapiaoBillInvoice displayEFapiaoBillInvoice
                = eFapiaoBillInvoiceRepository.getJpaQueryFactory()
                .selectFrom(QEFapiaoBillInvoice.eFapiaoBillInvoice)
                .where(QEFapiaoBillInvoice.eFapiaoBillInvoice.invoiceNo.eq(mainVo.getInvoiceNo())
                        .and(QEFapiaoBillInvoice.eFapiaoBillInvoice.invoiceRecordStatus.eq(InvoiceRecordStatus.COMPLETED)))
                .fetchFirst();

        if (Objects.isNull(displayEFapiaoBillInvoice)) {
            log.warn("[EFapiao][DisplayError] - Failed to find display invoice for processing");
            return;
        }

        Set<String> mcuArray = StringUtils.isEmpty(displayEFapiaoBillInvoice.getMcu()) ?
                new HashSet<>() : new HashSet<>(Set.of(displayEFapiaoBillInvoice.getMcu().split(COMMA)));
        mcuArray.add(Optional.ofNullable(mcu).orElse(StringUtils.EMPTY));
        displayEFapiaoBillInvoice.setMcu(String.join(COMMA, mcuArray));

        Set<String> jdeUnitArray = StringUtils.isEmpty(displayEFapiaoBillInvoice.getJdeUnit()) ?
                new HashSet<>() : new HashSet<>(Set.of(displayEFapiaoBillInvoice.getJdeUnit().split(COMMA)));
        jdeUnitArray.add(Optional.ofNullable(jdeUnit).orElse(StringUtils.EMPTY));
        displayEFapiaoBillInvoice.setJdeUnit(String.join(COMMA, jdeUnitArray));

        eFapiaoBillInvoiceRepository.save(displayEFapiaoBillInvoice);
    }

    private void invoiceMainCallBack2KerryWriteBack(CallBackKerryInvoiceMainVo mainVo
            , EFapiaoBillInvoice eFapiaoBillInvoice) {
        eFapiaoBillInvoice.setSalesBillNo(mainVo.getSalesbillNo());
        eFapiaoBillInvoice.setSellerName(Optional.ofNullable(mainVo.getSellerName()).orElse(StringUtils.EMPTY));
        eFapiaoBillInvoice.setAmountWithoutTax(Optional.ofNullable(mainVo.getAmountWithoutTax()).orElse(BigDecimal.ZERO));
        eFapiaoBillInvoice.setAmountWithTax(Optional.ofNullable(mainVo.getAmountWithTax()).orElse(BigDecimal.ZERO));
        eFapiaoBillInvoice.setTaxAmount(Optional.ofNullable(mainVo.getTaxAmount()).orElse(BigDecimal.ZERO));
        eFapiaoBillInvoice.setPdfUrl(Optional.ofNullable(mainVo.getPdfUrl()).orElse(StringUtils.EMPTY));
        eFapiaoBillInvoice.setXmlUrl(nonNullStringFunction.apply(mainVo.getXmlUrl()));
        eFapiaoBillInvoice.setOfdUrl(nonNullStringFunction.apply(mainVo.getOfdUrl()));
        eFapiaoBillInvoice.setMakingReason(nonNullStringFunction.apply(mainVo.getMakingReason()));
        eFapiaoBillInvoice.setInvoiceNo(Optional.ofNullable(mainVo.getInvoiceNo()).orElse(StringUtils.EMPTY));
        eFapiaoBillInvoice.setInvoiceCode(Optional.ofNullable(mainVo.getInvoiceCode()).orElse(StringUtils.EMPTY));
        eFapiaoBillInvoice.setPaperDrewDate(InvoiceUtils.transDateFormat(mainVo.getPaperDrewDate()));

        eFapiaoBillInvoice.setInvoiceType(Optional.ofNullable(InvoiceTypeEnum.getByCode(mainVo.getInvoiceType()))
                .map(InvoiceTypeEnum::getKipCode).orElse(StringUtils.EMPTY));
        eFapiaoBillInvoice.setCompanyCode(nonNullStringFunction.apply(mainVo.getSellerNo()));
        eFapiaoBillInvoice.setMcu(nonNullStringFunction.apply(mainVo.getBusinessType()));
        eFapiaoBillInvoice.setDoco(nonNullStringFunction.apply(mainVo.getExt2()));
        eFapiaoBillInvoice.setAn8(Optional.ofNullable(mainVo.getExt3()).orElse("0"));
        eFapiaoBillInvoice.setUpdatedTime(ZonedDateTime.now().withNano(0));

        // 默认无意义值
        eFapiaoBillInvoice.setConstant3(BigDecimal.ZERO);
        eFapiaoBillInvoice.setConstant4(BigDecimal.ZERO);
        eFapiaoBillInvoice.setConstant5(BigDecimal.ZERO);

        List<String> fullDigitalInvoiceType = List.of(QC.getCode(), QS.getCode(), CZ.getCode(), SZ.getCode());
        if (Objects.nonNull(mainVo.getInvoiceType())
                && fullDigitalInvoiceType.contains(mainVo.getInvoiceType())
                && CollectionUtils.isNotEmpty(mainVo.getDetailVos())) {
            mainVo.getDetailVos().forEach(detailVo -> {
                eFapiaoBillInvoice.setLeaseTermStart(nonNullStringFunction.apply(detailVo.getLeaseTermStart()));
                eFapiaoBillInvoice.setLeaseTermEnd(nonNullStringFunction.apply(detailVo.getLeaseTermEnd()));
            });
        }
    }

    private BigDecimal transTaxRate2Num(String taxRate) {
        try {
            return StringUtils.isBlank(taxRate) ? BigDecimal.ZERO : new BigDecimal(taxRate);
        } catch (Exception e) {
            log.warn("[EFapiao][TaxRateError] - Failed to convert tax rate '{}' to numeric value: error={}", taxRate, e.getMessage());
            return new BigDecimal("-0.01"); // -0.01做特殊数字，标记异常场景
        }
    }

    private UploadInvoiceMainVo getInvoiceMainVo(EFapiaoJDEBill eFapiaoJDEBill) {
        UploadInvoiceMainVo mainVo = new UploadInvoiceMainVo();
        mainVo.setBusinessType(eFapiaoJDEBill.getMcu());
        mainVo.setSalesbillNo(eFapiaoJDEBill.getKco()
                + eFapiaoJDEBill.getBillType()
                + eFapiaoJDEBill.getDoc()
                + eFapiaoJDEBill.getPaymentItem());
        mainVo.setSalesbillType(eFapiaoSalesBillType);
        String invoiceTypeKipCode = getInvoiceTypeKipCode(eFapiaoJDEBill.getInvoiceType());
        mainVo.setInvoiceType(invoiceTypeKipCode);
        mainVo.setSellerNo(eFapiaoJDEBill.getCompanyCode());
        mainVo.setPurchaserName(eFapiaoJDEBill.getPurchaserName());
        mainVo.setPurchaserTaxNo(eFapiaoJDEBill.getPurchaserTaxNo());

        // 发票实际展示的内容是：purchaserBankName + purchaserBankAccount，
        // 但是传入的purchaserBankName现在已经是purchaserBankName + purchaserBankAccount，因此需要分隔
        String purchaserAddressAccount = eFapiaoJDEBill.getPurchaserAddress();
        if (StringUtils.isNotEmpty(purchaserAddressAccount)) {
            NameAndAccount nameAndAccount = splitNameAccount(purchaserAddressAccount);
            mainVo.setPurchaserAddress(nameAndAccount.getName());
            mainVo.setPurchaserTel(nameAndAccount.getAccount());
        }

        String purchaserBankNameAccount = eFapiaoJDEBill.getPurchaserBankName();
        if (StringUtils.isNotEmpty(purchaserBankNameAccount)) {
            NameAndAccount nameAndAccount = splitNameAccount(purchaserBankNameAccount);
            mainVo.setPurchaserBankName(nameAndAccount.getName());
            mainVo.setPurchaserBankAccount(nameAndAccount.getAccount());
        }

        // 数电票，行业特殊票种不动产租赁场景适配
        String mailingAddress = eFapiaoJDEBill.getMailingAddress();
        String remark = eFapiaoJDEBill.getMailingAddress() + "\n合同号：" + eFapiaoJDEBill.getDoco();
        if (List.of(InvoiceTypeEnum.QC.getKipCode(), InvoiceTypeEnum.QS.getKipCode()
                , InvoiceTypeEnum.CZ.getKipCode(), InvoiceTypeEnum.SZ.getKipCode()).contains(invoiceTypeKipCode)
                && StringUtils.isNotEmpty(eFapiaoJDEBill.getGoodsTaxNo())
                && eFapiaoJDEBill.getGoodsTaxNo().startsWith(specialInvoiceLeaseGoodsTaxPrefix)) {
            log.info("[EFapiao][SpecialLease] - Processing full digital special lease invoice for salesBillNo={}", mainVo.getSalesbillNo());

            String realEstatePlace = eFapiaoJDEBill.getRealEstatePlace();
            if (!StringUtils.isAnyEmpty(mailingAddress, realEstatePlace)
                    && mailingAddress.length() > realEstatePlace.length()) {
                mailingAddress = mailingAddress.substring(realEstatePlace.length());
            }
            remark = "合同号：" + eFapiaoJDEBill.getDoco();
        }

        // 票易通存在备注长度限制
        remark = StringUtils.abbreviate(nonNullStringFunction.apply(remark), 200);
        mainVo.setRemark(remark);

        // 含税价格
        mainVo.setPriceMethod(1);
        mainVo.setSystemId(kerryInvoiceProperties.getSystemId());
        mainVo.setTimeStamp((new Date()).getTime() + StringUtils.EMPTY);
        // 填充签名参数
        mainVo.setSign(InvoiceUtils.getSign(mainVo.getSalesbillNo(), mainVo.getTimeStamp(), kerryInvoiceProperties));
        mainVo.setAmountWithTax(Optional.ofNullable(eFapiaoJDEBill.getAmountWithTax())
                .map(e -> e.divide(BIG_DECIMAL_100)).orElse(BigDecimal.ZERO));
        mainVo.setAmountWithoutTax(Optional.ofNullable(eFapiaoJDEBill.getAmountWithoutTax())
                .map(e -> e.divide(BIG_DECIMAL_100)).orElse(BigDecimal.ZERO));
        mainVo.setTaxAmount(Optional.ofNullable(eFapiaoJDEBill.getTaxAmount())
                .map(e -> e.divide(BIG_DECIMAL_100)).orElse(BigDecimal.ZERO));
        mainVo.setExt1(eFapiaoJDEBill.getJdeUnit());
        mainVo.setExt2(eFapiaoJDEBill.getDoco());
        mainVo.setExt3(eFapiaoJDEBill.getAn8());

        List<UploadInvoiceDetailVo> detailVoList = new ArrayList<>();
        UploadInvoiceDetailVo detailVo = new UploadInvoiceDetailVo();
        detailVo.setSalesbillItemNo(mainVo.getSalesbillNo() + "001");
        detailVo.setItemName(eFapiaoJDEBill.getItemName());
        detailVo.setItemSpec(eFapiaoJDEBill.getItemSpec());
        detailVo.setQuantity(BigDecimal.ZERO.compareTo(eFapiaoJDEBill.getAmountWithTax()) > 0
                ? BigDecimal.valueOf(-1) : BigDecimal.ONE);
        detailVo.setGoodsTaxNo(eFapiaoJDEBill.getGoodsTaxNo());
        detailVo.setTaxRate(Optional.ofNullable(eFapiaoJDEBill.getTaxRate())
                .map(e -> e.divide(BigDecimal.valueOf(100000L))).orElse(BigDecimal.ZERO));

        detailVo.setAmountWithTax(Optional.ofNullable(eFapiaoJDEBill.getAmountWithTax())
                .map(e -> e.divide(BIG_DECIMAL_100)).orElse(BigDecimal.ZERO));
        detailVo.setAmountWithoutTax(Optional.ofNullable(eFapiaoJDEBill.getAmountWithoutTax())
                .map(e -> e.divide(BIG_DECIMAL_100)).orElse(BigDecimal.ZERO));
        detailVo.setTaxAmount(Optional.ofNullable(eFapiaoJDEBill.getTaxAmount())
                .map(e -> e.divide(BIG_DECIMAL_100)).orElse(BigDecimal.ZERO));
        detailVo.setUnitPriceWithTax(Optional.ofNullable(eFapiaoJDEBill.getAmountWithTax())
                .map(e -> e.divide(BIG_DECIMAL_100).abs()).orElse(BigDecimal.ZERO));
        detailVo.setTaxPre(eFapiaoJDEBill.getTaxDiscount());
        detailVo.setTaxPreCon(eFapiaoJDEBill.getTaxDiscountDesc());
        detailVo.setRealEstateAddress(mailingAddress);
        detailVo.setRealEstateNo(eFapiaoJDEBill.getRealEstateNo());
        detailVo.setCrossCitySign("false");
        detailVo.setAreaUnit("01");
        detailVo.setRealEstatePlace(eFapiaoJDEBill.getRealEstatePlace());
        detailVo.setExt1(eFapiaoJDEBill.getMcu());
        detailVo.setExt3(eFapiaoJDEBill.getDoco());
        detailVo.setExt4(eFapiaoJDEBill.getAn8());

        final String DATE_SPLIT_SIGN = "-";
        String itemSpec = eFapiaoJDEBill.getItemSpec(); // 2015/07/01-2015/09/30
        if (StringUtils.isNotEmpty(itemSpec) && itemSpec.contains(DATE_SPLIT_SIGN)) {
            itemSpec = itemSpec.replace("/", "");
            String[] itemSpecArray = itemSpec.split(DATE_SPLIT_SIGN);

            if (itemSpecArray.length == 2) {
                detailVo.setLeaseTermStart(itemSpecArray[0]);
                detailVo.setLeaseTermEnd(itemSpecArray[1]);
            }
        }
        detailVoList.add(detailVo);
        mainVo.setDetailVos(detailVoList);
        return mainVo;
    }

    /**
     * JDE发票类型转为票易通的发票类型
     * JDE发票类型code：N-纸质普票，NE-电子普票，Y-纸质专票，YE-电子专票，NQ-数电电子普票，YQ-数电电子专票
     */
    private String getInvoiceTypeKipCode(String invoiceTypeJde) {

        switch (invoiceTypeJde) {
            case "N":
                return "gvat";
            case "NE":
                return "gvate";
            case "Y":
                return "svat";
            case "YE":
                return "svate";
            case "NQ":
                return "gvatq";
            case "YQ":
                return "svatq";
            default:
                log.error("[EFapiao][InvalidType] - Unrecognized JDE invoice type: {}", invoiceTypeJde);
                return "";
        }
    }

    // 分隔名称（文字形式）和账号（号码形式），如：花旗银行北京分行********** -> 花旗银行北京分行 **********
    private NameAndAccount splitNameAccount(String nameAccount) {
        if (StringUtils.isEmpty(nameAccount)) {
            log.info("[EFapiao][NameAccount] - Name account string is empty");
            return NameAndAccount.builder()
                    .name(StringUtils.EMPTY)
                    .account(StringUtils.EMPTY)
                    .build();
        }

        // 从尾部开始，找到第一个非数字内容位置
        char[] nameAccountArray = nameAccount.toCharArray();
        for (int i = nameAccountArray.length - 1; i >= 0; i--) {
            char tempChar = nameAccountArray[i];
            if (!Character.isDigit(tempChar) && '-' != tempChar) {
                return NameAndAccount.builder()
                        .name(nameAccount.substring(0, i + 1))
                        .account(nameAccount.substring(i + 1))
                        .build();
            }
        }

        log.info("[EFapiao][NameAccount] - Could not split name and account, using full string as name");
        return NameAndAccount.builder()
                .name(nameAccount)
                .account(StringUtils.EMPTY)
                .build();
    }

    @Data
    @Builder
    private static class NameAndAccount {

        private String name;

        private String account;

    }

}
