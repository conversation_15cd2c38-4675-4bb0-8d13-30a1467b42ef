package com.kerryprops.kip.bill.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kerryprops.kip.bill.common.enums.AptPayVerifyStatus;
import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import com.kerryprops.kip.bill.common.enums.BillPayModule;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.PaymentCateEnum;
import com.kerryprops.kip.bill.common.enums.PaymentPayType;
import com.kerryprops.kip.bill.common.utils.IdWorker;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptPayBillRepository;
import com.kerryprops.kip.bill.dao.AptPayRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPayBill;
import com.kerryprops.kip.bill.dao.entity.AptPayConfig;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.AptPayService;
import com.kerryprops.kip.bill.utils.BillUtil;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.common.constants.AppConstants.COMMA;
import static com.kerryprops.kip.bill.common.utils.BillingFun.exceptionToNull;
import static com.kerryprops.kip.bill.common.utils.NonNullUtils.nonNullStringFunction;

@Slf4j
@Service
@RequiredArgsConstructor
public class AptPayServiceImpl implements AptPayService {

    private final ObjectMapper objectMapper;

    private final AptPayRepository aptPayRepository;

    private final AptPayBillRepository payBillRepository;

    private final AptBillRepository aptBillRepository;

    @Override
    @Transactional
    public AptPay cancelAptPay(AptPaymentInfo paymentInfo, List<AptBill> aptBills) {
        String userName = Optional.ofNullable(UserInfoUtils.getUser())
                .map(v -> v.getNickName())
                .map(v -> URLDecoder.decode(v, StandardCharsets.UTF_8))
                .orElse("Kerry +");
        BillPayModule payModule = paymentInfo.getBillPayModule();
        BillPayChannel payChannel = getBillPayChannel(payModule);
        String description = paymentInfo.getDescription();
        if (StringUtils.isBlank(description)) {
            description = "线上支付";
        }

        String payDesc = CollectionUtils.isEmpty(aptBills)
                ? payChannel.getInfo() : generateDescription(aptBills);

        log.info("取消支付 paymentInfo : {}", paymentInfo);
        AptPay pay = AptPay.builder()
                .payChannel(payChannel)
                .payDesc(payDesc)
                .paymentCate(Optional.ofNullable(paymentInfo.getPaymentCate()).map(Enum::name).orElse(PaymentCateEnum.UNKNOWN.name()))
                .payDate(new Date())
                .payAct(paymentInfo.getPayAct())
                .verifyStatus(AptPayVerifyStatus.TO_BE_VERIFIED)
                .paymentInfoId(paymentInfo.getId())
                .versionNum(1)
                .totalAmt(paymentInfo.getAmt())
                .advanceAmount(BigDecimal.ZERO)
                .projectId(paymentInfo.getProjectId())
                .buildingId(paymentInfo.getBuildingId())
                .floorId(paymentInfo.getFloorId())
                .roomId(paymentInfo.getRoomId())
                .sendJdeStatus(0)
                .payTranx("0")
                .deletedTime(new Date())
                .deletedBy(userName)
                .payType(Optional.ofNullable(paymentInfo.getPayType()).map(v -> v.getInfo()).orElse(PaymentPayType.UNKNOWN.getInfo()))
                .payDetail(null)
                .positionItem(paymentInfo.getPositionItem())
                .comments(description)
                .deletedAt(1)
                .createBy(UserInfoUtils.getKerryAccount())
                .billPayModule(payModule)
                .build();
        return aptPayRepository.save(pay);
    }

    /**
     * 保存物业账单收款单
     */
    @Override
    @Transactional
    public void savePayInfo(AptPaymentInfo paymentInfo,
                            AptPayConfig payConfig,
                            String tranxId,
                            double paidAmt,
                            Date createDate,
                            List<AptBill> aptBills) {

        BillPayChannel payChannel = getBillPayChannel(paymentInfo);
        String payAct = paymentInfo.getPayAct();
        if (StringUtils.isBlank(payAct)) {
            payAct = IdWorker.getFlowIdWorkerInstance().nextStrId();
        }
        AptBill sampleBill = aptBills.get(0);
        String description = paymentInfo.getDescription();
        if (StringUtils.isBlank(description)) {
            description = "线上支付";
        }

        String payDesc = CollectionUtils.isEmpty(aptBills)
                ? payChannel.getInfo() : generateDescription(aptBills);

        AptPay pay = AptPay.builder()
                .payChannel(payChannel)
                .payDesc(payDesc)
                .payConfigId(payConfig.getId())
                .payType(payConfig.getPaymentType())
                .paymentCate(paymentInfo.getPaymentCate().name())
                .payTranx(tranxId)
                .tax(payConfig.getTax())
                .taxAmt(BillUtil.calcTaxAmount(paidAmt, payConfig.getTax(), payConfig.getMax()))
                .totalAmt(paidAmt)
                .advanceAmount(paymentInfo.getAdvanceAmount())
                .payDate(createDate)
                .payAct(payAct)
                .payDetail(payConfig.getPaymentDetail())
                .verifyStatus(AptPayVerifyStatus.TO_BE_VERIFIED)
                .paymentInfoId(paymentInfo.getId())
                .versionNum(1)
                .projectId(paymentInfo.getProjectId())
                .buildingId(paymentInfo.getBuildingId())
                .floorId(paymentInfo.getFloorId())
                .roomId(paymentInfo.getRoomId())
                .bu(Optional.ofNullable(paymentInfo.getBu()).filter(StringUtils::isNotBlank)
                        .orElse(sampleBill.getBu()))
                .unit(Optional.ofNullable(paymentInfo.getUnit()).filter(StringUtils::isNotBlank)
                        .orElse(sampleBill.getUnit()))
                .an8(Optional.ofNullable(paymentInfo.getAn8()).filter(StringUtils::isNotBlank)
                        .orElse(sampleBill.getAn8()))
                .alph(Optional.ofNullable(paymentInfo.getAlph()).filter(StringUtils::isNotBlank)
                        .orElse(sampleBill.getAlph()))
                .doco(Optional.ofNullable(paymentInfo.getDoco()).filter(StringUtils::isNotBlank)
                        .orElse(sampleBill.getDoco()))
                .positionItem(paymentInfo.getPositionItem())
                .comments(description)
                .billPayModule(Optional.ofNullable(paymentInfo.getBillPayModule()).orElse(BillPayModule.KERRY))
                .createBy(UserInfoUtils.getKerryAccount())
                .build();

        var save = aptPayRepository.save(pay);
        log.info("save apt pay record: {}", exceptionToNull(() -> objectMapper.writeValueAsString(save)));
        for (AptBill aptBill : aptBills) {
            AptPayBill build = AptPayBill.builder().payId(save.getId()).billId(aptBill.getId())
                    .billNo(aptBill.getBillNo()).build();
            payBillRepository.save(build);
        }
    }

    /**
     * 保存杂费支付收款单
     */
    @Override
    @Transactional
    public void savePayInfo(AptPaymentInfo paymentInfo, AptPayConfig payConfig, String tranxId, double paidAmt, Date createDate) {
        BillPayChannel payChannel = getBillPayChannel(paymentInfo);
        BigDecimal feeTax = paymentInfo.getFeeTax();
        BigDecimal paidAmount = BigDecimal.valueOf(paidAmt);
        BigDecimal feeTaxAmount = BillUtil.calcFeeTaxAmount(paidAmount, feeTax);
        String infoPayAct = paymentInfo.getPayAct();
        String payAct = StringUtils.isBlank(infoPayAct) ? IdWorker.getFlowIdWorkerInstance().nextStrId() : infoPayAct;
        Optional<AptBill> aptBill = aptBillRepository.findLastBillByRoomId(paymentInfo.getRoomId());

        AptPay pay = new AptPay();
        pay.setPayChannel(payChannel);
        pay.setPayDesc(payChannel.getInfo());
        pay.setPayConfigId(payConfig.getId());
        pay.setPayType(payConfig.getPaymentType());
        pay.setPaymentCate(paymentInfo.getPaymentCate().name());
        pay.setPayTranx(tranxId);
        pay.setTax(payConfig.getTax());
        pay.setTaxAmt(BillUtil.calcTaxAmount(paidAmt, payConfig.getTax(), payConfig.getMax()));
        pay.setTotalAmt(paidAmt);
        pay.setAdvanceAmount(paymentInfo.getAdvanceAmount());
        pay.setPayDate(createDate);
        pay.setPayAct(payAct);
        pay.setPayDetail(payConfig.getPaymentDetail());
        pay.setVerifyStatus(AptPayVerifyStatus.TO_BE_VERIFIED);
        pay.setPaymentInfoId(paymentInfo.getId());
        pay.setVersionNum(1);
        pay.setProjectId(paymentInfo.getProjectId());
        pay.setBuildingId(paymentInfo.getBuildingId());
        pay.setFloorId(paymentInfo.getFloorId());
        pay.setRoomId(paymentInfo.getRoomId());
        pay.setBu(Optional.ofNullable(paymentInfo.getBu()).filter(StringUtils::isNotBlank)
                .orElse(aptBill.map(AptBill::getBu).orElse(null)));
        pay.setUnit(Optional.ofNullable(paymentInfo.getUnit()).filter(StringUtils::isNotBlank)
                .orElse(aptBill.map(AptBill::getUnit).orElse(null)));
        pay.setAn8(Optional.ofNullable(paymentInfo.getAn8()).filter(StringUtils::isNotBlank)
                .orElse(aptBill.map(AptBill::getAn8).orElse(null)));
        pay.setAlph(Optional.ofNullable(paymentInfo.getAlph()).filter(StringUtils::isNotBlank)
                .orElse(aptBill.map(AptBill::getAlph).orElse(null)));
        pay.setDoco(Optional.ofNullable(paymentInfo.getDoco()).filter(StringUtils::isNotBlank)
                .orElse(aptBill.map(AptBill::getDoco).orElse(null)));
        pay.setPositionItem(paymentInfo.getPositionItem());
        pay.setComments(paymentInfo.getDescription());
        pay.setBillPayModule(Optional.ofNullable(paymentInfo.getBillPayModule()).orElse(BillPayModule.KERRY));
        pay.setCreateBy(paymentInfo.getCreateBy());
        pay.setFeeId(paymentInfo.getFeeId());
        pay.setFeeName(paymentInfo.getFeeName());
        pay.setFeeTax(paymentInfo.getFeeTax());
        pay.setFeeTaxAmount(feeTaxAmount);

        var save = aptPayRepository.save(pay);
        log.info("save apt pay record: {}", exceptionToNull(() -> objectMapper.writeValueAsString(save)));
    }

    // 描述字段样例：A2-2B, 1/8/9月管理费;2/3月水费
    private String generateDescription(List<AptBill> aptBills) {
        if (CollectionUtils.isEmpty(aptBills)) {
            log.info("save_apt_pay apt_bills_is_empty");
            return StringUtils.EMPTY;
        }

        log.info("save_apt_pay generate_description apt_bills: {}", JSONObject.toJSONString(aptBills));
        String aptBillUnit = aptBills.stream().map(AptBill::getUnit).distinct().collect(Collectors.joining(COMMA));
        String aptBillBuildingName = aptBills.stream().map(AptBill::getPositionItem).filter(Objects::nonNull)
                .map(PositionItemResponse::getBuildingName).filter(StringUtils::isNotEmpty)
                .distinct().collect(Collectors.joining(COMMA));
        Map<String, List<AptBill>> categoryAptBillsMap = aptBills.stream().collect(Collectors.groupingBy(AptBill::getCategory));

        StringBuilder descriptionSb = new StringBuilder();
        descriptionSb.append(aptBillBuildingName).append("-").append(aptBillUnit);
        categoryAptBillsMap.forEach((category, aptBillList) -> {
            String months = aptBillList.stream().map(AptBill::getMonth).distinct().sorted()
                    .map(String::valueOf).collect(Collectors.joining("/"));

            descriptionSb.append(COMMA).append(months).append("月").append(category);
        });
        return StringUtils.abbreviate(nonNullStringFunction.apply(descriptionSb.toString()), StringUtils.EMPTY, 30);
    }

    private BillPayChannel getBillPayChannel(AptPaymentInfo paymentInfo) {
        if (BillPaymentStatus.DIRECT_DEBIT_PAID.equals(paymentInfo.getPaymentStatus())) {
            return BillPayChannel.DIRECT_DEBITS;
        }
        if (BillPayModule.CASHIER.equals(paymentInfo.getBillPayModule())
                || BillPayModule.CASHIER_FEE.equals(paymentInfo.getBillPayModule())) {
            return paymentInfo.getPayChannel();
        }
        return BillPayChannel.ONLINE;
    }

    private BillPayChannel getBillPayChannel(BillPayModule payModule) {
        if (BillPayModule.CASHIER.equals(payModule) || BillPayModule.CASHIER_FEE.equals(payModule)) {
            return BillPayChannel.OFFLINE;
        }
        return BillPayChannel.ONLINE;
    }

}
