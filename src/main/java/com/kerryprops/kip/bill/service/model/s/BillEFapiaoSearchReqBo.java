package com.kerryprops.kip.bill.service.model.s;

import com.google.common.collect.ImmutableList;
import com.kerryprops.kip.bill.common.constants.AppConstants;
import com.kerryprops.kip.bill.common.enums.InvoiceRecordStatus;
import com.kerryprops.kip.bill.common.enums.InvoiceState;
import com.kerryprops.kip.bill.common.enums.SendStatus;
import com.kerryprops.kip.bill.common.jpa.QueryFilter;
import com.kerryprops.kip.bill.dao.entity.QEFapiaoBillInvoice;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.kerryprops.kip.bill.common.enums.BillInvoiceBizTypeEnum.SY_UTILITY;

@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BillEFapiaoSearchReqBo implements QueryFilter {

    private String mcu;

    private List<String> mcus;

    private String doco;

    private Integer messageStatus;

    private Integer emailStatus;

    private Integer smsStatus;

    private String an8;

    private String alpha;

    private String sellerName;

    private String companyCode;

    // 只返回已经开出票的数据
    private InvoiceRecordStatus invoiceRecordStatus = InvoiceRecordStatus.COMPLETED;

    // 发票状态: 正常、作废、红冲
    private String invoiceState;

    private String issuanceDate;

    private String invoiceNo;

    private String invoiceType;

    // refer to com.kerryprops.kip.bill.webservice.impl.StaffBillController.populateDataFields
    private List<String> buildingIds;

    private String projectId;

    // JDE
    private String bizType;

    private Collection<Long> ids;


    @Override
    public List<Optional<Predicate>> predicates() {
        Optional<Predicate> mcusPredicateOpt;
        if (Objects.nonNull(mcus) && mcus.isEmpty()) {
            mcusPredicateOpt = Optional.ofNullable(mcus).map(QEFapiaoBillInvoice.eFapiaoBillInvoice.mcu::in);
        } else {
            mcusPredicateOpt = Optional.ofNullable(mcus)
                    .flatMap(e -> e.stream().map(QEFapiaoBillInvoice.eFapiaoBillInvoice.mcu::contains)
                            .reduce(BooleanExpression::or));
        }

        Optional<Predicate> bizTypePredicateOpt = Optional.empty();
        if (StringUtils.isNotEmpty(bizType)) {
            if (SY_UTILITY.getBizCode().equalsIgnoreCase(bizType)) {
                bizTypePredicateOpt = Optional.of(bizType)
                        .map(QEFapiaoBillInvoice.eFapiaoBillInvoice.bizType::eq);
            } else {
                bizTypePredicateOpt = Optional.of(SY_UTILITY.getBizCode())
                        .map(QEFapiaoBillInvoice.eFapiaoBillInvoice.bizType::ne);
            }
        }

        ImmutableList.Builder builder = ImmutableList.<Optional<Predicate>>builder();
        builder
                .add(mcusPredicateOpt)
                .add(bizTypePredicateOpt)
                .add(Optional.ofNullable(mcu).map(QEFapiaoBillInvoice.eFapiaoBillInvoice.mcu::contains))
                .add(Optional.ofNullable(an8).map(QEFapiaoBillInvoice.eFapiaoBillInvoice.an8::eq))
                .add(Optional.ofNullable(doco).map(QEFapiaoBillInvoice.eFapiaoBillInvoice.doco::eq))
                .add(Optional.ofNullable(companyCode).map(QEFapiaoBillInvoice.eFapiaoBillInvoice.companyCode::eq))
                .add(Optional.ofNullable(invoiceNo).map(QEFapiaoBillInvoice.eFapiaoBillInvoice.invoiceNo::contains))
                .add(Optional.ofNullable(issuanceDate).map(QEFapiaoBillInvoice.eFapiaoBillInvoice.paperDrewDate::eq))
                .add(Optional.ofNullable(invoiceRecordStatus).map(QEFapiaoBillInvoice.eFapiaoBillInvoice.invoiceRecordStatus::eq))
                .add(Optional.ofNullable(invoiceState).map(InvoiceState::fromDesc).map(QEFapiaoBillInvoice.eFapiaoBillInvoice.state::eq))
                .add(Optional.ofNullable(messageStatus).map(SendStatus::fromIndex)
                        .map(QEFapiaoBillInvoice.eFapiaoBillInvoice.messageSendStatus::eq))
                .add(Optional.ofNullable(emailStatus).map(SendStatus::fromIndex)
                        .map(QEFapiaoBillInvoice.eFapiaoBillInvoice.emailSendStatus::eq))
                .add(Optional.ofNullable(smsStatus).map(SendStatus::fromIndex)
                        .map(QEFapiaoBillInvoice.eFapiaoBillInvoice.smsSendStatus::eq))
                .add(Optional.ofNullable(alpha).map(QEFapiaoBillInvoice.eFapiaoBillInvoice.purchaserName::contains))
                .add(Optional.ofNullable(sellerName).map(QEFapiaoBillInvoice.eFapiaoBillInvoice.sellerName::contains))
                .add(Optional.ofNullable(ids).map(QEFapiaoBillInvoice.eFapiaoBillInvoice.id::in))
                .add(Optional.ofNullable(invoiceType)/*.map(InvoiceTypeEnum::fromKipCode)*/
                        .map(QEFapiaoBillInvoice.eFapiaoBillInvoice.invoiceType::eq))
                .add(Optional.of(QEFapiaoBillInvoice.eFapiaoBillInvoice.invoiceType.notIn(AppConstants.NOT_SHOW_INVOICE_TYPES)))
                .add(Optional.of(QEFapiaoBillInvoice.eFapiaoBillInvoice.isDelete.eq(0)))
        ;

        /*if (StringUtils.isNotBlank(issuanceDate)) {
            try {
                ZonedDateTime queryFromDate = str2ZonedDatetimeFun.apply(issuanceDate + " 00:00:00");
                ZonedDateTime queryToDate = queryFromDate.plusDays(1);
                builder = builder.add(Optional.of(QEFapiaoBillInvoice.eFapiaoBillInvoice.createdTime
                        .between(queryFromDate, queryToDate)));
            } catch (Exception e) {
                log.error("e_fapiao_search_date error", e);
            }
        }*/
        return builder.build();
    }

}
