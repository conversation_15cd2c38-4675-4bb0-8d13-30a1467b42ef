package com.kerryprops.kip.bill.service.impl;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.DirectDebitsBatchStatus;
import com.kerryprops.kip.bill.common.exceptions.RestInvalidParamException;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.utils.PrimaryKeyUtil;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.config.PaymentConfigProps;
import com.kerryprops.kip.bill.dao.AptBillDirectDebitsBatchRepository;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptDirectDebitsBatchBillRepository;
import com.kerryprops.kip.bill.dao.AptPaymentInfoRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptBillDirectDebitsAgreement;
import com.kerryprops.kip.bill.dao.entity.AptBillDirectDebitsBatch;
import com.kerryprops.kip.bill.dao.entity.AptDirectDebitsBatchBill;
import com.kerryprops.kip.bill.dao.entity.AptPaymentTransInfo;
import com.kerryprops.kip.bill.dao.entity.QAptBill;
import com.kerryprops.kip.bill.dao.entity.QAptDirectDebitsBatchBill;
import com.kerryprops.kip.bill.dao.entity.QAptPaymentInfo;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.AptBillAgreementService;
import com.kerryprops.kip.bill.service.AptBillDirectDebitsBatchBillService;
import com.kerryprops.kip.bill.service.AptBillDirectDebitsBatchService;
import com.kerryprops.kip.bill.service.model.s.AptBillDirectDebitsBatchBo;
import com.kerryprops.kip.bill.service.model.s.AptBillSearchReqBo;
import com.kerryprops.kip.bill.service.model.s.AptDirectDebitsBatchBillSearchReqBo;
import com.kerryprops.kip.bill.webservice.vo.req.DirectDebitsBatchesCreationRequest;
import com.kerryprops.kip.bill.webservice.vo.resp.DirectDebitsBatchBillAmountResource;
import com.kerryprops.kip.bill.webservice.vo.resp.DirectDebitsBatchBillDetailResource;
import com.kerryprops.kip.bill.webservice.vo.resp.DirectDebitsBillBatchQueryResource;
import com.kerryprops.kip.hiveas.feign.dto.resp.ProjectRespDto;
import com.kerryprops.kip.hiveas.webservice.resource.resp.ProjectResp;
import com.kerryprops.kip.hiveas.webservice.vo.resp.BuildingResponseVo;
import com.kerryprops.kip.pmw.variables.PspName;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;
import jakarta.persistence.EntityManager;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.common.constants.AppConstants.DEFAULT_ZONE_ID;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.AGREEMENT_NOT_EXISTS;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.BATCH_NOT_EXISTS;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.CLOSING_MONTH_FORMAT_NOT_VALID;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.DIRECT_DEBITS_BILL_NOT_FOUND;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.DIRECT_DEBITS_BILL_NOT_VALID;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.DUPLICATED_WITHHOLDING_BATCH;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.ONLY_AWAIT_BATCH_DELETABLE;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.UN_SUPPORT_PUSH_DEBITS_BILL;
import static com.kerryprops.kip.bill.dao.entity.QAptBillDirectDebitsBatch.aptBillDirectDebitsBatch;
import static java.util.Optional.ofNullable;

@Slf4j
@Service
@AllArgsConstructor
public class AptBillDirectDebitsBatchServiceImpl implements AptBillDirectDebitsBatchService {

    private final Function<AptBillDirectDebitsBatch, DirectDebitsBillBatchQueryResource> toQueryResource = batch -> {
        DirectDebitsBillBatchQueryResource resource = new DirectDebitsBillBatchQueryResource();
        resource.setBatchNo(batch.getBatchNo());
        resource.setStatus(batch.getStatus());
        resource.setBillCount(batch.getBillCount());
        resource.setClosingMonth(batch.getClosingMonth());
        resource.setProjectId(batch.getProjectId());
        resource.setProjectName(batch.getProjectName());
        resource.setPspName(batch.getPspName());
        resource.setCreatedBy(batch.getCreatedBy());
        resource.setLastModifiedBy(batch.getLastModifiedBy());
        resource.setCreatedTime(batch.getCreatedTime());
        resource.setUpdatedTime(batch.getUpdatedTime());
        resource.setPaymentConfirmedCount(batch.getPaymentConfirmedCount());
        resource.setBillSentCount(batch.getBillSentCount());
        return resource;
    };

    private final HiveAsClient hiveAsClient;

    private final EntityManager entityManager;

    private final PaymentConfigProps paymentConfigs;

    private final AptBillRepository aptBillRepository;

    private final AptBillAgreementService agreementService;

    private final AptPaymentInfoRepository paymentInfoRepository;

    private final AptBillDirectDebitsBatchRepository batchRepository;

    private final AptBillDirectDebitsBatchBillService batchBillService;

    private final AptDirectDebitsBatchBillRepository batchBillRepository;

    @Override
    public Page<DirectDebitsBillBatchQueryResource> queryDirectDebitsBatches(AptBillDirectDebitsBatchBo bo
            , Pageable pageable) {

        Page<DirectDebitsBillBatchQueryResource> paged = batchRepository.findAll(bo.toPredicates(), pageable)
                .map(toQueryResource);
        List<String> batchNos = paged.stream().map(DirectDebitsBillBatchQueryResource::getBatchNo)
                .collect(Collectors.toList());

        List<Map<String, Object>> failedAmtLst = batchBillRepository.queryFailedAmount(batchNos);
        if (CollectionUtils.isNotEmpty(failedAmtLst)) {
            Map<String, BigDecimal> amtMap = failedAmtLst.stream()
                    .collect(Collectors.toMap(m -> (String) m.get("batch_no"), m -> (BigDecimal) m.get("sum_amt")));
            paged.forEach(res -> Optional.ofNullable(amtMap.get(res.getBatchNo()))
                    .ifPresent(res::setDirectDebitFailedAmount));
        }
        return paged;
    }

    @Override
    @Transactional
    public DirectDebitsBillBatchQueryResource deleteDirectDebitsBatch(String batchNo) {
        AptBillDirectDebitsBatch batch = batchRepository.findTopByBatchNo(batchNo);
        if (Objects.isNull(batch)) {
            throw new RestInvalidParamException(BATCH_NOT_EXISTS);
        }
        if (!Objects.equals(DirectDebitsBatchStatus.AWAIT, batch.getStatus())) {
            throw new RestInvalidParamException(ONLY_AWAIT_BATCH_DELETABLE);
        }
        if (Objects.equals(batch.getIsDel(), 1)) {
            return toQueryResource.apply(batch);
        }
        LoginUser loginUser = UserInfoUtils.getUser();
        String userId = loginUser.getUniqueUserId();
        batch.setLastModifiedBy(userId);
        batch.setIsDel(1);
        batch = batchRepository.save(batch);
        Collection<Long> billIds = batchBillRepository.findByBatchNo(batchNo).stream()
                .map(AptDirectDebitsBatchBill::getBillId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(billIds)) {
            List<AptBill> aptBills = aptBillRepository.findAllById(billIds);
            if (CollectionUtils.isNotEmpty(aptBills)) {
                aptBills.forEach(ab -> {
                    if (BillPaymentStatus.DIRECT_DEBIT_PAYING.equals(ab.getPaymentStatus())) {
                        ab.setPaymentStatus(BillPaymentStatus.TO_BE_PAID);
                    }
                });
                aptBillRepository.saveAll(aptBills);
            }
        }
        return toQueryResource.apply(batch);
    }

    @Override
    @Transactional
    public DirectDebitsBillBatchQueryResource createDirectDebitsBatch(DirectDebitsBatchesCreationRequest request) {
        Map<String, String> roomPspMap = agreementService.queryAllActiveAgreementRoomsAndPsp(request.getProjectId());
//        Collection<String> activeRooms = agreementService.queryAllActiveAgreementRooms(request.getProjectId());
        Collection<String> activeRooms = roomPspMap.keySet();
        if (CollectionUtils.isEmpty(activeRooms)) {
            throw new RestInvalidParamException(AGREEMENT_NOT_EXISTS);
        }
        int targetBillMoth = Integer.parseInt(request.getClosingMonth());
        Predicate predicate = QAptBill.aptBill.roomId.in(activeRooms).and(QAptBill.aptBill.deletedAt.eq(0))
                //.and(QAptBill.aptBill.status.eq(BillStatus.TO_BE_SENT))
                .and(QAptBill.aptBill.paymentStatus.in(List.of(BillPaymentStatus.TO_BE_PAID)))
                .and(QAptBill.aptBill.billMonth.loe(targetBillMoth));
        List<AptBill> aptBills = Lists.newLinkedList(aptBillRepository.findAll(predicate));
        if (CollectionUtils.isEmpty(aptBills)) {
            throw new RestInvalidParamException(DIRECT_DEBITS_BILL_NOT_FOUND);
        }

        LoginUser loginUser = UserInfoUtils.getUser();
        String userId = loginUser.getUniqueUserId();
        AptBillDirectDebitsBatch batch = new AptBillDirectDebitsBatch();
        batch.setIsDel(0);
        batch.setLastModifiedBy(userId);
        String batchNo = PrimaryKeyUtil.primaryKey(PrimaryKeyUtil.DIRECT_DEBITS_BATCH, null);
        batch.setBatchNo(batchNo);
        batch.setBillCount(aptBills.size());
        batch.setBillSentCount(0);
        batch.setClosingMonth(request.getClosingMonth());
        batch.setCreatedBy(userId);
        batch.setPaymentConfirmedCount(0);
        batch.setProjectId(request.getProjectId());
        String projectName = ofNullable(hiveAsClient.getProjectById(request.getProjectId()))
                .map(RespWrapVo::getData).map(ProjectResp::getProject).map(ProjectRespDto::getName).orElse(null);
        batch.setProjectName(projectName);
        batch.setPspName("alipay");
        batch.setStatus(DirectDebitsBatchStatus.AWAIT);
        batch = batchRepository.save(batch);
        // save batch bill
        Map<String, String> buildingToCoMap = Maps.newHashMap();
        Collection<AptDirectDebitsBatchBill> newBatchBills = aptBills.stream().map(ab -> {
            AptDirectDebitsBatchBill batchBill = new AptDirectDebitsBatchBill();
            batchBill.setBatchNo(batchNo);
            batchBill.setBillId(ab.getId());
            batchBill.setBillNo(ab.getBillNo());
            batchBill.setBuildingId(ofNullable(ab.getBuildingId()).orElse(Strings.EMPTY));
            batchBill.setRoomId(ofNullable(ab.getRoomId()).orElse(Strings.EMPTY));
            if (StringUtils.isBlank(buildingToCoMap.get(ab.getBuildingId()))) {
                BuildingResponseVo buildingResponseVo = hiveAsClient.getBuildingById(ab.getBuildingId());
                if (Objects.isNull(buildingResponseVo) || Objects.isNull(buildingResponseVo.getBuilding())) {
                    throw new RuntimeException("Co_not_configured for building " + ab.getBuildingId());
                }
                String propertyManagementCo = buildingResponseVo.getBuilding().getPropertyManagementCo();
                if (StringUtils.isEmpty(propertyManagementCo)) {
                    throw new RuntimeException("Co_not_configured for building " + ab.getBuildingId());
                }
                buildingToCoMap.put(ab.getBuildingId(), propertyManagementCo);
            }
            batchBill.setCompanyCode(ofNullable(buildingToCoMap.get(ab.getBuildingId())).orElse(Strings.EMPTY));
            batchBill.setPaymentInfoId(Strings.EMPTY);
            batchBill.setPspName(Optional.ofNullable(roomPspMap.get(ab.getRoomId())).orElse(Strings.EMPTY));
            return batchBill;
        }).collect(Collectors.toSet());
        batchBillRepository.saveAll(newBatchBills);
        aptBills.forEach(ab -> ab.setPaymentStatus(BillPaymentStatus.DIRECT_DEBIT_PAYING));
        aptBillRepository.saveAll(aptBills);
        return toQueryResource.apply(batch);
    }

    @Override
    public boolean validateDirectDebitsBatchCreation(DirectDebitsBatchesCreationRequest request) {
        String closingMonth = request.getClosingMonth();
        try {
            DateTimeFormatter.ofPattern("yyyyMM").parse(closingMonth);
        } catch (Exception e) {
            throw new RestInvalidParamException(CLOSING_MONTH_FORMAT_NOT_VALID);
        }
        if (Boolean.TRUE.equals(paymentConfigs.isDirectDebitsDailyUniqBatch())) {
            ZonedDateTime startOfDay = ZonedDateTime.now(DEFAULT_ZONE_ID).truncatedTo(ChronoUnit.DAYS);
            // concat predicates
            BooleanExpression predicate = aptBillDirectDebitsBatch.isDel.eq(0)
                    .and(aptBillDirectDebitsBatch.projectId.eq(request.getProjectId()))
                    .and(aptBillDirectDebitsBatch.createdTime.between(startOfDay, startOfDay.plusDays(1).minusSeconds(1)))
                    .and(aptBillDirectDebitsBatch.status.in(List.of(DirectDebitsBatchStatus.AWAIT
                            , DirectDebitsBatchStatus.SENT)));
            List<AptBillDirectDebitsBatch> batches = Lists.newLinkedList(batchRepository.findAll(predicate));
            if (CollectionUtils.isNotEmpty(batches)) {
                throw new RestInvalidParamException(DUPLICATED_WITHHOLDING_BATCH);
            }
        }
        return true;
    }

    @Override
    public Page<DirectDebitsBatchBillDetailResource> queryBillsInBatch(String batchNo
            , Pageable pageable, AptBillSearchReqBo searchReqBo) {
        return (Page<DirectDebitsBatchBillDetailResource>) doQueryBillsInBatch(batchNo, pageable, searchReqBo);
    }

    /**
     * for export excel
     */
    @Override
    public List<DirectDebitsBatchBillDetailResource> queryBillsInBatch(String batchNo, AptBillSearchReqBo searchReqBo) {
        return (List<DirectDebitsBatchBillDetailResource>) doQueryBillsInBatch(batchNo, null, searchReqBo);
    }

    @Override
    @Transactional
    public boolean deleteDirectDebitsBatchBills(String batchNo, List<String> billNos) {
        AptBillDirectDebitsBatch batch = batchRepository.findTopByBatchNo(batchNo);
        if (Objects.isNull(batch)) {
            throw new RestInvalidParamException(BATCH_NOT_EXISTS);
        }
        if (!DirectDebitsBatchStatus.AWAIT.equals(batch.getStatus())) {
            throw new RestInvalidParamException(ONLY_AWAIT_BATCH_DELETABLE);
        }
        List<AptDirectDebitsBatchBill> batchBills = batchBillRepository.findByBatchNoAndBillNoIn(batchNo, billNos);
        if (CollectionUtils.isEmpty(batchBills) || !Objects.equals(batchBills.size(), billNos.size())) {
            throw new RestInvalidParamException(DIRECT_DEBITS_BILL_NOT_VALID);
        }
        batchBillRepository.deleteAll(batchBills);
        log.info("delete_direct_debits_batch_bills batchNo: {}, bills: {}", batchNo, billNos);
        batchBillService.resetAptBillToBePaidStatus(batchBills);
        batch.setBillCount(batch.getBillCount() - batchBills.size());
        batch.setLastModifiedBy(UserInfoUtils.getUser().getUniqueUserId());
        batchRepository.save(batch);
        log.info("batch bill count updated");
        return true;
    }

    @Override
    public DirectDebitsBatchBillAmountResource calculateBatchBillAmount(String batchNo, AptBillSearchReqBo searchReqBo) {
        var batch = batchRepository.findTopByBatchNo(batchNo);
        if (Objects.isNull(batch)) {
            throw new RestInvalidParamException(BATCH_NOT_EXISTS);
        }
        DirectDebitsBatchBillAmountResource reqResource = new DirectDebitsBatchBillAmountResource();
        reqResource.setBatchNo(batchNo);
        reqResource.setStatus(batch.getStatus());

        Collection<Long> billIds;
        if (BillPaymentStatus.DIRECT_DEBIT_FAILED.equals(searchReqBo.getPaymentStatus())) {
            searchReqBo.setPaymentStatus(null);
            billIds = queryDirectDebitFailedBills(batchNo, searchReqBo);
        } else {
            billIds = batchBillRepository.getJpaQueryFactory()
                    .selectDistinct(QAptDirectDebitsBatchBill.aptDirectDebitsBatchBill.billId)
                    .from(QAptDirectDebitsBatchBill.aptDirectDebitsBatchBill)
                    .where(QAptDirectDebitsBatchBill.aptDirectDebitsBatchBill.batchNo.eq(batchNo)).fetch();
        }
        if (CollectionUtils.isEmpty(billIds)) {
            reqResource.setTotalAmount(BigDecimal.ZERO);
            return reqResource;
        }
        Predicate predicate = QAptBill.aptBill.id.in(billIds).and(searchReqBo.toPredicates());

        JPAQuery<BigDecimal> query = new JPAQuery<>(entityManager);
        Double sumAmount = ofNullable(query.select(QAptBill.aptBill.amt.sum())
                .from(QAptBill.aptBill).where(predicate).fetchOne()).orElse(0.0);
        // setting sum amount
        reqResource.setTotalAmount(BigDecimal.valueOf(sumAmount).setScale(2, RoundingMode.HALF_UP));
        return reqResource;
    }

    @Override
    @Transactional
    public List<DirectDebitsBillBatchQueryResource> lapsedCheck() {
        BooleanExpression predicate = aptBillDirectDebitsBatch.isDel.eq(0)
                .and(aptBillDirectDebitsBatch.status.eq(DirectDebitsBatchStatus.AWAIT));
        List<AptBillDirectDebitsBatch> batches = Lists.newLinkedList(batchRepository.findAll(predicate));
        if (CollectionUtils.isEmpty(batches)) {
            log.info("empty await batch set");
            return Collections.emptyList();
        }
        batches = batches.stream().filter(batch -> batch.getCreatedTime().truncatedTo(ChronoUnit.DAYS)
                .compareTo(ZonedDateTime.now().truncatedTo(ChronoUnit.DAYS)) != 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(batches)) {
            log.info("no current day await batch set");
            return Collections.emptyList();
        }
        for (AptBillDirectDebitsBatch eachBatch : batches) {
            eachBatch.setStatus(DirectDebitsBatchStatus.LAPSED);
            List<AptDirectDebitsBatchBill> batchBills = batchBillRepository.findByBatchNo(eachBatch.getBatchNo());
            if (CollectionUtils.isNotEmpty(batchBills)) {
                batchBillService.resetAptBillToBePaidStatus(batchBills);
            }
            batchRepository.save(eachBatch);
            log.info("update batch {} status to LAPSED", eachBatch.getBatchNo());
        }
        return batches.stream().map(toQueryResource).collect(Collectors.toList());
    }

    /**
     * 1、在C端产生缴费订单，按楼栋+单元合并产生一个订单
     * 2、按订单汇总后的金额发送至支付宝后缴费
     * 3、代扣批头状态更新为“已发送”
     */
    @Override
    @Transactional
    public DirectDebitsBillBatchQueryResource pushDirectDebitsBatchBills(String batchNo) {
        AptBillDirectDebitsBatch batch = batchRepository.findTopByBatchNo(batchNo);
        if (Objects.isNull(batch)) {
            throw new RestInvalidParamException(BATCH_NOT_EXISTS);
        }
        if (DirectDebitsBatchStatus.LAPSED.equals(batch.getStatus())) {
            throw new RestInvalidParamException(UN_SUPPORT_PUSH_DEBITS_BILL);
        }
        if (DirectDebitsBatchStatus.SENT.equals(batch.getStatus())) {
            return toQueryResource.apply(batch);
        }
        List<AptDirectDebitsBatchBill> batchBills = batchBillRepository.findByBatchNo(batchNo);
        if (CollectionUtils.isEmpty(batchBills)) {
            throw new RestInvalidParamException(DIRECT_DEBITS_BILL_NOT_FOUND);
        }
        // 执行代扣
        batchBillService.combineBillsByRoomAndCreatePaymentInfo(batch, batchBills);
        batch.setStatus(DirectDebitsBatchStatus.SENT);
        return toQueryResource.apply(batchRepository.save(batch));
    }

    private Iterable<DirectDebitsBatchBillDetailResource> doQueryBillsInBatch(String batchNo
            , Pageable pageable, AptBillSearchReqBo searchReqBo) {
        AptBillDirectDebitsBatch batch = batchRepository.findTopByBatchNo(batchNo);
        if (Objects.isNull(batch)) {
            throw new RestInvalidParamException(BATCH_NOT_EXISTS);
        }
        // query all bills in specific batchNo
        List<AptDirectDebitsBatchBill> batchBills;
        if (StringUtils.isNotBlank(searchReqBo.getPspName())) {
            batchBills = batchBillRepository.findByBatchNoAndPspName(batchNo, searchReqBo.getPspName());
        } else {
            batchBills = batchBillRepository.findByBatchNo(batchNo);
        }
        if (CollectionUtils.isEmpty(batchBills)) {
            return Objects.isNull(pageable) ? Collections.emptyList()
                    : new PageImpl<>(Collections.emptyList());
        }
        // query apt bill by billIds
        Collection<Long> billIds;
        if (BillPaymentStatus.DIRECT_DEBIT_FAILED.equals(searchReqBo.getPaymentStatus())) {
            searchReqBo.setPaymentStatus(null);
            billIds = queryDirectDebitFailedBills(batchNo, searchReqBo);
            if (CollectionUtils.isEmpty(billIds)) {
                return Objects.isNull(pageable) ? Collections.emptyList()
                        : new PageImpl<>(Collections.emptyList());
            }
        } else {
            billIds = batchBills.stream().map(AptDirectDebitsBatchBill::getBillId)
                    .collect(Collectors.toSet());
        }
        Predicate predicate = QAptBill.aptBill.id.in(billIds).and(searchReqBo.toPredicates());
        Iterable<DirectDebitsBatchBillDetailResource> iterableResources;
        if (Objects.nonNull(pageable)) {
            iterableResources = aptBillRepository.findAll(predicate, pageable)
                    .map(b -> BeanUtil.copy(b, DirectDebitsBatchBillDetailResource.class));
        } else {
            iterableResources = Lists.newLinkedList(aptBillRepository.findAll(predicate))
                    .stream().map(b -> BeanUtil.copy(b, DirectDebitsBatchBillDetailResource.class))
                    .collect(Collectors.toList());
        }
        iterableResources.forEach(r -> r.setBatchNo(batchNo));
        // setting companyCode info
        Map<String, String> billNoToCoMap = batchBills.stream()
                .collect(Collectors.toMap(AptDirectDebitsBatchBill::getBillNo
                        , AptDirectDebitsBatchBill::getCompanyCode, (a, b) -> b));
        Map<String, String> billNoToPspMap = batchBills.stream()
                .collect(Collectors.toMap(AptDirectDebitsBatchBill::getBillNo
                        , AptDirectDebitsBatchBill::getPspName, (a, b) -> b));
        iterableResources.forEach(r -> {
            r.setCompanyCode(billNoToCoMap.get(r.getBillNo()));
            r.setPspName(billNoToPspMap.get(r.getBillNo()));
        });

        // payment info
        Collection<String> paymentInfoIds = batchBills.stream().map(AptDirectDebitsBatchBill::getPaymentInfoId)
                .collect(Collectors.toSet());
        List<AptPaymentTransInfo> paymentInfos = paymentInfoRepository.findTransInfoByIdIn(paymentInfoIds);
        Collection<String> linkedPaymentAgreementNos = paymentInfos.stream().map(AptPaymentTransInfo::getAgreementNo)
                .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        List<AptBillDirectDebitsAgreement> linkedPaymentAgreements = agreementService
                .findAllByAgreementNos(linkedPaymentAgreementNos);
        Map<String, AptBillDirectDebitsAgreement> agreementIdentityMap = linkedPaymentAgreements.stream()
                .collect(Collectors.toMap(AptBillDirectDebitsAgreement::getAgreementNo, Function.identity()));
        Map<Long, String> billIdToPayInfoMap = batchBills.stream()
                .collect(Collectors.toMap(AptDirectDebitsBatchBill::getBillId
                        , AptDirectDebitsBatchBill::getPaymentInfoId, (a, b) -> b));
        Map<String, AptPaymentTransInfo> infoMap = paymentInfos.stream()
                .collect(Collectors.toMap(AptPaymentTransInfo::getPaymentOrderNo, Function.identity()));
        iterableResources.forEach(r -> {
            AptPaymentTransInfo tmpInfo = infoMap.get(billIdToPayInfoMap.get(r.getId()));
            if (Objects.nonNull(tmpInfo)) {
                // billing模块的支付订单号
                r.setPaymentOrderNo(tmpInfo.getPaymentOrderNo());
                // 支付平台的商户订单号
                r.setPaymentTransNo(tmpInfo.getPaymentTransNo());
                // 支付平台的平台流水号
                r.setPspTransNo(tmpInfo.getPspTransNo());
                r.setFailedReason(tmpInfo.getFailedReason());
                // TODO 是否需要限定条件
//                if (!BillPaymentStatus.DIRECT_DEBIT_STATUSES.contains(r.getPaymentStatus())) {
                r.setPaymentStatus(tmpInfo.getPaymentStatus());
//                }
                // agreement info
                AptBillDirectDebitsAgreement tmp = agreementIdentityMap.get(tmpInfo.getAgreementNo());
                if (Objects.nonNull(tmp)) {
                    r.setUserProfileId(tmp.getUserProfileId());
                    if (PspName.WECHATPAY.name().equalsIgnoreCase(tmp.getPspName())) {
                        r.setPspLogonId(StringUtils.abbreviateMiddle(tmp.getPspLogonId(), "******", 12));
                    } else {
                        r.setPspLogonId(tmp.getPspLogonId());
                    }
                    r.setAgreementNo(tmp.getAgreementNo());
                    r.setUserPhoneNumber(tmp.getUserPhoneNumber());
                }
            }
        });

        // setting agreement info
        Collection<String> roomIds = Lists.newLinkedList(iterableResources).stream()
                .filter(t -> StringUtils.isBlank(t.getAgreementNo()))
                .map(DirectDebitsBatchBillDetailResource::getRoomId).collect(Collectors.toSet());
        List<AptBillDirectDebitsAgreement> agreements = agreementService.findAllActiveByRoomIdIn(roomIds);
        Map<String, AptBillDirectDebitsAgreement> agreementMap = agreements.stream()
                .collect(Collectors.toMap(AptBillDirectDebitsAgreement::getRoomId, Function.identity(), (a, b) -> b));
        iterableResources.forEach(r -> {
            AptBillDirectDebitsAgreement tmp = agreementMap.get(r.getRoomId());
            if (Objects.nonNull(tmp)) {
                r.setUserProfileId(tmp.getUserProfileId());
                if (PspName.WECHATPAY.name().equalsIgnoreCase(tmp.getPspName())) {
                    r.setPspLogonId(StringUtils.abbreviateMiddle(tmp.getPspLogonId(), "******", 12));
                } else {
                    r.setPspLogonId(tmp.getPspLogonId());
                }
                r.setAgreementNo(tmp.getAgreementNo());
                r.setUserPhoneNumber(tmp.getUserPhoneNumber());
            }
        });
        return iterableResources;
    }

    private List<Long> queryDirectDebitFailedBills(String batchNo, AptBillSearchReqBo searchReqBo) {
        AptDirectDebitsBatchBillSearchReqBo billSearchReqBo = new AptDirectDebitsBatchBillSearchReqBo();
        billSearchReqBo.setBatchNo(batchNo);
        billSearchReqBo.setBuildingIds(searchReqBo.getBuildingIds());
        billSearchReqBo.setRoomIds(searchReqBo.getRoomIds());
        // query all bills in specific batchNo
        Predicate predicate = QAptPaymentInfo.aptPaymentInfo.id
                .eq(QAptDirectDebitsBatchBill.aptDirectDebitsBatchBill.paymentInfoId)
                .and(QAptPaymentInfo.aptPaymentInfo.paymentStatus.eq(BillPaymentStatus.DIRECT_DEBIT_FAILED))
                .and(billSearchReqBo.toPredicates());
        return batchBillRepository.getJpaQueryFactory()
                .select(QAptDirectDebitsBatchBill.aptDirectDebitsBatchBill.billId)
                .from(QAptDirectDebitsBatchBill.aptDirectDebitsBatchBill, QAptPaymentInfo.aptPaymentInfo)
                .where(predicate).fetch();
    }

}
