package com.kerryprops.kip.bill.service;

import com.kerryprops.kip.bill.dao.entity.AptBillDirectDebitsAgreement;
import com.kerryprops.kip.bill.feign.entity.CustomerUserResource;
import com.kerryprops.kip.bill.service.model.s.AptBillAgreementBo;
import com.kerryprops.kip.bill.webservice.vo.resp.DirectDebitsAgreementResource;
import com.kerryprops.kip.bill.webservice.vo.resp.PayableStaffAptBillRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.RoomAndUserSignResource;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffAptBillRespVo;
import com.kerryprops.kip.pmw.client.resource.AsyncAgreementStatusChangedResource.AsyncAgreementStatusChangedBodyResource;
import com.kerryprops.kip.pmw.client.resource.QueryAgreementOutputResource;
import com.kerryprops.kip.pmw.client.resource.TerminateAgreementOutputResource.TerminateAgreementOutputBodyResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface AptBillAgreementService {

    void agreementTerminated(TerminateAgreementOutputBodyResource outputBodyResource);

    void saveOrUpdateAgreement(AsyncAgreementStatusChangedBodyResource bodyResource);

    boolean syncAgreement(QueryAgreementOutputResource.PmwAgreementResource pmwAgreementResource);

    Page<DirectDebitsAgreementResource> queryAgreements(AptBillAgreementBo bo, Pageable pageable);

    List<PayableStaffAptBillRespVo> addAgreementInfo(List<StaffAptBillRespVo> respVos, String roomId);

    Optional<RoomAndUserSignResource> getUserAndRoomSignInfo(String roomId);

    List<AptBillDirectDebitsAgreement> findAllActiveByRoomIdIn(Collection<String> roomIds);

    Collection<String> queryAllActiveAgreementRooms(String projectId);

    Map<String, String> queryAllActiveAgreementRoomsAndPsp(String projectId);

    List<AptBillDirectDebitsAgreement> findAllByAgreementNos(Collection<String> agreementNos);

    AptBillDirectDebitsAgreement getTerminatedAgreement(String roomId);

    Optional<CustomerUserResource> determineAgreementUserProfile(String projectId
            , String roomId, String openId, String appId);

}
