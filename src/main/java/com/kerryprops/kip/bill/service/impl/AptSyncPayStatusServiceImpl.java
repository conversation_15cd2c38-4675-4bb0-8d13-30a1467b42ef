package com.kerryprops.kip.bill.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.common.utils.CommonUtil;
import com.kerryprops.kip.bill.config.SyncJdeConfig;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptJdeBillRepository;
import com.kerryprops.kip.bill.dao.JDEBillRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptJdeBill;
import com.kerryprops.kip.bill.dao.entity.JDEAptBill;
import com.kerryprops.kip.bill.dao.entity.QAptBill;
import com.kerryprops.kip.bill.dao.entity.QAptJdeBill;
import com.kerryprops.kip.bill.service.AptSyncPayStatusService;
import com.kerryprops.kip.bill.service.model.AptPaymentTimestamp;
import com.kerryprops.kip.bill.service.model.JdeConfirmResult;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IteratorUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AptSyncPayStatusServiceImpl implements AptSyncPayStatusService {

    private static final String QUERY_SQL_PREFIX = "select * from %s.F03B11";

    @Autowired
    private AptBillRepository aptBillRepository;

    @Autowired
    private AptJdeBillRepository aptJdeBillRepository;

    @Autowired
    private JDEBillRepository jdeBillRepository;

    @Override
    public JdeConfirmResult confirmAllPaidJdeItems(String buString) {
        //准备参数1：查询SQL语句
        String sql = String.format(QUERY_SQL_PREFIX, SyncJdeConfig.getJdeDbPrefix()) + " where RPPST='P' and RPMCU in (%s)";
        String querySQL = String.format(sql, buString);

        //准备参数2：未支付账单的查询表达式
        BooleanExpression aptBillExpression = QAptBill.aptBill.deletedAt.eq(0)
                .and(QAptBill.aptBill.paymentStatus.in(List.of(BillPaymentStatus.TO_BE_PAID, BillPaymentStatus.PART_PAID)))
                .and(QAptBill.aptBill.projectId.isNotNull())
                .and(QAptBill.aptBill.projectId.isNotEmpty());

        //准备参数3：JDE账单过滤谓词
        Predicate<AptJdeBill> aptJdeBillPredicate = new Predicate<>() {

            @Override
            public boolean test(AptJdeBill aptJdeBill) {
                return aptJdeBill != null && aptJdeBill.getJdeVerification() != 1;
            }
        };

        //实施查询处理
        JdeConfirmResult jdeConfirmResult = confirmAllJdeItems(querySQL, aptBillExpression, aptJdeBillPredicate);
        return jdeConfirmResult;
    }

    @Override
    public JdeConfirmResult confirmAllUnpaidJdeItems(String buString) {
        //准备参数1：查询SQL语句
        String sql = String.format(QUERY_SQL_PREFIX, SyncJdeConfig.getJdeDbPrefix()) + " where RPPST='A' and RPMCU in (%s)";
        String querySQL = String.format(sql, buString);

        //准备参数2：账单查询表达式
        BooleanExpression aptBillExpression = QAptBill.aptBill.deletedAt.eq(0)
                .and(QAptBill.aptBill.paymentStatus.in(Lists.newArrayList(BillPaymentStatus.TO_BE_PAID, BillPaymentStatus.PART_PAID)))
                .and(QAptBill.aptBill.status.in(List.of(BillStatus.TO_BE_PAID, BillStatus.JDE_VERIFIED)))
                .and(QAptBill.aptBill.projectId.isNotNull())
                .and(QAptBill.aptBill.projectId.isNotEmpty());

        //准备参数3：JDE账单的过滤谓词
        Predicate<AptJdeBill> aptJdeBillPredicate = new Predicate<>() {

            @Override
            public boolean test(AptJdeBill aptJdeBill) {
                return true;
            }
        };

        //实施查询处理
        JdeConfirmResult jdeConfirmResult = confirmAllJdeItems(querySQL, aptBillExpression, aptJdeBillPredicate);
        return jdeConfirmResult;
    }


    @Override
    public JdeConfirmResult confirmIncrementalJdeItems(String buString, AptPaymentTimestamp timestamp) {
        //检测参数合法性
        JdeConfirmResult jdeConfirmResult = new JdeConfirmResult();
        if (Objects.isNull(timestamp)) {
            log.info("stop incremental update since timestamp is null");
            return jdeConfirmResult;
        }

        //检索K+本地账单(支付中 + 在线支付)
        BooleanExpression b1 = QAptBill.aptBill.deletedAt.eq(0)
                .and(QAptBill.aptBill.paymentStatus.in(List.of(BillPaymentStatus.PAYING
                        , BillPaymentStatus.DIRECT_DEBIT_PAYING)))
                .and(QAptBill.aptBill.projectId.isNotNull())
                .and(QAptBill.aptBill.projectId.isNotEmpty());
        BooleanExpression b2 = QAptBill.aptBill.deletedAt.eq(0)
                .and(QAptBill.aptBill.status.in(List.of(BillStatus.ONLINE_PAID, BillStatus.CASHIER_PAID)))
                .and(QAptBill.aptBill.projectId.isNotNull())
                .and(QAptBill.aptBill.projectId.isNotEmpty());

        List<AptJdeBill> list = queryAptJdeBills(b1.or(b2));
        Map<String, List<AptJdeBill>> excludeDBBillMap = list.stream()
                .collect(Collectors.groupingBy(AptJdeBill::groupByBill));

        //查询JDE数据库
        String sql = String.format(QUERY_SQL_PREFIX, SyncJdeConfig.getJdeDbPrefix());
        sql += " where (RPUPMJ > %s or (RPUPMJ = %s and RPUPMT >= %s)) and RPMCU in (%s)";
        String querySQL = String.format(sql, timestamp.getDate(), timestamp.getDate(), timestamp.getTime(), buString);

        List<JDEAptBill> jdeAptBills = jdeBillRepository.queryPaymentStatus(querySQL);
        if (CollectionUtils.isEmpty(jdeAptBills)) {
            log.info("no JDE bill is updated");
            return jdeConfirmResult;
        }

        //过滤查询结果，记录最新时间戳和更新账单记录
        List<AptJdeBill> aptJdeBills = new LinkedList<>();
        AptPaymentTimestamp latestTimestamp = (AptPaymentTimestamp) timestamp.clone();
        jdeAptBills.forEach(jdeBill -> {
            //忽略支付中账单以及在线支付账单
            String billKey = jdeBill.groupByBill();
            if (excludeDBBillMap.containsKey(billKey)) {
                log.info("ignore paying bill or online paid bill. billKey is {}", billKey);
                return;
            }

            BooleanExpression expression = QAptJdeBill.aptJdeBill.rdDct.eq(jdeBill.getDocumentType())
                    .and(QAptJdeBill.aptJdeBill.rdDoc.eq(jdeBill.getDocumentCode()))
                    .and(QAptJdeBill.aptJdeBill.rdKco.eq(jdeBill.getCompanyCode()))
                    .and(QAptJdeBill.aptJdeBill.rdSfx.eq(jdeBill.getPaymentSfx()));
            Iterable<AptJdeBill> iterable = aptJdeBillRepository.findAll(expression);
            List<AptJdeBill> bills = Optional.ofNullable(iterable)
                    .map(e -> IteratorUtils.toList(e.iterator())).orElse(null);
            if (CollectionUtils.isEmpty(bills)) {
                log.info("no local Jde bill is found");
                return;
            }

            //更新状态
            AptJdeBill aptJdeBill = bills.get(0);
            if (updateJdeBillPaymentStatus(aptJdeBill, jdeBill)) {
                aptJdeBills.add(aptJdeBill);
                latestTimestamp.updateToLatest(jdeBill.getUpdatedDate(), jdeBill.getUpdatedTime());
            }

        });

        jdeConfirmResult.setAptJdeBills(aptJdeBills);
        jdeConfirmResult.setTimestamp(latestTimestamp);
        return jdeConfirmResult;
    }

    @Override
    public AptPaymentTimestamp confirmPayingBillTimestamp() {
        AptPaymentTimestamp timestamp = null;

        //查询支付中账单
        BooleanExpression aptBillExpression = QAptBill.aptBill.deletedAt.eq(0)
                .and(QAptBill.aptBill.paymentStatus.in(Lists.newArrayList(BillPaymentStatus.PAYING)))
                .and(QAptBill.aptBill.projectId.isNotNull())
                .and(QAptBill.aptBill.projectId.isNotEmpty());
        List<AptJdeBill> aptJdeBills = queryAptJdeBills(aptBillExpression);
        if (CollectionUtils.isEmpty(aptJdeBills)) {
            log.info("no paying jde bills are found");
            return timestamp;
        }

        //获取查询结果中的最小时间戳
        for (AptJdeBill aptJdeBill : aptJdeBills) {

            long rdDoc = aptJdeBill.getRdDoc();
            String rdKco = aptJdeBill.getRdKco();
            String rdDct = aptJdeBill.getRdDct();
            String rdSfx = aptJdeBill.getRdSfx();
            log.info("query payment status. {}-{}-{}-{}", rdDoc, rdDct, rdKco, rdSfx);

            List<JDEAptBill> jdeAptBills = jdeBillRepository.findAllByCombineKeys(rdDoc, rdKco, rdDct, rdSfx);
            if (CollectionUtils.isEmpty(jdeAptBills)) {
                log.info("no validate payment record found.{}-{}-{}-{}", rdDoc, rdDct, rdKco, rdSfx);
                continue;
            }

            JDEAptBill jdeAptBill = jdeAptBills.get(0);
            long updatedDate = jdeAptBill.getUpdatedDate();
            long updatedTime = jdeAptBill.getUpdatedTime();
            if (timestamp == null) {
                timestamp = new AptPaymentTimestamp(updatedDate, updatedTime);
            } else {
                timestamp.updateToEarlier(updatedDate, updatedTime);
            }
        }
        log.info("paying timestamp is {}", timestamp);

        return timestamp;
    }

    @Override
    public boolean updateJdeBillPaymentStatus(AptJdeBill aptJdeBill, JDEAptBill jdeAptBill) {
        if (Objects.isNull(aptJdeBill)) {
            log.info("fail to update payment status for null AptJdeBill");
            return false;
        }
        if (Objects.isNull(jdeAptBill)) {
            log.info("fail to update payment status for null JdePayStatus");
            return false;
        }

        boolean updated = false;
        // JDE 为「已支付」
        if ("P".equalsIgnoreCase(jdeAptBill.getPaymentStatus())) {
            if (aptJdeBill.getJdeVerification() != 1) {
                updated = true;
                aptJdeBill.setJdeVerification(1);
                aptJdeBill.setJdeVerificationTime(new Date());
                aptJdeBill.setRdUpmj(jdeAptBill.getUpdatedDate());
                aptJdeBill.setRdUpmt(String.valueOf(jdeAptBill.getUpdatedTime()));
            }
        // JDE 为「未支付」
        } else if ("A".equalsIgnoreCase(jdeAptBill.getPaymentStatus())) {
            AptPaymentTimestamp jdeTS = new AptPaymentTimestamp(jdeAptBill.getUpdatedDate(), jdeAptBill.getUpdatedTime());
            AptPaymentTimestamp localTS = new AptPaymentTimestamp(aptJdeBill.getRdUpmj()
                    , CommonUtil.parseLongQuietly(aptJdeBill.getRdUpmt()));
            // local的时间 小于 JDE时间，则：
            if (localTS.compareTo(jdeTS) < 0) {
                updated = true;
                aptJdeBill.setJdeVerification(0);
                aptJdeBill.setJdeVerificationTime(null);
                aptJdeBill.setRdUpmj(jdeAptBill.getUpdatedDate());
                aptJdeBill.setRdUpmt(String.valueOf(jdeAptBill.getUpdatedTime()));
                aptJdeBill.setRdAg(Double.valueOf(jdeAptBill.getUnpaidAmount()));
            }
        }
        return updated;
    }

    private JdeConfirmResult confirmAllJdeItems(String querySQL, BooleanExpression aptBillExpression,
                                                Predicate<AptJdeBill> aptJdeBillPredicate) {
        //效验参数合法性
        JdeConfirmResult jdeConfirmResult = new JdeConfirmResult();
        if (Objects.isNull(aptJdeBillPredicate)) {
            log.info("param error. aptJdeBillPredicate is null");
            return jdeConfirmResult;
        }

        //检索JDE中间表以获取账单的支付状态
        List<JDEAptBill> jdeAptBills = jdeBillRepository.queryPaymentStatus(querySQL);
        if (CollectionUtils.isEmpty(jdeAptBills)) {
            log.info("no jde payment status is found");
            return jdeConfirmResult;
        }

        //检索K+本地账单
        List<AptJdeBill> aptJdeBills = queryAptJdeBills(aptBillExpression);
        if (CollectionUtils.isEmpty(aptJdeBills)) {
            log.info("no jde bill is found");
            return jdeConfirmResult;
        }

        //查询「真·最早时间戳」，避免未匹配到K+合同时则返回当前时间的时间戳
        AptPaymentTimestamp timestamp = new AptPaymentTimestamp();
        jdeAptBills.forEach(jdeBill -> {
            timestamp.updateToEarlier(jdeBill.getUpdatedDate(), jdeBill.getUpdatedTime());
        });

        //刷新K+账单的支付状态
        List<AptJdeBill> aptJdeBillList = new LinkedList<>();
        Map<String, List<JDEAptBill>> groupedJDEBills = jdeAptBills.stream()
                .collect(Collectors.groupingBy(JDEAptBill::groupByBill));
        Map<String, List<AptJdeBill>> groupedAptJdeBills = aptJdeBills.stream().filter(aptJdeBillPredicate)
                .collect(Collectors.groupingBy(AptJdeBill::groupByBill));
        for (String bill : groupedAptJdeBills.keySet()) {
            List<JDEAptBill> jdeAptBillList = groupedJDEBills.get(bill);
            if (CollectionUtils.isEmpty(jdeAptBillList)) {
                continue;
            }
            AptJdeBill aptJdeBill = groupedAptJdeBills.get(bill).get(0);
            JDEAptBill jdeAptBill = jdeAptBillList.get(0);
            if (updateJdeBillPaymentStatus(aptJdeBill, jdeAptBill)) {
                log.info("bill={}, {}", bill, JSONObject.toJSONString(jdeAptBill));
                aptJdeBillList.add(aptJdeBill);
                timestamp.updateToLatest(jdeAptBill.getUpdatedDate(), jdeAptBill.getUpdatedTime());
            }
        }

        jdeConfirmResult.setTimestamp(timestamp);
        jdeConfirmResult.setAptJdeBills(aptJdeBillList);
        return jdeConfirmResult;
    }

    private List<AptJdeBill> queryAptJdeBills(BooleanExpression aptBillBooleanExpression) {
        List<AptJdeBill> aptJdeBills = new LinkedList<>();
        if (Objects.isNull(aptBillBooleanExpression)) {
            log.info("aptBillBooleanExpression is null");
            return aptJdeBills;
        }

        Iterable<AptBill> aptBillIterable = aptBillRepository.findAll(aptBillBooleanExpression);
        List<AptBill> aptBills = Optional.ofNullable(aptBillIterable)
                .map(Lists::newLinkedList).orElse(Lists.newLinkedList());
        if (CollectionUtils.isEmpty(aptBills)) {
            log.info("no target apt bills are found");
            return aptJdeBills;
        }

        List<String> billNos = aptBills.stream().map(AptBill::getBillNo).collect(Collectors.toList());
        Iterable<AptJdeBill> jdeBillIterable = aptJdeBillRepository.findAll(QAptJdeBill.aptJdeBill.billNumber.in(billNos));
        return Optional.ofNullable(jdeBillIterable).map(Lists::newLinkedList).orElse(Lists.newLinkedList());
    }

}
