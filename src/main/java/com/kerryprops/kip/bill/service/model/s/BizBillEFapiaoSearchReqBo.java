package com.kerryprops.kip.bill.service.model.s;

import com.google.common.collect.ImmutableList;
import com.kerryprops.kip.bill.common.constants.AppConstants;
import com.kerryprops.kip.bill.common.enums.InvoiceRecordStatus;
import com.kerryprops.kip.bill.common.enums.InvoiceState;
import com.kerryprops.kip.bill.common.enums.SendStatus;
import com.kerryprops.kip.bill.common.jpa.QueryFilter;
import com.kerryprops.kip.bill.dao.entity.QEFapiaoBillInvoice;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static com.kerryprops.kip.bill.common.enums.BillInvoiceBizTypeEnum.SY_UTILITY;

@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BizBillEFapiaoSearchReqBo implements QueryFilter {

    private List<String> mcus;

    private String doco;

    private Integer messageStatus;

    private Integer emailStatus;

    private Integer smsStatus;

    private String an8;

    private String alpha;

    private String companyCode;

    // 只返回已经开出票的数据
    private InvoiceRecordStatus invoiceStatus = InvoiceRecordStatus.COMPLETED;

    // 发票状态: 正常、作废、红冲
    private String invoiceState;

    private String issuanceDateFrom;

    private String issuanceDateTo;

    private String invoiceNo;

    private String invoiceType;

    // refer to com.kerryprops.kip.bill.webservice.impl.StaffBillController.populateDataFields
    private List<String> buildingIds;

    private String projectId;

    // JDE
    private String bizType;

    private Collection<Long> ids;

    private Long id;

    private Set<String> docos;

    private Set<String> an8s;

    private Set<String> alphs;

    @Override
    public List<Optional<Predicate>> predicates() {
        Optional<Predicate> bizTypePredicateOpt = Optional.empty();
        if (StringUtils.isNotEmpty(bizType)) {
            if (SY_UTILITY.getBizCode().equalsIgnoreCase(bizType)) {
                bizTypePredicateOpt = Optional.of(bizType)
                        .map(QEFapiaoBillInvoice.eFapiaoBillInvoice.bizType::eq);
            } else {
                bizTypePredicateOpt = Optional.of(SY_UTILITY.getBizCode())
                        .map(QEFapiaoBillInvoice.eFapiaoBillInvoice.bizType::ne);
            }
        }

        ImmutableList.Builder builder = ImmutableList.<Optional<Predicate>>builder();
        builder
                .add(bizTypePredicateOpt)
                .add(Optional.ofNullable(docos).map(QEFapiaoBillInvoice.eFapiaoBillInvoice.doco::in))
                .add(Optional.ofNullable(an8s).map(QEFapiaoBillInvoice.eFapiaoBillInvoice.an8::in))
//                .add(Optional.ofNullable(alphs).map(QEFapiaoBillInvoice.eFapiaoBillInvoice.::in))
                .add(Optional.ofNullable(invoiceStatus).map(QEFapiaoBillInvoice.eFapiaoBillInvoice.invoiceRecordStatus::eq))
                .add(Optional.ofNullable(invoiceState).map(InvoiceState::fromDesc).map(QEFapiaoBillInvoice.eFapiaoBillInvoice.state::eq))
                .add(Optional.ofNullable(mcus).map(QEFapiaoBillInvoice.eFapiaoBillInvoice.mcu::in))
                .add(Optional.ofNullable(doco).map(QEFapiaoBillInvoice.eFapiaoBillInvoice.doco::eq))
//                .add(Optional.ofNullable(messageStatus).map(SendStatus::fromIndex)
//                        .map(QEFapiaoBillInvoice.eFapiaoBillInvoice.messageSendStatus::eq))
//                .add(Optional.ofNullable(emailStatus).map(SendStatus::fromIndex)
//                        .map(QEFapiaoBillInvoice.eFapiaoBillInvoice.emailSendStatus::eq))
//                .add(Optional.ofNullable(smsStatus).map(SendStatus::fromIndex)
//                        .map(QEFapiaoBillInvoice.eFapiaoBillInvoice.smsSendStatus::eq))
                .add(Optional.ofNullable(an8).map(QEFapiaoBillInvoice.eFapiaoBillInvoice.an8::eq))
                .add(Optional.ofNullable(alpha).map(QEFapiaoBillInvoice.eFapiaoBillInvoice.purchaserName::likeIgnoreCase))
                .add(Optional.ofNullable(companyCode).map(QEFapiaoBillInvoice.eFapiaoBillInvoice.companyCode::eq))
                .add(Optional.ofNullable(ids).map(QEFapiaoBillInvoice.eFapiaoBillInvoice.id::in))
                .add(Optional.ofNullable(id).map(QEFapiaoBillInvoice.eFapiaoBillInvoice.id::eq))
                .add(Optional.ofNullable(invoiceNo).map(QEFapiaoBillInvoice.eFapiaoBillInvoice.invoiceNo::contains))
                .add(Optional.of(QEFapiaoBillInvoice.eFapiaoBillInvoice.invoiceType.notIn(AppConstants.NOT_SHOW_INVOICE_TYPES)))
                .add(Optional.of(QEFapiaoBillInvoice.eFapiaoBillInvoice.isDelete.eq(0)))
        ;
        // 时间段筛选
        if (StringUtils.isNotBlank(issuanceDateFrom) || StringUtils.isNotBlank(issuanceDateTo)) {
            try {
                /*ZonedDateTime queryFromDate;
                ZonedDateTime queryToDate;
                // set from date
                if (StringUtils.isNotBlank(issuanceDateFrom)) {
                    queryFromDate = str2ZonedDatetimeFun.apply(issuanceDateFrom);
                } else {
                    queryFromDate = str2ZonedDatetimeFun.apply("1970-01-01 00:00:00");
                }
                // set to date
                if (StringUtils.isNotBlank(issuanceDateTo)) {
                    queryToDate = str2ZonedDatetimeFun.apply(issuanceDateTo + " 23:59:59");
                } else {
                    queryToDate = ZonedDateTime.now();
                }
                builder = builder.add(Optional.of(QEFapiaoBillInvoice.eFapiaoBillInvoice
                        .createdTime.between(queryFromDate, queryToDate)));*/
                if (StringUtils.isBlank(issuanceDateFrom)) {
                    issuanceDateFrom = "1970-01-01";
                }
                if (StringUtils.isBlank(issuanceDateTo)) {
                    issuanceDateTo = ZonedDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                }
                builder = builder.add(Optional.of(QEFapiaoBillInvoice.eFapiaoBillInvoice
                        .paperDrewDate.between(issuanceDateFrom, issuanceDateTo)));
            } catch (Exception e) {
                log.error("e_fapiao_search_date error", e);
            }
        }
        // 只有推送过的发票才在B端显示
        BooleanExpression be = QEFapiaoBillInvoice.eFapiaoBillInvoice.messageSendStatus.in(SendStatus.SuccessStatuses)
                .or(QEFapiaoBillInvoice.eFapiaoBillInvoice.emailSendStatus.in(SendStatus.SuccessStatuses))
                .or(QEFapiaoBillInvoice.eFapiaoBillInvoice.smsSendStatus.in(SendStatus.SuccessStatuses));
        builder = builder.add(Optional.of(be));

        return builder.build();
    }

}
