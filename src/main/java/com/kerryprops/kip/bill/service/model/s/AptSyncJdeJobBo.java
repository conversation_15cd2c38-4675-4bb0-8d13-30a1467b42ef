package com.kerryprops.kip.bill.service.model.s;

import com.kerryprops.kip.bill.common.enums.JobType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * 名   称：apt_sync_jde_job
 * 描   述：
 * 作   者：David Wei
 * 时   间：2021/09/06 10:39:31
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AptSyncJdeJobBo {

    private Long id;

    private Integer status;

    private JobType type;

    private Integer localCnt;

    private Integer remoteCnt;

    private String errorMsg;

    private Date startTime;

    private Date endTime;

    private String createBy;

    private Date createTime;

    private Date updateTime;


}
