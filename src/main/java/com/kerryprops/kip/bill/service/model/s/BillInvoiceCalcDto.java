package com.kerryprops.kip.bill.service.model.s;

import com.kerryprops.kip.bill.common.enums.BillPayModule;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.PaymentCateEnum;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR> 2024-01-15 15:53:15
 **/
@Getter
@Setter
public class BillInvoiceCalcDto {

    private String paymentInfoId;

    private Double amt;

    private String projectId;

    private String buildingId;

    private BigDecimal advanceAmount;

    private PaymentCateEnum paymentCate;

    private BillPayModule billPayModule;

    private String cancelType;

    private BillPaymentStatus paymentStatus;

    private Long feeId;

    public static BillInvoiceCalcDto of(AptPay pay) {
        BillInvoiceCalcDto dto = new BillInvoiceCalcDto();
        dto.setPaymentStatus(BillPaymentStatus.PAID);

        dto.setPaymentInfoId(pay.getPaymentInfoId());
        dto.setAmt(pay.getTotalAmt());
        dto.setProjectId(pay.getProjectId());
        dto.setBuildingId(pay.getBuildingId());
        dto.setAdvanceAmount(pay.getAdvanceAmount());
        dto.setPaymentCate(PaymentCateEnum.valueOf(pay.getPaymentCate()));
        dto.setBillPayModule(pay.getBillPayModule());
        dto.setFeeId(pay.getFeeId());
        return dto;
    }

    public static BillInvoiceCalcDto of(AptPaymentInfo paymentInfo) {
        BillInvoiceCalcDto bo = new BillInvoiceCalcDto();
        bo.setPaymentInfoId(paymentInfo.getId());
        bo.setAmt(paymentInfo.getAmt());
        bo.setProjectId(paymentInfo.getProjectId());
        bo.setBuildingId(paymentInfo.getBuildingId());
        bo.setAdvanceAmount(paymentInfo.getAdvanceAmount());
        bo.setPaymentCate(paymentInfo.getPaymentCate());
        bo.setBillPayModule(paymentInfo.getBillPayModule());
        bo.setPaymentStatus(paymentInfo.getPaymentStatus());
        bo.setFeeId(paymentInfo.getFeeId());
        return bo;
    }

}
