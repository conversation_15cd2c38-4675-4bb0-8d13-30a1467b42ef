package com.kerryprops.kip.bill.service;

import com.kerryprops.kip.bill.dao.entity.EFapiaoJDEBill;
import com.kerryprops.kip.bill.webservice.vo.req.CallBackKerryInvoiceMainVo;

import java.util.List;

/**
 * e-fapiao：JDE侧数据Service接口
 *
 * <AUTHOR>
 * @Date 2023-3-28
 */
public interface EFapiaoJdeBillService {

    List<EFapiaoJDEBill> queryByCompanyCodeAndMonthAndBu(String companyCode, String startDate, String bus);

    String queryMaxDateByCompanyCodeAndBu(String companyCode, String bus);

    EFapiaoJDEBill queryBySalesBillNo(String salesBillNo);

    /**
     * 标记账单已读，即已提交开票申请
     */
    void writeBackUploaded(EFapiaoJDEBill eFapiaoJdeBill);

    /**
     * 开票完成，发票信息回写JDE
     */
    void writeBackInvoice2Jde(CallBackKerryInvoiceMainVo mainVo);

}
