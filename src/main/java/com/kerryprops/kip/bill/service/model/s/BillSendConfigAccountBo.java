package com.kerryprops.kip.bill.service.model.s;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class BillSendConfigAccountBo {

    private Long configId;

    private String tenantManagerId;

    private String emailUsername;

    private String phoneNumber;

    private String loginNo;

}
