package com.kerryprops.kip.bill.service;

import com.kerryprops.kip.bill.webservice.vo.req.CashierAptPaySearchRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierOfflinePayRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierQRCodePaymentRequest;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierAptPaymentInfoResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierOfflinePayResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierPaymentReceiptResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierPaymentTransactionResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierQRCodePaymentResource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR> 2023-11-24 09:39:39
 **/
public interface CounterCashierService {

    CashierOfflinePayResource offlinePay(CashierOfflinePayRequest request);

    CashierQRCodePaymentResource counterCashierQRCodePayment(CashierQRCodePaymentRequest request);

    CashierAptPaymentInfoResource queryCashierPaymentInfo(String paymentInfoId);

    Page<CashierPaymentTransactionResource> queryCashierAptPays(CashierAptPaySearchRequest searchRequest
            , Pageable pageable);

    void exportCashierAptPays(CashierAptPaySearchRequest searchRequest, HttpServletResponse response);

    CashierPaymentReceiptResource acquirePaymentReceipt(String paymentInfoId);

}
