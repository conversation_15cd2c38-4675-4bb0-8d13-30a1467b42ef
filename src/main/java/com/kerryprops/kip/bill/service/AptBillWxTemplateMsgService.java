package com.kerryprops.kip.bill.service;

import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.feign.entity.PaymentConfirmedCommand;
import com.kerryprops.kip.pmw.client.resource.AsynPaymentResultResource;

import java.util.function.Consumer;

public interface AptBillWxTemplateMsgService {

    void sendPaymentResultNotice(AptPaymentInfo aptPaymentInfo
            , AsynPaymentResultResource.AsynPaymentResultBodyResource backResource
            , Consumer<PaymentConfirmedCommand> commandConsumer);

    void sendDirectPaymentFailedMsg(AptPaymentInfo aptPaymentInfo
            , AsynPaymentResultResource.AsynPaymentResultBodyResource backResource);

}
