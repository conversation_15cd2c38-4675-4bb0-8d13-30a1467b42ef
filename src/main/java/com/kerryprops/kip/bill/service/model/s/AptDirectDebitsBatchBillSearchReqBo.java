package com.kerryprops.kip.bill.service.model.s;

import com.google.common.collect.ImmutableList;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.jpa.QueryFilter;
import com.kerryprops.kip.bill.dao.entity.QAptDirectDebitsBatchBill;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.querydsl.core.types.Predicate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Optional;

/**
 * 账单查询的 view
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class AptDirectDebitsBatchBillSearchReqBo implements QueryFilter {

    private String batchNo;

    private List<String> buildingIds;

    private List<String> roomIds;

    @Override
    public List<Optional<Predicate>> predicates() {
        ImmutableList.Builder<Optional<Predicate>> builder = ImmutableList.builder();
        builder.add(Optional.ofNullable(batchNo).map(QAptDirectDebitsBatchBill.aptDirectDebitsBatchBill.batchNo::eq))
                .add(Optional.ofNullable(maxBindingScope()).map(QAptDirectDebitsBatchBill.aptDirectDebitsBatchBill.buildingId::in))
                .add(Optional.ofNullable(buildingIds).map(QAptDirectDebitsBatchBill.aptDirectDebitsBatchBill.buildingId::in))
                .add(Optional.ofNullable(roomIds).map(QAptDirectDebitsBatchBill.aptDirectDebitsBatchBill.roomId::in))
        ;
        return builder.build();
    }

    public List<String> maxBindingScope() {
        LoginUser loginUser = UserInfoUtils.getUser();
        if (loginUser == null) {
            throw new RuntimeException("not login.");
        }
        if (Boolean.TRUE.equals(loginUser.isSuperAdmin())) {
            return null;
        }
        return loginUser.toBuildingIdList();
    }

}
