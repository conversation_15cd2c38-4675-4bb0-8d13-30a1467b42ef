package com.kerryprops.kip.bill.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.kerryprops.kip.bill.common.enums.FeeTypeEnum;
import com.kerryprops.kip.bill.common.enums.InvoiceTypeEnum;
import com.kerryprops.kip.bill.common.enums.SmsContentEnum;
import com.kerryprops.kip.bill.common.utils.StringFormatUtils;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.dao.entity.EFapiaoBillInvoice;
import com.kerryprops.kip.bill.dao.entity.InvoiceRecord;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.clients.MessageClient;
import com.kerryprops.kip.bill.feign.entity.CreateShortUrlCommand;
import com.kerryprops.kip.bill.feign.entity.CreateShortUrlResource;
import com.kerryprops.kip.bill.feign.entity.SendSmsV2Command;
import com.kerryprops.kip.bill.feign.entity.SmsResultVo;
import com.kerryprops.kip.bill.webservice.vo.req.CallBackKerryInvoiceMainVo;
import com.kerryprops.kip.hiveas.feign.dto.resp.BuildingRespDto;
import com.kerryprops.kip.hiveas.webservice.vo.resp.BuildingResponseVo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.common.enums.BillInvoiceBizTypeEnum.getInvoiceBizDesc;
import static com.kerryprops.kip.bill.common.utils.BillingFun.exceptionToNull;
import static com.kerryprops.kip.bill.log4j.BSConversationFilter.setLoggingContext;

@Slf4j
@Service
public class AptInvoiceSmsService {

    @Autowired
    MessageClient unifiedMessageClient;

    @Autowired
    HiveAsClient hiveAsClient;

    @Autowired
    private ObjectMapper objectMapper;

    @Async
    public void sendSms(AptPaymentInfo aptPaymentInfo, InvoiceRecord invoiceRecord, CallBackKerryInvoiceMainVo mainVo
            , String invokedInvoiceCode, String invokedInvoiceNo, String convId) {
        setLoggingContext(convId, null);
        log.info("send_sms_for_invoice, payment info id: {}, invoice record: {}"
                , aptPaymentInfo.getId(), exceptionToNull(() -> objectMapper.writeValueAsString(invoiceRecord)));

        String email = invoiceRecord.getEmail();
        if (StringUtils.isNotBlank(email) && StringFormatUtils.isEmailFormat(email)) {
            // 如果是正常的email，则由票易通发邮件给用户！？
            log.info("send_sms_for_invoice EMAIL: [{}], Matched ------", email);
            return;
        }

        // 是否已经发送过短信
        if (List.of(InvoiceTypeEnum.QC.getCode(), InvoiceTypeEnum.QS.getCode()
                        , InvoiceTypeEnum.CZ.getCode(), InvoiceTypeEnum.SZ.getCode())
                .contains(mainVo.getInvoiceType())) {
            // 数电场景
            isAlreadySendFullDigital(invokedInvoiceNo, mainVo, invoiceRecord);
        } else {
            // 税控场景
            isAlreadySend(invokedInvoiceCode, invokedInvoiceNo, mainVo, invoiceRecord);
        }

        if (StringUtils.isEmpty(invoiceRecord.getPhone())) {
            log.warn("send_sms_for_invoice invoice_record_phone_is_empty");
            return;
        }

        if (StringFormatUtils.isEmailFormat(invoiceRecord.getPhone())) {
            log.warn("send_sms_for_invoice invoice_record_phone_is_email");
            return;
        }

        List<String> phones = List.of(invoiceRecord.getPhone());

        Map<String, Object> params = new HashMap<>(8);
        params.put("name", invoiceRecord.getIssuer());
        params.put("type", FeeTypeEnum.PROPERTY_MANAGEMENT.getDesc());
        params.put("orderNo", invoiceRecord.getOrderNo());
        String projectId = aptPaymentInfo.getProjectId();
        String pdfUrl = mainVo.getPdfUrl();
        pdfUrl = createShortUrl(phones, projectId, pdfUrl);
        params.put("url", pdfUrl);
        String content = getContent(params);

        try {
            SendSmsV2Command sendSmsV2Command = new SendSmsV2Command();
            sendSmsV2Command.setContent(content);
            sendSmsV2Command.setProjectId(projectId);

            List<SendSmsV2Command.AreaPhone> areaPhones = phones.stream().map(phone -> {
                SendSmsV2Command.AreaPhone areaPhone = new SendSmsV2Command.AreaPhone();
                areaPhone.setAreaCode(invoiceRecord.getAreaCode());
                areaPhone.setPhoneNumber(phone);
                return areaPhone;
            }).collect(Collectors.toList());
            sendSmsV2Command.setAreaPhones(areaPhones);

            BuildingResponseVo buildingResponseVo = hiveAsClient.getBuildingById(aptPaymentInfo.getBuildingId());
            if (Objects.isNull(buildingResponseVo)) {
                log.warn("send_sms_for_invoice hive_result empty for building: {}", aptPaymentInfo.getBuildingId());
                return;
            }

            // 设置业态
            Optional.ofNullable(buildingResponseVo)
                    .map(BuildingResponseVo::getBuilding)
                    .map(BuildingRespDto::getFormats)
                    .map(Enum::name)
                    .ifPresent(sendSmsV2Command::setAssetType);

            log.info("send_sms_for_invoice param: {}", JSONObject.toJSONString(sendSmsV2Command));
            SmsResultVo vo = unifiedMessageClient.sendSmsV2(sendSmsV2Command);
            log.info("send_sms_for_invoice result: {}", JSONObject.toJSONString(vo));
        } catch (Exception e) {
            log.error("send_sms_for_invoice error: ", e);
        }
    }

    public String getContent(List<EFapiaoBillInvoice> eFapiaoBillInvoices, List<String> phones, String projectId) {
        EFapiaoBillInvoice sample = eFapiaoBillInvoices.get(0);
        Map<String, Object> params = Maps.newHashMap();
        params.put("name", sample.getSellerName());
        params.put("type", getInvoiceBizDesc(sample.getBizType()));
        String pdfUrl = sample.getPdfUrl();
        String url = createShortUrl(phones, projectId, pdfUrl);
        params.put("url", url);
        params.put("count", String.valueOf(eFapiaoBillInvoices.size()));
        String content = SmsContentEnum.FAPIAO_CONTENT.getContent();
        content = StringFormatUtils.format(content, params);
        if (eFapiaoBillInvoices.size() > 1) {
            StringBuilder sb = new StringBuilder(content);
            for (int i = 1; i < eFapiaoBillInvoices.size(); i++) {
                String pdfUrl1 = eFapiaoBillInvoices.get(i).getPdfUrl();
                String url1 = createShortUrl(phones, projectId, pdfUrl1);
                sb.append("，").append(url1);
            }
            content = sb.toString();
        }
        return content;
    }

    private void isAlreadySend(String invokedInvoiceCode, String invokedInvoiceNo
            , CallBackKerryInvoiceMainVo mainVo, InvoiceRecord invoiceRecord) {
        List<String> invokedInvoiceCodes = StringUtils.isEmpty(invokedInvoiceCode) ?
                Collections.emptyList() : Arrays.asList((invokedInvoiceCode.split(",")));
        List<String> invokedInvoiceNos = StringUtils.isEmpty(invokedInvoiceNo) ?
                Collections.emptyList() : Arrays.asList((invokedInvoiceNo.split(",")));

        String codeAndNo = mainVo.getInvoiceCode() + '_' + mainVo.getInvoiceNo();

        // 发票代码invoiceCode、发票号码invoiceNo两个字段对应在一起具有唯一性，单独来看不唯一
        if (invokedInvoiceCodes.size() == invokedInvoiceNos.size()) {
            for (int i = 0; i < invokedInvoiceCodes.size(); i++) {
                String tempCodeAndNo = invokedInvoiceCodes.get(i) + '_' + invokedInvoiceNos.get(i);

                if (codeAndNo.equals(tempCodeAndNo)) {
                    log.info("send_sms_for_invoice Already Send Message: [{}], Invoice code [{}], Invoice no [{}]."
                            , invoiceRecord.getPhone(), mainVo.getInvoiceCode(), mainVo.getInvoiceNo());
                    break;
                }
            }
        } else {
            log.info("send_sms_for_invoice Invoked invoice codes or invoice No lost");
        }
    }

    private void isAlreadySendFullDigital(String invokedInvoiceNo
            , CallBackKerryInvoiceMainVo mainVo, InvoiceRecord invoiceRecord) {
        // 数电场景，发票代码invoiceCode为空、使用发票号码invoiceNo确认是否重复
        if (StringUtils.isNotEmpty(invokedInvoiceNo)) {
            if (invokedInvoiceNo.contains(mainVo.getInvoiceNo())) {
                log.info("full_digital send_sms_for_invoice Already Send Message: [{}], Invoice no [{}]."
                        , invoiceRecord.getPhone(), mainVo.getInvoiceNo());
            }
        } else {
            log.info("send_sms_for_invoice invoked_invoice_no_is_empty");
        }
    }

    private String getContent(Map<String, Object> params) {
        String content = SmsContentEnum.INVOICE_CONTENT.getContent();
        return StringFormatUtils.format(content, params);
    }

    private String createShortUrl(List<String> phones, String projectId, String pdfUrl) {
        CreateShortUrlCommand command = new CreateShortUrlCommand();
        command.setLongUrl(pdfUrl);
        command.setPhoneNumbers(phones);
        command.setSmsType("NOTIFICATION");
        command.setProjectId(projectId);
        try {
            CreateShortUrlResource vo1 = unifiedMessageClient.createShortUrl(command);
            return vo1.getShortUrl();
        } catch (Exception e) {
            log.error("createShortUrl[调用接口异常。降级处理：返回原链接] || pdfUrl : {} ", pdfUrl, e);
            return pdfUrl;
        }

    }

    @Data
    public static class SmsSendDto {

        private String content;

        private List<String> phoneNumbers;

    }


}
