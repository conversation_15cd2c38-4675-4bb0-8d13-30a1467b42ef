package com.kerryprops.kip.bill.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.enums.DirectDebitAgreementSignType;
import com.kerryprops.kip.bill.common.enums.DirectDebitAgreementStatus;
import com.kerryprops.kip.bill.common.enums.DirectDebitTerminatedType;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.AptBillDirectDebitsAgreementRepository;
import com.kerryprops.kip.bill.dao.AptPaymentInfoRepository;
import com.kerryprops.kip.bill.dao.entity.AptBillDirectDebitsAgreement;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.feign.clients.CUserClient;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.entity.CustomerUserResource;
import com.kerryprops.kip.bill.feign.entity.WxOpenIDResource;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.AptBillAgreementService;
import com.kerryprops.kip.bill.service.model.s.AptBillAgreementBo;
import com.kerryprops.kip.bill.webservice.vo.resp.DirectDebitsAgreementResource;
import com.kerryprops.kip.bill.webservice.vo.resp.PayableStaffAptBillRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import com.kerryprops.kip.bill.webservice.vo.resp.RoomAndUserSignResource;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffAptBillRespVo;
import com.kerryprops.kip.hiveas.feign.dto.resp.RoomRespDto;
import com.kerryprops.kip.hiveas.webservice.resource.resp.BuildingSimple;
import com.kerryprops.kip.hiveas.webservice.resource.resp.FloorSimple;
import com.kerryprops.kip.hiveas.webservice.resource.resp.ProjectSimple;
import com.kerryprops.kip.hiveas.webservice.resource.resp.RoomResp;
import com.kerryprops.kip.pmw.client.resource.AsyncAgreementStatusChangedResource.AsyncAgreementStatusChangedBodyResource;
import com.kerryprops.kip.pmw.client.resource.QueryAgreementOutputResource;
import com.kerryprops.kip.pmw.client.resource.TerminateAgreementOutputResource.TerminateAgreementOutputBodyResource;
import com.kerryprops.kip.pmw.variables.PspName;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.common.constants.AppConstants.NEXT_MILLENNIUM;
import static com.kerryprops.kip.bill.dao.entity.QAptBillDirectDebitsAgreement.aptBillDirectDebitsAgreement;
import static java.util.Optional.ofNullable;

@Slf4j
@Service
@AllArgsConstructor
public class AptBillAgreementServiceImpl implements AptBillAgreementService {

    private final AptPaymentInfoRepository aptPaymentInfoRepository;

    private final AptBillDirectDebitsAgreementRepository agreementRepository;

    private final HiveAsClient hiveAsClient;

    private final CUserClient profileClient;

    @Override
    @Transactional
    public void agreementTerminated(TerminateAgreementOutputBodyResource outputBodyResource) {
        AptBillDirectDebitsAgreement agreement = agreementRepository
                .findTopByAgreementNo(outputBodyResource.getAgreementNo());
        agreement.setAgreementStatus(DirectDebitAgreementStatus.TERMINATED);
        agreement.setUnsignTime(outputBodyResource.getUnsignedTime());
        agreement.setUnsignType(DirectDebitTerminatedType.valueOf(outputBodyResource.getUnsignType()));
        agreement.setInvalidTime(outputBodyResource.getUnsignedTime());
        agreementRepository.save(agreement);
    }

    @Override
    public void saveOrUpdateAgreement(AsyncAgreementStatusChangedBodyResource bodyResource) {
        this.syncAgreement(bodyResource);
    }

    @Override
    public boolean syncAgreement(QueryAgreementOutputResource.PmwAgreementResource bodyResource) {
        if (bodyResource.getPspName().equalsIgnoreCase("alipay")) {
            return syncAlipayAgreement(bodyResource);
        } else {
            return syncWxPayAgreement(bodyResource);
        }
    }

    @Override
    public Page<DirectDebitsAgreementResource> queryAgreements(AptBillAgreementBo bo, Pageable pageable) {
        return agreementRepository.findAll(bo.toPredicates(), pageable).map(agreementRecord -> {
            DirectDebitsAgreementResource resource = new DirectDebitsAgreementResource();
            resource.setAgreementNo(agreementRecord.getAgreementNo());
            resource.setAgreementStatus(agreementRecord.getAgreementStatus());
            resource.setProjectId(agreementRecord.getProjectId());
            resource.setProjectName(agreementRecord.getProjectName());
            resource.setBuildingId(agreementRecord.getBuildingId());
            resource.setBuildingName(agreementRecord.getBuildingName());
            resource.setFloorId(agreementRecord.getFloorId());
            resource.setFloorName(agreementRecord.getFloorName());
            resource.setRoomId(agreementRecord.getRoomId());
            resource.setRoomName(agreementRecord.getRoomName());
            resource.setPspName(agreementRecord.getPspName());
            resource.setInvalidTime(agreementRecord.getInvalidTime());
            if (PspName.WECHATPAY.name().equalsIgnoreCase(resource.getPspName())) {
                Optional.ofNullable(agreementRecord.getPspLogonId())
                        .map(str -> StringUtils.abbreviateMiddle(str, "******", 12))
                        .ifPresent(resource::setPspLogonId);
                if (!DirectDebitAgreementStatus.TERMINATED.equals(agreementRecord.getAgreementStatus())) {
                    resource.setInvalidTime(null);
                }
            } else {
                resource.setPspLogonId(agreementRecord.getPspLogonId());
            }
            resource.setSignedTime(agreementRecord.getSignTime());
            resource.setSignType(agreementRecord.getSignType());
            resource.setUserProfileId(agreementRecord.getUserProfileId());
            resource.setUserPhoneNumber(agreementRecord.getUserPhoneNumber());
            resource.setPersonalProductCode(agreementRecord.getPersonalProductCode());
            return resource;
        });
    }

    @Override
    public List<PayableStaffAptBillRespVo> addAgreementInfo(List<StaffAptBillRespVo> respVos, String roomId) {
        if (CollectionUtils.isEmpty(respVos)) {
            return Collections.emptyList();
        }
        // query room's ACTIVE agreement records
        List<PayableStaffAptBillRespVo> payableRespVos = respVos.stream()
                .map(e -> BeanUtil.copy(e, PayableStaffAptBillRespVo.class)).collect(Collectors.toList());
        Optional<RoomAndUserSignResource> op = getUserAndRoomSignInfo(roomId);
        op.ifPresent(sr -> payableRespVos.forEach(e -> {
            e.setSignedDirectDebits(Boolean.TRUE);
            e.setDirectDebitsSignatory(sr.isDirectDebitsSignatory());
            e.setDuplicateSigned(sr.isDuplicateSigned());
            e.setSignedPsp(sr.getSignedPsp());
        }));
        return payableRespVos;
    }

    @Override
    public Optional<RoomAndUserSignResource> getUserAndRoomSignInfo(String roomId) {
        RoomAndUserSignResource sr = new RoomAndUserSignResource();
        BooleanExpression predicate = aptBillDirectDebitsAgreement.roomId.eq(roomId)
                .and(aptBillDirectDebitsAgreement.isDel.eq(0))
                .and(aptBillDirectDebitsAgreement.agreementStatus.eq(DirectDebitAgreementStatus.ACTIVE));
        List<AptBillDirectDebitsAgreement> agreements = Lists.newLinkedList(agreementRepository.findAll(predicate));
        if (CollectionUtils.isEmpty(agreements)) {
            return Optional.empty();
        }
        boolean isDuplicateSigned = agreements.size() > 1;
        sr.setSignedDirectDebits(Boolean.TRUE);
        sr.setDuplicateSigned(isDuplicateSigned);
        sr.setSignedPsp(agreements.get(0).getPspName());
        // judge agreement condition

        LoginUser loginUser = UserInfoUtils.getUser();
        if (Objects.nonNull(loginUser)) {
            boolean isDirectDebitsSignatory = agreements.stream()
                    .anyMatch(a -> Objects.equals(loginUser.getUniqueUserId(), a.getUserProfileId()));
            sr.setDirectDebitsSignatory(isDirectDebitsSignatory);
        }
        return Optional.of(sr);
    }

    @Override
    public List<AptBillDirectDebitsAgreement> findAllActiveByRoomIdIn(Collection<String> roomIds) {
        return agreementRepository.findAllByRoomIdInAndAgreementStatusAndIsDel(roomIds
                , DirectDebitAgreementStatus.ACTIVE, 0);
    }

    @Override
    public Collection<String> queryAllActiveAgreementRooms(String projectId) {
        return agreementRepository.findAllByProjectIdAndAgreementStatusAndIsDel(projectId
                , DirectDebitAgreementStatus.ACTIVE, 0)
                .stream().map(AptBillDirectDebitsAgreement::getRoomId).collect(Collectors.toSet());
    }

    @Override
    public Map<String, String> queryAllActiveAgreementRoomsAndPsp(String projectId) {
        Map<String, String> ret = Maps.newHashMap();
        agreementRepository.findAllByProjectIdAndAgreementStatusAndIsDel(projectId
                , DirectDebitAgreementStatus.ACTIVE, 0).forEach(agr -> ret.put(agr.getRoomId(), agr.getPspName()));
        return ret;
    }

    @Override
    public List<AptBillDirectDebitsAgreement> findAllByAgreementNos(Collection<String> agreementNos) {
        if (CollectionUtils.isEmpty(agreementNos)) {
            return Collections.emptyList();
        }
        return agreementRepository.findAllByAgreementNoIn(agreementNos);
    }

    @Override
    public AptBillDirectDebitsAgreement getTerminatedAgreement(String roomId) {
        return agreementRepository.findTopByRoomIdAndAgreementStatusAndIsDelOrderByCreatedTimeDesc(roomId
                , DirectDebitAgreementStatus.TERMINATED, 0);
    }

    @Override
    public Optional<CustomerUserResource> determineAgreementUserProfile(String projectId
            , String roomId, String openId, String appId) {
        List<WxOpenIDResource> openIDResources = profileClient.openIdList(appId, projectId, List.of(roomId));
        if (CollectionUtils.isEmpty(openIDResources)) {
            return Optional.empty();
        }
        Optional<WxOpenIDResource> openIDResourceOp = openIDResources.stream()
                .filter(r -> Objects.equals(r.getOpenId(), openId)).findFirst();
        if (openIDResourceOp.isEmpty()) {
            return Optional.empty();
        }
        String userId = openIDResourceOp.get().getUserId();
        RespWrapVo<CustomerUserResource> userVo = profileClient.getCustomerUserById(userId);
        if (!RespWrapVo.isResponseValidWithData(userVo)) {
            log.info("determineAgreementUserProfile: user not found: {}", userId);
            return Optional.empty();
        }
        return Optional.of(userVo.getData());
    }

    private boolean syncWxPayAgreement(QueryAgreementOutputResource.PmwAgreementResource bodyResource) {
        String agreementNo = bodyResource.getAgreementNo();
        String roomId = bodyResource.getPersonalProductCode();
        log.info("sync_bill_agreement for room: {}, agreementNo: {}", roomId, agreementNo);
        AptBillDirectDebitsAgreement agreement = agreementRepository.findTopByAgreementNo(agreementNo);
        if (Objects.isNull(agreement)) {
            agreement = new AptBillDirectDebitsAgreement();
            agreement.setAgreementNo(agreementNo);
            agreement.setOrderNo(Strings.EMPTY);
            agreement.setSignType(DirectDebitAgreementSignType.DIRECT_SIGN);
            agreement.setSignTime(ofNullable(bodyResource.getSignTime()).orElse(ZonedDateTime.now()));
            agreement.setUnsignType(DirectDebitTerminatedType.UNKNOWN);
            agreement.setUnsignTime(NEXT_MILLENNIUM);
            agreement.setPspLogonId(ofNullable(bodyResource.getPspLogonId()).orElse(Strings.EMPTY));
            agreement.setPspName(ofNullable(bodyResource.getPspName()).orElse(Strings.EMPTY));
            agreement.setPersonalProductCode("postpaid");
            agreement.setInvalidTime(ofNullable(bodyResource.getInvalidTime()).orElse(NEXT_MILLENNIUM));
            agreement.setIsDel(0);
            agreement.setUserPhoneNumber(ofNullable(bodyResource.getUserPhoneNumber()).orElse(Strings.EMPTY));
            agreement.setUserProfileId(ofNullable(bodyResource.getUserProfileId()).orElse(Strings.EMPTY));
            // setting info from hive
            if (Boolean.FALSE.equals(queryAgreementHiveInfo(agreement, roomId))) {
                return false;
            }
            // user info
            Optional<CustomerUserResource> userOp = determineAgreementUserProfile(agreement, bodyResource.getAppId());
            if (userOp.isPresent()) {
                CustomerUserResource user = userOp.get();
                agreement.setUserProfileId(user.getId());
                if (StringUtils.isNotBlank(user.getPhoneNumber())) {
                    agreement.setUserPhoneNumber(user.getPhoneNumber());
                }
            }
        }
        DirectDebitAgreementStatus latestStatus = DirectDebitAgreementStatus.valueOf(bodyResource.getAgreementStatus());
        if (DirectDebitAgreementStatus.TERMINATED.equals(latestStatus)) {
            agreement.setUnsignType(DirectDebitTerminatedType.valueOf(bodyResource.getUnsignType()));
            agreement.setUnsignTime(ofNullable(bodyResource.getUnsignTime()).orElse(ZonedDateTime.now()));
            agreement.setInvalidTime(agreement.getUnsignTime());
        }
        agreement.setAgreementStatus(latestStatus);
        agreementRepository.save(agreement);
        return Boolean.TRUE;
    }

    private boolean queryAgreementHiveInfo(AptBillDirectDebitsAgreement agreement, String roomId) {
        RespWrapVo<RoomResp> roomRespRespWrapVo = hiveAsClient.getRoomById(roomId);
        if (!RespWrapVo.isResponseValidWithData(roomRespRespWrapVo)) {
            log.error("sync_wxpay_agreement: not valid roomId: {}", roomId);
            return false;
        }
        RoomResp roomResp = roomRespRespWrapVo.getData();
        agreement.setBuildingId(ofNullable(roomResp.getBuilding()).map(BuildingSimple::getId).orElse(Strings.EMPTY));
        agreement.setBuildingName(ofNullable(roomResp.getBuilding()).map(BuildingSimple::getName).orElse(Strings.EMPTY));
        agreement.setProjectId(ofNullable(roomResp.getProject()).map(ProjectSimple::getId).orElse(Strings.EMPTY));
        agreement.setProjectName(ofNullable(roomResp.getProject()).map(ProjectSimple::getName).orElse(Strings.EMPTY));
        agreement.setFloorId(ofNullable(roomResp.getFloor()).map(FloorSimple::getId).orElse(Strings.EMPTY));
        agreement.setFloorName(ofNullable(roomResp.getFloor()).map(FloorSimple::getName).orElse(Strings.EMPTY));
        agreement.setRoomId(ofNullable(roomResp.getRoom()).map(RoomRespDto::getId).orElse(Strings.EMPTY));
        agreement.setRoomName(ofNullable(roomResp.getRoom()).map(RoomRespDto::getRoomNo).orElse(Strings.EMPTY));
        return true;
    }

    private boolean syncAlipayAgreement(QueryAgreementOutputResource.PmwAgreementResource bodyResource) {
        String agreementNo = bodyResource.getAgreementNo();
        String orderNo = bodyResource.getOrderNo();
        log.info("sync_bill_agreement for order: {}, agreementNo: {}", orderNo, agreementNo);

        DirectDebitAgreementStatus latestStatus = DirectDebitAgreementStatus.valueOf(bodyResource.getAgreementStatus());
        AptBillDirectDebitsAgreement agreement = agreementRepository
                .findTopByAgreementNo(agreementNo);
        if (Objects.isNull(agreement)) {
            agreement = new AptBillDirectDebitsAgreement();
            agreement.setAgreementNo(agreementNo);
            agreement.setOrderNo(ofNullable(orderNo).orElse(Strings.EMPTY));
            if (StringUtils.isNotBlank(orderNo)) {
                // pay_and_sign
                AptPaymentInfo aptPaymentInfo = aptPaymentInfoRepository.getById(orderNo);
                if (Objects.nonNull(aptPaymentInfo)) {
                    PositionItemResponse positionItem = aptPaymentInfo.getPositionItem();
                    agreement.setBuildingId(ofNullable(aptPaymentInfo.getBuildingId()).orElse(Strings.EMPTY));
                    agreement.setBuildingName(ofNullable(positionItem.getBuildingName()).orElse(Strings.EMPTY));
                    agreement.setProjectId(ofNullable(aptPaymentInfo.getProjectId()).orElse(Strings.EMPTY));
                    agreement.setProjectName(ofNullable(positionItem.getProjectName()).orElse(Strings.EMPTY));
                    agreement.setFloorId(ofNullable(aptPaymentInfo.getFloorId()).orElse(Strings.EMPTY));
                    agreement.setFloorName(ofNullable(positionItem.getFloorName()).orElse(Strings.EMPTY));
                    agreement.setRoomId(ofNullable(aptPaymentInfo.getRoomId()).orElse(Strings.EMPTY));
                    agreement.setRoomName(ofNullable(positionItem.getRoomName()).orElse(Strings.EMPTY));
                    agreement.setSignType(DirectDebitAgreementSignType.PAY_AND_SIGN);
                }
            } else {
                // direct sign
                // setting info from hive
                if (Boolean.FALSE.equals(queryAgreementHiveInfo(agreement, bodyResource.getPersonalProductCode()))) {
                    return false;
                }
                agreement.setSignType(DirectDebitAgreementSignType.DIRECT_SIGN);
            }
            agreement.setSignTime(ofNullable(bodyResource.getSignTime()).orElse(ZonedDateTime.now()));
            agreement.setUnsignType(DirectDebitTerminatedType.UNKNOWN);
            agreement.setUnsignTime(NEXT_MILLENNIUM);
        }
        if (DirectDebitAgreementStatus.TERMINATED.equals(latestStatus)) {
            agreement.setUnsignType(DirectDebitTerminatedType.valueOf(bodyResource.getUnsignType()));
            agreement.setUnsignTime(ofNullable(bodyResource.getUnsignTime()).orElse(ZonedDateTime.now()));
        }
        // agreement.setPersonalProductCode(ofNullable(bodyResource.getPersonalProductCode()).orElse(Strings.EMPTY));
        agreement.setPersonalProductCode("GENERAL_WITHHOLDING_P");
        agreement.setUserPhoneNumber(ofNullable(bodyResource.getUserPhoneNumber()).orElse(Strings.EMPTY));
        agreement.setPspLogonId(ofNullable(bodyResource.getPspLogonId()).orElse(Strings.EMPTY));
        agreement.setPspName(ofNullable(bodyResource.getPspName()).orElse(Strings.EMPTY));
        agreement.setUserProfileId(ofNullable(bodyResource.getUserProfileId()).orElse(Strings.EMPTY));
        agreement.setAgreementStatus(latestStatus);
        agreement.setInvalidTime(ofNullable(bodyResource.getInvalidTime()).orElse(NEXT_MILLENNIUM));
        agreement.setIsDel(0);
        agreementRepository.save(agreement);
        return Boolean.TRUE;
    }

    private Optional<CustomerUserResource> determineAgreementUserProfile(AptBillDirectDebitsAgreement agreement
            , String appId) {
        String profileId = agreement.getUserProfileId();
        RespWrapVo<CustomerUserResource> userVo = profileClient.getCustomerUserById(profileId);
        if (!RespWrapVo.isResponseValidWithData(userVo)) {
            log.info("determineAgreementUserProfile: user not found: {}", profileId);
            return Optional.empty();
        }
        return Optional.of(userVo.getData());
//        return determineAgreementUserProfile(agreement.getProjectId(), agreement.getRoomId()
//                , agreement.getPspLogonId(), appId);
    }

}
