package com.kerryprops.kip.bill.service.model.s;

import com.google.common.collect.ImmutableList;
import com.kerryprops.kip.bill.common.enums.DirectDebitsBatchStatus;
import com.kerryprops.kip.bill.common.jpa.QueryFilter;
import com.querydsl.core.types.Predicate;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

import static com.kerryprops.kip.bill.common.constants.AppConstants.DEFAULT_ZONE_ID;
import static com.kerryprops.kip.bill.dao.entity.QAptBillDirectDebitsBatch.aptBillDirectDebitsBatch;
import static java.util.Optional.ofNullable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AptBillDirectDebitsBatchBo implements QueryFilter {

    private String projectId;

    private String generatedDate;

    private String pspName;

    private String status;

    @Override
    public List<Optional<Predicate>> predicates() {
        ImmutableList.Builder<Optional<Predicate>> builder = ImmutableList.builder();
        builder
                .add(ofNullable(projectId).map(aptBillDirectDebitsBatch.projectId::eq))
                .add(ofNullable(generatedDate).filter(StringUtils::isNotBlank).map(d -> {
                    ZonedDateTime gDate = LocalDate.parse(generatedDate.substring(0, 10)
                            , DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay(DEFAULT_ZONE_ID);
                    return aptBillDirectDebitsBatch.createdTime.between(gDate
                            , gDate.plusDays(1).minusSeconds(1));
                }))
                .add(ofNullable(status).map(DirectDebitsBatchStatus::valueOf)
                        .map(aptBillDirectDebitsBatch.status::eq))
                .add(ofNullable(pspName).map(aptBillDirectDebitsBatch.pspName::eq))
                .add(Optional.of(aptBillDirectDebitsBatch.isDel.eq(0)))
        ;
        return builder.build();
    }

}
