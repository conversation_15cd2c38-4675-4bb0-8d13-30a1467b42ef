package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.common.enums.InvoiceRedflagStatusEnum;
import com.kerryprops.kip.bill.common.utils.InvoiceUtils;
import com.kerryprops.kip.bill.config.EFapiaoConfig;
import com.kerryprops.kip.bill.dao.entity.EFapiaoJDEBill;
import com.kerryprops.kip.bill.dao.impl.JDBCJdeEFapiaoBillOriginRepository;
import com.kerryprops.kip.bill.dao.impl.JDBCJdeEFapiaoBillReadRepository;
import com.kerryprops.kip.bill.dao.impl.JDBCJdeEFapiaoBillWriteBackRepository;
import com.kerryprops.kip.bill.feign.clients.MessageClient;
import com.kerryprops.kip.bill.feign.entity.EmailSendCommand;
import com.kerryprops.kip.bill.service.EFapiaoJdeBillService;
import com.kerryprops.kip.bill.webservice.vo.req.CallBackKerryInvoiceMainVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.kerryprops.kip.bill.common.constants.AppConstants.ALERT_MAIL_RECEIVERS;
import static com.kerryprops.kip.bill.common.constants.JdeConstants.SALES_BILL_READED;
import static com.kerryprops.kip.bill.common.constants.JdeConstants.SALES_BILL_UNREADED;
import static com.kerryprops.kip.bill.common.utils.InvoiceUtils.INVOICE_STATUS_SUCCESS;
import static com.kerryprops.kip.bill.common.utils.InvoiceUtils.STATUS_CANCEL;
import static com.kerryprops.kip.bill.common.utils.InvoiceUtils.STATUS_NORMAL;
import static com.kerryprops.kip.bill.common.utils.InvoiceUtils.splitJdeSalesbillNo;

/**
 * e-fapiao：JDE侧数据Service类
 *
 * <AUTHOR> Yan
 * @Date 2023-3-28
 */
@Service
@Slf4j
public class EFapiaoJdeBillServiceImpl implements EFapiaoJdeBillService {

    @Autowired
    EFapiaoConfig eFapiaoConfig;

    @Value("${env}")
    private String env;

    @Autowired
    private MessageClient messageClient;

    @Autowired
    private JDBCJdeEFapiaoBillOriginRepository jdbcJdeBillOriginRepository;

    @Autowired
    private JDBCJdeEFapiaoBillWriteBackRepository jdbcJdeBillWriteBackRepository;

    @Autowired
    private JDBCJdeEFapiaoBillReadRepository jdbcJdeBillReadRepository;

    @Override
    public List<EFapiaoJDEBill> queryByCompanyCodeAndMonthAndBu(String companyCode, String month, String bu) {
        // 加空格，兼容Oracle填充字段
        bu = eFapiaoConfig.getBlanks() + bu;
        try {
            List<EFapiaoJDEBill> eFapiaoJDEBills
                    = jdbcJdeBillOriginRepository.queryByCompanyCodeAndMonthAndBu(companyCode, bu, month);
            return Optional.ofNullable(eFapiaoJDEBills).orElse(Collections.emptyList());
        } catch (Exception e) {
            log.error("sync_e_fapiao_bills_from_jde query_failed: ", e);
            EmailSendCommand command = new EmailSendCommand();
            command.setSubject(env + ": sync e-fapiao bills from jde query failed.");
            command.setSendTos(ALERT_MAIL_RECEIVERS);
            command.setText(ExceptionUtils.getStackTrace(e));
            messageClient.sendWithReplyAlicloud(command);
            return Collections.emptyList();
        }
    }

    @Override
    public String queryMaxDateByCompanyCodeAndBu(String companyCode, String bu) {
        try {
            String maxDate = jdbcJdeBillOriginRepository.queryMaxDateByCompanyCodeAndBu(companyCode, bu);
            return Optional.ofNullable(maxDate).orElse(StringUtils.EMPTY);
        } catch (Exception e) {
            log.error("sync_e_fapiao_bills_from_jde query_max_date_failed: ", e);
            EmailSendCommand command = new EmailSendCommand();
            command.setSubject(env + ": sync_e_fapiao_bills_from_jde query_max_date_failed.");
            command.setSendTos(ALERT_MAIL_RECEIVERS);
            command.setText(ExceptionUtils.getStackTrace(e));
            messageClient.sendWithReplyAlicloud(command);
            return StringUtils.EMPTY;
        }
    }

    @Override
    public EFapiaoJDEBill queryBySalesBillNo(String salesBillNo) {
        return jdbcJdeBillOriginRepository.queryBySalesBillNo(salesBillNo);
    }

    @Override
    public void writeBackUploaded(EFapiaoJDEBill eFapiaoJDEBill) {
        int count = jdbcJdeBillReadRepository.countBySalesbillNo(eFapiaoJDEBill);
        if (count > 0) {
            jdbcJdeBillReadRepository.updateStatusReaded(eFapiaoJDEBill.getKco()
                    , eFapiaoJDEBill.getBillType(), eFapiaoJDEBill.getDoc()
                    , eFapiaoJDEBill.getPaymentItem(), SALES_BILL_READED);
        } else {
            jdbcJdeBillReadRepository.insertReadedInfo(eFapiaoJDEBill);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void writeBackInvoice2Jde(CallBackKerryInvoiceMainVo mainVo) {
        if (INVOICE_STATUS_SUCCESS != mainVo.getInvoiceStatus()) {
            log.warn("[EFapiao][JDEInvoiceFailed] - Failed to issue invoice in JDE system: salesBillNo={}, error={}", mainVo.getSalesbillNo(), mainVo.getMessage());

            String salesbillNo = mainVo.getSalesbillNo();;
            String[] jdeSalesbillNoArray = splitJdeSalesbillNo(salesbillNo);
            jdbcJdeBillReadRepository.updateStatusReaded(jdeSalesbillNoArray[0]
                    , jdeSalesbillNoArray[1], Integer.parseInt(jdeSalesbillNoArray[2])
                    , jdeSalesbillNoArray[3], SALES_BILL_UNREADED);
            return;
        }

        if (STATUS_NORMAL == mainVo.getStatus()
                && InvoiceRedflagStatusEnum.RED_FLAG_DEFAULT.getIndexStr().equals(mainVo.getRedFlag())) {
            // 正常开票场景
            log.info("e_fapiao_write_back_invoice ordinary_scene");

            // 回写发票信息
            final String JDE_STATUS_NORMAL = "1";
            boolean useSalesBillAmount = useSalesBillAmount(mainVo);
            jdbcJdeBillWriteBackRepository.insert(mainVo
                    , InvoiceUtils.transDateFormat(mainVo.getPaperDrewDate())
                    , getJdeFormatDate(LocalDateTime.now())
                    , useSalesBillAmount ? mainVo.getSalesbillAmountWithTax() : mainVo.getAmountWithTax()
                    , useSalesBillAmount ? mainVo.getSalesbillAmountWithoutTax() : mainVo.getAmountWithoutTax()
                    , useSalesBillAmount ? mainVo.getSalesbillTaxAmount() : mainVo.getTaxAmount()
                    , JDE_STATUS_NORMAL);
        } else if (STATUS_NORMAL == mainVo.getStatus()
                && InvoiceRedflagStatusEnum.RED_FLAG_RED_INVOICE.getIndexStr().equals(mainVo.getRedFlag())) {
            // 红冲发票场景
            log.info("e_fapiao_write_back_invoice red_flag_scene");

            // 回写发票信息
            final String JDE_STATUS_RED_FLAG = "2";
            boolean useSalesBillAmount = useSalesBillAmount(mainVo);
            jdbcJdeBillWriteBackRepository.insert(mainVo
                    , InvoiceUtils.transDateFormat(mainVo.getPaperDrewDate())
                    , getJdeFormatDate(LocalDateTime.now())
                    , useSalesBillAmount ? mainVo.getSalesbillAmountWithTax().abs().negate() : mainVo.getAmountWithTax()
                    , useSalesBillAmount ? mainVo.getSalesbillAmountWithoutTax().abs().negate() : mainVo.getAmountWithoutTax()
                    , useSalesBillAmount ? mainVo.getSalesbillTaxAmount().abs().negate() : mainVo.getTaxAmount()
                    , JDE_STATUS_RED_FLAG);
        } else if (STATUS_CANCEL == mainVo.getStatus()) {
            // 发票作废场景
            log.info("e_fapiao_write_back_invoice cancel_scene");

            // 作废数据回写JDE
            jdbcJdeBillWriteBackRepository.updateStatusFalse(mainVo);
        }
    }

    // 儒日历：123064，1就是21世纪，23就是23年，064就是23年的第64天，所以123064对应的就是2023/3/5
    private int getJdeFormatDate(LocalDateTime localDateTime) {
        int year = localDateTime.getYear();

        int centuryNum = year / 100 - 19;
        int yearNum = year % 100;
        int dayOfYear = localDateTime.getDayOfYear();

        return centuryNum * 100000 + yearNum * 1000 + dayOfYear;
    }

    private boolean useSalesBillAmount(CallBackKerryInvoiceMainVo mainVo) {

        BigDecimal invoiceAmountWithTax = Optional.ofNullable(mainVo.getAmountWithTax()).orElse(BigDecimal.ZERO);
        BigDecimal salesbillAmountWithTax = Optional.ofNullable(mainVo.getSalesbillAmountWithTax()).orElse(BigDecimal.ZERO);

        int invoiceAmountWithTaxInt = Math.abs(invoiceAmountWithTax.multiply(BigDecimal.valueOf(100L)).intValue());
        int salesbillAmountWithTaxInt = Math.abs(salesbillAmountWithTax.multiply(BigDecimal.valueOf(100L)).intValue());

        // 账单合并/组合开票场景：invoiceAmountWithTaxInt > salesbillAmountWithTaxInt
        // 拆票场景/一般场景：invoiceAmountWithTaxInt = salesbillAmountWithTaxInt
        return invoiceAmountWithTaxInt > salesbillAmountWithTaxInt;
    }

}
