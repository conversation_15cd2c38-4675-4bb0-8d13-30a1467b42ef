package com.kerryprops.kip.bill.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import com.kerryprops.kip.bill.common.enums.BillPayModule;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.PaymentPayType;
import com.kerryprops.kip.bill.common.utils.IdWorker;
import com.kerryprops.kip.bill.common.utils.PrimaryKeyUtil;
import com.kerryprops.kip.bill.config.PaymentConfigProps;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptDirectDebitsBatchBillRepository;
import com.kerryprops.kip.bill.dao.AptPaymentInfoRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptBillDirectDebitsAgreement;
import com.kerryprops.kip.bill.dao.entity.AptBillDirectDebitsBatch;
import com.kerryprops.kip.bill.dao.entity.AptDirectDebitsBatchBill;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.AptBillAgreementService;
import com.kerryprops.kip.bill.service.AptBillDirectDebitsBatchBillService;
import com.kerryprops.kip.bill.service.AptBillWxTemplateMsgService;
import com.kerryprops.kip.bill.utils.BillUtil;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import com.kerryprops.kip.hiveas.webservice.vo.resp.BuildingResponseVo;
import com.kerryprops.kip.pmw.client.resource.AsynPaymentResultResource.AsynPaymentResultBodyResource;
import com.kerryprops.kip.pmw.client.resource.CombinedPaymentTxInputResource;
import com.kerryprops.kip.pmw.client.resource.CombinedPaymentTxOutputResource;
import com.kerryprops.kip.pmw.client.resource.DirectPayResource;
import com.kerryprops.kip.pmw.client.resource.HeaderResource;
import com.kerryprops.kip.pmw.client.resource.ProductItem;
import com.kerryprops.kip.pmw.client.service.PaymentClientService;
import com.kerryprops.kip.pmw.variables.PspName;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.common.utils.BillingFun.exceptionToNull;
import static com.kerryprops.kip.bill.log4j.BSConversationFilter.refreshCorrelationId;

@Slf4j
@Service
@AllArgsConstructor
public class AptBillDirectDebitsBatchBillServiceImpl implements AptBillDirectDebitsBatchBillService {

    private final ObjectMapper objectMapper;

    private final HiveAsClient hiveAsClient;

    private final AptBillRepository aptBillRepository;

    private final PaymentConfigProps paymentConfigProps;

    private final AptBillAgreementService agreementService;

    private final PaymentClientService paymentClientService;

    private final AptPaymentInfoRepository paymentInfoRepository;

    private final AptBillWxTemplateMsgService templateMsgService;

    private final AptDirectDebitsBatchBillRepository batchBillRepository;

    @Override
    @Transactional
    public void combineBillsByRoomAndCreatePaymentInfo(AptBillDirectDebitsBatch batch
            , List<AptDirectDebitsBatchBill> batchBills) {
        // 按照单元合并账单，一个单元生成一个支付订单
        log.info("combine_bills_by_room_and_create_payment_info for '{}'", batch.getBatchNo());
        Map<String, List<AptDirectDebitsBatchBill>> roomAndBatchBillMap = batchBills.stream()
                .collect(Collectors.groupingBy(AptDirectDebitsBatchBill::getRoomId));
        for (Map.Entry<String, List<AptDirectDebitsBatchBill>> entry : roomAndBatchBillMap.entrySet()) {
            createDirectDebitsPayment(entry.getKey(), entry.getValue());
        }
        log.info("done combine_bills_by_room_and_create_payment_info for '{}'", batch.getBatchNo());
    }

    @Override
    public List<AptBill> getAptBillsByPaymentOrderNo(String paymentOrderNo) {
        if (StringUtils.isBlank(paymentOrderNo)) {
            return Collections.emptyList();
        }
        List<AptDirectDebitsBatchBill> batchBills = batchBillRepository.findByPaymentInfoId(paymentOrderNo);
        if (CollectionUtils.isEmpty(batchBills)) {
            return Collections.emptyList();
        }
        return aptBillRepository.findAllById(batchBills.stream().map(AptDirectDebitsBatchBill::getBillId)
                .collect(Collectors.toSet()));
    }

    @Override
    public Map<String, List<AptDirectDebitsBatchBill>> getRoomBillsOfBatch(AptBillDirectDebitsBatch batch) {
        String batchNo = batch.getBatchNo();
        log.info("cron_conductDirectPayment: run for batch '{}'", batchNo);
        List<AptDirectDebitsBatchBill> batchBills = batchBillRepository.findByBatchNo(batchNo);
        if (CollectionUtils.isEmpty(batchBills)) {
            log.info("cron_conductDirectPayment: run for batch '{}' but empty batchBills", batchNo);
            return null;
        }
        // 根据paymentInfoId来聚合bills
        Map<String, List<AptDirectDebitsBatchBill>> paymentInfoIdAndBatchBillMap = batchBills.stream()
                .collect(Collectors.groupingBy(AptDirectDebitsBatchBill::getPaymentInfoId));
        log.info("cron_conductDirectPayment: got batchBills for {} rooms", paymentInfoIdAndBatchBillMap.keySet().size());
        return paymentInfoIdAndBatchBillMap;
    }

    @Override
    @Transactional
    public void conductRoomDirectPaymentHandler(String paymentInfoId, List<AptDirectDebitsBatchBill> roomBatchBills) {
        new ConductRoomDirectPaymentHandler(paymentInfoId, roomBatchBills).handle();
    }

    @Override
    public void resetAptBillToBePaidStatus(List<AptDirectDebitsBatchBill> batchBills) {
        if (CollectionUtils.isEmpty(batchBills)) {
            return;
        }
        List<AptBill> lst = aptBillRepository.findAllById(batchBills.stream().map(AptDirectDebitsBatchBill::getBillId)
                .collect(Collectors.toSet()));
        lst.forEach(ab -> {
            if (BillPaymentStatus.DIRECT_DEBIT_PAYING.equals(ab.getPaymentStatus())) {
                ab.setPaymentStatus(BillPaymentStatus.TO_BE_PAID);
                log.info("aptBill '{}' reset_status_toBePaid", ab.getBillNo());
            } else {
                log.info("aptBill '{}' cant_reset_status_toBePaid cos status is: {}"
                        , ab.getBillNo(), ab.getPaymentStatus());
            }
        });
        aptBillRepository.saveAll(lst);
        log.info("resetAptBillToBePaidStatus saved");
    }

    private void createDirectDebitsPayment(String roomId, List<AptDirectDebitsBatchBill> batchBills) {
        refreshCorrelationId();
        log.info("create_direct_debits_payment_for_room: {}", roomId);
        Set<Long> ids = batchBills.stream()
                .map(AptDirectDebitsBatchBill::getBillId)
                .collect(Collectors.toSet());
        List<AptBill> aptBills = aptBillRepository.findAllById(ids);

        // 排除已删除的账单
        aptBills = aptBills.stream()
                .filter(b -> Objects.equals(0, b.getDeletedAt()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(aptBills)) {
            log.info("create_direct_payment for room: {}, empty valid aptBills", roomId);
            return;
        }
        // 计算此单元本次代扣的总金额
        BigDecimal sumBigDecimal = aptBills.stream()
                .map(AptBill::getAmt)
                .map(BigDecimal::valueOf)
                .reduce(BigDecimal::add)
                .map(BillUtil::formatAmount)
                .orElse(BigDecimal.ZERO);
        double sumAmt = sumBigDecimal.doubleValue();

        String paymentInfoId = PrimaryKeyUtil.createPaymentId();
        // set payType
        PaymentPayType payTypeEnum;
        List<AptBillDirectDebitsAgreement> lst = agreementService.findAllActiveByRoomIdIn(List.of(roomId));
        if (CollectionUtils.isNotEmpty(lst)) {
            payTypeEnum = PaymentPayType.fromPspName(lst.get(lst.size() - 1).getPspName());
        } else {
            AptBillDirectDebitsAgreement tmpAgr = agreementService.getTerminatedAgreement(roomId);
            payTypeEnum = Optional.ofNullable(tmpAgr).map(AptBillDirectDebitsAgreement::getPspName)
                    .map(PaymentPayType::fromPspName).orElse(PaymentPayType.UNKNOWN);
        }
        AptBill sampleBill = aptBills.get(0);
        var paymentInfo = AptPaymentInfo.builder()
                .id(paymentInfoId)
                .payAct(IdWorker.getFlowIdWorkerInstance().nextStrId())
                .amt(sumAmt)
                .paymentStatus(BillPaymentStatus.DIRECT_DEBIT_PAYING)
                .projectId(sampleBill.getProjectId())
                .buildingId(sampleBill.getBuildingId())
                .floorId(sampleBill.getFloorId())
                .roomId(sampleBill.getRoomId())
                .positionItem(sampleBill.getPositionItem())
                .deleted(0)
                .paySession(null)
                .pspTransNo(Strings.EMPTY)
                .paymentTransNo(Strings.EMPTY)
                .failedReason(Strings.EMPTY)
                .userProfileId(Strings.EMPTY)
                .agreementNo(Strings.EMPTY)
                .xmlUrl(Strings.EMPTY)
                .ofdUrl(Strings.EMPTY)
                .createTime(new Date())
                .updateTime(new Date())
                .payType(payTypeEnum)
                .billPayModule(BillPayModule.KERRY)
                .createBy(UserInfoUtils.getKerryAccount())
                .bu(sampleBill.getBu())
                .doco(sampleBill.getDoco())
                .alph(sampleBill.getAlph())
                .an8(sampleBill.getAn8())
                .unit(sampleBill.getUnit())
                .description(BillPayChannel.DIRECT_DEBITS.getInfo())
                .build();
        batchBills.forEach(tmp -> tmp.setPaymentInfoId(paymentInfoId));
        batchBillRepository.saveAll(batchBills);
        paymentInfoRepository.save(paymentInfo);
        log.info("direct_pay_for_room: {}, payment_info: {}", roomId
                , exceptionToNull(() -> objectMapper.writeValueAsString(paymentInfo)));
    }

    private CombinedPaymentTxOutputResource.CombinedPaymentTxOutputBodyResource callPaymentAPI(AptBill aptBill
            , AptBillDirectDebitsAgreement agreement, AptPaymentInfo aptPaymentInfo) {
        CombinedPaymentTxInputResource.CombinedPaymentTxInputBodyResource request =
                new CombinedPaymentTxInputResource.CombinedPaymentTxInputBodyResource();

        request.setProjectId(aptBill.getProjectId());
        request.setRoomId(agreement.getRoomId());
        request.setNotifyUrl(paymentConfigProps.getNotifyUrl());
        request.setCurrency("CNY");
        // 代扣时效(分钟)
        request.setPayTtl(paymentConfigProps.getDirectDebitsPayTtl());
        request.setOrderSource("KIP_BILLING");
        request.setCategory("Dev_Marketing");//no changing currently.
        request.setProductType("BILLING");

        String buildingId = aptBill.getBuildingId();
        BuildingResponseVo buildingResponseVo = hiveAsClient.getBuildingById(buildingId);
        if (Objects.isNull(buildingResponseVo) || Objects.isNull(buildingResponseVo.getBuilding())) {
            throw new RuntimeException("Co not configured for building " + buildingId);
        }
        String propertyManagementCo = buildingResponseVo.getBuilding().getPropertyManagementCo();
        if (StringUtils.isEmpty(propertyManagementCo)) {
            throw new RuntimeException("Co not configured for building " + buildingId);
        }
        request.setCompanyCode(propertyManagementCo);
        request.setFirstName(agreement.getPspLogonId());
        request.setGuestEmail(agreement.getPspLogonId());
        request.setGuestMobile(agreement.getUserPhoneNumber());
        request.setGuestProfileId(agreement.getUserProfileId());
        request.setFamilyName(null);
        request.setOrderNo(aptPaymentInfo.getId());
        request.setPspCoupon(agreement.getAgreementNo());
        String amount = new DecimalFormat("0.00").format(aptPaymentInfo.getAmt());
        request.setOrderAmount(amount);
        request.setSupportInstallment("N");
        if (Objects.nonNull(aptBill.getPositionItem())) {
            PositionItemResponse item = aptBill.getPositionItem();
            ProductItem[] productItems = new ProductItem[1];
            request.setProducts(productItems);
            productItems[0] = new ProductItem();
            productItems[0].setDescription(item.getProjectName() + "-" + item.getBuildingName() + "-" + item.getRoomName());
            productItems[0].setQty("1");
            productItems[0].setUnitPrice(amount);
        }

        //代扣相关参数
        request.setAgreementNo(agreement.getAgreementNo());
        request.setRegion("cn");
        if (PspName.ALIPAY.name().equalsIgnoreCase(agreement.getPspName())) {
            request.setPayChannel("APP");
            request.setPayOption("ALIPAY_WITHHOLDING");
        } else if (PspName.WECHATPAY.name().equalsIgnoreCase(agreement.getPspName())) {
            request.setPayChannel("APP");
            request.setPayOption("WECHATPAY_DIRECT_DEBIT");
        }

        HeaderResource.Builder builder = new HeaderResource.Builder();
        builder.setSub("kip");
        HeaderResource headerResource = builder.build();
        CombinedPaymentTxInputResource inputResource = new CombinedPaymentTxInputResource(headerResource
                , request, null);
        CombinedPaymentTxOutputResource response = paymentClientService
                .createCombinedPaymentTransaction("zh-cn", null, null, null, inputResource);
        log.info("apt_conduct_room_direct_payment combine_response: {}", response.getBody());
        return response.getBody();
    }

    /**
     * 单个单元的可代扣账单
     * 执行代扣流程
     */
    private class ConductRoomDirectPaymentHandler {

        private final String paymentInfoId;

        private final AptPaymentInfo aptPaymentInfo;

        private final List<AptDirectDebitsBatchBill> roomBatchBills;

        private String roomId;

        private AptBillDirectDebitsAgreement directDebitsAgreement;

        private List<AptBill> aptBills;

        private CombinedPaymentTxOutputResource.CombinedPaymentTxOutputBodyResource paymentResource;

        ConductRoomDirectPaymentHandler(String paymentInfoId, List<AptDirectDebitsBatchBill> roomBatchBills) {
            this.paymentInfoId = paymentInfoId;
            this.roomBatchBills = roomBatchBills;
            this.aptPaymentInfo = paymentInfoRepository.getById(paymentInfoId);
        }

        /**
         * 执行代扣 主流程
         */
        public void handle() {
            log.info("apt_conduct_room_direct_payment for paymentInfo: '{}'", paymentInfoId);
            // 只处理代扣中的单元账单
            if (!BillPaymentStatus.DIRECT_DEBIT_PAYING.equals(aptPaymentInfo.getPaymentStatus())) {
                log.info("apt_conduct_room_direct_payment skipped, room: '{}'，paymentInfo:'{}', paymentStatus: '{}'"
                        , roomId, paymentInfoId, aptPaymentInfo.getPaymentStatus());
                return;
            }
            java.util.function.Predicate<ConductRoomDirectPaymentHandler> predicate = h -> determineRoomId();
            predicate = predicate.and(h -> checkAmount())
                    .and(h -> determineAgreement())
                    .and(h -> determineAptBills())
                    .and(h -> determinePaymentOutputResource())
            ;
            if (Boolean.FALSE.equals(predicate.test(this))) {
                //
                return;
            }
            handlePaymentResource();
        }

        private void failFast(String failedReason) {
            aptPaymentInfo.setPaymentStatus(BillPaymentStatus.DIRECT_DEBIT_FAILED);
            aptPaymentInfo.setFailedReason(failedReason);
            resetAptBillToBePaidStatus(roomBatchBills);
            paymentInfoRepository.save(aptPaymentInfo);
        }

        private boolean checkAmount() {
            if (BigDecimal.ZERO.compareTo(BigDecimal.valueOf(aptPaymentInfo.getAmt())) >= 0) {
                log.info("create_direct_payment for room: {}, 金额小于等于0", roomId);
                aptPaymentInfo.setPayType(PaymentPayType.ALIPAY);
                aptPaymentInfo.setPaymentTime(new Date());
                failFast("订单总金额小于等于0");
                return false;
            }
            return true;
        }

        private boolean determineRoomId() {
            // find roomId
            Optional<String> roomOp = Optional.ofNullable(aptPaymentInfo.getRoomId())
                    .filter(StringUtils::isNotBlank)
                    .or(() -> roomBatchBills.stream().map(AptDirectDebitsBatchBill::getRoomId)
                            .filter(StringUtils::isNotBlank).findFirst());
            if (roomOp.isEmpty()) {
                log.info("apt_conduct_room_direct_payment: paymentInfo:'{}', 无对应的roomId", paymentInfoId);
                failFast("缺失单元信息");
                return false;
            }
            roomId = roomOp.get();
            log.info("apt_conduct_room_direct_payment handle for paymentInfo: '{}', rooms: {}", paymentInfoId, roomId);
            return true;
        }

        private boolean determineAgreement() {
            // query agreement for this room
            List<AptBillDirectDebitsAgreement> lst = agreementService.findAllActiveByRoomIdIn(List.of(roomId));
            if (CollectionUtils.isEmpty(lst)) {
                log.info("apt_conduct_room_direct_payment: room: '{}'，无有效的协议，paymentInfo:'{}'，无法发起支付。"
                        , roomId, paymentInfoId);
                aptPaymentInfo.setPayType(PaymentPayType.ALIPAY);
                AptBillDirectDebitsAgreement tmpAgr = agreementService.getTerminatedAgreement(roomId);
                Optional.ofNullable(tmpAgr).ifPresent(tmp -> {
                    aptPaymentInfo.setUserProfileId(tmp.getUserProfileId());
                    aptPaymentInfo.setAgreementNo(tmp.getAgreementNo());
                    aptPaymentInfo.setPaymentTime(new Date());
                    aptPaymentInfo.setPayType(PaymentPayType.fromPspName(tmpAgr.getPspName()));
                });
                failFast("代扣协议已经失效");
                // 代扣突然失效的，也要发模板消息
                if (Objects.nonNull(tmpAgr)) {
                    AsynPaymentResultBodyResource backResource = new AsynPaymentResultBodyResource();
                    backResource.setGuestProfileId(tmpAgr.getUserProfileId());
                    backResource.setGuestEmail(tmpAgr.getPspLogonId());
                    templateMsgService.sendDirectPaymentFailedMsg(aptPaymentInfo, backResource);
                }
                return false;
            }
            directDebitsAgreement = lst.get(lst.size() - 1);
            aptPaymentInfo.setAgreementNo(directDebitsAgreement.getAgreementNo());
            if (Objects.nonNull(directDebitsAgreement.getUserProfileId())) {
                aptPaymentInfo.setUserProfileId(directDebitsAgreement.getUserProfileId());
            }
            aptPaymentInfo.setPayType(PaymentPayType.fromPspName(directDebitsAgreement.getPspName()));
            String pspName = directDebitsAgreement.getPspName();
            roomBatchBills.forEach(ab -> ab.setPspName(pspName));
            return true;
        }

        private boolean determineAptBills() {
            aptBills = aptBillRepository.findAllById(roomBatchBills.stream()
                    .map(AptDirectDebitsBatchBill::getBillId).collect(Collectors.toSet()))
                    .stream().filter(b -> b.getDeletedAt() == 0).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(aptBills)) {
                log.info("apt_conduct_room_direct_payment: room: '{}'，paymentInfo:'{}'，无有效AptBill记录"
                        , roomId, paymentInfoId);
                failFast("无可代扣的账单");
                return false;
            }
            return true;
        }

        private boolean determinePaymentOutputResource() {
            try {
                paymentResource = callPaymentAPI(aptBills.get(0), directDebitsAgreement, aptPaymentInfo);
                return true;
            } catch (Exception e) {
                log.error("apt_conduct_room_direct_payment: room: '{}'，paymentInfo:'{}'，call_payment_API_failed"
                        , roomId, paymentInfoId, e);
                failFast(StringUtils.abbreviate("支付模块接口报错: " + e.getMessage(), 120));
                return false;
            }
        }

        private void handlePaymentResource() {
            DirectPayResource directPayResource = paymentResource.getDirectPayResource();
            aptPaymentInfo.setPspTransNo(directPayResource.getPspTransId());
            aptPaymentInfo.setPaymentTransNo(directPayResource.getConfirmedPspTradeNo());
            aptPaymentInfo.setPaySession(paymentResource.getSessionDetail().getSessionInfo().getSessionId());
            if (directPayResource.getState().equals("SUCCESS") || directPayResource.getState().equals("PROCESSING")) {
                aptPaymentInfo.setBillPayModule(BillPayModule.KERRY);
                aptPaymentInfo.setCreateBy(UserInfoUtils.getKerryAccount());
                paymentInfoRepository.save(aptPaymentInfo);
                log.info("apt_conduct_room_direct_payment call_payment_api_OK: room: '{}'，paymentInfo: {}"
                        , roomId, paymentInfoId);
            } else {
                String error = directPayResource.getError();
                if (StringUtils.isNotBlank(error) && error.contains(":")) {
                    String[] errorElements = error.split(":");
                    if (errorElements.length > 1) {
                        error = errorElements[1];
                    }
                }
                failFast(error);
                AsynPaymentResultBodyResource backResource = new AsynPaymentResultBodyResource();
                backResource.setGuestProfileId(aptPaymentInfo.getUserProfileId());
                backResource.setGuestEmail(directDebitsAgreement.getPspLogonId());
                templateMsgService.sendDirectPaymentFailedMsg(aptPaymentInfo, backResource);
                log.info("apt_conduct_room_direct_payment: room: '{}'，paymentInfo:'{}'" +
                                "，call_payment_api_response_failed: {}"
                        , roomId, paymentInfoId, directPayResource.getError());
            }
        }

    }

}
