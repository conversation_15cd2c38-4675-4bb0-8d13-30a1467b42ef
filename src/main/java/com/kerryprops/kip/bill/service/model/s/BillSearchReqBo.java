package com.kerryprops.kip.bill.service.model.s;

import com.google.common.collect.ImmutableList;
import com.kerryprops.kip.bill.common.enums.SendStatus;
import com.kerryprops.kip.bill.common.jpa.QueryFilter;
import com.kerryprops.kip.bill.dao.entity.QBillEntity;
import com.querydsl.core.types.Predicate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 账单查询的 view
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class BillSearchReqBo implements QueryFilter {


    private static final long serialVersionUID = 1L;

    private List<String> maxBus;

    private List<String> searchedBus;

    /**
     * 账单年
     */
    private Integer tpFyr;

    /**
     * 账单月
     */
    private Integer tpPn;

    /**
     * jde合同号
     */
    private String tpDoco;

    @Schema(title = "公司名称")
    private String tpAlph;

    /**
     * 用户编号
     */
    private String tpAn8;

    /**
     * 建筑物编号
     */
    private String tpMcu;

    /**
     * 楼盘ID
     */
    private String projectId;

    /**
     * 楼盘相关的BU列表
     */
    private List<String> busRelatedToProjectId;

    /**
     * 账单单元
     */
    private String tpUnit;

    /**
     * 账单状态 0未发送、5发送成功、10发送失败;15已删除；20文件缺失
     */
    private Integer tpStatus;

    /**
     * 账单站内信发送状态 0未发送，5已发送
     */
    private Integer mailStatus;

    /**
     * 账单邮件发送状态 0未发送，5已发送 10发送失败 15部分成功 30都成功
     */
    private Integer emailStatus;

    /**
     * 账单打印时间
     */
    private String formatDate;


    /**
     * 账单类型
     */
    private String tpDct;

    /**
     * 用户阅读状态 0 未读、1已读  不管是哪里进行阅读了都是设置为已读
     */
    private Integer readStatus;

    private Long id;

    private List<Long> ids;

    @Schema(title = "是否移除 0 正常 1 已移除")
    private String delFlag;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(title = "账单移除时间范围开始")
    private Date deleteTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(title = "账单移除时间范围结束")
    private Date deleteTimeEnd;

    @Override
    public List<Optional<Predicate>> predicates() {
        if (StringUtils.isEmpty(delFlag)) {
            delFlag = "0";
        }
        if (deleteTimeEnd != null) {
            Calendar c = Calendar.getInstance();
            c.setTime(deleteTimeEnd);
            c.add(Calendar.DAY_OF_MONTH, 1);
            deleteTimeEnd = c.getTime();
        }
        if (StringUtils.isNotEmpty(tpAlph)) {
            tpAlph = "%" + tpAlph + "%";
        }

        List<String> tpMcus = null;
        if (StringUtils.isNotEmpty(tpMcu)) {
            tpMcus = Arrays.stream(StringUtils.split(tpMcu, ","))
                    .map(e -> StringUtils.trim(e))
                    .filter(e -> StringUtils.isNotEmpty(e))
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(busRelatedToProjectId)) {
            if (CollectionUtils.isEmpty(tpMcus)) {
                tpMcus = busRelatedToProjectId;
            } else {
                tpMcus = tpMcus.stream().filter(s -> busRelatedToProjectId.contains(s)).collect(Collectors.toList());
            }
        }

        ImmutableList.Builder builder = ImmutableList.<Optional<Predicate>>builder();
        builder.add(Optional.ofNullable(delFlag).map(QBillEntity.billEntity.delFlag::eq))
                .add(Optional.ofNullable("Y").map(QBillEntity.billEntity.tpEv01::eq))
                .add(Optional.ofNullable(ids).map(QBillEntity.billEntity.id::in))
                .add(Optional.ofNullable(id).map(QBillEntity.billEntity.id::eq))
                .add(Optional.ofNullable(maxBus).map(QBillEntity.billEntity.tpMcu::in))
                .add(Optional.ofNullable(formatDate).map(QBillEntity.billEntity.formatDate::eq))
                .add(Optional.ofNullable(searchedBus).map(QBillEntity.billEntity.tpMcu::in))
                .add(Optional.ofNullable(tpDoco).map(QBillEntity.billEntity.tpDoco::eq))
                .add(Optional.ofNullable(tpAn8).map(QBillEntity.billEntity.tpAn8::eq))
                .add(Optional.ofNullable(tpUnit).map(QBillEntity.billEntity.tpUnit::eq))
                .add(Optional.ofNullable(tpMcus).map(QBillEntity.billEntity.tpMcu::in))
                .add(Optional.ofNullable(tpAlph).map(QBillEntity.billEntity.tpAlph::likeIgnoreCase))
                .add(Optional.ofNullable(tpStatus).map(QBillEntity.billEntity.tpStatus::eq))
                .add(Optional.ofNullable(mailStatus).map(QBillEntity.billEntity.mailStatus::eq))
                .add(Optional.ofNullable(tpFyr).map(QBillEntity.billEntity.tpFyr::eq))
                .add(Optional.ofNullable(tpPn).map(QBillEntity.billEntity.tpPn::eq))
                .add(Optional.ofNullable(tpDct).map(QBillEntity.billEntity.tpDct::eq))
                .add(Optional.ofNullable(deleteTimeStart).map(QBillEntity.billEntity.deleteTime::goe))
                .add(Optional.ofNullable(deleteTimeEnd).map(QBillEntity.billEntity.deleteTime::lt))
                .add(Optional.ofNullable(readStatus).map(QBillEntity.billEntity.readStatus::eq));

        if (emailStatus != null && emailStatus == SendStatus.MSG_SUCCESS.getIndex()) {
            builder.add(Optional.ofNullable(Arrays.asList(SendStatus.MSG_PARTIAL_SUCCESS.getIndex()
                    , SendStatus.MSG_SUCCESS.getIndex())).map(QBillEntity.billEntity.emailStatus::in));
        } else {
            builder.add(Optional.ofNullable(emailStatus).map(QBillEntity.billEntity.emailStatus::in));
        }
        return builder.build();
    }

}
