package com.kerryprops.kip.bill.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.PaymentPayType;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.config.DataMigrationConfig;
import com.kerryprops.kip.bill.dao.entity.AptBillDirectDebitsAgreement;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.feign.clients.CUserClient;
import com.kerryprops.kip.bill.feign.clients.MessageClient;
import com.kerryprops.kip.bill.feign.entity.CustomerUserResource;
import com.kerryprops.kip.bill.feign.entity.HOAFeesPayFailedReminderCommand;
import com.kerryprops.kip.bill.feign.entity.PaymentConfirmedCommand;
import com.kerryprops.kip.bill.service.AptBillAgreementService;
import com.kerryprops.kip.bill.service.AptBillWxTemplateMsgService;
import com.kerryprops.kip.pmw.client.resource.AsynPaymentResultResource.AsynPaymentResultBodyResource;
import io.jsonwebtoken.lang.Collections;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;

import static com.kerryprops.kip.bill.common.utils.BillingFun.exceptionToNull;

@Slf4j
@Service
@AllArgsConstructor
public class AptBillWxTemplateMsgServiceImpl implements AptBillWxTemplateMsgService {

    private final DataMigrationConfig dm;

    private final CUserClient cUserClient;

    private final ObjectMapper objectMapper;

    private final MessageClient messageClient;

    private final AptBillAgreementService aptBillAgreementService;

    @Override
    public void sendPaymentResultNotice(AptPaymentInfo aptPaymentInfo, AsynPaymentResultBodyResource backResource
            , Consumer<PaymentConfirmedCommand> commandConsumer) {
        try {
            log.info("send_payment_result_template of '{}' for status '{}'"
                    , aptPaymentInfo.getId(), aptPaymentInfo.getPaymentStatus());
            PaymentConfirmedCommand command = new PaymentConfirmedCommand();
            command.setProjectId(aptPaymentInfo.getProjectId());
            command.setBuildingIds(Lists.newArrayList(aptPaymentInfo.getBuildingId()));
            command.setPaymentType("物业账单"); // 新旧模板不变
            command.setPaymentMethod(aptPaymentInfo.getPayType().getInfo()); // 新旧模板不变
            command.setPaymentAmount(BigDecimal.valueOf(aptPaymentInfo.getAmt())
                    .setScale(2, RoundingMode.HALF_UP) + "元"); // 新旧模板不变
            command.setPaymentTime(new SimpleDateFormat("YYYY年M月d日 HH:mm") // 新旧模板不变
                    .format(Optional.ofNullable(aptPaymentInfo.getPaymentTime()).orElse(new Date())));
            if (BillPaymentStatus.DIRECT_DEBIT_STATUSES.contains(aptPaymentInfo.getPaymentStatus())) {
                // 设置 C端用户的userID
                // 新模板这个字段么得了
                Optional.ofNullable(backResource.getGuestProfileId())
                        .map(List::of).ifPresent(command::setUserIds);
                if (PaymentPayType.WECHAT.equals(aptPaymentInfo.getPayType())) {
                    String agreementNo = aptPaymentInfo.getAgreementNo();
                    List<AptBillDirectDebitsAgreement> agmts = aptBillAgreementService.findAllByAgreementNos(List.of(agreementNo));
                    if (Collections.isEmpty(agmts)) {
                        command.setPayerName("Guest");
                    } else {
                        command.setPayerName(agmts.get(0).getUserPhoneNumber());
                    }
                } else {
                    command.setPayerName(backResource.getGuestEmail());
                }
            } else {
                setUserInfoByUserCode(aptPaymentInfo, command);
            }
            // set headline hrl remark
            commandConsumer.accept(command);
            if (CollectionUtils.isEmpty(command.getUserIds())) {
                log.info("c_user_not_found, skip template msg");
                return;
            }
            messageClient.sendPaymentResultWxMessage(command);
            log.info("send_payment_result_template command: {}"
                    , exceptionToNull(() -> objectMapper.writeValueAsString(command)));
        } catch (Exception e) {
            log.error("send_payment_result_template failed.", e);
        }
    }

    @Override
    public void sendDirectPaymentFailedMsg(AptPaymentInfo aptPaymentInfo, AsynPaymentResultBodyResource backResource) {
        try {
            HOAFeesPayFailedReminderCommand command = new HOAFeesPayFailedReminderCommand();
            // 设置 C端用户的userID
            Optional.ofNullable(backResource.getGuestProfileId())
                    .map(List::of).ifPresent(command::setUserIds);
            command.setProjectId(aptPaymentInfo.getProjectId());
            command.setBuildingIds(Lists.newArrayList(aptPaymentInfo.getBuildingId()));
            // headline
            if (PaymentPayType.WECHAT.equals(aptPaymentInfo.getPayType())) {
                command.setHeadline("物业账单微信代扣失败提醒");
                String agreementNo = aptPaymentInfo.getAgreementNo();
                List<AptBillDirectDebitsAgreement> agmts = aptBillAgreementService.findAllByAgreementNos(List.of(agreementNo));
                if (Collections.isEmpty(agmts)) {
                    command.setPayerName("Guest");
                } else {
                    command.setPayerName(agmts.get(0).getUserPhoneNumber());
                }
            } else {
                command.setHeadline("物业账单支付宝代扣失败提醒");
                // 缴费账户
                command.setPayerName(backResource.getGuestEmail());
            }
            // 订单号
            command.setPaymentOrderNo(aptPaymentInfo.getId());
            // 支付金额
            command.setPaymentAmount(BigDecimal.valueOf(aptPaymentInfo.getAmt())
                    .setScale(2, RoundingMode.HALF_UP) + "元");
            // 支付时间
            command.setPaymentTime(new SimpleDateFormat("YYYY年M月d日 HH:mm")
                    .format(Optional.ofNullable(aptPaymentInfo.getPaymentTime()).orElse(new Date())));
            // remark
            command.setRemark("-->点击详情，请尽快完成自助缴费");
            // url
            command.setUrl(dm.getPayFailedMsgUrl().replace("ROOM_ID", aptPaymentInfo.getRoomId()));
            log.info("send_payment_result_template command: {}"
                    , exceptionToNull(() -> objectMapper.writeValueAsString(command)));
            messageClient.sendPaymentFailedWxMessage(command);
        } catch (Exception e) {
            log.error("send_payment_result_template failed.", e);
        }
    }

    private void setUserInfoByUserCode(AptPaymentInfo aptPaymentInfo, PaymentConfirmedCommand command) {
        log.info("query profile-service to fetch userId & nickName");
        if (Objects.isNull(aptPaymentInfo.getBindUserId())) {
            log.info("not_bound_user");
            return;
        }
        int code = aptPaymentInfo.getBindUserId().intValue();
        RespWrapVo<CustomerUserResource> resourceRespWrapVo = cUserClient.queryUserByCode(code);
        if (!RespWrapVo.isResponseValidWithData(resourceRespWrapVo)) {
            log.error("no customer user found: {}", code);
            return;
        }
        CustomerUserResource customerUserResource = resourceRespWrapVo.getData();
        if (Objects.isNull(customerUserResource) || StringUtils.isEmpty(customerUserResource.getId())) {
            log.error("invalid CustomerUserResource: {}", code);
            return;
        }
        String userId = customerUserResource.getId();
        String userName = customerUserResource.getNickName();
        command.setUserIds(List.of(userId));
        command.setPayerName(userName);
    }

}
