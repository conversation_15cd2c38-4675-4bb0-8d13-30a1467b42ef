package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.enums.BillPayModule;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillSelectMode;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.common.enums.PayCancelTypeEnum;
import com.kerryprops.kip.bill.common.enums.PaymentPayType;
import com.kerryprops.kip.bill.common.exceptions.AppException;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.utils.IdWorker;
import com.kerryprops.kip.bill.common.utils.PrimaryKeyUtil;
import com.kerryprops.kip.bill.config.PaymentConfigProps;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptJdeBillRepository;
import com.kerryprops.kip.bill.dao.AptPayConfigRepository;
import com.kerryprops.kip.bill.dao.AptPaymentBillRepository;
import com.kerryprops.kip.bill.dao.AptPaymentInfoRepository;
import com.kerryprops.kip.bill.dao.BillSelectConfigRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptJdeBill;
import com.kerryprops.kip.bill.dao.entity.AptPayConfig;
import com.kerryprops.kip.bill.dao.entity.AptPaymentBill;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.dao.entity.QAptJdeBill;
import com.kerryprops.kip.bill.dao.entity.QAptPayConfig;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.clients.KipInvoiceClient;
import com.kerryprops.kip.bill.feign.entity.TaxClassifyCodesResource;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.AptBillDirectDebitsBatchBillService;
import com.kerryprops.kip.bill.service.AptPayService;
import com.kerryprops.kip.bill.service.PaymentBillService;
import com.kerryprops.kip.bill.service.model.s.AptPayInvoiceBo;
import com.kerryprops.kip.bill.service.model.s.BillInvoiceCalcDto;
import com.kerryprops.kip.bill.service.model.s.CreatePaySessionDto;
import com.kerryprops.kip.bill.utils.BillUtil;
import com.kerryprops.kip.bill.webservice.vo.req.AptPaymentVo;
import com.kerryprops.kip.bill.webservice.vo.req.CallBackKerryInvoiceMainVo;
import com.kerryprops.kip.bill.webservice.vo.resp.AptBillVo;
import com.kerryprops.kip.bill.webservice.vo.resp.AptPaymentInfoVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillSessionResult;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import com.kerryprops.kip.hiveas.webservice.vo.resp.BuildingResponseVo;
import com.kerryprops.kip.pmw.client.resource.CancelOrderInputResource;
import com.kerryprops.kip.pmw.client.resource.HeaderResource;
import com.kerryprops.kip.pmw.client.resource.PaymentSessionInputResource;
import com.kerryprops.kip.pmw.client.resource.ProductItem;
import com.kerryprops.kip.pmw.client.resource.SessionOutputResource;
import com.kerryprops.kip.pmw.client.service.PaymentClientService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.Duration;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.common.enums.BillPaymentStatus.DIRECT_DEBIT_STATUSES;

@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentBillServiceImpl implements PaymentBillService {

    private static final String TAX_CLASSIFY_CODE_CACHE = "TAX_CLASSIFY_CODE_CACHE";

    private static final String ONLINE_PAY_DESC = "线上支付";

    private final AptPaymentInfoRepository paymentInfoRepository;

    private final AptPaymentBillRepository paymentBillRepository;

    private final PaymentConfigProps paymentConfigProps;

    private final AptBillService aptBillService;

    private final PaymentClientService paymentClientService;

    private final HiveAsClient hiveAsClient;

    private final AptBillRepository aptBillRepository;

    private final AptPayConfigRepository payConfigRepository;

    private final AptPayService aptPayService;

    private final AptJdeBillRepository jdeBillRepository;

    private final KipInvoiceClient kipInvoiceClient;

    private final AptBillDirectDebitsBatchBillService batchBillService;

    private final BillSelectConfigRepository billSelectConfigRepository;

    @Value("${billing.notInvoice:Z16M}")
    private String notInvoice;

    @Override
    public AptPayInvoiceBo calcBillInvoice(BillInvoiceCalcDto dto) {
        if (Objects.isNull(dto)) {
            throw new AppException("7000011", "PaymentInfo null error");
        }

        if (StringUtils.isNotBlank(dto.getCancelType())) {
            return new AptPayInvoiceBo();
        }

        BillPaymentStatus paymentStatus = dto.getPaymentStatus();
        List<AptBill> bills = Objects.nonNull(paymentStatus) && DIRECT_DEBIT_STATUSES.contains(paymentStatus)
                ? batchBillService.getAptBillsByPaymentOrderNo(dto.getPaymentInfoId())
                : aptBillRepository.queryAptBillByPaymentId(dto.getPaymentInfoId());

        fillRdGlc(bills);

        String buildingId = dto.getBuildingId();
        BigDecimal advanceAmount = dto.getAdvanceAmount();
        BigDecimal payAmount = BigDecimal.valueOf(dto.getAmt());
        String paymentCate = dto.getPaymentCate().name();
        Long feeId = dto.getFeeId();
        BigDecimal canInvoiceBillAmount = BigDecimal.ZERO;
        BigDecimal billAmount = BigDecimal.ZERO;
        String isAdvanceBilling = "0";
        Map<Long, String> isBillingMap = new HashMap<>();
        boolean hasPrepaid = BigDecimal.ZERO.compareTo(advanceAmount) < 0;

        String propertyManagementCo = getCompanyCode(bills, buildingId);
        List<TaxClassifyCodesResource> classifyCodes = listTaxClassifyCode(propertyManagementCo);
        Set<String> billCodeCache = classifyCodes.isEmpty()
                ? Collections.emptySet()
                : classifyCodes
                .stream()
                .map(v -> v.getBillCode())
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        for (AptBill aptBill : bills) {
            Long billId = aptBill.getId();
            isBillingMap.put(billId, "0");
            BigDecimal amt = BigDecimal.valueOf(aptBill.getAmt());
            billAmount = billAmount.add(amt);
            // rd_glc 总账冲销-票据码
            String rdGlc = aptBill.getRdGlc();
            if (StringUtils.isNotBlank(rdGlc)) {
                if (notInvoice.contains(rdGlc)) {
                    isBillingMap.put(billId, "2");
                    continue;
                }
                if (billCodeCache.contains(rdGlc)) {
                    // 可开票
                    isBillingMap.put(billId, "1");
                    canInvoiceBillAmount = canInvoiceBillAmount.add(amt);
                }
            }
        }
        if (hasPrepaid) {
            if (billCodeCache.contains(paymentCate)) {
                isAdvanceBilling = "1";
                canInvoiceBillAmount = canInvoiceBillAmount.add(advanceAmount);
            } else {
                log.info("税收分类编码模块，没有对应配置. propertyManagementCo : {} || billCode : {}",
                        propertyManagementCo, paymentCate);
            }
        }

        boolean hasFeePay = BillPayModule.CASHIER_FEE.equals(dto.getBillPayModule())
                && Objects.nonNull(feeId) && feeId > 0;
        if (hasFeePay) {
            if (classifyCodes.stream().anyMatch(v -> feeId.equals(v.getFeeId()))) {
                isAdvanceBilling = "1";
                canInvoiceBillAmount = canInvoiceBillAmount.add(payAmount);
            } else {
                log.info("税收分类编码模块，没有对应配置. propertyManagementCo : {} || feeId : {}",
                        propertyManagementCo, feeId);
            }
        }

        AptPayInvoiceBo bo = new AptPayInvoiceBo();
        bo.setBillAmount(billAmount);
        bo.setCanBillInvoice(isBillingMap);
        bo.setCanInvoiceAmount(canInvoiceBillAmount);
        bo.setIsAdvanceBilling(isAdvanceBilling);
        bo.setBills(bills);
        return bo;
    }

    /**
     * @Method: billPayment
     * @Description: 账单支付
     * @Date: 2022/5/27 22:24
     * @Author: Jane Dong
     * @param: aptPaymentVos, checkAmount
     * @return: com.kerryprops.kip.bill.webservice.vo.resp.BillSessionResult
     */
    @Override
    @Transactional
    public BillSessionResult billPayment(List<AptPaymentVo> aptPaymentVos, boolean checkAmount) {
        if (CollectionUtils.isEmpty(aptPaymentVos)) {
            throw new AppException("700008", "aptPaymentVos null");
        }

        List<String> billNos = aptPaymentVos.stream().map(AptPaymentVo::getBillNo).collect(Collectors.toList());
        List<AptBill> aptBills = aptBillService.queryAptBillsByBillNos(billNos);
        if (CollectionUtils.isEmpty(aptBills) || aptBills.size() != billNos.size()) {
            throw new AppException("700007", "部分账单不可支付，请核对后重试");
        }

        if (checkAmount) {
            Map<String, List<AptPaymentVo>> map = aptPaymentVos.stream()
                    .collect(Collectors.groupingBy(AptPaymentVo::getBillNo));
            aptBills.forEach(aptBill -> {
                        String billNo = aptBill.getBillNo();
                        AptPaymentVo aptPaymentVo = map.get(billNo).get(0);
                        BigDecimal billDbAmount = BigDecimal.valueOf(aptBill.getAmt());
                        BigDecimal billUiAmount = BigDecimal.valueOf(aptPaymentVo.getAmount());
                        if (billUiAmount.compareTo(billDbAmount) != 0) {
                            throw new AppException("700009", "账单金额有变化，请刷新");
                        }
                    }
            );
        }

        Iterable<AptPayConfig> payConfigIterable = payConfigRepository.findAll(
                QAptPayConfig.aptPayConfig.paymentType.in(List.of(PaymentPayType.WECHAT.getInfo(), PaymentPayType.ALIPAY.getInfo()))
                        .and(QAptPayConfig.aptPayConfig.projectId.eq(aptBills.get(0).getProjectId()))
        );

        if (!payConfigIterable.iterator().hasNext()) {
            throw new AppException("703", "当前楼盘未配置收款方式，请与物业联系");
        }

        validBillSelectConfig(aptBills);

        //处理订单
        AptPaymentInfo aptPaymentInfo = savePaymentInfo(aptBills);

        //处理原始订单状态
        updateStatusBy(aptBills, BillPaymentStatus.PAYING);

        BillSessionResult billSessionResult = new BillSessionResult();
        billSessionResult.setBillNo(aptPaymentInfo.getId());
        billSessionResult.setPaymentSessionId(aptPaymentInfo.getPaySession());
        return billSessionResult;
    }

    /**
     * @Method: cancelPayment
     * @Description: 用户主动取消支付
     * @Date: 2022/2/6 13:44
     * @Author: Kanon
     * @param: paymentId
     * @return: void
     */
    @Override
    @Transactional
    public void cancelPayment(String paymentId) {
        AptPaymentInfo aptPaymentInfo = getBillPaymentInfoById(paymentId);
        if (Objects.isNull(aptPaymentInfo)) {
            log.info("backResource paymentId: {}", paymentId);
            throw new AppException("701", "PaymentInfo not found: " + paymentId);
        }

        //越权检测
        if (!Objects.equals(UserInfoUtils.getUser().getUserId(), aptPaymentInfo.getBindUserId())) {
            throw new AppException("601", "unauthorized operation");
        }

        //状态检测
        if (!BillPaymentStatus.TO_BE_PAID.name().equals(aptPaymentInfo.getPaymentStatus().name())) {
            throw new AppException("602", "check payment status");
        }
        // cancel payment order
        cancelPaymentTransaction(aptPaymentInfo);

        List<AptBill> aptBills = aptBillRepository.queryAptBillByPaymentId(aptPaymentInfo.getId());
        aptPayService.cancelAptPay(aptPaymentInfo, aptBills);
        updateStatusBy(aptBills, BillPaymentStatus.TO_BE_PAID);
    }

    @Override
    @Transactional
    public void cancelPaymentTransaction(AptPaymentInfo aptPaymentInfo) {
        this.cancelPaymentOrder(aptPaymentInfo.getId());
        if (!BillPayModule.CASHIER.equals(aptPaymentInfo.getBillPayModule())
                && !BillPayModule.CASHIER_FEE.equals(aptPaymentInfo.getBillPayModule())) {
            final int CANCEL_INVOICE = -1;
            aptPaymentInfo.setPaymentStatus(BillPaymentStatus.CANCEL);
            aptPaymentInfo.setCancelType(PayCancelTypeEnum.USER_CANCELLED.name());
            aptPaymentInfo.setAppliedInvoice(CANCEL_INVOICE);
            aptPaymentInfo.setUpdateTime(new Date());
            paymentInfoRepository.save(aptPaymentInfo);
        }
    }

    @Override
    public AptPaymentInfo invoiceCallback(String paymentId, CallBackKerryInvoiceMainVo mainVo) {
        AptPaymentInfo aptPaymentInfo = getBillPaymentInfoById(paymentId);
        aptPaymentInfo.setInvoiceUrl(this.getData(aptPaymentInfo.getInvoiceUrl(), mainVo.getPdfUrl()));
        aptPaymentInfo.setXmlUrl(this.getData(aptPaymentInfo.getXmlUrl(), mainVo.getXmlUrl()));
        aptPaymentInfo.setOfdUrl(this.getData(aptPaymentInfo.getOfdUrl(), mainVo.getOfdUrl()));
        paymentInfoRepository.save(aptPaymentInfo);
        return aptPaymentInfo;
    }

    @Override
    public List<AptPaymentInfo> queryMyPaymentInfoList(Long userId, String buildingId, String roomId) {
        /*BooleanExpression queryPar = QAptPaymentInfo.aptPaymentInfo.bindUserId.eq(userId)
                .and(QAptPaymentInfo.aptPaymentInfo.buildingId.eq(buildingId))
                .and(QAptPaymentInfo.aptPaymentInfo.roomId.eq(roomId));
        Iterable<AptPaymentInfo> infoRepositoryAll = paymentInfoRepository
                .findAll(queryPar, Sort.by(Sort.Direction.DESC, "createTime"));
        return Lists.newArrayList(infoRepositoryAll);*/
        LoginUser loginUser = UserInfoUtils.getUser();
        return paymentInfoRepository.findAllByUserAndRoom(loginUser.getUserId(), loginUser.getUniqueUserId(), roomId);
    }

    @Override
    public void applyInvoice(String paymentId) {
        var aptPaymentInfo = getBillPaymentInfoById(paymentId);
        aptPaymentInfo.setAppliedInvoice(2);
        paymentInfoRepository.save(aptPaymentInfo);
    }

    @Override
    public void cancelInvoice(String paymentId) {
        var aptPaymentInfo = getBillPaymentInfoById(paymentId);
        aptPaymentInfo.setAppliedInvoice(3);
        paymentInfoRepository.save(aptPaymentInfo);
    }

    @Override
    public AptPaymentInfoVo queryPaymentInfoById(String paymentId) {
        //查询缴费账单信息
        var aptPaymentInfo = getBillPaymentInfoById(paymentId);
        BillInvoiceCalcDto dto = BillInvoiceCalcDto.of(aptPaymentInfo);
        AptPayInvoiceBo bo = calcBillInvoice(dto);
        Map<Long, String> canBillInvoice = bo.getCanBillInvoice();
        List<AptBillVo> aptBillVos = bo.getBills()
                .stream()
                .map(v -> {
                    AptBillVo billVo = BeanUtil.copy(v, AptBillVo.class);
                    billVo.setIsBilling(canBillInvoice.get(v.getId()));
                    return billVo;
                })
                .collect(Collectors.toList());

        AptPaymentInfoVo aptPaymentInfoVo = BeanUtil.copy(aptPaymentInfo, AptPaymentInfoVo.class);
        aptPaymentInfoVo.setAptBillList(aptBillVos);
        aptPaymentInfoVo.setIsAdvanceBilling(bo.getIsAdvanceBilling());
        aptPaymentInfoVo.setCanInvoiceBillAmount(bo.getCanInvoiceAmount());
        return aptPaymentInfoVo;
    }

    @Override
    public AptPaymentInfo getBillPaymentInfoById(String id) {
        return paymentInfoRepository.findById(id).orElse(null);
    }

    /**
     * 创建支付session
     *
     * @Date: 2022/1/17 11:40
     * @Author: Kanon
     */
    @Override
    public SessionOutputResource createPaymentSession(CreatePaySessionDto createPaySessionDto) {
        String buildingId = createPaySessionDto.getBuildingId();
        BuildingResponseVo buildingResponseVo = hiveAsClient.getBuildingById(buildingId);
        if (Objects.isNull(buildingResponseVo) || Objects.isNull(buildingResponseVo.getBuilding())) {
            throw new RuntimeException("Co not configured for building " + buildingId);
        }
        String propertyManagementCo = buildingResponseVo.getBuilding().getPropertyManagementCo();
        if (StringUtils.isEmpty(propertyManagementCo)) {
            throw new RuntimeException("Co not configured for building " + buildingId);
        }
        String timeout = String.valueOf(createPaySessionDto.getPayTimeout().toMinutes());
        String amount = new DecimalFormat("0.00").format(createPaySessionDto.getAmt());
        PositionItemResponse positionItem = createPaySessionDto.getPositionItem();
        ProductItem productItem = null;
        if (Objects.nonNull(positionItem)) {
            productItem = new ProductItem();
            productItem.setDescription(positionItem.getProjectName()
                    + "-" + positionItem.getBuildingName()
                    + "-" + positionItem.getRoomName());
            productItem.setQty("1");
            productItem.setUnitPrice(amount);
        }

        PaymentSessionInputResource.PaymentSessionInputBodyResource request
                = new PaymentSessionInputResource.PaymentSessionInputBodyResource();
        request.setProjectId(createPaySessionDto.getProjectId());
        request.setNotifyUrl(paymentConfigProps.getNotifyUrl());
        request.setCurrency("CNY");
        request.setPayTtl(timeout);
        request.setOrderSource("KIP_BILLING");
        //no changing currently.
        request.setCategory("Dev_Marketing");
        request.setProductType("BILLING");
        request.setCompanyCode(propertyManagementCo);
        request.setFirstName(UserInfoUtils.getKerryAccount());
        request.setGuestMobile(UserInfoUtils.getPhoneNumber());
        request.setGuestProfileId(UserInfoUtils.getUserProfileId());
        request.setFamilyName(null);
        request.setOrderNo(String.valueOf(createPaySessionDto.getOrderNo()));
        request.setOrderAmount(amount);
        request.setSupportInstallment("N");
        request.setProducts(productItem);

        HeaderResource.Builder builder = new HeaderResource.Builder();
        builder.setSub("kip");
        HeaderResource headerResource = builder.build();

        PaymentSessionInputResource inputResource = new PaymentSessionInputResource(headerResource, request, null);
        log.info("call create payment session api body: {}", inputResource);
        return paymentClientService.createPaymentSession("", "", inputResource);
    }

    public void validBillSelectConfig(List<AptBill> bills) {
        AptBill firstBill = bills.get(0);
        String projectId = firstBill.getProjectId();
        String buildingId = firstBill.getBuildingId();
        String roomId = firstBill.getRoomId();
        BillSelectMode selectMode = billSelectConfigRepository.findBillSelectMode(projectId, buildingId, roomId);
        if (BillSelectMode.ANY_BILLS.equals(selectMode)) {
            return;
        }

        int billsSize = bills.size();
        if (BillSelectMode.BILLS_NO_JUMP_SELECT.equals(selectMode)) {
            var unpaidBillTimes = aptBillRepository.findUnpaidBillDurations(projectId, buildingId, roomId, billsSize);
            var toPayBillTimes = bills.stream().map(v -> BillUtil.getBillYearMonth(v.getYear(), v.getMonth())).sorted().toList();
            for (int i = 0, size = toPayBillTimes.size(); i < size; i++) {
                Integer toPayBillTime = toPayBillTimes.get(i);
                Integer unpaidBillTime = unpaidBillTimes.get(i);
                if (!Objects.equals(toPayBillTime, unpaidBillTime)) {
                    log.info("Bill selection jump detected. Expected: {}, Found: {}", unpaidBillTime, toPayBillTime);
                    throw new AppException("700011", "禁止跳期选择账单");
                }
            }
        }

        if (BillSelectMode.ALL_BILLS_REQUIRED.equals(selectMode)) {
            var unpaidBillCount = aptBillRepository.countUnpaidBills(projectId, buildingId, roomId);
            if (billsSize != unpaidBillCount) {
                log.info("unpaidBillCount : {} , billsSize : {}", unpaidBillCount, billsSize);
                throw new AppException("700010", "必须选择单元下的所有账单");
            }
        }
    }

    @SuppressWarnings("unchecked")
    private List<TaxClassifyCodesResource> listTaxClassifyCode(String propertyManagementCo) {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        String key = TAX_CLASSIFY_CODE_CACHE + "_" + propertyManagementCo;
        if (requestAttributes != null) {
            List<TaxClassifyCodesResource> vos = (List<TaxClassifyCodesResource>) requestAttributes.getAttribute(key, RequestAttributes.SCOPE_REQUEST);
            if (vos != null) {
                return vos;
            }
        }
        List<TaxClassifyCodesResource> vos = kipInvoiceClient.listTaxClassifyConfig(propertyManagementCo);
        if (requestAttributes != null) {
            requestAttributes.setAttribute(key, vos, RequestAttributes.SCOPE_REQUEST);
        }
        return vos;
    }


    private void fillRdGlc(List<AptBill> bills) {
        for (AptBill bill : bills) {
            // 有总账冲销-票据码, 跳过；没有，则填充
            if (StringUtils.isNotBlank(bill.getRdGlc())) {
                continue;
            }
            Iterable<AptJdeBill> billRepositoryAll = jdeBillRepository.findAll(QAptJdeBill.aptJdeBill.billNumber.eq(bill.getBillNo()));
            for (AptJdeBill aptJdeBill : billRepositoryAll) {
                bill.setRdGlc(aptJdeBill.getRdGlc());
                return;
            }
        }
    }

    private String getCompanyCode(List<AptBill> bills, String buildingId) {
        String buildingId1 = Optional.ofNullable(bills)
                .filter(CollectionUtils::isNotEmpty)
                .map(v -> v.get(0))
                .map(v -> v.getBuildingId())
                .orElse(buildingId);
        return hiveAsClient.getPropertyManagementCo(buildingId1);
    }

    private void cancelPaymentOrder(String paymentOrderNo) {
        // set cancel payment body
        CancelOrderInputResource.CancelOrderInputBodyResource cancelOrderInputBodyResource
                = new CancelOrderInputResource.CancelOrderInputBodyResource();
        cancelOrderInputBodyResource.setOrderSource("KIP_BILLING");
        cancelOrderInputBodyResource.setOrderNo(paymentOrderNo);
        // cancel payment header
        HeaderResource.Builder builder = new HeaderResource.Builder();
        builder.setSub("kip");
        HeaderResource headerResource = builder.build();
        // cancel payment request
        CancelOrderInputResource cancelOrderInputResource
                = new CancelOrderInputResource(headerResource, cancelOrderInputBodyResource, null);
        // call cancel payment API
        try {
            paymentClientService.cancelOrder(cancelOrderInputResource);
        } catch (Exception e) {
            log.error("error occurred while cancelPaymentOrder for '{}'", paymentOrderNo, e);
        }
    }

    private String getData(String data, String append) {
        if (StringUtils.isNotBlank(data) && data.contains(append)) {
            return data;
        }
        StringJoiner joiner = new StringJoiner(",");
        if (StringUtils.isNotBlank(data)) {
            joiner.add(data);
        }
        joiner.add(append);
        return joiner.toString();
    }

    /**
     * @Method: savePaymentInfo
     * @Description: 保存支付记录
     * @Date: 2022/1/17 11:39
     * @Author: Kanon
     * @param: aptBills
     * @return: AptPaymentInfo
     */
    private AptPaymentInfo savePaymentInfo(List<AptBill> aptBills) {
        //获取合并账单总金额
        var sunAmt = aptBills.stream().map(AptBill::getAmt).reduce(Double::sum).get();
        var paymentId = PrimaryKeyUtil.createPaymentId();
        String projectId = aptBills.stream().map(AptBill::getProjectId).filter(StringUtils::isNotEmpty)
                .findFirst().orElse(null);
        var createPaySessionDto = new CreatePaySessionDto();
        AptBill sampleBill = aptBills.get(0);
        createPaySessionDto.setProjectId(projectId);
        createPaySessionDto.setAmt(sunAmt);
        createPaySessionDto.setOrderNo(paymentId);
        createPaySessionDto.setPositionItem(sampleBill.getPositionItem());
        createPaySessionDto.setBuildingId(sampleBill.getBuildingId());
        createPaySessionDto.setPayTimeout(Duration.ofMinutes(15));

        var paymentSession = createPaymentSession(createPaySessionDto);
        String sessionId = paymentSession.getBody().getSessionInfo().getSessionId();
        var paymentInfo = AptPaymentInfo.builder()
                .id(paymentId)
                .amt(sunAmt)
                .paymentStatus(BillPaymentStatus.TO_BE_PAID)
                .projectId(sampleBill.getProjectId())
                .buildingId(sampleBill.getBuildingId())
                .floorId(sampleBill.getFloorId())
                .roomId(sampleBill.getRoomId())
                .positionItem(sampleBill.getPositionItem())
                .bindUserId(UserInfoUtils.getUserId())
                .deleted(0)
                .userProfileId(UserInfoUtils.getUserProfileId())
                .pspTransNo(Strings.EMPTY)
                .paymentTransNo(Strings.EMPTY)
                .failedReason(Strings.EMPTY)
                .xmlUrl(Strings.EMPTY)
                .ofdUrl(Strings.EMPTY)
                .agreementNo(Strings.EMPTY)
                .createTime(new Date())
                .updateTime(new Date())
                .paySession(sessionId)
                .billPayModule(BillPayModule.KERRY)
                .createBy(UserInfoUtils.getKerryAccount())
                .bu(sampleBill.getBu())
                .doco(sampleBill.getDoco())
                .alph(sampleBill.getAlph())
                .an8(sampleBill.getAn8())
                .unit(sampleBill.getUnit())
                .payAct(IdWorker.getFlowIdWorkerInstance().nextStrId())
                .description(ONLINE_PAY_DESC)
                .build();

        paymentInfoRepository.save(paymentInfo);

        var aptBillIds = aptBills.stream().map(AptBill::getId).collect(Collectors.toList());

        savePaymentBill(aptBillIds, paymentId);

        return paymentInfo;
    }

    /**
     * @Method: savePaymentBill
     * @Description: 保存支付关联关系
     * @Date: 2022/1/17 11:39
     * @Author: Kanon
     * @param: aptBillIds
     * @param: payMentId
     */
    private void savePaymentBill(List<Long> aptBillIds, String payMentId) {
        aptBillIds.forEach(id -> {
            var aptPaymentBill = new AptPaymentBill();
            aptPaymentBill.setBillId(id);
            aptPaymentBill.setPaymentInfoId(payMentId);
            aptPaymentBill.setDeleted(0);

            paymentBillRepository.save(aptPaymentBill);
        });

    }

    /**
     * @Method: updateStatusBy
     * @Description: 修改原始账单状态
     * @Date: 2022/1/17 11:41
     * @Author: Kanon
     * @param: aptBills
     * @param: billPaymentStatus
     */
    private void updateStatusBy(List<AptBill> aptBills, BillPaymentStatus billPaymentStatus) {
        aptBills.forEach(bill -> {
            if (BillPaymentStatus.PAID.equals(billPaymentStatus)
                    || BillPaymentStatus.DIRECT_DEBIT_PAID.equals(billPaymentStatus)) {
                bill.setStatus(BillStatus.ONLINE_PAID);
                bill.setPayTime(new Date());
                bill.setPaymentResult(ONLINE_PAY_DESC);
            }
            bill.setPaymentStatus(billPaymentStatus);
            bill.setUpdateTime(new Date());
            aptBillRepository.save(bill);
        });
    }

}
