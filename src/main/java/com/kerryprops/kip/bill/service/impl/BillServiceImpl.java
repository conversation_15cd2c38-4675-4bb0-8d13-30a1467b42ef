package com.kerryprops.kip.bill.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.kerryprops.kip.bill.common.aop.RedisLock;
import com.kerryprops.kip.bill.common.constants.AppConstants;
import com.kerryprops.kip.bill.common.constants.BillConstants;
import com.kerryprops.kip.bill.common.current.JsonUtils;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.enums.ContentTypeEnum;
import com.kerryprops.kip.bill.common.enums.EmailStatus;
import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.enums.SendBillStatus;
import com.kerryprops.kip.bill.common.enums.SendStatus;
import com.kerryprops.kip.bill.common.exceptions.AppException;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.utils.DateUtils;
import com.kerryprops.kip.bill.common.utils.jde.OracleJdbc;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.config.SyncJdeConfig;
import com.kerryprops.kip.bill.dao.BillEmailTraceRepository;
import com.kerryprops.kip.bill.dao.BillRepository;
import com.kerryprops.kip.bill.dao.BillSendConfigAn8LinkRepository;
import com.kerryprops.kip.bill.dao.BillSendConfigRepository;
import com.kerryprops.kip.bill.dao.BizContentReadRepository;
import com.kerryprops.kip.bill.dao.EFapiaoBillInvoiceRepository;
import com.kerryprops.kip.bill.dao.MessageTemplateRepository;
import com.kerryprops.kip.bill.dao.entity.BillEmailTrace;
import com.kerryprops.kip.bill.dao.entity.BillEntity;
import com.kerryprops.kip.bill.dao.entity.BillSendConfig;
import com.kerryprops.kip.bill.dao.entity.BillSendConfigAn8Link;
import com.kerryprops.kip.bill.dao.entity.BizContentReadEntity;
import com.kerryprops.kip.bill.dao.entity.MessageTemplateEntity;
import com.kerryprops.kip.bill.dao.entity.QBillEmailTrace;
import com.kerryprops.kip.bill.dao.entity.QBillEntity;
import com.kerryprops.kip.bill.dao.entity.QBizContentReadEntity;
import com.kerryprops.kip.bill.dao.entity.QEFapiaoBillInvoice;
import com.kerryprops.kip.bill.feign.clients.FileClient;
import com.kerryprops.kip.bill.feign.clients.MessageCenterClient;
import com.kerryprops.kip.bill.feign.clients.MessageClient;
import com.kerryprops.kip.bill.feign.entity.AttachmentDto;
import com.kerryprops.kip.bill.feign.entity.EmailReplyVo;
import com.kerryprops.kip.bill.feign.entity.EmailSendCommand;
import com.kerryprops.kip.bill.feign.entity.MessageDto;
import com.kerryprops.kip.bill.feign.entity.MessageType;
import com.kerryprops.kip.bill.feign.entity.NotificationPushRequest;
import com.kerryprops.kip.bill.feign.entity.OssPreSignedUrlResponse;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.IBillSendConfigService;
import com.kerryprops.kip.bill.service.IBillService;
import com.kerryprops.kip.bill.service.model.leg.Bill;
import com.kerryprops.kip.bill.service.model.leg.BillSelectVo;
import com.kerryprops.kip.bill.service.model.leg.BillUserSelectVo;
import com.kerryprops.kip.bill.service.model.s.BillSendHistoryReqDto;
import com.kerryprops.kip.bill.webservice.vo.req.BizBillReadReqVo;
import com.kerryprops.kip.bill.webservice.vo.req.DocoBillPayer;
import com.kerryprops.kip.bill.webservice.vo.req.EmailResultVo;
import com.kerryprops.kip.bill.webservice.vo.req.EmailStatusDto;
import com.kerryprops.kip.bill.webservice.vo.resp.BillPayer;
import com.kerryprops.kip.bill.webservice.vo.resp.ContentUnreadInfoVo;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffBillReceiverRespVo;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.impl.JPAUpdateClause;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IteratorUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.JdbcUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.BatchUpdateException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.Spliterators;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

import static com.kerryprops.kip.bill.common.constants.AppConstants.STAFF_BILL_SEND_BILL_LIST_REDIS_KEY;
import static com.kerryprops.kip.bill.common.utils.BillingFun.exceptionToNull;
import static com.kerryprops.kip.bill.log4j.BSConversationFilter.refreshCorrelationId;

/**
 * 电子账单Service业务层处理
 *
 * <AUTHOR>
 * @date 2020-10-15
 */
@Slf4j
@Service
@AllArgsConstructor
public class BillServiceImpl implements IBillService {

    private static final String JDE_SYNC_FLAG_TPEV03 = "Y";

    private static final int JDE_SYNC_BATCH_SIZE = 1000;

    private final FileClient fileClient;

    private final ObjectMapper objectMapper;

    private final JdbcTemplate jdbcTemplate;

    private final MessageClient messageClient;

    private final BillRepository billRepository;

    private final MessageCenterClient messageCenterClient;

    private final BillEmailTraceRepository emailTraceRepository;

    private final BillSendConfigRepository sendConfigRepository;

    private final BillSendConfigAn8LinkRepository linkRepository;

    private final MessageTemplateRepository messageTemplateRepository;

    private final IBillSendConfigService billSendConfigService;

    private final EFapiaoBillInvoiceRepository eFapiaoBillInvoiceRepository;

    private final BizContentReadRepository bizContentReadRepository;

    @Override
    public Page<BillSelectVo> selectBillVoList(Pageable pageable, Predicate predicate) {
        Page<BillEntity> billEntities = billRepository.findAll(predicate, pageable);
        Page<BillSelectVo> billSelectVos = billEntities.map(e -> BeanUtil.copy(e, BillSelectVo.class));
        return billSelectVos;
    }

    @Override
    public Bill selectBillVo(Predicate predicate) {
        BillEntity billEntity = billRepository.findOne(predicate).orElseGet(() -> null);
        if (billEntity == null) {
            return null;
        }
        return BeanUtil.copy(billEntity, Bill.class);
    }

    @Override
    public long countByPredicate(Predicate predicate) {
        return billRepository.count(predicate);
    }

    @Override
    public Boolean deleteBill(Predicate predicate) {
        Iterable<BillEntity> billEntityIterable = billRepository.findAll(predicate);
        if (billEntityIterable == null || billEntityIterable.iterator() == null) {
            log.info("bill not found");
            return false;
        }
        List<BillEntity> billEntities = Lists.newArrayList(billEntityIterable.iterator());
        for (BillEntity billEntity : billEntities) {
            try {
                if (billEntity.getTpStatus() != null && billEntity.getTpStatus() == 5) {
                    log.info("Bill already sent, cant delete.");
                    return false;
                }
                billEntity.setDelFlag("1");
                billEntity.setDeleteTime(new Date());
                billEntity.setDeleteBy(UserInfoUtils.getUser().getNickName());
                billEntity.setUpdateBy(UserInfoUtils.getUser().getNickName());
                billEntity.setUpdateTime(new Date());
                billRepository.save(billEntity);
            } catch (Exception e) {
                log.error("remove bill {} failed.", JSONObject.toJSONString(billEntity), e);
            }
        }
        return true;
    }

    @Override
    public List<Integer> selectBillDistinctYears() {
        return billRepository.selectBillDistinctYears();
    }

    @Override
    public List<Bill> selectBillVoList(Predicate predicate) {
        Iterable<BillEntity> billEntities = billRepository.findAll(predicate);
        if (billEntities == null) {
            return null;
        }
        List<BillEntity> billEntityList = IteratorUtils.toList(billEntities.iterator());
        return billEntityList.stream().map(e -> BeanUtil.copy(e, Bill.class)).collect(Collectors.toList());
    }

    @Override
    public Set<StaffBillReceiverRespVo> queryBillReceiversByDocoAn8(String jdeDoco, String an8) {
        if (StringUtils.isEmpty(jdeDoco) || StringUtils.isEmpty(an8)) {
            log.info("invalid query bill receivers param: jdeDoco={}, an8={}", jdeDoco, an8);
            return Collections.emptySet();
        }
        List<BillSendConfig> sendConfigs = sendConfigRepository.queryActiveConfigsByDoco(jdeDoco);
        if (CollectionUtils.isEmpty(sendConfigs)) {
            log.info("empty_bill_send_config query by jdeDoco={}, an8={}", jdeDoco, an8);
            return Collections.emptySet();
        }
        List<BillSendConfigAn8Link> an8Links = linkRepository.selectActiveAn8Links(sendConfigs.stream()
                .map(BillSendConfig::getId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(an8Links)) {
            log.info("empty_bill_send_an8Links query by jdeDoco={}, an8={}", jdeDoco, an8);
            return Collections.emptySet();
        }
        Map<Long, List<BillSendConfigAn8Link>> an8LinkMap = an8Links.stream()
                .collect(Collectors.groupingBy(BillSendConfigAn8Link::getConfigId));
        sendConfigs = sendConfigs.stream().filter(config -> {
            List<BillSendConfigAn8Link> tmpLst = an8LinkMap.get(config.getId());
            if (CollectionUtils.isEmpty(tmpLst)) {
                log.info("tmpLst is empty");
                return false;
            }
            return tmpLst.stream().anyMatch(tmpAn8Link -> Objects.equals(an8, tmpAn8Link.getAn8()));
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sendConfigs)) {
            log.info("empty_bill_send_config_and_an8 query by jdeDoco={}, an8={}", jdeDoco, an8);
            return Collections.emptySet();
        }
        return sendConfigs.stream().map(config -> StaffBillReceiverRespVo.builder()
                .userId(config.getTenantManagerId())
                .phoneNumber(config.getPhoneNumber())
                .userName(Optional.ofNullable(config.getEmailUsername())
                        .filter(StringUtils::isNotBlank).orElse("无账号"))
                .email(config.getEmail()).build()).collect(Collectors.toSet());
    }

    @Override
    public Map<DocoBillPayer, Set<StaffBillReceiverRespVo>> selectBatchBillReceivers(Set<DocoBillPayer> docoBillPayers) {
        if (Objects.isNull(docoBillPayers)) {
            log.info("docoBillPayer is null");
            return Collections.emptyMap();
        }
        Map<DocoBillPayer, Set<StaffBillReceiverRespVo>> resultMap = new HashMap<>();
        for (DocoBillPayer docoBillPayer : docoBillPayers) {
            List<BillSendConfig> billSendConfigs = billSendConfigService.filterBillSendConfigs(docoBillPayer);
            if (CollectionUtils.isEmpty(billSendConfigs)) {
                log.info("bill receiver(s) NOT found for doco: {}, an8: {}, alph: {}"
                        , docoBillPayer.getDoco(), docoBillPayer.getAn8(), docoBillPayer.getAlph());
                continue;
            }
            Set<StaffBillReceiverRespVo> receivers = billSendConfigs.stream()
                    .map(billSendConfig ->
                            StaffBillReceiverRespVo.builder()
                                    .userId(billSendConfig.getTenantManagerId())
                                    .userName(billSendConfig.getEmailUsername())
                                    .phoneNumber(billSendConfig.getPhoneNumber())
                                    .loginAccount(billSendConfig.getLoginNo())
                                    .email(billSendConfig.getEmail())
                                    .billReceiverConfigId(billSendConfig.getId())
                                    .build()).collect(Collectors.toSet());
            resultMap.put(docoBillPayer, receivers);
        }
        return resultMap;
    }

    @Override
    public Bill selectBillById(Long id) {
        BillEntity billEntity = billRepository.findById(id).get();
        if (Objects.isNull(billEntity)) {
            return null;
        }
        return BeanUtil.copy(billEntity, Bill.class);
    }

    @Override
    public List<Bill> selelctBillListByIds(List<Long> billIds) {
        if (CollectionUtils.isEmpty(billIds)) {
            return null;
        }
        Iterable<Long> billIdIt = StreamSupport.stream(Spliterators.spliteratorUnknownSize(billIds.iterator(), 0), false).collect(Collectors.toList());
        List<BillEntity> billEntities = billRepository.findAllById(billIdIt);
        if (CollectionUtils.isEmpty(billEntities)) {
            return null;
        }
        return billEntities.stream().map(e -> BeanUtil.copy(e, Bill.class)).collect(Collectors.toList());
    }

    @Override
    @Async
    @RedisLock(key = STAFF_BILL_SEND_BILL_LIST_REDIS_KEY, expire = 60L, suffixArgIndexArray = {2})
    public void sendBillList(List<Bill> bills, Integer sendType, String redisLockKeySuffix, String nickName) {
        log.info("sending_bill_list, size: {}, sendType: {}"
                , Optional.ofNullable(bills).map(List::size).orElse(0), sendType);
        if (CollectionUtils.isEmpty(bills)) {
            return;
        }
        if (sendType == 1) { // 发站内信
            sendMail(bills, nickName);
        } else if (sendType == 2) {// 发邮件
            sendEmail(bills, nickName);
        }
    }

    @Override
    public Boolean emailCallBack(EmailResultVo emailResultVo) {
        log.info("emailCallBack: {}", emailResultVo);
        if (emailResultVo.getSendStatus() == null
                || emailResultVo.getSendStatus().equals(EmailStatus.LOCAL_SUCCESS)
        ) {
            log.info("middle status.");
            return true;
        }
        String requestId = emailResultVo.getRequestId();
        Iterable<BillEmailTrace> emailTraces = emailTraceRepository.findAll(QBillEmailTrace.billEmailTrace.requestId.eq(requestId));
        if (emailTraces == null || emailTraces.iterator() == null) {
            log.info("No bill found. {}", requestId);
            return true;
        }
        Iterator<BillEmailTrace> billEmailTraceIterator = emailTraces.iterator();
        while (billEmailTraceIterator.hasNext()) {
            BillEmailTrace emailTrace = billEmailTraceIterator.next();
            log.info("Email trace: {}", emailTrace);
            try {
                Iterable<BillEmailTrace> billAllTraces = emailTraceRepository.findAll(QBillEmailTrace.billEmailTrace.billId.eq(emailTrace.getBillId()));
                if (billAllTraces == null || billAllTraces.iterator() == null) {
                    log.info("Cant find email traces by billId: {}", emailTrace.getBillId());
                    continue;
                }
                String currentRequestId = emailTrace.getRequestId();
                long maxBatchNo = 0l;
                long currentBatchNo = 0l;
                for (BillEmailTrace billAllTrace : billAllTraces) {
                    String tmpBatchNo = billAllTrace.getBatchNo();
                    if (StringUtils.isEmpty(tmpBatchNo)) {
                        continue;
                    }
                    long bn = Long.parseLong(tmpBatchNo);
                    if (bn > maxBatchNo) {
                        maxBatchNo = bn;
                    }
                    if (StringUtils.equals(currentRequestId, billAllTrace.getRequestId())) {
                        currentBatchNo = bn;
                    }
                }
                long tmpCurrentBatchNo = currentBatchNo;
                List<String> billAllRequestIds;
                if (currentBatchNo == 0) {
                    billAllRequestIds = Lists.newArrayList(billAllTraces.iterator())
                            .stream()
                            .map(BillEmailTrace::getRequestId)
                            .collect(Collectors.toList());
                } else {
                    billAllRequestIds = Lists.newArrayList(billAllTraces.iterator())
                            .stream()
                            .filter(e -> StringUtils.isNotEmpty(e.getBatchNo()) && Long.parseLong(e.getBatchNo()) == tmpCurrentBatchNo)
                            .map(BillEmailTrace::getRequestId)
                            .collect(Collectors.toList());
                }
                log.info("all email requestId {} for bill {}", Arrays.toString(billAllRequestIds.toArray()), emailTrace.getBillId());

                BillEntity billEntity = billRepository.findById(emailTrace.getBillId()).get();
                log.info("bill {}, bill_emailErr {}", billEntity.getId(), billEntity.getEmailErr());

                List<StaffBillReceiverRespVo> userInfoList = new ArrayList<>(JSONObject.parseArray(billEntity.getEmailErr(), StaffBillReceiverRespVo.class));
                StaffBillReceiverRespVo staffBillReceiverRespVo = userInfoList.stream().filter(e -> StringUtils.equals(e.getRequestId(), requestId)).findFirst().get();

                Boolean state;
                String msg;
                try {
                    if (emailResultVo.getSendStatus().equals(EmailStatus.LOCAL_FAILED)) {
                        msg = SendBillStatus.SEND_FAIL.getMessage() + "：邮件供应商服务不可用";
                        state = false;
                        staffBillReceiverRespVo.setSendStatus(msg);
                        staffBillReceiverRespVo.setSendTime(DateUtils.getTime());
                    } else {
                        EmailStatusDto emailStatusDto = emailResultVo.getResults().stream().filter(e -> StringUtils.equalsIgnoreCase(e.getEmail(), emailTrace.getEmail())).findFirst().get();
                        state = emailStatusDto.getState();
                        if (state) {
                            msg = SendBillStatus.SEND_SUCCESS.getMessage();
                        } else {
                            msg = SendBillStatus.SEND_FAIL.getMessage() + "：" + emailStatusDto.getMessage();
                        }
                        String deliverDate = emailStatusDto.getDeliverTime();
                        if (StringUtils.isEmpty(deliverDate)) {
                            deliverDate = DateUtils.getTime();
                        }
                        staffBillReceiverRespVo.setSendTime(deliverDate);
                    }
                } catch (Exception e) {
                    msg = SendBillStatus.SEND_FAIL.getMessage() + "：" + e.getMessage();
                    state = false;
                }
                staffBillReceiverRespVo.setSendStatus(msg);

                int processingCnt = 0;
                int successCnt = 0;
                int failedCnt = 0;
                for (StaffBillReceiverRespVo e : userInfoList) {
                    if (!billAllRequestIds.contains(e.getRequestId())) {
                        continue;
                    }
                    if (StringUtils.equalsIgnoreCase(e.getSendStatus(), SendBillStatus.SENDING.getMessage())) {
                        processingCnt++;
                    } else if (StringUtils.equalsIgnoreCase(e.getSendStatus(), SendBillStatus.SEND_SUCCESS.getMessage())) {
                        successCnt++;
                    } else {
                        failedCnt++;
                    }
                }
                Integer bs;
                if (processingCnt > 0) {
                    bs = BillConstants.MSG_SENDING;
                } else if (successCnt == billAllRequestIds.size()) {
                    bs = BillConstants.MSG_SUCCESS;
                } else if (failedCnt == billAllRequestIds.size()) {
                    bs = BillConstants.MSG_FAILURE;
                } else {
                    bs = BillConstants.MSG_PARTIAL_SUCCESS;
                }
                log.info("bill {} userInfo: {}", emailTrace.getBillId(), Arrays.toString(userInfoList.toArray()));
                if (currentBatchNo == maxBatchNo) {
                    billEntity.setEmailStatus(bs);
                }
                billEntity.setEmailErr(JSONObject.toJSONString(userInfoList));
                billRepository.updateEmailErr(billEntity.getId(), billEntity.getEmailStatus(), billEntity.getEmailErr());

                emailTrace.setEmailState(state);
                emailTrace.setMessage(msg);
                emailTrace.setStatus(1);
                emailTraceRepository.save(emailTrace);
            } catch (Exception e) {
                log.error("update email callback failed.", e);
            }
        }
        return true;
    }

    @Override
    public Page<BillUserSelectVo> searchPagedBill(Pageable pageable, Predicate predicate) {
        Page<BillEntity> billEntities = billRepository.findAll(predicate, pageable);
        return billEntities.map(e -> BeanUtil.copy(e, BillUserSelectVo.class));
    }

    @Override
    public Page<BillUserSelectVo> searchPagedBill(Pageable pageable, Specification specification) {
        Page<BillEntity> billEntities = billRepository.findAll(specification, pageable);
        return billEntities.map(e -> BeanUtil.copy(e, BillUserSelectVo.class));
    }

    @Override
    public Page<BillUserSelectVo> searchPagedBill(Pageable pageable, BillSendHistoryReqDto billSendHistoryReqDto) {
        String bus = billSendHistoryReqDto.getBus();
        List<String> buList = StringUtils.isEmpty(bus) ? Collections.EMPTY_LIST :
                Arrays.stream(StringUtils.split(bus, AppConstants.COMMA)).collect(Collectors.toList());

        Page<BillEntity> billEntities = billRepository.queryList(billSendHistoryReqDto,
                billSendHistoryReqDto.getTpAlph(), buList, pageable);
        Page<BillUserSelectVo> billUserSelectVos = billEntities.map(e -> BeanUtil.copy(e, BillUserSelectVo.class));
        return billUserSelectVos;
    }

    @Override
    public List<BillUserSelectVo> searchPagedBill(Specification specification) {
        List<BillEntity> billEntities = billRepository.findAll(specification);
        if (CollectionUtils.isEmpty(billEntities)) {
            return null;
        }
        return billEntities.stream().map(e -> BeanUtil.copy(e, BillUserSelectVo.class)).collect(Collectors.toList());
    }

    @Override
    public List<BillUserSelectVo> searchPagedBill(BillSendHistoryReqDto billSendHistoryReqDto) {
        String bus = billSendHistoryReqDto.getBus();
        List<String> buList = StringUtils.isEmpty(bus) ? Collections.EMPTY_LIST :
                Arrays.stream(StringUtils.split(bus, AppConstants.COMMA)).collect(Collectors.toList());

        List<BillEntity> billEntities = billRepository.queryList(billSendHistoryReqDto,
                billSendHistoryReqDto.getTpAlph(), buList);
        if (CollectionUtils.isEmpty(billEntities)) {
            return null;
        }
        return billEntities.stream().map(e -> BeanUtil.copy(e, BillUserSelectVo.class)).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Integer updateBillReadStatus(BizBillReadReqVo vo) {
        BillEntity bill = billRepository.findById(vo.getId()).get();
        if (Objects.isNull(bill)) {
            log.info("update_bill_read Bill can't be found. {}", vo);
            return 0;
        } else {
            log.info("update_bill_read received id: {}, mailOrMobileSource: {}", vo.getId(), vo.getMailOrMobileSource());
        }
        JPAUpdateClause jdeUpdate = billRepository.getJpaQueryFactory().update(QBillEntity.billEntity);
        // 阅读状态 0 未读、1已读
        jdeUpdate.set(QBillEntity.billEntity.readStatus, 1);
        // 更新阅读时间类型：0 站内信阅读 1 C端阅读
        if (vo.getMailOrMobileSource() != null) {
            if (vo.getMailOrMobileSource() == 1) {
                if (bill.getMobileReadTime() != null) { // 手机端最早阅读时间为非空，已读
                    return 0;
                }
                jdeUpdate.set(QBillEntity.billEntity.mobileReadTime, DateUtils.getNowDate());
            } else if (vo.getMailOrMobileSource() == 0) {
                if (bill.getMailReadTime() != null) { // 站内信最早阅读时间为非空，已读
                    return 0;
                }
                jdeUpdate.set(QBillEntity.billEntity.mailReadTime, DateUtils.getNowDate());
            }
        }
        BooleanExpression whereClause = QBillEntity.billEntity.id.eq(bill.getId());
        return (int) jdeUpdate.where(whereClause).execute();
    }

    @Override
    public ContentUnreadInfoVo userBillReadStatus(Predicate predicate) {
        List<Long> billIds = getUserUnreadBills(predicate);
        long unreadCount = Optional.ofNullable(billIds).map(List::size).orElse(0);

        ContentUnreadInfoVo billUnreadInfoVo = new ContentUnreadInfoVo();
        billUnreadInfoVo.setUnreadContentCount(unreadCount);
        billUnreadInfoVo.setHasUnreadContent(unreadCount > 0);
        return billUnreadInfoVo;
    }

    @Override
    public Collection<Long> getUserReadBillIds(Collection<Long> billIds) {
        if (CollectionUtils.isEmpty(billIds)) {
            return Collections.emptyList();
        }
        Long userId = UserInfoUtils.getUser().getUserId();
        return bizContentReadRepository.findContentIdsByTypeAndUserIdAndContentIds(ContentTypeEnum.B_BILL, userId, billIds);
    }

    @Override
    public boolean setUserBillRead(long id) {
        Long userId = UserInfoUtils.getUser().getUserId();
        Optional<BizContentReadEntity> op = bizContentReadRepository
                .findFirstByContentIdAndAndTypeAndUserId(id, ContentTypeEnum.B_BILL, userId);

        if (Objects.nonNull(op) && op.isPresent()) {
            return Boolean.TRUE;
        }

        BizContentReadEntity bizContentReadEntity = new BizContentReadEntity();
        bizContentReadEntity.setType(ContentTypeEnum.B_BILL);
        bizContentReadEntity.setContentId(id);
        bizContentReadEntity.setUserId(userId);
        bizContentReadRepository.save(bizContentReadEntity);
        return Boolean.TRUE;
    }

    @Override
    public Boolean setUserBillAllReaded(Predicate predicate) {
        List<Long> unreadBillIds = getUserUnreadBills(predicate);

        if (CollectionUtils.isEmpty(unreadBillIds)) {
            return Boolean.TRUE;
        }

        Long userId = UserInfoUtils.getUser().getUserId();
        List<BizContentReadEntity> bizContentReadEntities = new ArrayList<>();
        unreadBillIds.forEach(userBillId -> {
            BizContentReadEntity bizContentReadEntity = new BizContentReadEntity();
            bizContentReadEntity.setType(ContentTypeEnum.B_BILL);
            bizContentReadEntity.setUserId(userId);
            bizContentReadEntity.setContentId(userBillId);
            bizContentReadEntities.add(bizContentReadEntity);
        });
        bizContentReadRepository.saveAll(bizContentReadEntities);
        return Boolean.TRUE;
    }

    /**
     * 根据以下条件查找之前已同步的 还没发送的相同账单
     *
     * @param bill tpCo 公司编号
     *             tpAn8 地址号
     *             tpDct 账单类型
     *             tpDoco 合同号
     *             tpFyr 账单年
     *             tpPn 账单月
     *             tpCrtutime 账单时间
     * @return
     */
    @Override
    public List<BillEntity> selectOldBill(Bill bill) {
        return billRepository.selectOldBill(bill.getTpCo(), bill.getTpAn8(),
                bill.getTpDct(), bill.getTpDoco(), bill.getTpFyr(), bill.getTpPn(), bill.getTpCrtutime());
    }

    /**
     * 根据以下条件查找相同账单
     *
     * @param bill tpCo 公司编号
     *             tpAn8 地址号
     *             tpDct 账单类型
     *             tpDoco 合同号
     *             tpFyr 账单年
     *             tpPn 账单月
     * @return
     */
    @Override
    public List<BillEntity> selectDuplicateBill(Bill bill) {
        return billRepository.selectDuplicateBill(bill.getTpCo(), bill.getTpAn8(),
                bill.getTpDct(), bill.getTpDoco(), bill.getTpFyr(), bill.getTpPn());
    }

    /**
     * 根据需要进行 更新旧账单并插入新账单
     *
     * @param oldBill 旧账单
     * @param newBill 新账单
     * @return
     */
    @Override
    @Transactional
    public int insertOrUpdateNewBill(List<BillEntity> oldBill, Bill newBill) {
        log.info("oldBill: {}, \nnewBill: {}", oldBill, newBill);
        int result = 0;
        //有旧账单且没有发送，新账单也是Y  就将旧账单删除
        if (CollectionUtils.isNotEmpty(oldBill)) {
            log.info("delete oldBill : {}", JsonUtils.objToString(oldBill));
            billRepository.deleteAll(oldBill);
            result++;
        }
        if (0 == newBill.getTpStatus()) {
            newBill.setCreateTime(DateUtils.getNowDate());
            newBill.setMailStatus(BillConstants.MSG_NOT_SEND);
            newBill.setEmailStatus(BillConstants.MSG_NOT_SEND);
            BillEntity billEntity = BeanUtil.copy(newBill, BillEntity.class);
            billEntity.setDelFlag("0");
            log.info("save billEntity : {}", JsonUtils.objToString(billEntity));
            BillEntity savedBillEntity = billRepository.save(billEntity);
            newBill.setId(savedBillEntity.getId());
            result++;
        }
        log.info("insertOrUpdateNewBill result : {}", result);
        return result;
    }

    @Override
    public List<BillUserSelectVo> searchBill(Predicate predicate) {
        List<BillEntity> billEntities = Lists.newArrayList(billRepository.findAll(predicate));
        List<BillUserSelectVo> billUserSelectVos = billEntities.stream().map(e -> BeanUtil.copy(e, BillUserSelectVo.class)).collect(Collectors.toList());
        return billUserSelectVos;
    }

    /**
     * 更新账单阅读时间
     *
     * @param id
     * @return
     */
    @Override
    public int updateReadTime(Long id) {
        return billRepository.updateReadTime(id);
    }

    /**
     * 向JDE数据库回写标志位
     *
     * @param billList
     */
    @Override
    public void writeBackJDESyncFlag(List<Bill> billList) {
        //declare variables
        Connection conn = null;
        PreparedStatement ptst = null;
        if (CollectionUtils.isEmpty(billList)) {
            log.info("[Mall]no bills to sync JDE flag.");
            return;
        }

        List<BillEntity> billEntityList = billList.stream().map(bill -> {
            BillEntity billEntity = BeanUtil.copy(bill, BillEntity.class);
            return billEntity;
        }).filter(billEntity -> billEntity.getId() != null && billEntity.getId() > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(billEntityList)) {
            log.info("[Mall]no available bills to sync JDE flag");
            return;
        }

        try {
            long startTime = System.currentTimeMillis();
            conn = OracleJdbc.getOracleConnection();
            conn.setAutoCommit(false);

            //dct,co,an8,fyr,pn,crtutime
            String sql = new StringBuffer("update ")
                    .append(SyncJdeConfig.getBillTable())
                    .append(" set TPEV03 = 'Y' ")
                    .append("where ")
                    .append("TPDCT = ? ")
                    .append("and TPCO = ? ")
                    .append("and TPAN8 = ? ")
                    .append("and TPFYR = ? ")
                    .append("and TPPN = ? ")
                    .append("and TPCRTUTIME = ? ").toString();
            ptst = conn.prepareStatement(sql);

            int total = billEntityList.size();
            int batch = total / JDE_SYNC_BATCH_SIZE;
            batch = total % JDE_SYNC_BATCH_SIZE == 0 ? batch : batch + 1;
            for (int i = 0; i < batch; i++) {
                int fromIndex = JDE_SYNC_BATCH_SIZE * i;
                int toIndex = JDE_SYNC_BATCH_SIZE * i + JDE_SYNC_BATCH_SIZE;
                toIndex = toIndex >= total ? total : toIndex;

                List<BillEntity> billEntities = billEntityList.subList(fromIndex, toIndex);
                batchSyncFlagIntoJDE(billEntities, ptst);
            }

            //submit connection
            conn.commit();

            //write flag into local database
            List<BillEntity> syncBills = billEntityList.stream()
                    .filter(billEntity -> JDE_SYNC_FLAG_TPEV03.equals(billEntity.getTpEv03()))
                    .collect(Collectors.toList());
            List<Long> syncBillNumbers = syncBills.stream()
                    .map(BillEntity::getId).collect(Collectors.toList());
            log.info("[Mall]syncBillNumbers = {}", syncBillNumbers);
            batchSyncFlagIntoLocal(syncBills);

            long endTime = System.currentTimeMillis();
            log.info("[Mall]sync flag. the size of BillEntities={}, eclipsedTime={}",
                    billEntityList.size(), (endTime - startTime));
        } catch (BatchUpdateException e) {
            log.error("[Mall]batch update JDE flag failed. ErrMsg={}", e.getMessage());
            try {
                conn.rollback();
            } catch (SQLException ex) {
                log.error("[Mall]run rollback failed. ErrMsg={}", e.getMessage());
            }

        } catch (SQLException e) {
            log.error("[Mall]execute sql failed. ErrMsg={}", e.getMessage());
        } finally {
            JdbcUtils.closeStatement(ptst);
            JdbcUtils.closeConnection(conn);
        }
    }

    @Override
    public List<String> selectDocos(List<String> bus) {
        if (CollectionUtils.isEmpty(bus)) {
            return Collections.emptyList();
        }
        return billRepository.selectDocos(bus);
    }

    @Override
    public Set<BillPayer> selectBillPayers(String doco) {
        return Stream.concat(selectBillTenants(doco).stream(), selectEFapiaoPurchaser(doco).stream())
                .collect(Collectors.toSet());
    }

    @Override
    public Set<BillPayer> selectBillTenants(Set<String> docos, Set<String> an8s) {
        if (CollectionUtils.isEmpty(docos)) {
            return Collections.emptySet();
        }
        JPAQueryFactory jpaQueryFactory = billRepository.getJpaQueryFactory();
        List<Tuple> tupleList = jpaQueryFactory.select(QBillEntity.billEntity.tpAn8, QBillEntity.billEntity.tpAlph)
                .from(QBillEntity.billEntity)
                .where(QBillEntity.billEntity.tpDoco.in(docos).and(QBillEntity.billEntity.tpAn8.in(an8s))
                        .and(QBillEntity.billEntity.delFlag.eq("0")))
                .groupBy(QBillEntity.billEntity.tpAn8, QBillEntity.billEntity.tpAlph)
                .fetch();
        if (CollectionUtils.isEmpty(tupleList)) {
            return Collections.emptySet();
        }
        return tupleList.stream().map(tuple -> BillPayer.builder().tpAlph(tuple.get(1, String.class))
                .tpAn8(tuple.get(0, String.class)).build()).collect(Collectors.toSet());
    }

    @Override
    public List<String> selectDcts(List<String> bus) {
        if (CollectionUtils.isEmpty(bus)) {
            return Collections.emptyList();
        }
        return billRepository.selectDcts(bus);
    }

    @Override
    public List<BillPayer> fuzzySelectPayers(List<String> bus, String delFlag, String query) {
        if (CollectionUtils.isEmpty(bus)) {
            return Collections.emptyList();
        }

        BooleanExpression predicate = QBillEntity.billEntity.delFlag.eq(delFlag)
                .and(QBillEntity.billEntity.tpMcu.in(bus));

        if (StringUtils.isNotEmpty(query)) {
            BooleanExpression selectPredicate = QBillEntity.billEntity.tpAlph.contains(query)
                    .or(QBillEntity.billEntity.tpAn8.contains(query));

            int indexOfSplit = query.lastIndexOf("-");
            if (-1 != indexOfSplit) {
                String queryPurchaserName = query.substring(0, indexOfSplit);
                String queryAn8 = query.substring(indexOfSplit + 1);

                selectPredicate = selectPredicate.or((QBillEntity.billEntity.tpAlph.contains(queryPurchaserName))
                        .and(QBillEntity.billEntity.tpAn8.contains(queryAn8)));
            }

            predicate = predicate.and(selectPredicate);
        }

        List<Tuple> tuples = billRepository.getJpaQueryFactory()
                .select(QBillEntity.billEntity.tpAlph, QBillEntity.billEntity.tpAn8)
                .from(QBillEntity.billEntity)
                .where(predicate)
                .groupBy(QBillEntity.billEntity.tpAlph, QBillEntity.billEntity.tpAn8)
                .orderBy(QBillEntity.billEntity.tpAlph.asc())
                .fetch();

        if (CollectionUtils.isEmpty(tuples)) {
            return Collections.emptyList();
        }

        return tuples.stream().map(t -> new BillPayer(t.get(0, String.class), t.get(1, String.class)))
                .collect(Collectors.toList());
    }

    @Override
    public List<String> fuzzySelectDocos(List<String> bus, String query) {
        if (CollectionUtils.isEmpty(bus)) {
            return Collections.emptyList();
        }

        BooleanExpression predicate = QBillEntity.billEntity.delFlag.eq("0")
                .and(QBillEntity.billEntity.tpMcu.in(bus));
        if (StringUtils.isNotEmpty(query)) {
            predicate = predicate.and(QBillEntity.billEntity.tpDoco.contains(query));
        }

        JPAQueryFactory jpaQueryFactory = billRepository.getJpaQueryFactory();
        List<String> docos = jpaQueryFactory.select(QBillEntity.billEntity.tpDoco)
                .from(QBillEntity.billEntity)
                .where(predicate)
                .groupBy(QBillEntity.billEntity.tpDoco)
                .orderBy(QBillEntity.billEntity.tpDoco.asc())
                .fetch();
        return docos;
    }

    @Override
    public Set<String> getDocos() {
        LoginUser loginUser = UserInfoUtils.getUser();
        String loginAccount = loginUser.getLoginAccount();
        List<String> docoList = billSendConfigService.queryDocos(loginAccount);
        if (CollectionUtils.isEmpty(docoList)) {
            log.info("Tenant is not binding doco.");
            throw new RuntimeException("Tenant is not binding contract");
        }
        Set<String> docoSet = new HashSet<>(docoList);
        return docoSet;
    }

    @Override
    public Set<BillPayer> getBillPayers() {
        LoginUser loginUser = UserInfoUtils.getUser();
        String loginAccount = loginUser.getLoginAccount();
        List<BillPayer> billPayers = billSendConfigService.queryBillPayers(loginAccount);
        if (CollectionUtils.isEmpty(billPayers)) {
            log.info("Current login user is not binding an8.");
            throw new AppException(RespCodeEnum.AN8_ERROR);
        }
        Set<BillPayer> billPayerSet = new HashSet<>(billPayers);
        return billPayerSet;
    }

    @Override
    public Set<BillPayer> getInvoiceBillPayers() {
        LoginUser loginUser = UserInfoUtils.getUser();
        String loginAccount = loginUser.getLoginAccount();
        List<BillPayer> billPayers = billSendConfigService.queryBillPayers(loginAccount);
        if (CollectionUtils.isEmpty(billPayers)) {
            log.info("Current login user is not binding an8.");
            throw new AppException(RespCodeEnum.AN8_INVOICE_ERROR);
        }
        Set<BillPayer> billPayerSet = new HashSet<>(billPayers);
        return billPayerSet;
    }

    private Integer sendEmail(List<Bill> bills, String nickName) {
        var messageTemplateEntity = messageTemplateRepository.findById(1L)
                .orElseThrow(() -> new NoSuchElementException("no email template found."));

        int successCnt = 0;
        log.info("start_send_bill, {}", Arrays.toString(bills.stream().map(Bill::getId).toArray()));
        // 用户 ID 针对的 账单集合
        Map<StaffBillReceiverRespVo, Set<Bill>> userIdBillMap = new HashMap<>();
        //合同号和地址号 对应的 用户id 列表
        Map<String, Set<StaffBillReceiverRespVo>> docoAn8Map = new HashMap<>();
        Map<Long, String> billBatchNoMap = new HashMap<>();
        for (Bill bill : bills) {
            billBatchNoMap.put(bill.getId(), System.currentTimeMillis() + "");
            Integer bcs = null;
            Set<StaffBillReceiverRespVo> userInfoList = null;
            try {
                String key = bill.getTpDoco() + "_" + bill.getTpAn8();
                if (docoAn8Map.containsKey(key)) {
                    userInfoList = docoAn8Map.get(key);
                } else {
                    userInfoList = queryBillReceiversByDocoAn8(bill.getTpDoco(), bill.getTpAn8());
                    docoAn8Map.put(key, userInfoList);
                }
                if (CollectionUtils.isEmpty(userInfoList)) {
                    log.info("no_receiver_found_for_bill: {}", bill);
                    continue;
                }

                userInfoList.forEach(e -> {
                    if (!userIdBillMap.containsKey(e)) {
                        userIdBillMap.put(e, new HashSet<>());
                    }
                    userIdBillMap.get(e).add(bill);
                });
                bcs = SendStatus.MSG_SENDING.getIndex();
            } catch (Exception e) {
                log.error("find bill receiver failed." + bill.getId(), e);
                bcs = SendStatus.MSG_FAILED.getIndex();
            }

            try {
                billRepository.updateSendEMailInfo(bill.getId(), SendStatus.MSG_SENT.getIndex(), bcs,
                        DateFormatUtils.format(new Date(), DateUtils.YYYY_MM_DD_HH_MM_SS),
                        nickName);
            } catch (Exception e) {
                log.error("Update bill status failed.", e);
            }
        }

        for (Map.Entry<StaffBillReceiverRespVo, Set<Bill>> entry : userIdBillMap.entrySet()) {
            Set<Bill> userBills = entry.getValue();
            StaffBillReceiverRespVo user = entry.getKey();
            refreshCorrelationId();
            log.info("e_bill_sending_email_to_user: {}, bills: {}", exceptionToNull(() -> objectMapper.writeValueAsString(user))
                    , exceptionToNull(() -> userBills.stream().map(Bill::getId).collect(Collectors.toList())));
            String userId = user.getUserId();
            String sendStatus = null;
            String requestId = null;
            try {
                MessageTemplateEntity templateEntity = cloneTemplateEntity(messageTemplateEntity);
                EmailSendCommand emailSendResource = new EmailSendCommand();
                String email = user.getEmail();
                if (StringUtils.isEmpty(email)) {
                    log.info("No email found: {}", JSONObject.toJSONString(user));
                    continue;
                }
                email = email.trim();
                emailSendResource.setSendTos(Lists.newArrayList(email));

                String ch_date = DateUtils.parseDateToStr(DateUtils.CH_YYYY_MM_DD, new Date());
                //emailSendResource.setSubject(bill.getTpDl01() + "(" + ch_date + ")");
                emailSendResource.setSubject(templateEntity.getTempSubject());

                List<AttachmentDto> attachments = new ArrayList<>(userBills.size());
                List<List<String>> foreachList = new ArrayList<>();

                boolean seqNeeded = false;
                if (userBills.size() > 1) {
                    seqNeeded = true;
                }
                int index = 1;
                for (Bill bill : userBills) {
                    AttachmentDto attchment = new AttachmentDto();
                    String attachmentName = "新账单";
                    if (StringUtils.isNotEmpty(bill.getTpDl01())) {
                        attachmentName = bill.getTpDl01() + "-账期(" + bill.getTpFyr() + "-" + bill.getTpPn() + ")";
                    }
                    if (seqNeeded) {
                        attachmentName += "-" + index;
                        index++;
                    }
                    attachmentName += ".pdf";
                    attchment.setName(attachmentName);
                    List<OssPreSignedUrlResponse> preSignedUrlResponses = fileClient
                            .getPreSignedUrls(Lists.newArrayList(bill.getFileUrl()));
                    attchment.setUrl(preSignedUrlResponses.get(0).getPreSignedUrl());
                    attachments.add(attchment);

                    List<String> eachContent = new ArrayList<>();
                    eachContent.add(bill.getTpDl03());
                    eachContent.add(bill.getTpAlph());
                    eachContent.add(bill.getTpDl01());
                    eachContent.add(ch_date);
                    foreachList.add(eachContent);
                }

                emailSendResource.setBusinessType("BILL");
                emailSendResource.setHtml(true);
                emailSendResource.setText(parseContent(templateEntity, null, foreachList));
                emailSendResource.setAttachments(attachments);
                log.info("Send email payload: {}", emailSendResource);

                RespWrapVo<EmailReplyVo> emailReplyVoRespWrapVo = messageClient.sendWithReply(emailSendResource);// 发邮件
                if (emailReplyVoRespWrapVo == null
                        || !StringUtils.equals(emailReplyVoRespWrapVo.getCode(), RespCodeEnum.SUCCESS.getCode())
                        || emailReplyVoRespWrapVo.getData() == null
                        || StringUtils.isEmpty(emailReplyVoRespWrapVo.getData().getRequestId())) {
                    throw new RuntimeException("邮件服务不可用");
                }
                requestId = emailReplyVoRespWrapVo.getData().getRequestId();
                List<BillEmailTrace> emailTraces = new ArrayList<>();

                for (Bill bill : userBills) {
                    emailTraces.add(BillEmailTrace.builder()
                            .batchNo(billBatchNoMap.get(bill.getId()))
                            .requestId(requestId)
                            .billId(bill.getId())
                            .status(0)
                            .email(email)
                            .build());
                }

                try {
                    emailTraceRepository.saveAll(emailTraces);
                } catch (Exception e) {
                    log.error("save email trace failed.", e);
                }

                sendStatus = SendBillStatus.SENDING.getMessage();
                try {
                    //发送提醒
                    for (Bill bill : userBills) {
                        sendMsg(bill, Lists.newArrayList(userId));
                    }
                } catch (Exception x) {
                    log.error("Send notice failed.", x);
                }
            } catch (Exception e) {
                log.error("Send email failed.", e);
                sendStatus = SendBillStatus.SEND_FAIL.getMessage() + "：" + e.getMessage();
            }

            refreshCorrelationId();
            log.info("[Email][Completed] - Email delivery finished, proceeding to update email status for requestId={}", requestId);
            try {
                StaffBillReceiverRespVo staffBillReceiverRespVo = StaffBillReceiverRespVo.builder()
                        .requestId(requestId)
                        .sendStatus(sendStatus)
                        .userId(userId)
                        .email(user.getEmail())
                        .userName(user.getUserName())
                        .build();
                for (Bill bill : userBills) {
                    try {
                        BillEntity billEntity = billRepository.findById(bill.getId()).get();
                        Set<StaffBillReceiverRespVo> userInfoList = new HashSet<>();
                        if (StringUtils.isNotEmpty(billEntity.getEmailErr())) {
                            userInfoList.addAll(JSONObject.parseArray(billEntity.getEmailErr(), StaffBillReceiverRespVo.class));
                        }
                        staffBillReceiverRespVo.setBatchNo(billBatchNoMap.get(bill.getId()));
                        userInfoList.add(staffBillReceiverRespVo);
                        if (CollectionUtils.isNotEmpty(userInfoList)) {
                            List<StaffBillReceiverRespVo> sl = userInfoList.stream().sorted((o1, o2) -> {
                                try {
                                    long d1 = 0L;
                                    if (StringUtils.isNotEmpty(o1.getBatchNo())) {
                                        d1 = Long.parseLong(o1.getBatchNo());
                                    }
                                    long d2 = 0L;
                                    if (StringUtils.isNotEmpty(o2.getBatchNo())) {
                                        d2 = Long.parseLong(o2.getBatchNo());
                                    }
                                    long x = d1 - d2;
                                    return (int) x;
                                } catch (Exception e) {
                                    //ignore
                                }
                                return 0;
                            }).limit(50L).collect(Collectors.toList());
                            billEntity.setEmailErr(JSONObject.toJSONString(sl));
                        }
                        log.info("update_bill_send_email_error, bill: {}, email_error: {}"
                                , billEntity.getId(), billEntity.getEmailErr());
                        billRepository.updateEmailErr(billEntity.getId(), billEntity.getEmailErr());
                    } catch (Exception e) {
                        log.error("update_email_error update bill failed.", e);
                    }
                }
            } catch (Exception e) {
                log.error("generic_update_bill_email_error failed.", e);
            }
        }
        for (Bill bill : bills) {
            if (bill.getEmailStatus() != BillConstants.MSG_FAILURE) {
                successCnt++;
            }
        }
        return successCnt;
    }

    private Integer sendMail(List<Bill> bills, String nickName) {
        int successCnt = 0;
        for (Bill bill : bills) {
            try {
                var userInfoList = queryBillReceiversByDocoAn8(bill.getTpDoco(), bill.getTpAn8());
                String mailDate = DateFormatUtils.format(new Date(), DateUtils.YYYY_MM_DD_HH_MM_SS);
                if (CollectionUtils.isEmpty(userInfoList)) {
                    log.info("No receiver found {}", bill);
                    billRepository.updateSendMailInfo(bill.getId(), SendStatus.MSG_SENT.getIndex()
                            , BillConstants.MSG_SUCCESS, mailDate, nickName);
                    continue;
                }
                List<String> userIds = userInfoList.stream()
                        .map(StaffBillReceiverRespVo::getUserId)
                        .filter(StringUtils::isNotEmpty)
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(userIds)) {
                    log.info("cant send mail as no kip account binding, bill: [{}]", bill);
                    billRepository.updateSendMailInfo(bill.getId(), SendStatus.MSG_SENT.getIndex()
                            , BillConstants.MSG_SUCCESS, mailDate, nickName);
                    continue;
                }
                log.info("Send message to userId: {}", Arrays.toString(userIds.toArray()));
                //发送站内信
                int tbs = BillConstants.MSG_SUCCESS;
                if (!sendMsg(bill, userIds)) {
                    log.error("Send message failed.");
                    tbs = BillConstants.MSG_FAILURE;
                }

                billRepository.updateSendMailInfo(bill.getId(), SendStatus.MSG_SENT.getIndex()
                        , tbs, mailDate, nickName);

                log.info("sendMail success. bill : {} ", JsonUtils.objToString(bill));
                successCnt++;
            } catch (Exception e) {
                log.error("Send bill failed.", e);
            }
        }
        return successCnt;
    }

    private String parseContent(MessageTemplateEntity template, List<String> headList, List<List<String>> foreachList) {
        if (template == null) {
            throw new RuntimeException("该邮件模板不存在");
        }
        StringBuffer stringBuffer = new StringBuffer();
        String head = template.getHeadContent();
        String foreachContent = template.getForeachContent();
        if (headList != null && headList.size() > 0) {
            int k = 0;
            int listSize = headList.size();
            while (head.indexOf(AppConstants.TEMP_REPLACE_CONTENT) > -1) {
                int i = head.indexOf(AppConstants.TEMP_REPLACE_CONTENT);
//                if(listSize<(k+1)){
//                    head = head.substring(0, i) + head.substring(i+5);
//                }else{
//                    head = head.substring(0, i) + headList.get(k)+ head.substring(i+5);
//                }
                if (i == 0) {
                    head = headList.get(k) + head.substring(i + 5);
                } else {
                    if (k > headList.size() - 1) {
                        break;
                    }
                    head = head.substring(0, i) + headList.get(k) + head.substring(i + 5);
                }
                k++;
            }
        }
        stringBuffer.append(head);
        if (StringUtils.isNotEmpty(foreachContent)) {
            if (foreachList != null && foreachList.size() > 0) {
                for (List<String> stringList : foreachList) {
                    String forContent = foreachContent;
                    int k = 0;
                    while (forContent.contains(AppConstants.TEMP_REPLACE_CONTENT)) {
                        int i = forContent.indexOf(AppConstants.TEMP_REPLACE_CONTENT);
                        if (i == 0) {
                            forContent = stringList.get(k) + forContent.substring(i + 5);
                        } else {
                            if (k > stringList.size() - 1) {
                                break;
                            }
                            forContent = forContent.substring(0, i) + stringList.get(k) + forContent.substring(i + 5);
                        }
                        k++;
                    }
                    stringBuffer.append(forContent);
                }
            }
        }
        if (StringUtils.isNotEmpty(template.getTailContent())) {
            stringBuffer.append(template.getTailContent());
        }
        return stringBuffer.toString();
    }

    private boolean sendMsg(Bill bill, List<String> userIds) {
        if (CollectionUtils.isNotEmpty(userIds)) {
            userIds = userIds.stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(userIds)) {
            log.info("no userId to send message");
            return false;
        }
        //发送新账单消息
        String content = bill.getTpFyr() + "年" + bill.getTpPn() + "月  " + bill.getTpDl01() + "已送达，敬请查收";
        MessageDto messageDto = new MessageDto();
        messageDto.setToType("B");
        messageDto.setMessageType(MessageType.BILL_NOTICE);
        messageDto.setUserIds(userIds);
        messageDto.setContent(content);
        messageDto.setParams(Map.of("billId", bill.getId()));
        log.info("Send message payload: {}", JsonUtils.objToString(messageDto));
        var sendMessageResult = messageCenterClient.sendMessage(messageDto);
        var pushRequest = NotificationPushRequest.of(messageDto, "您有新账单已送达");
        if (!pushRequest.getUserIds().isEmpty()) {
            messageClient.pushNotification(pushRequest);
        }

        if (!RespWrapVo.isResponseValidWithData(sendMessageResult)) {
            log.error("Send message failed: {}", sendMessageResult);
            return false;
        }
        return true;
    }

    private List<Long> getUserUnreadBills(Predicate predicate) {
        long bUserId = UserInfoUtils.getUser().getUserId();
        BooleanExpression unreadBooleanExpression =
                bizContentReadRepository.getJpaQueryFactory()
                        .select(QBizContentReadEntity.bizContentReadEntity.contentId)
                        .from(QBizContentReadEntity.bizContentReadEntity)
                        .where(QBizContentReadEntity.bizContentReadEntity.type.eq(ContentTypeEnum.B_BILL)
                                .and(QBizContentReadEntity.bizContentReadEntity.userId.eq(bUserId)))
                        .contains(QBillEntity.billEntity.id).not();

        return billRepository.getJpaQueryFactory()
                .select(QBillEntity.billEntity.id)
                .from(QBillEntity.billEntity)
                .where(predicate, unreadBooleanExpression)
                .fetch();
    }

    private MessageTemplateEntity cloneTemplateEntity(MessageTemplateEntity entity) {
        if (entity == null) {
            return null;
        }
        MessageTemplateEntity e = BeanUtil.copy(entity, MessageTemplateEntity.class);
        e.setHeadContent(entity.getHeadContent());
        e.setForeachContent(entity.getForeachContent());
        return e;
    }

    private void batchSyncFlagIntoJDE(List<BillEntity> billEntities, PreparedStatement ptst)
            throws SQLException {
        if (CollectionUtils.isEmpty(billEntities)) {
            log.error("[Mall]billEntities is empty");
            return;
        }
        if (ptst == null) {
            log.error("[Mall]ptst is null");
            return;
        }
        for (int i = 0; i < billEntities.size(); i++) {
            BillEntity billEntity = billEntities.get(i);

            //dct,co,an8,fyr,pn,crtutime
            int index = 1;
            ptst.setString(index++, billEntity.getTpDct());
            ptst.setString(index++, billEntity.getTpCo());
            ptst.setString(index++, billEntity.getTpAn8());
            ptst.setInt(index++, billEntity.getTpFyr());
            ptst.setInt(index++, billEntity.getTpPn());
            ptst.setTimestamp(index++, new Timestamp(billEntity.getTpCrtutime().getTime()));
            ptst.addBatch();
        }

        //execute a batch of sql
        int[] updates = ptst.executeBatch();

        //update local data according to update result
        for (int j = 0; j < updates.length; j++) {
            //successful entry1: the number represents number of affected row
            //successful entry2: the number of affected rows is not available
            if (updates[j] >= 0 || updates[j] == Statement.SUCCESS_NO_INFO) {
                BillEntity billEntity = billEntities.get(j);
                billEntity.setTpEv03(JDE_SYNC_FLAG_TPEV03);
            }
        }

        //clear batch result
        ptst.clearBatch();
    }

    private void batchSyncFlagIntoLocal(List<BillEntity> billEntities) {
        if (CollectionUtils.isEmpty(billEntities)) {
            log.info("[Mall]no BillEntity to update TPEV03 field");
            return;
        }

        long startTime = System.currentTimeMillis();
        final String sql = "update kerry_bill set tp_ev03 = 'Y' where id = ? ";
        int[] updatedCountArray = jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {

            @Override
            public void setValues(PreparedStatement preparedStatement, int i) throws SQLException {
                BillEntity billEntity = billEntities.get(i);
                preparedStatement.setLong(1, billEntity.getId());
            }

            @Override
            public int getBatchSize() {
                return billEntities.size();
            }
        });

        for (int i = 0; i < updatedCountArray.length; i++) {
            int result = updatedCountArray[i];
            BillEntity billEntity = billEntities.get(i);
            if (result == Statement.EXECUTE_FAILED) {
                log.error("[Mall]fail to update BillEntity [id={}]", billEntity.getId());
            }
        }

        long endTime = System.currentTimeMillis();
        log.info("[Mall]batch sync flag into KIP. items={}, eclipsedTime={}",
                billEntities.size(), (endTime - startTime));
    }

    private Set<BillPayer> selectBillTenants(String doco) {
        if (StringUtils.isEmpty(doco)) {
            return Collections.emptySet();
        }
        JPAQueryFactory jpaQueryFactory = billRepository.getJpaQueryFactory();
        List<String> an8s = jpaQueryFactory.selectDistinct(QBillEntity.billEntity.tpAn8)
                .from(QBillEntity.billEntity)
                .where(QBillEntity.billEntity.tpDoco.eq(doco).and(QBillEntity.billEntity.delFlag.eq("0")))
                .fetch();
        if (CollectionUtils.isEmpty(an8s)) {
            return Collections.emptySet();
        }
        return an8s.stream().map(an8 -> {
            List<Tuple> tupleList = jpaQueryFactory.select(QBillEntity.billEntity.tpAlph, QBillEntity.billEntity.tpAn8)
                    .from(QBillEntity.billEntity)
                    .where((QBillEntity.billEntity.tpDoco.eq(doco))
                            .and(QBillEntity.billEntity.tpAn8.eq(an8))
                            .and(QBillEntity.billEntity.delFlag.eq("0")))
                    .orderBy(QBillEntity.billEntity.createTime.desc()).limit(1)
                    .fetch();
            if (CollectionUtils.isEmpty(tupleList)) {
                return null;
            }
            Tuple tuple = tupleList.get(0);
            return BillPayer.builder().tpAlph(tuple.get(0, String.class))
                    .tpAn8(tuple.get(1, String.class)).build();
        }).filter(Objects::nonNull).collect(Collectors.toSet());
    }

    private Set<BillPayer> selectEFapiaoPurchaser(String doco) {
        if (StringUtils.isEmpty(doco)) {
            return Collections.emptySet();
        }
        List<String> an8s = eFapiaoBillInvoiceRepository.getJpaQueryFactory()
                .selectDistinct(QEFapiaoBillInvoice.eFapiaoBillInvoice.an8)
                .from(QEFapiaoBillInvoice.eFapiaoBillInvoice)
                .where(QEFapiaoBillInvoice.eFapiaoBillInvoice.doco.eq(doco)
                        .and(QEFapiaoBillInvoice.eFapiaoBillInvoice.isDelete.eq(0)))
                .fetch();
        if (CollectionUtils.isEmpty(an8s)) {
            return Collections.emptySet();
        }
        return an8s.stream().map(an8 -> {
            List<Tuple> tupleList = eFapiaoBillInvoiceRepository.getJpaQueryFactory()
                    .select(QEFapiaoBillInvoice.eFapiaoBillInvoice.purchaserName, QEFapiaoBillInvoice.eFapiaoBillInvoice.an8)
                    .from(QEFapiaoBillInvoice.eFapiaoBillInvoice)
                    .where((QEFapiaoBillInvoice.eFapiaoBillInvoice.doco.eq(doco))
                            .and(QEFapiaoBillInvoice.eFapiaoBillInvoice.an8.eq(an8))
                            .and(QEFapiaoBillInvoice.eFapiaoBillInvoice.isDelete.eq(0)))
                    .orderBy(QEFapiaoBillInvoice.eFapiaoBillInvoice.createdTime.desc()).limit(1)
                    .fetch();
            if (CollectionUtils.isEmpty(tupleList)) {
                return null;
            }
            Tuple tuple = tupleList.get(0);
            return BillPayer.builder().tpAlph(tuple.get(0, String.class))
                    .tpAn8(tuple.get(1, String.class)).build();
        }).filter(Objects::nonNull).collect(Collectors.toSet());
    }


}
