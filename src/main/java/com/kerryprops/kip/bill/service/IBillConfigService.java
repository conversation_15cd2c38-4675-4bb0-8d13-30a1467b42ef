package com.kerryprops.kip.bill.service;


import com.kerryprops.kip.bill.dao.entity.BillConfigEntity;
import com.kerryprops.kip.bill.service.model.leg.BillConfig;
import com.kerryprops.kip.bill.webservice.vo.req.BillConfigAddDto;
import com.kerryprops.kip.bill.webservice.vo.req.BillConfigListDto;
import com.kerryprops.kip.bill.webservice.vo.req.BillConfigUpdateDto;

import java.util.List;
import java.util.Map;

/**
 * 账单配置项 Service接口
 *
 * <AUTHOR>
 * @date 2020-10-15
 */
public interface IBillConfigService {

    /**
     * 查询账单配置项 列表
     *
     * @param billConfig 账单配置项
     * @return 账单配置项 集合
     */
    List<BillConfig> selectBillConfigList(BillConfig billConfig);

    /**
     * 过去当前的账单来源  map  ===》 key 来源类型； value 来源服务
     *
     * @return
     */
    Map<String, String> getBillConfigList();

    /**
     * 根据账单配置项ID获取账单配置项信息。
     *
     * @param id 账单配置项的唯一标识ID
     * @return 对应的账单配置项实体实例
     */
    BillConfigEntity getBillConfig(Long id);

    /**
     * 根据条件查询账单配置项列表。
     *
     * @param dto 包含查询条件的账单配置项列表查询数据传输对象
     * @return 返回符合查询条件的账单配置项实体集合
     */
    List<BillConfigEntity> listBillConfigs(BillConfigListDto dto);

    /**
     * 添加新的账单配置项。
     *
     * @param dto 包含新增账单配置项数据的传输对象 {@link BillConfigAddDto}，包括必填的账单类型、来源名称及服务地址信息。
     * @return 新增后的账单配置项实体 {@link BillConfigEntity}。
     */
    BillConfigEntity addBillConfig(BillConfigAddDto dto);

    /**
     * 更新账单配置项信息。
     *
     * @param id  用于标识需要更新的账单配置项的唯一ID
     * @param dto 包含更新账单配置项数据的传输对象，数据包括账单类型、账单来源名称、服务地址及删除标识等信息
     * @return 更新后的账单配置项实体
     */
    BillConfigEntity updateBillConfig(Long id, BillConfigUpdateDto dto);

    /**
     * 根据账单配置项ID删除对应的账单配置项信息。
     *
     * @param id 需要删除的账单配置项的唯一标识ID
     * @return 被删除的账单配置项实体对象，如果指定ID的账单配置项不存在，则返回null
     */
    BillConfigEntity deleteBillConfigById(Long id);

}
