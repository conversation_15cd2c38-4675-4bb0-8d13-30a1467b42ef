package com.kerryprops.kip.bill.service.impl;

import com.google.common.collect.Lists;
import com.kerryprops.kip.bill.common.aop.BillErrorEnum;
import com.kerryprops.kip.bill.common.enums.AptPayDeleteStatusEnum;
import com.kerryprops.kip.bill.common.enums.AptPaySendJdeStatusEnum;
import com.kerryprops.kip.bill.common.enums.AptPayVerifyStatus;
import com.kerryprops.kip.bill.common.enums.BillPayModule;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.common.enums.PayCancelTypeEnum;
import com.kerryprops.kip.bill.common.enums.PaymentPayType;
import com.kerryprops.kip.bill.common.exceptions.CounterCashierBizException;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptPayRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPayConfig;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.dao.entity.HiveContextAware;
import com.kerryprops.kip.bill.dao.entity.QAptPay;
import com.kerryprops.kip.bill.dao.entity.QAptPaymentInfo;
import com.kerryprops.kip.bill.dao.impl.JDBCJdeCashierFeeRepository;
import com.kerryprops.kip.bill.feign.clients.KipInvoiceClient;
import com.kerryprops.kip.bill.feign.entity.FeeConfigVo;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.CounterCashierFeeService;
import com.kerryprops.kip.bill.service.PaymentBillService;
import com.kerryprops.kip.bill.service.model.s.AptPayInvoiceBo;
import com.kerryprops.kip.bill.service.model.s.BillInvoiceCalcDto;
import com.kerryprops.kip.bill.service.model.s.CreatePaySessionDto;
import com.kerryprops.kip.bill.utils.BillUtil;
import com.kerryprops.kip.bill.webservice.vo.req.CashierFeePayRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierFeePaysRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierFeePaysVerifyRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierFeePrepayRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierFeeQRCodePayRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierFeeSendJdeDO;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierFeePayDetailResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierFeePayReceptionExportVo;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierFeePayResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierFeePayStaffExportVo;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierFeePaysResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierQRCodePaymentResource;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import com.kerryprops.kip.bill.webservice.vo.resp.RoomAn8RespVo;
import com.kerryprops.kip.hiveas.webservice.resource.resp.RoomResp;
import com.kerryprops.kip.pmw.client.resource.SessionOutputResource;
import com.querydsl.jpa.impl.JPAQuery;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.common.utils.BillingFun.exportByEasyExcel;

/**
 * <AUTHOR> 2023-12-15 16:21:01
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class CounterCashierFeeServiceImpl extends AbstractCounterCashierService implements CounterCashierFeeService {

    private final JDBCJdeCashierFeeRepository jdbcJdeCashierFeeRepository;

    private final AptPayRepository aptPayRepository;

    private final AptBillRepository aptBillRepository;

    private final KipInvoiceClient kipInvoiceClient;

    private final PaymentBillService paymentBillService;

    @Value("${kip.payment.feePayTtl:48h}")
    private Duration payTimeout;

    @Override
    @Transactional
    public CashierFeePayResource feeOfflinePay(CashierFeePayRequest request) {
        AptPayConfig payConfig = getPayConfig(request);
        PositionItemResponse position = getPosition(request.getRoomId());
        FeeConfigVo feeConfig = getFeeConfig(request.getFeeId());
        Optional<RoomAn8RespVo> roomAn8 = aptBillRepository.findLastRoomAn8(request.getRoomId());

        AptPaymentInfo paymentInfo = request.toAptPaymentInfo(position, feeConfig);
        roomAn8.ifPresent(v -> v.fillRoomAn8(paymentInfo));
        paymentInfo.setPaymentStatus(BillPaymentStatus.PAID);
        aptPaymentInfoRepository.insert(paymentInfo);

        AptPay aptPay = request.toAptPay(paymentInfo, payConfig);
        roomAn8.ifPresent(v -> v.fillRoomAn8(aptPay));
        aptPayRepository.insert(aptPay);

        CashierFeePayResource resource = new CashierFeePayResource();
        resource.setPayAct(aptPay.getPayAct());
        resource.setPaymentInfoId(aptPay.getPaymentInfoId());
        return resource;
    }

    @Override
    @SuppressWarnings("deprecation")
    public Page<CashierFeePaysResource> queryFeePays(CashierFeePaysRequest request, Pageable pageable) {
        Integer isFinanceStaff = request.getIsFinanceStaff();
        if (Objects.equals(1, isFinanceStaff)) {
            QAptPay t1 = QAptPay.aptPay;
            QAptPaymentInfo t2 = QAptPaymentInfo.aptPaymentInfo;
            JPAQuery<AptPay> jpaQuery = aptPayRepository.getJpaQueryFactory()
                    .selectFrom(t1)
                    .leftJoin(t2)
                    // 排除超时取消||主动取消记录
                    .on(t1.paymentInfoId.eq(t2.id).and(
                            t2.cancelType.eq(PayCancelTypeEnum.TIMEOUT_CANCELLED.name())
                                    .or(t2.cancelType.eq(PayCancelTypeEnum.USER_CANCELLED.name()))
                    ))
                    .where(t2.id.isNull().and(request.toPredicates()))
                    .offset(pageable.getOffset())
                    .limit(pageable.getPageSize())
                    .orderBy(t1.createTime.desc(), t1.updateTime.desc());
            List<AptPay> fetch = jpaQuery.fetch();
            long count = fetch.isEmpty() ? 0L : jpaQuery.fetchCount();
            Page<AptPay> aptPays = new PageImpl<>(fetch, pageable, count);

            return aptPays.map(vo -> {
                BillInvoiceCalcDto dto = BillInvoiceCalcDto.of(vo);
                AptPayInvoiceBo bo = paymentBillService.calcBillInvoice(dto);

                CashierFeePaysResource resource = CashierFeePaysResource.of(vo);
                resource.setCanInvoiceBillAmount(bo.getCanInvoiceAmount());
                resource.setIsAdvanceBilling(bo.getIsAdvanceBilling());
                return resource;
            });

        } else {
            Page<AptPaymentInfo> paymentInfos = aptPaymentInfoRepository.findAll(request.toPredicates(), pageable);
            return paymentInfos.map(vo -> {
                BillInvoiceCalcDto dto = BillInvoiceCalcDto.of(vo);
                AptPayInvoiceBo bo = paymentBillService.calcBillInvoice(dto);

                CashierFeePaysResource resource = CashierFeePaysResource.of(vo);
                resource.setCanInvoiceBillAmount(bo.getCanInvoiceAmount());
                resource.setIsAdvanceBilling(bo.getIsAdvanceBilling());
                return resource;
            });
        }
    }

    @Override
    public void exportFeePays(CashierFeePaysRequest request, HttpServletResponse response) {
        Integer isFinanceStaff = request.getIsFinanceStaff();

        if (Objects.equals(1, isFinanceStaff)) {
            QAptPay t1 = QAptPay.aptPay;
            QAptPaymentInfo t2 = QAptPaymentInfo.aptPaymentInfo;
            JPAQuery<AptPay> jpaQuery = aptPayRepository.getJpaQueryFactory()
                    .selectFrom(t1)
                    .leftJoin(t2)
                    // 排除超时取消||主动取消记录
                    .on(t1.paymentInfoId.eq(t2.id).and(
                            t2.cancelType.eq(PayCancelTypeEnum.TIMEOUT_CANCELLED.name())
                                    .or(t2.cancelType.eq(PayCancelTypeEnum.USER_CANCELLED.name()))
                    ))
                    .where(t2.id.isNull().and(request.toPredicates()))
                    .orderBy(t1.createTime.desc(), t1.updateTime.desc());
            List<AptPay> aptPays = jpaQuery.fetch();

            List<CashierFeePayStaffExportVo> staffExportVos = aptPays.stream().map(vo -> {
                CashierFeePaysResource resource = CashierFeePaysResource.of(vo);
                CashierFeePayStaffExportVo cashierFeePayExportVo
                        = BeanUtil.copy(resource, CashierFeePayStaffExportVo.class);

                cashierFeePayExportVo.setProjectName(vo.getPositionItem().getProjectName());
                cashierFeePayExportVo.setBuildingName(vo.getPositionItem().getBuildingName());
                cashierFeePayExportVo.setRoomNo(vo.getPositionItem().getRoomName());
                return cashierFeePayExportVo;
            }).collect(Collectors.toList());

            final String FILE_NAME = "cashier_fee_pays_finance_staff_file";
            final String SHEET_NAME = "cashier_fee_pays_finance_staff";
            exportByEasyExcel(response, staffExportVos
                    , CashierFeePayStaffExportVo.class, FILE_NAME, SHEET_NAME);

        } else {
            Iterable<AptPaymentInfo> aptPaymentInfoIterable = aptPaymentInfoRepository.findAll(request.toPredicates());
            List<AptPaymentInfo> aptPaymentInfos = Lists.newArrayList(aptPaymentInfoIterable.iterator());

            List<CashierFeePayReceptionExportVo> receptionExportVos = aptPaymentInfos.stream()
                    .sorted(Comparator.comparing(AptPaymentInfo::getCreateTime).reversed())
                    .map(vo -> {
                        CashierFeePaysResource resource = CashierFeePaysResource.of(vo);
                        CashierFeePayReceptionExportVo cashierFeePaysExportVo
                                = BeanUtil.copy(resource, CashierFeePayReceptionExportVo.class);

                        cashierFeePaysExportVo.setProjectName(vo.getPositionItem().getProjectName());
                        cashierFeePaysExportVo.setBuildingName(vo.getPositionItem().getBuildingName());
                        cashierFeePaysExportVo.setRoomNo(vo.getPositionItem().getRoomName());
                        return cashierFeePaysExportVo;
                    }).collect(Collectors.toList());

            final String FILE_NAME = "cashier_fee_pays_reception_file";
            final String SHEET_NAME = "cashier_fee_pays_reception";
            exportByEasyExcel(response, receptionExportVos
                    , CashierFeePayReceptionExportVo.class, FILE_NAME, SHEET_NAME);
        }
    }

    @Override
    public CashierFeePayDetailResource queryFeePayDetail(String paymentInfoId) {
        AptPay aptPay = aptPayRepository.findTopByPaymentInfoId(paymentInfoId);
        if (Objects.isNull(aptPay)) {
            AptPaymentInfo paymentInfo = aptPaymentInfoRepository.findById(paymentInfoId).orElseThrow();
            return CashierFeePayDetailResource.of(paymentInfo);
        }
        return CashierFeePayDetailResource.of(aptPay);
    }

    @Override
    @Transactional
    public CashierFeePayResource prepay(CashierFeePrepayRequest request) {
        validPayConfig(request);
        PositionItemResponse position = getPosition(request.getRoomId());
        FeeConfigVo feeConfig = getFeeConfig(request.getFeeId());
        Optional<RoomAn8RespVo> roomAn8 = aptBillRepository.findLastRoomAn8(request.getRoomId());

        AptPaymentInfo paymentInfo = request.toAptPaymentInfo(position, feeConfig);
        roomAn8.ifPresent(v -> v.fillRoomAn8(paymentInfo));
        SessionOutputResource paySession = createPaySession(paymentInfo);
        paymentInfo.setPaySession(paySession.getBody().getSessionInfo().getSessionId());
        paymentInfo.setPaymentStatus(BillPaymentStatus.TO_BE_PAID);
        aptPaymentInfoRepository.insert(paymentInfo);

        CashierFeePayResource resource = new CashierFeePayResource();
        resource.setPaymentInfoId(paymentInfo.getId());
        resource.setPayAct(paymentInfo.getPayAct());
        return resource;
    }

    @Override
    public CashierFeePayDetailResource switchFeePayVerifyStatus(Long id) {
        AptPay aptPay = aptPayRepository.findById(id).orElse(null);

        if (Objects.isNull(aptPay)) {
            throw new CounterCashierBizException(BillErrorEnum.NOT_FOUND.getCode().toString()
                    , BillErrorEnum.NOT_FOUND.getMessage());
        }

        if (AptPayDeleteStatusEnum.DELETED.getCode().equals(aptPay.getDeletedAt())) {
            throw new CounterCashierBizException(BillErrorEnum.PARAMS_ERROR.getCode().toString()
                    , "数据已被取消，不支持此操作");
        }

        if (AptPaySendJdeStatusEnum.WRITE_TO_JDE_SUCCESS.getCode()
                .equals(aptPay.getSendJdeStatus())) {
            throw new CounterCashierBizException(BillErrorEnum.PARAMS_ERROR.getCode().toString()
                    , "数据已写入JDE，请勿操作");
        }

        AptPayVerifyStatus verifyStatusNew = AptPayVerifyStatus.VERIFIED.equals(aptPay.getVerifyStatus())
                ? AptPayVerifyStatus.TO_BE_VERIFIED : AptPayVerifyStatus.VERIFIED;
        aptPay.setVerifyStatus(verifyStatusNew);
        AptPay aptPayNew = aptPayRepository.save(aptPay);
        return CashierFeePayDetailResource.of(aptPayNew);
    }

    @Override
    public List<CashierFeePayDetailResource> conditionalVerifyFeePay(CashierFeePaysVerifyRequest req) {
        Iterable<AptPay> aptPaysIterable = aptPayRepository.findAll(req.toPredicates());

        if (!aptPaysIterable.iterator().hasNext()) {
            log.info("conditional_verify_fee_pay apt_pays_is_null");
            return Collections.emptyList();
        }

        for (AptPay aptPay : aptPaysIterable) {
            aptPay.setVerifyStatus(AptPayVerifyStatus.VERIFIED);
        }

        List<AptPay> aptPaysNew = aptPayRepository.saveAll(aptPaysIterable);
        return aptPaysNew.stream().map(CashierFeePayDetailResource::of).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public CashierFeePayDetailResource cancelFeePay(Long id) {
        AptPay aptPay = aptPayRepository.findById(id).orElse(null);

        if (Objects.isNull(aptPay)) {
            throw new CounterCashierBizException(BillErrorEnum.NOT_FOUND.getCode().toString()
                    , BillErrorEnum.NOT_FOUND.getMessage());
        }

        if (AptPayDeleteStatusEnum.DELETED.getCode().equals(aptPay.getDeletedAt())) {
            throw new CounterCashierBizException(BillErrorEnum.PARAMS_ERROR.getCode().toString()
                    , "数据已被取消，请勿重复操作");
        }

        aptPay.setDeletedAt(AptPayDeleteStatusEnum.DELETED.getCode());
        aptPay.setDeletedTime(new Date());
        aptPay.setDeletedBy(UserInfoUtils.getUser().getNickName());

        AptPay aptPayNew = aptPayRepository.save(aptPay);

        String paymentInfoId = aptPayNew.getPaymentInfoId();
        if (StringUtils.isEmpty(paymentInfoId)) {
            throw new CounterCashierBizException(BillErrorEnum.NOT_FOUND.getCode().toString()
                    , "未找到账单数据，请联系管理员");
        }

        List<AptBill> aptBills = aptBillRepository.findAllByBillNo(paymentInfoId);
        if (CollectionUtils.isNotEmpty(aptBills)) {
            aptBills.forEach(aptBill -> {
                aptBill.setStatus(BillStatus.TO_BE_PAID);
                aptBill.setPaymentStatus(BillPaymentStatus.TO_BE_PAID);
            });

            aptBillRepository.saveAll(aptBills);
        }

        AptPaymentInfo aptPaymentInfo = aptPaymentInfoRepository.findTopById(paymentInfoId);
        if (Objects.nonNull(aptPaymentInfo)) {
            aptPaymentInfo.setPaymentStatus(BillPaymentStatus.CANCEL);
            aptPaymentInfo.setCancelType(PayCancelTypeEnum.ADMIN_CANCELLED.name());
            aptPaymentInfoRepository.save(aptPaymentInfo);
        }

        return CashierFeePayDetailResource.of(aptPayNew);
    }

    @Override
    public Integer writeBackFee2JDE(String projectId) {
        CashierFeeSendJdeDO cashierFeeSendJdeDO = new CashierFeeSendJdeDO();
        cashierFeeSendJdeDO.setProjectId(projectId);
        Iterable<AptPay> aptPaysIterable = aptPayRepository.findAll(cashierFeeSendJdeDO.toPredicates());

        if (!aptPaysIterable.iterator().hasNext()) {
            log.info("cashier_fee write_back_fee_2_jde apt_pays_empty project_id=[{}]", projectId);
            return 0;
        }

        log.info("cashier_fee write_back_fee_2_jde start");
        AtomicInteger sumCount = new AtomicInteger();
        AtomicInteger errorCount = new AtomicInteger();
        aptPaysIterable.forEach(aptPay -> {
            sumCount.getAndIncrement();
            FeeConfigVo feeConfigVo = getFeeConfig(aptPay.getFeeId());
            try {
                jdbcJdeCashierFeeRepository.saveCashierFee(aptPay, feeConfigVo);
            } catch (Exception e) {
                errorCount.getAndIncrement();
                log.info("cashier_fee write_back_fee_2_jde apt_payment_info_id=[{}], apt_pay_act=[{}], error: "
                        , aptPay.getPaymentInfoId(), aptPay.getPayAct(), e);
            }
        });

        log.info("cashier_fee write_back_fee_2_jde finish, sum_count=[{}], error_count=[{}]", sumCount.get(), errorCount.get());
        return sumCount.get() - errorCount.get();
    }

    @Override
    public CashierQRCodePaymentResource counterCashierQRCodePayment(CashierFeeQRCodePayRequest request) {
        HiveContextAware hiveContext = new HiveContextAware() {

            @Override
            public String getProjectId() {
                return request.getProjectId();
            }

            @Override
            public String getBuildingId() {
                return request.getBuildingId();
            }

            @Override
            public String getFloorId() {
                return null;
            }

            @Override
            public String getRoomId() {
                return request.getRoomId();
            }

            @Override
            public PositionItemResponse getPositionItem() {
                return getPosition(request.getRoomId());
            }
        };
        Optional.ofNullable(request.getPayerInfo())
                .filter(v -> StringUtils.isNotBlank(v.getAn8()))
                .or(() -> aptBillRepository.findLastRoomAn8(request.getRoomId()))
                .ifPresent(v -> request.setPayerInfo(v));

        return new CounterCashierFeePaymentHandler(request, hiveContext).handle();
    }

    /**
     * 创建待支付会话
     */
    private SessionOutputResource createPaySession(AptPaymentInfo paymentInfo) {
        CreatePaySessionDto dto = new CreatePaySessionDto();
        dto.setProjectId(paymentInfo.getProjectId());
        dto.setBuildingId(paymentInfo.getBuildingId());
        dto.setAmt(paymentInfo.getAmt());
        dto.setOrderNo(paymentInfo.getId());
        dto.setPositionItem(paymentInfo.getPositionItem());
        dto.setPayTimeout(payTimeout);
        return paymentBillService.createPaymentSession(dto);
    }

    private FeeConfigVo getFeeConfig(Long feeId) {
        if (Objects.isNull(feeId)) {
            throw new CounterCashierBizException(BillErrorEnum.PARAMS_ERROR.getCode().toString()
                    , BillErrorEnum.PARAMS_ERROR.getMessage());
        }

        FeeConfigVo feeConfig = kipInvoiceClient.getFeeConfig(feeId);
        if (Objects.isNull(feeConfig)) {
            throw new CounterCashierBizException("400013", "未找到杂费配置，请与物业联系");
        }
        String taxRate = feeConfig.getTaxRate();
        if (StringUtils.isBlank(taxRate)) {
            throw new CounterCashierBizException("400014", "当前二级杂费未配置税率，请与物业联系");
        }
        return feeConfig;
    }

    private AptPayConfig getPayConfig(CashierFeePayRequest request) {
        String projectId = request.getProjectId();
        AptPayConfig vo = aptPayConfigRepository.findTopByProjectIdAndPaymentType(projectId, request.getPayType());
        if (Objects.isNull(vo)) {
            throw new CounterCashierBizException("400012", "当前楼盘未配置收款方式，请与物业联系");
        }
        return vo;
    }

    private void validPayConfig(CashierFeePrepayRequest request) {
        String projectId = request.getProjectId();
        List<String> type = List.of(PaymentPayType.WECHAT.getInfo(), PaymentPayType.ALIPAY.getInfo());
        if (!aptPayConfigRepository.existsByProjectIdAndPaymentTypeIn(projectId, type)) {
            throw new CounterCashierBizException("400015", "当前楼盘未配置远程收款方式，请与物业联系");
        }
    }

    private PositionItemResponse getPosition(String roomId) {
        RespWrapVo<RoomResp> roomRespVoRespWrapVo = hiveAsClient.getRoomById(roomId);
        if (!RespWrapVo.isResponseValidWithData(roomRespVoRespWrapVo)) {
            throw new RuntimeException("room not found: " + roomId);
        }
        RoomResp room = roomRespVoRespWrapVo.getData();
        PositionItemResponse positionItem = new PositionItemResponse();
        positionItem.setProjectName(room.getProject().getName());
        positionItem.setBuildingName(room.getBuilding().getName());
        positionItem.setFloorName(room.getFloor().getName());
        positionItem.setRoomName(room.getRoom().getRoomNo());
        return positionItem;
    }

    private class CounterCashierFeePaymentHandler extends AbstractCounterCashierPaymentHandler {

        private final Long feeId;

        private final BigDecimal feeAmount;

        private final HiveContextAware hiveInfo;

        public CounterCashierFeePaymentHandler(CashierFeeQRCodePayRequest request, HiveContextAware hiveContext) {
            super(request.getPayOption(), request.getAuthCode(), request.getDescription()
                    , request.getPayerInfo(), request.getPaymentTime());
            this.hiveInfo = hiveContext;
            this.feeAmount = BillUtil.formatAmount(request.getFeeAmount());
            this.feeId = request.getFeeId();
        }

        @Override
        BigDecimal getPaymentAmount() {
            return this.feeAmount;
        }

        @Override
        Supplier<HiveContextAware> fetchHiveContext() {
            return () -> this.hiveInfo;
        }

        @Override
        Consumer<AptPaymentInfo> beforePaymentInfoSaved() {
            return paymentInfo -> paymentInfo.setBillPayModule(BillPayModule.CASHIER_FEE);
        }

        @Override
        void handlePaySuccess(String payState, String payDesc) {
            // fee config
            FeeConfigVo feeConfig = getFeeConfig(this.feeId);
            String taxRate = feeConfig.getTaxRate();
            BigDecimal feeTax = new BigDecimal(taxRate);
            aptPaymentInfo.setFeeId(this.feeId);
            aptPaymentInfo.setFeeName(feeConfig.getName());
            aptPaymentInfo.setFeeTax(feeTax);
            BigDecimal feeTaxAmount = BillUtil.calcFeeTaxAmount(this.feeAmount, feeTax);
            aptPaymentInfo.setFeeTaxAmount(feeTaxAmount);
            super.handlePaySuccess(payState, payDesc);
        }

    }

}
