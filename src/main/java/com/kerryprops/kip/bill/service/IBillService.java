package com.kerryprops.kip.bill.service;

import com.kerryprops.kip.bill.dao.entity.BillEntity;
import com.kerryprops.kip.bill.service.model.leg.Bill;
import com.kerryprops.kip.bill.service.model.leg.BillSelectVo;
import com.kerryprops.kip.bill.service.model.leg.BillUserSelectVo;
import com.kerryprops.kip.bill.service.model.s.BillSendHistoryReqDto;
import com.kerryprops.kip.bill.webservice.vo.req.BizBillReadReqVo;
import com.kerryprops.kip.bill.webservice.vo.req.DocoBillPayer;
import com.kerryprops.kip.bill.webservice.vo.req.EmailResultVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillPayer;
import com.kerryprops.kip.bill.webservice.vo.resp.ContentUnreadInfoVo;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffBillReceiverRespVo;
import com.querydsl.core.types.Predicate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 电子账单Service接口
 *
 * <AUTHOR>
 * @date 2020-10-15
 */
public interface IBillService {

    Page<BillUserSelectVo> searchPagedBill(Pageable pageable, Specification specification);

    Page<BillUserSelectVo> searchPagedBill(Pageable pageable, BillSendHistoryReqDto billSendHistoryReqDto);

    List<BillUserSelectVo> searchPagedBill(Specification specification);

    List<BillUserSelectVo> searchPagedBill(BillSendHistoryReqDto billSendHistoryReqDto);

    /**
     * 根据查询参数 查询电子账单列表
     *
     * @param pageable
     * @param predicate
     * @return
     */
    Page<BillSelectVo> selectBillVoList(Pageable pageable, Predicate predicate);

    Map<DocoBillPayer, Set<StaffBillReceiverRespVo>> selectBatchBillReceivers(Set<DocoBillPayer> docoBillPayers);

    Boolean emailCallBack(EmailResultVo emailResultVo);

    /**
     * 查询电子账单
     *
     * @param id 电子账单ID
     * @return 电子账单
     */
    Bill selectBillById(Long id);

    /**
     * 根据ids 查询多个电子账单
     *
     * @param billIds
     * @return
     */
    List<Bill> selelctBillListByIds(List<Long> billIds);

    /**
     * 进行站内信发送账单  若旧账单的通知还未读 旧删除
     *
     * @return
     */
    void sendBillList(List<Bill> bills, Integer sendType, String redisLockKeySuffix, String nickName);

    Bill selectBillVo(Predicate predicate);

    long countByPredicate(Predicate predicate);

    Boolean deleteBill(Predicate predicate);

    List<Integer> selectBillDistinctYears();

    List<Bill> selectBillVoList(Predicate predicate);

    /**
     * 根据以下条件查找之前已同步的 还没发送的相同账单
     *
     * @param bill tpCo 公司编号
     *             tpAn8 地址号
     *             tpDct 账单类型
     *             tpDoco 合同号
     *             tpFyr 账单年
     *             tpPn 账单月
     *             tpCrtutime 账单时间
     * @return
     */
    List<BillEntity> selectOldBill(Bill bill);

    /**
     * 根据以下条件查找相同账单
     *
     * @param bill tpCo 公司编号
     *             tpAn8 地址号
     *             tpDct 账单类型
     *             tpDoco 合同号
     *             tpFyr 账单年
     *             tpPn 账单月
     * @return
     */
    List<BillEntity> selectDuplicateBill(Bill bill);

    /**
     * 用户自己查询用户的账单  仅对账单表做处理，只能查看已发送过的账单
     *
     * @param predicate
     * @return
     */
    Page<BillUserSelectVo> searchPagedBill(Pageable pageable, Predicate predicate);

    List<BillUserSelectVo> searchBill(Predicate predicate);

    /**
     * 根据需要进行 更新旧账单并插入新账单
     *
     * @param oldBill 旧账单
     * @param newBill 新账单
     * @return
     */
    int insertOrUpdateNewBill(List<BillEntity> oldBill, Bill newBill);

    /**
     * 进行更新 账单是否已读、最初阅读时间
     *
     * @param billReadReqVo
     * @return
     */
    Integer updateBillReadStatus(BizBillReadReqVo billReadReqVo);

    ContentUnreadInfoVo userBillReadStatus(Predicate predicate);

    Collection<Long> getUserReadBillIds(Collection<Long> billIds);

    boolean setUserBillRead(long id);

    Boolean setUserBillAllReaded(Predicate predicate);


    /**
     * 更新账单阅读时间
     *
     * @param id
     * @return
     */
    int updateReadTime(Long id);

    /**
     * 向JDE数据库回写标志位
     *
     * @param billList
     */
    void writeBackJDESyncFlag(List<Bill> billList);

    Set<BillPayer> selectBillPayers(String doco);

    List<String> selectDocos(List<String> bus);

    List<String> selectDcts(List<String> bus);

    List<BillPayer> fuzzySelectPayers(List<String> bus, String delFlag, String query);

    List<String> fuzzySelectDocos(List<String> bus, String query);

    Set<StaffBillReceiverRespVo> queryBillReceiversByDocoAn8(String jdeDoco, String an8);

    Set<String> getDocos();

    Set<BillPayer> getBillPayers();

    Set<BillPayer> getInvoiceBillPayers();

    Set<BillPayer> selectBillTenants(Set<String> docos, Set<String> an8s);

}
