package com.kerryprops.kip.bill.service.model.c;

import com.google.common.collect.ImmutableList;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillPushStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.common.jpa.QueryFilter;
import com.kerryprops.kip.bill.dao.entity.QAptBill;
import com.querydsl.core.types.Predicate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Optional;

/**
 * 账单查询的 view
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class ConsumerAptBillSearchReqBo implements QueryFilter {

    @Schema(title = "合同号")
    private String billNo;

    @Schema(title = "账单年开始")
    private Integer beginYear;

    @Schema(title = "账单年结束")
    private Integer endYear;

    @Schema(title = "账单月开始")
    private Integer beginMonth;

    @Schema(title = "账单月结束")
    private Integer endMonth;

    @Schema(title = "系统状态")
    private BillStatus status;

    @Schema(title = "用户支付状态")
    private BillPaymentStatus paymentStatus;

    @Schema(title = "用户支付状态list")
    private List<BillPaymentStatus> paymentStatusList;

    @Schema(title = "账单推送状态")
    private BillPushStatus pushStatus;

    @Schema(title = "楼盘id")
    private List<String> projectIds;

    @Schema(title = "楼栋ID")
    private List<String> buildingIds;

    @Schema(title = "楼层ID")
    private List<String> floorIds;

    @Schema(title = "房间ID")
    private List<String> roomIds;

    private String roomId;

    private List<String> lbsBuildingIds;

    @Override
    public List<Optional<Predicate>> predicates() {
        ImmutableList.Builder builder = ImmutableList.<Optional<Predicate>>builder();
        builder.add(Optional.ofNullable(0).map(QAptBill.aptBill.deletedAt::eq))
                .add(Optional.ofNullable(lbsBuildingIds).map(QAptBill.aptBill.buildingId::in))
                .add(Optional.ofNullable(buildingIds).map(QAptBill.aptBill.buildingId::in))
                .add(Optional.ofNullable(roomId).map(QAptBill.aptBill.roomId::eq))
                .add(Optional.ofNullable(roomIds).map(QAptBill.aptBill.roomId::in))
                .add(Optional.ofNullable(paymentStatus).map(QAptBill.aptBill.paymentStatus::eq))
                .add(Optional.ofNullable(paymentStatusList).map(QAptBill.aptBill.paymentStatus::in))
                .add(Optional.ofNullable(status).map(QAptBill.aptBill.status::eq))

        ;
        return builder.build();
    }

}
