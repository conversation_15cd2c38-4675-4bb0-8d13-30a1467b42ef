package com.kerryprops.kip.bill.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kerryprops.kip.bill.common.enums.JDEDocType;
import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.exceptions.ImportBillException;
import com.kerryprops.kip.bill.dao.entity.BillEntity;
import com.kerryprops.kip.bill.feign.clients.FileClient;
import com.kerryprops.kip.bill.feign.entity.UploadPublicFileResponse;
import com.kerryprops.kip.bill.service.IBillService;
import com.kerryprops.kip.bill.service.KerryBillImportService;
import com.kerryprops.kip.bill.service.model.leg.Bill;
import com.kerryprops.kip.bill.webservice.impl.StaffSyncBillController;
import com.kerryprops.kip.bill.webservice.vo.req.ImportKerryBillRequest;
import com.kerryprops.kip.bill.webservice.vo.req.ImportKerryBillRequest.ImportKerryBill;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class KerryBillImportServiceImpl implements KerryBillImportService {

    private FileClient fileClient;

    private IBillService billService;

    @Override
    @Transactional
    public int importBill(ImportKerryBillRequest request) {
        // 只有一条账单的情况
        List<ImportKerryBill> importBills = request.getBills();
        if (importBills.size() == 1) {
            saveImportBill(importBills.get(0));
            return 1;
        }
        // 有多条账单的情况
        int count = 0;
        List<Object> detailErrorInfos = Lists.newArrayList();
        for (ImportKerryBill importedBill : importBills) {
            try {
                saveImportBill(importedBill);
                count = count + 1;
            } catch (Exception e) {
                Map<String, Object> objectMap = Maps.newHashMap();
                objectMap.put("errorMessage", e.getMessage());
                objectMap.put("bill", importedBill);
                detailErrorInfos.add(objectMap);
            }
        }
        if (CollectionUtils.isEmpty(detailErrorInfos)) {
            return count;
        }
        ImportBillException exception;
        if (count > 0) {
            exception = new ImportBillException(RespCodeEnum.IMPORT_BILL_ERROR);
        } else {
            exception = new ImportBillException(RespCodeEnum.IMPORT_BILL_PARTIAL_ERROR);
        }
        exception.setImportedBillCount(count);
        exception.setDetailErrorInfos(detailErrorInfos);
        throw exception;
    }

    /**
     * 参考 {@link StaffSyncBillController#sync()}, 处理方式与同步JDE对账单一致。
     */
    private void saveImportBill(ImportKerryBill importedBill) {
        if (!JDEDocType.VC.name().equals(importedBill.getDocumentType())) {
            throw new ImportBillException(RespCodeEnum.IMPORT_DOC_TYPE_ERROR);
        }
        if (!importedBill.getBillFilename().endsWith(".pdf")) {
            throw new ImportBillException(RespCodeEnum.IMPORT_FILE_FORMAT_ERROR);
        }
        // upload file to Oss
        String fileUrl = importedBill.getBillFileUrl();
        String newFileUrl;
        if (fileUrl.contains("kip") && fileUrl.contains("oss-cn-shanghai.aliyuncs.com")) {
            // 已经上传至kerry KIP的oss，直接保存file链接即可
            newFileUrl = fileUrl;
        } else {
            UploadPublicFileResponse uploadPublicFileResponse;
            try {
                uploadPublicFileResponse = fileClient.uploadPrivateFile(fileUrl);
                newFileUrl = uploadPublicFileResponse.getUrl();
            } catch (Exception e) {
                log.error("import_bill_upload_file_error", e);
                throw new ImportBillException(RespCodeEnum.IMPORT_BILL_PDF_FAILED);
            }
        }
        Bill bill = importObjToBill(importedBill);
        bill.setTpStatus(0);
        bill.setFileUrl(newFileUrl);
        List<BillEntity> oldBill = null;
        List<BillEntity> duplicateBills = billService.selectDuplicateBill(bill);
        if (CollectionUtils.isNotEmpty(duplicateBills)) {
            List<BillEntity> existingNewBills = duplicateBills.stream()
                    .filter(e -> e.getTpCrtutime() != null && e.getTpCrtutime().compareTo(bill.getTpCrtutime()) >= 0)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(existingNewBills)) {
                log.error("import_bill_already_existed: existingNewBills {}", existingNewBills);
                throw new ImportBillException(RespCodeEnum.IMPORT_BILL_DUPLICATED);
            }
            //根据以下条件查找之前已同步的 还没发送的相同账单
            //查找条件：旧账单(账单打印时间 < 当前Bill的账单打印时间) && 其余指标相同(dct,co,an8,fyr,pn)
            duplicateBills.removeAll(existingNewBills);
            oldBill = duplicateBills.stream().filter(e -> e.getTpStatus() == 0).collect(Collectors.toList());
        }
        billService.insertOrUpdateNewBill(oldBill, bill);
    }

    private Bill importObjToBill(ImportKerryBill importedBill) {
        Bill bill = new Bill();
        bill.setTpDct(importedBill.getDocumentType());
        bill.setTpDl01(importedBill.getDocumentTextDesc());
        bill.setTpGtfilenm(importedBill.getBillFilename());
        bill.setTpCo(importedBill.getCompanyCode());
        bill.setTpDl03(importedBill.getCompanyName());
        bill.setTpAn8(importedBill.getAn8());
        bill.setTpAlph(importedBill.getAlph());
        bill.setTpDoco(importedBill.getDoco());
        bill.setTpMcu(importedBill.getMcu());
        bill.setTpDc(importedBill.getMcuDesc());
        bill.setTpCrtutime(importedBill.getGenerateTime());
        bill.setTpFyr(importedBill.getBillYear());
        bill.setTpPn(importedBill.getBillMonth());
        bill.setTpEv01(importedBill.getDisplayWebsite());
        bill.setTpEv02(importedBill.getBillSource());
        bill.setFormatDate(importedBill.getPrintTime());
        bill.setTpGtitnm(importedBill.getDocumentEngDesc());
        // TODO 是否能映射成hive实际的jde room？？？
        bill.setTpUnit(importedBill.getUnit());
        bill.setBillMonth(bill.getTpFyr() * 100 + bill.getTpPn());
        return bill;
    }

}
