package com.kerryprops.kip.bill.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.kerryprops.kip.bill.common.enums.ContentTypeEnum;
import com.kerryprops.kip.bill.common.enums.InvoiceTypeEnum;
import com.kerryprops.kip.bill.common.enums.NotifyType;
import com.kerryprops.kip.bill.common.enums.SendStatus;
import com.kerryprops.kip.bill.dao.BizContentReadRepository;
import com.kerryprops.kip.bill.dao.EFapiaoBillInvoiceRepository;
import com.kerryprops.kip.bill.dao.EFapiaoSendRecordRepository;
import com.kerryprops.kip.bill.dao.entity.BizContentReadEntity;
import com.kerryprops.kip.bill.dao.entity.EFapiaoBillInvoice;
import com.kerryprops.kip.bill.dao.entity.EFapiaoSendRecord;
import com.kerryprops.kip.bill.dao.entity.QBizContentReadEntity;
import com.kerryprops.kip.bill.dao.entity.QEFapiaoBillInvoice;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.clients.HiveClient;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.IBillService;
import com.kerryprops.kip.bill.service.KerryBillEFapiaoService;
import com.kerryprops.kip.bill.service.model.s.BillEFapiaoSearchReqBo;
import com.kerryprops.kip.bill.service.model.s.BizBillEFapiaoSearchReqBo;
import com.kerryprops.kip.bill.webservice.vo.req.DocoBillPayer;
import com.kerryprops.kip.bill.webservice.vo.req.EmailResultVo;
import com.kerryprops.kip.bill.webservice.vo.req.EmailStatusDto;
import com.kerryprops.kip.bill.webservice.vo.req.SmsResultCallbackVo;
import com.kerryprops.kip.bill.webservice.vo.req.SmsStatusVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillInvoiceReceiverResource;
import com.kerryprops.kip.bill.webservice.vo.resp.BillInvoiceResource;
import com.kerryprops.kip.bill.webservice.vo.resp.BillPayer;
import com.kerryprops.kip.bill.webservice.vo.resp.BizBillInvoiceResource;
import com.kerryprops.kip.bill.webservice.vo.resp.ContentUnreadInfoVo;
import com.kerryprops.kip.bill.webservice.vo.resp.SendStatusResource;
import com.kerryprops.kip.bill.webservice.vo.resp.SendStatusResourceSpec;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffBillReceiverRespVo;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.common.enums.BillInvoiceBizTypeEnum.getInvoiceBizDesc;
import static com.kerryprops.kip.bill.common.utils.BillingFun.str2ZonedDatetimeFun;

@Slf4j
@Service
@AllArgsConstructor
public class KerryBillEFapiaoServiceImpl implements KerryBillEFapiaoService {

    private static final Function<SendStatus, SendStatusResource> parseSendStatusResource = sendStatus -> Optional
            .ofNullable(sendStatus).map(st -> {
                SendStatusResource sendStatusResource = new SendStatusResource();
                sendStatusResource.setStatus(st.getIndex());
                return sendStatusResource;
            }).orElse(new SendStatusResource(SendStatus.MSG_NOT_SEND.getIndex(), null));

    private static final BiFunction<String, EFapiaoBillInvoice, BillInvoiceResource> setBasicFields = (projectId, info) -> {
        BillInvoiceResource resource = new BillInvoiceResource();
        resource.setProjectId(projectId);
        resource.setId(info.getId());
        resource.setPayerAn8(info.getAn8());
        // TODO alph 付款人&购方公司是否一样？？
        resource.setPayerAlph(info.getPurchaserName());
        resource.setPurchaserName(info.getPurchaserName());
        resource.setDoco(info.getDoco());
        resource.setMcu(info.getMcu());
        resource.setBizType(getInvoiceBizDesc(info.getBizType()));
        resource.setLeaseTermStart(info.getLeaseTermStart());
        resource.setLeaseTermEnd(info.getLeaseTermEnd());
        // JDE单元号
        resource.setJdeUnit(info.getJdeUnit());
        resource.setSalesBillNo(info.getSalesBillNo());
        // 销方名称
        resource.setSellerName(info.getSellerName());
        // fapiao info
        resource.setInvoiceUrl(info.getPdfUrl());
        resource.setOfdUrl(info.getOfdUrl());
        resource.setXmlUrl(info.getXmlUrl());
        // 含税金额
        resource.setInvoiceAmt(info.getAmountWithTax());
        // 发票号
        resource.setInvoiceNo(info.getInvoiceNo());
        // 正常、作废、红冲
        resource.setInvoiceStatus(info.getState() != null ? info.getState().getDesc() : null);
        // gvat增值税普通发票, svat增值税专用发票 等等
        resource.setInvoiceType(info.getInvoiceType());
        Optional.ofNullable(info.getInvoiceType()).map(InvoiceTypeEnum::fromKipCode).map(InvoiceTypeEnum::getDesc)
                .ifPresent(resource::setInvoiceTypeName);
        // 发票出票时间
        /*resource.setIssuanceDate(info.getCreatedTime()
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));*/
        resource.setIssuanceDate(info.getPaperDrewDate());
        resource.setEmailSendStatus(parseSendStatusResource.apply(info.getEmailSendStatus()));
        resource.setSmsSendStatus(parseSendStatusResource.apply(info.getSmsSendStatus()));
        resource.setMessageSendStatus(parseSendStatusResource.apply(info.getMessageSendStatus()));

        return resource;
    };

    private final HiveClient hiveService;

    private final IBillService billService;

    private final HiveAsClient hiveAsClient;

    private final EFapiaoSendRecordRepository fapiaoSendRecordRepository;

    private final EFapiaoBillInvoiceRepository eFapiaoBillInvoiceRepository;

    private final BizContentReadRepository bizContentReadRepository;

    @Override
    public Page<BillInvoiceResource> eFapiaoList(BillEFapiaoSearchReqBo reqBo, Pageable pageable) {
        if (StringUtils.isBlank(reqBo.getMcu())) {
            List<String> mcus = hiveAsClient.populateDataFields(reqBo.getProjectId(), reqBo.getBuildingIds());
            reqBo.setMcus(mcus);
        }
        Page<EFapiaoBillInvoice> paged = eFapiaoBillInvoiceRepository.findAll(reqBo.toPredicates(), pageable);
        // basic fields
        Page<BillInvoiceResource> pagedResource = paged.map(info -> setBasicFields.apply(reqBo.getProjectId(), info));
        // receivers
        queryInvoiceReceivers(pagedResource);
        // query hive info
        queryHiveInfo(pagedResource);
        // query send status
        querySendStatus(pagedResource);
        return pagedResource;
    }

    @Override
    public List<BillInvoiceResource> eFapiaoList(BillEFapiaoSearchReqBo reqBo) {
        List<EFapiaoBillInvoice> list = Lists.newLinkedList(eFapiaoBillInvoiceRepository.findAll(reqBo.toPredicates()));
        // basic fields
        List<BillInvoiceResource> billInvoiceResources = list.stream()
                .map(info -> setBasicFields.apply(reqBo.getProjectId(), info)).collect(Collectors.toList());
        // receivers
        queryInvoiceReceivers(billInvoiceResources);
        // query hive info
        queryHiveInfo(billInvoiceResources);
        // query send status
        querySendStatus(billInvoiceResources);
        return billInvoiceResources;
    }

    @Override
    public Page<BizBillInvoiceResource> userEFapiaoList(BizBillEFapiaoSearchReqBo reqBo, Pageable pageable) {
        Set<BillPayer> billPayers = billService.getInvoiceBillPayers();
//        Set<String> tenantAlphs = billPayers.stream().map(BillPayer::getTpAlph).collect(Collectors.toSet());
        Set<String> tenantAn8s = billPayers.stream().map(BillPayer::getTpAn8).collect(Collectors.toSet());

        reqBo.setDocos(billService.getDocos());
//        reqBo.setAlphs(tenantAlphs);
        reqBo.setAn8s(tenantAn8s);
        Page<EFapiaoBillInvoice> paged = eFapiaoBillInvoiceRepository.findAll(reqBo.toPredicates(), pageable);

        Long userId = UserInfoUtils.getUser().getUserId();
        Collection<Long> invoiceIds = paged.stream().map(EFapiaoBillInvoice::getId).collect(Collectors.toSet());
        List<Long> readContentIds = bizContentReadRepository
                .findContentIdsByTypeAndUserIdAndContentIds(ContentTypeEnum.E_FAPIAO, userId, invoiceIds);
        return paged.map(bi -> {
            BizBillInvoiceResource resource = new BizBillInvoiceResource();
            resource.setBizType(getInvoiceBizDesc(bi.getBizType()));
            resource.setId(bi.getId());
            resource.setInvoiceAmt(bi.getAmountWithTax());
            resource.setInvoiceNo(bi.getInvoiceNo());
            resource.setInvoiceType(bi.getInvoiceType());
            Optional.ofNullable(bi.getInvoiceType()).map(InvoiceTypeEnum::fromKipCode).map(InvoiceTypeEnum::getDesc)
                    .ifPresent(resource::setInvoiceTypeName);
            resource.setIssuanceDate(bi.getPaperDrewDate());
            resource.setPurchaserName(bi.getPurchaserName());
            resource.setSellerName(bi.getSellerName());
            resource.setLeaseTermStart(bi.getLeaseTermStart());
            resource.setLeaseTermEnd(bi.getLeaseTermEnd());
            resource.setInvoiceUrl(bi.getPdfUrl());
            resource.setInvoiceOfdUrl(bi.getOfdUrl());
            resource.setInvoiceXmlUrl(bi.getXmlUrl());
            resource.setInvoiceState(bi.getState().getDesc());
            resource.setIsRead(readContentIds.contains(bi.getId()));
            return resource;
        });
    }

    @Override
    public ContentUnreadInfoVo userInvoiceReadStatus() {
        BizBillEFapiaoSearchReqBo reqBo = getUserReqBo();
        List<Long> invoiceIds = getUserUnreadInvoices(reqBo.toPredicates());
        long unreadCount = Optional.ofNullable(invoiceIds).map(List::size).orElse(0);

        ContentUnreadInfoVo invoiceUnreadInfoVo = new ContentUnreadInfoVo();
        invoiceUnreadInfoVo.setUnreadContentCount(unreadCount);
        invoiceUnreadInfoVo.setHasUnreadContent(unreadCount > 0);
        return invoiceUnreadInfoVo;
    }

    @Override
    public Boolean setUserInvoiceReaded(Long id) {
        Long userId = UserInfoUtils.getUser().getUserId();
        Optional<BizContentReadEntity> op = bizContentReadRepository
                .findFirstByContentIdAndAndTypeAndUserId(id, ContentTypeEnum.E_FAPIAO, userId);

        if (Objects.nonNull(op) && op.isPresent()) {
            return Boolean.TRUE;
        }

        BizContentReadEntity bizContentReadEntity = new BizContentReadEntity();
        bizContentReadEntity.setType(ContentTypeEnum.E_FAPIAO);
        bizContentReadEntity.setUserId(userId);
        bizContentReadEntity.setContentId(id);
        bizContentReadRepository.save(bizContentReadEntity);
        return true;
    }

    @Override
    public Boolean setUserInvoiceAllReaded() {
        BizBillEFapiaoSearchReqBo reqBo = getUserReqBo();
        List<Long> unreadInvoiceIds = getUserUnreadInvoices(reqBo.toPredicates());

        if (CollectionUtils.isEmpty(unreadInvoiceIds)) {
            return Boolean.TRUE;
        }

        Long userId = UserInfoUtils.getUser().getUserId();
        List<BizContentReadEntity> bizContentReadEntities = new ArrayList<>();
        unreadInvoiceIds.forEach(unreadInvoiceId -> {
            BizContentReadEntity bizContentReadEntity = new BizContentReadEntity();
            bizContentReadEntity.setType(ContentTypeEnum.E_FAPIAO);
            bizContentReadEntity.setUserId(userId);
            bizContentReadEntity.setContentId(unreadInvoiceId);
            bizContentReadEntities.add(bizContentReadEntity);
        });
        bizContentReadRepository.saveAll(bizContentReadEntities);
        return Boolean.TRUE;
    }

    @Override
    public List<BillPayer> fuzzyQueryPurchaserNames(List<String> bus, String query) {
        if (CollectionUtils.isEmpty(bus)) {
            return Collections.emptyList();
        }

        BooleanExpression predicate = QEFapiaoBillInvoice.eFapiaoBillInvoice.isDelete.eq(0)
                .and(QEFapiaoBillInvoice.eFapiaoBillInvoice.mcu.in(bus));

        if (StringUtils.isNotEmpty(query)) {
            BooleanExpression selectPredicate = QEFapiaoBillInvoice.eFapiaoBillInvoice.purchaserName.contains(query)
                    .or(QEFapiaoBillInvoice.eFapiaoBillInvoice.an8.contains(query));

            int indexOfSplit = query.lastIndexOf("-");
            if (-1 != indexOfSplit) {
                String queryPurchaserName = query.substring(0, indexOfSplit);
                String queryAn8 = query.substring(indexOfSplit + 1);

                selectPredicate = selectPredicate.or((QEFapiaoBillInvoice.eFapiaoBillInvoice.purchaserName.contains(queryPurchaserName))
                        .and(QEFapiaoBillInvoice.eFapiaoBillInvoice.an8.contains(queryAn8)));
            }

            predicate = predicate.and(selectPredicate);
        }

        List<Tuple> tuples = eFapiaoBillInvoiceRepository.getJpaQueryFactory()
                .select(QEFapiaoBillInvoice.eFapiaoBillInvoice.purchaserName
                        , QEFapiaoBillInvoice.eFapiaoBillInvoice.an8)
                .from(QEFapiaoBillInvoice.eFapiaoBillInvoice)
                .where(predicate)
                .groupBy(QEFapiaoBillInvoice.eFapiaoBillInvoice.purchaserName
                        , QEFapiaoBillInvoice.eFapiaoBillInvoice.an8)
                .orderBy(QEFapiaoBillInvoice.eFapiaoBillInvoice.purchaserName.asc())
                .fetch();

        if (CollectionUtils.isEmpty(tuples)) {
            return Collections.emptyList();
        }

        return tuples.stream().map(t -> new BillPayer(t.get(0, String.class), t.get(1, String.class)))
                .collect(Collectors.toList());
    }

    @Override
    public String queryUrlsByBillInvoiceId(long id) {
        Optional<EFapiaoBillInvoice> billFapiaoOp = eFapiaoBillInvoiceRepository.findById(id);
        if (billFapiaoOp.isPresent() && StringUtils.isNotBlank(billFapiaoOp.get().getPdfUrl())) {
            EFapiaoBillInvoice eFapiaoBillInvoice = billFapiaoOp.get();
            JSONObject billInvoiceUrlJson = new JSONObject();

            billInvoiceUrlJson.put("pdfUrl", eFapiaoBillInvoice.getPdfUrl());
            billInvoiceUrlJson.put("ofdUrl", eFapiaoBillInvoice.getOfdUrl());
            billInvoiceUrlJson.put("xmlUrl", eFapiaoBillInvoice.getXmlUrl());
            return billInvoiceUrlJson.toString();
        } else {
            throw new RuntimeException("发票不存在");
        }
    }

    @Override
    @Transactional
    public void handleSmsCallback(SmsResultCallbackVo smsResultCallbackVo) {
        try {
            long requestId = smsResultCallbackVo.getRequestId();
            List<EFapiaoSendRecord> records = fapiaoSendRecordRepository.findAllByRequestId(String.valueOf(requestId));
            if (CollectionUtils.isEmpty(records) || CollectionUtils.isEmpty(smsResultCallbackVo.getResults())) {
                log.info("send record not found");
                return;
            }
            Map<String, SmsStatusVo> map = smsResultCallbackVo.getResults().stream()
                    .collect(Collectors.toMap(SmsStatusVo::getPhone, Function.identity()));
            records.forEach(sendRecord -> {
                SmsStatusVo vo = map.get(sendRecord.getSms());
                if (Objects.nonNull(vo)) {
                    sendRecord.setSendTime(str2ZonedDatetimeFun.apply(vo.getDeliverTime()));
                    sendRecord.setSendStatus(Boolean.TRUE.equals(vo.getState()) ? SendStatus.MSG_SUCCESS
                            : SendStatus.MSG_FAILED);
                    Optional.ofNullable(vo.getMessage()).map(s -> StringUtils.abbreviate(s, 250))
                            .ifPresent(sendRecord::setSendDesc);
                    log.info("update sms send status for: {} to {}", sendRecord.getSms(), sendRecord.getSendStatus());
                }
            });
            fapiaoSendRecordRepository.saveAll(records);
            updateBillFapiaoStatus(records.get(0).getBatchNo(), EFapiaoBillInvoice::getSmsSendStatus
                    , EFapiaoBillInvoice::setSmsSendStatus);
        } catch (Exception e) {
            log.error("error_handle_sms_callback", e);
        }
    }

    @Override
    @Transactional
    public void handleEmailCallback(EmailResultVo emailResultVo) {
        try {
            String requestId = emailResultVo.getRequestId();
            List<EFapiaoSendRecord> records = fapiaoSendRecordRepository.findAllByRequestId(String.valueOf(requestId));
            if (CollectionUtils.isEmpty(records) || CollectionUtils.isEmpty(emailResultVo.getResults())) {
                log.info("not found send record");
                return;
            }
            Map<String, EmailStatusDto> map = emailResultVo.getResults().stream()
                    .collect(Collectors.toMap(dto -> StringUtils.lowerCase(dto.getEmail()), Function.identity()));
            for (EFapiaoSendRecord sendRecord : records) {
                EmailStatusDto statusDto = map.get(StringUtils.lowerCase(sendRecord.getEmail()));
                if (Objects.nonNull(statusDto)) {
                    sendRecord.setSendTime(str2ZonedDatetimeFun.apply(statusDto.getDeliverTime()));
                    sendRecord.setSendStatus(Boolean.TRUE.equals(statusDto.getState()) ? SendStatus.MSG_SUCCESS
                            : SendStatus.MSG_FAILED);
                    Optional.ofNullable(statusDto.getMessage()).map(s -> StringUtils.abbreviate(s, 250))
                            .ifPresent(sendRecord::setSendDesc);
                    log.info("update email send status for: {} to {}", sendRecord.getEmail(), sendRecord.getSendStatus());
                }
            }
            fapiaoSendRecordRepository.saveAll(records);
            updateBillFapiaoStatus(records.get(0).getBatchNo(), EFapiaoBillInvoice::getEmailSendStatus
                    , EFapiaoBillInvoice::setEmailSendStatus);
        } catch (Exception e) {
            log.error("error_handle_email_callback", e);
        }
    }

    private BizBillEFapiaoSearchReqBo getUserReqBo() {
        Set<BillPayer> billPayers = billService.getInvoiceBillPayers();
        Set<String> tenantAn8s = billPayers.stream().map(BillPayer::getTpAn8).collect(Collectors.toSet());

        BizBillEFapiaoSearchReqBo reqBo = new BizBillEFapiaoSearchReqBo();
        reqBo.setDocos(billService.getDocos());
        reqBo.setAn8s(tenantAn8s);
        return reqBo;
    }

    private List<Long> getUserUnreadInvoices(Predicate predicate) {
        long bUserId = UserInfoUtils.getUser().getUserId();
        BooleanExpression unreadBooleanExpression =
                bizContentReadRepository.getJpaQueryFactory()
                        .select(QBizContentReadEntity.bizContentReadEntity.contentId)
                        .from(QBizContentReadEntity.bizContentReadEntity)
                        .where(QBizContentReadEntity.bizContentReadEntity.type.eq(ContentTypeEnum.E_FAPIAO)
                                .and(QBizContentReadEntity.bizContentReadEntity.userId.eq(bUserId)))
                        .contains(QEFapiaoBillInvoice.eFapiaoBillInvoice.id).not();

        return eFapiaoBillInvoiceRepository.getJpaQueryFactory()
                .select(QEFapiaoBillInvoice.eFapiaoBillInvoice.id)
                .from(QEFapiaoBillInvoice.eFapiaoBillInvoice)
                .where(predicate, unreadBooleanExpression)
                .fetch();
    }

    private void updateBillFapiaoStatus(String batchNo, Function<EFapiaoBillInvoice, SendStatus> fun
            , BiConsumer<EFapiaoBillInvoice, SendStatus> consumer) {
        List<EFapiaoSendRecord> records = fapiaoSendRecordRepository.findAllByBatchNo(batchNo);
        SendStatus validSendStatus = validateSendStatus(records);
        doUpdateBillFapiaoStatus(records.get(0).getBillFapiaoId(), validSendStatus, fun, consumer);
    }

    private void doUpdateBillFapiaoStatus(long id, SendStatus validSendStatus
            , Function<EFapiaoBillInvoice, SendStatus> fun, BiConsumer<EFapiaoBillInvoice, SendStatus> consumer) {
        Optional<EFapiaoBillInvoice> eFapiaoBillInvoiceOp = eFapiaoBillInvoiceRepository
                .findById(id);
        if (eFapiaoBillInvoiceOp.isEmpty()) {
            return;
        }
        EFapiaoBillInvoice eFapiaoBillInvoice = eFapiaoBillInvoiceOp.get();
        SendStatus oldSendStatus = fun.apply(eFapiaoBillInvoice);
        if (!Objects.equals(oldSendStatus, validSendStatus)) {
            consumer.accept(eFapiaoBillInvoice, validSendStatus);
            eFapiaoBillInvoiceRepository.save(eFapiaoBillInvoice);
            log.info("update_conflict_bill_fapiao_status id: {}, from: {}, to: {}"
                    , id, oldSendStatus, validSendStatus);
        }
    }

    private Iterable<BillInvoiceResource> queryInvoiceReceivers(Iterable<BillInvoiceResource> iterableResource) {
        // query invoice receivers
        Map<DocoBillPayer, List<BillInvoiceResource>> an8InvoiceMap = Maps.newLinkedHashMap();
        iterableResource.forEach(res -> {
            if (!StringUtils.isAnyEmpty(res.getDoco(), res.getPayerAn8())) {
                DocoBillPayer key = DocoBillPayer.builder().doco(res.getDoco()).an8(res.getPayerAn8())
                        .alph(res.getPurchaserName()).build();
                if (!an8InvoiceMap.containsKey(key)) {
                    an8InvoiceMap.put(key, Lists.newLinkedList());
                }
                an8InvoiceMap.get(key).add(res);
            }
        });
        // query invoice receivers
        Map<DocoBillPayer, Set<StaffBillReceiverRespVo>> docoAn8SetMap
                = billService.selectBatchBillReceivers(an8InvoiceMap.keySet());
        if (docoAn8SetMap != null && !docoAn8SetMap.isEmpty()) {
            for (Map.Entry<DocoBillPayer, List<BillInvoiceResource>> docoAn8ListEntry : an8InvoiceMap.entrySet()) {
                DocoBillPayer docoAn8 = docoAn8ListEntry.getKey();
                List<BillInvoiceResource> staffBillRespVos = docoAn8ListEntry.getValue();
                Set<StaffBillReceiverRespVo> userInfoList = docoAn8SetMap.get(docoAn8);
                if (CollectionUtils.isNotEmpty(userInfoList)) {
                    for (BillInvoiceResource resource : staffBillRespVos) {
                        Set<BillInvoiceReceiverResource> receiverResources = userInfoList.stream().map(ui -> {
                            BillInvoiceReceiverResource receiverResource = new BillInvoiceReceiverResource();
                            receiverResource.setUserId(ui.getUserId());
                            receiverResource.setUserName(ui.getUserName());
                            receiverResource.setPhoneNumber(ui.getPhoneNumber());
                            receiverResource.setLoginAccount(ui.getLoginAccount());
                            receiverResource.setEmail(ui.getEmail());
                            receiverResource.setBillReceiverConfigId(ui.getBillReceiverConfigId());
                            return receiverResource;
                        }).collect(Collectors.toSet());
                        resource.setUserInfoList(receiverResources);
                    }
                }
            }
        }
        return iterableResource;
    }

    private Iterable<BillInvoiceResource> queryHiveInfo(Iterable<BillInvoiceResource> iterableResource) {
        iterableResource.forEach(res -> {
            if (StringUtils.isNotBlank(res.getMcu()) && StringUtils.isNotBlank(res.getJdeUnit())) {
                HiveClient.HiveBuBuildingRequest hiveReq = new HiveClient.HiveBuBuildingRequest();
                hiveReq.setBu(List.of(res.getMcu().split(",")));
                hiveReq.setJdeRoomNo(List.of(res.getJdeUnit().split(",")));
                List<HiveClient.HiveBuBuildingResponse> hiveResp = hiveService.getBuildingByBuAndJdeRoomNo(List.of(hiveReq));
                if (CollectionUtils.isNotEmpty(hiveResp)) {
                    res.setBuildingId(hiveResp.stream().map(HiveClient.HiveBuBuildingResponse::getId)
                            .filter(StringUtils::isNotBlank).collect(Collectors.joining(",")));
                    res.setBuildingName(hiveResp.stream().map(HiveClient.HiveBuBuildingResponse::getName)
                            .filter(StringUtils::isNotBlank).collect(Collectors.joining(",")));
                    Optional.ofNullable(hiveResp.get(0)).map(HiveClient.HiveBuBuildingResponse::getProjectId)
                            .filter(StringUtils::isNotBlank).ifPresent(res::setProjectId);
                }
            }
        });
        return iterableResource;
    }

    /**
     * 查询发票出票后，推送邮件、短信、站内信的发送状态
     */
    private Iterable<BillInvoiceResource> querySendStatus(Iterable<BillInvoiceResource> iterableResource) {
        Set<Long> billInvoiceIds = Sets.newLinkedHashSet();
        iterableResource.forEach(r -> billInvoiceIds.add(r.getId()));
        List<EFapiaoSendRecord> sendRecords = fapiaoSendRecordRepository.findAllByBillFapiaoIdIn(billInvoiceIds);
        Map<Long, List<EFapiaoSendRecord>> sendRecordIdMap = sendRecords.stream()
                .collect(Collectors.groupingBy(EFapiaoSendRecord::getBillFapiaoId));
        iterableResource.forEach(billInvoiceResource -> {
            List<EFapiaoSendRecord> subSendRecords = sendRecordIdMap.get(billInvoiceResource.getId());
            if (CollectionUtils.isEmpty(subSendRecords)) {
                return;
            }
            Map<NotifyType, List<EFapiaoSendRecord>> notifyTypeListMap = subSendRecords.stream()
                    .collect(Collectors.groupingBy(EFapiaoSendRecord::getNotifyType));
            for (Map.Entry<NotifyType, List<EFapiaoSendRecord>> entry : notifyTypeListMap.entrySet()) {
                constructSendStatus(billInvoiceResource, entry.getKey(), entry.getValue());
            }
        });
        return iterableResource;
    }

    private void constructSendStatus(BillInvoiceResource billInvoiceResource, NotifyType notifyType
            , List<EFapiaoSendRecord> eFapiaoSendRecords) {
        if (CollectionUtils.isEmpty(eFapiaoSendRecords)) {
            return;
        }
        List<SendStatusResourceSpec> specs = eFapiaoSendRecords.stream().map(r -> {
            SendStatusResourceSpec spec = new SendStatusResourceSpec();
            spec.setSendStatus(r.getSendStatus().getIndex());
            spec.setUserId(r.getUserId());
            spec.setUserName(r.getUserName());
            spec.setBatchNo(r.getBatchNo());
            spec.setRequestId(r.getRequestId());
            spec.setEmail(r.getEmail());
            spec.setSms(r.getSms());
            spec.setSendTime(Optional.ofNullable(r.getCreatedTime()).orElse(ZonedDateTime.now()));
            if (SendStatus.MSG_SUCCESS.equals(r.getSendStatus())) {
                spec.setDeliveryTime(r.getSendTime());
            }
            spec.setSendDesc(r.getSendDesc());
            return spec;
        }).sorted(Comparator.comparing(SendStatusResourceSpec::getSendTime).reversed()).collect(Collectors.toList());
        String batchNo = specs.get(0).getBatchNo();
        // 可以重复发送，所以取最新发送的状态
        SendStatusResource sendStatusResource;
        if (Objects.equals(notifyType, NotifyType.EMAIL)) {
            sendStatusResource = billInvoiceResource.getEmailSendStatus();
        } else if (Objects.equals(notifyType, NotifyType.SMS)) {
            sendStatusResource = billInvoiceResource.getSmsSendStatus();
        } else {
            sendStatusResource = billInvoiceResource.getMessageSendStatus();
        }
        sendStatusResource.setSendSpecs(specs);
        if (!NotifyType.MESSAGE.equals(notifyType)) {
            SendStatus validStatus = validateSendStatus(eFapiaoSendRecords.stream()
                    .filter(s -> Objects.equals(batchNo, s.getBatchNo())).collect(Collectors.toList()));
            if (!Objects.equals(validStatus.getIndex(), sendStatusResource.getStatus())) {
                sendStatusResource.setStatus(validStatus.getIndex());
                log.info("construct_update_bill_fapiao_status for {}", notifyType.name());
                doUpdateBillFapiaoStatus(billInvoiceResource.getId(), validStatus
                        , NotifyType.EMAIL.equals(notifyType) ? EFapiaoBillInvoice::getEmailSendStatus
                                : EFapiaoBillInvoice::getSmsSendStatus
                        , NotifyType.EMAIL.equals(notifyType) ? EFapiaoBillInvoice::setEmailSendStatus
                                : EFapiaoBillInvoice::setSmsSendStatus);
            }
        }
    }

    private SendStatus validateSendStatus(List<EFapiaoSendRecord> specs) {
        SendStatus sendStatus;
        if (specs.stream().allMatch(s -> SendStatus.MSG_SUCCESS.equals(s.getSendStatus()))) {
            sendStatus = SendStatus.MSG_SUCCESS;
        } else if (specs.stream().allMatch(s -> SendStatus.MSG_FAILED.equals(s.getSendStatus()))) {
            sendStatus = SendStatus.MSG_FAILED;
        } else if (specs.stream().anyMatch(s -> SendStatus.MSG_SUCCESS.equals(s.getSendStatus()))) {
            sendStatus = SendStatus.MSG_PARTIAL_SUCCESS;
        } else if (specs.stream().anyMatch(s -> SendStatus.MSG_SENDING.equals(s.getSendStatus()))) {
            sendStatus = SendStatus.MSG_SENDING;
        } else {
            sendStatus = SendStatus.MSG_NOT_SEND;
        }
        return sendStatus;
    }

}
