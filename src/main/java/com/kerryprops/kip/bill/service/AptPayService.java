package com.kerryprops.kip.bill.service;

import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPayConfig;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

public interface AptPayService {

    AptPay cancelAptPay(AptPaymentInfo paymentInfo, List<AptBill> aptBills);

    @Transactional
    void savePayInfo(AptPaymentInfo paymentInfo, AptPayConfig payConfig, String tranxId
            , double paidAmt, Date createDate, List<AptBill> aptBills);

    /**
     * 没有账单的缴费|收款记录
     */
    void savePayInfo(AptPaymentInfo paymentInfo, AptPayConfig payConfig, String tranxId
            , double paidAmt, Date createDate);

}
