package com.kerryprops.kip.bill.service.model.s;

import com.google.common.collect.ImmutableList;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillPushStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.common.jpa.QueryFilter;
import com.kerryprops.kip.bill.dao.entity.QAptBill;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.querydsl.core.types.Predicate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Optional;

import static com.kerryprops.kip.bill.common.utils.PayStatusUtils.getPayStatusPredicate;

/**
 * 账单管理-账单查询的 view
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class AptBillManageSearchReqBo implements QueryFilter {

    @Schema(title = "账单ID")
    private Long id;

    @Schema(title = "账单号")
    private String billNo;

    @Schema(title = "费项")
    private List<String> categorys;

    @Schema(title = "账单年开始")
    private Integer beginYear;

    @Schema(title = "账单年结束")
    private Integer endYear;

    @Schema(title = "账单月开始")
    private Integer beginMonth;

    @Schema(title = "账单月结束")
    private Integer endMonth;

    @Schema(title = "账单年")
    private Integer year;

    @Schema(title = "账单月")
    private Integer month;

    @Schema(title = "付款人")
    private String alph;

    @Schema(title = "系统状态")
    private BillStatus status;

    @Schema(title = "用户支付状态")
    private BillPaymentStatus paymentStatus;

    @Schema(title = "账单推送状态")
    private BillPushStatus pushStatus;

    @Schema(title = "楼盘CODE")
    private List<String> projectIds;

    @Schema(title = "楼栋ID")
    private List<String> buildingIds;

    @Schema(title = "楼层ID")
    private List<String> floorIds;

    @Schema(title = "房间ID")
    private List<String> roomIds;

    @Override
    public List<Optional<Predicate>> predicates() {
        Integer beginBillMonth = null;
        Integer endBillMonth = null;
        Optional<Predicate> paymentStatusOptional = Optional.empty();

        if (beginYear != null && beginMonth != null) {
            beginBillMonth = beginYear * 100 + beginMonth;
        }
        if (endYear != null && endMonth != null) {
            endBillMonth = endYear * 100 + endMonth;
        }

        if (null != paymentStatus) {
            paymentStatusOptional = getPayStatusPredicate(paymentStatus);
        }

        ImmutableList.Builder<Optional<Predicate>> builder = ImmutableList.builder();
        builder.add(Optional.of(0).map(QAptBill.aptBill.deletedAt::eq))
                .add(Optional.ofNullable(maxBindingScope()).map(QAptBill.aptBill.buildingId::in))
                .add(Optional.ofNullable(id).map(QAptBill.aptBill.id::eq))
                .add(Optional.ofNullable(billNo).map(QAptBill.aptBill.billNo::eq))
                .add(Optional.ofNullable(beginBillMonth).map(QAptBill.aptBill.billMonth::goe))
                .add(Optional.ofNullable(endBillMonth).map(QAptBill.aptBill.billMonth::loe))
                .add(Optional.ofNullable(status).map(QAptBill.aptBill.status::eq))
                .add(Optional.ofNullable(year).map(QAptBill.aptBill.year::eq))
                .add(Optional.ofNullable(month).map(QAptBill.aptBill.month::eq))
                .add(Optional.ofNullable(pushStatus).map(QAptBill.aptBill.pushStatus::eq))
                .add(Optional.ofNullable(projectIds).map(QAptBill.aptBill.projectId::in))
                .add(Optional.ofNullable(buildingIds).map(QAptBill.aptBill.buildingId::in))
                .add(Optional.ofNullable(floorIds).map(QAptBill.aptBill.floorId::in))
                .add(Optional.ofNullable(roomIds).map(QAptBill.aptBill.roomId::in))
                .add(Optional.ofNullable(alph).map(e -> ('%' + e + '%')).map(QAptBill.aptBill.alph::likeIgnoreCase))
                .add(Optional.ofNullable(categorys).map(QAptBill.aptBill.category::in))
                .add(paymentStatusOptional);
        return builder.build();
    }

    public List<String> maxBindingScope() {
        LoginUser loginUser = UserInfoUtils.getUser();
        if (loginUser == null) {
            throw new RuntimeException("not login.");
        }
        if (Boolean.TRUE.equals(loginUser.isSuperAdmin())) {
            return null;
        }
        return loginUser.toBuildingIdList();
    }

}
