package com.kerryprops.kip.bill.service.model.leg;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 账单配置项 2019-12-09新增对象 kerry_bill_config
 *
 * <AUTHOR>
 * @date 2020-10-15
 */
public class BillConfig extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 来自不同 服务的账单类型
     */
    private String sourceType;

    /**
     * 账单来源的中文描述
     */
    private String sourceName;

    /**
     * 去对应的平台获取账单的服务地址
     */
    private String httpService;

    /**
     * $column.columnComment
     */
    private String delFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSourceType() {
        return sourceType != null ? sourceType.toUpperCase() : null;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public String getHttpService() {
        return httpService;
    }

    public void setHttpService(String httpService) {
        this.httpService = httpService;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("sourceType", getSourceType())
                .append("sourceName", getSourceName())
                .append("httpService", getHttpService())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("delFlag", getDelFlag())
                .toString();
    }

}
