package com.kerryprops.kip.bill.service.model.s;

import com.kerryprops.kip.bill.common.enums.JobType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * 名   称：apt_sync_jde_job_log
 * 描   述：
 * 作   者：David <PERSON>
 * 时   间：2021/09/06 11:41:03
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AptSyncJdeJobLogBo {

    private Long id;

    private Long jobId;

    //0 已创建 3 处理中 4处理完成 5 处理失败
    private Integer status;

    private JobType type;

    private Integer localCnt;

    private String errorMsg;

    private Date startTime;

    private Date endTime;

    private String createBy;

    private Date createTime;

    private Date updateTime;


}
