package com.kerryprops.kip.bill.service.model.b;

import com.google.common.collect.ImmutableList;
import com.kerryprops.kip.bill.common.jpa.QueryFilter;
import com.kerryprops.kip.bill.dao.entity.QBillEntity;
import com.querydsl.core.types.Predicate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 针对用户进行查询账单信息 查询实体
 */
@Data
@Schema
public class BizBillSearchBo implements QueryFilter {

    private Long id;

    private Set<String> docos;

    private Set<String> an8s;

    private Set<String> alphs;

    private Integer tpFyrStart;

    private Integer tpPnStart;

    private Integer tpFyrEnd;

    private Integer tpPnEnd;

    /*阅读状态 0 未读、1已读	len: 10*/
    private Boolean isNotRead;

    @Override
    public List<Optional<Predicate>> predicates() {
        Integer beginBillMonth = null;
        Integer endBillMonth = null;
        if (tpFyrStart != null) {
            beginBillMonth = tpFyrStart * 100 + tpPnStart;
        }
        if (tpFyrEnd != null) {
            endBillMonth = tpFyrEnd * 100 + tpPnEnd;
        }

        ImmutableList.Builder builder = ImmutableList.<Optional<Predicate>>builder();
        builder.add(Optional.ofNullable("0").map(QBillEntity.billEntity.delFlag::eq))
                .add(Optional.ofNullable("Y").map(QBillEntity.billEntity.tpEv01::eq))
                .add(Optional.ofNullable(5).map(QBillEntity.billEntity.tpStatus::eq))
                .add(Optional.ofNullable(id).map(QBillEntity.billEntity.id::eq))
                .add(Optional.ofNullable(docos).map(QBillEntity.billEntity.tpDoco::in))
                .add(Optional.ofNullable(an8s).map(QBillEntity.billEntity.tpAn8::in))
                .add(Optional.ofNullable(alphs).map(QBillEntity.billEntity.tpAlph::in))
                .add(Optional.ofNullable(beginBillMonth).map(QBillEntity.billEntity.billMonth::goe))
                .add(Optional.ofNullable(endBillMonth).map(QBillEntity.billEntity.billMonth::loe))
                .add(Optional.ofNullable(isNotRead).map(e -> QBillEntity.billEntity.readStatus.eq(0)
                        .or(QBillEntity.billEntity.readStatus.isNull())))
        ;
        return builder.build();
    }

}
