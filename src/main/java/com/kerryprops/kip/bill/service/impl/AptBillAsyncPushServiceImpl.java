package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptBillOperator;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.AptBillAsyncPushService;
import com.kerryprops.kip.bill.service.AptBillPushService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.log4j.BSConversationFilter.setLoggingContext;

@Slf4j
@Service
@AllArgsConstructor
public class AptBillAsyncPushServiceImpl implements AptBillAsyncPushService {

    private AptBillPushService aptBillPushService;

    @Override
    @Async
    public void asyncPushAptBill(List<AptBill> aptBillList, AptBillOperator aptBillOperator
            , String conversationId, LoginUser userInfo) {
        setLoggingContext(conversationId, null);
        UserInfoUtils.setUser(userInfo);
        log.info("exec_push_apt_bill size: {}", aptBillList.size());
        Map<String, List<AptBill>> billMap = aptBillList.stream()
                .filter(e -> StringUtils.isNotEmpty(e.getRoomId()))
                .collect(Collectors.groupingBy(AptBill::getRoomId));
        Set<String> roomIds = billMap.keySet();

        aptBillPushService.pushAptBill(billMap, roomIds, aptBillOperator, aptBillList.get(0).getProjectId());
    }

}