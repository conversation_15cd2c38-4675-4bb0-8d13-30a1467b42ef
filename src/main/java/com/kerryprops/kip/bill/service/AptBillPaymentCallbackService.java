package com.kerryprops.kip.bill.service;

import com.kerryprops.kip.pmw.client.resource.AsynPaymentResultResource;
import com.kerryprops.kip.pmw.client.resource.AsyncPaymentFailedResource;

public interface AptBillPaymentCallbackService {

    void handlePaymentCallback(AsynPaymentResultResource.AsynPaymentResultBodyResource backResource);

    void handlePaymentFailedCallback(AsyncPaymentFailedResource paymentFailedResource);

}
