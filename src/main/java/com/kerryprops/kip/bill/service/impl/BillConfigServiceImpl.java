package com.kerryprops.kip.bill.service.impl;

import com.google.common.collect.Lists;
import com.kerryprops.kip.bill.common.current.JsonUtils;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.dao.BillConfigRepository;
import com.kerryprops.kip.bill.dao.entity.BillConfigEntity;
import com.kerryprops.kip.bill.dao.entity.QBillConfigEntity;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.IBillConfigService;
import com.kerryprops.kip.bill.service.model.leg.BillConfig;
import com.kerryprops.kip.bill.webservice.vo.req.BillConfigAddDto;
import com.kerryprops.kip.bill.webservice.vo.req.BillConfigListDto;
import com.kerryprops.kip.bill.webservice.vo.req.BillConfigUpdateDto;
import com.querydsl.core.BooleanBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 账单配置项 2019-12-09新增Service业务层处理
 *
 * <AUTHOR>
 * @date 2020-10-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BillConfigServiceImpl implements IBillConfigService {

    /**
     * 表示未删除状态的常量值。
     */
    private static final String NOT_DELETED_FLAG = "0";

    /**
     * 表示删除标记的常量。
     */
    private static final String DELETED_FLAG = "1";

    private final BillConfigRepository billConfigRepository;

    /**
     * 查询账单配置项 2019-12-09新增列表
     *
     * @param billConfig 账单配置项 2019-12-09新增
     * @return 账单配置项 2019-12-09新增
     */
    @Override
    public List<BillConfig> selectBillConfigList(BillConfig billConfig) {
        List<BillConfigEntity> billConfigEntities = billConfigRepository.selectBillConfigList(billConfig);
        return billConfigEntities.stream().map(e -> BeanUtil.copy(e, BillConfig.class)).collect(Collectors.toList());
    }

    /**
     * 过去当前的账单来源  map  ===》 key 来源类型； value 来源服务
     *
     * @return
     */
    @Override
    public Map<String, String> getBillConfigList() {
        Map<String, String> map = new HashMap<>();
        BillConfig billConfig = new BillConfig();
        List<BillConfig> configList = selectBillConfigList(billConfig);
        for (BillConfig config : configList) {
            map.put(config.getSourceType(), config.getHttpService());
        }
        return map;
    }

    @Override
    public BillConfigEntity getBillConfig(Long id) {
        return billConfigRepository.findById(id).orElseThrow();
    }

    @Override
    public List<BillConfigEntity> listBillConfigs(BillConfigListDto dto) {
        var billConfigQuery = QBillConfigEntity.billConfigEntity;

        String sourceType = dto.getSourceType();
        String sourceName = dto.getSourceName();
        String httpService = dto.getHttpService();

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(billConfigQuery.delFlag.eq(dto.getDelFlag()));
        if (StringUtils.isNotBlank(sourceType)) {
            builder.and(billConfigQuery.sourceType.eq(sourceType));
        }
        if (StringUtils.isNotBlank(sourceName)) {
            builder.and(billConfigQuery.sourceName.like(createWildcardPattern(sourceName)));
        }
        if (StringUtils.isNotBlank(httpService)) {
            builder.and(billConfigQuery.httpService.like(createWildcardPattern(httpService)));
        }
        var billConfigs = billConfigRepository.findAll(builder);
        return Lists.newArrayList(billConfigs);
    }

    @Override
    public BillConfigEntity addBillConfig(BillConfigAddDto dto) {
        String nickName = UserInfoUtils.getKerryAccount();
        BillConfigEntity billConfig = dto.toBillConfigEntity();
        billConfig.setCreateBy(nickName);
        billConfig.setUpdateBy(nickName);
        billConfig.setDelFlag(NOT_DELETED_FLAG);
        return billConfigRepository.save(billConfig);
    }

    @Override
    public BillConfigEntity updateBillConfig(Long id, BillConfigUpdateDto dto) {
        return billConfigRepository.saveDiff(id, dto);
    }

    @Override
    public BillConfigEntity deleteBillConfigById(Long id) {
        var billConfigOptional = billConfigRepository.findById(id);
        if (billConfigOptional.isEmpty()) {
            log.info("not found BillConfigEntity by id: {}", id);
            return null;
        }
        BillConfigEntity billConfig = billConfigOptional.orElseThrow();
        billConfig.setDelFlag(DELETED_FLAG);
        billConfigRepository.save(billConfig);
        log.info("delete BillConfigEntity : {}", JsonUtils.objToString(billConfig));
        return billConfig;
    }

    private String createWildcardPattern(String searchTerm) {
        return "%" + searchTerm + "%";
    }

}
