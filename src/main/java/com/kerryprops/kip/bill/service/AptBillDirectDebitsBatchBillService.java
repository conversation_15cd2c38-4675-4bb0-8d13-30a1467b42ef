package com.kerryprops.kip.bill.service;

import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptBillDirectDebitsBatch;
import com.kerryprops.kip.bill.dao.entity.AptDirectDebitsBatchBill;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

public interface AptBillDirectDebitsBatchBillService {

    void combineBillsByRoomAndCreatePaymentInfo(AptBillDirectDebitsBatch batch, List<AptDirectDebitsBatchBill> batchBills);

    List<AptBill> getAptBillsByPaymentOrderNo(String paymentOrderNo);

    Map<String, List<AptDirectDebitsBatchBill>> getRoomBillsOfBatch(AptBillDirectDebitsBatch batch);

    @Transactional
    void conductRoomDirectPaymentHandler(String paymentInfoId, List<AptDirectDebitsBatchBill> roomBatchBills);

    void resetAptBillToBePaidStatus(List<AptDirectDebitsBatchBill> batchBills);

}
