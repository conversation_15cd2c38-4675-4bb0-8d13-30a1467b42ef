package com.kerryprops.kip.bill.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.PaymentCateEnum;
import com.kerryprops.kip.bill.common.enums.PaymentPayType;
import com.kerryprops.kip.bill.common.exceptions.CounterCashierBizException;
import com.kerryprops.kip.bill.common.utils.BillingFun.ValidationSupplier;
import com.kerryprops.kip.bill.common.utils.IdWorker;
import com.kerryprops.kip.bill.common.utils.PrimaryKeyUtil;
import com.kerryprops.kip.bill.config.PaymentConfigProps;
import com.kerryprops.kip.bill.dao.AptPayConfigRepository;
import com.kerryprops.kip.bill.dao.AptPaymentInfoRepository;
import com.kerryprops.kip.bill.dao.entity.AptPayConfig;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.dao.entity.HiveContextAware;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierQRCodePaymentResource;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import com.kerryprops.kip.bill.webservice.vo.resp.RoomAn8RespVo;
import com.kerryprops.kip.pmw.client.resource.CombinedPaymentTxInputResource;
import com.kerryprops.kip.pmw.client.resource.CombinedPaymentTxInputResource.CombinedPaymentTxInputBodyResource;
import com.kerryprops.kip.pmw.client.resource.CombinedPaymentTxOutputResource;
import com.kerryprops.kip.pmw.client.resource.CombinedPaymentTxOutputResource.CombinedPaymentTxOutputBodyResource;
import com.kerryprops.kip.pmw.client.resource.DirectPayResource;
import com.kerryprops.kip.pmw.client.resource.HeaderResource;
import com.kerryprops.kip.pmw.client.resource.ProductItem;
import com.kerryprops.kip.pmw.client.service.PaymentClientService;
import com.kerryprops.kip.pmw.variables.PayOption;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Supplier;

import static com.kerryprops.kip.bill.common.utils.BillingFun.exceptionToNull;

/**
 * 扫描用户付款二维码支付的处理流程
 */
@Slf4j
@Service
public abstract class AbstractCounterCashierService {

    @Resource
    HiveAsClient hiveAsClient;

    @Resource
    ObjectMapper objectMapper;

    @Resource
    PaymentConfigProps paymentConfigProps;

    @Resource
    PaymentClientService paymentClientService;

    @Resource
    AptPayConfigRepository aptPayConfigRepository;

    @Resource
    AptPaymentInfoRepository aptPaymentInfoRepository;

    private CombinedPaymentTxOutputBodyResource doAuthCodePayment(HiveContextAware hiveContext
            , AptPaymentInfo aptPaymentInfo, String authCode, PayOption payOption) {
        CombinedPaymentTxInputBodyResource request =
                new CombinedPaymentTxInputBodyResource();

        request.setProjectId(hiveContext.getProjectId());
        request.setNotifyUrl(paymentConfigProps.getNotifyUrl());
        request.setCurrency("CNY");
        // AUTH_CODE的超时时间为 秒
        request.setPayTtl(String.valueOf(paymentConfigProps.getUserQRCodePaymentTtlSeconds() - 15));
        request.setOrderSource("KIP_BILLING");
        request.setCategory("Dev_Marketing");
        request.setProductType("BILLING");

        String buildingId = hiveContext.getBuildingId();
        String propertyManagementCo = hiveAsClient.getPropertyManagementCo(buildingId);
        request.setCompanyCode(propertyManagementCo);
        request.setOrderNo(aptPaymentInfo.getId());
        String amount = new DecimalFormat("0.00").format(aptPaymentInfo.getAmt());
        request.setOrderAmount(amount);
        request.setSupportInstallment("N");
        if (Objects.nonNull(hiveContext.getPositionItem())) {
            PositionItemResponse item = hiveContext.getPositionItem();
            ProductItem[] productItems = new ProductItem[1];
            request.setProducts(productItems);
            productItems[0] = new ProductItem();
            productItems[0].setDescription(item.getProjectName() + "-" + item.getBuildingName()
                    + "-" + item.getRoomName());
            productItems[0].setQty("1");
            productItems[0].setUnitPrice(amount);
        }
        // transaction params
        request.setRegion("cn");
        request.setPayChannel("AUTH_CODE");
        request.setAuthCode(authCode);
        request.setPayOption(payOption.name());

        HeaderResource.Builder builder = new HeaderResource.Builder();
        builder.setSub("kip");
        HeaderResource headerResource = builder.build();
        CombinedPaymentTxInputResource inputResource = new CombinedPaymentTxInputResource(headerResource
                , request, null);
        CombinedPaymentTxOutputResource paymentResponse = paymentClientService
                .createCombinedPaymentTransaction("zh-cn", null, null, null, inputResource);
        log.info("apt_bill_auth_code_com_pay response: {}"
                , exceptionToNull(() -> objectMapper.writeValueAsString(paymentResponse.getBody())));
        return paymentResponse.getBody();
    }

    private AptPaymentInfo createPaymentInfo(BigDecimal decimalSumAmt, HiveContextAware hiveContext
            , String paymentDesc, PaymentPayType payType, RoomAn8RespVo payerInfo) {

        double sumAmt = decimalSumAmt.doubleValue();
        return AptPaymentInfo.builder()
                .id(PrimaryKeyUtil.createPaymentId())
                .payAct(IdWorker.getFlowIdWorkerInstance().nextStrId())
                .amt(sumAmt)
                .paymentStatus(BillPaymentStatus.PAYING)
                .projectId(hiveContext.getProjectId())
                .buildingId(hiveContext.getBuildingId())
                .floorId(hiveContext.getFloorId())
                .roomId(hiveContext.getRoomId())
                .positionItem(hiveContext.getPositionItem())
                .deleted(0)
                .paySession(null)
                .pspTransNo(Strings.EMPTY)
                .paymentTransNo(Strings.EMPTY)
                .failedReason(Strings.EMPTY)
                .userProfileId(Strings.EMPTY)
                .agreementNo(Strings.EMPTY)
                .xmlUrl(Strings.EMPTY)
                .ofdUrl(Strings.EMPTY)
                .createTime(new Date())
                .updateTime(new Date())
                .description(Optional.ofNullable(paymentDesc).orElse(Strings.EMPTY))
                .advanceAmount(BigDecimal.ZERO)
                .paymentCate(PaymentCateEnum.UNKNOWN)
                .payType(payType)
                .payTypeInfo(payType.getInfo())
                .createBy(UserInfoUtils.getKerryAccount())
                .payChannel(BillPayChannel.OFFLINE)
                .bu(Optional.ofNullable(payerInfo).map(RoomAn8RespVo::getBu).orElse(Strings.EMPTY))
                .unit(Optional.ofNullable(payerInfo).map(RoomAn8RespVo::getUnit).orElse(Strings.EMPTY))
                .an8(Optional.ofNullable(payerInfo).map(RoomAn8RespVo::getAn8).orElse(Strings.EMPTY))
                .alph(Optional.ofNullable(payerInfo).map(RoomAn8RespVo::getAlph).orElse(Strings.EMPTY))
                .doco(Optional.ofNullable(payerInfo).map(RoomAn8RespVo::getDoco).orElse(Strings.EMPTY))
                .build();
    }

    abstract class AbstractCounterCashierPaymentHandler {

        final PayOption payOption;

        final String authCode;

        final String paymentDesc;

        final PaymentPayType billPayType;

        final CashierQRCodePaymentResource response = new CashierQRCodePaymentResource();

        final RoomAn8RespVo payerInfo;

        private final Date paymentTime;

        AptPaymentInfo aptPaymentInfo;

        AbstractCounterCashierPaymentHandler(String payOption, String authCode
                , String paymentDesc, RoomAn8RespVo payerInfo, Date paymentTime) {
            this.payOption = PayOption.valueOf(payOption);
            this.billPayType = PayOption.ALIPAY.equals(this.payOption) ? PaymentPayType.ALIPAY : PaymentPayType.WECHAT;
            this.authCode = authCode;
            this.paymentDesc = paymentDesc;
            this.payerInfo = payerInfo;
            this.paymentTime = paymentTime;
        }

        abstract BigDecimal getPaymentAmount();

        abstract Supplier<HiveContextAware> fetchHiveContext();

        abstract Consumer<AptPaymentInfo> beforePaymentInfoSaved();

        ValidationSupplier cashierPaymentValidation() {
            return this::validatePayConfig;
        }

        PaymentPayType getPayType() {
            return this.payOption.equals(PayOption.ALIPAY) ? PaymentPayType.ALIPAY
                    : PaymentPayType.WECHAT;
        }

        CashierQRCodePaymentResource handle() {
            // do validation & setting
            cashierPaymentValidation().get();
            HiveContextAware hiveContext = fetchHiveContext().get();
            // biz_flow template methods below
            BigDecimal decimalSumAmt = getPaymentAmount();
            // create payment info
            aptPaymentInfo = createPaymentInfo(decimalSumAmt, hiveContext, this.paymentDesc
                    , getPayType(), this.payerInfo);
            // do auth code payment
            CombinedPaymentTxOutputResource.CombinedPaymentTxOutputBodyResource paymentResult
                    = doAuthCodePayment(hiveContext, aptPaymentInfo, this.authCode, this.payOption);
            DirectPayResource directPayResource = paymentResult.getDirectPayResource();
            // handle payment result
            aptPaymentInfo.setPspTransNo(directPayResource.getPspTransId());
            aptPaymentInfo.setPaymentTransNo(directPayResource.getConfirmedPspTradeNo());
            aptPaymentInfo.setPaySession(paymentResult.getSessionDetail().getSessionInfo().getSessionId());
            // before payment info save
            beforePaymentInfoSaved().accept(aptPaymentInfo);
            if (directPayResource.getState().equals("SUCCESS") || directPayResource.getState().equals("PROCESSING")) {
                Optional.ofNullable(paymentTime).ifPresent(aptPaymentInfo::setPaymentTime);
                handlePaySuccess(directPayResource.getState(), directPayResource.getError());
            } else {
                handlePayFailed(directPayResource.getState(), directPayResource.getError());
            }
            // construct response
            response.setPaymentAmount(decimalSumAmt);
            response.setOrderNo(aptPaymentInfo.getId());
            response.setPayOption(this.payOption);
            response.setPayAct(aptPaymentInfo.getPayAct());
            return response;
        }

        void handlePaySuccess(String payState, String payDesc) {
            // save payment info
            aptPaymentInfoRepository.save(aptPaymentInfo);
            log.info("apt_bill_auth_code_com_pay call_payment_api_OK paymentInfo: {}"
                    , exceptionToNull(() -> objectMapper.writeValueAsString(aptPaymentInfo)));
            if (payState.equals("SUCCESS")) {
                response.setPaymentStatus(BillPaymentStatus.PAID);
                response.setPaymentDesc(payDesc);
            } else {
                response.setPaymentStatus(BillPaymentStatus.PAYING);
                response.setPaymentDesc(payDesc);
            }
            response.setPaymentTime(ZonedDateTime.now());
        }

        void handlePayFailed(String payState, String payDesc) {
            aptPaymentInfo.setFailedReason(StringUtils.abbreviate(payDesc, 120));
            log.info("apt_bill_auth_code_com_pay paymentInfo:'{}'，call_payment_api_response_failed: {}"
                    , aptPaymentInfo.getId(), payDesc);
            aptPaymentInfo.setPaymentStatus(BillPaymentStatus.CANCEL);
            aptPaymentInfoRepository.save(aptPaymentInfo);
            response.setPaymentStatus(BillPaymentStatus.CANCEL);
            response.setPaymentDesc(payDesc);
        }

        /**
         * 参考 {@link PaymentBillServiceImpl#billPayment(List, boolean)}
         * 收款方式需要在S端配置
         */
        private boolean validatePayConfig() {
            AptPayConfig aptPayConfig = aptPayConfigRepository.findTopByProjectIdAndPaymentType(
                    fetchHiveContext().get().getProjectId(), billPayType.getInfo());
            if (Objects.isNull(aptPayConfig)) {
                throw new CounterCashierBizException("700003", "当前楼盘未配置当前收款方式");
            }
            return true;
        }

    }

}