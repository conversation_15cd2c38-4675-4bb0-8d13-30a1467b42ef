package com.kerryprops.kip.bill.service;

import com.kerryprops.kip.bill.dao.entity.EFapiaoJDEBill;
import com.kerryprops.kip.bill.webservice.vo.req.CallBackKerryInvoiceMainVo;

/**
 * e-fapiao：本地侧数据Service接口
 *
 * <AUTHOR>
 * @Date 2023-3-28
 */
public interface EFapiaoBillService {

    /**
     * 发票信息回写
     */
    void invoiceWriteBack(CallBackKerryInvoiceMainVo mainVo);

    /**
     * 发票信息回写: 导入发票场景专用
     */
    void excelImportInvoiceWriteBack(CallBackKerryInvoiceMainVo mainVo);

    /**
     * B端-提交开票申请
     */
    void invoiceUpload(EFapiaoJDEBill eFapiaoJdeBill);

}
