package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.common.enums.InvoiceCustomerType;
import com.kerryprops.kip.bill.common.enums.InvoiceRecordStatus;
import com.kerryprops.kip.bill.common.enums.InvoiceTypeEnum;
import com.kerryprops.kip.bill.common.enums.InvoicedStatus;
import com.kerryprops.kip.bill.common.enums.OrderType;
import com.kerryprops.kip.bill.common.utils.NonNullUtils;
import com.kerryprops.kip.bill.dao.AptPayRepository;
import com.kerryprops.kip.bill.dao.AptPaymentInfoRepository;
import com.kerryprops.kip.bill.dao.InvoiceRecordRepository;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.dao.entity.InvoiceRecord;
import com.kerryprops.kip.bill.feign.entity.UploadInvoiceMainV2Vo;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.webservice.vo.req.CallBackKerryInvoiceMainVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;
import java.util.StringJoiner;

import static com.kerryprops.kip.bill.log4j.BSConversationFilter.getConversationId;

@Service
@Slf4j
public class InvoiceApplicationService {

    private static final String EMPTY_STRING = "";

    @Autowired
    private InvoiceRecordRepository invoiceRecordRepository;

    @Autowired
    private AptInvoiceSmsService aptInvoiceSmsService;

    @Autowired
    private AptPaymentInfoRepository aptPaymentInfoRepository;

    @Autowired
    private AptPayRepository aptPayRepository;

    @Transactional
    public void updateInvoiceInvoked(InvoiceRecord invoiceRecord) {
        invoiceRecord.setIsInvoiceInvoked(Boolean.TRUE);
        invoiceRecordRepository.save(invoiceRecord);
    }

    public InvoiceRecord getInvoiceRecordByOrderNo(String orderNo) {
        try {
            return invoiceRecordRepository.getByOrderNo(orderNo);
        } catch (Exception e) {
            log.error("found invoice order fail, orderNo={}, exception={}", orderNo, e.getMessage());
            return null;
        }
    }

    @Transactional
    public InvoiceRecord saveInvoiceRecord(UploadInvoiceMainV2Vo mainVo, String sellerName, String areaCode) {
        InvoiceRecord record = new InvoiceRecord();
        record.setCustomerName(mainVo.getPurchaserName());
        record.setSellerName(sellerName);
        record.setTaxNo(mainVo.getPurchaserTaxNo());
        record.setAddress(mainVo.getPurchaserAddress());
        record.setBankName(mainVo.getPurchaserBankName());
        record.setBankAccount(mainVo.getPurchaserBankAccount());
        record.setAreaCode(areaCode);
        record.setPhone(mainVo.getPurchaserTel());
        if (StringUtils.isNotEmpty(mainVo.getReceiveUserEmail())) {
            record.setEmail(mainVo.getReceiveUserEmail());
        } else {
            record.setPhone(mainVo.getReceiveUserTel());
        }
        record.setOrderNo(mainVo.getSalesbillNo());
        record.setOrderType(OrderType.REPAIR_ORDER);
        Optional.ofNullable(UserInfoUtils.getUser()).ifPresent(it -> record.setUserId(it.getUserId() + EMPTY_STRING));
        record.setApplyTime(LocalDateTime.now());
        record.setStatus(InvoiceRecordStatus.PROCESSING);
        record.setAmount(mainVo.getAmountWithTax());
        // 发票类型
        InvoiceTypeEnum typeEnum = InvoiceTypeEnum.getByKipCode(mainVo.getInvoiceType());
        Optional.ofNullable(typeEnum).ifPresent(it -> record.setInvoiceType(it.getDesc()));
        record.setTotalAmount(mainVo.getAmountWithTax());
        record.setTaxAmount(mainVo.getAmountWithTax());
        record.setCustomerType(Objects.equals(mainVo.getExt2(), "1") ? InvoiceCustomerType.INDIVIDUAL : InvoiceCustomerType.COMPANY);
        // 暂存楼盘名称
        record.setIssuer(mainVo.getProjectName());
        record.setXmlUrl(NonNullUtils.nonNullStringFunction.apply(record.getXmlUrl()));
        record.setOfdUrl(NonNullUtils.nonNullStringFunction.apply(record.getOfdUrl()));
        record.setErrorMessage(StringUtils.EMPTY);
        invoiceRecordRepository.save(record);

        return record;
    }

    /**
     * 更新InvoiceRecord开票记录表信息
     *
     * @return null: 异常场景未搜索到相关数据，非null：InvoiceRecord开票记录相关条目
     */
    @Transactional
    public void updateInvoiceRecord(AptPaymentInfo aptPaymentInfo, CallBackKerryInvoiceMainVo mainVo) {
        InvoiceRecord record = invoiceRecordRepository.getByOrderNo(mainVo.getSalesbillNo());
        if (Objects.isNull(record)) {
            log.warn("[Invoice][RecordNotFound] - No invoice record found for salesBillNo={}", mainVo.getSalesbillNo());
            return;
        }

        if (mainVo.getInvoiceStatus() == 0) {
            //处理开票失败通知
            record.setStatus(InvoiceRecordStatus.FAILED);
            record.setErrorMessage(mainVo.getMessage());
            invoiceRecordRepository.save(record);
            log.warn("[Invoice][IssueFailed] - Invoice issuance failed for salesBillNo={}, error={}", mainVo.getSalesbillNo(), mainVo.getMessage());
        } else if (mainVo.getInvoiceStatus() == 1) {
            //处理开票成功通知
            String invokedInvoiceCode = record.getInvoiceCode();
            String invokedInvoiceNo = record.getInvoiceNo();
            record.setInvoiceCode(this.getData(record.getInvoiceCode(), mainVo.getInvoiceCode()));
            record.setPdfUrl(this.getData(record.getPdfUrl(), mainVo.getPdfUrl()));
            record.setXmlUrl(this.getData(record.getXmlUrl(), mainVo.getXmlUrl()));
            record.setOfdUrl(this.getData(record.getOfdUrl(), mainVo.getOfdUrl()));
            record.setInvoiceTime(LocalDateTime.now());
            record.setStatus(InvoiceRecordStatus.COMPLETED);
            if (StringUtils.isNotBlank(mainVo.getSellerName())) {
                record.setSellerName(mainVo.getSellerName());
            }
            // 发票号码
            record.setInvoiceNo(this.getData(record.getInvoiceNo(), mainVo.getInvoiceNo()));

            // 发票含税金额
            String mainVoAmountWithTax = Optional.ofNullable(mainVo.getAmountWithTax())
                    .map(String::valueOf).orElse(EMPTY_STRING);
            record.setTotalAmountAfterSplit(this.getData(record.getTotalAmountAfterSplit(), mainVoAmountWithTax));

            // 发票服务是否已经调用
            record.setIsInvoiceInvoked(true);
            invoiceRecordRepository.save(record);
            // 异步发送短信通知
            aptInvoiceSmsService.sendSms(aptPaymentInfo, record, mainVo, invokedInvoiceCode, invokedInvoiceNo, getConversationId());
        }
    }

    /**
     * 更新已开票状态
     *
     * @param paymentId
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateInvoicedStatus(String paymentId, InvoicedStatus invoicedStatus) {
        aptPaymentInfoRepository.updateInvoicedStatus(paymentId, invoicedStatus);
        aptPayRepository.updateInvoicedStatus(paymentId, invoicedStatus);
    }

    /**
     * 存在拆票的可能
     *
     * @param data
     * @param append
     * @return
     */
    private String getData(String data, String append) {
        StringJoiner joiner = new StringJoiner(",");
        if (StringUtils.isNotBlank(data)) {
            joiner.add(data);
        }
        joiner.add(append);
        return joiner.toString();
    }

}