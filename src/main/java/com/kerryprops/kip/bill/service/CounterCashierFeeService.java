package com.kerryprops.kip.bill.service;

import com.kerryprops.kip.bill.webservice.vo.req.CashierFeePayRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierFeePaysRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierFeePaysVerifyRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierFeePrepayRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierFeeQRCodePayRequest;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierFeePayDetailResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierFeePayResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierFeePaysResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierQRCodePaymentResource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * <AUTHOR> 2023-12-15 16:19:48
 **/
public interface CounterCashierFeeService {

    CashierFeePayResource feeOfflinePay(CashierFeePayRequest request);

    Page<CashierFeePaysResource> queryFeePays(CashierFeePaysRequest req, Pageable pageable);

    void exportFeePays(CashierFeePaysRequest req, HttpServletResponse response);

    CashierFeePayDetailResource queryFeePayDetail(String paymentInfoId);

    CashierFeePayResource prepay(CashierFeePrepayRequest request);

    CashierFeePayDetailResource switchFeePayVerifyStatus(Long id);

    List<CashierFeePayDetailResource> conditionalVerifyFeePay(CashierFeePaysVerifyRequest req);

    CashierFeePayDetailResource cancelFeePay(Long id);

    Integer writeBackFee2JDE(String projectId);

    CashierQRCodePaymentResource counterCashierQRCodePayment(CashierFeeQRCodePayRequest request);

}
