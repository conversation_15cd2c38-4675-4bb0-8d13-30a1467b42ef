package com.kerryprops.kip.bill.service.impl;

import com.alibaba.fastjson.JSON;
import com.kerryprops.kip.bill.common.enums.BillSelectMode;
import com.kerryprops.kip.bill.dao.BillSelectConfigRepository;
import com.kerryprops.kip.bill.dao.entity.BillSelectConfig;
import com.kerryprops.kip.bill.dao.entity.QBillSelectConfig;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.webservice.vo.req.BillSelectConfigAddDto;
import com.kerryprops.kip.bill.webservice.vo.req.BillSelectConfigExistDto;
import com.kerryprops.kip.bill.webservice.vo.req.BillSelectConfigListDto;
import com.kerryprops.kip.bill.webservice.vo.req.BillSelectConfigPutDto;
import com.querydsl.core.BooleanBuilder;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.NoSuchElementException;

/**
 * BillSelectConfigService.
 *
 * <AUTHOR> Yu 2025-02-27 15:59:09
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class BillSelectConfigService {

    private final BillSelectConfigRepository billSelectConfigRepository;

    /**
     * 根据项目ID、楼栋ID和房间ID获取账单选择模式.
     *
     * @param projectId  项目ID
     * @param buildingId 楼栋ID
     * @param roomId     房间ID
     * @return 对应的账单选择模式，如果不存在则返回 ANY_BILLS
     */
    public BillSelectMode getBillSelectMode(String projectId, String buildingId, String roomId) {
        return billSelectConfigRepository.findBillSelectMode(projectId, buildingId, roomId);
    }

    /**
     * 检查账单选择配置是否存在.
     *
     * @param dto 包含项目ID、楼栋ID和房间ID的账单选择配置查询条件对象
     * @return 如果指定的账单选择配置存在，则返回true；否则返回false
     */
    public boolean existConfig(BillSelectConfigExistDto dto) {
        return billSelectConfigRepository.existsConfig(dto.getProjectId(), dto.getBuildingId(), dto.getRoomId());
    }

    /**
     * 添加账单选择配置.
     *
     * @param dto 包含需要添加的账单选择配置信息的数据传输对象
     * @return 保存后的账单选择配置对象
     */
    @Transactional
    public BillSelectConfig addConfig(BillSelectConfigAddDto dto) {
        BillSelectConfig config = dto.toBillSelectConfig();
        config.setOperatorName(UserInfoUtils.getKerryAccount());
        return billSelectConfigRepository.save(config);
    }

    /**
     * 更新账单选择配置.
     *
     * @param id     配置的唯一标识ID
     * @param config 包含待更新的账单选择配置信息的数据传输对象
     * @return 更新后的账单选择配置对象
     */
    @Transactional
    public BillSelectConfig updateConfig(Long id, BillSelectConfigPutDto config) {
        return billSelectConfigRepository.saveDiff(id, config);
    }

    /**
     * 删除账单选择配置.
     *
     * @param id 待删除的账单选择配置的唯一标识ID
     * @return 被删除的账单选择配置对象
     */
    @Transactional
    public BillSelectConfig deleteConfig(Long id) {
        BillSelectConfig billSelectConfig = billSelectConfigRepository.findById(id).orElseThrow();
        log.info("delete billSelectConfig : {}", JSON.toJSONString(billSelectConfig));
        billSelectConfigRepository.deleteById(id);
        return billSelectConfig;
    }

    /**
     * 获取账单选择配置的分页列表.
     *
     * @param dto      包含筛选条件的账单选择配置列表数据传输对象
     * @param pageable 分页信息对象，用于指定分页数据和排序规则
     * @return 包含账单选择配置的分页对象，包含筛选条件下的账单选择配置列表
     */
    public Page<BillSelectConfig> listConfig(BillSelectConfigListDto dto, Pageable pageable) {
        BillSelectMode billSelectMode = dto.getBillSelectMode();
        String buildingId = dto.getBuildingId();
        String roomId = dto.getRoomId();

        var billSelectConfigQuery = QBillSelectConfig.billSelectConfig;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(billSelectConfigQuery.projectId.eq(dto.getProjectId()));
        if (billSelectMode != null) {
            builder.and(billSelectConfigQuery.billSelectMode.eq(billSelectMode));
        }
        if (buildingId != null) {
            builder.and(billSelectConfigQuery.buildingId.eq(buildingId));
        }
        if (roomId != null) {
            builder.and(billSelectConfigQuery.roomId.eq(roomId));
        }
        return billSelectConfigRepository.findAll(builder, pageable);
    }

    /**
     * 根据给定的ID获取账单选择配置.
     *
     * @param id 要获取配置的唯一标识符
     * @return 与指定ID关联的BillSelectConfig对象
     * @throws NoSuchElementException 如果未找到具有给定ID的配置
     */
    public BillSelectConfig getConfig(Long id) {
        return billSelectConfigRepository.findById(id).orElseThrow();
    }

}
