package com.kerryprops.kip.bill.service.model;

import com.kerryprops.kip.bill.common.utils.CommonUtil;
import com.kerryprops.kip.bill.dao.entity.AptJdeBill;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

@Data
@ToString
@Slf4j
public class JdeConfirmResult {

    private AptPaymentTimestamp timestamp;

    private List<AptJdeBill> aptJdeBills;

    public boolean isValidResponse() {
        return timestamp != null && aptJdeBills != null;
    }

    public JdeConfirmResult merge(final JdeConfirmResult that) {
        if (that == null) {
            log.info("input null JdeConfirmResult");
            return this;
        }
        int cmp = CommonUtil.comparePossiblyNull(this.timestamp, that.timestamp);
        AptPaymentTimestamp newTimestamp = cmp >= 0 ? this.timestamp : that.timestamp;

        List<AptJdeBill> newList = new LinkedList<>();
        Optional.ofNullable(this.aptJdeBills).orElse(new LinkedList<>()).forEach(e -> newList.add(e));
        Optional.ofNullable(that.aptJdeBills).orElse(new LinkedList<>()).forEach(e -> newList.add(e));

        JdeConfirmResult jdeConfirmResult = new JdeConfirmResult();
        jdeConfirmResult.aptJdeBills = newList;
        jdeConfirmResult.timestamp = newTimestamp;
        return jdeConfirmResult;
    }

}
