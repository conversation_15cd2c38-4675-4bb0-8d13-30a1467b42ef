package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.dao.AptJdeBillRepository;
import com.kerryprops.kip.bill.dao.JDEBillRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptJdeBill;
import com.kerryprops.kip.bill.dao.entity.JDEAptBill;
import com.kerryprops.kip.bill.service.AptSyncPayStatusService;
import com.kerryprops.kip.bill.service.StaffManualSyncAptBillService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

@Slf4j
@Service
@AllArgsConstructor
public class StaffManualSyncAptBillServiceImpl implements StaffManualSyncAptBillService {

    private final AptBillService aptBillService;

    private final JDEBillRepository jdeBillRepository;

    private final AptJdeBillRepository aptJdeBillRepository;

    private final AptSyncPayStatusService aptSyncPayStatusService;

    @Override
    @Transactional
    public AptBill manualSync(AptBill aptBill, List<AptJdeBill> aptJdeBills) {
        this.syncJDEBillInDB(aptJdeBills);
        if (compareAndUpdateAptBill(aptBill, aptJdeBills)) {
            aptBillService.saveOrUpdateBill(aptBill);
        }
        return aptBill;
    }

    /**
     * query JDE and update apt_sync_bills records
     * TODO k+ bill <-> jde bill, 1 -> n?, 1 -> 1?, n -> n?
     */
    private void syncJDEBillInDB(List<AptJdeBill> aptJdeBills) {
        for (AptJdeBill jdeBill : aptJdeBills) {
            long docNo = jdeBill.getRdDoc();
            String companyCode = jdeBill.getRdKco();
            String type = jdeBill.getRdDct();
            String paymentSfx = jdeBill.getRdSfx();
            log.info("query payment status. {}-{}-{}-{}", docNo, companyCode, type, paymentSfx);
            List<JDEAptBill> jdePayStatusList = jdeBillRepository.findAllByCombineKeys(docNo, companyCode
                    , type, paymentSfx);
            if (CollectionUtils.isEmpty(jdePayStatusList)) {
                log.info("no validate payment record found.{}-{}-{}-{}", docNo, companyCode, type, paymentSfx);
                continue;
            }
            JDEAptBill jdePayStatus = jdePayStatusList.get(0);
            boolean updated = aptSyncPayStatusService.updateJdeBillPaymentStatus(jdeBill, jdePayStatus);
            if (updated) {
                aptJdeBillRepository.save(jdeBill);
            }
        }
    }

    private boolean compareAndUpdateAptBill(AptBill aptBill, List<AptJdeBill> aptJdeBills) {
        long jdeVerifiedCnt = aptJdeBills.stream().filter(e -> e.getJdeVerification() == 1).count();
        BigDecimal jdeTotalAmt = aptJdeBills.stream().map(AptJdeBill::getRdAg).map(BigDecimal::valueOf)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO)
                .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
        int totalCnt = aptJdeBills.size();
        boolean updated = false;
        if (jdeVerifiedCnt == totalCnt) {
            if (!aptBill.getStatus().equals(BillStatus.JDE_VERIFIED)) {
                updated = true;
                aptBill.setStatus(BillStatus.JDE_VERIFIED);
                aptBill.setPaymentStatus(BillPaymentStatus.PAID);
                aptBill.setPaymentResult("JDE核销");
            }
        } else if (jdeVerifiedCnt != 0) {
            updated = true;
            aptBill.setStatus(BillStatus.TO_BE_PAID);
            aptBill.setPaymentStatus(BillPaymentStatus.PART_PAID);
            aptBill.setPaymentResult("JDE部分核销，请线下与物业确认并解决");
        } else {
            if (!aptBill.getStatus().equals(BillStatus.TO_BE_PAID)) {
                updated = true;
                aptBill.setStatus(BillStatus.TO_BE_PAID);
                aptBill.setPaymentStatus(BillPaymentStatus.TO_BE_PAID);
                aptBill.setPaymentResult(StringUtils.EMPTY);
            }
        }
        if (jdeTotalAmt.compareTo(BigDecimal.valueOf(aptBill.getAmt())) != 0) {
            updated = true;
            aptBill.setAmt(jdeTotalAmt.doubleValue());
        }
        return updated;
    }

}
