package com.kerryprops.kip.bill.service.model.s;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * 名   称：apt_sync_jde_log
 * 描   述：
 * 作   者：David <PERSON>
 * 时   间：2021/09/06 10:39:31
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AptSyncPayToJdeBo {

    private Long id;

    private Long jobId;

    private Long payId;

    private Integer status;

    private String errorMsg;

    private String rccknu;

    private Integer rclnid;

    private String rctyin;

    private String rckco;

    private String rcdct;

    private Long rcdoc;

    private String rcsfx;

    private Long rcan8;

    private String rcalph;

    private Integer rcdmtj;

    private Double rcag;

    private String rcglba;

    private String rccrcd;

    private String rcglc;

    private String rcpo;

    private String rcmcu;

    private String rcunit;

    private String rcedsp;

    private String rcrmk;

    private Double rcaaaj;

    private Date createTime;

    private Date updateTime;


}
