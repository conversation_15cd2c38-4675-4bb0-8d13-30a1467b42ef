package com.kerryprops.kip.bill.service.model.s;

import com.google.common.collect.ImmutableList;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.enums.RefLogType;
import com.kerryprops.kip.bill.common.jpa.QueryFilter;
import com.kerryprops.kip.bill.dao.entity.AptBillOperationStatus;
import com.kerryprops.kip.bill.dao.entity.AptBillOperator;
import com.kerryprops.kip.bill.dao.entity.QAptBillOperation;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.querydsl.core.types.Predicate;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.kerryprops.kip.bill.common.constants.AppConstants.DEFAULT_ZONE_ID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AptBillOperationBo implements QueryFilter {

    private Date searchDateFrom;

    private Date searchDateTo;

    private String projectId;

    private String buildingId;

    private String roomId;

    private String billNo;

    // 日志类型，1-同步，2-推送
    private RefLogType logType;

    // 操作状态
    private AptBillOperationStatus operationStatus;

    @Override
    public List<Optional<Predicate>> predicates() {
        List<AptBillOperator> operators = null;
        if (Objects.isNull(logType) || RefLogType.SYNC_LOG.equals(logType)) {
            operators = Arrays.asList(AptBillOperator.AUTO_SYNC, AptBillOperator.MANUAL_SYNC);
        } else if (RefLogType.PUSH_LOG.equals(logType)) {
            operators = Arrays.asList(AptBillOperator.PUSH_ALL, AptBillOperator.PUSH_CONDITIONAL
                    , AptBillOperator.PUSH_SELECTED);
        }

        ImmutableList.Builder<Optional<Predicate>> builder = ImmutableList.builder();
        builder.add(Optional.ofNullable(maxBindingScope()).map(QAptBillOperation.aptBillOperation.buildingId::in))
                .add(Optional.ofNullable(operators).map(QAptBillOperation.aptBillOperation.operator::in))
                .add(Optional.ofNullable(billNo).map(QAptBillOperation.aptBillOperation.billNo::eq))
                .add(Optional.ofNullable(roomId).map(QAptBillOperation.aptBillOperation.roomId::eq))
                .add(Optional.ofNullable(projectId).map(QAptBillOperation.aptBillOperation.projectId::eq))
                .add(Optional.ofNullable(buildingId).map(QAptBillOperation.aptBillOperation.buildingId::eq))
                .add(Optional.ofNullable(operationStatus).map(QAptBillOperation.aptBillOperation.operationStatus::eq))
                .add(Optional.ofNullable(searchDateFrom)
                        .map(d -> ZonedDateTime.ofInstant(d.toInstant(), DEFAULT_ZONE_ID))
                        .map(QAptBillOperation.aptBillOperation.createdTime::goe))
                .add(Optional.ofNullable(searchDateTo)
                        .map(d -> ZonedDateTime.ofInstant(d.toInstant(), DEFAULT_ZONE_ID))
                        .map(QAptBillOperation.aptBillOperation.createdTime::loe))
        ;
        if (RefLogType.SYNC_LOG.equals(logType)) {
            builder.add(Optional.of(0).map(QAptBillOperation.aptBillOperation.diffCount::gt));
        }
        return builder.build();
    }

    public List<String> maxBindingScope() {
        LoginUser loginUser = UserInfoUtils.getUser();
        if (loginUser == null) {
            throw new RuntimeException("not login.");
        }
        if (Boolean.TRUE.equals(loginUser.isSuperAdmin())) {
            return null;
        }
        return loginUser.toBuildingIdList();
    }

}
