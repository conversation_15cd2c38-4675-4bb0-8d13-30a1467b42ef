package com.kerryprops.kip.bill.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.kerryprops.kip.bill.common.enums.BillPayModule;
import com.kerryprops.kip.bill.common.enums.BillPayStateEnum;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.common.enums.PayCancelTypeEnum;
import com.kerryprops.kip.bill.common.enums.PaymentPayType;
import com.kerryprops.kip.bill.common.exceptions.AppException;
import com.kerryprops.kip.bill.config.DataMigrationConfig;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptJdeBillRepository;
import com.kerryprops.kip.bill.dao.AptPayConfigRepository;
import com.kerryprops.kip.bill.dao.AptPaymentBillRepository;
import com.kerryprops.kip.bill.dao.AptPaymentInfoRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptJdeBill;
import com.kerryprops.kip.bill.dao.entity.AptPayConfig;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.dao.entity.QAptJdeBill;
import com.kerryprops.kip.bill.dao.entity.QAptPayConfig;
import com.kerryprops.kip.bill.feign.entity.PaymentConfirmedCommand;
import com.kerryprops.kip.bill.service.AptBillDirectDebitsBatchBillService;
import com.kerryprops.kip.bill.service.AptBillPaymentCallbackService;
import com.kerryprops.kip.bill.service.AptBillWxTemplateMsgService;
import com.kerryprops.kip.bill.service.AptPayService;
import com.kerryprops.kip.bill.utils.BillUtil;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import com.kerryprops.kip.pmw.client.resource.AsynPaymentResultResource.AsynPaymentResultBodyResource;
import com.kerryprops.kip.pmw.client.resource.AsyncPaymentFailedResource;
import com.kerryprops.kip.pmw.client.resource.AsyncPaymentFailedResource.AsyncPaymentFailedBodyResource;
import com.kerryprops.kip.pmw.client.security.CipherUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.common.constants.AppConstants.DEFAULT_ZONE_ID;
import static com.kerryprops.kip.bill.common.utils.BillingFun.exceptionToNull;

@Slf4j
@Service
@AllArgsConstructor
public class AptBillPaymentCallbackServiceImpl implements AptBillPaymentCallbackService {

    private final CipherUtil cipherUtil;

    private final DataMigrationConfig dm;

    private final ObjectMapper objectMapper;

    private final AptPayService aptPayService;

    private final AptBillRepository aptBillRepository;

    private final AptPaymentBillRepository aptPaymentBillRepository;

    private final AptJdeBillRepository jdeBillRepository;

    private final AptPayConfigRepository payConfigRepository;

    private final AptPaymentInfoRepository paymentInfoRepository;

    private final AptBillWxTemplateMsgService templateMsgService;

    private final AptBillDirectDebitsBatchBillService batchBillService;


    @Override
    @Transactional
    public void handlePaymentCallback(AsynPaymentResultBodyResource backResource) {
        getHandler(backResource).handle();
    }

    @Override
    @Transactional
    public void handlePaymentFailedCallback(AsyncPaymentFailedResource paymentFailedResource) {
        // 校验回调签名
        cipherUtil.verify(paymentFailedResource);

        AsyncPaymentFailedBodyResource bodyResource = paymentFailedResource.getBody();
        log.info("received_pay_failed_callback req: {}", exceptionToNull(() -> objectMapper.writeValueAsString(bodyResource)));
        String paymentInfoId = bodyResource.getOrderNo();
        String errorMsg = bodyResource.getErrorMsg();
        if (StringUtils.isBlank(paymentInfoId)) {
            log.info("received_pay_failed_callback, empty paymentInfoId");
            return;
        }
        if (StringUtils.isBlank(errorMsg)) {
            log.info("received_pay_failed_callback, empty errorMsg");
            return;
        }
        Optional<AptPaymentInfo> op = paymentInfoRepository.findById(paymentInfoId);
        if (op.isEmpty()) {
            log.info("received_pay_failed_callback, aptPaymentInfo not found");
            return;
        }
        AptPaymentInfo paymentInfo = op.get();
        String oldFailedReason = paymentInfo.getFailedReason();
        if (!Objects.equals(errorMsg, oldFailedReason) && !"success".equalsIgnoreCase(errorMsg)) {
            if ("扣款超时".equals(oldFailedReason)) {
                paymentInfo.setFailedReason(errorMsg);
            } else {
                paymentInfo.setFailedReason(oldFailedReason + "; " + errorMsg);
            }
            paymentInfoRepository.save(paymentInfo);
            log.info("received_pay_failed_callback: update failed reason from '{}' to '{}'", oldFailedReason, errorMsg);
        }
    }

    AptPaymentCallbackHandler getHandler(AsynPaymentResultBodyResource backResource) {
        String paymentInfoId = backResource.getOrderNo();
        var paymentInfoOp = paymentInfoRepository.findById(paymentInfoId);
        if (paymentInfoOp.isEmpty()) {
            log.info("backResource OrderNo: {}", paymentInfoId);
            throw new AppException("700009", "PaymentInfo not found: " + paymentInfoId);
        }
        AptPaymentInfo paymentInfo = paymentInfoOp.get();
        BillPayModule billPayModule = paymentInfo.getBillPayModule();
        if (billPayModule == BillPayModule.CASHIER_FEE) {
            return new CashierFeeAptPaymentCallbackHandler(backResource, paymentInfo);
        } else if (Objects.nonNull(paymentInfo.getAdvanceAmount())
                && paymentInfo.getAdvanceAmount().compareTo(BigDecimal.ZERO) > 0
                && paymentInfo.getAdvanceAmount().compareTo(BigDecimal.valueOf(paymentInfo.getAmt())) == 0) {
            var coll = aptPaymentBillRepository.findAllByPaymentInfoIdAndDeleted(paymentInfoId, 0);
            if (CollectionUtils.isEmpty(coll)) {
                // 纯预收的情况使用如下处理，否则，使用common处理流程。
                return new CounterCashierPurePreCollectionPaymentCallbackHandler(backResource, paymentInfo);
            }
        }
        if (BillPaymentStatus.DIRECT_DEBIT_STATUSES.contains(paymentInfo.getPaymentStatus())) {
            return new DirectDebitsAptPaymentCallbackHandler(backResource, paymentInfo);
        }
        return new CommonAptPaymentCallbackHandler(backResource, paymentInfo);
    }

    /**
     * 处理普通回调
     */
    private class CommonAptPaymentCallbackHandler extends AptPaymentCallbackHandler {

        CommonAptPaymentCallbackHandler(AsynPaymentResultBodyResource backResource, AptPaymentInfo paymentInfo) {
            super(backResource, paymentInfo);
        }

        @Override
        void determineAptBills() {
            this.aptBills = aptBillRepository.queryAptBillByPaymentId(aptPaymentInfo.getId());
        }

        @Override
        void determineBillPayType2() {
            super.determineBillPayType2();
            if (PaymentPayType.ALI_WITHHOLD.equals(this.payType)) {
                this.payType = PaymentPayType.ALIPAY;
            } else if (PaymentPayType.WX_DIRECT_DEBIT.equals(this.payType)) {
                this.payType = PaymentPayType.WECHAT;
            }
        }

        @Override
        void handleSuccess() {
            generalHandleSuccess(BillPaymentStatus.PAID);
            super.sendPaymentResultTemplateMsg(command -> {
                command.setHeadline("您好，您有一个物业账单支付成功");
                // 旧版本这个字段不显示了，新版本新版本复用此字段显示“房号”
                PositionItemResponse positionItem = aptPaymentInfo.getPositionItem();
                if (Optional.ofNullable(positionItem).map(PositionItemResponse::getRoomName).isPresent()) {
                    command.setRemark(positionItem.getProjectName() + "-" + positionItem.getBuildingName()
                            + "-" + positionItem.getRoomName());
                } else {
                    command.setRemark("点击详情 --> 去开票");
                }
                command.setUrl(dm.getWxPaymentConfirmedMsgUrl().replace("ORDER_NO", aptPaymentInfo.getId()));
            });
        }

        @Override
        void handleExpired() {
            generalHandleExpired(BillPaymentStatus.CANCEL, BillPaymentStatus.PAYING);
            aptPayService.cancelAptPay(aptPaymentInfo, aptBills);
        }

    }

    /**
     * 处理代扣回调
     */
    private class DirectDebitsAptPaymentCallbackHandler extends AptPaymentCallbackHandler {

        DirectDebitsAptPaymentCallbackHandler(AsynPaymentResultBodyResource backResource, AptPaymentInfo paymentInfo) {
            super(backResource, paymentInfo);
        }

        @Override
        void determineAptBills() {
            aptBills = batchBillService.getAptBillsByPaymentOrderNo(paymentInfoId);
        }

        @Override
        boolean validatePaymentStatus() {
            if (!BillPaymentStatus.DIRECT_DEBIT_PAYING.equals(aptPaymentInfo.getPaymentStatus())) {
                log.warn("PaymentInfo status changed.");
                return false;
            }
            return true;
        }

        @Override
        void handleSuccess() {
            generalHandleSuccess(BillPaymentStatus.DIRECT_DEBIT_PAID);
            super.sendPaymentResultTemplateMsg(command -> {
                if (PaymentPayType.ALIPAY.equals(paymentPayTypeEnum)) {
                    command.setHeadline("物业账单支付宝代扣成功");
                    command.setPaymentMethod("支付宝代扣");
                } else {
                    command.setHeadline("物业账单微信代扣成功");
                    command.setPaymentMethod("微信代扣");
                }
                PositionItemResponse positionItem = aptPaymentInfo.getPositionItem();
                if (Optional.ofNullable(positionItem).map(PositionItemResponse::getRoomName).isPresent()) {
                    command.setRemark(positionItem.getProjectName() + "-" + positionItem.getBuildingName()
                            + "-" + positionItem.getRoomName());
                } else {
                    command.setRemark("点击详情 --> 去开票");
                }
                command.setUrl(dm.getWxPaymentConfirmedMsgUrl().replace("ORDER_NO", aptPaymentInfo.getId()));
            });
        }

        @Override
        void handleExpired() {
            if (StringUtils.isBlank(aptPaymentInfo.getFailedReason())) {
                aptPaymentInfo.setFailedReason("扣款超时");
            }
            generalHandleExpired(BillPaymentStatus.DIRECT_DEBIT_FAILED, BillPaymentStatus.DIRECT_DEBIT_PAYING);
            // KIP-5146
            /*super.sendPaymentResultTemplateMsg(command -> {
                command.setHeadline("物业账单支付宝代扣失败提醒");
                command.setRemark("-->点击详情，请尽快完成自助缴费");
                command.setUrl(dm.getPayFailedMsgUrl().replace("ROOM_ID", aptPaymentInfo.getRoomId()));
                command.setPaymentMethod("支付宝代扣");
            });*/
            templateMsgService.sendDirectPaymentFailedMsg(aptPaymentInfo, callbackResource);
        }

    }

    private class CashierFeeAptPaymentCallbackHandler extends AptPaymentCallbackHandler {

        public CashierFeeAptPaymentCallbackHandler(AsynPaymentResultBodyResource backResource
                , AptPaymentInfo paymentInfo) {
            super(backResource, paymentInfo);
        }

        @Override
        void determineAptBills() {
            this.aptBills = Collections.emptyList();
        }

        @Override
        void handleSuccess() {
            generalHandleSuccess(BillPaymentStatus.PAID);
        }

        @Override
        void handleExpired() {
            generalHandleExpired(BillPaymentStatus.CANCEL, BillPaymentStatus.PAYING);
            aptPayService.cancelAptPay(aptPaymentInfo, aptBills);
        }

    }

    private class CounterCashierPurePreCollectionPaymentCallbackHandler extends AptPaymentCallbackHandler {

        public CounterCashierPurePreCollectionPaymentCallbackHandler(AsynPaymentResultBodyResource backResource
                , AptPaymentInfo paymentInfo) {
            super(backResource, paymentInfo);
        }

        @Override
        void determineAptBills() {
            this.aptBills = Collections.emptyList();
        }

        @Override
        void handleSuccess() {
            generalHandleSuccess(BillPaymentStatus.PAID);
        }

        @Override
        void handleExpired() {
            generalHandleExpired(BillPaymentStatus.CANCEL, BillPaymentStatus.PAYING);
        }

    }

    /**
     * 回调处理类
     */
    private abstract class AptPaymentCallbackHandler {

        AsynPaymentResultBodyResource callbackResource;

        String paymentInfoId;

        AptPaymentInfo aptPaymentInfo;

        List<AptBill> aptBills;

        // cash | wechatpay | alipay
        PaymentPayType paymentPayTypeEnum;

        AptPayConfig payConfig;

        PaymentPayType payType;

        AptPaymentCallbackHandler(AsynPaymentResultBodyResource backResource, AptPaymentInfo aptPaymentInfo) {
            this.callbackResource = backResource;
            this.paymentInfoId = backResource.getOrderNo();
            this.aptPaymentInfo = aptPaymentInfo;
        }

        public void handle() {
            determineAptBills();
            determinePayType();
            determineBillPayType2();

            String callbackState = callbackResource.getState();
            if (BillPayStateEnum.SUCCESS.name().equalsIgnoreCase(callbackState)) {
                handleSuccess();
            } else if (BillPayStateEnum.EXPIRED.name().equalsIgnoreCase(callbackState)) {
                handleExpired();
            } else {
                handleOtherStatus();
            }
        }

        void determinePayType() {
            if (Objects.isNull(aptPaymentInfo.getPayType()) || PaymentPayType.UNKNOWN.equals(aptPaymentInfo.getPayType())) {
                this.paymentPayTypeEnum = PaymentPayType.fromPspName(callbackResource.getPspName());
            } else {
                this.paymentPayTypeEnum = aptPaymentInfo.getPayType();
            }
        }

        void determineBillPayType2() {
            payType = PaymentPayType.convertToBillPayType(paymentPayTypeEnum
                    , callbackResource.getPayOption());
        }

        void determinePayConfig() {
            Iterable<AptPayConfig> payConfigIterable = payConfigRepository
                    .findAll(QAptPayConfig.aptPayConfig.paymentType.eq(payType.getInfo())
                            .and(QAptPayConfig.aptPayConfig.projectId.eq(aptPaymentInfo.getProjectId())));
            //2021-11-03, samatha - 不分区线上线下配置，如果有多个配置，则取第一个。
            if (payConfigIterable.iterator().hasNext()) {
                this.payConfig = payConfigIterable.iterator().next();
            } else {
                throw new AppException("7000010", "pay config not found");
            }
        }

        void verifyJdeBills() {
            //verify jde bill.
            var billNos = aptBills.stream().map(AptBill::getBillNo).collect(Collectors.toList());
            Iterable<AptJdeBill> jdeBillIterable = jdeBillRepository.
                    findAll(QAptJdeBill.aptJdeBill.billNumber.in(billNos));
            jdeBillIterable.forEach(jdeBill -> {
                jdeBill.setOnlineVerification(1);
                jdeBill.setOnlineVerificationTime(new Date());
            });
            if (jdeBillIterable.iterator().hasNext()) {
                jdeBillRepository.saveAll(jdeBillIterable);
                log.info("verify Jde Bills and saved: {}"
                        , exceptionToNull(() -> objectMapper.writeValueAsString(Lists.newLinkedList(jdeBillIterable))));
            }
        }

        void generalHandleSuccess(BillPaymentStatus paymentStatus) {
            determinePayConfig();
            aptPaymentInfo.setPaymentStatus(paymentStatus);
            aptPaymentInfo.setAppliedInvoice(1);
            // pay type bases on payment service provider name
            aptPaymentInfo.setPayType(paymentPayTypeEnum);
            String payTypeInfo = aptPaymentInfo.getPayTypeInfo();
            if (StringUtils.isBlank(payTypeInfo)) {
                aptPaymentInfo.setPayTypeInfo(paymentPayTypeEnum.getInfo());
            }
            // set pay time
            Date payTime;
            if (Objects.isNull(aptPaymentInfo.getPaymentTime())) {
                ZonedDateTime payDateTime = ZonedDateTime.parse(callbackResource.getFinishedDate()
                        , DateTimeFormatter.ISO_OFFSET_DATE_TIME.withZone(DEFAULT_ZONE_ID));
                payTime = Date.from(payDateTime.toInstant());
                aptPaymentInfo.setPaymentTime(payTime);
            } else {
                payTime = aptPaymentInfo.getPaymentTime();
            }
            aptPaymentInfo.setPaymentTransNo(callbackResource.getUniquePaymentId());
            aptPaymentInfo.setPspTransNo(callbackResource.getPspTin());
            // save payment info
            paymentInfoRepository.save(aptPaymentInfo);
            log.info("payment info saved: {}", exceptionToNull(() -> objectMapper.writeValueAsString(aptPaymentInfo)));

            double paidAmt = BillUtil.formatAmount(new BigDecimal(callbackResource.getOrderAmount())).doubleValue();
            if (CollectionUtils.isEmpty(aptBills)) {
                aptPayService.savePayInfo(aptPaymentInfo, payConfig, callbackResource.getPspTin()
                        , paidAmt, payTime);
            } else {
                aptPayService.savePayInfo(aptPaymentInfo, payConfig, callbackResource.getPspTin()
                        , paidAmt, payTime, aptBills);
                for (AptBill aptBill : aptBills) {
                    aptBill.setPayTime(new Date());
                    if (BillPayModule.CASHIER.equals(aptPaymentInfo.getBillPayModule())) {
                        aptBill.setStatus(BillStatus.CASHIER_PAID);
                        aptBill.setPaymentResult(BillStatus.CASHIER_PAID.getInfo());
                    } else {
                        aptBill.setStatus(BillStatus.ONLINE_PAID);
                        if (BillPaymentStatus.DIRECT_DEBIT_PAID.equals(paymentStatus)) {
                            aptBill.setPaymentResult("代扣");
                        } else {
                            aptBill.setPaymentResult("线上支付");
                        }
                    }
                    aptBill.setPaymentStatus(paymentStatus);
                    aptBill.setUpdateTime(new Date());
                }
                aptBillRepository.saveAll(aptBills);
                for (AptBill bill : aptBills) {
                    log.info("update_apt_bill: {}", exceptionToNull(() -> objectMapper.writeValueAsString(bill)));
                }
                // 更新apt_sync_bills 标记位
                verifyJdeBills();
            }
        }

        void generalHandleExpired(BillPaymentStatus paymentInfoStatus, BillPaymentStatus billPaymentStatus) {
            if (Boolean.FALSE.equals(validatePaymentStatus())) {
                return;
            }
            aptPaymentInfo.setCancelType(PayCancelTypeEnum.TIMEOUT_CANCELLED.name());
            aptPaymentInfo.setPaymentStatus(paymentInfoStatus);
            aptPaymentInfo.setPayType(paymentPayTypeEnum);
            String payTypeInfo = aptPaymentInfo.getPayTypeInfo();
            if (StringUtils.isBlank(payTypeInfo)) {
                aptPaymentInfo.setPayTypeInfo(paymentPayTypeEnum.getInfo());
            }

            paymentInfoRepository.save(aptPaymentInfo);
            for (AptBill bill : aptBills) {
                // 还原apt_bill支付状态为：TO_BE_PAID
                if (billPaymentStatus.equals(bill.getPaymentStatus())) {
                    bill.setUpdateTime(new Date());
                    bill.setPaymentStatus(BillPaymentStatus.TO_BE_PAID);
                    aptBillRepository.save(bill);
                }
            }
        }

        void sendPaymentResultTemplateMsg(Consumer<PaymentConfirmedCommand> commandConsumer) {
            templateMsgService.sendPaymentResultNotice(this.aptPaymentInfo, this.callbackResource, commandConsumer);
        }

        abstract void determineAptBills();

        abstract void handleSuccess();

        abstract void handleExpired();

        boolean validatePaymentStatus() {

            if (!List.of(BillPaymentStatus.TO_BE_PAID, BillPaymentStatus.PAYING)
                    .contains(aptPaymentInfo.getPaymentStatus())) {
                log.warn("PaymentInfo status changed. not to_be_paid or paying");
                return false;
            }
            return true;
        }

        void handleOtherStatus() {
            throw new RuntimeException("un-supported PaymentInfo status.");
        }

    }

}