package com.kerryprops.kip.bill.service.model.s;

import com.google.common.collect.ImmutableList;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.enums.AptPayVerifyStatus;
import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import com.kerryprops.kip.bill.common.enums.BillPayModule;
import com.kerryprops.kip.bill.common.enums.InvoicedStatus;
import com.kerryprops.kip.bill.common.jpa.QueryFilter;
import com.kerryprops.kip.bill.dao.entity.QAptPay;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.utils.BillUtil;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import com.querydsl.core.types.Predicate;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 名   称：apt_offline_pay
 * 描   述：
 * 作   者：David Wei
 * 时   间：2021/08/31 15:38:33
 * --------------------------------------------------
 * 修改历史
 * 序号    日期    修改人     修改原因
 * 1
 * **************************************************
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AptPayBo implements QueryFilter {

    private Long id;

    //费用类型说明
    @Enumerated(value = EnumType.STRING)
    private BillPayChannel payChannel;

    //描述
    private String payDesc;

    //收费时间段开始
    private String beginDate;

    //收费时间段结束
    private String endDate;

    //支付配置ID
    private Long payConfigId;

    //支付方式
    private String payType;

    //科目账
    private String paymentCate;

    //手续费率
    private Double tax;

    //手续费
    private Double taxAmt;

    //合计
    private Double totalAmt;

    //支付时间
    private Date payDate;

    private Date payDateStart;

    private Date payDateEnd;

    //支付流水
    private String payTranx;

    //收款号
    private String payAct;

    //支付详情
    private String payDetail;

    //是否已复核
    private Integer verifyStatus;

    //是否写入JDE
    private Integer sendJdeStatus;

    @Schema(title = "楼盘CODE")
    private String projectId;

    @Schema(title = "楼栋ID")
    private String buildingId;

    @Schema(title = "楼层ID")
    private String floorId;

    @Schema(title = "房间ID")
    private String roomId;

    private PositionItemResponse positionItem;

    //备注
    private String comments;

    //收款人
    private Integer deletedAt;

    private String createBy;

    private Date createTime;

    private Date updateTime;

    private InvoicedStatus invoicedStatus;

    public static Integer toTaxFeelFeeFenWithRate(double totalAmt, double taxRate, double maxTax) {
        double taxAmt = BillUtil.calcTaxAmount(totalAmt, taxRate, maxTax);
        BigDecimal totalAmtDecimal = BigDecimal.valueOf(totalAmt);
        BigDecimal taxAmtDecimal = BigDecimal.valueOf(taxAmt);
        BigDecimal fee = totalAmtDecimal.subtract(taxAmtDecimal);
        fee = BillUtil.formatAmount(fee);
        return Objects.requireNonNull(fee).multiply(BigDecimal.valueOf(100d)).intValue();
    }

    @Override
    public List<Optional<Predicate>> predicates() {
        if (deletedAt == null) {
            deletedAt = 0;
        }

        ImmutableList.Builder<Optional<Predicate>> builder = ImmutableList.builder();
        builder.add(
                Optional.of(deletedAt).map(QAptPay.aptPay.deletedAt::eq))
                .add(Optional.of(List.of(BillPayModule.KERRY, BillPayModule.CASHIER)).map(QAptPay.aptPay.billPayModule::in))
                .add(Optional.ofNullable(maxBindingScope()).map(QAptPay.aptPay.buildingId::in))
                .add(Optional.ofNullable(id).map(QAptPay.aptPay.id::eq))
                .add(Optional.ofNullable(payDateStart).map(QAptPay.aptPay.payDate::after))
                .add(Optional.ofNullable(payDateEnd).map(QAptPay.aptPay.payDate::before))
                .add(Optional.ofNullable(payAct).map(e -> "%" + e + "%").map(QAptPay.aptPay.payAct::like))
                .add(Optional.ofNullable(roomId).map(QAptPay.aptPay.roomId::eq))
                .add(Optional.ofNullable(projectId).map(QAptPay.aptPay.projectId::eq))
                .add(Optional.ofNullable(buildingId).map(QAptPay.aptPay.buildingId::eq))
                .add(Optional.ofNullable(payConfigId).map(QAptPay.aptPay.payConfigId::eq))
                .add(Optional.ofNullable(payType).map(QAptPay.aptPay.payType::eq))
                .add(Optional.ofNullable(sendJdeStatus).map(QAptPay.aptPay.sendJdeStatus::eq))
                .add(Optional.ofNullable(verifyStatus).map(AptPayVerifyStatus::fromIndex)
                        .map(QAptPay.aptPay.verifyStatus::eq))
                .add(Optional.ofNullable(invoicedStatus).map(QAptPay.aptPay.invoicedStatus::eq));

        return builder.build();
    }

    public List<String> maxBindingScope() {
        LoginUser loginUser = UserInfoUtils.getUser();
        if (loginUser == null) {
            throw new RuntimeException("not login.");
        }
        if (Boolean.TRUE.equals(loginUser.isSuperAdmin())) {
            return null;
        }
        return loginUser.toBuildingIdList();
    }

}
