package com.kerryprops.kip.bill.service.model.leg;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffBillReceiverRespVo;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.Set;

/**
 * 电子账单对象 kerry_bill
 *
 * <AUTHOR>
 * @date 2020-10-15
 */
public class Bill extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 账单类型
     */
    private String tpDct;

    /**
     * 账单中文描述
     */
    private String tpDl01;

    /**
     * 账单文件的链接地址
     */
    private String fileUrl;

    /**
     * 账单文件名
     */
    private String tpGtfilenm;

    /**
     * 公司编号
     */
    private String tpCo;

    /**
     * 公司名称
     */
    private String tpDl03;

    /**
     * 用户编号
     */
    private String tpAn8;

    /**
     * 用户名称
     */
    private String tpAlph;

    /**
     * jde合同号
     */
    private String tpDoco;

    /**
     * 建筑物编号
     */
    private String tpMcu;

    /**
     * 建筑物描述
     */
    private String tpDc;

    /**
     * 账单生成日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date tpCrtutime;

    /**
     * 账单状态 0未发送、5发送成功、10发送失败;15已删除；20文件缺失
     */
    private Integer tpStatus;

    /**
     * 账单年
     */
    private Integer tpFyr;

    /**
     * 账单月
     */
    private Integer tpPn;

    private Integer billMonth;

    /**
     * 网站显示 Y为显示
     */
    private String tpEv01;

    /**
     * 账单来源
     */
    private String tpEv02;

    /**
     * 删除标志
     */
    private String delFlag;

    /**
     * 账单打印时间
     */
    private String formatDate;

    /**
     * 账单文件名称
     */
    private String tpGtitnm;

    /**
     * 账单单元
     */
    private String tpUnit;

    /**
     * 账单站内信发送状态
     */
    private Integer mailStatus;

    /**
     * 账单邮件发送状态
     */
    private Integer emailStatus;

    /**
     * 账单站内信发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date mailDate;

    /**
     * 账单邮件发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date emailDate;

    /**
     * 账单的邮件解析结果
     */
    private String emailErr;

    /**
     * 站内信最早阅读时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date mailReadTime;

    /**
     * 手机端的最早阅读时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date mobileReadTime;

    /**
     * 用户阅读状态 0 未读、1已读  不管是哪里进行阅读了都是设置为已读
     */
    private Integer readStatus;

    private Set<StaffBillReceiverRespVo> userInfoList;

    private Integer sourceId;

    public Bill() {
    }

    public Bill(String tpDct, String tpDl01, String tpGtfilenm, String tpCo, String tpDl03, String tpAn8, String tpAlph, String tpDoco, String tpMcu, String tpDc, Date tpCrtutime,
                Integer tpFyr, Integer tpPn, String tpEv01, String tpEv02, String formatDate, String tpGtitnm, String tpUnit) {
        this.tpDct = tpDct;
        this.tpDl01 = tpDl01;
        this.tpGtfilenm = tpGtfilenm;
        this.tpCo = tpCo;
        this.tpDl03 = tpDl03;
        this.tpAn8 = tpAn8;
        this.tpAlph = tpAlph;
        this.tpDoco = tpDoco;
        this.tpMcu = tpMcu;
        this.tpDc = tpDc;
        this.tpCrtutime = tpCrtutime;
        this.tpFyr = tpFyr;
        this.tpPn = tpPn;
        this.tpEv01 = tpEv01;
        this.tpEv02 = tpEv02;
        this.formatDate = formatDate;
        this.tpGtitnm = tpGtitnm;
        this.tpUnit = tpUnit;
        this.emailStatus = 0;
        this.mailStatus = 0;
    }

    public Integer getSourceId() {
        return sourceId;
    }

    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    public Set<StaffBillReceiverRespVo> getUserInfoList() {
        return userInfoList;
    }

    public void setUserInfoList(Set<StaffBillReceiverRespVo> userInfoList) {
        this.userInfoList = userInfoList;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTpDct() {
        return tpDct;
    }

    public void setTpDct(String tpDct) {
        this.tpDct = tpDct;
    }

    public String getTpDl01() {
        return tpDl01;
    }

    public void setTpDl01(String tpDl01) {
        this.tpDl01 = tpDl01;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getTpGtfilenm() {
        return tpGtfilenm;
    }

    public void setTpGtfilenm(String tpGtfilenm) {
        this.tpGtfilenm = tpGtfilenm;
    }

    public String getTpCo() {
        return tpCo;
    }

    public void setTpCo(String tpCo) {
        this.tpCo = tpCo;
    }

    public String getTpDl03() {
        return tpDl03;
    }

    public void setTpDl03(String tpDl03) {
        this.tpDl03 = tpDl03;
    }

    public String getTpAn8() {
        return tpAn8;
    }

    public void setTpAn8(String tpAn8) {
        this.tpAn8 = tpAn8;
    }

    public String getTpAlph() {
        return tpAlph;
    }

    public void setTpAlph(String tpAlph) {
        this.tpAlph = tpAlph;
    }

    public String getTpDoco() {
        return tpDoco;
    }

    public void setTpDoco(String tpDoco) {
        this.tpDoco = tpDoco;
    }

    public String getTpMcu() {
        return tpMcu;
    }

    public void setTpMcu(String tpMcu) {
        this.tpMcu = tpMcu;
    }

    public String getTpDc() {
        return tpDc;
    }

    public void setTpDc(String tpDc) {
        this.tpDc = tpDc;
    }

    public Date getTpCrtutime() {
        return tpCrtutime;
    }

    public void setTpCrtutime(Date tpCrtutime) {
        this.tpCrtutime = tpCrtutime;
    }

    public Integer getTpStatus() {
        return tpStatus;
    }

    public void setTpStatus(Integer tpStatus) {
        this.tpStatus = tpStatus;
    }

    public Integer getTpFyr() {
        return tpFyr;
    }

    public void setTpFyr(Integer tpFyr) {
        this.tpFyr = tpFyr;
    }

    public Integer getTpPn() {
        return tpPn;
    }

    public void setTpPn(Integer tpPn) {
        this.tpPn = tpPn;
    }

    public String getTpEv01() {
        return tpEv01;
    }

    public void setTpEv01(String tpEv01) {
        this.tpEv01 = tpEv01;
    }

    public String getTpEv02() {
        return tpEv02;
    }

    public void setTpEv02(String tpEv02) {
        this.tpEv02 = tpEv02;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getFormatDate() {
        return formatDate;
    }

    public void setFormatDate(String formatDate) {
        this.formatDate = formatDate;
    }

    public String getTpGtitnm() {
        return tpGtitnm;
    }

    public void setTpGtitnm(String tpGtitnm) {
        this.tpGtitnm = tpGtitnm;
    }

    public String getTpUnit() {
        return tpUnit;
    }

    public void setTpUnit(String tpUnit) {
        this.tpUnit = tpUnit;
    }

    public Integer getMailStatus() {
        return mailStatus;
    }

    public void setMailStatus(Integer mailStatus) {
        this.mailStatus = mailStatus;
    }

    public Integer getEmailStatus() {
        return emailStatus;
    }

    public void setEmailStatus(Integer emailStatus) {
        this.emailStatus = emailStatus;
    }

    public Date getMailDate() {
        return mailDate;
    }

    public void setMailDate(Date mailDate) {
        this.mailDate = mailDate;
    }

    public Date getEmailDate() {
        return emailDate;
    }

    public void setEmailDate(Date emailDate) {
        this.emailDate = emailDate;
    }

    public String getEmailErr() {
        return emailErr;
    }

    public void setEmailErr(String emailErr) {
        this.emailErr = emailErr;
    }

    public Date getMailReadTime() {
        return mailReadTime;
    }

    public void setMailReadTime(Date mailReadTime) {
        this.mailReadTime = mailReadTime;
    }

    public Date getMobileReadTime() {
        return mobileReadTime;
    }

    public void setMobileReadTime(Date mobileReadTime) {
        this.mobileReadTime = mobileReadTime;
    }

    public Integer getReadStatus() {
        return readStatus;
    }

    public void setReadStatus(Integer readStatus) {
        this.readStatus = readStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("tpDct", getTpDct())
                .append("tpDl01", getTpDl01())
                .append("fileUrl", getFileUrl())
                .append("tpGtfilenm", getTpGtfilenm())
                .append("tpCo", getTpCo())
                .append("tpDl03", getTpDl03())
                .append("tpAn8", getTpAn8())
                .append("tpAlph", getTpAlph())
                .append("tpDoco", getTpDoco())
                .append("tpMcu", getTpMcu())
                .append("tpDc", getTpDc())
                .append("tpCrtutime", getTpCrtutime())
                .append("tpStatus", getTpStatus())
                .append("tpFyr", getTpFyr())
                .append("tpPn", getTpPn())
                .append("tpEv01", getTpEv01())
                .append("tpEv02", getTpEv02())
                .append("delFlag", getDelFlag())
                .append("formatDate", getFormatDate())
                .append("tpGtitnm", getTpGtitnm())
                .append("tpUnit", getTpUnit())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("mailStatus", getMailStatus())
                .append("emailStatus", getEmailStatus())
                .append("mailDate", getMailDate())
                .append("emailDate", getEmailDate())
                .append("emailErr", getEmailErr())
                .toString();
    }

    public Integer getBillMonth() {
        return billMonth;
    }

    public void setBillMonth(Integer billMonth) {
        this.billMonth = billMonth;
    }

}
