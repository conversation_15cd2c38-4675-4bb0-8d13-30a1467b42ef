package com.kerryprops.kip.bill.service.model.leg;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.ImmutableList;
import com.kerryprops.kip.bill.common.jpa.QueryFilter;
import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import com.kerryprops.kip.bill.dao.entity.QBillEntity;
import com.querydsl.core.types.Predicate;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 针对用户进行查询账单信息 查询实体
 */
@Data
public class BillUserSelectVo extends BaseEntity implements QueryFilter {

    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 账单类型
     */
    private String tpDct;

    /**
     * 账单中文描述
     */
    private String tpDl01;

    /**
     * 账单文件的链接地址
     */
    private String fileUrl;

    /**
     * 账单文件名
     */
    private String tpGtfilenm;

    /**
     * 公司编号
     */
    private String tpCo;

    /**
     * 公司名称
     */
    private String tpDl03;

    /**
     * 用户编号
     */
    private String tpAn8;

    /**
     * 用户编号集合
     */
    private List<String> tpAn8List;

    /**
     * 用户名称
     */
    private String tpAlph;

    /**
     * jde合同号
     */
    private String tpDoco;

    /**
     * 用户的合同号集合
     */
    private List<String> tpDocoList;

    /**
     * 建筑物编号
     */
    private String tpMcu;

    /**
     * 建筑物编号 集合
     */
    private List<String> tpMcuList;

    /**
     * 建筑物描述
     */
    private String tpDc;

    /**
     * 账单生成日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date tpCrtutime;

    /**
     * 账单状态 0未发送、5发送成功、10发送失败;15已删除；20文件缺失
     */
    private Integer tpStatus;

    /**
     * 账单年
     */
    private Integer tpFyr;

    /**
     * 账单月
     */
    private Integer tpPn;

    private Integer tpFyrStart;

    private Integer tpPnStart;

    private Integer tpFyrEnd;

    private Integer tpPnEnd;

    /**
     * 网站显示 Y为显示
     */
    private String tpEv01;

    /**
     * 账单来源
     */
    private String tpEv02;

    /**
     * 删除标志
     */
    private String delFlag;

    /**
     * 账单打印时间
     */
    private String formatDate;

    /**
     * 账单文件名称
     */
    private String tpGtitnm;

    /**
     * 账单单元
     */
    private String tpUnit;

    /**
     * 账单站内信发送状态 0未发送，5已发送
     */
    private Integer mailStatus;

    /**
     * 账单邮件发送状态 0未发送，5已发送
     */
    private Integer emailStatus;

    /**
     * 账单站内信发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date mailDate;

    /**
     * 账单邮件发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date emailDate;

    /**
     * 账单的邮件解析结果
     */
    private String emailErr;

    /**
     * 是否在前端可以选中进行发送账单
     */
    private boolean checkedFlag;

    /**
     * 站内信阅读时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date mailReadTime;

    /**
     * 账单的发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date billSendTime;

    /**
     * 手机端的最早阅读时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date mobileReadTime;

    /**
     * 用户阅读状态 0 未读、1已读  不管是哪里进行阅读了都是设置为已读
     */
    private Integer readStatus;

    /**
     * 定义 本次应该是更新 站内信的最早阅读时间 还是手机端的最早阅读时间
     * 为 1 是更新手机端的阅读时间，为0是更新站内信的阅读时间
     */
    private Integer mailOrMobileSource;

    private String updateBy;

    @Override
    public List<Optional<Predicate>> predicates() {
        Integer beginBillMonth = null;
        Integer endBillMonth = null;
        if (tpFyrStart != null) {
            beginBillMonth = tpFyrStart * 100 + tpPnStart;
        }
        if (tpFyrEnd != null) {
            endBillMonth = tpFyrEnd * 100 + tpPnEnd;
        }

        ImmutableList.Builder builder = ImmutableList.<Optional<Predicate>>builder();
        builder.add(Optional.ofNullable("0").map(QBillEntity.billEntity.delFlag::eq))
                .add(Optional.ofNullable("Y").map(QBillEntity.billEntity.tpEv01::eq))
                .add(Optional.ofNullable(5).map(QBillEntity.billEntity.tpStatus::eq))
                .add(Optional.ofNullable(tpDoco).map(QBillEntity.billEntity.tpDoco::eq))
                .add(Optional.ofNullable(tpAn8).map(QBillEntity.billEntity.tpAn8::eq))
                .add(Optional.ofNullable(tpDct).map(QBillEntity.billEntity.tpDct::eq))
                .add(Optional.ofNullable(tpUnit).map(QBillEntity.billEntity.tpUnit::eq))
                .add(Optional.ofNullable(tpFyr).map(QBillEntity.billEntity.tpFyr::eq))
                .add(Optional.ofNullable(tpPn).map(QBillEntity.billEntity.tpPn::eq))
                .add(Optional.ofNullable(readStatus).map(QBillEntity.billEntity.readStatus::eq))
                .add(Optional.ofNullable(mailStatus).map(QBillEntity.billEntity.mailStatus::eq))
                .add(Optional.ofNullable(beginBillMonth).map(QBillEntity.billEntity.billMonth::goe))
                .add(Optional.ofNullable(endBillMonth).map(QBillEntity.billEntity.billMonth::loe))
        ;

        if (CollectionUtils.isNotEmpty(tpMcuList)) {
            builder.add(Optional.ofNullable(tpMcuList).map(QBillEntity.billEntity.tpMcu::in));
        } else {
            builder.add(Optional.ofNullable(tpAn8List).map(QBillEntity.billEntity.tpAn8::in))
                    .add(Optional.ofNullable(tpDocoList).map(QBillEntity.billEntity.tpDoco::in));
        }
        return builder.build();
    }

}
