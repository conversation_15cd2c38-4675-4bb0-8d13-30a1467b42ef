package com.kerryprops.kip.bill.service.model.s;

import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import lombok.Data;

import java.time.Duration;

@Data
public class CreatePaySessionDto {

    private String projectId;

    private String buildingId;

    private Double amt;

    private String orderNo;

    /**
     * 支付超时时间，默认单位：分钟
     */
    private Duration payTimeout;

    private PositionItemResponse positionItem;

}