package com.kerryprops.kip.bill.service;

import com.kerryprops.kip.bill.service.model.s.AptBillDirectDebitsBatchBo;
import com.kerryprops.kip.bill.service.model.s.AptBillSearchReqBo;
import com.kerryprops.kip.bill.webservice.vo.req.DirectDebitsBatchesCreationRequest;
import com.kerryprops.kip.bill.webservice.vo.resp.DirectDebitsBatchBillAmountResource;
import com.kerryprops.kip.bill.webservice.vo.resp.DirectDebitsBatchBillDetailResource;
import com.kerryprops.kip.bill.webservice.vo.resp.DirectDebitsBillBatchQueryResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface AptBillDirectDebitsBatchService {

    Page<DirectDebitsBillBatchQueryResource> queryDirectDebitsBatches(AptBillDirectDebitsBatchBo bo
            , Pageable pageable);

    DirectDebitsBillBatchQueryResource deleteDirectDebitsBatch(String batchNo);

    DirectDebitsBillBatchQueryResource createDirectDebitsBatch(DirectDebitsBatchesCreationRequest request);

    Page<DirectDebitsBatchBillDetailResource> queryBillsInBatch(String batchNo
            , Pageable pageable, AptBillSearchReqBo searchReqBo);

    boolean validateDirectDebitsBatchCreation(DirectDebitsBatchesCreationRequest request);

    List<DirectDebitsBatchBillDetailResource> queryBillsInBatch(String batchNo, AptBillSearchReqBo searchReqBo);

    DirectDebitsBillBatchQueryResource pushDirectDebitsBatchBills(String batchNo);

    boolean deleteDirectDebitsBatchBills(String batchNo, List<String> billNos);

    DirectDebitsBatchBillAmountResource calculateBatchBillAmount(String batchNo, AptBillSearchReqBo searchReqBo);

    List<DirectDebitsBillBatchQueryResource> lapsedCheck();

}
