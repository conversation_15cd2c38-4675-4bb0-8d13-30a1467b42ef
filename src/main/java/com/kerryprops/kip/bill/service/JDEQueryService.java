package com.kerryprops.kip.bill.service;

import com.kerryprops.kip.bill.common.utils.jde.OracleJdbc;
import com.kerryprops.kip.bill.webservice.impl.JDEQueryController.QueryResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Service for executing JDE database queries.
 * This service handles database operations for JDE data access.
 *
 * <AUTHOR>
 * @since 2025-5-15
 */
@Slf4j
@Service
public class JDEQueryService {

    /**
     * Custom exception for JDE query errors.
     */
    public static class JDEQueryException extends RuntimeException {

        public JDEQueryException(String message, Throwable cause) {
            super(message, cause);
        }

    }

    /**
     * Executes the SQL query and processes the results.
     * 
     * WARNING: This method executes raw SQL without parameter binding.
     * It is intended for internal development use only.
     *
     * @param sql            The SQL query to execute
     * @param timeoutSeconds Query timeout in seconds
     * @return The query results
     * @throws SQLException If a database access error occurs
     */
    public QueryResult executeQuery(String sql, int timeoutSeconds) throws SQLException {
        // Log the original SQL query
        log.info("Original SQL query: {}", sql);
        
        try (var conn = OracleJdbc.getOracleConnection(); 
             var stmt = Objects.requireNonNull(conn).createStatement()) {

            // Set query timeout with sensible default (30 seconds) if not properly specified
            int timeout = (timeoutSeconds > 0) ? timeoutSeconds : 30;
            stmt.setQueryTimeout(timeout);
            
            // Log if default timeout is used
            if (timeoutSeconds <= 0) {
                log.info("Using default query timeout of {} seconds", timeout);
            }

            // Start timing execution
            long startTime = System.currentTimeMillis();
            
            // Execute query and process results
            try (var rs = stmt.executeQuery(sql)) {
                // Process results
                List<Map<String, Object>> results = processResultSet(rs);
                
                // Calculate execution time
                long executionTime = System.currentTimeMillis() - startTime;
                
                // Log query execution results
                log.info("SQL query executed in {} ms, returned {} rows", executionTime, results.size());
                
                return new QueryResult(results);
            }
        }
    }

    /**
     * Converts a ResultSet to a structured list of maps.
     *
     * @param rs The ResultSet to process
     * @return List of maps representing the data
     * @throws SQLException If a database access error occurs
     */
    private List<Map<String, Object>> processResultSet(ResultSet rs) throws SQLException {
        var resultList = new ArrayList<Map<String, Object>>();
        var metaData = rs.getMetaData();
        var columnCount = metaData.getColumnCount();

        while (rs.next()) {
            var row = new LinkedHashMap<String, Object>();

            for (int i = 1; i <= columnCount; i++) {
                var columnName = metaData.getColumnLabel(i)
                                         .toUpperCase();
                var value = rs.getObject(i);
                row.put(columnName, value);
            }

            resultList.add(row);
        }

        return resultList;
    }

}
