package com.kerryprops.kip.bill.service;


import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.service.model.s.AptPayInvoiceBo;
import com.kerryprops.kip.bill.service.model.s.BillInvoiceCalcDto;
import com.kerryprops.kip.bill.service.model.s.CreatePaySessionDto;
import com.kerryprops.kip.bill.webservice.vo.req.AptPaymentVo;
import com.kerryprops.kip.bill.webservice.vo.req.CallBackKerryInvoiceMainVo;
import com.kerryprops.kip.bill.webservice.vo.resp.AptPaymentInfoVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillSessionResult;
import com.kerryprops.kip.pmw.client.resource.SessionOutputResource;

import java.util.List;

public interface PaymentBillService {

    /**
     * 实时计算支付的账单和发票数据
     */
    AptPayInvoiceBo calcBillInvoice(BillInvoiceCalcDto dto);

    SessionOutputResource createPaymentSession(CreatePaySessionDto createPaySessionDto);

    BillSessionResult billPayment(List<AptPaymentVo> aptPayVos, boolean checkAmount);

    void cancelPayment(String paymentId);

    List<AptPaymentInfo> queryMyPaymentInfoList(Long userId, String buildingId, String roomId);

    void applyInvoice(String paymentId);

    void cancelInvoice(String paymentId);

    AptPaymentInfoVo queryPaymentInfoById(String paymentId);

    AptPaymentInfo invoiceCallback(String orderNo, CallBackKerryInvoiceMainVo mainVo);

    AptPaymentInfo getBillPaymentInfoById(String id);

    void cancelPaymentTransaction(AptPaymentInfo aptPaymentInfo);

}
