package com.kerryprops.kip.bill.service;

import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptBillOperation;
import com.kerryprops.kip.bill.dao.entity.AptBillOperationContent;
import com.kerryprops.kip.bill.dao.entity.AptBillOperator;
import com.kerryprops.kip.bill.service.model.s.AptBillOperationBo;
import com.kerryprops.kip.bill.webservice.vo.resp.OperationChangedFiledRespVo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;

public interface AptBillOperationService {

    boolean saveAutoOperationLog(AptBill aptBill, List<OperationChangedFiledRespVo> changes);

    boolean saveOperationLog(AptBill aptBill, List<OperationChangedFiledRespVo> changes
            , AptBillOperator operator, String comment);

    Page<AptBillOperation> queryOperationLogs(AptBillOperationBo bo, Pageable pageable);

    List<AptBillOperation> queryOperationLogs(AptBillOperationBo bo);

    List<AptBillOperationContent> queryOperationContents(Collection<Long> ids);

}
