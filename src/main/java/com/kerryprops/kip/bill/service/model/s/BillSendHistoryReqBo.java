package com.kerryprops.kip.bill.service.model.s;

import com.kerryprops.kip.bill.common.jpa.QueryFilter;
import com.querydsl.core.types.Predicate;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Path;
import jakarta.persistence.criteria.Root;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 账单查询的 view
 */
@Data
@Schema
public class BillSendHistoryReqBo implements QueryFilter {

    private static final long serialVersionUID = 1L;

    private List<String> bus;

    private Date emailDate;

    private String tpAlph;

    public Specification getAc() {

        Specification specification = new Specification() {

            @Override
            public jakarta.persistence.criteria.Predicate toPredicate(Root root, CriteriaQuery query, CriteriaBuilder cb) {
                //增加筛选条件
                jakarta.persistence.criteria.Predicate predicate = cb.conjunction();
                predicate.getExpressions().add(cb.equal(root.get("delFlag"), 0));
                predicate.getExpressions().add(cb.equal(root.get("tpEv01"), "Y"));
                //账单状态 0未发送、5发送成功、10发送失败
                predicate.getExpressions().add(cb.notEqual(root.get("tpStatus"), "0"));
                if (StringUtils.isNotEmpty(tpAlph)) {
                    predicate.getExpressions().add(cb.like(root.get("tpAlph"), "%" + tpAlph + "%"));
                }

                if (CollectionUtils.isNotEmpty(bus)) {
                    Path<Object> path = root.get("tpMcu");
                    CriteriaBuilder.In<Object> in = cb.in(path);
                    for (String mcu : bus) {
                        in.value(mcu);
                    }
                    predicate.getExpressions().add(in);
                }
                Date start = null;
                Date end = null;
                if (emailDate != null) {
                    Calendar c = Calendar.getInstance();
                    c.setTime(emailDate);
                    c.set(Calendar.HOUR_OF_DAY, 0);
                    c.set(Calendar.MINUTE, 0);
                    c.set(Calendar.SECOND, 0);
                    c.set(Calendar.MILLISECOND, 0);
                    start = c.getTime();

                    c = Calendar.getInstance();
                    c.setTime(emailDate);
                    c.set(Calendar.HOUR_OF_DAY, 0);
                    c.set(Calendar.MINUTE, 0);
                    c.set(Calendar.SECOND, 0);
                    c.set(Calendar.MILLISECOND, 0);
                    c.add(Calendar.DAY_OF_MONTH, 1);
                    end = c.getTime();
                }
                if (emailDate != null) {
                    predicate.getExpressions().add(cb.between(root.get("emailDate").as(Date.class), start, end));
                }
                predicate.getExpressions().add(cb.or(cb.isNotNull(root.get("emailDate")), cb.isNotNull(root.get("mailDate"))));
                return predicate;
            }
        };
        return specification;
    }

    @Override
    public List<Optional<Predicate>> predicates() {
        return null;
    }

}
