package com.kerryprops.kip.bill.service;

import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptBillOperator;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface AptBillPushService {

    /**
     * 异步推送账单
     */
    void pushAptBill(Map<String, List<AptBill>> billMap, Set<String> roomIds
            , AptBillOperator aptBillOperator, String projectId);

}
