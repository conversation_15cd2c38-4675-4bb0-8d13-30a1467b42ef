package com.kerryprops.kip.bill.service.model.s;

import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.utils.BillUtil;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 2023-12-25 16:15:08
 **/
@Setter
@Getter
public class AptPayInvoiceBo {

    /**
     * 能否开票键值对
     * key: billId
     * value: isBilling
     */
    Map<Long, String> canBillInvoice = Collections.emptyMap();

    /**
     * 可开票金额
     */
    BigDecimal canInvoiceAmount;

    /**
     * 账单金额
     */
    BigDecimal billAmount;

    /**
     * 预付款的能否开票的标识
     */
    String isAdvanceBilling;

    List<AptBill> bills = Collections.emptyList();

    public BigDecimal getCanInvoiceAmount() {
        return BillUtil.formatAmount(canInvoiceAmount);
    }

    public BigDecimal getBillAmount() {
        return BillUtil.formatAmount(billAmount);
    }

}
