package com.kerryprops.kip.bill.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.exceptions.RestInvalidParamException;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.utils.BuConverter;
import com.kerryprops.kip.bill.common.utils.CommonUtil;
import com.kerryprops.kip.bill.dao.BillRepository;
import com.kerryprops.kip.bill.dao.BillSendConfigAn8LinkRepository;
import com.kerryprops.kip.bill.dao.BillSendConfigRepository;
import com.kerryprops.kip.bill.dao.entity.BillSendConfig;
import com.kerryprops.kip.bill.dao.entity.BillSendConfigAn8Link;
import com.kerryprops.kip.bill.dao.entity.QBillEntity;
import com.kerryprops.kip.bill.dao.entity.QBillSendConfig;
import com.kerryprops.kip.bill.dao.entity.QBillSendConfigAn8Link;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.clients.HiveClient;
import com.kerryprops.kip.bill.feign.clients.SUserClient;
import com.kerryprops.kip.bill.feign.entity.TenantManagerItemResponse;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.IBillSendConfigService;
import com.kerryprops.kip.bill.service.model.s.BillSendConfigAccountBo;
import com.kerryprops.kip.bill.service.model.s.DocoPayerBo;
import com.kerryprops.kip.bill.webservice.vo.req.BillPayerVo;
import com.kerryprops.kip.bill.webservice.vo.req.BillSendConfigReqVo;
import com.kerryprops.kip.bill.webservice.vo.req.DocoBillPayer;
import com.kerryprops.kip.bill.webservice.vo.req.StaffBillSendConfigInsertVo;
import com.kerryprops.kip.bill.webservice.vo.req.StaffBillSendConfigUpdateVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillPayer;
import com.kerryprops.kip.bill.webservice.vo.resp.BillSendConfigResource;
import com.kerryprops.kip.hiveas.feign.dto.BuildingDTO;
import com.kerryprops.kip.hiveas.webservice.vo.resp.ProjectBuildingVO;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.common.constants.AppConstants.COMMA;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.UPDATE_DELETE_ERROR;
import static com.kerryprops.kip.bill.common.utils.NonNullUtils.nonNullStringFunction;
import static java.util.Optional.ofNullable;

@Service
@Slf4j
public class BillSendConfigServiceImpl implements IBillSendConfigService {

    private static final int DELETE_STATUS = 1;

    private static final int ACTIVE_STATUS = 0;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private HiveAsClient hiveAsClient;

    @Autowired
    private SUserClient sUserClient;

    private final Function<String, TenantManagerItemResponse> mobileFunction = (String phoneNumber) -> {
        if (StringUtils.isEmpty(phoneNumber)) {
            return null;
        }

        List<TenantManagerItemResponse> responses = sUserClient.queryLoginAccounts(phoneNumber);
        return CollectionUtils.isEmpty(responses) ? null : responses.get(0);
    };

    @Autowired
    private BillRepository billRepository;

    @Autowired
    private HiveClient hiveService;

    @Autowired
    private BillSendConfigRepository configRepository;

    @Autowired
    private BillSendConfigAn8LinkRepository linkRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    @Transactional
    public BillSendConfigResource saveBillSendConfig(StaffBillSendConfigInsertVo staffBillSendConfigInsertVo) {
        JPAQueryFactory jpaQueryFactory = billRepository.getJpaQueryFactory();
        List<String> mcuList = jpaQueryFactory
                .select(QBillEntity.billEntity.tpMcu)
                .from(QBillEntity.billEntity)
                .where(QBillEntity.billEntity.delFlag.eq("0")
                        .and(QBillEntity.billEntity.tpDoco.eq(staffBillSendConfigInsertVo.getDoco())))
                .groupBy(QBillEntity.billEntity.tpMcu)
                .orderBy(QBillEntity.billEntity.tpMcu.asc()).fetch();
        List<String> unitList = jpaQueryFactory.select(QBillEntity.billEntity.tpUnit)
                .from(QBillEntity.billEntity)
                .where(QBillEntity.billEntity.delFlag.eq("0")
                        .and(QBillEntity.billEntity.tpDoco.eq(staffBillSendConfigInsertVo.getDoco())))
                .groupBy(QBillEntity.billEntity.tpUnit)
                .orderBy(QBillEntity.billEntity.tpUnit.asc()).fetch();

        String phoneNumber = ofNullable(staffBillSendConfigInsertVo.getPhoneNumber()).orElse(StringUtils.EMPTY);

        TenantManagerItemResponse tenantManagerItemResponse = mobileFunction.apply(phoneNumber);

        String tenantManagerId = Objects.isNull(tenantManagerItemResponse) || Objects.isNull(tenantManagerItemResponse.getId())
                ? StringUtils.EMPTY : String.valueOf(tenantManagerItemResponse.getId());

        String emailUsername = Objects.isNull(tenantManagerItemResponse) || Objects.isNull(tenantManagerItemResponse.getUserName())
                ? StringUtils.EMPTY : tenantManagerItemResponse.getUserName();

        String loginNo = Objects.isNull(tenantManagerItemResponse) || Objects.isNull(tenantManagerItemResponse.getLoginNo())
                ? StringUtils.EMPTY : tenantManagerItemResponse.getLoginNo();

        BillSendConfig config = BillSendConfig.builder()
                                              .doco(staffBillSendConfigInsertVo.getDoco())
                                              .phoneNumber(phoneNumber)
                                              .loginNo(loginNo)
                                              .tenantManagerId(tenantManagerId)
                                              .emailUsername(emailUsername)
                                              .email(nonNullStringFunction.apply(staffBillSendConfigInsertVo.getEmail()))
                                              .projectId(nonNullStringFunction.apply(staffBillSendConfigInsertVo.getProjectId()))
                                              .buildingId(nonNullStringFunction.apply(staffBillSendConfigInsertVo.getBuildingId()))
                                              .buildingName(nonNullStringFunction.apply(staffBillSendConfigInsertVo.getBuildingName()))
                                              .mcu(mcuList.stream().collect(Collectors.joining(COMMA)))
                                              .unit(unitList.stream().collect(Collectors.joining(COMMA)))
                                              .manualUpdateOperator(UserInfoUtils.getKerryAccount())
                                              .manualUpdateTime(ZonedDateTime.now())
                                              .isDel(ACTIVE_STATUS).createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                                              .build();
        BillSendConfig newConfig = configRepository.save(config);

        List<BillPayerVo> billPayerVos = staffBillSendConfigInsertVo.getBillPayerVos();
        List<BillSendConfigAn8Link> links = billPayerVos.stream()
                .map(billPayerVo -> BillSendConfigAn8Link.builder()
                        .configId(newConfig.getId()).alph(Optional.ofNullable(billPayerVo.getAlph())
                                .orElse(Strings.EMPTY)).an8(billPayerVo.getAn8())
                        .isDel(ACTIVE_STATUS).createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now()).build())
                .collect(Collectors.toList());
        List<BillSendConfigAn8Link> newAn8Links = linkRepository.saveAll(links);

        BillSendConfigResource resource = BeanUtil.copy(newConfig, BillSendConfigResource.class);
        resource.setAn8LinkList(newAn8Links);
        return resource;
    }

    @Override
    @Transactional
    public BillSendConfigResource updateBillSendConfig(StaffBillSendConfigUpdateVo staffBillSendConfigUpdateVo) {
        Long configId = staffBillSendConfigUpdateVo.getId();
        BillSendConfig oldConfig = configRepository.findById(configId).orElse(null);
        if (Objects.isNull(oldConfig)) {
            throw new RestInvalidParamException(RespCodeEnum.NOT_FOUND);
        }
        if (DELETE_STATUS == oldConfig.getIsDel()) {
            throw new RestInvalidParamException(UPDATE_DELETE_ERROR);
        }

        //更新账单发送配置
        String phoneNumber = ofNullable(staffBillSendConfigUpdateVo.getPhoneNumber()).orElse(StringUtils.EMPTY);
        TenantManagerItemResponse tenantManagerItemResponse = mobileFunction.apply(phoneNumber);

        String tenantManagerId = Objects.isNull(tenantManagerItemResponse) || Objects.isNull(tenantManagerItemResponse.getId())
                ? StringUtils.EMPTY : String.valueOf(tenantManagerItemResponse.getId());

        String emailUsername = Objects.isNull(tenantManagerItemResponse) || Objects.isNull(tenantManagerItemResponse.getUserName())
                ? StringUtils.EMPTY : tenantManagerItemResponse.getUserName();

        String loginNo = Objects.isNull(tenantManagerItemResponse) || Objects.isNull(tenantManagerItemResponse.getLoginNo())
                ? StringUtils.EMPTY : tenantManagerItemResponse.getLoginNo();

        oldConfig.setEmail(nonNullStringFunction.apply(staffBillSendConfigUpdateVo.getEmail()));
        oldConfig.setPhoneNumber(phoneNumber);
        oldConfig.setTenantManagerId(tenantManagerId);
        oldConfig.setEmailUsername(emailUsername);
        oldConfig.setLoginNo(loginNo);
        oldConfig.setProjectId(nonNullStringFunction.apply(staffBillSendConfigUpdateVo.getProjectId()));
        oldConfig.setBuildingId(nonNullStringFunction.apply(staffBillSendConfigUpdateVo.getBuildingId()));
        oldConfig.setBuildingName(nonNullStringFunction.apply(staffBillSendConfigUpdateVo.getBuildingName()));
        oldConfig.setManualUpdateOperator(UserInfoUtils.getKerryAccount());
        oldConfig.setManualUpdateTime(ZonedDateTime.now());
        oldConfig.setUpdatedTime(ZonedDateTime.now());
        BillSendConfig newConfig = configRepository.save(oldConfig);

        //遍历账单发送配置:AN8链接列表
        List<BillPayerVo> billPayerVos = staffBillSendConfigUpdateVo.getBillPayerVos();
        List<BillSendConfigAn8Link> dbAn8Links = linkRepository.selectAn8Links(configId);
        List<BillSendConfigAn8Link> an8Links = handleAN8LinkUpdate(configId, billPayerVos, dbAn8Links);
        linkRepository.saveAll(an8Links);
        List<BillSendConfigAn8Link> newAn8Links = linkRepository.selectAn8Links(configId);
        newAn8Links = newAn8Links.stream()
                .filter(billSendConfigAn8Link -> ACTIVE_STATUS == billSendConfigAn8Link.getIsDel())
                .collect(Collectors.toList());

        //向调用方返回资源
        BillSendConfigResource resource = BeanUtil.copy(newConfig, BillSendConfigResource.class);
        resource.setAn8LinkList(newAn8Links);
        return resource;
    }

    @Override
    public Page<BillSendConfigResource> searchBillSendConfig(Pageable pageable, BillSendConfigReqVo vo) {
        QBillSendConfig qConfig = QBillSendConfig.billSendConfig;
        QBillSendConfigAn8Link qAn8Link = QBillSendConfigAn8Link.billSendConfigAn8Link;
        Predicate predicate = qConfig.isDel.eq(0).and(qAn8Link.isDel.eq(0));
        predicate = StringUtils.isEmpty(vo.getDoco()) ? predicate : ExpressionUtils.and(predicate, qConfig.doco.eq(vo.getDoco()));
        predicate = StringUtils.isEmpty(vo.getUnit()) ? predicate : ExpressionUtils.and(predicate, qConfig.unit.contains(vo.getUnit()));
        predicate = StringUtils.isEmpty(vo.getMcu()) ? predicate : ExpressionUtils.and(predicate, qConfig.mcu.contains(vo.getMcu()));
        predicate = StringUtils.isEmpty(vo.getAlph()) ? predicate : ExpressionUtils.and(predicate, qAn8Link.alph.contains(vo.getAlph()));
        predicate = StringUtils.isEmpty(vo.getAn8()) ? predicate : ExpressionUtils.and(predicate, qAn8Link.an8.contains(vo.getAn8()));
        predicate = StringUtils.isEmpty(vo.getProjectId()) ? predicate :
                ExpressionUtils.and(predicate, qConfig.projectId.equalsIgnoreCase(vo.getProjectId()));
        predicate = CollectionUtils.isEmpty(vo.getBuildingIds()) ? predicate :
                ExpressionUtils.and(predicate, qConfig.buildingId.contains(vo.getBuildingIds().get(0)));

        JPAQueryFactory jpaQueryFactory = billRepository.getJpaQueryFactory();
        List<Long> allConfigIds = jpaQueryFactory.select(qConfig.id)
                .from(qConfig)
                .leftJoin(qAn8Link)
                .on(qConfig.id.eq(qAn8Link.configId))
                .where(predicate)
                .groupBy(qConfig.id)
                .orderBy(qConfig.id.asc()).fetch();

        Predicate configPredicate = qConfig.isDel.eq(0).and(qConfig.id.in(allConfigIds));
        Page<BillSendConfig> configPage = configRepository.findAll(configPredicate, pageable);
        Page<BillSendConfigResource> resources = configPage.map(e -> BeanUtil.copy(e, BillSendConfigResource.class));
        List<Long> configIds = configPage.map(BillSendConfig::getId).toList();

        Predicate an8Predicate = qAn8Link.isDel.eq(0).and(qAn8Link.configId.in(configIds));
        List<BillSendConfigAn8Link> an8Links = Lists.newArrayList(linkRepository.findAll(an8Predicate).iterator());

        resources.stream().forEach(billSendConfigResource -> {
            Long configId = billSendConfigResource.getId();
            List<BillSendConfigAn8Link> links = an8Links.stream()
                    .filter(billSendConfigAn8Link -> configId.equals(billSendConfigAn8Link.getConfigId()))
                    .collect(Collectors.toList());
            billSendConfigResource.setAn8LinkList(links);
        });
        return resources;
    }

    @Override
    @Transactional
    public boolean deleteBillSendConfig(List<Long> configIds) {
        if (CollectionUtils.isEmpty(configIds)) {
            log.error("input empty configIds");
            return false;
        }

        List<BillSendConfig> billSendConfigs = configRepository.findAllById(configIds);
        if (CollectionUtils.isEmpty(billSendConfigs)) {
            log.error("bill send config NOT exists. configIds={}", configIds);
            return false;
        }
        billSendConfigs = billSendConfigs.stream()
                .filter(billSendConfig -> ACTIVE_STATUS == billSendConfig.getIsDel()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(billSendConfigs)) {
            log.info("the specified bill-send-configs have been deleted");
            return true;
        }

        List<BillSendConfigAn8Link> an8Links = linkRepository.selectActiveAn8Links(configIds);
        if (CollectionUtils.isEmpty(an8Links)) {
            log.error("invalid bill send config with empty an8 links");
            return false;
        }

        billSendConfigs.stream().forEach(billSendConfig -> {
            billSendConfig.setIsDel(DELETE_STATUS);
            billSendConfig.setUpdatedTime(ZonedDateTime.now());
            billSendConfig.setManualUpdateOperator(UserInfoUtils.getKerryAccount());
            billSendConfig.setManualUpdateTime(ZonedDateTime.now());
        });
        configRepository.saveAll(billSendConfigs);

        an8Links.stream().forEach(billSendConfigAn8Link -> {
            billSendConfigAn8Link.setIsDel(DELETE_STATUS);
            billSendConfigAn8Link.setUpdatedTime(ZonedDateTime.now());
        });
        linkRepository.saveAll(an8Links);

        return true;
    }

    @Override
    public boolean syncMcuFromTenantBills() {
        JPAQueryFactory jpaQueryFactory = billRepository.getJpaQueryFactory();
        List<String> docoList = jpaQueryFactory
                .select(QBillSendConfig.billSendConfig.doco).distinct()
                .from(QBillSendConfig.billSendConfig)
                .where(QBillSendConfig.billSendConfig.isDel.eq(ACTIVE_STATUS))
                .groupBy(QBillSendConfig.billSendConfig.doco)
                .orderBy(QBillSendConfig.billSendConfig.doco.asc())
                .fetch();
        if (CollectionUtils.isEmpty(docoList)) {
            log.error("No Active BillSendConfig FOUND here");
            return false;
        }

        Map<String, String> mcuMap = queryMCUsByDoco(docoList);
        Map<String, String> unitMap = queryUnitsByDoco(docoList);

        List<BillSendConfig> updatedBillSendConfigs = new LinkedList<>();
        List<BillSendConfig> billSendConfigs = jpaQueryFactory
                .select(QBillSendConfig.billSendConfig)
                .from(QBillSendConfig.billSendConfig)
                .where(QBillSendConfig.billSendConfig.isDel.eq(ACTIVE_STATUS))
                .orderBy(QBillSendConfig.billSendConfig.id.asc())
                .fetch();
        billSendConfigs.stream().forEach(billSendConfig -> {
            String doco = billSendConfig.getDoco();
            String mcu = mcuMap.get(doco);
            String unit = unitMap.get(doco);

            if (CommonUtil.comparePossiblyNull(mcu, billSendConfig.getMcu()) != 1 ||
                    CommonUtil.comparePossiblyNull(unit, billSendConfig.getUnit()) != -1) {
                billSendConfig.setMcu(mcu);
                billSendConfig.setUnit(unit);
                updatedBillSendConfigs.add(billSendConfig);
            }
        });
        batchUpdateBillSendConfigMcus(updatedBillSendConfigs);

        return true;
    }

    @Override
    public boolean syncEnterpriseAccounts() {
        JPAQueryFactory jpaQueryFactory = configRepository.getJpaQueryFactory();
        QBillSendConfig qBillSendConfig = QBillSendConfig.billSendConfig;
        List<Tuple> tupleList = jpaQueryFactory
                .select(qBillSendConfig.id, qBillSendConfig.phoneNumber, qBillSendConfig.loginNo)
                .from(qBillSendConfig)
                .where(qBillSendConfig.isDel.eq(ACTIVE_STATUS).and(qBillSendConfig.phoneNumber.isNotEmpty()))
                .fetch();

        List<BillSendConfigAccountBo> accountBoList = new LinkedList<>();
        tupleList.stream().forEach(tuple -> {
            Long configId = tuple.get(0, Long.class);
            String phoneNumber = tuple.get(1, String.class);
            String loginNo = tuple.get(2, String.class);

            BillSendConfigAccountBo accountBo = BillSendConfigAccountBo.builder()
                    .configId(configId).phoneNumber(phoneNumber).loginNo(loginNo).build();
            accountBoList.add(accountBo);
        });

        List<String> phoneNumbers = accountBoList.stream()
                .map(BillSendConfigAccountBo::getPhoneNumber)
                .filter(phone -> StringUtils.isNotEmpty(phone))
                .distinct().collect(Collectors.toList());
        Map<String, TenantManagerItemResponse> accountMap = sUserClient.queryLoginAccounts(phoneNumbers);
        if (Objects.isNull(accountMap) || CollectionUtils.sizeIsEmpty(accountMap)) {
            log.error("no B-LoginAccount(s) FOUND");
            return false;
        }

        List<BillSendConfigAccountBo> updateAccountBoList = new LinkedList<>();
        for (BillSendConfigAccountBo accountBo : accountBoList) {
            String phoneNumber = accountBo.getPhoneNumber();
            String oldLoginNo = accountBo.getLoginNo();

            TenantManagerItemResponse tenantManagerItemResponse = accountMap.get(phoneNumber);
            String newLoginNo = Optional.ofNullable(tenantManagerItemResponse).map(TenantManagerItemResponse::getLoginNo)
                    .orElse(StringUtils.EMPTY);

            String tenantManagerId = Optional.ofNullable(tenantManagerItemResponse).map(TenantManagerItemResponse::getId)
                    .map(String::valueOf).orElse(StringUtils.EMPTY);

            String emailUsername = Optional.ofNullable(tenantManagerItemResponse).map(TenantManagerItemResponse::getUserName)
                    .orElse(StringUtils.EMPTY);

            //判断是否发生变化
            int compare = CommonUtil.comparePossiblyNull(oldLoginNo, newLoginNo);
            if (compare != 0) {
                BillSendConfigAccountBo updatedAccountBo = BillSendConfigAccountBo.builder()
                        .tenantManagerId(tenantManagerId).emailUsername(emailUsername)
                        .configId(accountBo.getConfigId()).phoneNumber(phoneNumber).loginNo(newLoginNo).build();
                updateAccountBoList.add(updatedAccountBo);
            }
        }
        batchUpdateBillSendConfigAccounts(updateAccountBoList);

        return true;
    }

    @Override
    public List<String> queryDocos(String bAccount) {
        if (StringUtils.isEmpty(bAccount)) {
            log.warn("[BillConfig][QueryFailed] - Unable to query bill payers due to invalid bAccount");
            return Collections.emptyList();
        }

        JPAQueryFactory jpaQueryFactory = configRepository.getJpaQueryFactory();
        List<String> docoList = jpaQueryFactory
                .select(QBillSendConfig.billSendConfig.doco)
                .from(QBillSendConfig.billSendConfig)
                .where(QBillSendConfig.billSendConfig.isDel.eq(0).and(QBillSendConfig.billSendConfig.loginNo.eq(bAccount)))
                .groupBy(QBillSendConfig.billSendConfig.doco)
                .orderBy(QBillSendConfig.billSendConfig.doco.asc())
                .fetch();
        return docoList;
    }

    @Override
    public List<BillPayer> queryBillPayers(String bAccount) {
        if (StringUtils.isEmpty(bAccount)) {
            log.warn("[BillPayer][QueryFailed] - Cannot query bill payers with empty bAccount");
            return Collections.emptyList();
        }

        JPAQueryFactory jpaQueryFactory = configRepository.getJpaQueryFactory();
        List<Long> configList = jpaQueryFactory
                .select(QBillSendConfig.billSendConfig.id)
                .from(QBillSendConfig.billSendConfig)
                .where(QBillSendConfig.billSendConfig.isDel.eq(0).and(QBillSendConfig.billSendConfig.loginNo.eq(bAccount)))
                .orderBy(QBillSendConfig.billSendConfig.id.asc())
                .fetch();
        if (CollectionUtils.isEmpty(configList)) {
            log.warn("[BillPayer][NotFound] - No bill payers associated with bAccount={}", bAccount);
            return Collections.emptyList();
        }

        QBillSendConfigAn8Link qLink = QBillSendConfigAn8Link.billSendConfigAn8Link;
        List<Tuple> tupleList = jpaQueryFactory
                .select(qLink.alph, qLink.an8)
                .from(qLink)
                .where(qLink.isDel.eq(0).and(qLink.configId.in(configList)))
                .groupBy(qLink.alph, qLink.an8)
                .orderBy(qLink.alph.asc(), qLink.an8.asc())
                .fetch();
        List<BillPayer> billPayers = new LinkedList<>();
        tupleList.stream().forEach(tuple -> {
            String alph = tuple.get(0, String.class);
            String an8 = tuple.get(1, String.class);
            billPayers.add(BillPayer.builder().tpAlph(alph).tpAn8(an8).build());
        });

        return billPayers;
    }

    @Override
    public List<BillPayer> fuzzyQueryPayers(String projectId, String query) {
        if (StringUtils.isEmpty(projectId)) {
            return Collections.emptyList();
        }

        BooleanExpression predicate = QBillSendConfig.billSendConfig.projectId.equalsIgnoreCase(projectId)
                .and(QBillSendConfig.billSendConfig.id
                        .eq(QBillSendConfigAn8Link.billSendConfigAn8Link.configId))
                .and(QBillSendConfig.billSendConfig.isDel.eq(0))
                .and(QBillSendConfigAn8Link.billSendConfigAn8Link.isDel.eq(0));

        if (StringUtils.isNotBlank(query)) {
            BooleanExpression selectPredicate = QBillSendConfigAn8Link.billSendConfigAn8Link.alph.contains(query)
                    .or(QBillSendConfigAn8Link.billSendConfigAn8Link.an8.contains(query));

            int indexOfSplit = query.lastIndexOf("-");
            if (-1 != indexOfSplit) {
                String queryPurchaserName = query.substring(0, indexOfSplit);
                String queryAn8 = query.substring(indexOfSplit + 1);

                selectPredicate = selectPredicate.or((QBillSendConfigAn8Link.billSendConfigAn8Link.alph.contains(queryPurchaserName))
                        .and(QBillSendConfigAn8Link.billSendConfigAn8Link.an8.contains(queryAn8)));
            }

            predicate = predicate.and(selectPredicate);
        }

        JPAQueryFactory jpaQueryFactory = billRepository.getJpaQueryFactory();
        List<Tuple> tuples = jpaQueryFactory.select(QBillSendConfigAn8Link.billSendConfigAn8Link.alph
                , QBillSendConfigAn8Link.billSendConfigAn8Link.an8)
                .from(QBillSendConfigAn8Link.billSendConfigAn8Link, QBillSendConfig.billSendConfig)
                .where(predicate).groupBy(QBillSendConfigAn8Link.billSendConfigAn8Link.id).fetch();
        if (CollectionUtils.isEmpty(tuples)) {
            return Collections.emptyList();
        }
        return tuples.stream().map(t -> new BillPayer(t.get(0, String.class), t.get(1, String.class)))
                .collect(Collectors.toList());
    }

    @Override
    public BillSendConfigResource queryById(long id) {
        Optional<BillSendConfig> op = configRepository.findById(id);
        if (op.isEmpty()) {
            return null;
        }
        BillSendConfig billSendConfig = op.get();
        BillSendConfigResource billSendConfigResource = BeanUtil.copy(billSendConfig, BillSendConfigResource.class);
        List<BillSendConfigAn8Link> an8Links = linkRepository.selectActiveAn8Links(List.of(billSendConfigResource.getId()));
        billSendConfigResource.setAn8LinkList(an8Links);
        return billSendConfigResource;
    }

    @Override
    public List<BillSendConfig> filterBillSendConfigs(DocoBillPayer docoBillPayer) {
        if (Objects.isNull(docoBillPayer)) {
            return Collections.emptyList();
        }
        QBillSendConfig config = QBillSendConfig.billSendConfig;
        QBillSendConfigAn8Link an8Link = QBillSendConfigAn8Link.billSendConfigAn8Link;
        BooleanExpression predicate = config.id.eq(an8Link.configId)
                .and(config.isDel.eq(0))
                .and(an8Link.isDel.eq(0))
                .and(config.doco.eq(docoBillPayer.getDoco()))
                .and(an8Link.an8.eq(docoBillPayer.getAn8()));
        JPAQueryFactory jpaQueryFactory = configRepository.getJpaQueryFactory();
        return jpaQueryFactory.select(config).from(config, an8Link).where(predicate).fetch();
    }

    private List<BillSendConfigAn8Link> handleAN8LinkUpdate(long configId, List<BillPayerVo> billPayerVos,
                                                            List<BillSendConfigAn8Link> dbAn8Links) {
        List<BillSendConfigAn8Link> resultList = new ArrayList<>();

        List<String> keepList = new ArrayList<>();
        Map<String, List<BillSendConfigAn8Link>> dbMap = dbAn8Links.stream().collect(Collectors.groupingBy(BillSendConfigAn8Link::groupByKey));
        Map<String, List<BillPayerVo>> inputMap = billPayerVos.stream().collect(Collectors.groupingBy(BillPayerVo::groupByKey));
        Iterator<Map.Entry<String, List<BillPayerVo>>> iterator = inputMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, List<BillPayerVo>> entry = iterator.next();
            String key = entry.getKey();
            BillPayerVo billPayerVo = inputMap.get(key).get(0);
            BillSendConfigAn8Link an8Link = Objects.isNull(dbMap) ? null :
                    (CollectionUtils.isEmpty(dbMap.get(key)) ? null : dbMap.get(key).get(0));

            if (Objects.nonNull(billPayerVo) && Objects.nonNull(an8Link)) {
                //保留该链接，确保状态处于"未删除"
                if (DELETE_STATUS == an8Link.getIsDel()) {
                    an8Link.setIsDel(ACTIVE_STATUS);
                    an8Link.setUpdatedTime(ZonedDateTime.now());
                    resultList.add(an8Link);
                }
                keepList.add(key);
            } else if (Objects.nonNull(billPayerVo)) {
                //插入新链接
                BillSendConfigAn8Link newLink = BillSendConfigAn8Link.builder()
                        .configId(configId).an8(billPayerVo.getAn8()).alph(billPayerVo.getAlph()).isDel(ACTIVE_STATUS)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now()).build();
                resultList.add(newLink);
            }
        }

        dbAn8Links.stream()
                .filter(billSendConfigAn8Link -> !keepList.contains(billSendConfigAn8Link.groupByKey()))
                .filter(billSendConfigAn8Link -> ACTIVE_STATUS == billSendConfigAn8Link.getIsDel())
                .forEach(billSendConfigAn8Link -> {
                    billSendConfigAn8Link.setIsDel(DELETE_STATUS);
                    billSendConfigAn8Link.setUpdatedTime(ZonedDateTime.now());
                    resultList.add(billSendConfigAn8Link);
                });
        return resultList;
    }

    private List<String> buildJdeBusFromProjects(List<String> projectIds) {
        if (CollectionUtils.isEmpty(projectIds)) {
            log.error("illegal projectIds");
            return null;
        }

        String[] projectIdArr = projectIds.toArray(new String[0]);
        List<ProjectBuildingVO> centers = hiveAsClient.getCenterByIds(projectIdArr);
        if (CollectionUtils.isEmpty(centers)) {
            log.error("hive unavailable and return empty data");
            return null;
        }

        List<BuildingDTO> buildings = centers.stream()
                .map(ProjectBuildingVO::getBuildings)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        Set<String> bus = new HashSet<>();
        bus.addAll(buildings.stream().map(e -> BuConverter.getBuList(e.getPropertyManagementBU()))
                .flatMap(Collection::stream).collect(Collectors.toList()));
        bus.addAll(buildings.stream().map(e -> BuConverter.getBuList(e.getPropertyDeveloperBU()))
                .flatMap(Collection::stream).collect(Collectors.toList()));
        return new ArrayList<>(bus);
    }

    private void batchUpdateBillSendConfigMcus(List<BillSendConfig> billSendConfigs) {
        if (CollectionUtils.isEmpty(billSendConfigs)) {
            log.info("no BillSendConfig to update mcu and unit");
            return;
        }

        long startTime = System.currentTimeMillis();
        String sql = new StringBuffer()
                .append("update kerry_bill_send_config")
                .append(" set ")
                .append("mcu = ?, ")
                .append("unit = ?, ")
                .append("updated_time = ? ")
                .append(" where id = ? ").toString();
        int[] updatedCountArray = jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {

            @Override
            public void setValues(PreparedStatement preparedStatement, int i) throws SQLException {
                BillSendConfig config = billSendConfigs.get(i);

                int index = 1;
                preparedStatement.setString(index++, Optional.ofNullable(config.getMcu()).orElse(StringUtils.EMPTY));
                preparedStatement.setString(index++, Optional.ofNullable(config.getUnit()).orElse(StringUtils.EMPTY));
                preparedStatement.setTimestamp(index++, Timestamp.from(ZonedDateTime.now(ZoneOffset.UTC).toInstant()));
                preparedStatement.setLong(index++, config.getId());
            }

            @Override
            public int getBatchSize() {
                return billSendConfigs.size();
            }
        });

        for (int i = 0; i < updatedCountArray.length; i++) {
            int result = updatedCountArray[i];
            BillSendConfig billSendConfig = billSendConfigs.get(i);
            if (result == Statement.EXECUTE_FAILED) {
                log.error("batch-update BillSendConfig failed. [{}]", JSONObject.toJSONString(billSendConfig));
            } else {
                log.info("batch-update BillSendConfig success. [{}]", JSONObject.toJSONString(billSendConfig));
            }
        }
        long endTime = System.currentTimeMillis();
        log.info("batchUpdateBillSendConfigs. items={}, eclipsedTime={}", billSendConfigs.size(), (endTime - startTime));
    }

    private List<DocoPayerBo> queryDocoPayers(String projectId) {
        List<String> projectIds = StringUtils.isEmpty(projectId) ? Collections.emptyList() : List.of(projectId);
        List<String> buList = buildJdeBusFromProjects(projectIds);
        if (CollectionUtils.isEmpty(buList)) {
            return Collections.emptyList();
        }

        JPAQueryFactory jpaQueryFactory = billRepository.getJpaQueryFactory();
        List<Tuple> tupleList = jpaQueryFactory
                .select(QBillEntity.billEntity.tpDoco, QBillEntity.billEntity.tpAlph, QBillEntity.billEntity.tpAn8)
                .from(QBillEntity.billEntity)
                .where(QBillEntity.billEntity.delFlag.eq("0").and(QBillEntity.billEntity.tpMcu.in(buList)))
                .groupBy(QBillEntity.billEntity.tpDoco, QBillEntity.billEntity.tpAlph, QBillEntity.billEntity.tpAn8)
                .orderBy(QBillEntity.billEntity.tpDoco.asc(), QBillEntity.billEntity.tpAlph.asc(), QBillEntity.billEntity.tpAn8.asc())
                .fetch();

        return tupleList.stream()
                .map(tuple -> DocoPayerBo.builder()
                        .doco(tuple.get(0, String.class))
                        .alph(tuple.get(1, String.class))
                        .an8(tuple.get(2, String.class)).build())
                .collect(Collectors.toList());
    }

/*    private void batchFillLoginNoByPhoneNumber(Set<BillSendReceiverBo> billSendReceiverBos) {
        List<String> phoneNumbers = billSendReceiverBos.stream()
                .map(BillSendReceiverBo::getPhoneNumber)
                .filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        Map<String, TenantManagerItemResponse> map = sUserClient.queryLoginAccounts(phoneNumbers);

        if (Objects.isNull(map) || CollectionUtils.sizeIsEmpty(map)) {
            log.error("no B-LoginAccount(s) FOUND");
            return;
        }

        billSendReceiverBos.stream().forEach(billSendReceiverBo -> {
            String phoneNumber = billSendReceiverBo.getPhoneNumber();
            phoneNumber = StringUtils.isEmpty(phoneNumber) ? StringUtils.EMPTY : phoneNumber;

            TenantManagerItemResponse response = StringUtils.isEmpty(phoneNumber) ? null : map.get(phoneNumber);
            String loginNo = Objects.isNull(response) ? StringUtils.EMPTY : response.getLoginNo();
            String userManagerId = Objects.isNull(response) ? StringUtils.EMPTY : String.valueOf(response.getId());
            String emailUsername = Objects.isNull(response) ? StringUtils.EMPTY : response.getUserName();

            billSendReceiverBo.setPhoneNumber(phoneNumber);
            billSendReceiverBo.setUserManagerId(userManagerId);
            billSendReceiverBo.setLoginNo(StringUtils.isEmpty(loginNo) ? StringUtils.EMPTY : loginNo);
            billSendReceiverBo.setEmailUsername(StringUtils.isEmpty(emailUsername) ? StringUtils.EMPTY : emailUsername);
        });
    }*/

    private Map<String, String> queryMCUsByDoco(List<String> docos) {
        JPAQueryFactory jpaQueryFactory = billRepository.getJpaQueryFactory();
        List<Tuple> tupleList = jpaQueryFactory
                .select(QBillEntity.billEntity.tpDoco, QBillEntity.billEntity.tpMcu)
                .from(QBillEntity.billEntity)
                .where(QBillEntity.billEntity.delFlag.eq("0").and(QBillEntity.billEntity.tpDoco.in(docos)))
                .groupBy(QBillEntity.billEntity.tpDoco, QBillEntity.billEntity.tpMcu)
                .orderBy(QBillEntity.billEntity.tpDoco.asc(), QBillEntity.billEntity.tpMcu.asc())
                .fetch();

        Map<String, List<String>> map = new HashMap<>();
        tupleList.stream().forEach(tuple -> {
            String doco = tuple.get(0, String.class);
            String mcu = tuple.get(1, String.class);

            List<String> mcuList = map.get(doco);
            if (Objects.isNull(mcuList)) {
                mcuList = new LinkedList<>();
                map.put(doco, mcuList);
            }
            mcuList.add(mcu);
        });

        Map<String, String> resultMap = new HashMap<>();
        for (Iterator<Map.Entry<String, List<String>>> iterator = map.entrySet().iterator(); iterator.hasNext(); ) {
            Map.Entry<String, List<String>> entry = iterator.next();
            List<String> mcuList = entry.getValue();
            String mcu = CollectionUtils.isEmpty(mcuList) ?
                    StringUtils.EMPTY : mcuList.stream().sorted().distinct().collect(Collectors.joining(","));
            resultMap.put(entry.getKey(), mcu);
        }
        return resultMap;
    }

    private Map<String, String> queryUnitsByDoco(List<String> docos) {
        JPAQueryFactory jpaQueryFactory = billRepository.getJpaQueryFactory();
        List<Tuple> tupleList = jpaQueryFactory
                .select(QBillEntity.billEntity.tpDoco, QBillEntity.billEntity.tpUnit)
                .from(QBillEntity.billEntity)
                .where(QBillEntity.billEntity.delFlag.eq("0").and(QBillEntity.billEntity.tpDoco.in(docos)))
                .groupBy(QBillEntity.billEntity.tpDoco, QBillEntity.billEntity.tpUnit)
                .orderBy(QBillEntity.billEntity.tpDoco.asc(), QBillEntity.billEntity.tpUnit.asc())
                .fetch();

        Map<String, List<String>> map = new HashMap<>();
        tupleList.stream().forEach(tuple -> {
            String doco = tuple.get(0, String.class);
            String unit = tuple.get(1, String.class);

            List<String> unitList = map.get(doco);
            if (Objects.isNull(unitList)) {
                unitList = new LinkedList<>();
                map.put(doco, unitList);
            }
            unitList.add(unit);
        });

        Map<String, String> resultMap = new HashMap<>();
        for (Iterator<Map.Entry<String, List<String>>> iterator = map.entrySet().iterator(); iterator.hasNext(); ) {
            Map.Entry<String, List<String>> entry = iterator.next();
            List<String> unitList = entry.getValue();
            String unit = CollectionUtils.isEmpty(unitList) ?
                    StringUtils.EMPTY : unitList.stream().sorted().distinct().collect(Collectors.joining(","));
            resultMap.put(entry.getKey(), unit);
        }
        return resultMap;
    }

/*    private Map<DocoAn8, Set<BillSendReceiverBo>> queryBillReceiverByDoco(List<DocoAn8> docoAn8List) {
        //step2: 请求hive-vas服务，根据合同号列表反查租户列表
        if (Objects.isNull(docoAn8List)) {
            log.error("an8Map is null");
            return null;
        }

        List<String> listDoco = docoAn8List.stream().map(DocoAn8::getDoco).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(listDoco)) {
            log.error("listDoco is empty");
            return null;
        }

        RespWrapVo<Set<TenantRespDto>> tenantListByDoco = hiveAsClient.getTenantIdByDoCo(listDoco);
        if (!RespWrapVo.isResponseValidWithData(tenantListByDoco)) {
            return null;
        }
        Set<TenantRespDto> tenantRespDtoSet = tenantListByDoco.getData();
        if (CollectionUtils.isEmpty(tenantRespDtoSet)) {
            return null;
        }

        Map<String, Set<TenantRespDto>> docoTenantMap = tenantRespDtoSet.stream()
                .filter(r -> Objects.equals(r.getStatus(), TenantStatus.ENABLE))
                .filter(r -> CollectionUtils.isNotEmpty(r.getContracts()))
                .map(r -> r.getContracts().stream()
                        .filter(contract -> StringUtils.isNotEmpty(contract.getDoco()))
                        .map(TenantRespDto.Contract::getDoco).distinct()
                        .collect(Collectors.toMap(Function.identity(), s -> r)))
                .flatMap(m -> m.entrySet().stream())
                .collect(Collectors.toMap(Map.Entry<String, TenantRespDto>::getKey, v -> {
                    Set<TenantRespDto> tenantRespDtos = new HashSet<>();
                    tenantRespDtos.add(v.getValue());
                    return tenantRespDtos;
                }, (Set<TenantRespDto> old, Set<TenantRespDto> xin) -> {
                    old.addAll(xin);
                    return old;
                }));


        Map<String, List<String>> tenantDocoMap = new HashMap<>();
        QueryJdeBillSendPersonListRequest request = new QueryJdeBillSendPersonListRequest();
        for (DocoAn8 docoAn8 : docoAn8List) {
            try {
                String jdeDoco = docoAn8.getDoco();
                String an8 = docoAn8.getAn8();

                Set<TenantRespDto> tenantRespDtos = docoTenantMap.get(jdeDoco);
                if (CollectionUtils.isEmpty(tenantRespDtos)) {
                    log.error("No tenant found: {}", jdeDoco);
                    continue;
                }

                tenantRespDtos = tenantRespDtos.stream().filter(e -> e.getId() != null).collect(Collectors.toSet());
                log.info("tenant found. doco: {}, tenantIds: {}"
                        , jdeDoco, Arrays.toString(tenantRespDtos.stream().map(TenantRespDto::getId).toArray()));
                for (TenantRespDto tenantRespDto : tenantRespDtos) {
                    String tenantId = tenantRespDto.getId();
                    QueryJdeBillSendPersonRequest queryJdeBillSendPersonRequest = new QueryJdeBillSendPersonRequest();
                    queryJdeBillSendPersonRequest.setAn8(an8);
                    queryJdeBillSendPersonRequest.setTenantId(tenantId);
                    request.getList().add(queryJdeBillSendPersonRequest);
                    if (!tenantDocoMap.containsKey(tenantId)) {
                        tenantDocoMap.put(tenantId, new ArrayList<>());
                    }
                    tenantDocoMap.get(tenantId).add(jdeDoco);
                }
            } catch (Exception e) {
                log.error("Search bill configure failed.", e);
            }
        }
        if (CollectionUtils.isEmpty(request.getList())) {
            log.info("No receiver found.");
            return null;
        }

        //step4: 请求kerry-staff-service，根据租户ID和财务AN8地址，获取账单接收人
        log.info("tenant request: {}", JSONObject.toJSONString(request));
        RespWrapVo<List<TenantBillConfigResponse>> tbc = sUserClient.queryJdeNotifyPersons(request);
        if (!RespWrapVo.isResponseValid(tbc)) {
            log.error("s profile service does not work.");
            return null;
        }
        if (CollectionUtils.isEmpty(tbc.getData())) {
            log.info("no bill receiver found.");
            return null;
        }

        Map<DocoAn8, Set<BillSendReceiverBo>> resultMap = new HashMap<>();
        List<TenantBillConfigResponse> tenantBillConfigResponses = tbc.getData();
        for (TenantBillConfigResponse tenantBill : tenantBillConfigResponses) {
            log.info("tenantBill: {}", JSONObject.toJSONString(tenantBill));
            String tenantId = tenantBill.getTenantId();
            String an8 = tenantBill.getAddress();
            BillSendReceiverBo bo = BillSendReceiverBo.builder().tenantId(tenantId)
                    .phoneNumber(tenantBill.getPhoneNumber()).email(tenantBill.getEmail()).build();
            List<String> docos = tenantDocoMap.get(tenantId);
            log.info("found_tenant_doco_link, tenantId: {}, an8: {}, docos: {}"
                    , tenantId, an8, Arrays.toString(docos.toArray()));
            for (String doco : docos) {
                String[] an8s = StringUtils.split(an8, ",");
                for (String singleAn8 : an8s) {
                    DocoAn8 docoAn8 = DocoAn8.builder().doco(doco).an8(singleAn8).build();
                    if (!resultMap.containsKey(docoAn8)) {
                        resultMap.put(docoAn8, new HashSet<>());
                    }
                    resultMap.get(docoAn8).add(bo);
                }
            }
        }
        return resultMap;
    }*/

    private void batchUpdateBillSendConfigAccounts(List<BillSendConfigAccountBo> boList) {
        if (CollectionUtils.isEmpty(boList)) {
            log.info("empty BillSendConfigAccountBo list");
            return;
        }

        long startTime = System.currentTimeMillis();
        String sql = new StringBuffer()
                .append("update kerry_bill_send_config")
                .append(" set ")
                .append("login_no = ?, ")
                .append("tenant_manager_id = ?, ")
                .append("email_username = ? ")
                .append(" where id = ? ").toString();
        int[] updatedCountArray = jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {

            @Override
            public void setValues(PreparedStatement preparedStatement, int i) throws SQLException {
                BillSendConfigAccountBo accountBo = boList.get(i);

                int index = 1;
                preparedStatement.setString(index++, accountBo.getLoginNo());
                preparedStatement.setString(index++, accountBo.getTenantManagerId());
                preparedStatement.setString(index++, accountBo.getEmailUsername());
                preparedStatement.setLong(index++, accountBo.getConfigId());
            }

            @Override
            public int getBatchSize() {
                return boList.size();
            }
        });

        for (int i = 0; i < updatedCountArray.length; i++) {
            int result = updatedCountArray[i];
            BillSendConfigAccountBo accountBo = boList.get(i);
            if (result == Statement.EXECUTE_FAILED) {
                log.error("batch-update BillSendConfigAccountBo failed. [{}]", JSONObject.toJSONString(accountBo));
            } else {
                log.info("batch-update BillSendConfigAccountBo success. [{}]", JSONObject.toJSONString(accountBo));
            }
        }
        long endTime = System.currentTimeMillis();
        log.info("batchUpdateBillSendConfigAccounts. items={}, eclipsedTime={}", boList.size(), (endTime - startTime));
    }

}
