package com.kerryprops.kip.bill.service.model.s;

import com.google.common.collect.ImmutableList;
import com.kerryprops.kip.bill.common.enums.DirectDebitAgreementStatus;
import com.kerryprops.kip.bill.common.jpa.QueryFilter;
import com.kerryprops.kip.bill.dao.entity.QAptBillDirectDebitsAgreement;
import com.querydsl.core.types.Predicate;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Optional;

import static java.util.Optional.ofNullable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AptBillAgreementBo implements QueryFilter {

    /*private Date searchDateFrom;
    private Date searchDateTo;*/
    private String projectId;

    private List<String> buildingIds;

    private List<String> roomIds;

    private String pspName;

    private String agreementStatus;

    @Override
    public List<Optional<Predicate>> predicates() {
        ImmutableList.Builder<Optional<Predicate>> builder = ImmutableList.builder();
        builder
                .add(ofNullable(roomIds).map(QAptBillDirectDebitsAgreement.aptBillDirectDebitsAgreement.roomId::in))
                .add(ofNullable(projectId).map(QAptBillDirectDebitsAgreement.aptBillDirectDebitsAgreement.projectId::eq))
                .add(ofNullable(buildingIds).map(QAptBillDirectDebitsAgreement.aptBillDirectDebitsAgreement.buildingId::in))
                .add(ofNullable(pspName).map(QAptBillDirectDebitsAgreement.aptBillDirectDebitsAgreement.pspName::eq))
                .add(ofNullable(agreementStatus).map(DirectDebitAgreementStatus::valueOf)
                        .map(QAptBillDirectDebitsAgreement.aptBillDirectDebitsAgreement.agreementStatus::eq))
                /*.add(ofNullable(searchDateFrom)
                        .map(d -> ZonedDateTime.ofInstant(d.toInstant(), DEFAULT_ZONE_ID))
                        .map(QAptBillDirectDebitsAgreement.aptBillDirectDebitsAgreement.createdTime::goe))
                .add(ofNullable(searchDateTo)
                        .map(d -> ZonedDateTime.ofInstant(d.toInstant(), DEFAULT_ZONE_ID))
                        .map(QAptBillDirectDebitsAgreement.aptBillDirectDebitsAgreement.createdTime::loe))*/
        ;
        return builder.build();
    }

    /*public List<String> maxBindingScope() {
        LoginUser loginUser = UserInfoUtils.getUser();
        if (loginUser == null) {
            throw new RuntimeException("not login.");
        }
        if (Boolean.TRUE.equals(loginUser.isSuperAdmin())) {
            return null;
        }
        return loginUser.toBuildingIdList();
    }*/

}
