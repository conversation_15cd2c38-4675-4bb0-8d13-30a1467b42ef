package com.kerryprops.kip.bill.service.model.s;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * "RCCKNU," + -> 'bill_number' -> apt_offline_pay.pay_act
 * "RCLNID," + -> lineID -> i
 * "RCTYIN,"+ -> 'U' ->
 * "RCKCO," + -> ' ' ->
 * "RCDCT," + -> ' ' ->
 * "RCDOC," + -> 0 ->
 * "RCSFX," + -> ' ' ->
 * "RCAN8," + -> reg_code -> apt_bill.an8
 * "RCALPH,"+ -> ' ' ->
 * "RCDMTJ," + -> 'ruday' -> apt_offline_pay.pay_date -> var ruday = "1" + YY-years + DDD-days
 * "RCAG," + -> ag -> apt_offline_pay.total_amt - apt_offline_pay.tax_amt
 * "RCGLBA,"+ -> 'glba' -> apt_pay_config.bank_account
 * "RCCRCD," + -> 'CNY' ->
 * "RCGLC, + -> 'relation_type' -> ‘A003’
 * RCPO,"+ -> 'doco' -> apt_bill.doco
 * "RCMCU," + -> '    array[i].bu' -> apt_bill.bu
 * "RCUNIT," + -> 'array[i].unit_code' -> apt_bill.unit
 * "RCEDSP," + -> '0' ->
 * "RCRMK," + -> 'remarks' -> apt_offline_pay.pay_desc
 * "RCAAAJ" + -> shouxufee -> apt_offline_pay.tax_amt
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RemoteJdePaymentBo {

    private static final String BLANK = " ";

    private String RCCKNU;

    private Integer RCLNID;

    private String RCTYIN = "U";

    private String RCKCO = BLANK;

    private String RCDCT = BLANK;

    private Integer RCDOC = 0;

    private String RCSFX = BLANK;

    private Integer RCAN8;

    private String RCALPH = BLANK;

    private String RCDMTJ;

    private Double RCAG;

    private String RCGLBA;

    private String RCCRCD = "CNY";

    private String RCGLC = "A003";

    private String RCPO;

    private String RCMCU;

    private String RCUNIT;

    private String RCEDSP = "0";

    private String RCRMK;

    private Double RCAAAJ;

}
