package com.kerryprops.kip.bill.service.model;

import com.kerryprops.kip.bill.common.utils.CommonUtil;
import com.kerryprops.kip.bill.common.utils.DateUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

@Data
@ToString
@Slf4j
@AllArgsConstructor
public class AptPaymentTimestamp implements Comparable<AptPaymentTimestamp>, Cloneable {

    private long date;

    private long time;

    public AptPaymentTimestamp() {
        String str = DateUtils.convertDateToJuLian(new Date());
        this.date = CommonUtil.parseLongQuietly(str);
        this.time = 235959;
    }

    @Override
    public int compareTo(AptPaymentTimestamp o) {
        int cmp = 0;
        if (this == o) {
            cmp = 0;
        } else if (o == null) {
            cmp = 1;
        } else {
            cmp = Long.compare(this.date, o.date);
            cmp = cmp == 0 ? Long.compare(this.time, o.time) : cmp;
        }
        return cmp;
    }

    public void updateToLatest(final long date, final long time) {
        AptPaymentTimestamp timestamp = new AptPaymentTimestamp(date, time);

        if (this.compareTo(timestamp) < 0) {
            setDate(timestamp.getDate());
            setTime(timestamp.getTime());
        }
    }

    public void updateToEarlier(final AptPaymentTimestamp that) {
        if (that == null) {
            log.info("input timestamp is null");
            return;
        }

        if (this.compareTo(that) > 0) {
            setDate(that.getDate());
            setTime(that.getTime());
        }
    }

    public void updateToEarlier(final long date, final long time) {
        this.updateToEarlier(new AptPaymentTimestamp(date, time));
    }

    @Override
    public Object clone() {
        AptPaymentTimestamp timestamp = null;
        try {
            timestamp = (AptPaymentTimestamp) super.clone();
        } catch (CloneNotSupportedException e) {
            log.error("fail to clone AptPaymentTimestamp. ErrMsg={}", e.getMessage());
        }
        return timestamp;
    }

}
