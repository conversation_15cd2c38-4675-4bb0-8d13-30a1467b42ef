package com.kerryprops.kip.bill.service;

import com.kerryprops.kip.bill.dao.entity.EFapiaoBillInvoice;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffBillReceiverRespVo;

import java.util.Map;
import java.util.Set;

public interface EFapiaoSendService {

    void notifyByEmail(String projectId, StaffBillReceiverRespVo receiver
            , Set<EFapiaoBillInvoice> billFapiaos, Map<Long, String> batchNoMap);

    void notifyBySms(String projectId, StaffBillReceiverRespVo receiver
            , Set<EFapiaoBillInvoice> billFapiaos, Map<Long, String> batchNoMap);

    void notifyByMessage(String projectId, StaffBillReceiverRespVo receiver
            , Set<EFapiaoBillInvoice> billFapiaos, Map<Long, String> batchNoMap);

    Map<StaffBillReceiverRespVo, Set<EFapiaoBillInvoice>> invoiceReceivers(String projectId, Set<Long> billFapiaoIds);

    /**
     * 发送开票成功的站内信
     *
     * @param projectId
     * @param invoiceIds
     * @return 收件人数量
     */
    int sendInvoiceMessage(String projectId, Set<Long> invoiceIds);

}
