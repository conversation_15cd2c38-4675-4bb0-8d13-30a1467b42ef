package com.kerryprops.kip.bill.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.enums.InvoiceRecordStatus;
import com.kerryprops.kip.bill.common.enums.NotifyType;
import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.enums.SendStatus;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.config.MsgCallbackProperties;
import com.kerryprops.kip.bill.dao.EFapiaoBillInvoiceRepository;
import com.kerryprops.kip.bill.dao.EFapiaoSendRecordRepository;
import com.kerryprops.kip.bill.dao.entity.EFapiaoBillInvoice;
import com.kerryprops.kip.bill.dao.entity.EFapiaoSendRecord;
import com.kerryprops.kip.bill.feign.clients.MessageCenterClient;
import com.kerryprops.kip.bill.feign.clients.MessageClient;
import com.kerryprops.kip.bill.feign.entity.AttachmentDto;
import com.kerryprops.kip.bill.feign.entity.EmailReplyVo;
import com.kerryprops.kip.bill.feign.entity.EmailSendCommandV2;
import com.kerryprops.kip.bill.feign.entity.MessageDto;
import com.kerryprops.kip.bill.feign.entity.MessageType;
import com.kerryprops.kip.bill.feign.entity.NotificationPushRequest;
import com.kerryprops.kip.bill.feign.entity.SendSmsV2Command;
import com.kerryprops.kip.bill.feign.entity.SmsResultVo;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.EFapiaoSendService;
import com.kerryprops.kip.bill.service.IBillService;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffBillReceiverRespVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.common.utils.BillingFun.exceptionToNull;

@Slf4j
@Service
@AllArgsConstructor
public class EFapiaoSendServiceImpl implements EFapiaoSendService {

    private final IBillService billService;

    private final ObjectMapper objectMapper;

    private final TemplateEngine templateEngine;

    private final MessageClient messageClient;

    private final MessageCenterClient messageCenterClient;

    private final AptInvoiceSmsService aptInvoiceSmsService;

    private final MsgCallbackProperties msgCallbackProperties;

    private final EFapiaoSendRecordRepository fapiaoSendRecordRepository;

    private final EFapiaoBillInvoiceRepository eFapiaoBillInvoiceRepository;

    @Override
    public Map<StaffBillReceiverRespVo, Set<EFapiaoBillInvoice>> invoiceReceivers(String projectId
            , Set<Long> billFapiaoIds) {
        List<EFapiaoBillInvoice> billFapiaos = eFapiaoBillInvoiceRepository.findAllById(billFapiaoIds);
        billFapiaos = billFapiaos.stream().filter(f -> InvoiceRecordStatus.COMPLETED.equals(f.getInvoiceRecordStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(billFapiaos)) {
            return Collections.emptyMap();
        }
        Map<StaffBillReceiverRespVo, Set<EFapiaoBillInvoice>> receiverInvoiceMap = Maps.newLinkedHashMap();
        Set<StaffBillReceiverRespVo> userInfoList = null;
        Map<String, Set<StaffBillReceiverRespVo>> docoAn8Map = new HashMap<>();
        for (EFapiaoBillInvoice billFapiao : billFapiaos) {
            String key = billFapiao.getDoco() + "_" + billFapiao.getAn8();
            if (docoAn8Map.containsKey(key)) {
                userInfoList = docoAn8Map.get(key);
            } else {
                userInfoList = billService.queryBillReceiversByDocoAn8(billFapiao.getDoco(), billFapiao.getAn8());
                docoAn8Map.put(key, userInfoList);
            }
            if (CollectionUtils.isNotEmpty(userInfoList)) {
                userInfoList.forEach(ui -> {
                    if (!receiverInvoiceMap.containsKey(ui)) {
                        receiverInvoiceMap.put(ui, Sets.newLinkedHashSet());
                    }
                    receiverInvoiceMap.get(ui).add(billFapiao);
                });
            }
        }
        return receiverInvoiceMap;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void notifyByEmail(String projectId, StaffBillReceiverRespVo receiver
            , Set<EFapiaoBillInvoice> billFapiaos, Map<Long, String> batchNoMap) {
        billFapiaos = billFapiaos.stream().filter(f -> InvoiceRecordStatus.COMPLETED.equals(f.getInvoiceRecordStatus()))
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(billFapiaos)) {
            return;
        }
        EmailSendCommandV2 command = new EmailSendCommandV2();
        command.setSendTos(List.of(receiver.getEmail()));
        command.setSubject("嘉里建设的电子发票");
        command.setHtml(true);
        command.setBusinessType("BILL_INVOICE");
        command.setCallbackUrl(msgCallbackProperties.getEmail());
        command.setText(generateEmailContent(billFapiaos));
        command.setAttachments(generateEmailAttachments(billFapiaos));
        RespWrapVo<EmailReplyVo> respWrapVo = messageClient.sendEmailV2(command);
        log.info("send_invoice_email_response: {}", exceptionToNull(() -> objectMapper.writeValueAsString(respWrapVo)));
        if (respWrapVo == null
                || !StringUtils.equals(respWrapVo.getCode(), RespCodeEnum.SUCCESS.getCode())
                || respWrapVo.getData() == null
                || StringUtils.isEmpty(respWrapVo.getData().getRequestId())) {
            throw new RuntimeException("邮件服务不可用");
        }
        String requestId = respWrapVo.getData().getRequestId();
        List<EFapiaoSendRecord> newSendRecords = Lists.newLinkedList();
        for (EFapiaoBillInvoice billFapiao : billFapiaos) {
            EFapiaoSendRecord fapiaoSendRecord = createSendRecord(batchNoMap.get(billFapiao.getId())
                    , requestId, receiver, billFapiao.getId());
            fapiaoSendRecord.setNotifyType(NotifyType.EMAIL);
            fapiaoSendRecord.setSendStatus(SendStatus.MSG_SENDING);
            newSendRecords.add(fapiaoSendRecord);
        }
        eFapiaoBillInvoiceRepository.updateSendEmailStatusByIds(billFapiaos.stream()
                .map(EFapiaoBillInvoice::getId).collect(Collectors.toSet()), SendStatus.MSG_SENDING);
        fapiaoSendRecordRepository.saveAll(newSendRecords);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void notifyBySms(String projectId, StaffBillReceiverRespVo receiver
            , Set<EFapiaoBillInvoice> billFapiaos, Map<Long, String> batchNoMap) {
        billFapiaos = billFapiaos.stream().filter(f -> InvoiceRecordStatus.COMPLETED.equals(f.getInvoiceRecordStatus()))
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(billFapiaos)) {
            return;
        }
        List<EFapiaoSendRecord> newSendRecords = Lists.newLinkedList();
        Map<String, List<EFapiaoBillInvoice>> fapiaoGroups = billFapiaos.stream()
                .collect(Collectors.groupingBy(EFapiaoBillInvoice::getCompanyCode));
        for (Map.Entry<String, List<EFapiaoBillInvoice>> entry : fapiaoGroups.entrySet()) {
            List<EFapiaoBillInvoice> groupedInvoices = entry.getValue();
            SendSmsV2Command command = new SendSmsV2Command();
            command.setAssetType("RETAIL");
            command.setProjectId(projectId);
            List<String> phoneNumbers = List.of(receiver.getPhoneNumber());
            command.setPhoneNumbers(phoneNumbers);
            command.setSmsType("NOTIFICATION");
            String content = aptInvoiceSmsService.getContent(groupedInvoices, phoneNumbers, projectId);
            command.setContent(content);
            command.setCallbackUrl(msgCallbackProperties.getSms());
            SmsResultVo smsResultVo = messageClient.sendSmsV2(command);
            log.info("send_invoice_sms_response: {}"
                    , exceptionToNull(() -> objectMapper.writeValueAsString(smsResultVo)));
            Long requestId = smsResultVo.getRequestId();
            // construct send record
            groupedInvoices.forEach(billFapiao -> {
                EFapiaoSendRecord fapiaoSendRecord = createSendRecord(batchNoMap.get(billFapiao.getId())
                        , String.valueOf(requestId), receiver, billFapiao.getId());
                fapiaoSendRecord.setNotifyType(NotifyType.SMS);
                fapiaoSendRecord.setSendStatus(SendStatus.MSG_SENDING);
                newSendRecords.add(fapiaoSendRecord);
            });
        }
        // save db
        fapiaoSendRecordRepository.saveAll(newSendRecords);
        eFapiaoBillInvoiceRepository.updateSendSmsStatusByIds(billFapiaos.stream()
                .map(EFapiaoBillInvoice::getId).collect(Collectors.toSet()), SendStatus.MSG_SENDING);
    }

    @Override
    @Transactional
    public int sendInvoiceMessage(String projectId, Set<Long> invoiceIds) {
        eFapiaoBillInvoiceRepository.updateSendMessageStatusByIds(invoiceIds, SendStatus.MSG_SUCCESS);
        var receiverInvoiceMap = invoiceReceivers(projectId, invoiceIds);
        Map<Long, String> batchNoMap = Maps.newLinkedHashMap();
        receiverInvoiceMap.values().stream()
                .flatMap(Collection::stream)
                .forEach(bi -> batchNoMap.put(bi.getId(), String.valueOf(System.currentTimeMillis())));
        for (Map.Entry<StaffBillReceiverRespVo, Set<EFapiaoBillInvoice>> entry : receiverInvoiceMap.entrySet()) {
            StaffBillReceiverRespVo receiver = entry.getKey();
            if (StringUtils.isBlank(receiver.getUserId())) {
                continue;
            }
            notifyByMessage(projectId, receiver, entry.getValue(), batchNoMap);
        }
        return receiverInvoiceMap.size();
    }

    @Override
    public void notifyByMessage(String projectId, StaffBillReceiverRespVo receiver
            , Set<EFapiaoBillInvoice> billFapiaos, Map<Long, String> batchNoMap) {
        billFapiaos = billFapiaos.stream().filter(f -> InvoiceRecordStatus.COMPLETED.equals(f.getInvoiceRecordStatus()))
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(billFapiaos)) {
            return;
        }

        List<EFapiaoSendRecord> sendRecords = Lists.newLinkedList();
        // receivers
        for (EFapiaoBillInvoice billFapiao : billFapiaos) {
            String content = String.format("您有新的发票，发票金额：%s元，收款方：%s，付款方：%s，开票时间：%s "
                    , billFapiao.getAmountWithTax().setScale(2, RoundingMode.HALF_UP)
                    , billFapiao.getSellerName(), billFapiao.getPurchaserName()
                    , billFapiao.getUpdatedTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            MessageDto messageDto = new MessageDto();
            messageDto.setContent(content);
            messageDto.setMessageType(MessageType.BILL_INVOICE);
            messageDto.setToType("B");
            messageDto.setUserId(receiver.getUserId());
            messageDto.setParams(Map.of("id", billFapiao.getId()));
            var respWrapVo = messageCenterClient.sendMessage(messageDto);
            log.info("send_mcs_message_response: {}", exceptionToNull(() -> objectMapper.writeValueAsString(respWrapVo)));
            NotificationPushRequest pushRequest = NotificationPushRequest.of(messageDto, "您有新的发票");
            if (!pushRequest.getUserIds().isEmpty()) {
                messageClient.pushNotification(pushRequest);
            }

            EFapiaoSendRecord fapiaoSendRecord = createSendRecord(batchNoMap.get(billFapiao.getId())
                    , "", receiver, billFapiao.getId());
            fapiaoSendRecord.setNotifyType(NotifyType.MESSAGE);
            if (RespWrapVo.isResponseValidWithData(respWrapVo)) {
                fapiaoSendRecord.setSendStatus(SendStatus.MSG_SUCCESS);
                fapiaoSendRecord.setSendDesc("已发送");
            } else {
                fapiaoSendRecord.setSendStatus(SendStatus.MSG_FAILED);
                fapiaoSendRecord.setSendDesc("发送失败");
            }
            sendRecords.add(fapiaoSendRecord);
        }
        if (CollectionUtils.isNotEmpty(sendRecords)) {
            fapiaoSendRecordRepository.saveAll(sendRecords);
        }
    }

    private EFapiaoSendRecord createSendRecord(String batchNo, String requestId, StaffBillReceiverRespVo receiver
            , long billFapiaoId) {
        EFapiaoSendRecord fapiaoSendRecord = new EFapiaoSendRecord();
        fapiaoSendRecord.setRequestId(requestId);
        fapiaoSendRecord.setBatchNo(batchNo);
        fapiaoSendRecord.setSendDesc("发送中");
        fapiaoSendRecord.setEmail(Optional.ofNullable(receiver.getEmail()).orElse(Strings.EMPTY));
        fapiaoSendRecord.setUserId(Optional.ofNullable(receiver.getUserId()).orElse(Strings.EMPTY));
        fapiaoSendRecord.setUserName(Optional.ofNullable(receiver.getUserName()).orElse(Strings.EMPTY));
        fapiaoSendRecord.setSms(Optional.ofNullable(receiver.getPhoneNumber()).orElse(Strings.EMPTY));
        fapiaoSendRecord.setSendTime(ZonedDateTime.now());
        fapiaoSendRecord.setBillFapiaoId(billFapiaoId);
        LoginUser loginUser = UserInfoUtils.getUser();
        if (Objects.nonNull(loginUser)) {
            fapiaoSendRecord.setOperateUserId(String.valueOf(loginUser.getUserId()));
            fapiaoSendRecord.setOperateUserEmail(loginUser.getLoginAccount());
        } else {
            fapiaoSendRecord.setOperateUserId(Strings.EMPTY);
            fapiaoSendRecord.setOperateUserEmail(Strings.EMPTY);
        }
        return fapiaoSendRecord;
    }

    private String generateEmailContent(Collection<EFapiaoBillInvoice> invoices) {
        Context context = new Context();
        context.setVariable("invoices", invoices);
        return templateEngine.process("bill_invoice", context);
    }

    private List<AttachmentDto> generateEmailAttachments(Collection<EFapiaoBillInvoice> invoices) {
        return invoices.stream()
                .filter(billFapiao -> StringUtils.isNotEmpty(billFapiao.getPdfUrl()))
                .map(billFapiao -> new AttachmentDto(
                        "invoice_" + billFapiao.getInvoiceCode() + '_' + billFapiao.getInvoiceNo() + ".pdf",
                        billFapiao.getPdfUrl()))
                .toList();
    }

}
