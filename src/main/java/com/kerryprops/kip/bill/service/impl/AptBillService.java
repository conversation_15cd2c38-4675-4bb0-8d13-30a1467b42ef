package com.kerryprops.kip.bill.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.kerryprops.kip.bill.common.aop.RedisLock;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillPushStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.common.utils.BuConverter;
import com.kerryprops.kip.bill.common.utils.jde.OracleJdbc;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.config.DataMigrationConfig;
import com.kerryprops.kip.bill.config.SyncJdeConfig;
import com.kerryprops.kip.bill.dao.AptBillLinkRepository;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptJdeBillRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptBillLink;
import com.kerryprops.kip.bill.dao.entity.AptJdeBill;
import com.kerryprops.kip.bill.dao.entity.QAptBill;
import com.kerryprops.kip.bill.dao.entity.QAptJdeBill;
import com.kerryprops.kip.bill.feign.clients.CUserClient;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.clients.MessageClient;
import com.kerryprops.kip.bill.feign.entity.EmailSendCommand;
import com.kerryprops.kip.bill.feign.entity.HiveRoomRequest;
import com.kerryprops.kip.bill.feign.entity.TenantStaffResource;
import com.kerryprops.kip.bill.feign.entity.UserWithRoomsResponse;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.log4j.BSConversationFilter;
import com.kerryprops.kip.bill.utils.BillUtil;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import com.kerryprops.kip.hiveas.feign.dto.resp.RoomRespDto;
import com.kerryprops.kip.hiveas.webservice.resource.resp.BuildingSimple;
import com.kerryprops.kip.hiveas.webservice.resource.resp.FloorSimple;
import com.kerryprops.kip.hiveas.webservice.resource.resp.ProjectSimple;
import com.kerryprops.kip.hiveas.webservice.resource.resp.RoomResp;
import com.kerryprops.kip.hiveas.webservice.vo.resp.ProjectBuildingVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.JdbcUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.BatchUpdateException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Spliterators;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static com.kerryprops.kip.bill.common.constants.AppConstants.ALERT_MAIL_RECEIVERS;
import static com.kerryprops.kip.bill.common.utils.BillingFun.exceptionToNull;

/**
 * apartment电子账单Service业务层处理
 *
 * <AUTHOR>
 * @date 2020-10-15
 */
@Slf4j
@Service
public class AptBillService {

    private static final String JDE_SYNC_FLAG_RDEV01 = "Y";

    private static final int JDE_SYNC_BATCH_SIZE = 1000;

    @Autowired
    private CUserClient cUserClient;

    @Autowired
    private HiveAsClient hiveAsClient;

    @Autowired
    private DataMigrationConfig dataMigrationConfig;

    @Autowired
    private AptJdeBillRepository jdeBillRepository;

    @Autowired
    private AptBillRepository billRepository;

    @Autowired
    private AptBillLinkRepository billLinkRepository;

    @Autowired
    private MessageClient messageClient;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Value("${env}")
    private String env;

    @Transactional
    @Async
    @RedisLock(key = "kip:billing:s:syncHive", expire = 30L)
    public void syncHive() {
        Iterable<AptBill> aptBillIterable = billRepository.findAll(QAptBill.aptBill.projectId.isNull().or(QAptBill.aptBill.projectId.isEmpty()));
        List<AptBill> aptBills = Lists.newArrayList(aptBillIterable.iterator());
        Map<String, List<AptBill>> billMap = aptBills.stream().collect(Collectors.groupingBy(AptBill::key));

        List<AptBill> list = new LinkedList<>();
        for (List<AptBill> bills : billMap.values()) {
            AptBill aBill = bills.get(0);
            String bu = aBill.getBu();
            String unit = aBill.getUnit();
            if (StringUtils.isEmpty(bu) || StringUtils.isEmpty(unit)) {
                log.info("bu or unit is null. AptBill[Id={}, billNo={}, bu={}, unit={}]",
                        aBill.getId(), aBill.getBillNo(), bu, unit);
                continue;
            }
            list.addAll(bills);
        }
        populateHiveInfo(list);
    }

    @Transactional
    @Async
    @RedisLock(key = "kip:billing:s:syncjdebill", expire = 30L, suffixArgIndexArray = {0})
    public void syncAptBill(String projectId) {
        List<String> projectIds = StringUtils.isEmpty(projectId) ? null : List.of(projectId);
        List<String> hiveBuList = getHiveBuList(projectIds);
        if (CollectionUtils.isEmpty(hiveBuList)) {
            log.info("quit syncing JDE Bills because hiveBu is empty");
            return;
        }

        List<AptJdeBill> jdeBills = pullBillFromJde(hiveBuList);
        if (CollectionUtils.isNotEmpty(jdeBills)) {
            List<AptBill> aptBills = handleNewJdeBills(jdeBills);
            populateHiveInfo(aptBills);
        } else {
            log.info("no more c-bill need to be synced");
        }

        long startTime = System.currentTimeMillis();
        Iterable<AptJdeBill> aptJdeBillIterable = jdeBillRepository.findAll(
                QAptJdeBill.aptJdeBill.rdMcu.in(hiveBuList)
                        .and(QAptJdeBill.aptJdeBill.rdEv01.notEqualsIgnoreCase(JDE_SYNC_FLAG_RDEV01)));
        List<AptJdeBill> aptJdeBills = Lists.newArrayList(aptJdeBillIterable);
        if (CollectionUtils.isNotEmpty(aptJdeBills)) {
            writeBackJDESyncFlag(aptJdeBills);
        } else {
            log.info("no more c-bill need to sync flag");
        }
        long endTime = System.currentTimeMillis();
        log.info("{}sync jde flag = {} millis", Thread.currentThread(), endTime - startTime);
    }

    public void updateBillStatus4Push(long id, BillPushStatus pushStatus, BillStatus billStatus) {
        log.info("id : {} || pushStatus : {} || billStatus : {}", id, pushStatus, billStatus);
        if (!billRepository.updateBillStatus4Push(id, pushStatus, billStatus)) {
            log.info("update bill[{}] failed.", id);
        }
    }

    public AptBill saveOrUpdateBill(AptBill aptBill) {
        aptBill = billRepository.save(aptBill);
        log.info("save AptBill. [{}]", JSON.toJSONString(aptBill));
        return aptBill;
    }

    /**
     * 从hive那边获取所有楼盘下的楼栋的所有BUs
     */
    public String buildHiveBuString(List<String> projectIds) {
        List<String> hiveBuList = getHiveBuList(projectIds);
        if (CollectionUtils.isEmpty(hiveBuList)) {
            log.info("no hive bu is found");
            return null;
        }

        List<String> jdeBuList = new ArrayList<>();
        hiveBuList.forEach(e -> jdeBuList.add("'    " + e + "'"));
        return StringUtils.join(jdeBuList, ",");
    }

    public List<AptBill> queryAptBillsByBillNos(Collection<String> billNos) {
        // 兼容counter cashier项目
        Iterable<AptBill> aptBillIterable = billRepository.findAll(QAptBill.aptBill.billNo.in(billNos)
                // .and(QAptBill.aptBill.roomId.in(populateRoomIds()))
                .and(QAptBill.aptBill.paymentStatus.eq(BillPaymentStatus.TO_BE_PAID))
                .and(QAptBill.aptBill.deletedAt.eq(0)));
        return Lists.newArrayList(aptBillIterable.iterator());
    }

    public List<AptBill> queryAptBillsByBillNos(Collection<String> billNos, BillPaymentStatus status) {
        var aptBillIterable = billRepository.findAll(QAptBill.aptBill.billNo.in(billNos)
                .and(QAptBill.aptBill.paymentStatus.eq(status))
                .and(QAptBill.aptBill.deletedAt.eq(0)));
        return Lists.newArrayList(aptBillIterable.iterator());
    }

    public AptBill getBillById(Long aptBillId) {
        if (Objects.isNull(aptBillId)) {
            log.error("get_apt_bill_by_id id_is_null");
            return null;
        }

        return billRepository.findById(aptBillId).orElse(null);
    }

    private List<AptBill> handleNewJdeBills(List<AptJdeBill> jdeBills) {
        //declare return value
        long startTime = System.currentTimeMillis();
        List<AptBill> aptBills = new LinkedList<>();

        Map<String, List<AptJdeBill>> aptJdeBillMap = jdeBills.stream().collect(Collectors.groupingBy(AptJdeBill::groupByBill));
        for (Map.Entry<String, List<AptJdeBill>> entrySet : aptJdeBillMap.entrySet()) {
            if (null == entrySet) {
                log.info("Jde billing entrySet is null");
                continue;
            }

            String billNo;
            String tempBillNo = entrySet.getKey();
            if (StringUtils.isEmpty(tempBillNo) || tempBillNo.split("_").length < 4) {
                billNo = getNextBillNo();
            } else {
                String[] rdInfo = tempBillNo.split("_");
                billNo = rdInfo[0] + rdInfo[2] + rdInfo[1] + rdInfo[3];
            }

            List<AptJdeBill> groupedBills = entrySet.getValue();
            try {
                groupedBills = jdeBillRepository.saveAll(groupedBills);
            } catch (Exception e) {
                EmailSendCommand command = new EmailSendCommand();
                command.setSubject(env + ": sync c-bill from jde failed: Save grouped bill failed");
                command.setSendTos(ALERT_MAIL_RECEIVERS);
                command.setText(ExceptionUtils.getStackTrace(e) + "\r" + billNo);
                messageClient.sendWithReplyAlicloud(command);
                continue;
            }

            try {
                double totalAmt = groupedBills.stream().filter(e -> e.getRdAg() != null)
                        .mapToDouble(AptJdeBill::getRdAg).sum();
                BigDecimal bigDecimal = new BigDecimal(totalAmt)
                        .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                totalAmt = bigDecimal.doubleValue();
                AptJdeBill aBill = groupedBills.get(0);
                String bu = aBill.getRdMcu();
                String unit = aBill.getRdUnit();
                if (StringUtils.isEmpty(bu) || StringUtils.isEmpty(unit)) {
                    throw new RuntimeException("bu or unit empty.");
                }
                AptBill aptBill = AptBill.builder()
                        .billNo(billNo)
                        .category(aBill.getRdDl01())
                        .bu(bu)
                        .unit(unit)
                        .an8(aBill.getRdAn8())
                        .alph(aBill.getRdAlph())
                        .doco(aBill.getRdDoco())
                        .rdGlc(aBill.getRdGlc())
                        .beginDate(convertDate(aBill.getRdDate01()))
                        .endDate(convertDate(aBill.getRdDate02()))
                        .year(convertYear(aBill.getRdDate01()))
                        .month(convertMonth(aBill.getRdDate01()))
                        .amt(totalAmt)
                        .status(BillStatus.INITIALED)
                        .paymentStatus(BillPaymentStatus.TO_BE_PAID)
                        .pushStatus(BillPushStatus.TO_BE_PUSHED)
                        .rdGlc(aBill.getRdGlc())
                        .build();
                if (aptBill.getYear() != null) {
                    aptBill.setBillMonth(aptBill.getYear() * 100 + aptBill.getMonth());
                }

                //update return value
                aptBills.add(aptBill);

                aptBill = saveOrUpdateBill(aptBill);

                List<AptBillLink> aptBillLinks = new ArrayList<>(groupedBills.size());
                groupedBills.forEach(e -> {
                    aptBillLinks.add(AptBillLink.builder()
                            .jdeBillId(e.getId())
                            .billNo(billNo)
                            .build());
                });

                billLinkRepository.saveAll(aptBillLinks);

                aptBill.setStatus(BillStatus.TO_BE_SENT);
                saveOrUpdateBill(aptBill);

                groupedBills.forEach(e -> {
                    e.setBillNumber(billNo);
                    jdeBillRepository.save(e);
                });
            } catch (Exception e) {
                log.error("wrap_jde_bill_failed, bill_no=[{}].", billNo, e);

                if (Boolean.FALSE.equals(e instanceof NumberFormatException)) {
                    EmailSendCommand command = new EmailSendCommand();
                    command.setSubject(env + " sync c-bill from jde failed: Save bill failed");
                    command.setSendTos(ALERT_MAIL_RECEIVERS);
                    command.setText(ExceptionUtils.getStackTrace(e) + "\r" + billNo
                            + "\r" + BSConversationFilter.getCorrelationId());
                    exceptionToNull(() -> messageClient.sendWithReplyAlicloud(command));
                }

                try {
                    jdeBillRepository.deleteAll(groupedBills);
                } catch (Exception err) {
                    log.error("delete_error_jde_bills_failed: {}", JSONObject.toJSONString(groupedBills), err);
                }
            }
        }
        long endTime = System.currentTimeMillis();
        log.info("parse AptJdeBill, items={}, eclipsedTime={}", jdeBills.size(), (endTime - startTime));

        return aptBills;
    }

    private void writeBackJDESyncFlag(List<AptJdeBill> aptJdeBills) {
        //declare variables
        Connection conn = null;
        PreparedStatement ptst = null;

        try {
            long startTime = System.currentTimeMillis();
            conn = OracleJdbc.getOracleConnection();
            conn.setAutoCommit(false);

            String sql = "update "
                    + SyncJdeConfig.getJdeDbPrefix() + ".F560311 set RDEV01 = 'Y' "
                    + "where RDDOC = ? and RDDCT = ? and RDKCO = ? and RDSFX = ? ";
            ptst = conn.prepareStatement(sql);

            int total = aptJdeBills.size();
            int batch = total / JDE_SYNC_BATCH_SIZE;
            batch = total % JDE_SYNC_BATCH_SIZE == 0 ? batch : batch + 1;
            for (int i = 0; i < batch; i++) {
                int fromIndex = JDE_SYNC_BATCH_SIZE * i;
                int toIndex = JDE_SYNC_BATCH_SIZE * i + JDE_SYNC_BATCH_SIZE;
                toIndex = toIndex >= total ? total : toIndex;

                List<AptJdeBill> bills = aptJdeBills.subList(fromIndex, toIndex);
                syncJDEFlagInOneBatch(bills, i + 1, ptst);
            }

            //submit connection
            conn.commit();

            //write flag into local database
            List<AptJdeBill> syncBills = aptJdeBills.stream()
                    .filter(bill -> JDE_SYNC_FLAG_RDEV01.equals(bill.getRdEv01()))
                    .collect(Collectors.toList());
            List<String> syncBillNumbers = syncBills.stream()
                    .map(AptJdeBill::getBillNumber).collect(Collectors.toList());
            log.info("syncBillNumbers = {}", syncBillNumbers);

            batchWriteBackSyncFlag(syncBills);

            long endTime = System.currentTimeMillis();
            log.info("sync flag. the size of AptJdeBills={}, eclipsedTime={}",
                    aptJdeBills.size(), (endTime - startTime));
        } catch (BatchUpdateException e) {
            log.error("batch update JDE flag failed. ErrMsg={}", e.getMessage());
            try {
                conn.rollback();
            } catch (SQLException ex) {
                log.error("run rollback failed. ErrMsg={}", e.getMessage());
            }

        } catch (SQLException e) {
            log.error("execute sql failed. ErrMsg={}", e.getMessage());
        } finally {
            JdbcUtils.closeStatement(ptst);
            JdbcUtils.closeConnection(conn);
        }
    }

    private void syncJDEFlagInOneBatch(List<AptJdeBill> aptJdeBills, int batch, PreparedStatement ptst)
            throws SQLException {
        for (int i = 0; i < aptJdeBills.size(); i++) {
            AptJdeBill aptJdeBill = aptJdeBills.get(i);

            int index = 1;
            ptst.setLong(index++, aptJdeBill.getRdDoc());
            ptst.setString(index++, aptJdeBill.getRdDct());
            ptst.setString(index++, aptJdeBill.getRdKco());
            ptst.setString(index++, aptJdeBill.getRdSfx());
            ptst.addBatch();
        }

        //execute a batch of sql
        int[] updates = ptst.executeBatch();

        //update local data according to update result
        for (int j = 0; j < updates.length; j++) {
            //successful entry1: the number represents number of affected row
            //successful entry2: the number of affected rows is not available
            if (updates[j] >= 0 || updates[j] == Statement.SUCCESS_NO_INFO) {
                AptJdeBill aptJdeBill = aptJdeBills.get(j);
                aptJdeBill.setRdEv01(JDE_SYNC_FLAG_RDEV01);
            }
        }

        //clear batch result
        ptst.clearBatch();
    }

    private void populateHiveInfo(List<AptBill> aptBills) {
        //check param
        if (CollectionUtils.isEmpty(aptBills)) {
            log.info("no aptBills to populate hive info");
            return;
        }

        //prepare params
        long startTime = System.currentTimeMillis();
        List<HiveRoomRequest> requests = new LinkedList<>();
        Map<String, List<AptBill>> buMap = aptBills.stream().collect(Collectors.groupingBy(AptBill::getBu));
        buMap.forEach((key, value) -> {
            Map<String, List<AptBill>> unitMap = value.stream().collect(Collectors.groupingBy(AptBill::getUnit));
            List<String> jdeRoomNos = new LinkedList<>(unitMap.keySet());

            HiveRoomRequest request = HiveRoomRequest.builder().bu(key).jdeRoomNos(jdeRoomNos).build();
            requests.add(request);
        });

        //query hive-vas service
        Map<String, List<RoomResp>> roomMap = getRooms(requests);
        if (roomMap == null || roomMap.isEmpty()) {
            log.info("No hive value found");
            return;
        }

        //handle response
        List<AptBill> aptBillList = new LinkedList<>();
        roomMap.forEach((bu, roomRespList) -> {
            if (CollectionUtils.isEmpty(roomRespList)) {
                log.info("No hive is found for the current bu:{}", bu);
                return;
            }

            for (RoomResp roomResp : roomRespList) {
                ProjectSimple project = roomResp.getProject();
                BuildingSimple building = roomResp.getBuilding();
                FloorSimple floor = roomResp.getFloor();
                RoomRespDto room = roomResp.getRoom();

                //query AptBill
                String unit = room.getJdeRoomNo();
                List<AptBill> bills = aptBills.stream()
                        .filter(e -> e.getBu().equalsIgnoreCase(bu) && e.getUnit().equalsIgnoreCase(unit))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(bills)) {
                    continue;
                }

                bills.forEach(aptBill -> {
                    //populate position info
                    PositionItemResponse position = PositionItemResponse.builder()
                            .projectName(project.getName())
                            .buildingName(building.getName())
                            .floorName(floor.getName())
                            .roomName(room.getRoomNo())
                            .build();

                    aptBill.setProjectId(project.getId());
                    aptBill.setBuildingId(building.getId());
                    aptBill.setFloorId(floor.getId());
                    aptBill.setRoomId(room.getId());
                    aptBill.setPositionItem(position);
                });
                aptBillList.addAll(bills);
            }
        });
        batchUpdateAptBills(aptBillList);

        long endTime = System.currentTimeMillis();
        log.info("populate billItems={}, eclipsed time={}", aptBills.size(), endTime - startTime);
    }

    private Map<String, List<RoomResp>> getRooms(List<HiveRoomRequest> hiveRoomRequests) {
        if (CollectionUtils.isEmpty(hiveRoomRequests)) {
            log.info("no room is found because hiveRoomRequests is empty");
            return null;
        }

        Map<String, List<RoomResp>> map = new LinkedHashMap<>();
        hiveRoomRequests.forEach(request -> {
            String bu = request.getBu();
            List<String> jdeRoomNos = request.getJdeRoomNos();
            List<RoomResp> roomRespList = new LinkedList<>();

            final int ROOM_LIMIT = 100;
            int total = jdeRoomNos.size();
            int batch = total / ROOM_LIMIT;
            batch = total % ROOM_LIMIT == 0 ? batch : batch + 1;
            for (int i = 0; i < batch; i++) {
                int fromIndex = ROOM_LIMIT * i;
                int toIndex = ROOM_LIMIT * i + ROOM_LIMIT;
                toIndex = Math.min(toIndex, total);
                List<String> rooms = jdeRoomNos.subList(fromIndex, toIndex);

                List<RoomResp> list = hiveAsClient.getBatchRoom(request.getBu(), rooms);
                if (CollectionUtils.isNotEmpty(list)) {
                    roomRespList.addAll(list);
                }
            }
            map.put(bu, roomRespList);
        });
        return map;
    }

    private void batchUpdateAptBills(List<AptBill> aptBills) {
        if (CollectionUtils.isEmpty(aptBills)) {
            log.info("no AptBill(s) to update");
            return;
        }

        try {
            batchUpdateHiveInfo(aptBills);

        } catch (Exception e) {
            log.error("install bill hive failed. error:{}", e.getMessage());
        }
    }

    private Date convertDate(String jdeDate) {
        if (StringUtils.isEmpty(jdeDate)) {
            return null;
        }
        Calendar c = toCalendar(jdeDate);
        return c.getTime();
    }

    private Integer convertYear(String jdeDate) {
        if (StringUtils.isEmpty(jdeDate)) {
            return null;
        }
        Calendar c = toCalendar(jdeDate);
        return c.get(Calendar.YEAR);
    }

    private Integer convertMonth(String jdeDate) {
        if (StringUtils.isEmpty(jdeDate)) {
            return null;
        }
        Calendar c = toCalendar(jdeDate);
        return c.get(Calendar.MONTH) + 1;
    }

    private Calendar toCalendar(String jdeDate) {
        String year = StringUtils.substring(jdeDate, 1, 3);
        String dayOfYear = StringUtils.substring(jdeDate, 3);
        Calendar c = Calendar.getInstance();
        c.set(Calendar.YEAR, Integer.parseInt("20" + year));
        c.set(Calendar.DAY_OF_YEAR, Integer.parseInt(dayOfYear));
        return c;
    }

    private String getNextBillNo() {
        return BillUtil.createBillNo();
    }

    private List<AptJdeBill> pullBillFromJde(List<String> hiveBuList) {
        List<String> jdeBuList = new ArrayList<>();
        hiveBuList.forEach(e -> jdeBuList.add("'    " + e + "'"));
        String jdeBuString = StringUtils.join(jdeBuList, ",");

        String bill = "select * from %s.F560311 WHERE RDEV01 != 'Y' and RDMCU in (%s)";
        String db = SyncJdeConfig.getJdeDbPrefix();
        String selectSql = String.format(bill, db, jdeBuString);
        log.info("select sql: {}", selectSql);

        List<AptJdeBill> billList = new ArrayList<>();
        try (Connection conn = OracleJdbc.getOracleConnection();
             Statement statement = conn.createStatement();
             ResultSet rs = statement.executeQuery(selectSql)
        ) {
            while (rs != null && rs.next()) {
                billList.add(populateBill(rs));
            }
        } catch (Exception e) {
            throw new RuntimeException("fetch jde bill failed.", e);
        }
        return billList;
    }

    private List<String> getHiveBuList(List<String> projectIds) {
        if (CollectionUtils.isEmpty(projectIds)) {
            String configuredProjectStr = dataMigrationConfig.getCBillProjects();
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(configuredProjectStr)) {
                projectIds = Arrays.stream(org.apache.commons.lang3.StringUtils
                        .split(configuredProjectStr, ",")).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(projectIds)) {
                return null;
            }
        }
        String[] projectIdArr = new String[projectIds.size()];
        projectIdArr = projectIds.toArray(projectIdArr);
        List<ProjectBuildingVO> clist = hiveAsClient.getCenterByIds(projectIdArr);
        if (CollectionUtils.isEmpty(clist)) {
            log.info("hive data unavailable.");
            return null;
        }
        List<String> bus = Lists.newLinkedList();
        clist.stream()
                .map(ProjectBuildingVO::getBuildings)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .forEach(e -> {
                    String mgrBu = e.getPropertyManagementBU();
                    if (StringUtils.isNotEmpty(mgrBu)) {
                        bus.addAll(BuConverter.getBuList(mgrBu));
                    }
                    String devBu = e.getPropertyDeveloperBU();
                    if (StringUtils.isNotEmpty(devBu)) {
                        bus.addAll(BuConverter.getBuList(devBu));
                    }
                });
        return bus;
    }

    private void saveBills(List<AptJdeBill> bills) {
        if (CollectionUtils.isEmpty(bills)) {
            return;
        }
        Iterable<AptJdeBill> billEntityIterable = StreamSupport.stream(Spliterators.spliteratorUnknownSize(bills.iterator(), 0), false).collect(Collectors.toList());
        jdeBillRepository.saveAll(billEntityIterable);
        bills.clear();
    }

    //--------------------------------------------------

    private AptJdeBill populateBill(ResultSet rs) throws Exception {
        Date now = new Date();
        int index = 1;
        return AptJdeBill.builder()
                .rdEdbt(StringUtils.trim(rs.getString(index++)))
                .rdDoc(rs.getLong(index++))
                .rdDct(StringUtils.trim(rs.getString(index++)))
                .rdKco(StringUtils.trim(rs.getString(index++)))
                .rdSfx(StringUtils.trim(rs.getString(index++)))
                .rdGlc(StringUtils.trim(rs.getString(index++)))
                .rdDl01(StringUtils.trim(rs.getString(index++)))
                .rdDl02(StringUtils.trim(rs.getString(index++)))
                .rdDate01(StringUtils.trim(rs.getString(index++)))
                .rdDate02(StringUtils.trim(rs.getString(index++)))
                .rdAg(rs.getDouble(index++))
                .rdMcu(StringUtils.trim(rs.getString(index++)))
                .rdDl03(StringUtils.trim(rs.getString(index++)))
                .rdUnit(StringUtils.trim(rs.getString(index++)))
                .rdAn8(StringUtils.trim(rs.getString(index++)))
                .rdAlph(StringUtils.trim(rs.getString(index++)))
                .rdDoco(StringUtils.trim(rs.getString(index++)))
                .rdEdsp(StringUtils.trim(rs.getString(index++)))
                .rdUds1(StringUtils.trim(rs.getString(index++)))
                .rdUds2(StringUtils.trim(rs.getString(index++)))
                .rdUds3(StringUtils.trim(rs.getString(index++)))
                .rdEv01(StringUtils.trim(rs.getString(index++)))
                .rdEv02(StringUtils.trim(rs.getString(index++)))
                .rdEv03(StringUtils.trim(rs.getString(index++)))
                .rdUser(StringUtils.trim(rs.getString(index++)))
                .rdUpmj(rs.getLong(index++))
                .rdUpmt(StringUtils.trim(rs.getString(index++)))
                .rdPid(StringUtils.trim(rs.getString(index++)))
                .rdJobn(StringUtils.trim(rs.getString(index++)))
                .createTime(now)
                .updateTime(now)
                .build();
    }

    /*private List<String> populateRoomIds() {
        List<String> roomIds = null;
        try {
            LoginUser loginUser = UserInfoUtils.getUser();
            RespWrapVo<UserWithRoomsResponse> userWithRoomsResponseRespWrapVo
                    = cUserClient.userAssociateRooms(loginUser.getUniqueUserId());
            List<TenantStaffResource> tenantStaffResources = Optional.ofNullable(userWithRoomsResponseRespWrapVo)
                    .map(RespWrapVo::getData)
                    .map(UserWithRoomsResponse::getRooms)
                    .orElse(Collections.emptyList());
            roomIds = tenantStaffResources
                    .stream()
                    .map(TenantStaffResource::getRoomNumber)
                    .collect(Collectors.toUnmodifiableList());
        } catch (Exception e) {
            log.error("query room user failed.", e);
        }
        if (CollectionUtils.isEmpty(roomIds)) {
            throw new RuntimeException("room user not found");
        }
        return roomIds;
    }*/

    private void batchWriteBackSyncFlag(List<AptJdeBill> aptJdeBills) {
        if (CollectionUtils.isEmpty(aptJdeBills)) {
            log.info("no AptJdeBill to update RDEV01 field");
            return;
        }

        long startTime = System.currentTimeMillis();
        final String sql = "update apt_sync_bills set rd_ev01 = 'Y' where id = ? ";
        int[] updatedCountArray = jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {

            @Override
            public void setValues(PreparedStatement preparedStatement, int i) throws SQLException {
                AptJdeBill aptJdeBill = aptJdeBills.get(i);
                preparedStatement.setLong(1, aptJdeBill.getId());
            }

            @Override
            public int getBatchSize() {
                return aptJdeBills.size();
            }
        });

        for (int i = 0; i < updatedCountArray.length; i++) {
            int result = updatedCountArray[i];
            AptJdeBill aptJdeBill = aptJdeBills.get(i);
            if (result == Statement.EXECUTE_FAILED) {
                log.error("fail to update AptJdeBill [billNo={}]", aptJdeBill.getBillNumber());
            }
        }

        long endTime = System.currentTimeMillis();
        log.info("batch sync flag into KIP. items={}, eclipsedTime={}", aptJdeBills.size(), (endTime - startTime));
    }

    private void batchUpdateHiveInfo(List<AptBill> aptBills) {
        if (CollectionUtils.isEmpty(aptBills)) {
            log.info("no AptBill to update hive info");
            return;
        }

        long startTime = System.currentTimeMillis();
        String sql = new StringBuffer()
                .append("update apt_bill")
                .append(" set ")
                .append("project_id = ?, ")
                .append("building_id = ?, ")
                .append("floor_id = ?, ")
                .append("room_id = ?, ")
                .append("position_item = ? ")
                .append(" where id = ? ").toString();
        int[] updatedCountArray = jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {

            @Override
            public void setValues(PreparedStatement preparedStatement, int i) throws SQLException {
                AptBill aptBill = aptBills.get(i);

                int index = 1;
                preparedStatement.setString(index++, aptBill.getProjectId());
                preparedStatement.setString(index++, aptBill.getBuildingId());
                preparedStatement.setString(index++, aptBill.getFloorId());
                preparedStatement.setString(index++, aptBill.getRoomId());
                preparedStatement.setString(index++, JSONObject.toJSONString(aptBill.getPositionItem()));
                preparedStatement.setLong(index++, aptBill.getId());
            }

            @Override
            public int getBatchSize() {
                return aptBills.size();
            }
        });

        for (int i = 0; i < updatedCountArray.length; i++) {
            int result = updatedCountArray[i];
            AptBill aptBill = aptBills.get(i);
            if (result == Statement.EXECUTE_FAILED) {
                log.error("batch-update AptBill failed. [{}]", JSONObject.toJSONString(aptBill));
            } else {
                log.info("batch-update AptBill success. [{}]", JSONObject.toJSONString(aptBill));
            }
        }
        long endTime = System.currentTimeMillis();
        log.info("batchUpdateHiveInfo. items={}, eclipsedTime={}", aptBills.size(), (endTime - startTime));
    }

}
