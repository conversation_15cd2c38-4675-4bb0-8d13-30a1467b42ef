package com.kerryprops.kip.bill.service.model.leg;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.ImmutableList;
import com.kerryprops.kip.bill.common.enums.SendStatus;
import com.kerryprops.kip.bill.common.jpa.QueryFilter;
import com.kerryprops.kip.bill.dao.entity.QBillEntity;
import com.kerryprops.kip.bill.service.model.SysUser;
import com.querydsl.core.types.Predicate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 账单查询的 view
 */
@Data
public class BillSelectVo extends BaseEntity implements QueryFilter {

    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    @Schema(title = "移除时间")
    private Date deleteTime;

    @Schema(title = "移除人")
    private String deleteBy;

    /**
     * 账单类型
     */
    private String tpDct;

    /**
     * 账单中文描述
     */
    private String tpDl01;

    /**
     * 账单文件的链接地址
     */
    private String fileUrl;

    /**
     * 账单文件名
     */
    private String tpGtfilenm;

    /**
     * 公司编号
     */
    private String tpCo;

    /**
     * 公司名称
     */
    private String tpDl03;

    /**
     * 用户编号
     */
    private String tpAn8;

    /**
     * 用户编号集合
     */
    private List<String> tpAn8List;

    /**
     * 用户名称
     */
    private String tpAlph;

    /**
     * jde合同号
     */
    private String tpDoco;

    /**
     * 用户的合同号集合
     */
    private List<String> tpDocoList;

    /**
     * 建筑物编号
     */
    private String tpMcu;

    /**
     * 建筑物编号 集合
     */
    private List<String> tpMcuList;

    /**
     * 用户手动输入的建筑物编号 集合
     */
    private String[] inputTpMcuStr;

    /**
     * 建筑物描述
     */
    private String tpDc;

    /**
     * 账单生成日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date tpCrtutime;

    /**
     * 账单状态 0未发送、5发送成功、10发送失败;15已删除；20文件缺失
     */
    private Integer tpStatus;

    /**
     * 账单年
     */
    private Integer tpFyr;

    /**
     * 账单月
     */
    private Integer tpPn;

    private Integer tpFyrStart;

    private Integer tpPnStart;

    private Integer tpFyrEnd;

    private Integer tpPnEnd;

    /**
     * 网站显示 Y为显示
     */
    private String tpEv01;

    /**
     * 账单来源
     */
    private String tpEv02;

    /**
     * 删除标志
     */
    private String delFlag;

    /**
     * 账单打印时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String formatDate;

    /**
     * 账单文件名称
     */
    private String tpGtitnm;

    /**
     * 账单单元
     */
    private String tpUnit;

    /**
     * 账单站内信发送状态 0未发送，5已发送
     */
    private Integer mailStatus;

    /**
     * 账单邮件发送状态 0未发送，5已发送 10发送失败 15部分成功 30都成功
     */
    private Integer emailStatus;

    /**
     * 账单站内信发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date mailDate;

    /**
     * 账单邮件发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date emailDate;

    /**
     * 账单的邮件解析结果
     */
    private String emailErr;

    /**
     * 是否在前端可以选中进行发送账单
     */
    private boolean checkedFlag;

    /**
     * 站内信阅读时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date mailReadTime;

    /**
     * 账单的发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date billSendTime;

    /**
     * 站内信是否已读
     */
    private Boolean readFlag;

    /**
     * 发件箱的*ID
     */
    private Long outboxId;


    /**
     * 手机端的最早阅读时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date mobileReadTime;

    /**
     * 用户阅读状态 0 未读、1已读  不管是哪里进行阅读了都是设置为已读
     */
    private Integer readStatus;

    /**
     * 记录关于该账单的账号信息
     */
    private List<SysUser> userInfoList;

    @Override
    public List<Optional<Predicate>> predicates() {
        ImmutableList.Builder builder = ImmutableList.<Optional<Predicate>>builder();
        builder.add(Optional.ofNullable("0").map(QBillEntity.billEntity.delFlag::eq))
                .add(Optional.ofNullable("Y").map(QBillEntity.billEntity.tpEv01::eq))
                .add(Optional.ofNullable(formatDate).map(QBillEntity.billEntity.formatDate::eq))
                .add(Optional.ofNullable(tpMcu).map(QBillEntity.billEntity.tpMcu::eq))
                .add(Optional.ofNullable(tpDoco).map(QBillEntity.billEntity.tpDoco::eq))
                .add(Optional.ofNullable(tpAn8).map(QBillEntity.billEntity.tpAn8::eq))
                .add(Optional.ofNullable(tpUnit).map(QBillEntity.billEntity.tpUnit::eq))
                .add(Optional.ofNullable(tpStatus).map(QBillEntity.billEntity.tpStatus::eq))
                .add(Optional.ofNullable(mailStatus).map(QBillEntity.billEntity.mailStatus::eq))
                .add(Optional.ofNullable(tpFyr).map(QBillEntity.billEntity.tpFyr::eq))
                .add(Optional.ofNullable(tpPn).map(QBillEntity.billEntity.tpPn::eq))
                .add(Optional.ofNullable(inputTpMcuStr).map(QBillEntity.billEntity.tpMcu::in));

        if (emailStatus != null && emailStatus == 30) {
            builder.add(Optional.ofNullable(Arrays.asList(SendStatus.MSG_PARTIAL_SUCCESS.getIndex()
                    , SendStatus.MSG_SUCCESS.getIndex())).map(QBillEntity.billEntity.mailStatus::in));
        } else {
            builder.add(Optional.ofNullable(emailStatus).map(QBillEntity.billEntity.mailStatus::in));
        }

        if (CollectionUtils.isNotEmpty(tpMcuList)) {
            builder.add(Optional.ofNullable(tpMcuList).map(QBillEntity.billEntity.tpMcu::in));
        } else {
            builder.add(Optional.ofNullable(tpAn8List).map(QBillEntity.billEntity.tpAn8::in))
                    .add(Optional.ofNullable(tpDocoList).map(QBillEntity.billEntity.tpDoco::in));
        }
        return builder.build();
    }

}
