package com.kerryprops.kip.bill.service;

import com.kerryprops.kip.bill.dao.entity.BillSendConfig;
import com.kerryprops.kip.bill.webservice.vo.req.BillSendConfigReqVo;
import com.kerryprops.kip.bill.webservice.vo.req.DocoBillPayer;
import com.kerryprops.kip.bill.webservice.vo.req.StaffBillSendConfigInsertVo;
import com.kerryprops.kip.bill.webservice.vo.req.StaffBillSendConfigUpdateVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillPayer;
import com.kerryprops.kip.bill.webservice.vo.resp.BillSendConfigResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface IBillSendConfigService {

    BillSendConfigResource saveBillSendConfig(StaffBillSendConfigInsertVo staffBillSendConfigInsertVo);

    BillSendConfigResource updateBillSendConfig(StaffBillSendConfigUpdateVo staffBillSendConfigUpdateVo);

    Page<BillSendConfigResource> searchBillSendConfig(Pageable pageable, BillSendConfigReqVo billSendConfigReqVo);

//    String syncFromProfileService(String projectId);

    boolean deleteBillSendConfig(List<Long> configIds);

    boolean syncMcuFromTenantBills();

    boolean syncEnterpriseAccounts();

    List<String> queryDocos(String bAccount);

    List<BillPayer> queryBillPayers(String bAccount);

    List<BillPayer> fuzzyQueryPayers(String projectId, String query);

    BillSendConfigResource queryById(long id);

    List<BillSendConfig> filterBillSendConfigs(DocoBillPayer docoBillPayer);

}
