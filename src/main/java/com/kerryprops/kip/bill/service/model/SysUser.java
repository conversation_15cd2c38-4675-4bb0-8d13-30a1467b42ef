package com.kerryprops.kip.bill.service.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kerryprops.kip.bill.service.model.leg.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 用户对象 sys_user
 * 属性上备注 db 为数据库字段
 *
 * <AUTHOR>
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysUser extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID  db
     */
    private Long userId;

    /**
     * 部门ID  db
     */
    private Long deptId;

    /**
     * 用户账号 db
     */
    private String userName;

    /**
     * 用户昵称 db
     */
    private String nickName;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 用户邮箱 db
     */
    private String email;

    /**
     * 用户的备用邮箱 多个以逗号分隔
     */
    private String emailCc;

    /**
     * 手机号码 db
     */
    private String phonenumber;

    /**
     * 用户性别 db
     */
    private String sex;

    /**
     * 用户头像 db
     */
    private String avatar;

    /**
     * 密码 db
     */
    private String password;

    /**
     * 盐加密
     */
    private String salt;

    /**
     * 帐号状态（0正常 1停用） db
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除） db
     */
    private String delFlag;

    /**
     * 最后登陆IP db
     */
    private String loginIp;

    /**
     * 最后登陆时间 db
     */
    private Date loginDate;

//    /** 部门对象 */
//    })
//    private SysDept dept;

    /**
     * 用户类型  通过 UserConstants.USER_TYPE_xxx  中获取对应的值  db
     */
    private String userType;

    /**
     * 使用开始时间  db
     */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startUseTime;

    /**
     * 使用结束时间  db
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endUseTime;

    /**
     * 用于储存 用户类型的绑定楼盘id; db
     */
    private String buildingId;

    /**
     * 用于记录新建用户的 随机密码，后期密码更新不在记录，当重置密码时再记录
     */
    private String randomNum;

//    /** 角色对象 */
//    private List<SysRole> roles;

    /**
     * 角色组
     */
    private Long[] roleIds;

    /**
     * 岗位组
     */
    private Long[] postIds;

    /**
     * 封装管理大楼组
     */
    private Long[] houseIds;

    /***
     * 不可进行编辑的大楼部分
     */
    private Long[] notEditHouseIds;

    /**
     * 封装针对用户类型 的管理公司组
     */
    private Long[] companyIds;

    /**
     * 针对用户类型的 多个租户地址号
     */
    private String[] an8s;

    /**
     * 封装 楼盘id数组的查询条件
     */
    private Long[] selectBuildingIds;

    /**
     * 仅在编辑用户时设置为 true
     */
    private boolean webEditInfoFlag;

    /**
     * 大楼Id
     */
    private Long houseId;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 用户ids
     */
    private List<Long> userIds;

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 用户登录在redis中存储unionId
     */
    private String unionId;

    public Long getCompanyId() {
        return companyId;
    }
//
//    private List<SysUserBindInfoVo> sysUserBindInfoVos;

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

}
