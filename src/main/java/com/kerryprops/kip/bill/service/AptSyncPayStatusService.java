package com.kerryprops.kip.bill.service;

import com.kerryprops.kip.bill.dao.entity.AptJdeBill;
import com.kerryprops.kip.bill.dao.entity.JDEAptBill;
import com.kerryprops.kip.bill.service.model.AptPaymentTimestamp;
import com.kerryprops.kip.bill.service.model.JdeConfirmResult;

public interface AptSyncPayStatusService {

    JdeConfirmResult confirmAllPaidJdeItems(final String buString);

    JdeConfirmResult confirmAllUnpaidJdeItems(String buString);

    JdeConfirmResult confirmIncrementalJdeItems(final String buString, final AptPaymentTimestamp timestamp);

    AptPaymentTimestamp confirmPayingBillTimestamp();

    boolean updateJdeBillPaymentStatus(AptJdeBill aptJdeBill, JDEAptBill jdeAptBill);

}
