package com.kerryprops.kip.bill.service.model.s;

import com.google.common.collect.ImmutableList;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillPushStatus;
import com.kerryprops.kip.bill.common.jpa.QueryFilter;
import com.kerryprops.kip.bill.dao.entity.QAptBill;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.querydsl.core.types.Predicate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Optional;

/**
 * 账单查询的 view
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class AptBillPushReqBo implements QueryFilter {

    private List<BillPushStatus> pushStatuses;

    private List<Long> billIds;

    private Integer maxYear;

    private Integer maxMonth;

    private List<BillPaymentStatus> paymentStatus;

    private String projectId;

    @Override
    public List<Optional<Predicate>> predicates() {
        Integer billMonth = null;
        if (maxYear != null) {
            billMonth = maxYear * 100;
        }
        if (maxMonth != null) {
            billMonth += maxMonth;
        }
        ImmutableList.Builder builder = ImmutableList.<Optional<Predicate>>builder();
        builder.add(Optional.ofNullable(0).map(QAptBill.aptBill.deletedAt::eq))
                .add(Optional.ofNullable(projectId).map(QAptBill.aptBill.projectId::eq))
                .add(Optional.ofNullable(maxBindingScope()).map(QAptBill.aptBill.buildingId::in))
                .add(Optional.ofNullable(billIds).map(QAptBill.aptBill.id::in))
                .add(Optional.ofNullable(billMonth).map(QAptBill.aptBill.billMonth::loe))
                .add(Optional.ofNullable(paymentStatus).map(QAptBill.aptBill.paymentStatus::notIn))
                .add(Optional.ofNullable(pushStatuses).map(QAptBill.aptBill.pushStatus::in));
        return builder.build();
    }

    public List<String> maxBindingScope() {
        LoginUser loginUser = UserInfoUtils.getUser();
        if (loginUser == null) {
            throw new RuntimeException("not login.");
        }
        if (loginUser.isSuperAdmin()) {
            return null;
        }
        return loginUser.toBuildingIdList();
    }

}
