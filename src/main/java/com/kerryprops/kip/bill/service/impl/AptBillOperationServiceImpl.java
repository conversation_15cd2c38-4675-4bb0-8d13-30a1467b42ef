package com.kerryprops.kip.bill.service.impl;

import com.google.common.collect.Lists;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.enums.BillPushStatus;
import com.kerryprops.kip.bill.dao.AptBillOperationContentRepository;
import com.kerryprops.kip.bill.dao.AptBillOperationRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptBillOperation;
import com.kerryprops.kip.bill.dao.entity.AptBillOperationContent;
import com.kerryprops.kip.bill.dao.entity.AptBillOperationStatus;
import com.kerryprops.kip.bill.dao.entity.AptBillOperator;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.AptBillOperationService;
import com.kerryprops.kip.bill.service.model.s.AptBillOperationBo;
import com.kerryprops.kip.bill.webservice.vo.resp.OperationChangedFiledRespVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class AptBillOperationServiceImpl implements AptBillOperationService {

    private AptBillOperationRepository operationRepository;

    private AptBillOperationContentRepository operationContentRepository;

    @Override
    @Transactional
    public boolean saveAutoOperationLog(AptBill aptBill, List<OperationChangedFiledRespVo> changes) {
        return doSaveOperationLog(aptBill, changes, AptBillOperator.AUTO_SYNC, Boolean.TRUE, StringUtils.EMPTY);
    }

    @Override
    @Transactional
    public boolean saveOperationLog(AptBill aptBill, List<OperationChangedFiledRespVo> changes
            , AptBillOperator operator, String comment) {
        return doSaveOperationLog(aptBill, changes, operator, Boolean.FALSE, comment);
    }

    @Override
    public Page<AptBillOperation> queryOperationLogs(AptBillOperationBo bo, Pageable pageable) {
        return operationRepository.findAll(bo.toPredicates(), pageable);
    }

    @Override
    public List<AptBillOperation> queryOperationLogs(AptBillOperationBo bo) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Iterable<AptBillOperation> billOperations = operationRepository.findAll(bo.toPredicates(), sort);
        return Lists.newLinkedList(billOperations);
    }

    @Override
    public List<AptBillOperationContent> queryOperationContents(Collection<Long> ids) {
        return operationContentRepository.findAllByOperationIdIn(ids);
    }

    private boolean doSaveOperationLog(AptBill aptBill, List<OperationChangedFiledRespVo> changes
            , AptBillOperator operator, boolean isAuto, String comment) {
        if (CollectionUtils.isEmpty(changes) && operator.getIndex() < AptBillOperator.PUSH_ALL.getIndex()) {
            log.info("no_changes_after_syncing apt_bill: {}", aptBill.getBillNo());
            return false;
        }
        AptBillOperation billOperation = new AptBillOperation();
        billOperation.setBillId(aptBill.getId());
        billOperation.setBillNo(aptBill.getBillNo());
        billOperation.setOperator(operator);
        billOperation.setComment(comment);
        BillPushStatus pushStatus = aptBill.getPushStatus();
        if (Objects.equals(BillPushStatus.PUSHED, pushStatus)) {
            billOperation.setOperationStatus(AptBillOperationStatus.PUSHED);
        } else if (Objects.equals(BillPushStatus.PUSH_FAILED, pushStatus)
                || (Objects.equals(BillPushStatus.PUSH_FAILED_NO_AUTH, pushStatus))) {
            billOperation.setOperationStatus(AptBillOperationStatus.PUSH_FAILED);
        } else {
            billOperation.setOperationStatus(AptBillOperationStatus.UNKNOWN);
        }
        billOperation.setOperationName(operator.name());
        billOperation.setDiffCount(Optional.ofNullable(changes).map(List::size).orElse(0));
        billOperation.setCategory(aptBill.getCategory());
        billOperation.setProjectId(aptBill.getProjectId());
        billOperation.setProjectName(aptBill.getPositionItem().getProjectName());
        billOperation.setBuildingId(aptBill.getBuildingId());
        billOperation.setBuildingName(aptBill.getPositionItem().getBuildingName());
        billOperation.setFloorId(aptBill.getFloorId());
        billOperation.setFloorName(aptBill.getPositionItem().getFloorName());
        billOperation.setRoomId(aptBill.getRoomId());
        billOperation.setRoomName(aptBill.getPositionItem().getRoomName());
        if (isAuto) {
            autoOperatorInfo(billOperation);
        } else {
            loginOperatorInfo(billOperation);
        }
        billOperation = operationRepository.save(billOperation);

        long operationId = billOperation.getId();

        if (CollectionUtils.isNotEmpty(changes)) {
            List<AptBillOperationContent> contents = toContent(operationId, changes);
            operationContentRepository.saveAll(contents);
        }
        log.info("{} changes_saved_after_syncing apt_bill: {}", changes.size(), aptBill.getBillNo());
        return true;
    }

    private void autoOperatorInfo(AptBillOperation billOperation) {
        billOperation.setOperateUserId("888888");
        billOperation.setOperateUserName("kerry+");
        billOperation.setOperateUserEmail("Kerry+");
    }

    private void loginOperatorInfo(AptBillOperation billOperation) {
        LoginUser userInfo = UserInfoUtils.getUser();
        billOperation.setOperateUserId(String.valueOf(userInfo.getUserId()));
        billOperation.setOperateUserName(userInfo.getNickName());
        billOperation.setOperateUserEmail(userInfo.getLoginAccount());
    }

    private List<AptBillOperationContent> toContent(long operationId, List<OperationChangedFiledRespVo> changes) {
        return changes.stream().map(change -> {
            AptBillOperationContent content = new AptBillOperationContent();
            content.setOperationId(operationId);
            content.setFieldName(change.getFieldName());
            content.setFieldAlias(change.getFieldAlias());
            content.setFieldOldValue(change.getFieldOldValue());
            content.setFieldNewValue(change.getFieldNewValue());
            return content;
        }).collect(Collectors.toList());
    }

}
