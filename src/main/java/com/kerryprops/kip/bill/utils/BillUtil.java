package com.kerryprops.kip.bill.utils;

import org.apache.commons.lang3.RandomUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Calendar;
import java.util.Objects;

/**
 * 账单相关业务工具类
 *
 * <AUTHOR> 2023-12-15 16:42:02
 **/
public class BillUtil {

    private BillUtil() {
    }

    public static String createBillNo() {
        Calendar c = Calendar.getInstance();
        return "B" + c.get(Calendar.YEAR) +
                RandomUtils.nextInt(100000, 999999) +
                (c.get(Calendar.MONTH) + 1) +
                c.get(Calendar.DATE) +
                c.get(Calendar.HOUR_OF_DAY) +
                c.get(Calendar.MINUTE) +
                c.get(Calendar.SECOND) +
                c.get(Calendar.MILLISECOND);
    }

    /**
     * 格式化需要展示或保存的金额.
     */
    public static BigDecimal formatAmount(BigDecimal amount) {
        if (Objects.isNull(amount)) {
            return null;
        }
        return amount.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 计算杂费税额
     *
     * @param amount 实收金额
     * @param feeTax 杂费税率
     * @return
     */
    public static BigDecimal calcFeeTaxAmount(BigDecimal amount, BigDecimal feeTax) {
        Objects.requireNonNull(amount, "实收金额不能为空");
        Objects.requireNonNull(feeTax, "杂费税率不能为空");

        // 税额=收款金额 / （1+税率）* 税率，举例：1050/(1+0.05)*0.05=50
        BigDecimal feeTaxAmount = amount.divide(feeTax.add(BigDecimal.ONE), 6, RoundingMode.HALF_UP)
                .multiply(feeTax);
        return formatAmount(feeTaxAmount);
    }

    /**
     * 计算手续费[税额]
     * 主要替换 AptPayBo.toTaxFee()
     *
     * @param totalAmt     支付金额
     * @param taxRate      税率
     * @param maxTaxAmount 最大税额
     * @return
     */
    public static double calcTaxAmount(Double totalAmt, Double taxRate, Double maxTaxAmount) {
        Objects.requireNonNull(totalAmt, "支付金额不能为空");
        Objects.requireNonNull(taxRate, "税率不能为空");
        Objects.requireNonNull(maxTaxAmount, "最大税额不能为空");

        BigDecimal totalAmtDecimal = BigDecimal.valueOf(totalAmt);
        BigDecimal taxRateDecimal = BigDecimal.valueOf(taxRate);
        BigDecimal taxAmount = totalAmtDecimal.multiply(taxRateDecimal);
        double taxFee = Objects.requireNonNull(formatAmount(taxAmount)).doubleValue();
        // 计算出来的税额，不能超过配置的最大税额
        return Math.min(maxTaxAmount, taxFee);
    }

    public static int getBillYearMonth(Integer year, Integer month) {
        Objects.requireNonNull(year, "year can't be null");
        Objects.requireNonNull(month, "month can't be null");

        String monthStr = month < 10 ? "0" + month : String.valueOf(month);
        String yearMonthStr = year + monthStr;
        return Integer.parseInt(yearMonthStr);
    }

}
