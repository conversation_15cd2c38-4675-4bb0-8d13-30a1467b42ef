package com.kerryprops.kip.bill.common.enums;

import lombok.Getter;

@Getter
public enum BillStatus {
    LACK_HIVE(-1, "HIVE数据未加载"),
    INITIALED(0, "已创建"),
    TO_BE_SENT(1, "待发送"),
    TO_BE_PAID(2, "待支付"),
    ONLINE_PAID(3, "已线上支付"),
    JDE_VERIFIED(4, "JDE已确认支付"),
    CASHIER_PAID(5, "线下支付");

    private final Integer code;

    private final String info;

    BillStatus(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

}
