package com.kerryprops.kip.bill.common.enums;

import lombok.Getter;

public enum JDEDocType {

    SA("对账单", "Statement of Account"),
    D1("付款通知书", "Debit Note"),
    D2("付款通知书", "Debit Note"),
    DN("付款通知书", "Debit Note"),
    BN("违约通知", "Breach Notice"),
    UT("付款通知书", "Debit Note"),
    RE("催款单", "Reminder"),
    OP("逾期欠款及滞纳利息计算表", "Overdue AR & Penalty Interest"),
    VC("储值卡账单", "Value Card Statement"),
    UM("水电煤明细表", "Utilities Miscellaneous Fee");

    @Getter
    private final String desc;

    @Getter
    private final String engDesc;

    JDEDocType(String desc, String engDesc) {
        this.desc = desc;
        this.engDesc = engDesc;
    }

}
