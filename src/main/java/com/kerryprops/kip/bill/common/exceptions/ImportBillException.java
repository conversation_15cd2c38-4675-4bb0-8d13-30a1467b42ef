package com.kerryprops.kip.bill.common.exceptions;

import com.kerryprops.kip.bill.common.enums.IRespEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ImportBillException extends RuntimeException {

    private String code;

    private List<Object> detailErrorInfos;

    private int importedBillCount;

    public ImportBillException(String code, String msg) {
        super(msg);
        this.code = code;
    }

    public ImportBillException(IRespEnum codeEnum) {
        super(codeEnum.getMessage());
        this.code = codeEnum.getCode();
    }

    public ImportBillException() {
    }

}
