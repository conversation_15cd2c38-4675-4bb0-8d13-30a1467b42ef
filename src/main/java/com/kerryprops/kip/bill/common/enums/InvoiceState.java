package com.kerryprops.kip.bill.common.enums;

import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

public enum InvoiceState {

    UNKNOWN("未知", 0),
    NORMAL("正常", 1),
    CANCELLED("作废", 2),
    RED_STATUS("已红冲", 3),
    RED_LETTER("红冲票", 4);

    @Getter
    String desc;

    @Getter
    int index;

    InvoiceState(String desc, int index) {
        this.desc = desc;
        this.index = index;
    }

    public static InvoiceState fromDesc(String desc) {
        return Stream.of(InvoiceState.values()).filter(is -> Objects.equals(is.desc, desc))
                .findFirst().orElseThrow(() -> new RuntimeException("发票状态有误"));
    }

    public static InvoiceState fromIndex(int index) {
        return Stream.of(InvoiceState.values()).filter(is -> Objects.equals(is.index, index))
                .findFirst().orElseThrow(() -> new RuntimeException("发票index有误"));
    }

}
