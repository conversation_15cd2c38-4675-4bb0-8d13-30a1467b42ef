package com.kerryprops.kip.bill.common.utils.jde;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

/***
 * 2020/8/4
 * 获取 Oracle 的数据库连接池
 */
@Slf4j
@Configuration
public class OracleJdbc {

    private static String jdeUrl;

    private static String jdeUserName;

    private static String jdePassword;

    @Value("${spring.datasource.jde.url}")
    public void setJdeUrl(String jdeUrl) {
        OracleJdbc.jdeUrl = jdeUrl;
    }

    @Value("${spring.datasource.jde.username}")
    public void setJdeUserName(String jdeUserName) {
        OracleJdbc.jdeUserName = jdeUserName;
    }

    @Value("${spring.datasource.jde.password}")
    public void setJdePassword(String jdePassword) {
        OracleJdbc.jdePassword = jdePassword;
    }

    public static Connection getOracleConnection() {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
        } catch (ClassNotFoundException e) {
            log.error("OracleDriver class not found", e);
        }

        try {
            return DriverManager.getConnection(jdeUrl, jdeUserName, jdePassword);
        } catch (SQLException e) {
            log.error("getOracleConnection error", e);
        }
        return null;
    }

}
