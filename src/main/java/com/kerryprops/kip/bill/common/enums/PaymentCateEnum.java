package com.kerryprops.kip.bill.common.enums;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: PaymentCate
 * @Description: 预收科目枚举
 * @Author: Kanon
 * @Date: 2021/12/10 11:52
 * @Version: 2.0
 */
public enum PaymentCateEnum {
    UNKNOWN("UNKNOWN", "未知"),
    A003("A003", "A003 管理费"),
    A004("A004", "A004 其他收款");

    public static final List<Map<String, String>> paymentCates = initCache();

    private final String code;

    private final String name;

    PaymentCateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    private static List<Map<String, String>> initCache() {
        List<Map<String, String>> cache = new ArrayList<>();
        for (PaymentCateEnum paymentCateEnum : PaymentCateEnum.values()) {
            if (!paymentCateEnum.equals(UNKNOWN)) {
                var map = new HashMap<String, String>();
                map.put("code", paymentCateEnum.code);
                map.put("name", paymentCateEnum.name);
                cache.add(map);
            }
        }
        return cache;
    }
}
