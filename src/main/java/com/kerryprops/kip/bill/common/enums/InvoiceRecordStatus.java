package com.kerryprops.kip.bill.common.enums;

import lombok.Getter;

import java.util.stream.Stream;

/**
 * 开票状态
 */
public enum InvoiceRecordStatus {

    PROCESSING(1),
    COMPLETED(2),
    FAILED(3),
    ENTOURAGE_COMPLETED(4);

    @Getter
    int index;

    InvoiceRecordStatus(int index) {
        this.index = index;
    }

    public static InvoiceRecordStatus fromIndex(int index) {
        return Stream.of(InvoiceRecordStatus.values()).filter(is -> is.index == index)
                .findFirst().orElseThrow(() -> new RuntimeException("发票开具状态有误"));
    }

}
