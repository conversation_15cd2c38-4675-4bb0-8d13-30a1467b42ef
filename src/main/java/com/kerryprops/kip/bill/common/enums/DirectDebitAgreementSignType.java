package com.kerryprops.kip.bill.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DirectDebitAgreementSignType {

    PAY_AND_SIGN(0),
    DIRECT_SIGN(1);

    int index;

    public static DirectDebitAgreementSignType fromIndex(int index) {
        for (DirectDebitAgreementSignType s : DirectDebitAgreementSignType.values()) {
            if (s.index == index) {
                return s;
            }
        }
        throw new UnsupportedOperationException("can't parse into agreement signType from index: " + index);
    }

}
