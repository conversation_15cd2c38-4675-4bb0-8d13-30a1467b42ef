package com.kerryprops.kip.bill.common.enums;

import lombok.Getter;

/**
 * 发送发票-业务类型枚举类
 *
 * <AUTHOR>
 * @date 2024-8-21
 */
@Getter
public enum BillInvoiceBizTypeEnum {

    PROPERTY_LEASING("LEA", "租赁账单"),
    SY_UTILITY("UTI","电表系统");

    private final String bizCode;

    private final String desc;

    BillInvoiceBizTypeEnum(String bizCode, String desc) {
        this.bizCode = bizCode;
        this.desc = desc;
    }

    /**
     * 获取发票业务类型描述
     */
    public static String getInvoiceBizDesc(String bizType) {
        return SY_UTILITY.getBizCode().equalsIgnoreCase(bizType)
                ? SY_UTILITY.getDesc() : PROPERTY_LEASING.getDesc();
    }

}
