package com.kerryprops.kip.bill.common.enums;

import lombok.Getter;

import java.util.stream.Stream;

/**
 * 发票状态汇总
 *
 * <AUTHOR>
 * @date 2023-4-11
 */
public enum InvoiceIssueStatus {

    // 开票请求中
    UPLOADING(0),

    // 开票请求响应成功
    UPLOAD_SUCCESS(1),

    // 开票请求响应失败
    UPLOAD_FAILED(2),

    // 发票状态：正常
    INVOICE_SUCCESS(3),

    // 发票状态：正常，蓝票已红冲
    INVOICE_SUCCESS_RED_STATUS(4),

    // 发票状态：正常，红冲票
    INVOICE_SUCCESS_RED_FLAG(5),


    // 发票状态：作废
    INVOICE_CANCEL(6),

    // 开票失败
    INVOICE_FAILED(7);

    @Getter
    int index;

    InvoiceIssueStatus(int index) {
        this.index = index;
    }

    /**
     * 枚举index转枚举
     */
    public static InvoiceIssueStatus fromIndex(int index) {
        return Stream.of(InvoiceIssueStatus.values()).filter(is -> is.index == index)
                .findFirst().orElseThrow(() -> new RuntimeException("发票开票请求状态有误"));
    }
}
