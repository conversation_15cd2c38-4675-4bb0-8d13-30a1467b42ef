package com.kerryprops.kip.bill.common.jpa;

import com.alibaba.fastjson.JSON;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.data.jpa.repository.support.JpaEntityInformation;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
public class BaseJpaRepositoryImpl<T, D> extends SimpleJpaRepository<T, D>
        implements BaseJpaRepository<T, D> {

    private final EntityManager entityManager;

    public BaseJpaRepositoryImpl(JpaEntityInformation<T, D> entityInformation,
                                 EntityManager entityManager) {
        super(entityInformation, entityManager);
        this.entityManager = entityManager;
    }

    @Override
    public JPAQueryFactory getJpaQueryFactory() {
        return new JPAQueryFactory(entityManager);
    }

    @Override
    @Transactional
    public void insert(T entity) {
        entityManager.persist(entity);
    }

    @Override
    @Transactional
    @SuppressWarnings("unchecked")
    public T saveDiff(D id, Object updateSource) {
        Objects.requireNonNull(id, "id must not be null");
        var entity = findById(id).orElseThrow();
        var sourceBeanMap = BeanMap.create(updateSource);
        var entityMap = BeanMap.create(entity);
        Map<Object, Object> origin = new HashMap<>();
        Map<Object, Object> diff = new HashMap<>();
        AtomicBoolean hasDiff = new AtomicBoolean(false);
        sourceBeanMap.forEach((k, v) -> {
            if (v != null && entityMap.containsKey(k)) {
                Object originalValue = entityMap.get(k);
                if (v.equals(originalValue)) {
                    return;
                }
                origin.put(k, originalValue);
                diff.put(k, v);
                entityMap.put(k, v);
                hasDiff.compareAndSet(false, true);
            }
        });
        if (hasDiff.get()) {
            log.info("entity : {} , id : {} , origin : {} , diff : {}", entity.getClass().getSimpleName(),
                    id, JSON.toJSONString(origin), JSON.toJSONString(diff));
            entityManager.merge(entity);
        }
        return entity;
    }

}
