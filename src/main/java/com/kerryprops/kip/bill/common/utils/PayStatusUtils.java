package com.kerryprops.kip.bill.common.utils;

import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.dao.entity.QAptBill;
import com.querydsl.core.types.Predicate;

import java.util.List;
import java.util.Optional;

/**
 * 将paymentStatus支付状态归并成已付款，未付款，支付中3种状态
 */
public class PayStatusUtils {

    /**
     * paymentStatus过滤条件归并成已付款，未付款，支付中，对过滤条件具体对应到各个实际状态
     */
    public static Optional<Predicate> getPayStatusPredicate(BillPaymentStatus paymentStatus) {
        if (null == paymentStatus) {
            return Optional.empty();
        }

        return switch (paymentStatus) {
            case PAID -> // 已付款
                    Optional
                            .of(List.of(BillPaymentStatus.PAID, BillPaymentStatus.DIRECT_DEBIT_PAID))
                            .map(QAptBill.aptBill.paymentStatus::in);
            case PAYING -> // 支付中
                    Optional
                            .of(List.of(BillPaymentStatus.PAYING, BillPaymentStatus.DIRECT_DEBIT_PAYING))
                            .map(QAptBill.aptBill.paymentStatus::in);
            case TO_BE_PAID -> // 未付款
                    Optional
                            .of(List.of(BillPaymentStatus.TO_BE_PAID, BillPaymentStatus.PART_PAID
                                    , BillPaymentStatus.CANCEL, BillPaymentStatus.DIRECT_DEBIT_FAILED))
                            .map(QAptBill.aptBill.paymentStatus::in);
            default -> Optional.empty();
        };
    }

    /**
     * paymentStatus支付状态归并成已付款，未付款，支付中3种状态
     *
     * @param paymentStatus 支付状态
     * @return 已付款，未付款，支付中3种状态
     */
    public static String transPaymentStatus(String paymentStatus) {
        if (null == paymentStatus) {
            return "";
        }

        if (paymentStatus.equals(BillPaymentStatus.PAID.toString())
                || paymentStatus.equals(BillPaymentStatus.DIRECT_DEBIT_PAID.toString())) {
            return "已付款";
        } else if (paymentStatus.equals(BillPaymentStatus.TO_BE_PAID.toString())
                || paymentStatus.equals(BillPaymentStatus.PART_PAID.toString())
                || paymentStatus.equals(BillPaymentStatus.CANCEL.toString())
                || paymentStatus.equals(BillPaymentStatus.DIRECT_DEBIT_FAILED.toString())) {
            return "未付款";
        } else if (paymentStatus.equals(BillPaymentStatus.PAYING.toString())
                || paymentStatus.equals(BillPaymentStatus.DIRECT_DEBIT_PAYING.toString())) {
            return "支付中";
        } else {
            return "未知状态";
        }
    }

}
