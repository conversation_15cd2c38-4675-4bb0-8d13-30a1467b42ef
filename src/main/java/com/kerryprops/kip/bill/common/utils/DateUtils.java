package com.kerryprops.kip.bill.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 时间工具类
 *
 * <AUTHOR>
@Deprecated
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    public static String CH_YYYY_MM_DD = "yyyy年MM月dd日";

    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static final String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow(final String format) {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(final Date date) {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    /**
     * 将儒日历的六位数字转为 yyyy-MM-dd 格式的日期
     *
     * @param sixStr 六位数字的儒日历 上个世纪可能是5位数字
     * @return
     */
    public static String convertDateYYYYMMDD(String sixStr) {
        int num = Integer.parseInt(sixStr);
        if (sixStr.length() < 6) {//在不够6位的情况下 前补零 成6位 字符串
            sixStr = String.format("%06d", num);
        }
        int year = (Integer.parseInt(sixStr.substring(0, 3))) + 1900;
        int day = Integer.parseInt(sixStr.substring(3, 6));
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.DAY_OF_YEAR, day);
        return parseDateToStr(DateUtils.YYYY_MM_DD, calendar.getTime());
    }

    // 儒略历：113260 = 前三位(2013-1900)+后三位(2013年中的第260天)
    // 'yyyy-MM-dd'时间格式转换为六位数字日历
    public static String convertDateToJuLian(Date date) {
        DecimalFormat df = new DecimalFormat("000");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        String SDpmdt1 = String.valueOf(calendar.get(Calendar.YEAR) - 1900);
        String SDpmdt2 = df.format(calendar.get(Calendar.DAY_OF_YEAR));
        String SDdrqj3 = SDpmdt1 + SDpmdt2;
        return SDdrqj3;
    }

    public static Date parseDate(String pattern, String dateStr) {

        if (StringUtils.isBlank(pattern) || StringUtils.isBlank(dateStr)) {
            return null;
        }
        Date date = null;
        SimpleDateFormat format = new SimpleDateFormat(pattern);
        format.setLenient(false);
        try {
            date = format.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

}
