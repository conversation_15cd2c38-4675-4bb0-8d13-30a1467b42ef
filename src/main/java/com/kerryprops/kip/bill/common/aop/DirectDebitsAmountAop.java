package com.kerryprops.kip.bill.common.aop;

import com.kerryprops.kip.bill.common.exceptions.RestInvalidParamException;
import com.kerryprops.kip.bill.config.PaymentConfigProps;
import com.kerryprops.kip.bill.dao.entity.AptDirectDebitsBatchBill;
import com.kerryprops.kip.bill.dao.entity.QAptBill;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import jakarta.persistence.EntityManager;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.TEST_BILL_TOTAL_AMOUNT_PROHIBITIVE;

/**
 * 仅 测试环境 加载
 * 非生产环境防止测试代扣金额过高
 */
@Slf4j
@Aspect
@Component
@AllArgsConstructor
@Profile("!proda & !prodb")
public class DirectDebitsAmountAop {

    private final EntityManager entityManager;

    private final PaymentConfigProps paymentConfigProps;

    @Before("pointcutMethods()")
    public void doBefore(JoinPoint joinPoint) {
        List<AptDirectDebitsBatchBill> batchBills = (List<AptDirectDebitsBatchBill>) joinPoint.getArgs()[1];
        Predicate predicate = QAptBill.aptBill.id.in(batchBills.stream().map(AptDirectDebitsBatchBill::getBillId)
                .collect(Collectors.toSet()));
        JPAQuery<BigDecimal> query = new JPAQuery<>(entityManager);
        Double sumAmount = Optional.ofNullable(query.select(QAptBill.aptBill.amt.sum())
                .from(QAptBill.aptBill).where(predicate).fetchOne()).orElse(0.0);
        if (sumAmount > paymentConfigProps.getDirectDebitsAmountLimit()) {
            throw new RestInvalidParamException(TEST_BILL_TOTAL_AMOUNT_PROHIBITIVE);
        }
    }

    @Pointcut("execution(public * com.kerryprops.kip.bill.service.AptBillDirectDebitsBatchBillService" +
            ".combineBillsByRoomAndCreatePaymentInfo(..))")
    private void pointcutMethods() {
    }

}
