package com.kerryprops.kip.bill.common.enums;

import lombok.Getter;

import java.util.Objects;

/***********************************************************************************************************************
 * Project - user-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/07/2021 10:46
 **********************************************************************************************************************/

@Getter
public enum FeeTypeEnum {

    MONTH_PARKING("月泊车位"),
    TIME_PARKING("时租车位"),
    FLEXIBLE_SPACE("灵活空间"),
    FLEXIBLE_STATION("灵活工位"),
    COMMUNITY_ACTIVITIES("社群活动"),
    KO_MARKET_ACTIVITIES("KO市场活动"),
    COMMERCIAL_COUPONS("有价商户券"),
    PROPERTY_MAINTENANCE("物业维修"),
    PROPERTY_FEE("物业杂费"),
    PROPERTY_MANAGEMENT("物业管理费"), // 物业缴费
    PROPERTY_LEASING("租赁账单"),
    WATER_RENT("水费"),
    ELECTRICITY_FEES("电费");

    private final String desc;

    FeeTypeEnum(String desc) {
        this.desc = desc;
    }

    public static FeeTypeEnum checkType(String type) {
        for (FeeTypeEnum typeEnum : FeeTypeEnum.values()) {
            if (Objects.equals(typeEnum.name(), type)) {
                return typeEnum;
            }
        }
        return null;
    }

}
