package com.kerryprops.kip.bill.common.exceptions;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.Optional;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ExceptionResource {

    private String errorCode;

    private String errorText;

    private String fieldName;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
    private ZonedDateTime timestamp = ZonedDateTime.now();

    private String correlationId;

    private String errorCategory;

    private Integer importedBillCount;

    private Object detailErrorInfo;

    public String getCode() {
        return Optional.ofNullable(errorCode).map(String::valueOf).orElse("1000");
    }

}
