package com.kerryprops.kip.bill.common.enums;

import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;

import java.util.Objects;

/**
 * 给 物业前台|财务 的杂费 收款详情 使用的状态
 *
 * <AUTHOR> 2023-12-25 11:10:39
 **/
public enum CashierPayStatus {
    /**
     * 待支付
     */
    TO_BE_PAID,
    /**
     * 已取消
     */
    CANCELLED,
    /**
     * 超时取消
     */
    CANCELLED_TIMEOUT,
    /**
     * 已审核
     */
    VERIFIED;

    public static CashierPayStatus of(AptPay aptPay) {
        if (Objects.equals(aptPay.getDeletedAt(), 1)) {
            return CANCELLED;
        }
        if (Objects.equals(AptPayVerifyStatus.VERIFIED, aptPay.getVerifyStatus())) {
            return VERIFIED;
        }
        return null;
    }

    public static CashierPayStatus of(AptPaymentInfo paymentInfo) {
        if (Objects.isNull(paymentInfo)) {
            return null;
        }
        if (Objects.equals(paymentInfo.getCancelType(), PayCancelTypeEnum.TIMEOUT_CANCELLED.name())) {
            return CANCELLED_TIMEOUT;
        }
        if (Objects.equals(paymentInfo.getPaymentStatus(), BillPaymentStatus.CANCEL)) {
            return CANCELLED;
        }
        if (Objects.equals(paymentInfo.getPaymentStatus(), BillPaymentStatus.TO_BE_PAID)) {
            return TO_BE_PAID;
        }
        return null;
    }

    public static CashierPayStatus of(AptPaymentInfo paymentInfo, AptPay aptPay) {
        CashierPayStatus status = of(paymentInfo);
        if (Objects.isNull(status) && Objects.nonNull(aptPay)) {
            return of(aptPay);
        }
        return status;
    }
}
