package com.kerryprops.kip.bill.common.jpa;

import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.NoRepositoryBean;

import java.util.NoSuchElementException;

@NoRepositoryBean
public interface BaseJpaRepository<T, D> extends JpaRepository<T, D> {

    JPAQueryFactory getJpaQueryFactory();

    /**
     * 插入一条实体记录到数据库。
     * 直接执行 em.persist()，在存在id时，可以减少一次 select 查询，而且比save()更符合语义。
     * 需要注意：如果在事务方法中，实体对象的属性有改动，还是会触发一次update的
     */
    void insert(T entity);

    /**
     * 根据指定的实体ID和更新源数据，比较差异并保存修改内容.
     * 如果更新源中的非null属性与现有实体属性存在差异，则更新实体，并使用EntityManager的merge方法持久化更改。
     *
     * @param id           实体的唯一标识符。
     * @param updateSource 包含要更新数据的源对象，其属性将与现有实体作比较。
     * @return 更新后的实体对象。
     * @throws NoSuchElementException 如果指定的ID未找到对应的实体，则抛出此异常。
     */
    T saveDiff(D id, Object updateSource);

}
