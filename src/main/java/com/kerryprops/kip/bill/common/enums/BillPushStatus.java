package com.kerryprops.kip.bill.common.enums;

import java.util.ArrayList;
import java.util.List;

public enum BillPushStatus {
    TO_BE_PUSHED(0, "待推送"),
    PUSHING(1, "推送中"),
    PUSHED(2, "已推送"),
    PUSH_FAILED(3, "推送失败"),
    PUSH_FAILED_NO_AUTH(4, "未授权房间");

    public static final List<Integer> ALL_STATUS = new ArrayList<Integer>();

    static {
        for (BillPushStatus bd : BillPushStatus.values()) {
            ALL_STATUS.add(bd.getCode());
        }
    }

    private final Integer code;

    private final String info;

    BillPushStatus(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
