package com.kerryprops.kip.bill.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DirectDebitsBatchStatus {

    UNKNOWN(0),
    AWAIT(1),
    SENT(2),
    LAPSED(3);

    int index;

    public static DirectDebitsBatchStatus fromIndex(int index) {
        for (DirectDebitsBatchStatus s : DirectDebitsBatchStatus.values()) {
            if (s.index == index) {
                return s;
            }
        }
        return UNKNOWN;
    }

}
