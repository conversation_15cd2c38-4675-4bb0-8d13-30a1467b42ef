package com.kerryprops.kip.bill.common.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;
import java.util.concurrent.TimeUnit;


@Component
public class PrimaryKeyUtil {


    public static final String PAYMENT = "P";

    public static final String DIRECT_DEBITS_BATCH = "BTH";

    private static final String YMD = "yyyyMMddHHmmss";

    private static RedisTemplate<String, Object> REDISTEMPLATE;

    /**
     * 生成订单号
     *
     * @return
     */
    public static String createPaymentId() {
        return primaryKey(PAYMENT, null);
    }

    public static String primaryKey(String key, Long delta) {
        try {
            // delta为空默认值1
            if (null == delta) {
                delta = 1L;
            }
            // 生成14位的时间戳(每秒使用新的时间戳当key)
            String timeStamp = new SimpleDateFormat(YMD).format(new Date());
            // 获得redis-key
            String newKey = key + timeStamp;
            // 获取自增值（时间戳+自定义key）
            Long increment = REDISTEMPLATE.opsForValue().increment(newKey, delta);
            // 设置时间戳生成的key的有效期为2秒
            REDISTEMPLATE.expire(newKey, 2, TimeUnit.SECONDS);
            // 获取订单号，时间戳 + 唯一自增Id(6位数,不过前方补0)
            return key + timeStamp + String.format("%06d", increment);
        } catch (Exception e) {
            // redis 宕机时采用时间戳加随机数
            String timeStamp = new SimpleDateFormat(YMD).format(new Date());
            Random random = new Random();
            //14位时间戳 + 6位随机数
            for (int i = 0; i < 6; i++) {
                timeStamp += (random.nextInt(10) + "");
            }
            return key + timeStamp;
        }
    }

    @Autowired
    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        PrimaryKeyUtil.REDISTEMPLATE = redisTemplate;
    }

}