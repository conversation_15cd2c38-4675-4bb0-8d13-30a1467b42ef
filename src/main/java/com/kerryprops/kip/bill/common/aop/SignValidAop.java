package com.kerryprops.kip.bill.common.aop;

import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.exceptions.ImportBillException;
import com.kerryprops.kip.bill.config.BsSignProperties;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - profile-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/22/2021 16:27
 **********************************************************************************************************************/

@Aspect
@Slf4j
@Component
public class SignValidAop {

    public static final long MAX_REQUEST = 10 * 60 * 1000L;

    private static final String DELIMITER = "&";

    @Autowired
    BsSignProperties kipSignProperties;

    @Before("checkSign()")
    public void doBefore(JoinPoint jp) {
        MethodSignature signature = (MethodSignature) jp.getSignature();
        Method method = signature.getMethod();
        SignValid signValid = method.getAnnotation(SignValid.class);

        HttpServletRequest request = ((ServletRequestAttributes) Objects
                .requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        String token = this.obtainHeader(request, "sign");
        String timestamp = this.obtainHeader(request, "timestamp");
        try {
            Boolean check = this.checkToken(token, timestamp, signValid.value());
            if (Boolean.FALSE.equals(check)) {
                throw new ImportBillException(RespCodeEnum.METHOD_SIGN_VALID_ERROR);
            }
        } catch (Throwable throwable) {
            throw new ImportBillException(RespCodeEnum.METHOD_SIGN_VALID_ERROR);
        }
    }

    @Pointcut("execution(public * *(..)) && @annotation(com.kerryprops.kip.bill.common.aop.SignValid)")
    private void checkSign() {
    }

    private Boolean checkToken(String token, String timestamp, String source) {
        if (StringUtils.isAnyBlank(token, timestamp)) {
            return false;
        }
        long now = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        long time = Long.parseLong(timestamp);
        if (now - time > MAX_REQUEST) {
            log.error("时间戳已过期: [{}] - [{}] - [{}]", now, time, (now - time));
            return false;
        }
        String crypt = this.getSignature(timestamp, source);
        return StringUtils.equals(crypt, token);
    }

    private String getSignature(String timestamp, String source) {
        BsSignProperties.SignConfig config = kipSignProperties.getByType(source);
        String[] signArr = new String[]{"systemId=" + config.getSystemId(), "systemSecret=" + config.getSystemSecret()
                , "timestamp=" + timestamp};
        Arrays.sort(signArr);
        String signStr = String.join(DELIMITER, signArr);
        return DigestUtils.md5DigestAsHex(signStr.getBytes());
    }

    private String obtainHeader(HttpServletRequest request, String key) {
        String result = request.getHeader(key);
        return StringUtils.isBlank(result) ? null : result;
    }

}
