package com.kerryprops.kip.bill.common.enums;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum BillPayChannel {

    UNKNOWN(-1, "未知"),
    ONLINE(0, "线上缴费"),
    OFFLINE(1, "线下缴费"),
    DIRECT_DEBITS(2, "协议代扣");

    private final int code;

    private final String info;

    BillPayChannel(int code, String info) {
        this.code = code;
        this.info = info;
    }

    public static BillPayChannel fromCode(int code) {
        return Arrays.stream(BillPayChannel.values()).filter(channel -> channel.code == code)
                .findFirst().orElse(BillPayChannel.ONLINE);
    }

}
