package com.kerryprops.kip.bill.common.constants;


import com.kerryprops.kip.bill.common.enums.InvoiceTypeEnum;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 一些常用的常量
 */
public abstract class AppConstants {

    public static final String APP_CODE = "appCode";

    public static final String COMMA = ",";

    /* TODO temporary hardcode */
    public static final List<String> ALERT_MAIL_RECEIVERS = List.of(
            "<EMAIL>"
            , "<EMAIL>"
    );

    public static final ZoneId DEFAULT_ZONE_ID = ZoneId.of("Asia/Shanghai");

    public static final String ALIPAY_DIRECT_PAYMENT_OPTION = "ALIPAY_WITHHOLDING";

    public static final String WX_DIRECT_PAYMENT_OPTION = "WECHATPAY_DIRECT_DEBIT";

    public static final ZonedDateTime NEXT_MILLENNIUM = ZonedDateTime.from(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
            .withZone(DEFAULT_ZONE_ID).parse("3000-01-01 00:00:00"));

    /**
     * 模板要进行替换的内容
     */
    public static final String TEMP_REPLACE_CONTENT = "{xxx}";

    public static final String APT_BILL_PUSH_REDIS_KEY_PREFIX = "kip:billing:apt_bill:s:push";

    public static final String STAFF_BILL_SEND_BILL_LIST_REDIS_KEY = "kip:billing:s:send_bill_list";

    /**
     * 该类型组下的值在jde中表示有效  对应表kerry_tss_jde_status_group type列
     */
    public static final String GROUP_JDE_VALID_TYPE = "1";

    /**
     * 该类型组下的值在tss中表示有效  对应表kerry_tss_jde_status_group type列
     */
    public static final String GROUP_VALID_TYPE = "2";

    /**
     * 该类型组下的值在tss中表示无效  对应表kerry_tss_jde_status_group type列
     */
    public static final String GROUP_INVALID_TYPE = "3";

    /**
     * tss表示同步过来的合同属于有效 对应表kerry_sync_jde tss_status列
     */
    public static final String TSS_VALID = "1";

    /**
     * tss表示同步过来的合同属于无效 对应表kerry_sync_jde tss_status列
     */
    public static final String TSS_INVALID = "2";

    /**
     * 公司绑定合同历史
     */
    public static final String COMPANY_CONSTRACT_BIND = "1";

    /**
     * 公司解绑合同历史
     */
    public static final String COMPANY_CONSTRACT_UNBIND = "2";

    public static final String[] NOT_SHOW_INVOICE_TYPES = {InvoiceTypeEnum.C.getKipCode(), InvoiceTypeEnum.S.getKipCode()
            , InvoiceTypeEnum.CZ.getKipCode(), InvoiceTypeEnum.SZ.getKipCode()};

    public static String DATE_FORMAT_YYYY_MM_DD = "yyyy-MM-dd";

    private AppConstants() {
    }

}

