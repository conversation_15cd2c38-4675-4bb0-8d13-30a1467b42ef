package com.kerryprops.kip.bill.common.utils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.kerryprops.kip.bill.annotation.LogOperation;
import com.kerryprops.kip.bill.annotation.LogOperationSerializer;
import com.kerryprops.kip.bill.webservice.vo.resp.OperationChangedFiledRespVo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentMap;

@Slf4j
@SuppressWarnings({"unchecked", "rawtypes"})
public class DiffFieldUtils {

    private static ConcurrentMap<Class<? extends LogOperationSerializer>, LogOperationSerializer> serializerMap
            = Maps.newConcurrentMap();

    private DiffFieldUtils() {
    }

    public static List<OperationChangedFiledRespVo> diffFields(Object obj, Object updatedObj) {
        return diffFields("", obj, updatedObj);
    }

    public static List<OperationChangedFiledRespVo> diffFields(String fieldPrefix, Object obj, Object updatedObj) {
        if (Objects.isNull(obj) && Objects.isNull(updatedObj)) {
            return Collections.emptyList();
        } else if (Objects.isNull(obj) || Objects.isNull(updatedObj)) {
            Class<?> clazz = Objects.isNull(obj) ? updatedObj.getClass() : obj.getClass();
            try {
                Object tempInstance = clazz.getDeclaredConstructor().newInstance();
                return diffFields(fieldPrefix, Objects.isNull(obj) ? tempInstance : obj
                        , Objects.isNull(updatedObj) ? tempInstance : updatedObj);
            } catch (Exception e) {
                log.error("diffFields error occurred:", e);
                return Collections.emptyList();
            }
        }
        return doDiffFields(fieldPrefix, obj, updatedObj);

    }

    private static List<OperationChangedFiledRespVo> doDiffFields(String fieldPrefix, Object obj, Object updatedObj) {
        try {
            Class<?> oldModelClazz = obj.getClass();
            Class<?> modelClazz = updatedObj.getClass();
            if (!Objects.equals(oldModelClazz, modelClazz)) {
                throw new IllegalArgumentException("diffFields for different Class instance is not supported");
            }
            List<Field> fieldList = getFields(obj.getClass());
            List<OperationChangedFiledRespVo> operationContents = Lists.newLinkedList();
            for (Field field : fieldList) {
                ReflectionUtils.makeAccessible(field);
                List<OperationChangedFiledRespVo> subOperationContents = diffField(new FieldWrapper(field, obj, updatedObj
                        , fieldPrefix));
                if (CollectionUtils.isNotEmpty(subOperationContents)) {
                    operationContents.addAll(subOperationContents);
                }
            }
            return operationContents;
        } catch (Exception e) {
            log.error("diffFields error occurred:", e);
            return Collections.emptyList();
        }
    }

    private static List<Field> getFields(Class<?> clazz) {
        return Arrays.asList(clazz.getDeclaredFields());
    }

    private static List<OperationChangedFiledRespVo> diffField(FieldWrapper field) {
        if (Objects.isNull(field.annotation)) {
            return Collections.emptyList();
        }
        if (field.annotation.deepLog()) {
            return deepDiffField(field);
        } else {
            OperationChangedFiledRespVo diffContent = directDiffField(field);
            return Optional.ofNullable(diffContent).map(List::of).orElse(Collections.emptyList());
        }
    }

    private static OperationChangedFiledRespVo directDiffField(FieldWrapper field) {
        if (Collection.class.isAssignableFrom(field.getField().getType())) {
            Collection<?> oldColl = (Collection<?>) field.getOldValue();
            Collection<?> newColl = (Collection<?>) field.getNewValue();

            Set<?> oldSet = CollectionUtils.isEmpty(oldColl) ? Collections.emptySet() : Sets.newHashSet(oldColl);
            Set<?> updatedSet = CollectionUtils.isEmpty(newColl) ? Collections.emptySet() : Sets.newHashSet(newColl);
            if (!Objects.equals(oldSet, updatedSet)) {
                return recordDiff(field, oldSet.toString(), updatedSet.toString());
            }
        } else {
            Object oldValue = field.getOldValue();
            Object newValue = field.getNewValue();
            if (oldValue == null || newValue == null) {
                return null;
            }
            if (!compareEquals(oldValue, newValue)) {
                return recordDiff(field, getLogValue(field, oldValue), getLogValue(field, newValue));
            }
        }
        return null;
    }

    private static String getLogValue(FieldWrapper field, Object obj) {
        Class<? extends LogOperationSerializer> clazz = field.getSerializer();
        if (Objects.equals(clazz, LogOperationSerializer.None.class)) {
            return String.valueOf(obj);
        }
        return Optional.ofNullable(serializerMap.get(clazz)).orElseGet(() -> {
            try {
                LogOperationSerializer serializer = clazz.getDeclaredConstructor().newInstance();
                serializerMap.putIfAbsent(clazz, serializer);
                return serializer;
            } catch (Exception e) {
                log.error("", e);
                return LogOperationSerializer.DEFAULT;
            }
        }).serialize(obj);
    }

    private static boolean compareEquals(Object oldValue, Object newValue) {
        if (Comparable.class.isAssignableFrom(oldValue.getClass())) {
            return ((Comparable) oldValue).compareTo(newValue) == 0;
        } else {
            return Objects.equals(oldValue, newValue);
        }
    }

    private static List<OperationChangedFiledRespVo> deepDiffField(FieldWrapper field) {
        if (Collection.class.isAssignableFrom(field.getField().getType())) {
            OperationChangedFiledRespVo diffContent = directDiffField(field);
            return Optional.ofNullable(diffContent).map(List::of).orElse(Collections.emptyList());
        } else {
            return diffFields(field.getFieldName() + ".", field.getOldValue(), field.getNewValue());
        }
    }

    private static OperationChangedFiledRespVo recordDiff(FieldWrapper field, String oldValueStr, String newValueStr) {
        OperationChangedFiledRespVo diffContent = new OperationChangedFiledRespVo();
        diffContent.setFieldName(field.getFieldName());
        diffContent.setFieldOldValue(oldValueStr);
        diffContent.setFieldNewValue(newValueStr);
        diffContent.setFieldAlias(field.getAnnotation().fieldAlias());
        return diffContent;
    }

    @Data
    private static class FieldWrapper {

        private Field field;

        private Object oldObj;

        private Object updatedObj;

        private LogOperation annotation;

        private String fieldNamePrefix;

        private Class<? extends LogOperationSerializer> serializer;

        public FieldWrapper(Field field, Object oldObj, Object updatedObj, String fieldNamePrefix) {
            this.field = field;
            this.oldObj = oldObj;
            this.updatedObj = updatedObj;
            this.annotation = field.getAnnotation(LogOperation.class);
            this.serializer = Optional.ofNullable(this.annotation).map(LogOperation::using).orElse(null);
            this.fieldNamePrefix = Objects.isNull(fieldNamePrefix) ? "" : fieldNamePrefix;
        }

        Object getOldValue() {
            try {
                return field.get(oldObj);
            } catch (Exception e) {
                log.error("getOldValue error:", e);
                return null;
            }
        }

        Object getNewValue() {
            try {
                return field.get(updatedObj);
            } catch (Exception e) {
                log.error("getOldValue error:", e);
                return null;
            }
        }

        String getFieldName() {
            return this.fieldNamePrefix + field.getName();
        }

    }

}
