package com.kerryprops.kip.bill.common.enums;

import org.apache.commons.lang3.StringUtils;

import static com.kerryprops.kip.bill.common.constants.AppConstants.ALIPAY_DIRECT_PAYMENT_OPTION;
import static com.kerryprops.kip.bill.common.constants.AppConstants.WX_DIRECT_PAYMENT_OPTION;

/**
 * 远程支付的支付方式.
 * 给 apt_payment_info 表 pay_type 字段使用。
 */
public enum PaymentPayType {
    UNKNOWN("未知", "unknown"),
    CASH("现金支付", "cash"),
    WECHAT("微信支付", "wechatpay"),
    ALIPAY("支付宝支付", "alipay"),
    ALI_WITHHOLD("支付宝代扣", "ali_withhold"),
    WX_DIRECT_DEBIT("微信代扣", "wechatpay_direct_debit");

    /**
     * 中文翻译值
     */
    private final String info;

    /**
     * tb_apt_bill_direct_debits_agreement 的 pspName
     */
    private final String pspName;

    PaymentPayType(String info, String pspName) {
        this.info = info;
        this.pspName = pspName;
    }

    public static PaymentPayType fromInfo(String info) {
        if (StringUtils.isBlank(info)) {
            return PaymentPayType.UNKNOWN;
        }
        for (PaymentPayType payTypeEnum : PaymentPayType.values()) {
            String infoTemp = payTypeEnum.getInfo();
            if (infoTemp.equalsIgnoreCase(info) || info.contains(infoTemp)) {
                return payTypeEnum;
            }
        }
        return PaymentPayType.UNKNOWN;
    }

    public static PaymentPayType fromPspName(String pspName) {
        for (PaymentPayType payTypeEnum : PaymentPayType.values()) {
            if (payTypeEnum.getPspName().equalsIgnoreCase(pspName)) {
                return payTypeEnum;
            }
        }
        return PaymentPayType.UNKNOWN;
    }

    public static PaymentPayType convertToBillPayType(PaymentPayType paymentPayType, String payOption) {
        PaymentPayType payType = PaymentPayType.WECHAT;
        if (PaymentPayType.WECHAT.equals(paymentPayType)) {
            payType = WX_DIRECT_PAYMENT_OPTION.equalsIgnoreCase(payOption)
                    ? PaymentPayType.WX_DIRECT_DEBIT : PaymentPayType.WECHAT;
        } else if (PaymentPayType.ALIPAY.equals(paymentPayType)) {
            payType = ALIPAY_DIRECT_PAYMENT_OPTION.equalsIgnoreCase(payOption)
                    ? PaymentPayType.ALI_WITHHOLD : PaymentPayType.ALIPAY;
        } else if (PaymentPayType.CASH.equals(paymentPayType)) {
            payType = PaymentPayType.CASH;
        } else if (PaymentPayType.UNKNOWN.equals(paymentPayType)) {
            payType = PaymentPayType.UNKNOWN;
        }
        return payType;
    }

    public String getInfo() {
        return info;
    }

    public String getPspName() {
        return pspName;
    }
}
