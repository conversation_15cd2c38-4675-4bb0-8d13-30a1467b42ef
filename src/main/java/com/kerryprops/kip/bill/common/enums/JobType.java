package com.kerryprops.kip.bill.common.enums;

import java.util.ArrayList;
import java.util.List;

public enum JobType {
    SYNC_PAY_TO_JDE(0, "同步支付信息到JDE"),
    SYNC_PAID_BILL_TO_JDE(1, "同步已付账单到JDE"),
    SYNC_PAID_BILL_FROM_JDE(2, "从JDE同步已付账单"),
    ;

    public static final List<Integer> ALL_STATUS = new ArrayList<Integer>();

    static {
        for (JobType bd : JobType.values()) {
            ALL_STATUS.add(bd.getCode());
        }
    }

    private final Integer code;

    private final String info;

    JobType(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
