package com.kerryprops.kip.bill.common.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;

import java.util.Collection;
import java.util.List;
import java.util.stream.Stream;

public enum SendStatus {

    MSG_NOT_SEND(0, "未发送"),
    MSG_SENDING(3, "发送中"),
    MSG_SENT(5, "发送中"),
    MSG_FAILED(10, "发送失败"),
    MSG_PARTIAL_SUCCESS(15, "部分发送成功"),
    MSG_SUCCESS(30, "发送成功");

    public static Collection<SendStatus> NotResendableStatuses = List.of(MSG_SENDING, MSG_SENT);

    public static Collection<SendStatus> SuccessStatuses = List.of(MSG_PARTIAL_SUCCESS, MSG_SUCCESS);

    @Getter
    int index;

    @Getter
    String desc;

    SendStatus(int index, String desc) {
        this.index = index;
        this.desc = desc;
    }

    @JsonCreator
    public static SendStatus fromIndex(int index) {
        return Stream.of(SendStatus.values())
                .filter(e -> e.index == index)
                .findFirst().orElse(MSG_NOT_SEND);
    }

}
