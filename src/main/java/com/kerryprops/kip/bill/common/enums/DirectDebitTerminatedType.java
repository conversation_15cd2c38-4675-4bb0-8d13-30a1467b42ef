package com.kerryprops.kip.bill.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DirectDebitTerminatedType {

    UNKNOWN(0),
    MERCHANT_UNSIGN(1),
    USER_UNSIGN(2);

    int index;

    public static DirectDebitTerminatedType fromIndex(int index) {
        for (DirectDebitTerminatedType s : DirectDebitTerminatedType.values()) {
            if (s.index == index) {
                return s;
            }
        }
        return UNKNOWN;

    }

}
