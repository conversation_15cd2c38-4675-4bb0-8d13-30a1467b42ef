package com.kerryprops.kip.bill.common.vo;

import com.alibaba.fastjson.JSONObject;
import com.kerryprops.kip.bill.common.enums.IRespEnum;
import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 接口响应结果封装对象
 */
@Schema(name = "RespWrapVo", description = "接口响应统一封装对象")
@Data
@Slf4j
public class RespWrapVo<T> {

    @Schema(title = "响应业务码")
    private String code = RespCodeEnum.SUCCESS.getCode();

    @Schema(title = "响应消息")
    private String message = RespCodeEnum.SUCCESS.getMessage();

    @Schema(title = "响应数据")
    private T data;

    public RespWrapVo() {
    }

    public RespWrapVo(T data) {
        this.data = data;
    }

    public RespWrapVo(String code, String message) {
        this(code, message, null);
    }

    public RespWrapVo(String code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public RespWrapVo(IRespEnum respEnum) {
        this(respEnum, null, null);
    }

    public RespWrapVo(IRespEnum respEnum, String appendMsg) {
        this(respEnum, appendMsg, null);
    }

    public RespWrapVo(IRespEnum respEnum, T data) {
        this(respEnum, null, data);
    }

    public RespWrapVo(IRespEnum respEnum, String appendMsg, T data) {
        code = respEnum.getCode();
        message = respEnum.getMessage() + (StringUtils.isBlank(appendMsg) ? "" : " : " + appendMsg);
        this.data = data;
    }

    public static boolean isResponseValid(RespWrapVo respWrapVo) {
        if (respWrapVo == null) {
            log.error("Response is null.");
            return false;
        }

        if (!StringUtils.equalsIgnoreCase(respWrapVo.getCode(), RespCodeEnum.SUCCESS.getCode())) {
            log.error("Errror response: {}", JSONObject.toJSONString(respWrapVo));
            return false;
        }
        return true;
    }

    public static boolean isResponseValidWithData(RespWrapVo respWrapVo) {
        if (respWrapVo == null) {
            log.error("Response is null.");
            return false;
        }

        if (!StringUtils.equalsIgnoreCase(respWrapVo.getCode(), RespCodeEnum.SUCCESS.getCode())) {
            log.error("Errror response: {}", JSONObject.toJSONString(respWrapVo));
            return false;
        }

        if (respWrapVo.getData() == null) {
            log.error("no data from response, {}", JSONObject.toJSONString(respWrapVo));
            return false;
        }
        return true;
    }

}