package com.kerryprops.kip.bill.common.enums;

import java.util.List;

public enum BillPaymentStatus {
    TO_BE_PAID(0, "待支付"),
    PAYING(1, "支付中"),
    PAID(2, "已支付"),
    PART_PAID(3, "<PERSON><PERSON>部分核销"),

    CANCEL(4, "已取消"),
    DIRECT_DEBIT_PAYING(5, "代扣中"),
    DIRECT_DEBIT_PAID(6, "代扣成功"),
    DIRECT_DEBIT_FAILED(7, "代扣失败"),
    ;

    public static final List<BillPaymentStatus> DIRECT_DEBIT_STATUSES
            = List.of(DIRECT_DEBIT_PAYING, DIRECT_DEBIT_PAID, DIRECT_DEBIT_FAILED);

    public static final String PAYMENT_STATUS_PAID = "已付款";

    public static final String PAYMENT_STATUS_TO_BE_PAID = "未付款";

    public static final String PAYMENT_STATUS_PAYING = "支付中";

    private final Integer code;

    private final String info;

    BillPaymentStatus(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
