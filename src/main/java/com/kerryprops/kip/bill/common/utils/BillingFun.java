package com.kerryprops.kip.bill.common.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.Objects;
import java.util.function.Function;
import java.util.function.Supplier;

import static com.kerryprops.kip.bill.common.constants.AppConstants.DEFAULT_ZONE_ID;

@Slf4j
public abstract class BillingFun {

    public static final Function<ZonedDateTime, String> zonedDatetimeStrFun = t
            -> t.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME.withZone(DEFAULT_ZONE_ID));

    public static final Function<ZonedDateTime, String> excelZonedDatetimeFun = t
            -> t.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").withZone(DEFAULT_ZONE_ID));

    public static final Function<String, ZonedDateTime> str2ZonedDatetimeFun = s
            -> ZonedDateTime.parse(s, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").withZone(DEFAULT_ZONE_ID));

    public static <E extends Throwable> void exceptionToVoid(ExceptionalConsumer<E> supplier) {
        try {
            supplier.accept();
        } catch (Throwable e) {
            log.error("exceptionToVoid catch error:", e);
        }
    }

    public static <T, E extends Throwable> T exceptionToNull(ExceptionalSupplier<T, E> supplier) {
        try {
            return supplier.get();
        } catch (Throwable e) {
            log.error("exceptionToNull catch error:", e);
            return null;
        }
    }

    /**
     * 对数据源导出为excel文件
     */
    public static void exportByEasyExcel(HttpServletResponse response, Collection<?> exportList
            , Class objClazz, String fileName, String sheetName) {
        try {
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename="
                    + URLEncoder.encode(fileName + ".xlsx", StandardCharsets.UTF_8));

            // 设置内容水平居中
            WriteCellStyle writeCellStyle = new WriteCellStyle();
            writeCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(writeCellStyle, writeCellStyle);

            EasyExcel.write(response.getOutputStream(), objClazz)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 自动列宽
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    .sheet(sheetName).doWrite(exportList);

            log.info("【EasyExcel】导出Excel完成，文件名：[{}]", fileName);
        } catch (IOException e) {
            log.error("【EasyExcel】导出Excel异常", e);
            throw new RuntimeException("【EasyExcel】导出Excel失败，请联系网站管理员！");
        }
    }

    public interface ExceptionalSupplier<T, E extends Throwable> {

        T get() throws E;

    }

    public interface ExceptionalConsumer<E extends Throwable> {

        void accept() throws E;

    }

    public interface ValidationSupplier extends Supplier<Boolean> {

        default ValidationSupplier andThen(ValidationSupplier after) {
            Objects.requireNonNull(after);
            return () -> get() && after.get();
        }

    }

}
