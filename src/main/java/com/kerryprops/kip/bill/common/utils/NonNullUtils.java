package com.kerryprops.kip.bill.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.function.Function;

import static java.util.Optional.ofNullable;

/**
 * 内容非空处理工具类
 *
 * <AUTHOR>
 * @date 2023-7-27
 */
public class NonNullUtils {

    /**
     * 处理非空字符串
     */
    public static Function<String, String> nonNullStringFunction = (String originString)
            -> ofNullable(originString).orElse(StringUtils.EMPTY);

}
