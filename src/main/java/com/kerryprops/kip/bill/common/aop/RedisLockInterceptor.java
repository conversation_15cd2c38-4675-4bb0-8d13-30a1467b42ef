package com.kerryprops.kip.bill.common.aop;

/***********************************************************************************************************************
 * Project - decoration-review-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 07/21/2021 15:35
 **********************************************************************************************************************/

import com.alibaba.fastjson.JSON;
import com.kerryprops.kip.bill.common.exceptions.AppException;
import com.kerryprops.kip.bill.common.utils.UuidUtils;
import com.kerryprops.kip.bill.dao.entity.AptBillOperator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

import java.lang.reflect.Method;
import java.util.Objects;

import static com.kerryprops.kip.bill.common.constants.AppConstants.APT_BILL_PUSH_REDIS_KEY_PREFIX;
import static com.kerryprops.kip.bill.log4j.BSConversationFilter.setLoggingContext;

@Slf4j
@Aspect
@Configuration
public class RedisLockInterceptor {

    @Autowired
    RedisTemplate<String, String> redisTemplate;

    @Around("execution(public * *(..)) && @annotation(RedisLock)")
    public Object interceptor(ProceedingJoinPoint pjp) throws Throwable {
        MethodSignature signature = (MethodSignature) pjp.getSignature();
        Method method = signature.getMethod();
        RedisLock lock = method.getAnnotation(RedisLock.class);
        if (StringUtils.isEmpty(lock.key())) {
            throw new RuntimeException("lock key can't be null...");
        }

        StringBuilder lockKey = new StringBuilder(lock.key());
        Boolean locked = Boolean.FALSE;

        try {
            Object[] args = pjp.getArgs();
            if (APT_BILL_PUSH_REDIS_KEY_PREFIX.equals(lockKey.toString()) && Objects.nonNull(args) && args.length > 0) {
                for (Object arg : args) {
                    if (arg instanceof AptBillOperator && AptBillOperator.PUSH_SELECTED.equals((arg))) {
                        log.info("redis_lock not_lock_for_push_selected args=[{}]", JSON.toJSONString(args));
                        setLoggingContext();
                        return pjp.proceed();
                    }
                }
            }

            if (Objects.nonNull(lock.suffixArgIndexArray()) && lock.suffixArgIndexArray().length > 0) {
                int[] lockSuffixArgIndexArray = lock.suffixArgIndexArray();
                for (int lockSuffixArgIndex : lockSuffixArgIndexArray) {
                    lockKey.append(":").append(args[lockSuffixArgIndex].toString());
                }
            }

            locked = redisTemplate.opsForValue().setIfAbsent(lockKey.toString(), UuidUtils.getUuid()
                    , lock.expire(), lock.timeUnit());
            log.info("lock redis with key: {}", lockKey);

            if (locked) {
                setLoggingContext();
                return pjp.proceed();
            } else {
                throw AppException.error(BillErrorEnum.DRAW_CONCURRENT_OPERATION);
            }
        } finally {
            if (locked) {
                log.info("delete redis lock: {}", lockKey);
                redisTemplate.delete(lockKey.toString());
            }
        }
    }

}
