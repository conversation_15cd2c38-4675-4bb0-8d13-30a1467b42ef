package com.kerryprops.kip.bill.common.utils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/***********************************************************************************************************************
 * Project - decoration-review-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 07/23/2021 11:50
 **********************************************************************************************************************/

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class StringFormatUtils {

    private static final Pattern pattern = Pattern.compile("\\{(.*?)\\}");

    public static String format(String content, Map<String, Object> param) {
        if (Objects.isNull(param)) {
            return content;
        }
        try {
            Matcher matcher = pattern.matcher(content);
            while (matcher.find()) {
                String key = matcher.group();
                String keyClone = key.substring(1, key.length() - 1).trim();
                Object value = param.get(keyClone);
                if (Objects.nonNull(value)) {
                    content = content.replace(key, value.toString());
                }
            }
        } catch (Exception e) {
            return null;
        }
        return content;
    }

    /**
     * 判断字符串是否是邮箱
     */
    public static boolean isEmailFormat(String content) {
        final String EMAIL_REGEX = "^(\\w+([-.][A-Za-z0-9]+)*){3,18}@\\w+([-.][A-Za-z0-9]+)*\\.\\w+([-.][A-Za-z0-9]+)*$";
        return Pattern.matches(EMAIL_REGEX, content);
    }

}
