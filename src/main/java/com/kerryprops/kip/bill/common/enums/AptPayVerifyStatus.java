package com.kerryprops.kip.bill.common.enums;

/**
 * 公寓小区-收款记录，支付审核状态
 *
 * @auth <PERSON><PERSON><PERSON>
 * @Date 2023-2-27 12:03:08
 */
public enum AptPayVerifyStatus {

    TO_BE_VERIFIED(0, "未复核"),
    VERIFIED(1, "已复核");

    int code;

    String info;

    AptPayVerifyStatus(int code, String info) {
        this.code = code;
        this.info = info;
    }

    public static AptPayVerifyStatus fromIndex(int code) {
        for (AptPayVerifyStatus s : AptPayVerifyStatus.values()) {
            if (s.code == code) {
                return s;
            }
        }
        throw new UnsupportedOperationException("can't parse into agreement signType from code: " + code);
    }

    public int getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
