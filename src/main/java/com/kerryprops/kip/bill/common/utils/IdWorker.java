package com.kerryprops.kip.bill.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.net.InetAddress;

@Slf4j
public class IdWorker {

    private static final long TOTAL_BITS = 64;

    private static final long EPOCH = 1581932106009L; // 时间起始标记点，作为基准 2020-11-19T16:19:23.985

    private static final long WORKER_ID_BITS = 16L; // 机器标识位数 k8s 集群部署/16 后16位唯一

    private static final long MAX_WORKER_ID = ~(-1L << WORKER_ID_BITS);// 机器最大id 2^16-1

    private static final long SEQUENCE_BITS = 6L; // 毫秒内自增位

    private static final long WORKER_ID_SHIFT = SEQUENCE_BITS; // 12

    private static final long TIMESTAMP_LEFT_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS; // 22

    private static final long SEQUENCE_MASK = ~(-1L << SEQUENCE_BITS); // 6, 111111,

    private static final String IP_POINT = "\\.";

    private static final String JVM_SERVER_IP = "server.ip";

    private static String vmIp;

    private static final IdWorker flowIdWorker = new IdWorker(workerId());

    private final long workerId;

    private long sequence = 0L; // 0，并发控制

    private long lastTimestamp = -1L;

    private IdWorker(long workerId) {
        if (workerId > MAX_WORKER_ID || workerId < 0) {
            throw new IllegalArgumentException(
                    String.format("worker Id can't be greater than %d or less than 0", MAX_WORKER_ID));
        }
        this.workerId = workerId;
    }

    public static IdWorker getFlowIdWorkerInstance() {
        return flowIdWorker;
    }

    public static String getVmIp() {
        if (StringUtils.isEmpty(vmIp)) {
            try {
                String vmIp = System.getProperty(JVM_SERVER_IP);
                if (!StringUtils.isEmpty(vmIp)) {
                    IdWorker.vmIp = vmIp;
                    return IdWorker.vmIp;
                }

                if (StringUtils.isEmpty(IdWorker.vmIp)) {
                    IdWorker.vmIp = InetAddress.getLocalHost().getHostAddress();
                }
            } catch (Exception e) {
                log.info("get ip failed", e);
                vmIp = "127.0.0.1";
            }
        }
        return vmIp;
    }

    public static long getVmIpToLong() {
        String ip = getVmIp();
        String[] ipOctets = ip.split(IP_POINT);
        return (Long.parseLong(ipOctets[2]) << 8) + Long.parseLong(ipOctets[3]);
    }

    public String nextStrId() {
        return Long.toString(nextId());
    }

    public synchronized long nextId() {
        long timestamp = timeGen();
        if (this.lastTimestamp == timestamp) { // 如果上一个timestamp与新产生的相等，则sequence加一(0-64循环); 对新的timestamp，sequence从0开始
            this.sequence = this.sequence + 1 & SEQUENCE_MASK;
            if (this.sequence == 0) {
                timestamp = this.tilNextMillis(this.lastTimestamp);// 重新生成timestamp
            }
        } else {
            this.sequence = 0;
        }

        if (timestamp < this.lastTimestamp) {
            log.info(String.format("clock moved backwards.Refusing to generate id for %d milliseconds",
                    (this.lastTimestamp - timestamp)));
            throw new RuntimeException(
                    String.format("clock moved backwards.Refusing to generate id for %d milliseconds",
                            (this.lastTimestamp - timestamp)));
        }

        this.lastTimestamp = timestamp;
        return ((timestamp - EPOCH) << TIMESTAMP_LEFT_SHIFT) | (this.workerId << WORKER_ID_SHIFT) | this.sequence;
    }

    private static long timeGen() {
        return System.currentTimeMillis();
    }

    private static long workerId() {
        return IdWorker.getVmIpToLong();
    }

    private long tilNextMillis(long lastTimestamp) {
        long timestamp = timeGen();
        while (timestamp <= lastTimestamp) {
            timestamp = timeGen();
        }
        return timestamp;
    }

}
