package com.kerryprops.kip.bill.common.aop;

/***********************************************************************************************************************
 * Project - decoration-review-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 07/21/2021 15:28
 **********************************************************************************************************************/

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface RedisLock {

    /**
     * redis lock key
     *
     * @return
     */
    String key() default "";

    /**
     * 过期时间
     *
     * @return
     */
    long expire();

    /**
     * 超时时间单位
     *
     * @return
     */
    TimeUnit timeUnit() default TimeUnit.MINUTES;

    /**
     * 涉及入参位次，例：{0,2}表示，将第0个、第2个入参引入，在redis锁中使用
     */
    int[] suffixArgIndexArray() default {};

}
