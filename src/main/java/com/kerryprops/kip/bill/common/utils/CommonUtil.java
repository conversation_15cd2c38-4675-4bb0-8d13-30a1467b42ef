package com.kerryprops.kip.bill.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import java.util.HashSet;
import java.util.Set;


/**
 * 通用静态方法处理工具
 */
@Deprecated
@Slf4j
public class CommonUtil {

    /**
     * 将非空属性从source拷贝到target中
     *
     * @param source
     * @param target
     */
    @Deprecated
    public static <S, T> void copyPropertiesIgnoreNull(S source, T target) {
        BeanUtils.copyProperties(source, target, getNullPropertyNames(source));
    }

    public static <T extends Comparable<T>> int comparePossiblyNull(T aThis, T aThat) {
        int result = 1;
        int BEFORE = -1;
        int AFTER = 1;

        if (aThis != null && aThat != null) {
            result = aThis.compareTo(aThat);
        } else {
            //at least one reference is null - special handling
            if (aThis == null && aThat == null) {
                //do nothing - they are not distinct
            } else if (aThis == null && aThat != null) {
                result = BEFORE;
            } else if (aThis != null && aThat == null) {
                result = AFTER;
            }
        }
        return result;
    }

    public static long parseLongQuietly(String str) {
        long l = 0;
        try {
            l = Long.parseLong(str);
        } catch (Exception e) {
            log.error("fail to parse {} into long. error={}", str, e.getMessage());
        }
        return l;
    }

    /**
     * 获取一个对象中的空值属性列表
     *
     * @param target
     * @param <T>
     * @return
     */
    @Deprecated
    private static <T> String[] getNullPropertyNames(T target) {
        BeanWrapper src = new BeanWrapperImpl(target);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

}