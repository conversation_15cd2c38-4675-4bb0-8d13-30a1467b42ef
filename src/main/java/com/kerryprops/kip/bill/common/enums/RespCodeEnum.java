package com.kerryprops.kip.bill.common.enums;

/**
 * 系统服务的响应代码,此枚举定义的最通用的相关代码,各子项目可进行复用
 * 代码定义规则如下:
 * 1. 6位数字组成的字符串
 * 2. 0开头为功能性的保留区间代码
 * 3. 1开头至3开头为业务区间代码
 * 4. 4开头为请求方规范及参数相关代码
 * 5. 5开头为服务方规范及响应相关代码
 */
public enum RespCodeEnum implements IRespEnum {

    SUCCESS("000000", "成功"),

    //业务错误码
    NO_AUTH("100000", "未绑定授权用户"),

    LOGIN_FAILED("100001", "登录失败,请检查用户名和密码"),
    NOT_FOUND("100002", "资源不存在"),
    CREATE_ERROR("100003", "创建失败"),
    UPDATE_ERROR("100004", "更新失败"),
    DELETE_ERROR("100005", "删除失败"),
    SYNC_PAYING_BILL_ERROR("100005", "支付中，无法同步"),
    SYNC_PAID_AND_NOT_JDE_VERIFIED_BILL_ERROR("100006", "已支付账单，非JDE核销场景无需同步"),
    EMPTY_CONTENT_EXPORT_EXCEL("100007", "暂无数据"),
    PROJECT_ID_REQUIRED("100008", "projectId is required"),
    AGREEMENT_NO_REQUIRED("100009", "agreementNo is required"),
    AGREEMENT_NOT_EXISTS("100010", "agreement not exists"),
    BILL_CLOSING_MONTH_REQUIRED("100011", "closingMonth is required"),
    BATCH_NOT_EXISTS("100012", "direct debit batch not exists"),
    ONLY_AWAIT_BATCH_DELETABLE("100013", "only batch in await status is deletable"),
    DUPLICATED_WITHHOLDING_BATCH("100014", "当前日期已有订单批，同一日期不可生成多个代扣订单批"),
    BATCH_BILL_NOT_EXISTS("100015", "direct debit batch bill not exists"),
    DIRECT_DEBITS_BILL_NOT_FOUND("100016", "暂无可代扣账单"),
    TEST_BILL_TOTAL_AMOUNT_PROHIBITIVE("100017", "test env bill total amount is prohibitive" +
            ", should not more than 0.3RMB"),
    DIRECT_DEBITS_BILL_IS_REQUIRED("100018", "账单号不能为空"),
    DIRECT_DEBITS_BILL_NOT_VALID("100019", "批次的账单号错误"),
    UN_SUPPORT_PUSH_DEBITS_BILL("100020", "批次不支持发起代扣"),
    CLOSING_MONTH_FORMAT_NOT_VALID("100021", "账单截止日期格式不正确"),
    IMPORT_BILL_ERROR("119999", "import bills failed"),
    IMPORT_BILL_PARTIAL_ERROR("120000", "import bills partial failed"),
    METHOD_SIGN_VALID_ERROR("120001", "签名验证错误"),
    IMPORT_DOC_TYPE_ERROR("120005", "documentType不正确"),
    IMPORT_FILE_FORMAT_ERROR("120006", "账单文件格式不正"),
    IMPORT_BILL_PDF_FAILED("120008", "账单PDF下载失败"),
    IMPORT_BILL_DUPLICATED("120009", "账单重复"),

    //参数错误码
    BAD_REQUEST("400000", "请求参数有误"),
    JWT_INVALID("400001", "jwt无效"),
    JWT_NOT_EXISTS("400002", "jwt不存在"),
    JWT_EXPIRED("400003", "jwt过期"),
    JWT_ERROR("400004", "jwt未知错误"),
    JWT_LOGOUT("400005", "已退出登录,请重新登录"),
    PARAM_CHECK_ERROR("400006", "参数校验异常"),
    AN8_ERROR("400007", "您的帐号还未被授权查看对应账单，请联系相关嘉里客服进行处理"),
    AN8_INVOICE_ERROR("400010", "无授权可查看的发票，请联系嘉里方申请权限"),

    NOT_LOGIN("400008", "用户未登录"),
    UPDATE_DELETE_ERROR("400009", "不允许更新已删除数据"),

    //系统错误码
    UNKNOWN_ERROR("500000", "未知系统错误"),
    ;

    private final String code;

    private final String message;

    RespCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }
}