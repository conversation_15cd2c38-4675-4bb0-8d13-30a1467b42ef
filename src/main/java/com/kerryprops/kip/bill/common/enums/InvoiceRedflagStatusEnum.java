package com.kerryprops.kip.bill.common.enums;

import lombok.Getter;

/**
 * 发票红冲状态
 *
 * <AUTHOR>
 * @date 2023-4-24
 */
public enum InvoiceRedflagStatusEnum {

    // 默认（非红冲）
    RED_FLAG_DEFAULT(0),

    // 待红冲
    TO_BE_RED_FLAG(1),

    // 部分待红冲
    TO_BE_PARTLY_RED_FLAG(2),

    // 蓝票已红冲
    RED_FLAG_RED_STATUS(3),

    // 蓝票部分已红冲
    RED_FLAG_PARTLY_RED_STATUS(4),

    // 红冲票
    RED_FLAG_RED_INVOICE(5);

    @Getter
    int index;

    InvoiceRedflagStatusEnum(int index) {
        this.index = index;
    }

    public String getIndexStr() {
        return String.valueOf(index);
    }
}
