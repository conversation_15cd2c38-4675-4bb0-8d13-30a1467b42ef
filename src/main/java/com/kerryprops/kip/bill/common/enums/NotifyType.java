package com.kerryprops.kip.bill.common.enums;

import lombok.Getter;

import java.util.stream.Stream;

public enum NotifyType {
    EMAIL(1),
    SMS(2),
    MESSAGE(3);

    @Getter
    int index;

    NotifyType(int index) {
        this.index = index;
    }

    public static NotifyType fromIndex(int index) {
        return Stream.of(NotifyType.values()).filter(nt -> index == nt.index)
                .findFirst().orElseThrow(() -> new RuntimeException("发送状态有误"));
    }

}
