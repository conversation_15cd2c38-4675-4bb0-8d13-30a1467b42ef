package com.kerryprops.kip.bill.common.utils;

import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 数据范围工具类
 *
 * <AUTHOR>
 * @date 2023-5-8
 */
@Slf4j
public class DataScopeUtils {

    /**
     * 根据楼盘id获取bu列表
     */
    public static List<String> getBusByProjectId(HiveAsClient hiveAsClient, String projectId) {
        if (StringUtils.isEmpty(projectId) || StringUtils.isEmpty(projectId.trim())) {
            final String PROJECT_ID_EMPTY_TIPS = "projectId_not_specified";
            log.info(PROJECT_ID_EMPTY_TIPS);
            throw new RuntimeException(PROJECT_ID_EMPTY_TIPS);
        }

        LoginUser loginUser = UserInfoUtils.getUser();
        if (Objects.isNull(loginUser)) {
            final String USER_NOT_LOGIN_TIPS = "user_not_login";
            log.info(USER_NOT_LOGIN_TIPS);
            throw new RuntimeException(USER_NOT_LOGIN_TIPS);
        }

        List<String> searchBus = hiveAsClient.convertToJdeBus(new String[]{projectId.trim()});
        if (loginUser.isSuperAdmin()) {
            return searchBus;
        }

        List<String> maxBus = hiveAsClient.convertToJdeBus(loginUser.toBuildingIdList());
        if (CollectionUtils.isEmpty(maxBus)) {
            return Collections.emptyList();
        }

        searchBus.retainAll(maxBus);
        return searchBus;
    }

}
