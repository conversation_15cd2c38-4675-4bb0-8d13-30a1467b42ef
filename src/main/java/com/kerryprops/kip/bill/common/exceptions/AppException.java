package com.kerryprops.kip.bill.common.exceptions;

import com.kerryprops.kip.bill.common.aop.BillErrorEnum;
import com.kerryprops.kip.bill.common.enums.IRespEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * 自定义异常类
 */
@Getter
@Setter
public class AppException extends RuntimeException {

    private String code;

    public AppException(String code, String msg) {
        super(msg);
        this.code = code;
    }

    public AppException(IRespEnum codeEnum) {
        super(codeEnum.getMessage());
        this.code = codeEnum.getCode();
    }

    public AppException(IRespEnum codeEnum, String appendMsg) {
        super(codeEnum.getMessage() + " : " + appendMsg);
        this.code = codeEnum.getCode();
    }

    public static AppException error(BillErrorEnum iException) {
        return new AppException(String.valueOf(iException.getCode()), iException.getMessage());
    }

}