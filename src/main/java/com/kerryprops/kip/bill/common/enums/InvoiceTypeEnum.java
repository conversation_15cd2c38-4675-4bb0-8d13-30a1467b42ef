package com.kerryprops.kip.bill.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - invoice-service
 *  发票类型枚举类
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 08/03/2021 10:55
 **********************************************************************************************************************/

@Getter
public enum InvoiceTypeEnum {

    C("c", "gvat", "税控纸质普通发票"),
    S("s", "svat", "税控纸质专用发票"),
    CE("ce", "gvate", "税控电子普通发票"),
    SE("se", "svate", "税控电子专用发票"),
    QC("qc", "gvatq", "数电普通发票"),
    QS("qs", "svatq", "数电专用发票"),
    CZ("cz", "gvatz", "数电纸质普通发票"),
    SZ("sz", "svatz", "数电纸质专用发票");

    /**
     * 票易通的发票类型码
     */
    private final String code;

    /**
     * kip平台的发票类型码
     */
    private final String kipCode;

    private final String desc;

    InvoiceTypeEnum(String code, String kipCode, String desc) {
        this.code = code;
        this.kipCode = kipCode;
        this.desc = desc;
    }

    /**
     * 通过kip平台的发票类型，获取发票枚举
     *
     * @param kipCode
     * @return
     */
    public static InvoiceTypeEnum getByKipCode(String kipCode) {
        for (InvoiceTypeEnum e : InvoiceTypeEnum.values()) {
            if (Objects.equals(e.getKipCode(), kipCode)) {
                return e;
            }
        }
        return null;
    }

    /**
     * 通过票易通平台的发票类型，获取发票枚举
     *
     * @param code
     * @return
     */
    public static InvoiceTypeEnum getByCode(String code) {
        for (InvoiceTypeEnum e : InvoiceTypeEnum.values()) {
            if (Objects.equals(e.getCode(), code)) {
                return e;
            }
        }
        return null;
    }

    public static InvoiceTypeEnum fromDesc(String desc) {
        return Arrays.stream(InvoiceTypeEnum.values()).filter(t -> t.desc.equalsIgnoreCase(desc)).findFirst()
                .orElseThrow(() -> new RuntimeException("发票类型有误"));
    }

    public static InvoiceTypeEnum fromKipCode(String kipCode) {
        return Arrays.stream(InvoiceTypeEnum.values()).filter(t -> t.kipCode.equalsIgnoreCase(kipCode)).findFirst()
                .orElseThrow(() -> new RuntimeException("发票kipCode有误"));
    }

}
