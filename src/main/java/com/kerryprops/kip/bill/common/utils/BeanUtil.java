package com.kerryprops.kip.bill.common.utils;

import com.github.dozermapper.core.Mapper;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 自定义bean工具类，用于bean的属性拷贝
 *
 * <AUTHOR> 2019-10-19
 **/
@Component
public class BeanUtil {

    private static Mapper mapperCache;

    private BeanUtil(Mapper mapper) {
        setMapperCache(mapper);
    }

    public static <T> T copy(Object from, Class<T> to) {
        Objects.requireNonNull(from, "from can't be null");
        Objects.requireNonNull(to, "to can't be null");

        return getMapper().map(from, to);
    }

    private static Mapper getMapper() {
        if (mapperCache == null) {
            throw new IllegalStateException("mapperCache not init");
        }
        return mapperCache;
    }

    private static void setMapperCache(Mapper mapperCache) {
        BeanUtil.mapperCache = mapperCache;
    }

}

