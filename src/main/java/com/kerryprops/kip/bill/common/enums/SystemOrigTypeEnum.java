package com.kerryprops.kip.bill.common.enums;

import lombok.Getter;

/**
 * 系统来源类型类型枚举类
 *
 * <AUTHOR>
 * @date 2023-10-23
 */
@Getter
public enum SystemOrigTypeEnum {

    INTERFACE("0", "接口传入"),
    PAGE_IMPORT("1", "页面导入"),
    MANUAL("2", "手工开票");

    private final String code;

    private final String desc;

    SystemOrigTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
