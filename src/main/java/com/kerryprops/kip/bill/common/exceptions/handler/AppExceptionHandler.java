package com.kerryprops.kip.bill.common.exceptions.handler;

import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.exceptions.AppException;
import com.kerryprops.kip.bill.common.exceptions.CounterCashierBizException;
import com.kerryprops.kip.bill.common.exceptions.ExceptionResource;
import com.kerryprops.kip.bill.common.exceptions.ImportBillException;
import com.kerryprops.kip.bill.common.exceptions.RestInternalServiceException;
import com.kerryprops.kip.bill.common.exceptions.RestInvalidParamException;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.log4j.BSConversationFilter;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 统一异常处理
 */
@Slf4j
@ControllerAdvice
public class AppExceptionHandler {

    @ExceptionHandler(CounterCashierBizException.class)
    @ResponseBody
    public ExceptionResource handleException(CounterCashierBizException e, HttpServletResponse response) {
        ExceptionResource exceptionResource = new ExceptionResource();
        exceptionResource.setErrorCode(e.getErrorCode());
        exceptionResource.setErrorText(e.getMessage());
        exceptionResource.setCorrelationId(BSConversationFilter.getCorrelationId());
        response.setStatus(HttpStatus.BAD_REQUEST.value());
        return exceptionResource;
    }

    @ExceptionHandler(RestInvalidParamException.class)
    @ResponseBody
    public ExceptionResource handleException(RestInvalidParamException e, HttpServletResponse response) {
        ExceptionResource exceptionResource = new ExceptionResource();
        exceptionResource.setErrorCode(e.getCode());
        exceptionResource.setErrorText(e.getMessage());
        exceptionResource.setCorrelationId(BSConversationFilter.getCorrelationId());
        response.setStatus(HttpStatus.BAD_REQUEST.value());
        return exceptionResource;
    }

    @ExceptionHandler(RestInternalServiceException.class)
    @ResponseBody
    public ExceptionResource handleException(RestInternalServiceException e, HttpServletResponse response) {
        ExceptionResource exceptionResource = new ExceptionResource();
        exceptionResource.setErrorText(HttpStatus.INTERNAL_SERVER_ERROR.getReasonPhrase());
        exceptionResource.setCorrelationId(BSConversationFilter.getCorrelationId());
        response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        return exceptionResource;
    }

    @ExceptionHandler(ImportBillException.class)
    @ResponseBody
    public ExceptionResource handleException(ImportBillException e, HttpServletResponse response) {
        ExceptionResource exceptionResource = new ExceptionResource();
        exceptionResource.setErrorText(e.getMessage());
        exceptionResource.setErrorCode(e.getCode());
        exceptionResource.setErrorCategory("TENANT_BILL_DATA_SYNC");
        exceptionResource.setImportedBillCount(e.getImportedBillCount());
        if (CollectionUtils.isNotEmpty(e.getDetailErrorInfos())) {
            exceptionResource.setDetailErrorInfo(e.getDetailErrorInfos());
        }
        exceptionResource.setCorrelationId(BSConversationFilter.getCorrelationId());
        response.setStatus(HttpStatus.BAD_REQUEST.value());
        return exceptionResource;
    }

    @ExceptionHandler(AppException.class)
    @ResponseBody
    public RespWrapVo<Void> handleException(AppException e) {
        log.error("====[  自定义异常捕获  ]=====", e);
        return new RespWrapVo<>(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(Exception.class)
    @ResponseBody
    public RespWrapVo<Void> handleException(Exception e) {
        log.error("====[  拦截到未知异常  ]=====", e);
        return new RespWrapVo<>(RespCodeEnum.UNKNOWN_ERROR.getCode(), StringUtils.isNotEmpty(e.getMessage()) ? e.getMessage() : RespCodeEnum.UNKNOWN_ERROR.getMessage());
    }

    @ExceptionHandler(BindException.class)
    @ResponseBody
    public RespWrapVo<Void> handleException(BindException e) {
        log.error("====[  参数校验异常  ]=====", e);
        return new RespWrapVo<>(RespCodeEnum.PARAM_CHECK_ERROR.getCode(), RespCodeEnum.PARAM_CHECK_ERROR.getMessage() + ": " + resolveException(e));
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public ExceptionResource handleException(MethodArgumentNotValidException e, HttpServletResponse response) {
        log.error("====[  自定义参数校验异常  ]=====", e);
        ExceptionResource exceptionResource = new ExceptionResource();
        exceptionResource.setErrorText(resolveException(e));
        exceptionResource.setErrorCode("120003");
        exceptionResource.setErrorCategory("TENANT_BILL_DATA_SYNC");
        exceptionResource.setImportedBillCount(0);
        exceptionResource.setCorrelationId(BSConversationFilter.getCorrelationId());
        response.setStatus(HttpStatus.BAD_REQUEST.value());
        return exceptionResource;
    }
//    public RespWrapVo<Void> handleException(MethodArgumentNotValidException e) {
//        log.error("====[  自定义参数校验异常  ]=====", e);
//        return new RespWrapVo<>(RespCodeEnum.PARAM_CHECK_ERROR.getCode(), RespCodeEnum.PARAM_CHECK_ERROR.getMessage() + ": " + resolveException(e));
//    }

    private String resolveException(Exception e) {
        if (e == null) {
            return "no exception";
        }
        BindingResult br = null;
        if (e instanceof BindException) {
            BindException b = (BindException) e;
            br = b.getBindingResult();
            if (br.getFieldError() == null || br.getFieldErrors().size() <= 0) {
                return "empty exception msg";
            }
        } else if (e instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException m = (MethodArgumentNotValidException) e;
            br = m.getBindingResult();
            if (br.getFieldError() == null || br.getFieldErrors().size() <= 0) {
                return "empty exception msg";
            }
        }
        if (br == null) {
            return "unknown exception: " + e.getMessage();
        }
        String msg = "";
        for (FieldError fieldError : br.getFieldErrors()) {
            if (!StringUtils.isBlank(msg)) {
                msg = ", ";
            }
            msg += fieldError.getDefaultMessage();
        }
        return msg;
    }

}