package com.kerryprops.kip.bill.common.utils;

import com.kerryprops.kip.bill.config.KerryInvoiceProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 发票业务工具类
 *
 * <AUTHOR>
 * @Date 2023-3-30
 */
@Slf4j
public class InvoiceUtils {

    /**
     * 开票状态：失败
     */
    public static final int INVOICE_STATUS_FAILED = 0;

    /**
     * 开票状态：成功
     */
    public static final int INVOICE_STATUS_SUCCESS = 1;

    /**
     * 发票状态：正常
     */
    public static final int STATUS_NORMAL = 1;

    /**
     * 发票状态：作废
     */
    public static final int STATUS_CANCEL = 0;

    private InvoiceUtils() {
    }

    /**
     * 生成签名信息
     */
    public static String getSign(String salesbillNo, String timeStamp, KerryInvoiceProperties kerryInvoiceProperties) {
        //需要加密的参数
        String param = "salesbillno=" + salesbillNo + "&systemid=" + kerryInvoiceProperties.getSystemId()
                + "&systemsecret=" + kerryInvoiceProperties.getSystemSecret() + "&timestamp=" + timeStamp;
        //密文
        return Md5Utils.MD5Encode(param, Md5Utils.UTF_8);
    }


    /**
     * 转换日期格式，20230310转换为2023-03-10
     */
    public static String transDateFormat(String originDate) {
        if (StringUtils.isEmpty(originDate)) {
            log.info("date_from_xforce is_empty.");
            return "1910-01-01"; // 因格式要求，使用假数据填充
        }

        if (originDate.length() < 8) {
            log.info("date_from_xforce is_not_long_enough.");
            return originDate;
        }

        StringBuilder dateStringBuild = new StringBuilder();
        final String SEPARATOR = "-";
        return dateStringBuild.append(originDate, 0, 4)
                .append(SEPARATOR)
                .append(originDate, 4, 6)
                .append(SEPARATOR).append(originDate, 6, 8)
                .toString();
    }


    /**
     * 分隔JDE业务单号为：单据公司(kco)-账单类型(billType)-单据号(doc)-单据付款项(paymentItem)
     * 例：43025RD24006318001 -> [43025, RD, 24006318, 001]
     */
    public static String[] splitJdeSalesbillNo(String salesbillNoJde) {
        if (StringUtils.isEmpty(salesbillNoJde) || salesbillNoJde.length() < 18) {
            throw new RuntimeException("JDE场景业务单号格式异常！");
        }

        String kco = salesbillNoJde.substring(0, 5);
        String billType = salesbillNoJde.substring(5, 7);
        String doc = salesbillNoJde.substring(7, 15);
        String paymentItem = salesbillNoJde.substring(15, 18);
        return new String[]{kco, billType, doc, paymentItem};
    }

}
