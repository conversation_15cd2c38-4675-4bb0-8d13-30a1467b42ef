package com.kerryprops.kip.bill.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class BuConverter {

    private BuConverter() {
    }

    public static List<String> getBuList(String bu) {
        if (StringUtils.isEmpty(bu)) {
            return Collections.emptyList();
        }
        String[] tmp = StringUtils.split(bu, ",");
        return Arrays.stream(tmp).filter(StringUtils::isNotEmpty)
                .map(StringUtils::trim).collect(Collectors.toList());
    }

}
