package com.kerryprops.kip.bill.common.current;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kerryprops.kip.bill.common.constants.AppConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: Bert
 * @date: 2021/5/20 12:30 下午
 */
@Slf4j
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LoginUser implements Serializable {

    private Long userId;

    @Schema(title = "登录来源，C:客户端；B: 企业用户端；S: 嘉里员工端")
    @Builder.Default
    private String fromType = "S";

    @Schema(title = "C端用户的uuid")
    @JsonProperty(value = "cId")
    private String cId;

    @Schema(title = "C端用户的uuid-cid")
    @JsonProperty(value = "cid")
    private String cid;

    @Schema(title = "用户昵称")
    private String nickName;

    @Schema(title = "用户手机号")
    private String phoneNumber;

    @Schema(title = "用户角色")
    private String roles;

    @Schema(title = "操作权限")
    private String scopes;

    @Schema(title = "用户所属公司id，多个值英文逗号分隔")
    private String companyIds;

    @Schema(title = "楼盘ids，多个值英文逗号分隔")
    private String projectIds;

    @Schema(title = "楼栋ids，多个值英文逗号分隔")
    private String buildingIds;

    private String loginAccount;

    public Long getUserId() {
        return userId;
    }

    public String getFromType() {
        return fromType;
    }

    public String getcId() {
        return cId;
    }

    public String getCid2() {
        return cid;
    }

    public String getUniqueUserId() {
        if ("C".equals(fromType)) {
            return cId;
        } else {
            return String.valueOf(userId);
        }
    }

    public String getNickName() {
        nickName = nickName == null ? null : URLDecoder.decode(nickName, StandardCharsets.UTF_8);
        return nickName;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public String getRoles() {
        return roles;
    }

    public String getScopes() {
        return scopes;
    }

    public String getCompanyIds() {
        return companyIds;
    }

    public String getProjectIds() {
        return projectIds;
    }

    public String getBuildingIds() {
        return buildingIds;
    }

    public List<String> toProjectIdList() {
        if (StringUtils.isEmpty(projectIds)) {
            return Collections.emptyList();
        }
        return Arrays.stream(StringUtils.split(projectIds, AppConstants.COMMA)).collect(Collectors.toList());
    }

    public List<String> toBuildingIdList() {
        if (StringUtils.isEmpty(buildingIds)) {
            return Collections.emptyList();
        }
        return Arrays.stream(StringUtils.split(buildingIds, AppConstants.COMMA)).collect(Collectors.toList());
    }

    public List<String> toCompanyIdList() {
        if (StringUtils.isEmpty(companyIds)) {
            return Collections.emptyList();
        }
        return Arrays.stream(StringUtils.split(companyIds, ",")).map(StringUtils::trim).collect(Collectors.toList());
    }

    public List<String> toRoleList() {
        if (StringUtils.isEmpty(roles)) {
            return Collections.emptyList();
        }
        return Arrays.stream(StringUtils.split(roles, ",")).map(StringUtils::trim).collect(Collectors.toList());
    }

    public Boolean isSuperAdmin() {
        return toRoleList().contains("SUPER_ADMIN");
    }

    public Boolean isStaff() {
        return "S".equals(this.fromType);
    }

    public Boolean isTenant() {
        return "B".equals(this.fromType);
    }

    public Boolean isClient() {
        return "C".equals(this.fromType);
    }

}
