package com.kerryprops.kip.bill.common.jpa;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Ops;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.Expressions;

import java.util.List;
import java.util.Optional;

public interface QueryFilter {

    List<Optional<Predicate>> predicates();

    default Predicate toPredicates() {
        return this.predicates()
                .stream()
                .filter(Optional::isPresent)
                .map(Optional::get)
                .reduce((predicate1, predicate2) -> Expressions.booleanOperation(Ops.AND, predicate1, predicate2))
                .orElse(new BooleanBuilder());
    }

}
