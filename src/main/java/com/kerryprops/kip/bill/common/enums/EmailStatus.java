package com.kerryprops.kip.bill.common.enums;

public enum EmailStatus {

    LOCAL_FAILED("本地发送失败"),
    LOCAL_SUCCESS("本地发送成功"),
    SUPPLIER_SEND("供应商发送中"),
    SUPPLIER_FAILED("供应商发送失败"),
    PARTIAL_SUCCESS("供应商部分发送成功"),
    FULL_SUCCESS("供应商全部发送成功");

    private final String message;

    EmailStatus(String message) {
        this.message = message;
    }

    public String getMessage() {
        return this.message;
    }
}
