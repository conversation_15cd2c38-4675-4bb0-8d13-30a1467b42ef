package com.kerryprops.kip.bill.common.current;

import jakarta.servlet.http.HttpServletRequest;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: Bert
 * @Date: 2021/6/1 5:47 下午
 * @Description: default
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class LoginUtils {

    public static final String USER_HTTP_HEADER = "X-User";

    public static LoginUser buildLoginUserFromHttpRequest(HttpServletRequest request) {
        String userInfo = request.getHeader(USER_HTTP_HEADER);
        LoginUser loginUser = StringUtils.isNotBlank(userInfo) ? JsonUtils.stringToObj(userInfo, LoginUser.class) : new LoginUser();
        if (loginUser != null && loginUser.isClient() && loginUser.getcId() == null) {
            loginUser.setCId(loginUser.getCid2());
        }
        return loginUser;
    }

}
