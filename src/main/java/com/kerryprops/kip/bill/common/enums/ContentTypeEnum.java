package com.kerryprops.kip.bill.common.enums;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * kerry bill: 内容类型枚举
 *
 * <AUTHOR>
 * @date 2023-6-13
 */
public enum ContentTypeEnum {
    E_FAPIAO(1, "e-发票"),
    B_BILL(2, "B端账单");

    private final Integer index;

    private final String info;

    ContentTypeEnum(Integer index, String info) {
        this.index = index;
        this.info = info;
    }

    public static ContentTypeEnum fromIndex(int index) {
        return Stream.of(ContentTypeEnum.values()).filter(ct -> Objects.equals(ct.index, index))
                .findFirst().orElseThrow(() -> new RuntimeException("信息类型index有误"));
    }

    public Integer getIndex() {
        return index;
    }

    public String getInfo() {
        return info;
    }
}
