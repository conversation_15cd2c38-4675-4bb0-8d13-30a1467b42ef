package com.kerryprops.kip.bill.common.enums;

import lombok.Getter;

/**
 * apt_pay支付记录取消/未取消状态枚举类
 *
 * <AUTHOR>
 * Created Date - 2024-1-3 19:33:05
 */
@Getter
public enum AptPayDeleteStatusEnum {
    NOT_DELETED(0, "未取消"),
    DELETED(1, "已取消");

    private final Integer code;

    private final String info;

    AptPayDeleteStatusEnum(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

}
