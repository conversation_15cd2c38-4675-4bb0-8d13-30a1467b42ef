package com.kerryprops.kip.bill.common.jpa;

import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.NoRepositoryBean;

@NoRepositoryBean
public interface JpaAndQueryDslExecutor<T, D>
        extends BaseJpaRepository<T, D>, QuerydslPredicateExecutor<T>, JpaSpecificationExecutor<T> {

}
