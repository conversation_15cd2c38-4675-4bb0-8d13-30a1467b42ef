package com.kerryprops.kip.bill.common.aop;

import lombok.Getter;

/**
 * @author: <PERSON>
 * @date: 2021/5/20 3:49 下午
 */
@Getter
public enum BillErrorEnum implements IException {

    PARAMS_ERROR(10500, "请求参数错误!"),
    DRAW_CONCURRENT_OPERATION(199999, "该数据正在处理中，请稍后重试"),
    NOT_FOUND(40400, "未找到该资源!"),
    INTERNAL_SERVER_ERROR(500000, "系统内部异常");

    private final Integer code;

    private final String message;

    BillErrorEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }
}
