package com.kerryprops.kip.bill.common.enums;

import lombok.Getter;

/**
 * apt_pay支付记录回写JDE状态枚举类
 *
 * <AUTHOR>
 * Created Date - 2024-1-3 19:33:05
 */
@Getter
public enum AptPaySendJdeStatusEnum {
    NOT_YET(0, "未写"),
    WRITE_TO_INTERMEDIATE_TABLE_SUCCESS(2, "写入中间表成功"),
    WRITE_TO_INTERMEDIATE_TABLE_FAIL(3, "写入中间表失败"),
    WRITE_TO_JDE_SUCCESS(4, "写入JDE成功"),
    WRITE_TO_JDE_FAIL(5, "写入JDE失败");

    private final Integer code;

    private final String info;

    AptPaySendJdeStatusEnum(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

}
