package com.kerryprops.kip.bill.common.utils.jde;

import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;

@Slf4j
public class OracleSql {

    public static String query(String sql, String columnName) {
        try (
                Connection conn = OracleJdbc.getOracleConnection();
                Statement statement = conn.createStatement();
                ResultSet rs = statement.executeQuery(sql);
        ) {
            while (rs.next()) {
                return rs.getString(columnName);
            }
        } catch (Exception e) {
            log.error("查询Oracle的sql出现错误：" + sql, e.getMessage());
        }
        return null;
    }

    /**
     * insert jde数据库并返回结果
     *
     * @param sql
     * @return
     */
    public static boolean getInsertResult(String sql) {
        try (
                Connection conn = OracleJdbc.getOracleConnection();
                Statement statement = conn.createStatement();
        ) {
            conn.setAutoCommit(true);
            statement.execute(sql);
        } catch (Exception e) {
            log.error("查询Oracle的sql出现错误：" + sql, e.getMessage());
            return false;
        }
        return true;
    }

}
