package com.kerryprops.kip.bill.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DirectDebitAgreementStatus {

    // alipay: TEMP, NORMAL, STOP
    UNKNOWN(0),
    INACTIVE(1),
    ACTIVE(2),
    TERMINATED(3);

    int index;

    public static DirectDebitAgreementStatus fromIndex(int index) {
        for (DirectDebitAgreementStatus s : DirectDebitAgreementStatus.values()) {
            if (s.index == index) {
                return s;
            }
        }
        return UNKNOWN;
    }

}
