package com.kerryprops.kip.bill.feign.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * B/C端：各业务发票信息统一查询响应Vo类
 *
 * <AUTHOR>
 * @date 2023-11-10
 */
@Data
public class InvoiceVo {

    @Schema(title = "发票信息id")
    private Long id;

    @Schema(title = "kerry源业务单号，无平台code拼接")
    private String originalSalesbillNo;

    @Schema(title = "票易通主业务单号 由平台code和业务单号拼接")
    private String salesbillNo;

    @Schema(title = "发票类型：gvat-增值税普通发票 svat-增值税专⽤发票 gvate-增值税电⼦普票发票 svate-增值税电⼦专⽤发票 " +
            "gvatq-数电增值税电子普通发票 svatq-数电增值税电子专用发票 gvatz-数电增值税纸质普通发票 svatz-数电增值税纸质专用发票")
    private String invoiceType;

    @Schema(title = "发票号码")
    private String invoiceNo;

    @Schema(title = "'发票代码'")
    private String invoiceCode;

    @Schema(title = "'开票日期'")
    private String paperDrewDate;

    @Schema(title = "含税金额 ，保留2位小数")
    private BigDecimal amountWithTax;

    @Schema(title = "'不含税金额 ，保留2位小数'")
    private BigDecimal amountWithoutTax;

    @Schema(title = "'税额 ，保留2位小数'")
    private BigDecimal taxAmount;

    @Schema(title = "税率 小数（0.03、0.06....），合并/组合开票时可能为0.05,0.06")
    private String taxRate;

    @Schema(title = "'发票备注'")
    private String remark;

    @Schema(title = "业务单开票状态 1-已上传（开票中） 2-开票成功 3-开票失败")
    private Integer invoiceStatus;

    @Schema(title = "业务单开票失败信息")
    private String invoiceIssueErrorMessage;

    @Schema(title = "发票状态：0未知；1正常；2作废；3已红冲；4红冲发票")
    private int state;

    @Schema(title = "红字信息编号 (红字开票时上传该字段)")
    private String redNotificationNo;

    @Schema(title = "'全电场景：红冲场景：开具原因'")
    private String makingReason;

    @Schema(title = "单据类型 (公寓租金，公寓杂费，停车场)")
    private String billType;

    @Schema(title = "'系统来源：如：KIP-ORDER、KIP-BILLING、ETCP、JDE、IMPORT'")
    private String systemOrig;

    @Schema(title = "'枚举类型：0接口传入，1页面导入，2手工开票'")
    private String systemOrigType;

    @Schema(title = "销方统一信用代码")
    private String sellerNo;

    @Schema(title = "销方银行账号")
    private String sellerBankAccount;

    @Schema(title = "销方地址")
    private String sellerAddress;

    @Schema(title = "销方银行名称")
    private String sellerBankName;

    @Schema(title = "销方名称")
    private String sellerName;

    @Schema(title = "销方税号")
    private String sellerTaxNo;

    @Schema(title = "销方电话")
    private String sellerTel;

    @Schema(title = "接收邮箱 邮箱票易通发")
    private String receiveUserEmail;

    @Schema(title = "接收方电话 短信由各平台发")
    private String receiveUserTel;

    @Schema(title = "购方统一信用代码(如果有则建议传)")
    private String purchaserNo;

    @Schema(title = "购方抬头（专票必填、禁止特殊字符）")
    private String purchaserName;

    @Schema(title = "购方统一信用代码（专票必填，个人购方传身份证信息）")
    private String purchaserTaxNo;

    @Schema(title = "公司地址")
    private String purchaserAddress;

    @Schema(title = "公司电话（专票必填）")
    private String purchaserTel;

    @Schema(title = "银行名称（专票必填、银行名称+账号总长不能超过100字节，一个中文占两个字节，一个英文或数字占一个字节 ，禁止特殊字符）")
    private String purchaserBankName;

    @Schema(title = "'银行账号'")
    private String purchaserBankAccount;

    @Schema(title = "发票路径")
    private String pdfUrl;

    @Schema(title = "'全电场景：全电xml地址'")
    private String xmlUrl;

    @Schema(title = "发票ofd路径")
    private String ofdUrl;

    @Schema(title = "税控票场景：原始发票号码 (红字开票时上传该字段)")
    private String originInvoiceNo;

    @Schema(title = "税控票场景：原始发票代码 (红字开票时上传该字段)")
    private String originInvoiceCode;

    @Schema(title = "扩展字段，票易通 ext1")
    private String ext1;

    @Schema(title = "扩展字段，票易通 ext2")
    private String ext2;

    @Schema(title = "扩展字段，票易通 ext3")
    private String ext3;

    @Schema(title = "扩展字段，票易通 ext4")
    private String ext4;

}
