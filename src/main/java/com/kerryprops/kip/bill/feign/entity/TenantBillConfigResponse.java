package com.kerryprops.kip.bill.feign.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/***********************************************************************************************************************
 * Project - user-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 07/28/2021 09:59
 **********************************************************************************************************************/

@Getter
@Setter
@Schema
@NoArgsConstructor
@AllArgsConstructor
@Data
public class TenantBillConfigResponse implements Serializable {

    @Schema(title = "主键id")
    private Long id;

    @Schema(title = "租户编号")
    private String tenantId;

    @Schema(title = "租户账号id")
    private Long managerId;

    @Schema(title = "租户管理员姓名")
    private String userName;

    @Schema(title = "联系电话")
    private String phoneNumber;

    @Schema(title = "租户账号id")
    private String email;

    @Schema(title = "an8地址号")
    private String address;

    @Schema(title = "创建时间")
    private Date createTime;

}
