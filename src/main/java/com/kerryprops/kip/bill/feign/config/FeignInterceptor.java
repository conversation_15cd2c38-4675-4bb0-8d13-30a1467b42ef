package com.kerryprops.kip.bill.feign.config;

import com.kerryprops.kip.bill.log4j.BSConversationFilter;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(prefix = "feign.client", name = "enabled", havingValue = "true")
public class FeignInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate template) {
        String conversationId = ThreadContext.get(BSConversationFilter.MDC_CONVERSATION_ID_KEY);
//        String correlationId = ThreadContext.get(BSConversationFilter.MDC_CORRELATION_ID_KEY);
        if (StringUtils.isNotEmpty(conversationId)) {
            template.header(BSConversationFilter.CONVERSATION_ID_HEADER, conversationId);
//            template.header(BSConversationFilter.CORRELATION_ID_HEADER, correlationId);
        }
    }

}
