package com.kerryprops.kip.bill.feign.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/***********************************************************************************************************************
 * Project - tenant-admin-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 08/18/2021 16:33
 **********************************************************************************************************************/


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class TenantStaffResponse implements Serializable {

    @Schema(title = "主键id")
    private Integer id;

    @Schema(title = "用户id")
    private String userId;

    @Enumerated(EnumType.STRING)
    @Schema(title = "认证类型")
    private IdentityAuditType auditType;

    @Schema(title = "状态")
    private Integer status;

    @Schema(title = "姓名")
    private String userName;

    @Schema(title = "手机号")
    private String mobile;

    @Schema(title = "楼栋名称")
    private String buildingName;

    @Schema(title = "楼盘id")
    private String projectId;

    @Schema(title = "楼栋id")
    private String buildingNumber;

    @Schema(title = "楼栋单元id")
    private String buildingUnitNumber;

    @Schema(title = "公司名称")
    private String companyName;

    @Schema(title = "公司编号")
    private String companyNumber;

    @Schema(title = "公司单元id")
    private String companyUnitNumber;

    @Schema(title = "房间id")
    private String roomNumber;

    @Schema(title = "jde账单号")
    private String jdeBill;

    @Schema(title = "性别")
    private Integer gender;

    @Schema(title = "职位")
    private String position;

    /**
     * 行政卫权限，0：否；1有
     */
    @Schema(title = "行政卫权限，0：否；1有")
    private Integer healthAuthority;

    /**
     * 是否已授权，0：否；1：有
     */
    @Schema(title = "是否已授权，0：否；1：有")
    private Integer authorizer;

    /**
     * 驳回原因
     */
    @Schema(title = "驳回原因")
    private String remark;

    @Schema(title = "邮箱")
    private String email;

    @Schema(title = "提交时间")
    private LocalDateTime createTime;

    @Schema(title = "审核通过时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date approveTime;

    @Schema(title = "离职时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date departureTime;

}
