package com.kerryprops.kip.bill.feign.clients;

import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.feign.entity.CreateShortUrlCommand;
import com.kerryprops.kip.bill.feign.entity.CreateShortUrlResource;
import com.kerryprops.kip.bill.feign.entity.EmailReplyVo;
import com.kerryprops.kip.bill.feign.entity.EmailSendCommand;
import com.kerryprops.kip.bill.feign.entity.EmailSendCommandV2;
import com.kerryprops.kip.bill.feign.entity.HOAFeesPayFailedReminderCommand;
import com.kerryprops.kip.bill.feign.entity.NotificationPushRequest;
import com.kerryprops.kip.bill.feign.entity.NotificationResource;
import com.kerryprops.kip.bill.feign.entity.PaymentConfirmedCommand;
import com.kerryprops.kip.bill.feign.entity.SendSmsV2Command;
import com.kerryprops.kip.bill.feign.entity.SmsResultVo;
import com.kerryprops.kip.bill.feign.entity.WxTemplateMsgRequestCommand;
import com.kerryprops.kip.bill.webservice.vo.req.EmailResultVo;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.UNKNOWN_ERROR;

/***********************************************************************************************************************
 * Project - hive-view-assembler-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - David Wei
 * Created Date - 06/09/2021 14:43
 **********************************************************************************************************************/
public interface MessageClient {

    Logger logger = LoggerFactory.getLogger(MessageClient.class);

    @PostMapping(value = "/email/send")
    RespWrapVo<EmailReplyVo> sendWithReply(@Valid @RequestBody EmailSendCommand command);

    @PostMapping(value = "/email/alicloud/send")
    RespWrapVo<EmailReplyVo> doSendWithReplyAlicloud(@Valid @RequestBody EmailSendCommand command);

    default RespWrapVo<EmailReplyVo> sendWithReplyAlicloud(EmailSendCommand command) {
        try {
            return this.doSendWithReplyAlicloud(command);
        } catch (Exception e) {
            logger.error("send_with_reply_alicloud_error", e);
            return new RespWrapVo(UNKNOWN_ERROR.getCode(), e.getMessage());
        }
    }

    @PostMapping(value = "email/v2/send")
    RespWrapVo<EmailReplyVo> sendEmailV2(@Valid @RequestBody EmailSendCommandV2 command);

    @Operation(summary = "查询邮件发送状态")
    @GetMapping(value = "/email/status/{requestId}")
    RespWrapVo<EmailResultVo> getSendStatus(@PathVariable("requestId") String requestId);

    @Operation(summary = "发公众号模板消息")
    @PostMapping("/wx-template/send")
    RespWrapVo<Boolean> sendWxTemplateMessage(@RequestBody @Valid WxTemplateMsgRequestCommand command);

    @Operation(summary = "发公众号模板消息")
    @PostMapping("/wx-template/send?msgType=PAYMENT_CONFIRMED")
    RespWrapVo<Boolean> sendPaymentResultWxMessage(@RequestBody @Valid PaymentConfirmedCommand command);

    @Operation(summary = "发公众号模板消息")
    @PostMapping("/wx-template/send?msgType=HOA_FEES_PAY_FAILED_REMINDER")
    RespWrapVo<Boolean> sendPaymentFailedWxMessage(@RequestBody @Valid HOAFeesPayFailedReminderCommand command);

    @PostMapping(value = "/v2/sms/send", produces = MediaType.APPLICATION_JSON_VALUE
            , consumes = MediaType.APPLICATION_JSON_VALUE)
    SmsResultVo sendSmsV2(@RequestBody SendSmsV2Command command);

    @PostMapping(value = "/sms/short_url", produces = MediaType.APPLICATION_JSON_VALUE
            , consumes = MediaType.APPLICATION_JSON_VALUE)
    CreateShortUrlResource createShortUrl(@RequestBody CreateShortUrlCommand command);

    @Operation(summary = "发送通知给指定b端用户")
    @PostMapping("/b/app/notifications/assign_push")
    NotificationResource pushNotification(@RequestBody NotificationPushRequest request);

}
