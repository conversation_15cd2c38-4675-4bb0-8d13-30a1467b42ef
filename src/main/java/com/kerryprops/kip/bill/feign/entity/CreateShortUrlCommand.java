package com.kerryprops.kip.bill.feign.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> 2023-08-09 17:26:11
 **/
@Data
public class CreateShortUrlCommand {

    @NotBlank
    @Pattern(regexp = "^https?://[0-9a-zA-Z._-]+/.*$")
    @Schema(title = "原始链接|长链接", required = true)
    private String longUrl;

    @NotEmpty
    @Schema(title = "联系手机号", required = true)
    private List<@NotBlank String> phoneNumbers;

    @NotNull
    @Schema(title = "账号类型[NOTIFICATION|COMMERCIAL]", required = true)
    private String smsType;

    @Schema(title = "项目id")
    private String projectId;

}
