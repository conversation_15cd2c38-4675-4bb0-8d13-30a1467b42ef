package com.kerryprops.kip.bill.feign.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class WxTemplateMsgCommand {

    @Schema(title = "first")
    private String first;

    @Schema(title = "keyword1")
    private String keyword1;

    @Schema(title = "keyword2")
    private String keyword2;

    @Schema(title = "keyword3")
    private String keyword3;

    @Schema(title = "keyword4")
    private String keyword4;

    @Schema(title = "keyword5")
    private String keyword5;

    @Schema(title = "remark")
    private String remark;

    @Schema(title = "url")
    private String url;

    @Schema(hidden = true)
    private String openId;

    @Schema(hidden = true)
    private String appId;

    @Schema(title = "模板消息类型：ROBOT_DELIVERY_NOTICE, HOA_FEES_REMINDER, PROPERTY_MANAGEMENT_NOTICE, EVENT_REMINDER, PAYMENT_CONFIRMED")
    private TemplateMsgType templateMsgType;

}
