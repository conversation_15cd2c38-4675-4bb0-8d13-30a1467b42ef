package com.kerryprops.kip.bill.feign.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerUserResource {

    @Schema(title = "用户ID")
    private String id;

    @Schema(title = "昵称")
    private String nickName;

    @Schema(title = "手机号码")
    private String phoneNumber;

    @Schema(title = "email")
    private String email;

}
