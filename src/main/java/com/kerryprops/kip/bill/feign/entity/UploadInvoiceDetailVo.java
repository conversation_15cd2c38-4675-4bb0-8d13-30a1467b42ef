package com.kerryprops.kip.bill.feign.entity;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;

public class UploadInvoiceDetailVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(
            title = "明细编号",
            required = true
    )
    private String salesbillItemNo;

    @Schema(
            title = "货物或应税劳务名称 ，禁止特殊字符",
            required = true
    )
    private String itemName;

    @Schema(title = "规格型号， 禁止特殊字符，如果对方填写则传过来")
    private String itemSpec;

    @Schema(title = "单位， 禁止特殊字符，如果对方填写则传过来")
    private String quantityUnit;

    @Schema(
            title = "数量",
            required = true
    )
    private BigDecimal quantity;

    @Schema(
            title = "商品货物税收分类编码（19位税编）",
            required = true
    )
    private String goodsTaxNo;

    @Schema(title = "value = 含税折扣行（传正数）")
    private BigDecimal outterDiscountWithTax;

    @Schema(
            title = "税率 小数（0.03、0.06....）",
            required = true
    )
    private BigDecimal taxRate;

    @Schema(title = "含税金额（如果priceMethod 为1 则传该字段必传） ：数量*单价（amountwithtax-outerdiscountwithtax > 0）")
    private BigDecimal amountWithTax;

    @Schema(title = "不含金额（如果priceMethod 为0 则传该字段必传）")
    private BigDecimal amountWithoutTax;

    @Schema(title = "税额")
    private BigDecimal taxAmount;

    @Schema(title = "含税单价（如果priceMethod 为1 则传该字段必传）")
    private BigDecimal unitPriceWithTax;

    @Schema(title = "单价（如果priceMethod 为0 则传该字段必传）")
    private BigDecimal unitPrice;

    @Schema(title = "是否享受税收优惠政策 0-不1-享受简易征收情况传入：“1”")
    private String taxPre;

    @Schema(title = "税收优惠简易征收情况传入：“按5%简易征收”")
    private String taxPreCon;

    @Schema(title = "零税率标志；标识进行注释例如非0税率；0-出口退税 1-免税 2-不征税 3-普通0税率")
    private String zeroTax;

    /**
     * 数电场景，行业特殊发票扩展信息：不动产销售类/不动产租赁类
     */
    @Schema(title = "不动产详细地址", required = true)
    private String realEstateAddress;

    @Schema(title = "房屋产权证书/不动产权证号码", required = true)
    private String realEstateNo;

    @Schema(title = "跨地市标志，字符 false-否, true-是", required = true)
    private String crossCitySign;

    @Schema(title = "面积单位,平方米-01, 亩-02, ㎡-03, 平方千米-04, 公顷-05, h㎡-06, k㎡-07", required = true)
    private String areaUnit;

    @Schema(title = "不动产地址（省市区）", required = true)
    private String realEstatePlace;

    /**
     * 数电场景，行业特殊发票扩展信息：不动产销售类
     */
    @Schema(title = "土地增值税项目编号", required = true)
    private String landVatItemNo;

    @Schema(title = "不动产单元代码/网签合同备案编码", required = true)
    private String realEstateCode;

    @Schema(title = "核定计税价格", required = true)
    private String taxablePrice;

    @Schema(title = "实际成交含税金额", required = true)
    private String transactionPrice;

    /**
     * 数电场景，行业特殊发票扩展信息：不动产租赁类
     */
    @Schema(title = "租赁期起，yyyyMMdd", required = true)
    private String leaseTermStart;

    @Schema(title = "租赁期止，yyyyMMdd", required = true)
    private String leaseTermEnd;

    @Schema(title = "备注")
    private String remark;

    @Schema(title = "扩展字段")
    private String ext1;

    @Schema(title = "扩展字段")
    private String ext2;

    @Schema(title = "扩展字段3  票易通 ext3")
    private String ext3;

    @Schema(title = "扩展字段4  票易通 ext4")
    private String ext4;

    public UploadInvoiceDetailVo() {
    }

    public UploadInvoiceDetailVo(String salesbillItemNo, String itemName, String itemSpec, String quantityUnit, BigDecimal quantity, String goodsTaxNo, BigDecimal outterDiscountWithTax, BigDecimal taxRate, BigDecimal amountWithTax, BigDecimal amountWithoutTax, BigDecimal taxAmount, BigDecimal unitPriceWithTax, BigDecimal unitPrice, String taxPre, String taxPreCon, String zeroTax, String remark, String ext1, String ext2, String ext3, String ext4) {
        this.salesbillItemNo = salesbillItemNo;
        this.itemName = itemName;
        this.itemSpec = itemSpec;
        this.quantityUnit = quantityUnit;
        this.quantity = quantity;
        this.goodsTaxNo = goodsTaxNo;
        this.outterDiscountWithTax = outterDiscountWithTax;
        this.taxRate = taxRate;
        this.amountWithTax = amountWithTax;
        this.amountWithoutTax = amountWithoutTax;
        this.taxAmount = taxAmount;
        this.unitPriceWithTax = unitPriceWithTax;
        this.unitPrice = unitPrice;
        this.taxPre = taxPre;
        this.taxPreCon = taxPreCon;
        this.zeroTax = zeroTax;
        this.remark = remark;
        this.ext1 = ext1;
        this.ext2 = ext2;
        this.ext3 = ext3;
        this.ext4 = ext4;
    }

    public static UploadInvoiceDetailVo.UploadInvoiceDetailVoBuilder builder() {
        return new UploadInvoiceDetailVo.UploadInvoiceDetailVoBuilder();
    }

    public String getSalesbillItemNo() {
        return this.salesbillItemNo;
    }

    public void setSalesbillItemNo(String salesbillItemNo) {
        this.salesbillItemNo = salesbillItemNo;
    }

    public String getItemName() {
        return this.itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getItemSpec() {
        return this.itemSpec;
    }

    public void setItemSpec(String itemSpec) {
        this.itemSpec = itemSpec;
    }

    public String getQuantityUnit() {
        return this.quantityUnit;
    }

    public void setQuantityUnit(String quantityUnit) {
        this.quantityUnit = quantityUnit;
    }

    public BigDecimal getQuantity() {
        return this.quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public String getGoodsTaxNo() {
        return this.goodsTaxNo;
    }

    public void setGoodsTaxNo(String goodsTaxNo) {
        this.goodsTaxNo = goodsTaxNo;
    }

    public BigDecimal getOutterDiscountWithTax() {
        return this.outterDiscountWithTax;
    }

    public void setOutterDiscountWithTax(BigDecimal outterDiscountWithTax) {
        this.outterDiscountWithTax = outterDiscountWithTax;
    }

    public BigDecimal getTaxRate() {
        return this.taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public BigDecimal getAmountWithTax() {
        return this.amountWithTax;
    }

    public void setAmountWithTax(BigDecimal amountWithTax) {
        this.amountWithTax = amountWithTax;
    }

    public BigDecimal getAmountWithoutTax() {
        return this.amountWithoutTax;
    }

    public void setAmountWithoutTax(BigDecimal amountWithoutTax) {
        this.amountWithoutTax = amountWithoutTax;
    }

    public BigDecimal getTaxAmount() {
        return this.taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public BigDecimal getUnitPriceWithTax() {
        return this.unitPriceWithTax;
    }

    public void setUnitPriceWithTax(BigDecimal unitPriceWithTax) {
        this.unitPriceWithTax = unitPriceWithTax;
    }

    public BigDecimal getUnitPrice() {
        return this.unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public String getTaxPre() {
        return this.taxPre;
    }

    public void setTaxPre(String taxPre) {
        this.taxPre = taxPre;
    }

    public String getTaxPreCon() {
        return this.taxPreCon;
    }

    public void setTaxPreCon(String taxPreCon) {
        this.taxPreCon = taxPreCon;
    }

    public String getZeroTax() {
        return this.zeroTax;
    }

    public void setZeroTax(String zeroTax) {
        this.zeroTax = zeroTax;
    }

    public String getRealEstateAddress() {
        return realEstateAddress;
    }

    public void setRealEstateAddress(String realEstateAddress) {
        this.realEstateAddress = realEstateAddress;
    }

    public String getRealEstateNo() {
        return realEstateNo;
    }

    public void setRealEstateNo(String realEstateNo) {
        this.realEstateNo = realEstateNo;
    }

    public String getCrossCitySign() {
        return crossCitySign;
    }

    public void setCrossCitySign(String crossCitySign) {
        this.crossCitySign = crossCitySign;
    }

    public String getAreaUnit() {
        return areaUnit;
    }

    public void setAreaUnit(String areaUnit) {
        this.areaUnit = areaUnit;
    }

    public String getRealEstatePlace() {
        return realEstatePlace;
    }

    public void setRealEstatePlace(String realEstatePlace) {
        this.realEstatePlace = realEstatePlace;
    }

    public String getLandVatItemNo() {
        return landVatItemNo;
    }

    public void setLandVatItemNo(String landVatItemNo) {
        this.landVatItemNo = landVatItemNo;
    }

    public String getRealEstateCode() {
        return realEstateCode;
    }

    public void setRealEstateCode(String realEstateCode) {
        this.realEstateCode = realEstateCode;
    }

    public String getTaxablePrice() {
        return taxablePrice;
    }

    public void setTaxablePrice(String taxablePrice) {
        this.taxablePrice = taxablePrice;
    }

    public String getTransactionPrice() {
        return transactionPrice;
    }

    public void setTransactionPrice(String transactionPrice) {
        this.transactionPrice = transactionPrice;
    }

    public String getLeaseTermStart() {
        return leaseTermStart;
    }

    public void setLeaseTermStart(String leaseTermStart) {
        this.leaseTermStart = leaseTermStart;
    }

    public String getLeaseTermEnd() {
        return leaseTermEnd;
    }

    public void setLeaseTermEnd(String leaseTermEnd) {
        this.leaseTermEnd = leaseTermEnd;
    }

    public String getRemark() {
        return this.remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getExt1() {
        return this.ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    public String getExt2() {
        return this.ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    public String getExt3() {
        return this.ext3;
    }

    public void setExt3(String ext3) {
        this.ext3 = ext3;
    }

    public String getExt4() {
        return this.ext4;
    }

    public void setExt4(String ext4) {
        this.ext4 = ext4;
    }

    public static class UploadInvoiceDetailVoBuilder {

        private String salesbillItemNo;

        private String itemName;

        private String itemSpec;

        private String quantityUnit;

        private BigDecimal quantity;

        private String goodsTaxNo;

        private BigDecimal outterDiscountWithTax;

        private BigDecimal taxRate;

        private BigDecimal amountWithTax;

        private BigDecimal amountWithoutTax;

        private BigDecimal taxAmount;

        private BigDecimal unitPriceWithTax;

        private BigDecimal unitPrice;

        private String taxPre;

        private String taxPreCon;

        private String zeroTax;

        private String remark;

        private String ext1;

        private String ext2;

        private String ext3;

        private String ext4;

        UploadInvoiceDetailVoBuilder() {
        }

        public UploadInvoiceDetailVo.UploadInvoiceDetailVoBuilder salesbillItemNo(String salesbillItemNo) {
            this.salesbillItemNo = salesbillItemNo;
            return this;
        }

        public UploadInvoiceDetailVo.UploadInvoiceDetailVoBuilder itemName(String itemName) {
            this.itemName = itemName;
            return this;
        }

        public UploadInvoiceDetailVo.UploadInvoiceDetailVoBuilder itemSpec(String itemSpec) {
            this.itemSpec = itemSpec;
            return this;
        }

        public UploadInvoiceDetailVo.UploadInvoiceDetailVoBuilder quantityUnit(String quantityUnit) {
            this.quantityUnit = quantityUnit;
            return this;
        }

        public UploadInvoiceDetailVo.UploadInvoiceDetailVoBuilder quantity(BigDecimal quantity) {
            this.quantity = quantity;
            return this;
        }

        public UploadInvoiceDetailVo.UploadInvoiceDetailVoBuilder goodsTaxNo(String goodsTaxNo) {
            this.goodsTaxNo = goodsTaxNo;
            return this;
        }

        public UploadInvoiceDetailVo.UploadInvoiceDetailVoBuilder outterDiscountWithTax(BigDecimal outterDiscountWithTax) {
            this.outterDiscountWithTax = outterDiscountWithTax;
            return this;
        }

        public UploadInvoiceDetailVo.UploadInvoiceDetailVoBuilder taxRate(BigDecimal taxRate) {
            this.taxRate = taxRate;
            return this;
        }

        public UploadInvoiceDetailVo.UploadInvoiceDetailVoBuilder amountWithTax(BigDecimal amountWithTax) {
            this.amountWithTax = amountWithTax;
            return this;
        }

        public UploadInvoiceDetailVo.UploadInvoiceDetailVoBuilder amountWithoutTax(BigDecimal amountWithoutTax) {
            this.amountWithoutTax = amountWithoutTax;
            return this;
        }

        public UploadInvoiceDetailVo.UploadInvoiceDetailVoBuilder taxAmount(BigDecimal taxAmount) {
            this.taxAmount = taxAmount;
            return this;
        }

        public UploadInvoiceDetailVo.UploadInvoiceDetailVoBuilder unitPriceWithTax(BigDecimal unitPriceWithTax) {
            this.unitPriceWithTax = unitPriceWithTax;
            return this;
        }

        public UploadInvoiceDetailVo.UploadInvoiceDetailVoBuilder unitPrice(BigDecimal unitPrice) {
            this.unitPrice = unitPrice;
            return this;
        }

        public UploadInvoiceDetailVo.UploadInvoiceDetailVoBuilder taxPre(String taxPre) {
            this.taxPre = taxPre;
            return this;
        }

        public UploadInvoiceDetailVo.UploadInvoiceDetailVoBuilder taxPreCon(String taxPreCon) {
            this.taxPreCon = taxPreCon;
            return this;
        }

        public UploadInvoiceDetailVo.UploadInvoiceDetailVoBuilder zeroTax(String zeroTax) {
            this.zeroTax = zeroTax;
            return this;
        }

        public UploadInvoiceDetailVo.UploadInvoiceDetailVoBuilder remark(String remark) {
            this.remark = remark;
            return this;
        }

        public UploadInvoiceDetailVo.UploadInvoiceDetailVoBuilder ext1(String ext1) {
            this.ext1 = ext1;
            return this;
        }

        public UploadInvoiceDetailVo.UploadInvoiceDetailVoBuilder ext2(String ext2) {
            this.ext2 = ext2;
            return this;
        }

        public UploadInvoiceDetailVo build() {
            return new UploadInvoiceDetailVo(this.salesbillItemNo, this.itemName, this.itemSpec, this.quantityUnit, this.quantity, this.goodsTaxNo, this.outterDiscountWithTax, this.taxRate, this.amountWithTax, this.amountWithoutTax, this.taxAmount, this.unitPriceWithTax, this.unitPrice, this.taxPre, this.taxPreCon, this.zeroTax, this.remark, this.ext1, this.ext2, this.ext3, this.ext4);
        }

        public String toString() {
            return "UploadInvoiceDetailVo.UploadInvoiceDetailVoBuilder(salesbillItemNo=" + this.salesbillItemNo + ", itemName=" + this.itemName + ", itemSpec=" + this.itemSpec + ", quantityUnit=" + this.quantityUnit + ", quantity=" + this.quantity + ", goodsTaxNo=" + this.goodsTaxNo + ", outterDiscountWithTax=" + this.outterDiscountWithTax + ", taxRate=" + this.taxRate + ", amountWithTax=" + this.amountWithTax + ", amountWithoutTax=" + this.amountWithoutTax + ", taxAmount=" + this.taxAmount + ", unitPriceWithTax=" + this.unitPriceWithTax + ", unitPrice=" + this.unitPrice + ", taxPre=" + this.taxPre + ", taxPreCon=" + this.taxPreCon + ", zeroTax=" + this.zeroTax + ", remark=" + this.remark + ", ext1=" + this.ext1 + ", ext2=" + this.ext2 + ")";
        }

    }

}