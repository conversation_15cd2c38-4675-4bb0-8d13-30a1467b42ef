package com.kerryprops.kip.bill.feign.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


@Data
public class UploadInvoiceMainV2Vo implements Serializable {

    private String feeType;

    private String salesbillNo;

    private String invoiceType;

    private String remark;

    private String sellerNo;

    private String sellerBankAccount;

    private String sellerAddress;

    private String sellerBankName;

    private String sellerName;

    private String sellerTaxNo;

    private String sellerTel;

    private String receiveUserEmail;

    private String receiveUserTel;

    private String purchaserNo;

    private String purchaserName;

    private String purchaserTaxNo;

    private String purchaserAddress;

    private String purchaserTel;

    private String purchaserBankName;

    private String purchaserBankAccount;

    private Integer priceMethod;

    private BigDecimal amountWithTax;

    private BigDecimal amountWithoutTax;

    private BigDecimal taxAmount;

    private String cashierName;

    private String checkerName;

    private String invoicerName;

    private String businessType;

    private String ext1;

    private String ext2;

    private String systemId;

    private String timeStamp;

    private String sign;

    private List<UploadInvoiceDetailV2Vo> detailVos;

    private String callbackUrl;

    // 无意义，只是为了暂存楼盘名
    private String projectName;

}