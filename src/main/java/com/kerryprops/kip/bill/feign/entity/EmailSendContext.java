package com.kerryprops.kip.bill.feign.entity;

import com.kerryprops.kip.bill.service.model.leg.Bill;
import lombok.Builder;
import lombok.Data;

import java.util.Set;

/*****************************************************************************
 * Project - unified-messaging-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - <PERSON>
 * Created Date - 07/05/2021 15:34
 *****************************************************************************/

@Data
@Builder
public class EmailSendContext {

    private String sendId;

    private String userId;

    private Set<Bill> userBills;

    private EmailSendCommand emailSendCommand;

}
