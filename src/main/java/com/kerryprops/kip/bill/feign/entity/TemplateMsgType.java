package com.kerryprops.kip.bill.feign.entity;

import com.fasterxml.jackson.annotation.JsonCreator;

public enum TemplateMsgType {

    //机器人配送提醒
    ROBOT_DELIVERY_NOTICE(1)
    // 物业缴费提醒
    ,
    HOA_FEES_REMINDER(2)
    // 物业管理通知
    ,
    PROPERTY_MANAGEMENT_NOTICE(3)
    // 服务通知 （班车/活动）
    ,
    EVENT_REMINDER(4)
    // 物业缴费支付成功通知
    ,
    PAYMENT_CONFIRMED(5)
    // 物业代扣失败通知
    ,
    HOA_FEES_PAY_FAILED_REMINDER(6);

    private int type;

    TemplateMsgType(int type) {
        this.type = type;
    }

    @JsonCreator
    public static TemplateMsgType fromName(String name) {
        for (TemplateMsgType type : TemplateMsgType.values()) {
            if (type.name().equalsIgnoreCase(name)) {
                return type;
            }
        }
        throw new RuntimeException("template message type not found for name: " + name);
    }

    public int getType() {
        return type;
    }
}
