package com.kerryprops.kip.bill.feign.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/*****************************************************************************
 * Project - unified-messaging-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - <PERSON>
 * Created Date - 07/05/2021 15:34
 *****************************************************************************/

@Data
@Schema
public class EmailSendCommand implements Serializable {

    @Schema(title = "发往的邮箱")
    private String sendTo;

    @Schema(title = "发往的邮箱_批量")
    private List<String> sendTos;

    @Schema(title = "邮件主题")
    @NotBlank(message = "subject can not be null")
    private String subject;

    @Schema(title = "邮件内容-文本")
    @NotBlank(message = "text can not be null")
    private String text;

    @Schema(title = "是否为富文本")
    private Boolean html;

    @Schema(title = "附件")
    private List<AttachmentDto> attachments;

    @Schema(title = "发送邮件的业务类型")
    private String businessType;

    @Schema(title = "发送邮件的源服务")
    private String source;

}
