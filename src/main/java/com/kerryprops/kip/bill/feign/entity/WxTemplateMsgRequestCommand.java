package com.kerryprops.kip.bill.feign.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Schema
@EqualsAndHashCode(callSuper = true)
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WxTemplateMsgRequestCommand extends WxTemplateMsgCommand {

    /* hive 楼盘ID */
    @Schema(title = "hive 楼盘ID")
    @NotBlank
    private String projectId;

    /* hive 楼栋ID */
    @Schema(title = "hive 楼栋ID")
    private List<String> buildingIds;

    /* profile user ID list */
    @Schema(title = "profile user ID list")
    @NotEmpty
    private List<String> userIds;

}
