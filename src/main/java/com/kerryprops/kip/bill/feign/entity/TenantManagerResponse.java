package com.kerryprops.kip.bill.feign.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - tenant-admin-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - Bert
 * Created Date - 07/21/2021 14:59
 **********************************************************************************************************************/

@Get<PERSON>
@Setter
@Schema
public class TenantManagerResponse implements Serializable {

    @Schema(title = "租户账号id")
    private Long id;

    @Schema(title = "租户账号姓名")
    private String userName;

    @Schema(title = "租户账号手机号")
    private String phoneNumber;

    @Schema(title = "租户账号邮箱")
    private String email;

    @Schema(title = "租户账号角色")
    private String roles;

}
