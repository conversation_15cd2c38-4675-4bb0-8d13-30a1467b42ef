package com.kerryprops.kip.bill.feign.clients;

import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.feign.entity.MessageDto;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/***********************************************************************************************************************
 * Project - user-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 08/03/2021 14:21
 **********************************************************************************************************************/
public interface MessageCenterClient {

    @Operation(description = "发送消息")
    @PostMapping(value = "/message/send")
    RespWrapVo<String> sendMessage(@RequestBody @Valid MessageDto messageDto);

}
