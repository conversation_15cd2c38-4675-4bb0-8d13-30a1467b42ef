package com.kerryprops.kip.bill.feign.clients;

import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.feign.entity.CustomerUserResource;
import com.kerryprops.kip.bill.feign.entity.UserWithRoomsResponse;
import com.kerryprops.kip.bill.feign.entity.WxOpenIDResource;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/***********************************************************************************************************************
 * Project - hive-view-assembler-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - <PERSON>
 * Created Date - 06/09/2021 14:43
 **********************************************************************************************************************/
public interface CUserClient {

    @GetMapping(value = "/customer/users/rooms")
    @Operation(description = "用户关联的公寓/住户信息")
    RespWrapVo<UserWithRoomsResponse> userAssociateRooms(@RequestParam("userId") String userId);

    @GetMapping(value = "/customer/users/essential/{code}")
    @Operation(description = "通过code获取用户基本信息")
    RespWrapVo<CustomerUserResource> queryUserByCode(@PathVariable("code") int code);

    @GetMapping("/customer/openId/list")
    List<WxOpenIDResource> openIdList(
            @RequestParam(value = "appId") String appId
            , @RequestParam(value = "projectId", required = false) String projectId
            , @RequestParam(value = "roomIds", required = false) Collection<String> roomIds);

    @GetMapping(value = "/customer/users/{id}")
    RespWrapVo<CustomerUserResource> getCustomerUserById(@PathVariable("id") String id);

}
