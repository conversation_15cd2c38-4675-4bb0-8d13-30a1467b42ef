package com.kerryprops.kip.bill.feign.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Collections;
import java.util.Set;

/**
 * NotificationBroadcastResource.
 *
 * <AUTHOR> 2024-11-01 11:36:39
 **/
@Data
public class NotificationResource {

    @Schema(title = "消息id", description = "null : 没有找到用户的设备，不推送消息")
    private String messageId;

    @Schema(title = "没有匹配registrationId的用户")
    private Set<Long> notFoundUserIds = Collections.emptySet();

}
