package com.kerryprops.kip.bill.feign.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * @Author: Bert
 * @Date: 2021/5/28 2:22 下午
 * @Description: default
 */

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema
public class TenantStaffResource implements Serializable {

    @Schema(title = "id")
    private Integer id;

    @Schema(title = "姓名")
    private String userName;

    @Schema(title = "认证类型")
    private IdentityAuditType auditType;

    @Schema(title = "认证状态(员工：1:待处理 2:在职中 3:已离职 4:已驳回； 公寓/住宅：1:待认证，2:已认证，3:已禁用)")
    private Integer status;

    @Schema(title = "lbs id")
    private String lbsId;

    @Schema(title = "楼盘id")
    private String projectId;

    @Schema(title = "楼栋名称 + 楼栋名称")
    private String buildingName;

    @Schema(title = "楼栋编号")
    private String buildingNumber;

    @Schema(title = "楼栋单元编号")
    private String buildingUnitNumber;

    @Schema(title = "公司名称")
    private String companyName;

    @Schema(title = "公司编号或id")
    private String companyNumber;

    @Schema(title = "公司单元编号或id")
    private String companyUnitNumber;

    @Schema(title = "房间id")
    private String roomNumber;

    @Schema(title = "公寓/住宅jde账单")
    private String jdeBill;

    @Schema(title = "性别")
    private Integer gender;

    @Schema(title = "职位")
    private String position;

    @Schema(title = "行政卫权限，0：否；1有")
    private Integer healthAuthority;

    @Schema(title = "公寓或住户是，该字段显示是否是授权人")
    private Integer authorizer;

}
