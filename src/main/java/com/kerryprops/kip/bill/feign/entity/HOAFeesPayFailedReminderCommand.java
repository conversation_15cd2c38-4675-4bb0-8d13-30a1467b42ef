package com.kerryprops.kip.bill.feign.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

import static com.kerryprops.kip.bill.feign.entity.TemplateMsgType.HOA_FEES_PAY_FAILED_REMINDER;

/**
 * 您好，您2021年5月21日发起的电费缴费支付失败
 * 订单号：111111111
 * 支付金额：100元
 * 缴费账户：1111111111
 * 支付时间：2022年3月15日 19:35
 * 感谢您使用付费吧
 * <p>
 * {{first.DATA}}
 * 订单号：{{keyword1.DATA}}
 * 支付金额：{{keyword2.DATA}}
 * 缴费账户：{{keyword3.DATA}}
 * 支付时间：{{keyword4.DATA}}
 * {{remark.DATA}}
 */
@Data
public class HOAFeesPayFailedReminderCommand {

    /* 标题 */
    @JsonProperty(value = "first")
    private String headline;

    /* 订单号 */
    @JsonProperty(value = "keyword1")
    private String paymentOrderNo;

    /* 支付金额 */
    @JsonProperty(value = "keyword2")
    private String paymentAmount;

    /* 缴费账户 */
    @JsonProperty(value = "keyword3")
    private String payerName;

    /* 支付时间 */
    @JsonProperty(value = "keyword4")
    private String paymentTime;

    /* 跳转链接 */
    @JsonProperty(value = "url")
    private String url;

    /* 备注 */
    @JsonProperty(value = "remark")
    private String remark;

    /* hive 楼盘ID */
    private String projectId;

    /* hive 楼栋ID */
    private List<String> buildingIds;

    /* profile user ID list */
    private List<String> userIds;

    /* 模板消息类型 */
    private String templateMsgType = HOA_FEES_PAY_FAILED_REMINDER.name();

}
