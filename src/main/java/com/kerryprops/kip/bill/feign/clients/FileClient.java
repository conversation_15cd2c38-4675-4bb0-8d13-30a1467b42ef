package com.kerryprops.kip.bill.feign.clients;

import com.kerryprops.kip.bill.feign.entity.OssPreSignedUrlResponse;
import com.kerryprops.kip.bill.feign.entity.UploadPrivateFileUrlDTO;
import com.kerryprops.kip.bill.feign.entity.UploadPublicFileResponse;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/***********************************************************************************************************************
 * Project - hive-view-assembler-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - David Wei
 * Created Date - 06/09/2021 14:43
 **********************************************************************************************************************/
public interface FileClient {

    @Operation(summary = "得到私有桶文件的预签名url")
    @GetMapping(value = "/s/file/oss/pre-signed-urls")
    List<OssPreSignedUrlResponse> getPreSignedUrls(@Valid @RequestParam("url") @NotEmpty List<String> urls);

    @PostMapping(value = "/s/file/oss/private-file-body", consumes = MediaType.APPLICATION_JSON_VALUE)
    UploadPublicFileResponse uploadPrivateFileBody(@Valid @RequestBody UploadPrivateFileUrlDTO uploadPrivateFileUrlDTO);

    default UploadPublicFileResponse uploadPrivateFile(String fileUrl) {
        UploadPrivateFileUrlDTO uploadPrivateFileUrlDTO = UploadPrivateFileUrlDTO.builder()
                .fileUrl(fileUrl).openOssContentDisposition(Boolean.FALSE).build();
        return uploadPrivateFileBody(uploadPrivateFileUrlDTO);
    }

}
