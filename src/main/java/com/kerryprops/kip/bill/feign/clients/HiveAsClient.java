package com.kerryprops.kip.bill.feign.clients;

import com.google.common.collect.Lists;
import com.kerryprops.kip.bill.common.constants.AppConstants;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.exceptions.AppException;
import com.kerryprops.kip.bill.common.utils.BuConverter;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.entity.HiveContextAware;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import com.kerryprops.kip.hiveas.feign.dto.BuildingDTO;
import com.kerryprops.kip.hiveas.webservice.resource.resp.ProjectResp;
import com.kerryprops.kip.hiveas.webservice.resource.resp.RoomResp;
import com.kerryprops.kip.hiveas.webservice.vo.resp.BuildingResponseVo;
import com.kerryprops.kip.hiveas.webservice.vo.resp.ProjectBuildingVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - hive-view-assembler-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - David Wei
 * Created Date - 06/09/2021 14:43
 **********************************************************************************************************************/
public interface HiveAsClient {

    Logger logger = LoggerFactory.getLogger(HiveAsClient.class);

    /**
     * 根据合同号查询租户id
     * acquire tenant detail info by contract code; version 2
     */
    /*@GetMapping("/hiveas/v2/tenant/id/{doCo}")
    RespWrapVo<Set<TenantRespDto>> getTenantIdByDoCo(@PathVariable("doCo") List<String> doCo);*/

    /**
     * acquire lbs's building infos
     */
    @GetMapping(value = "/building/project_lbs")
    List<BuildingResponseVo> getLbs(@RequestParam("lbsId") String lbsId);

    /**
     * query tenant info along with simplified infos of project, buildings, floors, rooms
     */
    /*@GetMapping("/hiveas/v2/tenant")
    RespWrapVo<TenantResp> getTenantById(@RequestParam("id") String id,
                                         @RequestParam("contractStatus") List<String> contractStatusList);*/

    /**
     * query building detail infos by building list along with full detail of floors, tmp visitors configs
     * , tenants, rooms and building application configs
     * 【{buildingIds}数量限制<=30】
     */
    @GetMapping("/building/list/{buildingIds}")
    Set<BuildingResponseVo> doGetBuildingByIds(@PathVariable("buildingIds") List<String> buildingIds);

    default List<BuildingResponseVo> getBuildingByIds(List<String> buildingIds) {
        if (CollectionUtils.isEmpty(buildingIds)) {
            logger.info("get_building_by_ids building_ids_is_empty");
            return Collections.emptyList();
        }

        List<List<String>> buildingIdLists = Lists.partition(buildingIds, 20);

        List<BuildingResponseVo> buildingResponseResult = Lists.newLinkedList();
        buildingIdLists.forEach(buildingIdList -> {
            Set<BuildingResponseVo> buildingResponseVos = this.doGetBuildingByIds(buildingIdList);
            buildingResponseResult.addAll(buildingResponseVos);
        });

        return buildingResponseResult;
    }

    default List<String> convertToJdeBus(List<String> kipBuildingIds) {
        if (CollectionUtils.isEmpty(kipBuildingIds)) {
            return Collections.emptyList();
        }
        List<BuildingResponseVo> buildingRespVos = this.getBuildingByIds(kipBuildingIds);

        List<String> bus = new ArrayList<>();
        if (CollectionUtils.isEmpty(buildingRespVos)) {
            return bus;
        }

        bus.addAll(buildingRespVos.stream().map(e -> BuConverter.getBuList(e.getBuilding().getPropertyManagementBU()))
                .flatMap(Collection::stream).collect(Collectors.toList()));
        bus.addAll(buildingRespVos.stream().map(e -> BuConverter.getBuList(e.getBuilding().getPropertyDeveloperBU()))
                .flatMap(Collection::stream).collect(Collectors.toList()));
        return bus;
    }


    /**
     * full info of buildings query by projectIds
     */
    @GetMapping("/project/list/{projectIds}?onlyProject=0")
    List<ProjectBuildingVO> doGetCenterByIds(@PathVariable("projectIds") String[] projectIds);

    default List<ProjectBuildingVO> getCenterByIds(String[] projectIds) {
        if (Objects.isNull(projectIds) || 0 == projectIds.length) {
            return Collections.emptyList();
        }

        List<List<String>> projectIdLists = Lists.partition(Lists.newArrayList(projectIds), 10);

        List<ProjectBuildingVO> projectBuildings = new LinkedList<>();
        projectIdLists.forEach(projectIdList -> {

            String[] projectIdListStr = projectIdList.toArray(new String[0]);
            List<ProjectBuildingVO> projectBuildingVos = doGetCenterByIds(projectIdListStr);
            if (CollectionUtils.isEmpty(projectBuildingVos)) {
                return;
            }
            projectBuildings.addAll(projectBuildingVos);
        });
        return projectBuildings;
    }

    /**
     * query project full info by projectId
     */
    @GetMapping("/hiveas/project")
    RespWrapVo<ProjectResp> getProjectById(@RequestParam("projectId") String projectId);

    default ProjectResp getProjectByID(String projectId) {
        RespWrapVo<ProjectResp> vo = this.getProjectById(projectId);
        if (Objects.isNull(vo)
                || !RespCodeEnum.SUCCESS.getCode().equalsIgnoreCase(vo.getCode())) {
            throw new AppException(RespCodeEnum.UNKNOWN_ERROR.getCode(), "query /hiveas/project received 5xx");
        }
        return vo.getData();
    }

    /**
     * get room detail info by roomId along with simplified info of project, buildings, floors, rooms
     */
    @GetMapping("/hiveas/v2/room")
    RespWrapVo<RoomResp> getRoomById(@RequestParam("roomId") String roomId);

    default HiveContextAware getRoomHiveContext(String roomId) {
        RespWrapVo<RoomResp> roomRespVoRespWrapVo = this.getRoomById(roomId);
        if (!RespWrapVo.isResponseValidWithData(roomRespVoRespWrapVo)) {
            throw new RuntimeException("room not found: " + roomId);
        }
        RoomResp room = roomRespVoRespWrapVo.getData();
        PositionItemResponse positionItem = new PositionItemResponse();
        positionItem.setProjectName(room.getProject().getName());
        positionItem.setBuildingName(room.getBuilding().getName());
        positionItem.setFloorName(room.getFloor().getName());
        positionItem.setRoomName(room.getRoom().getRoomNo());
        return new HiveContextAware() {

            @Override
            public String getProjectId() {
                return room.getProject().getId();
            }

            @Override
            public String getBuildingId() {
                return room.getBuilding().getId();
            }

            @Override
            public String getFloorId() {
                return room.getFloor().getId();
            }

            @Override
            public String getRoomId() {
                return room.getRoom().getId();
            }

            @Override
            public PositionItemResponse getPositionItem() {
                return positionItem;
            }
        };
    }

    /**
     * query building info by building id along with the simplified infos of floors.
     */
    default BuildingResponseVo getBuildingById(String buildingId) {
        if (StringUtils.isEmpty(buildingId)) {
            logger.info("find_building_by_building_ids, building_id_empty");
            return null;
        }

        List<BuildingResponseVo> buildingRespVos = this.getBuildingByIds(List.of(buildingId));
        if (CollectionUtils.isEmpty(buildingRespVos)) {
            logger.info("find_building_by_building_ids_empty, building_id: " + buildingId);
            return null;
        }

        return buildingRespVos.stream().findFirst().orElse(null);
    }

    default String getPropertyManagementCo(String buildingId) {
        BuildingResponseVo buildingResponseVo = this.getBuildingById(buildingId);
        if (Objects.isNull(buildingResponseVo) || Objects.isNull(buildingResponseVo.getBuilding())) {
            throw new RuntimeException("Co not configured for building " + buildingId);
        }
        String propertyManagementCo = buildingResponseVo.getBuilding().getPropertyManagementCo();
        if (StringUtils.isEmpty(propertyManagementCo)) {
            throw new RuntimeException("Co not configured for building " + buildingId);
        }
        return propertyManagementCo;
    }

    /**
     * get room detail info by BU & jdeRoomNos along with simplified info of project, buildings, floors, rooms
     */
    @GetMapping(value = "/hiveas/room")
    List<RoomResp> getBatchRoom(@RequestParam("propertyManagementBu") String propertyManagementBu
            , @RequestParam("jdeRoomNos") String jdeRoomNos);

    default List<RoomResp> getBatchRoom(String bu, List<String> jdeRoomNos) {
        String jdeRoomString = StringUtils.join(jdeRoomNos, ",");
        List<RoomResp> roomRespList = this.getBatchRoom(bu, jdeRoomString);
        if (CollectionUtils.isEmpty(roomRespList)) {
            return Collections.emptyList();
        }
        return roomRespList;
    }

    default List<String> convertToJdeBus(String[] projectIdArr) {
        if (Objects.isNull(projectIdArr) || CollectionUtils.sizeIsEmpty(projectIdArr)) {
            logger.info("illegal projectIdArr");
            return null;
        }

        List<ProjectBuildingVO> centers = this.getCenterByIds(projectIdArr);
        if (CollectionUtils.isEmpty(centers)) {
            logger.error("hive_return_empty_centers_data");
            return null;
        }

        List<BuildingDTO> buildingVos = centers.stream()
                .map(ProjectBuildingVO::getBuildings)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        Set<String> bus = new HashSet<>();
        bus.addAll(buildingVos.stream().map(e -> BuConverter.getBuList(e.getPropertyManagementBU()))
                .flatMap(Collection::stream).collect(Collectors.toList()));
        bus.addAll(buildingVos.stream().map(e -> BuConverter.getBuList(e.getPropertyDeveloperBU()))
                .flatMap(Collection::stream).collect(Collectors.toList()));
        return new ArrayList<>(bus);
    }

    default List<String> populateDataFields(String projectId, List<String> buildingIds) {
        List<String> searchedBus = null;
        LoginUser loginUser = UserInfoUtils.getUser();
        if (loginUser == null) {
            throw new RuntimeException("User not login.");
        }
        if (CollectionUtils.isNotEmpty(buildingIds)) {
            searchedBus = this.convertToJdeBus(buildingIds);
        } else if (Boolean.TRUE.equals(loginUser.isSuperAdmin())) {
            searchedBus = null;
        } else if (StringUtils.isNotBlank(projectId)) {
            String[] projectIdArr = StringUtils.split(projectId, AppConstants.COMMA);
            searchedBus = this.convertToJdeBus(projectIdArr);
            if (CollectionUtils.isNotEmpty(loginUser.toBuildingIdList())) {
                List<String> bus2 = this.convertToJdeBus(loginUser.toBuildingIdList());
                searchedBus = searchedBus.stream().filter(bus2::contains).collect(Collectors.toList());
            }
        } else if (CollectionUtils.isNotEmpty(loginUser.toBuildingIdList())) {
            searchedBus = this.convertToJdeBus(loginUser.toBuildingIdList());
        }
        return searchedBus;
    }

}
