package com.kerryprops.kip.bill.feign.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/***********************************************************************************************************************
 * Project - tenant-admin-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 08/27/2021 17:30
 **********************************************************************************************************************/

@Get<PERSON>
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class StaffQueryResource {

    @Schema(title = "认证类型，OFFICE：办公； APARTMENT：公寓； RESIDENCE：小区")
    private IdentityAuditType auditType;

    @Schema(title = "是否授权人，0：否；1：有")
    private Integer authorizer;

    @Schema(title = "房间id，查询公寓或小区时传这个参数值")
    private String roomId;

    @Schema(title = "租户id，查询办公时传这个参数值")
    private String tenantId;

    @Schema(title = "认证成功状态", hidden = true)
    private Integer status = StaffAuditStatusEnum.STATUS_2.getCode();

}
