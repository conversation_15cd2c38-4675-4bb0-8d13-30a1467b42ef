package com.kerryprops.kip.bill.feign.entity;

import lombok.Getter;

/**
 * @Author: <PERSON>
 * @Date: 2021/5/28 11:15 上午
 * @Description: default
 */

@Getter
public enum IdentityAuditType {

    OFFICE("办公楼"),
    APARTMENT("公寓"),
    PARKING("停车位"),
    RESIDENCE("小区"),
    RETAIL("商场");

    @Getter
    private final String name;

    IdentityAuditType(String name) {
        this.name = name;
    }
}
