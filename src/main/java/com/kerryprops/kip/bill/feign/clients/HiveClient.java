package com.kerryprops.kip.bill.feign.clients;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface HiveClient {

    @PostMapping("/building/bu_jdeRoomNo")
    List<HiveBuBuildingResponse> getBuildingByBuAndJdeRoomNo(@RequestBody List<HiveBuBuildingRequest> request);

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    class HiveBuBuildingRequest {

        List<String> bu;

        List<String> jdeRoomNo;

    }

    @Data
    class HiveBuBuildingResponses {

        List<HiveBuBuildingResponse> data;

    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    class HiveBuBuildingResponse {

        private String id;

        private String name;

        private String projectId;

        private String propertyManagementBU;

        private String propertyDeveloperBU;

        private String propertyManagementCo;

        private String propertyManagementName;

        private String propertyDeveloperCo;

        private String propertyDeveloperName;

        private String formats;

        private String status;

    }

}
