package com.kerryprops.kip.bill.feign.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.Duration;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * NotificationPushRequest.
 *
 * <AUTHOR> Yu 2024-11-01 10:57:04
 **/
@Data
public class NotificationPushRequest {

    @NotNull
    @Schema(title = "手机app")
    private PhoneApp phoneApp;

    @Schema(title = "华为消息分类", description = "默认值：WORK. 影响华为手机推送")
    private HuaweiCategory huaweiCategory;

    @Schema(title = "vivo消息分类", description = "默认值：TODO. 影响vivo手机推送")
    private VivoCategory vivoCategory;

    @NotEmpty
    @Schema(title = "接受人", description = "profile-service的userId")
    private Set<@NotNull Long> userIds;

    @NotBlank
    @Size(max = 32)
    @Schema(title = "通知标题")
    private String title;

    @NotBlank
    @Size(max = 4000)
    @Schema(title = "通知内容")
    private String content;

    @Schema(title = "额外字段", description = "手机接受通知时附加的字段")
    private Map<String, Object> extras;

    @Min(0L)
    @Max(864000L)
    @Schema(title = "离线消息保留时长", description = "单位为秒，最长保留10天。ios无效")
    private Long timeToLive;

    @Schema(title = "消息类型", description = "默认值为系统消息. 运营消息有发送限制")
    private PhoneMsgType msgType;

    @Schema(title = "是否显示角标数字", description = "默认值：是，效果为自增+1. 否：维持原有角标数字")
    private Boolean isShowBadgeNumber;

    public static NotificationPushRequest of(MessageDto dto, String title) {
        Map<String, Object> extras = new HashMap<>(dto.getParams());
        extras.put("messageType", dto.getMessageType());

        NotificationPushRequest request = new NotificationPushRequest();
        request.setPhoneApp(PhoneApp.KERRY_ENTERPRISE);
        request.setHuaweiCategory(HuaweiCategory.EXPRESS);
        request.setVivoCategory(VivoCategory.ORDER);
        request.setUserIds(getUserIds(dto));
        request.setTitle(title);
        request.setContent(dto.getContent());
        request.setExtras(extras);
        request.setTimeToLive(Duration.ofDays(1).toSeconds());
        request.setMsgType(PhoneMsgType.SYSTEM);
        request.setIsShowBadgeNumber(false);
        return request;
    }

    private static Set<Long> getUserIds(MessageDto dto) {
        List<String> userIds = dto.getUserIds();
        String userId = dto.getUserId();
        if (CollectionUtils.isEmpty(userIds)) {
            if (StringUtils.isBlank(userId)) {
                return Collections.emptySet();
            }
            userIds = List.of(userId);
        }
        return userIds.stream()
                .filter(StringUtils::isNumeric)
                .map(Long::valueOf)
                .collect(Collectors.toSet());
    }

    enum PhoneApp {
        /**
         * kerry+企业版app
         */
        KERRY_ENTERPRISE
    }

    enum PhoneMsgType {
        /**
         * 运营消息
         */
        OPERATION,
        /**
         * 系统消息
         */
        SYSTEM
    }

    enum HuaweiCategory {

        /**
         * 即时聊天
         */
        IM,

        /**
         * 音视频通话
         */
        VOIP,

        /**
         * 订阅
         */
        SUBSCRIPTION,

        /**
         * 出行
         */
        TRAVEL,

        /**
         * 健康
         */
        HEALTH,

        /**
         * 工作事项提醒
         */
        WORK,

        /**
         * 帐号动态
         */
        ACCOUNT,

        /**
         * 订单&物流
         */
        EXPRESS,

        /**
         * 财务
         */
        FINANCE,

        /**
         * 设备提醒
         */
        DEVICE_REMINDER,

        /**
         * 邮件
         */
        MAIL,

        /**
         * 语音播报（仅透传消息支持）
         */
        PLAY_VOICE,

        /**
         * 内容推荐、新闻、财经动态、生活资讯、社交动态、调研、产品促销、功能推荐、运营活动（仅对内容进行标识，不会加快消息发送）
         */
        MARKETING

    }

    enum VivoCategory {

        /**
         * 即时聊天
         */
        IM,

        /**
         * 日程待办
         */
        TODO,

        /**
         * 订阅
         */
        SUBSCRIPTION,

        /**
         * 订单与物流
         */
        ORDER,

        /**
         * 帐号动态
         */
        ACCOUNT,

        /**
         * 设备提醒
         */
        DEVICE_REMINDER,

        /**
         * 新闻
         */
        NEWS,

        /**
         * 内容推荐
         */
        CONTENT,

        /**
         * 社交动态
         */
        SOCIAL,

        /**
         * 内容推荐、新闻、财经动态、生活资讯、社交动态、调研、产品促销、功能推荐、运营活动（仅对内容进行标识，不会加快消息发送）
         */
        MARKETING

    }

}
