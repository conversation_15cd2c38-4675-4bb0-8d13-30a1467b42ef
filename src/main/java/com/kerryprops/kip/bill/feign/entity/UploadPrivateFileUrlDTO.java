package com.kerryprops.kip.bill.feign.entity;

import jakarta.validation.constraints.NotBlank;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 私有文件上传DTO
 *
 * <AUTHOR>
 * Created Date - 2024-1-16
 */
@Data
@Builder
public class UploadPrivateFileUrlDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotBlank
    private String fileUrl;

    private Boolean openOssContentDisposition;

}
