package com.kerryprops.kip.bill.feign.entity;

import lombok.Getter;

/***********************************************************************************************************************
 * Project - tenant-admin-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 08/12/2021 13:46
 **********************************************************************************************************************/

@Getter
public enum StaffAuditStatusEnum {

    STATUS_1(1, "office: 待处理, other: 待认证"),
    STATUS_2(2, "office: 在职中, other: 已认证"),
    STATUS_3(3, "office: 已离职, other: 已禁用"),
    STATUS_4(4, "office: 已驳回, other: 已驳回，这个数据删除");

    private final Integer code;

    private final String desc;

    StaffAuditStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
