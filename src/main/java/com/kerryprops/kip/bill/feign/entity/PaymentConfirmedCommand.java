package com.kerryprops.kip.bill.feign.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 您好，您有一个物业账单支付成功
 * 申请类型：物业账单
 * 支付方式：微信支付
 * 支付金额：0.02元
 * 支付人：用户李
 * 支付时间：2021年6月11日 18:23
 */
@Data
public class PaymentConfirmedCommand {

    /* 标题 */
    @JsonProperty(value = "first")
    private String headline;

    /* 申请类型 */
    @JsonProperty(value = "keyword1")
    private String paymentType;

    /* 支付方式 */
    @JsonProperty(value = "keyword2")
    private String paymentMethod;

    /* 支付金额 */
    @JsonProperty(value = "keyword3")
    private String paymentAmount;

    /* 支付人 */
    @JsonProperty(value = "keyword4")
    private String payerName;

    /* 支付时间 */
    @JsonProperty(value = "keyword5")
    private String paymentTime;

    /* 跳转链接 */
    @JsonProperty(value = "url")
    private String url;

    /* hive 楼盘ID */
    private String projectId;

    /* hive 楼栋ID */
    private List<String> buildingIds;

    /* profile user ID list */
    private List<String> userIds;

    /* 备注 */
    private String remark;

    /* 模板消息类型 */
    private String templateMsgType = "PAYMENT_CONFIRMED";

}
