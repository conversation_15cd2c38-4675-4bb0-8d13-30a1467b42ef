package com.kerryprops.kip.bill.feign.clients;

import com.kerryprops.kip.bill.common.current.JsonUtils;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.feign.entity.FeeConfigResponse;
import com.kerryprops.kip.bill.feign.entity.FeeConfigVo;
import com.kerryprops.kip.bill.feign.entity.ForPropertyVo;
import com.kerryprops.kip.bill.feign.entity.InvoiceVo;
import com.kerryprops.kip.bill.feign.entity.TaxClassifyCodesResource;
import com.kerryprops.kip.bill.feign.entity.UploadInvoiceMainV2Vo;
import com.kerryprops.kip.bill.feign.entity.UploadInvoiceMainVo;
import com.kerryprops.kip.bill.webservice.vo.resp.TaxClassifyCodeResponse;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collections;
import java.util.List;

public interface KipInvoiceClient {

    @PostMapping(
            value = {"/kerry/invoice/invoiceUpload"},
            produces = {"application/json"},
            consumes = {"application/json"}
    )
    @Deprecated
    RespWrapVo<Object> invoiceUpload(@RequestBody UploadInvoiceMainVo var1);

    @PostMapping(value = "/v2/kerry/invoice/invoiceUpload", produces = MediaType.APPLICATION_JSON_VALUE
            , consumes = MediaType.APPLICATION_JSON_VALUE)
    RespWrapVo<Object> invoiceUploadV2(@RequestBody UploadInvoiceMainV2Vo request);

    @GetMapping("/fee/config")
    FeeConfigVo getFeeConfig(@RequestParam("id") Long id);

    @GetMapping("/fee/configs")
    List<FeeConfigResponse> listFeeConfigs(@RequestParam("projectId") String projectId
            , @RequestParam("companyCode") String companyCode);

    /*@Operation(description = "物业缴费类型的公司税收分类编码查询")
    @GetMapping(value = "/tax/classify/forProperty")
    RespWrapVo<List<ForPropertyVo>> forProperty(@RequestParam("billCodes") String billCodes, @RequestParam("companyCode") String companyCode);*/

    @GetMapping(value = "/tax/classify/company", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    RespWrapVo<TaxClassifyCodeResponse> getClassifyCode(@RequestParam("companyCode") String companyCode, @RequestParam("feeType") String feeType);

    @GetMapping(value = "/v2/tax/classify_codes")
    List<TaxClassifyCodesResource> listTaxClassifyConfig(@RequestParam("companyCode") String companyCode);

    @RequestMapping(value = "/kerry/invoice_infos/order_types/{order_type}/salesbill_nos/{salesbill_no}",
            method = RequestMethod.GET,
            consumes = MediaType.TEXT_HTML_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    String listInvoice(@PathVariable("order_type") String orderType
            , @PathVariable("salesbill_no") String salesBillNo);

    default List<InvoiceVo> listBillingInvoice(String paymentId) {
        String body = listInvoice("BILLING", paymentId);
        if (StringUtils.startsWith(body, "[")) {
            return JsonUtils.stringToList(body, InvoiceVo.class);
        }
        return Collections.emptyList();
    }

}
