package com.kerryprops.kip.bill.feign.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UploadInvoiceMainVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(
            title = "业务单号",
            required = true
    )
    private String salesbillNo;

    @Schema(
            title = "单据类型(公寓HMS、停车场CARPARK、JDE-Manual等)",
            required = true
    )
    private String salesbillType;

    @Schema(
            title = "发票类型：gvat-增值税普通发票 svat-增值税专⽤发票 gvate-增值税电⼦普票发票 svate-增值税电⼦专⽤发票" +
                    " gvatq-数电普通发票 svatq-数电专用发票 gvatz-数电纸质普通发票 svatz-数电纸质专用发票",
            required = true
    )
    private String invoiceType;

    @Schema(title = "发票备注")
    private String remark;

    @Schema(
            title = "销方统一信用代码 ",
            required = true
    )
    private String sellerNo;

    @Schema(title = "销方银行账号")
    private String sellerBankAccount;

    @Schema(title = "销方地址")
    private String sellerAddress;

    @Schema(title = "销方银行名称")
    private String sellerBankName;

    @Schema(title = "销方名称")
    private String sellerName;

    @Schema(title = "销方税号")
    private String sellerTaxNo;

    @Schema(title = "销方电话")
    private String sellerTel;

    @Schema(title = "接收邮箱 邮箱票易通发")
    private String receiveUserEmail;

    @Schema(title = "接收方电话 短信由各平台发")
    private String receiveUserTel;

    @Schema(title = "购方编号")
    private String purchaserNo;

    @Schema(title = "购方抬头（专票必填、禁止特殊字符）")
    private String purchaserName;

    @Schema(title = "购方税号")
    private String purchaserTaxNo;

    @Schema(title = "公司地址（专票必填、地址电话总长不能超过100字节，一个中文占两个字节，一个英文或数字占一个字节，禁止特殊字符）")
    private String purchaserAddress;

    @Schema(title = "公司电话（专票必填）")
    private String purchaserTel;

    @Schema(title = "银行名称（专票必填、银行名称账号总长不能超过100字节，一个中文占两个字节，一个英文或数字占一个字节 ，禁止特殊字符）")
    private String purchaserBankName;

    @Schema(title = "银行账号（专票必填、银行名称账号总长不能超过100字节，一个中文占两个字节，一个英文或数字占一个字节 ，禁止特殊字符）")
    private String purchaserBankAccount;

    @Schema(
            title = "计价方式： 0 不含税单价、1.含税单价 值为1 只传含税金额",
            required = true
    )
    private Integer priceMethod;

    @Schema(title = "含税金额 ，保留2位小数")
    private BigDecimal amountWithTax;

    @Schema(title = "不含税金额 ，保留2位小数")
    private BigDecimal amountWithoutTax;

    @Schema(title = "税额 ，保留2位小数")
    private BigDecimal taxAmount;

    @Schema(title = "收款人姓名（优先使用该参数）如果没有则可以从发票管理平台")
    private String cashierName;

    @Schema(title = "复核人姓名（优先使用该参数）如果没有则可以从发票管理平台取")
    private String checkerName;

    @Schema(title = "开票人姓名（优先使用该参数）如果没有则可以从发票管理平台取")
    private String invoicerName;

    @Schema(title = "业态：********、******** 票易通 ext1 ")
    private String businessType;

    @Schema(title = "扩展字段1 北京特殊情况，枚举A/R、BJKR 票易通 ext2")
    private String ext1;

    @Schema(title = "扩展字段2  票易通 ext3")
    private String ext2;

    @Schema(title = "扩展字段3  票易通 ext4")
    private String ext3;

    @Schema(
            title = "系统id",
            required = true
    )
    private String systemId;

    @Schema(
            title = "时间戳",
            required = true
    )
    private String timeStamp;

    @Schema(
            title = "签名",
            required = true
    )
    private String sign;

    @Schema(title = "数电场景：原始发票号码 (红字开票时上传该字段)")
    private String originalInvoiceNo;

    @Schema(title = "数电场景：原始发票代码 (红字开票时上传该字段)")
    private String originalInvoiceCode;

    @Schema(title = "数电场景：原发票类型(红字开票时上传该字段)")
    private String originalInvoiceType;

    @Schema(title = "数电场景：原蓝票开票日期(红字开票时上传该字段)")
    private String originalDateIssued;

    @Schema(title = "数电场景：红字信息编号(红字开票时上传该字段)")
    private String redLetterNumber;

    @Schema(title = "数电场景：红冲原因((红字开票时上传该字段))：sales_return-销货退回，making_error-开票有误，taxable_service_end-应税服务终，sales_allowance-发生销售折让")
    private String reverseReason;

    @Schema(title = "明细集合")
    private List<UploadInvoiceDetailVo> detailVos;

    @Schema(title = "无意义，只是为了暂存楼盘名")
    private String projectName;

}
