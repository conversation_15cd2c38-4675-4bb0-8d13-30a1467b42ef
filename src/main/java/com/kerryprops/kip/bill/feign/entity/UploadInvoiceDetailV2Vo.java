package com.kerryprops.kip.bill.feign.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class UploadInvoiceDetailV2Vo implements Serializable {

    private String salesbillItemNo;

    private String itemSpec;

    private String quantityUnit;

    private BigDecimal quantity;

    private BigDecimal outterDiscountWithTax;

    private BigDecimal amountWithTax;

    private BigDecimal amountWithoutTax;

    private BigDecimal taxAmount;

    private BigDecimal unitPriceWithTax;

    private BigDecimal unitPrice;

    private String zeroTax;

    private String remark;

    private String ext1;

    private String ext2;

    private String billCode;

}