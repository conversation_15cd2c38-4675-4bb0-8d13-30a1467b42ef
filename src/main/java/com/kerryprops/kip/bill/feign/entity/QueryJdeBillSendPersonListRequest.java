package com.kerryprops.kip.bill.feign.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/***********************************************************************************************************************
 * Project - tenant-admin-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 07/23/2021 17:12
 **********************************************************************************************************************/

@Getter
@Setter
@Schema
public class QueryJdeBillSendPersonListRequest implements Serializable {

    @Valid
    @NotNull
    @Schema(title = "多个查询类")
    @Size(min = 1, message = "至少有一个对象")
    private List<QueryJdeBillSendPersonRequest> list = new ArrayList<>();

}
