package com.kerryprops.kip.bill.feign.clients;

import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.feign.entity.IdentityAuditType;
import com.kerryprops.kip.bill.feign.entity.TenantStaffResponse;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/***********************************************************************************************************************
 * Project - hive-view-assembler-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - <PERSON>
 * Created Date - 06/09/2021 14:43
 **********************************************************************************************************************/
public interface BUserClient {

    @GetMapping("/tenant/staffs/list")
    @Operation(summary = "查询租户认证成功的用户信息")
    RespWrapVo<List<TenantStaffResponse>> getStaffList(@RequestParam(value = "auditType", required = false) IdentityAuditType auditType,
                                                       @RequestParam("authorizer") Integer authorizer,
                                                       @RequestParam("roomId") String roomId);

    /*@GetMapping("/tenant/admin/financial/list")
    @Operation(summary = "查询租户下的财务人员列表")
    RespWrapVo<List<TenantManagerResponse>> getTenantFinancialList(@Parameter(description = "租户编号", required = true)
                                                                   @NotBlank(message = "租户编号不能为空")
                                                                   @RequestParam("tenantNo") String tenantNo);*/

}
