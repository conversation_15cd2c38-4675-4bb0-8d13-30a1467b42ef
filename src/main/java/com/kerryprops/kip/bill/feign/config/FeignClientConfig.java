package com.kerryprops.kip.bill.feign.config;

import com.kerryprops.kip.bill.feign.clients.BUserClient;
import com.kerryprops.kip.bill.feign.clients.CUserClient;
import com.kerryprops.kip.bill.feign.clients.FileClient;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.clients.HiveClient;
import com.kerryprops.kip.bill.feign.clients.KipInvoiceClient;
import com.kerryprops.kip.bill.feign.clients.KlClient;
import com.kerryprops.kip.bill.feign.clients.MessageCenterClient;
import com.kerryprops.kip.bill.feign.clients.MessageClient;
import com.kerryprops.kip.bill.feign.clients.SUserClient;
import feign.Feign;
import feign.Logger;
import feign.form.spring.SpringFormEncoder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.cloud.openfeign.support.SpringMvcContract;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.zalando.logbook.Logbook;
import org.zalando.logbook.openfeign.FeignLogbookLogger;

@Getter
@Setter
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "application.services")
@ConditionalOnProperty(prefix = "feign.client", name = "enabled", havingValue = "true")
public class FeignClientConfig {

    private final FeignInterceptor tokenInterceptor;

    private final SpringMvcContract contract = new SpringMvcContract();

    private final SpringFormEncoder encoder;

    private final SpringDecoder decoder;

    private final Logbook logbook;

    private String hiveAs;

    private String cUser;

    private String bUser;

    private String sUser;

    private String file;

    private String message;

    private String messageCenter;

    private String klClient;

    private String kipInvoice;

    private String unifiedMessage = "http://unified-messaging-service";

    private String hiveService = "http://hive-service";

    public FeignClientConfig(FeignInterceptor tokenInterceptor, Logbook logbook,
                             ObjectFactory<HttpMessageConverters> messageConverters) {
        this.tokenInterceptor = tokenInterceptor;
        this.logbook = logbook;
        encoder = new SpringFormEncoder(new SpringEncoder(messageConverters));
        decoder = new SpringDecoder(messageConverters);
    }

    @Bean
    @ConditionalOnMissingBean(name = "klClient")
    @ConditionalOnProperty(prefix = "application.services", name = "klClient")
    public KlClient klClient() {
        return buildClient(KlClient.class, klClient);
    }

    @Bean
    @ConditionalOnMissingBean(name = "hiveAsClient")
    @ConditionalOnProperty(prefix = "application.services", name = "hiveAs")
    public HiveAsClient hiveAsClient() {
        return buildClient(HiveAsClient.class, hiveAs);
    }

    @Bean
    @ConditionalOnMissingBean(name = "cUserClient")
    @ConditionalOnProperty(prefix = "application.services", name = "cUser")
    public CUserClient cUserClient() {
        return buildClient(CUserClient.class, cUser);
    }

    @Bean
    @ConditionalOnMissingBean(name = "bUserClient")
    @ConditionalOnProperty(prefix = "application.services", name = "bUser")
    public BUserClient bUserClient() {
        return buildClient(BUserClient.class, bUser);
    }

    @Bean
    @ConditionalOnMissingBean(name = "kipInvoiceClient")
    @ConditionalOnProperty(prefix = "application.services", name = "kipInvoice")
    public KipInvoiceClient kipInvoiceClient() {
        return buildClient(KipInvoiceClient.class, kipInvoice);
    }

    @Bean
    @ConditionalOnMissingBean(name = "sUserClient")
    @ConditionalOnProperty(prefix = "application.services", name = "sUser")
    public SUserClient sUserClient() {
        return buildClient(SUserClient.class, sUser);
    }

    @Bean
    @ConditionalOnMissingBean(name = "fileClient")
    @ConditionalOnProperty(prefix = "application.services", name = "file")
    public FileClient fileClient() {
        return buildClient(FileClient.class, file);
    }

    @Bean
    @ConditionalOnMissingBean(name = "messageClient")
    @ConditionalOnProperty(prefix = "application.services", name = "message")
    public MessageClient messageClient() {
        return buildClient(MessageClient.class, message);
    }


    @Bean
    @ConditionalOnMissingBean(name = "messageCenterClient")
    @ConditionalOnProperty(prefix = "application.services", name = "messageCenter")
    public MessageCenterClient messageCenterClient() {
        return buildClient(MessageCenterClient.class, messageCenter);
    }

    @Bean
    @ConditionalOnMissingBean(name = "hiveClient")
    @ConditionalOnProperty(prefix = "application.services", name = "hive", matchIfMissing = true)
    public HiveClient hiveClient() {
        return buildClient(HiveClient.class, hiveService);
    }

    private <T> T buildClient(Class<T> clientClass, String baseUrl) {
        return Feign.builder()
                    .encoder(encoder)
                    .decoder(decoder)
                    .logLevel(Logger.Level.BASIC)
                    .logger(new FeignLogbookLogger(logbook))
                    .contract(contract)
                    .requestInterceptor(tokenInterceptor)
                    .target(clientClass, baseUrl);
    }

}
