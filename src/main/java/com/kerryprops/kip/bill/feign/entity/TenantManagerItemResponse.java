package com.kerryprops.kip.bill.feign.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/***********************************************************************************************************************
 * Project - user-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - Bert
 * Created Date - 07/15/2021 17:57
 **********************************************************************************************************************/

@Get<PERSON>
@Setter
@Schema
@NoArgsConstructor
public class TenantManagerItemResponse implements Serializable {

    @Schema(title = "租户账户id")
    private Long id;

    @Schema(title = "是否禁用，0：未禁用；1：启用")
    private String status = "1";

    @Schema(title = "租户账户姓名")
    private String userName;

    @Schema(title = "租户账户登录号")
    private String loginNo;

    @Schema(title = "租户账户手机号")
    private String phoneNumber;

    @Schema(title = "租户账户邮箱")
    private String email;

    @Schema(title = "租户账户角色，多个")
    private String roles;

    @Schema(title = "最近登录时间")
    private Date lastLoginTime;

    @Schema(title = "租户账户创建时间")
    private Date createTime;

    @Schema(title = "租户id")
    private String tenantId;

    @Schema(title = "租户名称")
    private String tenantName;

    @Schema(title = "楼栋名称，可能会有多个")
    private String buildingNames;

    public TenantManagerItemResponse(Long id, String status, String tenantId, String tenantName, String userName,
                                     String loginNo, String phoneNumber, String email, String roles, Date createTime, Date lastLoginTime) {
        this.id = id;
        this.status = status;
        this.tenantId = tenantId;
        this.tenantName = tenantName;
        this.userName = userName;
        this.loginNo = loginNo;
        this.phoneNumber = phoneNumber;
        this.email = email;
        this.roles = roles;
        this.createTime = createTime;
        this.lastLoginTime = lastLoginTime;
    }

}
