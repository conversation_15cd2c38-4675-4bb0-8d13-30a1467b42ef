package com.kerryprops.kip.bill.feign.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/***********************************************************************************************************************
 * Project - profile-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 08/13/2021 11:29
 **********************************************************************************************************************/

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema
public class UserWithRoomsResponse implements Serializable {

    @Schema(title = "用户ID")
    private String id;

    @Schema(title = "姓名")
    private String nickName;

    @Schema(title = "手机号码")
    private String phoneNumber;

    @Schema(title = "用户头像")
    private String avatar;

    @Schema(title = "性别，0：未知，1：男，2：女")
    private Integer gender;

    @Schema(title = "出生年月")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthDate;

    @Builder.Default
    @Schema(title = "rooms", name = "公寓或房间信息")
    private List<TenantStaffResource> rooms = new ArrayList<>();

}
