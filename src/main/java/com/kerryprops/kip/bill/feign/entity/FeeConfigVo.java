package com.kerryprops.kip.bill.feign.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> 2023-12-18 17:02:07
 **/
@NoArgsConstructor
@Data
public class FeeConfigVo {

    private String projectId;

    private String companyCode;

    private String name;

    private Integer pid;

    private String parentName;

    private String type;

    private String classifyCode;

    private String taxRate;

    private String billType;

    private Integer isTaxDiscount;

    private String bu;

    // 科目账
    private String paymentCate;

    // 明细账
    private String paymentDetail;

    private Integer isFreeze;

    private Integer id;

    private String createTime;

    private String updateTime;

}
