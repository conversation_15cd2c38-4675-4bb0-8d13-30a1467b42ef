package com.kerryprops.kip.bill.feign.clients;

import com.kerryprops.kip.bill.feign.entity.TenantManagerItemResponse;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/***********************************************************************************************************************
 * Project - hive-view-assembler-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - <PERSON>
 * Created Date - 06/09/2021 14:43
 **********************************************************************************************************************/
public interface SUserClient {

    /*@GetMapping("/tenant/bill/config/detailById")
    @Operation(summary = "根据租户账号id查询账单配置信息")
    RespWrapVo<List<TenantBillConfigResponse>> queryTenantBillConfigsByUserId(@RequestParam("userId") Long userId);

    @PostMapping(value = "/tenant/bill/config/jdeList", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @Operation(description = "查询租户jde账单通知人员")
    RespWrapVo<List<TenantBillConfigResponse>> queryJdeNotifyPersons(@Valid @RequestBody QueryJdeBillSendPersonListRequest request);

    @GetMapping("/tenant/manager/detail")
    @Operation(summary = "获取租户用户账号详情")
    RespWrapVo<TenantManagerItemResponse> tenantManagerDetail(@NotNull(message = "租户账号id不能为空") @RequestParam("id") Long id,
                                                              @NotBlank(message = "企业id不能为空") @RequestParam("tenantId") String tenantId);
*/
    @GetMapping("/tenant/manager/getListByPhoneNumbers")
    @Operation(summary = "根据手机号批量查询B端账号")
    List<TenantManagerItemResponse> queryLoginAccounts(@RequestParam("phoneNumbers") String phoneNumbers);

    default Map<String, TenantManagerItemResponse> queryOneBatchOfLoginAccounts(List<String> phoneNumbers) {
        String phoneNumberString = StringUtils.join(phoneNumbers, ",");
        List<TenantManagerItemResponse> responseList = queryLoginAccounts(phoneNumberString);
        if (CollectionUtils.isEmpty(responseList)) {
            return Collections.emptyMap();
        }

        Map<String, TenantManagerItemResponse> map = new HashMap<>();
        responseList.stream().forEach(tenantManagerItemResponse -> {
            String phoneNumber = tenantManagerItemResponse.getPhoneNumber();
            if (!map.containsKey(phoneNumber)) {
                map.put(phoneNumber, tenantManagerItemResponse);
            }
        });
        return map;
    }

    default Map<String, TenantManagerItemResponse> queryLoginAccounts(List<String> phoneNumbers) {
        final int BATCH_SIZE = 50;
        int total = phoneNumbers.size();
        Map<String, TenantManagerItemResponse> map = new HashMap<>();

        int batch = total / BATCH_SIZE;
        batch = total % BATCH_SIZE == 0 ? batch : batch + 1;
        for (int i = 0; i < batch; i++) {
            int fromIndex = BATCH_SIZE * i;
            int toIndex = BATCH_SIZE * i + BATCH_SIZE;
            toIndex = Math.min(toIndex, total);

            List<String> subList = phoneNumbers.subList(fromIndex, toIndex);
            Map<String, TenantManagerItemResponse> subMap = queryOneBatchOfLoginAccounts(subList);
            map.putAll(subMap);
        }
        return map;
    }

}
