package com.kerryprops.kip.bill.feign.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Schema
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AttachmentDto implements Serializable {

    @Schema(title = "附件名")
    private String name;

    @Schema(title = "附件链接")
    private String url;

}