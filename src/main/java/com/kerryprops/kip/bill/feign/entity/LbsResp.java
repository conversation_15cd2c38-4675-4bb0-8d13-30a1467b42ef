package com.kerryprops.kip.bill.feign.entity;

import com.kerryprops.kip.hiveas.webservice.vo.resp.response.BuildingResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/***********************************************************************************************************************
 * Project - hive-view-assembler-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - xinrui
 * Created Date - 10/29/2021 16:34
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LbsResp {

    @Schema
    private List<BuildingResponse> buildings;

}
