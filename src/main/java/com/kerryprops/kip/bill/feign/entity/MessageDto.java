package com.kerryprops.kip.bill.feign.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Schema
@Data
public class MessageDto implements Serializable {

    @Schema(title = "消息类型")
    @NotNull
    private MessageType messageType;

    @Schema(title = "内容")
    @NotBlank
    private String content;

    @Schema(title = "接收人ID")
    private String userId;

    @Schema(title = "接收人ID-批量")
    private List<String> userIds;

    @Schema(title = "接收人所属端：B、C、S")
    @NotBlank
    private String toType;

    @Schema(title = "内容参数")
    private Map<String, Object> params;

}
