package com.kerryprops.kip.bill.feign.clients;

import com.alibaba.fastjson.JSONObject;
import com.kerryprops.kip.bill.feign.entity.LoginBody;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/***********************************************************************************************************************
 * Project - hive-view-assembler-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - <PERSON>
 * Created Date - 06/09/2021 14:43
 **********************************************************************************************************************/
@Deprecated
public interface KlClient {

    @PostMapping(value = "/portal/newLogin")
    @Operation(summary = "KL登陆")
    JSONObject login(@RequestBody LoginBody loginBody);

    @GetMapping(value = "/portal/billing/bill/list")
//    @Headers(value = {"Authorization:{token}"})
    @Operation(description = "bill list")
    JSONObject list(@RequestParam(value = "pageNum", required = false) Integer pageNum,
                    @RequestParam(value = "pageSize", required = false) Integer pageSize,
                    @RequestParam(value = "year", required = false) Integer year,
                    @RequestParam(value = "month", required = false) Integer month,
                    @RequestParam(value = "inputTpMcuStr", required = false) String inputTpMcuStr,
                    @RequestHeader(value = "authorization") String authorization);

}
