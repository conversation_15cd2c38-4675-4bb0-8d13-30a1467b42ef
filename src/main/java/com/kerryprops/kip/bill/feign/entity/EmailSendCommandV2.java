package com.kerryprops.kip.bill.feign.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/*****************************************************************************
 * Project - unified-messaging-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - <PERSON>
 * Created Date - 07/05/2021 15:34
 *****************************************************************************/

@Data
@Schema
@JsonIgnoreProperties(ignoreUnknown = true)
public class EmailSendCommandV2 implements Serializable {

    @Schema(title = "发往的邮箱", required = true)
    @NotEmpty(message = "parameter sendTos is required")
    private List<String> sendTos;

    @Schema(title = "邮件主题")
    @NotBlank(message = "subject can not be null")
    private String subject;

    @Schema(title = "邮件内容-文本")
    @NotBlank(message = "email content can not be null")
    private String text;

    @Schema(title = "是否为富文本")
    private boolean html;

    @Schema(title = "附件")
    private List<AttachmentDto> attachments;

    @Schema(title = "发送邮件的业务类型", example = "COMMON")
    private String businessType = "COMMON";

    @Schema(title = "发送邮件的源服务", example = "KIP")
    private String source = "KIP";

    @Schema(title = "业务方回调接口URL")
    private String callbackUrl;

    @Schema(hidden = true)
    private String requestId;

}
