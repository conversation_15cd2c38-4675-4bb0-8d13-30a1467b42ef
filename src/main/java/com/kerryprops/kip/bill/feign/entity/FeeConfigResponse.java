package com.kerryprops.kip.bill.feign.entity;

import lombok.Data;

/**
 * 杂费配置Response
 *
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * Created Date - 2024-2-26 20:21:21
 */
@Data
public class FeeConfigResponse {

    // 楼盘id
    private String projectId;

    // 公司编码
    private String companyCode;

    // 费用名
    private String name;

    // 上一级费用目录id，没有则为 -1
    private Long pid;

    // 上一级费用名
    private String parentName;

    // 费用类型[level-1:一级杂费|level-2:二级杂费|tax:税金|tip:手续费]
    private String type;

    // 税收分类编码
    private String classifyCode;

    // 税率
    private String taxRate;

    // 业务单类型
    private String billType;

    // 是否税收优惠
    private Integer isTaxDiscount;

    // 经营单位编码
    private String bu;

    // 科目账
    private String paymentCate;

    // 明细账
    private String paymentDetail;

    // 是否冻结[0:否|1:是]
    private Integer isFreeze;

}
