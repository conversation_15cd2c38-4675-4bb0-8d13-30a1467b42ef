package com.kerryprops.kip.bill.feign.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Schema(name = "预签名url")
public class OssPreSignedUrlResponse {

    @Schema(title = "原url")
    private String originalUrl;

    @Schema(title = "预签名url")
    private String preSignedUrl;

}
