package com.kerryprops.kip.bill.feign.entity;


import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @ClassName: ForPropertyVo
 * @Author: <PERSON>non
 * @Date: 2022/2/8 15:04
 * @Version: 2.0
 */
@NoArgsConstructor
@Data
public class ForPropertyVo {

    private String billCode;

    private String billType;

    private Integer blockChain;

    private String classifyCode;

    private String companyCode;

    private String createTime;

    private String feeType;

    private String feeTypeName;

    private Integer id;

    private BigDecimal rate;

    private String serviceName;

    private Long feeId;

    private String feeName;

}
