package com.kerryprops.kip.bill.feign.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - tenant-admin-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 07/23/2021 16:56
 **********************************************************************************************************************/

@Getter
@Setter
@Schema
public class QueryJdeBillSendPersonRequest implements Serializable {

    @NotBlank(message = "租户编号不能为空")
    @Schema(title = "hive租户编号")
    private String tenantId;

    @Schema(title = "an8地址号")
    private String an8;

}
