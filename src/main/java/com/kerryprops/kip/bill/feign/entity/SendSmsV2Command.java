package com.kerryprops.kip.bill.feign.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SendSmsV2Command implements Serializable {

    // 最大1000个号码，国内短信填写，国际短信无需再传
    private List<String> phoneNumbers;

    // 国际短信接受手机号，最大1000个号码
    private List</* @Valid */ AreaPhone> areaPhones;

    // 短信内容
    private String content;

    // 楼盘
    private String projectId;

    // 消息类型；字段格式为String。通知类：NOTIFICATION， 营销类：COMMERCIAL。默认：通知类
    private String smsType;

    // 业态: APARTMENT, OFFICE, PARKING, RESIDENCE, RETAIL
    private String assetType;

    private String callbackUrl;

    @Data
    public static class AreaPhone {

        // 接收短信手机号码
        private String phoneNumber;

        // 国际电话区号，推荐携带 +，例如：+44
        private String areaCode;

    }

}
