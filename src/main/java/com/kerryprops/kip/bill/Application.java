package com.kerryprops.kip.bill;

import com.kerryprops.kip.bill.common.jpa.BaseJpaRepositoryImpl;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableJpaRepositories(repositoryBaseClass = BaseJpaRepositoryImpl.class)
@SpringBootApplication(scanBasePackages = {"com.kerryprops.kip.bill"})
@EnableScheduling
@EnableAsync(proxyTargetClass = true)
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

}
