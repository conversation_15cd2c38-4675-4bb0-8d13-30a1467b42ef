package com.kerryprops.kip.bill.interceptors;

import com.kerryprops.kip.bill.common.current.LoginUser;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.Objects;

@Component
@Slf4j
public class BizAuthInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        LoginUser loginUser = UserInfoUtils.getUser();
        if (Objects.isNull(loginUser)) {
            log.error("未登陆用户");
            return false;
        }

        if (!StringUtils.equalsIgnoreCase("B", loginUser.getFromType())) {
            log.error("用户类型非法: {}", loginUser);
            return false;
        }

        return true;
    }

}