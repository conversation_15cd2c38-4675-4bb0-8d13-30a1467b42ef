package com.kerryprops.kip.bill.interceptors;

import com.kerryprops.kip.bill.common.current.LoginUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/***********************************************************************************************************************
 * Project - decoration-review-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - Bert
 * Created Date - 06/28/2021 16:04
 **********************************************************************************************************************/

@Slf4j
@Component
public class UserInfoInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String userInfo = request.getHeader(LoginUtils.USER_HTTP_HEADER);
        if (StringUtils.isNotBlank(userInfo)) {
            log.info("X-User: {}", userInfo);
            UserInfoUtils.setUser(userInfo);
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        UserInfoUtils.removeUser();
    }

}
