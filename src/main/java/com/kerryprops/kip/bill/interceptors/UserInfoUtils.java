package com.kerryprops.kip.bill.interceptors;

import com.kerryprops.kip.bill.common.current.JsonUtils;
import com.kerryprops.kip.bill.common.current.LoginUser;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/***********************************************************************************************************************
 * Project - decoration-review-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - Bert
 * Created Date - 06/28/2021 16:08
 **********************************************************************************************************************/

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class UserInfoUtils {

    private static final ThreadLocal<LoginUser> userLocal = new ThreadLocal<>();

    public static LoginUser setUser(String userString) {
        LoginUser loginUser = JsonUtils.stringToObj(userString, LoginUser.class);
        if (loginUser != null && loginUser.isClient() && loginUser.getcId() == null) {
            loginUser.setCId(loginUser.getCid2());
        }
        userLocal.set(loginUser);
        return loginUser;
    }

    public static void removeUser() {
        userLocal.remove();
    }

    public static LoginUser getUser() {
        return userLocal.get();
    }

    public static void setUser(LoginUser user) {
        userLocal.set(user);
    }

    /**
     * 获取当前请求用户-S端账号的userId
     *
     * @return
     */
    public static Long getUserId() {
        LoginUser loginUser = userLocal.get();
        if (Objects.nonNull(loginUser)) {
            return loginUser.getUserId();
        }
        return null;
    }

    public static Boolean isSuperAdmin() {
        LoginUser loginUser = userLocal.get();
        if (Objects.nonNull(loginUser) && StringUtils.isNotBlank(loginUser.getRoles())) {
            return loginUser.getRoles().contains("SUPER_ADMIN");
        }
        return Boolean.FALSE;
    }

    public static String getUsername() {
        return Optional.ofNullable(getUser()).map(LoginUser::getNickName).orElse(null);
    }

    public static List<String> maxBindingScope() {
        LoginUser loginUser = getUser();
        if (loginUser == null) {
            throw new RuntimeException("not login.");
        }
        if (Boolean.TRUE.equals(loginUser.isSuperAdmin())) {
            return null;
        }
        return loginUser.toBuildingIdList();
    }

    /**
     * 获取当前请求用户的kerry+账号
     *
     * @return
     */
    public static String getKerryAccount() {
        return Optional.ofNullable(getUser())
                .map(LoginUser::getNickName)
                .orElse(StringUtils.EMPTY);
    }

    /**
     * 获取当前请求用户的唯一id， C端用户回cid，S端账号返回userId
     *
     * @return
     */
    public static String getUserProfileId() {
        return Optional.ofNullable(getUser())
                .map(LoginUser::getUniqueUserId)
                .orElse(StringUtils.EMPTY);
    }

    public static String getPhoneNumber() {
        return Optional.ofNullable(getUser())
                .map(LoginUser::getPhoneNumber)
                .orElse(StringUtils.EMPTY);
    }

}
