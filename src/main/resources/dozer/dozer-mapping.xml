<?xml version="1.0" encoding="UTF-8"?>
<mappings xmlns="http://dozermapper.github.io/schema/bean-mapping"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://dozermapper.github.io/schema/bean-mapping http://dozermapper.github.io/schema/bean-mapping.xsd">

    <!--全局配置，配置日期的映射格式-->
    <configuration>
        <date-format>yyyy-MM-dd HH:mm:ss</date-format>
    </configuration>
<!--    wildcard 默认为true,自动对对象中每一个属性进行映射(含隐式);若为false,只会对显式映射的属性进行复制-->
<!--        <mapping wildcard="true">-->
<!--            <class-a>com.huang.dozer.bean.User</class-a>-->
<!--            <class-b>com.huang.dozer.bean.UserXmlDestinationObject</class-b>-->
<!--            <field>-->
<!--                <a>name</a>-->
<!--                <b>username</b>-->
<!--            </field>-->
<!--            <field>-->
<!--                <a>birthday</a>-->
<!--                <b>dateOfBirth</b>-->
<!--            </field>-->
<!--        </mapping>-->
</mappings>
