
SET NAMES utf8;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
--  Table structure for `apt_bill`
--  query by: project_id is null
--  query by: bill_no, payment_status, push_status, room_id, building_id
-- ----------------------------
DROP TABLE IF EXISTS `apt_bill`;
CREATE TABLE `apt_bill` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `bill_no` varchar(32) NOT NULL DEFAULT '',
  `category` varchar(64) DEFAULT NULL,
  `bu` varchar(32) NOT NULL DEFAULT '',
  `unit` varchar(32) NOT NULL DEFAULT '',
  `an8` varchar(32) DEFAULT NULL,
  `alph` varchar(128) DEFAULT NULL,
  `doco` varchar(64) DEFAULT NULL,
  `begin_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `year` int DEFAULT NULL,
  `month` int DEFAULT NULL,
  `bill_month` int DEFAULT NULL,
  `amt` decimal(32,2) NOT NULL,
  `status` varchar(32) NOT NULL DEFAULT '',
  `payment_status` varchar(32) DEFAULT NULL,
  `payment_result` varchar(256) DEFAULT NULL,
  `push_status` varchar(32) DEFAULT NULL,
  `project_id` varchar(32) DEFAULT NULL,
  `building_id` varchar(32) DEFAULT NULL,
  `floor_id` varchar(32) DEFAULT NULL,
  `room_id` varchar(32) DEFAULT NULL,
  `position_item` varchar(500) DEFAULT NULL,
  `deleted_at` int NOT NULL DEFAULT '0',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `pay_session` varchar(128) DEFAULT NULL COMMENT '支付session',
  `rd_glc` varchar(32) DEFAULT NULL COMMENT '票据码',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  PRIMARY KEY (`id`),
  KEY `idx_bill_no` (`bill_no`) comment 'index for biz bill number',
  KEY `idx_building_id` (`building_id`) comment 'index for building id',
  KEY `idx_room_id` (`room_id`) comment 'index for room ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
--  Table structure for `apt_bill_link`
-- ----------------------------
DROP TABLE IF EXISTS `apt_bill_link`;
CREATE TABLE `apt_bill_link` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `bill_no` varchar(32) NOT NULL DEFAULT '',
  `jde_bill_id` int NOT NULL,
  `deleted_at` int NOT NULL DEFAULT '0',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
--  Table structure for `apt_bill_log`
-- ----------------------------
DROP TABLE IF EXISTS `apt_bill_log`;
CREATE TABLE `apt_bill_log` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `bill_id` int DEFAULT NULL,
  `bill_no` varchar(32) NOT NULL DEFAULT '',
  `category` varchar(64) DEFAULT NULL,
  `bu` varchar(32) NOT NULL DEFAULT '',
  `unit` varchar(32) NOT NULL DEFAULT '',
  `an8` varchar(32) DEFAULT NULL,
  `begin_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `year` int DEFAULT NULL,
  `month` int DEFAULT NULL,
  `amt` decimal(32,2) NOT NULL,
  `status` varchar(32) NOT NULL DEFAULT '',
  `project_id` varchar(32) DEFAULT NULL,
  `building_id` varchar(32) DEFAULT NULL,
  `floor_id` varchar(32) DEFAULT NULL,
  `room_id` varchar(32) DEFAULT NULL,
  `payment_status` varchar(32) DEFAULT NULL,
  `payment_result` varchar(256) DEFAULT NULL,
  `push_status` varchar(32) DEFAULT NULL,
  `deleted_at` int NOT NULL DEFAULT '0',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_bill_no` (`bill_no`) comment 'index for bill no',
  KEY `idx_bill_id` (`bill_id`) comment 'index for bill id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- ----------------------------
--  Table structure for `apt_pay`
-- ----------------------------
DROP TABLE IF EXISTS `apt_pay`;
CREATE TABLE `apt_pay` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `pay_channel` varchar(32) DEFAULT NULL COMMENT '费用类型说明',
  `pay_desc` varchar(64) DEFAULT NULL COMMENT '描述',
  `begin_date` datetime DEFAULT NULL COMMENT '收费时间段开始',
  `end_date` datetime DEFAULT NULL COMMENT '收费时间段结束',
  `pay_config_id` int DEFAULT NULL COMMENT '支付配置ID',
  `pay_type` varchar(64) DEFAULT NULL COMMENT '支付方式',
  `payment_cate` varchar(11) DEFAULT NULL COMMENT '科目账',
  `tax` decimal(32,6) DEFAULT NULL COMMENT '手续费率',
  `tax_amt` decimal(32,2) DEFAULT NULL COMMENT '手续费',
  `total_amt` decimal(32,2) DEFAULT NULL COMMENT '合计',
  `pay_date` datetime DEFAULT NULL COMMENT '支付时间',
  `pay_tranx` varchar(32) NOT NULL DEFAULT '' COMMENT '支付流水',
  `pay_act` varchar(64) DEFAULT NULL COMMENT '收款号',
  `pay_detail` varchar(512) DEFAULT NULL COMMENT '支付详情',
  `verify_status` int NOT NULL DEFAULT '0' COMMENT '复核状态 0 未复核 1 已复核',
  `send_jde_status` int NOT NULL DEFAULT '0' COMMENT '是否写入JDE',
  `project_id` varchar(32) DEFAULT NULL,
  `building_id` varchar(32) DEFAULT NULL,
  `floor_id` varchar(32) DEFAULT NULL,
  `room_id` varchar(32) DEFAULT NULL,
  `position_item` varchar(500) DEFAULT NULL,
  `bu` varchar(64) DEFAULT NULL,
  `unit` varchar(32) DEFAULT NULL,
  `doco` varchar(32) DEFAULT NULL,
  `an8` varchar(32) DEFAULT NULL,
  `alph` varchar(128) DEFAULT NULL,
  `comments` varchar(64) DEFAULT NULL COMMENT '备注',
  `deleted_at` int NOT NULL DEFAULT '0',
  `create_by` varchar(64) DEFAULT NULL COMMENT '收款人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_time` datetime DEFAULT NULL COMMENT '账单移除时间',
  `deleted_by` varchar(128) DEFAULT NULL COMMENT '账单移除人',
  `payment_info_id` varchar(32) DEFAULT NULL COMMENT '账单id',
  `version_num` int DEFAULT '0' COMMENT '版本号',
  `status` int DEFAULT '1' COMMENT '状态',
  PRIMARY KEY (`id`),
  KEY `idx_pay_date` (`pay_date`) comment 'index for pay_date',
  KEY `idx_building_id` (`building_id`) comment 'index for building ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
--  Table structure for `apt_pay_bill`
-- ----------------------------
DROP TABLE IF EXISTS `apt_pay_bill`;
CREATE TABLE `apt_pay_bill` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `pay_id` int NOT NULL,
  `bill_id` int NOT NULL,
  `bill_no` varchar(64) DEFAULT '',
  `confirmed` int NOT NULL DEFAULT '0',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_pay_id` (`pay_id`) comment 'pay_id index',
  KEY `idx_bill_id` (`bill_id`) comment 'bill_id index'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
--  Table structure for `apt_pay_config`
-- ----------------------------
DROP TABLE IF EXISTS `apt_pay_config`;
CREATE TABLE `apt_pay_config` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `channel` varchar(32) DEFAULT NULL COMMENT '付费渠道',
  `payment_type` varchar(32) DEFAULT NULL COMMENT '付费方式',
  `payment_cate` varchar(32) DEFAULT NULL COMMENT '科目账',
  `payment_detail` varchar(32) DEFAULT NULL COMMENT '明细账',
  `bank_account` varchar(64) DEFAULT NULL COMMENT '总账银行账户',
  `project_id` varchar(32) DEFAULT NULL,
  `building_id` varchar(32) DEFAULT NULL,
  `position_item` varchar(500) DEFAULT NULL,
  `tax` decimal(32,6) DEFAULT NULL COMMENT '手续费率',
  `max` decimal(32,2) DEFAULT NULL COMMENT '封顶',
  `comments` varchar(256) DEFAULT NULL COMMENT '备注',
  `delete_at` int NOT NULL DEFAULT '0',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `mcu` varchar(32) DEFAULT NULL COMMENT '科目MCU',
  `company_code` varchar(32) NOT NULL DEFAULT '' COMMENT '公司',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq` (`project_id`, `company_code`, `channel`, `payment_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
--  Table structure for `apt_payment_bill`
-- ----------------------------
DROP TABLE IF EXISTS `apt_payment_bill`;
CREATE TABLE `apt_payment_bill` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `payment_info_id` varchar(32) DEFAULT NULL COMMENT '组合账单id',
  `bill_id` int DEFAULT NULL COMMENT '账单id',
  `deleted` int NOT NULL COMMENT '是否删除 0 未删除 1 已删除',
  PRIMARY KEY (`id`),
  KEY `idx_bill_id` (`bill_id`) comment 'index for bill id',
  KEY `idx_pay_info` (`payment_info_id`) comment 'index for payment id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='账单支付关联账单关联表';

-- ----------------------------
--  Table structure for `apt_payment_info`
-- ----------------------------
DROP TABLE IF EXISTS `apt_payment_info`;
CREATE TABLE `apt_payment_info` (
  `id` varchar(32) NOT NULL,
  `amt` decimal(32,2) DEFAULT NULL COMMENT '金额',
  `payment_status` varchar(64) DEFAULT NULL COMMENT '支付状态',
  `cancel_type` varchar(32) DEFAULT NULL COMMENT '取消方式',
  `pay_session` varchar(255) DEFAULT NULL COMMENT '支付session',
  `pay_type` varchar(16) DEFAULT NULL COMMENT '支付方式',
  `payment_time` datetime DEFAULT NULL COMMENT '支付时间',
  `applied_invoice` int DEFAULT NULL COMMENT '是否已开票',
  `bind_user_id` int DEFAULT NULL COMMENT '绑定用户user',
  `project_id` varchar(32) DEFAULT NULL COMMENT '楼盘',
  `building_id` varchar(32) DEFAULT NULL COMMENT '楼栋',
  `room_id` varchar(64) DEFAULT NULL COMMENT '房间id',
  `floor_id` varchar(64) DEFAULT NULL COMMENT '楼层id',
  `position_item` varchar(500) DEFAULT NULL COMMENT '地址信息',
  `deleted` int DEFAULT NULL COMMENT '是否删除',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `invoice_url` varchar(1200) DEFAULT NULL,
  `xml_url` varchar(1500) NOT NULL DEFAULT '' COMMENT '全电xml地址',
  `ofd_url` varchar(1500) NOT NULL DEFAULT '' COMMENT '发票ofd地址',
  `psp_trans_no` varchar(64) NOT NULL DEFAULT '' COMMENT '支付平台流水号',
  `payment_trans_no` varchar(45) NOT NULL DEFAULT '' COMMENT 'KIP支付单号',
  `failed_reason` varchar(128) NOT NULL DEFAULT '' COMMENT '支付失败原因',
  `agreement_no` varchar(45) NOT NULL DEFAULT '' COMMENT 'KIP支付协议号',
  `user_profile_id` varchar(32) NOT NULL DEFAULT '' COMMENT 'KIP profile userID',
  PRIMARY KEY (`id`),
  KEY `idx_payment_userid` (`bind_user_id`) USING BTREE COMMENT '用户id索引',
  KEY `idx_payment_createtime` (`create_time`) USING BTREE COMMENT '创建时间索引 用户列表查询排序',
  KEY `idx_room_id` (`room_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='账单支付基础信息';

-- ----------------------------
--  Table structure for `apt_sync_bills`
-- ----------------------------
DROP TABLE IF EXISTS `apt_sync_bills`;
CREATE TABLE `apt_sync_bills` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `rd_edbt` varchar(15) DEFAULT '' COMMENT 'EDI批号',
  `rd_doc` int NOT NULL COMMENT '单据号',
  `rd_dct` varchar(2) NOT NULL DEFAULT '' COMMENT '单据类型',
  `rd_kco` varchar(5) NOT NULL DEFAULT '' COMMENT '单据公司',
  `rd_sfx` varchar(3) NOT NULL DEFAULT '' COMMENT '单据付款项',
  `rd_glc` varchar(4) DEFAULT NULL COMMENT '总账冲销-票据码',
  `rd_dl01` varchar(30) DEFAULT NULL COMMENT '费用类型说明',
  `rd_dl02` varchar(30) DEFAULT NULL COMMENT '费用属性/管理费/非管理费',
  `rd_date01` varchar(6) DEFAULT NULL COMMENT '账单开始日期1+YY+天数',
  `rd_date02` varchar(6) DEFAULT NULL COMMENT '账单结束日期1+YY+天数',
  `rd_ag` decimal(32,2) DEFAULT NULL COMMENT '总金额',
  `rd_mcu` varchar(12) DEFAULT NULL COMMENT 'BU',
  `rd_dl03` varchar(30) DEFAULT NULL COMMENT '建筑物',
  `rd_unit` varchar(8) DEFAULT NULL COMMENT '单元',
  `rd_an8` varchar(32) DEFAULT NULL COMMENT '地址号',
  `rd_alph` varchar(40) DEFAULT NULL COMMENT '租户名称',
  `rd_doco` varchar(32) DEFAULT NULL COMMENT '租赁合同号',
  `rd_edsp` varchar(1) DEFAULT NULL COMMENT '微小区读取标记',
  `rd_uds1` varchar(32) DEFAULT NULL COMMENT '账单开始日期YYYYMMDD',
  `rd_uds2` varchar(32) DEFAULT NULL COMMENT '账单结束日期YYYYMMDD',
  `rd_uds3` varchar(32) DEFAULT NULL COMMENT '账单创建日期',
  `rd_ev01` varchar(1) DEFAULT NULL COMMENT '预留标志位1',
  `rd_ev02` varchar(1) DEFAULT NULL COMMENT '预留标志位2',
  `rd_ev03` varchar(1) DEFAULT NULL COMMENT '预留标志位3',
  `rd_user` varchar(10) DEFAULT NULL COMMENT '用户号',
  `rd_upmj` int DEFAULT NULL COMMENT '程序号',
  `rd_upmt` varchar(32) DEFAULT NULL COMMENT '工作站号',
  `rd_pid` varchar(10) DEFAULT NULL,
  `rd_jobn` varchar(10) DEFAULT NULL,
  `pay_number_jde` varchar(32) DEFAULT NULL COMMENT '收款号',
  `bill_number` varchar(32) DEFAULT NULL COMMENT '账单号',
  `new_import` varchar(64) DEFAULT NULL COMMENT '差异导入',
  `online_verification` int NOT NULL DEFAULT '0',
  `online_verification_time` datetime DEFAULT NULL,
  `jde_verification` int NOT NULL DEFAULT '0',
  `jde_verification_time` datetime DEFAULT NULL,
  `deleted_at` int NOT NULL DEFAULT '0',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_bill_no` (`bill_number`) COMMENT 'index for bill_no',
  KEY `idx_rd_doc` (`rd_doc`) COMMENT 'index for rd_doc'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
--  Table structure for `apt_sync_bills_error`
-- ----------------------------
DROP TABLE IF EXISTS `apt_sync_bills_error`;
CREATE TABLE `apt_sync_bills_error` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `payload` text NOT NULL,
  `type` int NOT NULL COMMENT '1: 未能写入sync_bills表 2：未能正确处理并写未apt_bill表',
  `deleted_at` int DEFAULT '0',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
--  Table structure for `apt_sync_jde_job`
-- ----------------------------
DROP TABLE IF EXISTS `apt_sync_jde_job`;
CREATE TABLE `apt_sync_jde_job` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `type` varchar(32) DEFAULT NULL,
  `status` int NOT NULL COMMENT '0 已创建 3 处理中 4处理完成 5 处理失败',
  `local_cnt` int NOT NULL,
  `remote_cnt` int DEFAULT NULL,
  `error_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `start_time` datetime DEFAULT NULL,
  `end_time` datetime DEFAULT NULL,
  `create_by` varchar(32) NOT NULL DEFAULT '',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
--  Table structure for `apt_sync_jde_job_log`
-- ----------------------------
DROP TABLE IF EXISTS `apt_sync_jde_job_log`;
CREATE TABLE `apt_sync_jde_job_log` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `job_id` int DEFAULT NULL,
  `type` varchar(32) DEFAULT NULL,
  `status` int DEFAULT NULL COMMENT '0 已创建 3 处理中 4处理完成 5 处理失败',
  `local_cnt` int DEFAULT NULL,
  `remote_cnt` int DEFAULT NULL,
  `error_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `start_time` datetime DEFAULT NULL,
  `end_time` datetime DEFAULT NULL,
  `create_by` varchar(32) DEFAULT '',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `log_create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
--  Table structure for `apt_sync_paid_bill_to_jde`
-- ----------------------------
DROP TABLE IF EXISTS `apt_sync_paid_bill_to_jde`;
CREATE TABLE `apt_sync_paid_bill_to_jde` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `job_id` int NOT NULL,
  `pay_bill_id` int DEFAULT NULL,
  `status` int NOT NULL,
  `error_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `RBCKNU` varchar(32) NOT NULL DEFAULT '',
  `RCKCO` varchar(5) DEFAULT '',
  `RCDCT` varchar(2) DEFAULT '',
  `RCDOC` int DEFAULT NULL,
  `RCSFX` varchar(3) DEFAULT '',
  `RCAG` decimal(11,2) DEFAULT NULL,
  `RCDMTJ` int DEFAULT NULL,
  `RCEDSP` varchar(1) DEFAULT '',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_bill_no` (`RBCKNU`) comment 'index for bill no',
  KEY `idx_doc_no` (`RCDOC`) comment 'index for doc no'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
--  Table structure for `apt_sync_pay_to_jde`
-- ----------------------------
DROP TABLE IF EXISTS `apt_sync_pay_to_jde`;
CREATE TABLE `apt_sync_pay_to_jde` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `job_id` int NOT NULL,
  `pay_id` int DEFAULT NULL,
  `status` int NOT NULL,
  `error_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `RCCKNU` varchar(32) NOT NULL DEFAULT '',
  `RCLNID` int NOT NULL,
  `RCTYIN` varchar(1) DEFAULT '',
  `RCKCO` varchar(5) DEFAULT '',
  `RCDCT` varchar(2) DEFAULT '',
  `RCDOC` int DEFAULT NULL,
  `RCSFX` varchar(3) DEFAULT '',
  `RCAN8` int DEFAULT NULL,
  `RCALPH` varchar(40) DEFAULT '',
  `RCDMTJ` int DEFAULT NULL,
  `RCAG` decimal(11,2) DEFAULT NULL,
  `RCGLBA` varchar(8) DEFAULT '',
  `RCCRCD` varchar(3) DEFAULT '',
  `RCGLC` varchar(4) DEFAULT '',
  `RCPO` varchar(8) DEFAULT '',
  `RCMCU` varchar(12) DEFAULT '',
  `RCUNIT` varchar(8) DEFAULT '',
  `RCEDSP` varchar(1) DEFAULT '',
  `RCRMK` varchar(30) DEFAULT '',
  `RCAAAJ` decimal(11,2) DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_bill_no` (`RCCKNU`) comment 'index for bill no',
  KEY `idx_doc` (`RCDOC`) comment 'index for doc no'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
--  Table structure for `bill_error`
-- ----------------------------

/*DROP TABLE IF EXISTS `bill_error`;
CREATE TABLE `bill_error` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `type` varchar(64) NOT NULL DEFAULT '' COMMENT '错误类型',
  `err` text NOT NULL COMMENT '错误明细',
  `reviewed` int NOT NULL DEFAULT '0' COMMENT '是否处理',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;*/

-- ----------------------------
--  Table structure for `bill_jobs`
-- ----------------------------
/*DROP TABLE IF EXISTS `bill_jobs`;
CREATE TABLE `bill_jobs` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `type` varchar(32) NOT NULL DEFAULT '' COMMENT '类型',
  `status` int NOT NULL COMMENT '状态',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;*/

-- ----------------------------
--  Table structure for `invoice_record`
-- ----------------------------
DROP TABLE IF EXISTS `invoice_record`;
CREATE TABLE `invoice_record` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `invoice_no` varchar(64) DEFAULT NULL COMMENT '发票号号',
  `customer_name` varchar(255) NOT NULL COMMENT '客户名称',
  `seller_name` varchar(255) DEFAULT NULL COMMENT '销方名称',
  `tax_no` varchar(32) DEFAULT NULL COMMENT '单位税号',
  `address` varchar(255) DEFAULT NULL COMMENT '注册地址',
  `area_code` varchar(20) NOT NULL DEFAULT '' COMMENT '注册电话国际电话区号',
  `phone` varchar(64) DEFAULT NULL COMMENT '注册电话',
  `bank_name` varchar(255) DEFAULT NULL COMMENT '开户银行',
  `bank_account` varchar(128) DEFAULT NULL COMMENT '银行账号',
  `email` varchar(128) DEFAULT NULL COMMENT '接收发票的电子邮箱',
  `order_no` varchar(64) NOT NULL COMMENT '订单号',
  `order_type` varchar(32) NOT NULL COMMENT '订单类型',
  `user_id` varchar(32) DEFAULT '' COMMENT '用户id',
  `apply_time` datetime NOT NULL COMMENT '申请开票日期',
  `status` varchar(32) NOT NULL COMMENT '开票状态：开票中/已开票',
  `is_invoice_invoked` tinyint(1) DEFAULT '0' COMMENT '是否推送了开票信息到金壬系统',
  `amount` decimal(10,2) DEFAULT NULL COMMENT '不含税金额',
  `tax_rate` decimal(10,2) DEFAULT NULL COMMENT '税率',
  `tax_amount` decimal(10,2) DEFAULT NULL COMMENT '税额',
  `total_amount_after_split` varchar(100) NOT NULL DEFAULT '' COMMENT '拆票后含税金额',
  `total_amount` decimal(10,2) NOT NULL COMMENT '含税金额',
  `invoice_time` datetime DEFAULT NULL COMMENT '开票日期',
  `payee` varchar(255) DEFAULT NULL COMMENT '收款人',
  `checker` varchar(255) DEFAULT NULL COMMENT '复核人',
  `issuer` varchar(255) DEFAULT NULL COMMENT '开票人',
  `pdf_url` varchar(1200) DEFAULT NULL COMMENT '发票PDF地址',
  `xml_url` varchar(1500) NOT NULL DEFAULT '' COMMENT '全电xml地址',
  `ofd_url` varchar(1500) NOT NULL DEFAULT '' COMMENT '发票ofd地址',
  `invoice_code` varchar(128) DEFAULT NULL COMMENT '发票代码',
  `invoice_type` varchar(64) DEFAULT NULL COMMENT '发票类型',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `customer_type` varchar(32) NOT NULL COMMENT '客户类型',
  `error_message` text NOT NULL COMMENT '失败信息描述',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_invoice_record_invoice_no` (`invoice_no`),
  KEY 'idx_invoice_record_order_no' ('order_no'),
  KEY `idx_invoice_record_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='订单发票的开票记录';

-- ----------------------------
--  Table structure for `kerry_bill`
-- ----------------------------
DROP TABLE IF EXISTS `kerry_bill`;
CREATE TABLE `kerry_bill` (
  `Id` bigint NOT NULL AUTO_INCREMENT,
  `tp_dct` varchar(50) DEFAULT NULL COMMENT '账单类型',
  `tp_dl01` varchar(255) DEFAULT NULL COMMENT '账单中文描述',
  `file_url` varchar(255) DEFAULT NULL COMMENT '账单文件的链接地址',
  `tp_gtfilenm` varchar(255) DEFAULT NULL COMMENT '账单文件名',
  `tp_co` varchar(64) DEFAULT NULL COMMENT '公司编号',
  `tp_dl03` varchar(255) DEFAULT NULL COMMENT '公司名称',
  `tp_an8` varchar(32) DEFAULT NULL COMMENT '用户编号',
  `tp_alph` varchar(255) DEFAULT NULL COMMENT '用户名称',
  `tp_doco` varchar(32) DEFAULT NULL COMMENT 'jde合同号',
  `tp_mcu` varchar(100) DEFAULT NULL COMMENT '建筑物编号',
  `tp_dc` varchar(200) DEFAULT NULL COMMENT '建筑物描述',
  `tp_crtutime` datetime DEFAULT NULL COMMENT '账单生成日期',
  `tp_status` int DEFAULT '0' COMMENT '账单状态 0未发送、5发送成功、10发送失败',
  `tp_fyr` int DEFAULT NULL COMMENT '账单年',
  `tp_pn` int DEFAULT NULL COMMENT '账单月',
  `bill_month` int DEFAULT NULL,
  `tp_ev01` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '网站显示 Y为显示',
  `tp_ev02` varchar(4) DEFAULT NULL COMMENT '账单来源',
  `tp_ev03` varchar(2) DEFAULT NULL COMMENT 'JDE同步回置标志位',
  `del_flag` varchar(1) DEFAULT '0' COMMENT '删除标志',
  `format_date` varchar(20) DEFAULT NULL COMMENT '账单打印时间',
  `tp_gtitnm` varchar(200) DEFAULT NULL COMMENT '账单文件名称',
  `tp_unit` varchar(200) DEFAULT NULL COMMENT '账单单元',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `mail_status` int DEFAULT '0' COMMENT '账单站内信发送状态',
  `email_status` int DEFAULT '0' COMMENT '账单邮件发送状态',
  `mail_date` datetime DEFAULT NULL COMMENT '账单站内信发送时间',
  `email_date` datetime DEFAULT NULL COMMENT '账单邮件发送时间',
  `email_err` text COMMENT '账单的邮件解析结果',
  `mail_read_time` datetime DEFAULT NULL COMMENT '站内信最早阅读时间',
  `mobile_read_time` datetime DEFAULT NULL COMMENT '手机端最早阅读时间',
  `read_status` int DEFAULT '0' COMMENT '阅读状态 0 未读、1已读',
  `source_id` int DEFAULT NULL COMMENT '数据迁移源ID',
  `ext_1` varchar(200) DEFAULT NULL COMMENT '备份字段1',
  `delete_time` datetime DEFAULT NULL COMMENT '账单移除时间',
  `delete_by` varchar(128) DEFAULT NULL COMMENT '账单移除人',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `uk_kerry_bill` (`tp_dct`,`tp_co`,`tp_mcu`,`tp_an8`,`tp_fyr`,`tp_pn`,`tp_crtutime`),
  KEY `idx_an8_doco` (`tp_doco`,`tp_an8`),
  KEY `idx_tp_pn` (`tp_pn`),
  KEY `idx_tp_mcu` (`tp_mcu`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='嘉里电子账单';

-- ----------------------------
--  Table structure for `kerry_bill_config`
-- ----------------------------
DROP TABLE IF EXISTS `kerry_bill_config`;
CREATE TABLE `kerry_bill_config` (
  `Id` bigint NOT NULL AUTO_INCREMENT,
  `source_type` varchar(2) DEFAULT NULL COMMENT '来自不同 服务的账单类型',
  `source_name` varchar(100) DEFAULT NULL COMMENT '账单来源的中文描述',
  `http_service` varchar(255) DEFAULT NULL COMMENT '去对应的平台获取账单的服务地址',
  `create_by` varchar(100) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_by` varchar(100) DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `del_flag` varchar(2) DEFAULT '0',
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='账单配置项表 2019-12-09新增';

-- ----------------------------
--  Table structure for `kerry_bill_email_trace`
-- ----------------------------
DROP TABLE IF EXISTS `kerry_bill_email_trace`;
CREATE TABLE `kerry_bill_email_trace` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `request_id` varchar(32) NOT NULL DEFAULT '' COMMENT '邮件请求ID',
  `bill_id` int NOT NULL COMMENT '账单ID',
  `status` int NOT NULL COMMENT '状态',
  `email` varchar(64) DEFAULT NULL,
  `email_state` varchar(11) DEFAULT NULL,
  `message` varchar(256) DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `batch_no` varchar(32) DEFAULT NULL COMMENT '批次号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bill_email` (`request_id`,`bill_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
--  Table structure for `kerry_bill_log`
-- ----------------------------
DROP TABLE IF EXISTS `kerry_bill_log`;
CREATE TABLE `kerry_bill_log` (
  `Id` bigint NOT NULL AUTO_INCREMENT,
  `tp_dct` varchar(50) DEFAULT NULL COMMENT '账单类型',
  `tp_dl01` varchar(255) DEFAULT NULL COMMENT '账单中文描述',
  `file_url` varchar(512) DEFAULT NULL COMMENT '账单文件的链接地址',
  `tp_gtfilenm` varchar(255) DEFAULT NULL COMMENT '账单文件名',
  `tp_co` varchar(64) DEFAULT NULL COMMENT '公司编号',
  `tp_dl03` varchar(255) DEFAULT NULL COMMENT '公司名称',
  `tp_an8` varchar(32) DEFAULT NULL COMMENT '用户编号',
  `tp_alph` varchar(255) DEFAULT NULL COMMENT '用户名称',
  `tp_doco` varchar(32) DEFAULT NULL COMMENT 'jde合同号',
  `tp_mcu` varchar(100) DEFAULT NULL COMMENT '建筑物编号',
  `tp_dc` varchar(200) DEFAULT NULL COMMENT '建筑物描述',
  `tp_crtutime` datetime DEFAULT NULL COMMENT '账单生成日期',
  `tp_status` int DEFAULT '0' COMMENT '账单状态 0未发送、5发送成功、10发送失败',
  `tp_fyr` int DEFAULT NULL COMMENT '账单年',
  `tp_pn` int DEFAULT NULL COMMENT '账单月',
  `tp_ev01` varchar(2) DEFAULT '' COMMENT '网站显示 Y为显示',
  `tp_ev02` varchar(4) DEFAULT NULL COMMENT '账单来源',
  `del_flag` varchar(1) DEFAULT '0' COMMENT '删除标志',
  `format_date` varchar(20) DEFAULT NULL COMMENT '账单打印时间',
  `tp_gtitnm` varchar(200) DEFAULT NULL COMMENT '账单文件名称',
  `tp_unit` varchar(200) DEFAULT NULL COMMENT '账单单元',
  `create_time` datetime DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL,
  `update_by` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='嘉里电子账单log';

-- ----------------------------
--  Table structure for `kerry_message_template`
-- ----------------------------
DROP TABLE IF EXISTS `kerry_message_template`;
CREATE TABLE `kerry_message_template` (
  `Id` bigint NOT NULL AUTO_INCREMENT,
  `temp_subject` varchar(100) DEFAULT NULL COMMENT '发送邮件的主题',
  `temp_name` varchar(255) NOT NULL DEFAULT '' COMMENT '模板名称',
  `head_content` text NOT NULL,
  `foreach_content` text COMMENT '需要进行循环替换的内容',
  `tail_content` text,
  `send_channel` int NOT NULL DEFAULT '0' COMMENT '0为UMail商务通道、1为嘉里通道',
  `create_by` varchar(100) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_by` varchar(100) DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `del_flag` varchar(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='发送邮件的模板';

-- ----------------------------
--  Table structure for `kerry_notice_inbox`
-- ----------------------------
DROP TABLE IF EXISTS `kerry_notice_inbox`;
CREATE TABLE `kerry_notice_inbox` (
  `Id` bigint NOT NULL AUTO_INCREMENT,
  `receive_subject` bigint DEFAULT '0' COMMENT '接收主体id',
  `subject_type` int DEFAULT '2' COMMENT '接收主体类型 2大楼、3公司',
  `read_flag` tinyint(1) DEFAULT '0' COMMENT '是否已读0否、1是',
  `read_time` datetime DEFAULT NULL COMMENT '阅读时间',
  `notice_type` int DEFAULT '0' COMMENT '接收的通知类型0公告，1通知',
  `outbox_id` bigint DEFAULT NULL COMMENT '发件箱id',
  `fun_type` int DEFAULT NULL COMMENT '所属功能 BusinessDefinitionz中取值',
  `temp_id` bigint DEFAULT '0' COMMENT '模板id',
  `bill_id` bigint DEFAULT '0' COMMENT '账单id',
  `doco` varchar(20) DEFAULT '' COMMENT '合同号',
  `an8` varchar(20) DEFAULT '' COMMENT '用户编号',
  `mcu` varchar(20) NOT NULL DEFAULT '',
  `create_time` datetime DEFAULT NULL,
  `update_by` varchar(100) DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `del_flag` varchar(2) DEFAULT '0',
  `role_ids` varchar(100) DEFAULT '0' COMMENT '适用全部的用户角色值放0',
  PRIMARY KEY (`Id`),
  KEY `idx_fun_type` (`fun_type`),
  KEY `idx_outbox_id` (`outbox_id`),
  KEY `idx_bill_id` (`bill_id`),
  KEY `idx_mcu` (`mcu`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='通知收件箱';

-- ----------------------------
--  Table structure for `kerry_notice_outbox`
-- ----------------------------
DROP TABLE IF EXISTS `kerry_notice_outbox`;
CREATE TABLE `kerry_notice_outbox` (
  `Id` bigint NOT NULL AUTO_INCREMENT COMMENT '发件箱id',
  `sender_user` bigint DEFAULT NULL COMMENT '发送人id',
  `sender_subject` bigint DEFAULT NULL COMMENT '发送主体（楼盘、大楼、公司）',
  `subject_type` int DEFAULT NULL COMMENT '发送主体类型1楼盘、2大楼、3公司',
  `notice_channel` int DEFAULT '0' COMMENT '通知通道 1站内信、2站内信/短信、3站内信/邮件、4站内信/短信/邮件',
  `title` varchar(255) DEFAULT NULL COMMENT '标题',
  `mail_content` longtext COMMENT '站内信邮件内容',
  `sms_content` varchar(512) DEFAULT NULL COMMENT '短信内容',
  `notice_type` int DEFAULT '1' COMMENT '通知类型0公告、1通知',
  `fun_type` int DEFAULT '0' COMMENT '所属功能 BusinessDefinition中取值',
  `up_shelf_time` datetime DEFAULT NULL COMMENT '上架时间',
  `down_shelf_time` datetime DEFAULT NULL COMMENT '下架时间',
  `shelf_type` int DEFAULT '0' COMMENT '下架类型 0 未下架，1已下架',
  `self_flag` tinyint DEFAULT '0' COMMENT '是否主动发出 1是,0否',
  `aging_flag` tinyint DEFAULT '0' COMMENT '是否为时效性 0否,1是',
  `sender_emp_flag` tinyint DEFAULT '0' COMMENT '是否发送给员工',
  `sender_type` int DEFAULT '1' COMMENT '发送方类型 1物业、2用户',
  `del_flag` varchar(2) DEFAULT '0' COMMENT '删除标志',
  `create_by` varchar(100) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_by` varchar(100) DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `doco` varchar(20) DEFAULT '' COMMENT '合同号',
  `an8` varchar(20) DEFAULT '' COMMENT '用户编号',
  `mcu` varchar(20) DEFAULT NULL COMMENT '发送账单时的mcu',
  `bill_id` bigint DEFAULT '0' COMMENT '账单id',
  `file_url` varchar(1024) DEFAULT NULL COMMENT '发件箱附件',
  `temp_id` bigint DEFAULT '0',
  `send_flag` varchar(2) DEFAULT '' COMMENT '设置定时通知是否发送 0 未发送、1已发送',
  `send_date` datetime DEFAULT NULL COMMENT '设定的定时发送的时间',
  `draft_flag` varchar(2) DEFAULT '0' COMMENT '默认为0 非草稿状态，1为草稿状态',
  `role_ids` varchar(100) DEFAULT '0' COMMENT '适用全部的用户角色值放0',
  PRIMARY KEY (`Id`),
  KEY `idx_self_flag` (`self_flag`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_fun_type` (`fun_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='通知发件箱';

-- ----------------------------
--  Table structure for `kerry_rent_finance_email_setting`
-- ----------------------------
DROP TABLE IF EXISTS `kerry_rent_finance_email_setting`;
CREATE TABLE `kerry_rent_finance_email_setting` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `building_id` bigint DEFAULT NULL COMMENT '楼盘id',
  `house_id` bigint DEFAULT NULL COMMENT '大楼id',
  `email_collection` varchar(255) DEFAULT NULL COMMENT '邮箱集合',
  `del_flag` char(1) DEFAULT '0',
  `create_by` varchar(255) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_by` varchar(255) DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='长租车位维护财务邮箱';

-- ----------------------------
--  Table structure for `kerry_send_blacklist`
-- ----------------------------
DROP TABLE IF EXISTS `kerry_send_blacklist`;
CREATE TABLE `kerry_send_blacklist` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `address` varchar(128) DEFAULT NULL COMMENT '黑名单的信息地址',
  `category` int DEFAULT '2' COMMENT '类型1为手机号码，2为邮件',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `type` int DEFAULT NULL COMMENT '预留类型：后期可以进行区分哪些黑名单也发送',
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='联客发送信息的黑名单，黑名单中的不进行发送信息';

-- ----------------------------
--  Table structure for `kerry_send_email_log`
-- ----------------------------
DROP TABLE IF EXISTS `kerry_send_email_log`;
CREATE TABLE `kerry_send_email_log` (
  `Id` bigint NOT NULL AUTO_INCREMENT,
  `fkey_ids` varchar(512) DEFAULT NULL COMMENT '外键ids',
  `fkey_type` int DEFAULT NULL COMMENT '外键类型 1通知表、2账单表、3没有表',
  `send_user_id` bigint DEFAULT NULL COMMENT '发送人',
  `batch_code` varchar(64) DEFAULT NULL COMMENT '批次号',
  `send_channel` int DEFAULT '0' COMMENT '发送的通道',
  `send_subject` varchar(255) DEFAULT NULL COMMENT '要发送的邮件主题',
  `send_content` longtext COMMENT '要发送的邮件的内容',
  `send_emails` text COMMENT '收件方的 邮箱，多个以逗号分隔',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `send_status` int DEFAULT '0' COMMENT '发送状态 0未发送，5已发送',
  `del_flag` varchar(2) DEFAULT '0' COMMENT '解析过后，更改为已删除',
  `attachment_flag` tinyint NOT NULL DEFAULT '0' COMMENT '邮件是否包含附件',
  `send_result` text,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='联客的邮件发送记录，用于进行job发送';

-- ----------------------------
--  Table structure for `kerry_send_message_log`
-- ----------------------------
DROP TABLE IF EXISTS `kerry_send_message_log`;
CREATE TABLE `kerry_send_message_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `temp_id` bigint DEFAULT NULL,
  `created_time` datetime DEFAULT NULL,
  `send_tels` text,
  `plan_time` datetime DEFAULT NULL,
  `status` bigint DEFAULT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `local_day` bigint DEFAULT NULL,
  `user_id` bigint DEFAULT NULL,
  `del_flag` bigint DEFAULT '0',
  `draw_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;

-- ----------------------------
--  Table structure for `kerry_sms_template`
-- ----------------------------
DROP TABLE IF EXISTS `kerry_sms_template`;
CREATE TABLE `kerry_sms_template` (
  `Id` bigint NOT NULL AUTO_INCREMENT,
  `temp_name` varchar(100) DEFAULT NULL COMMENT '模板名称',
  `content` varchar(512) DEFAULT NULL COMMENT '模板内容',
  `create_by` varchar(100) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_by` varchar(100) DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  `del_flag` varchar(2) DEFAULT '0',
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='短信模板';


DROP TABLE IF EXISTS `tb_apt_bill_direct_debits_agreement`;
CREATE TABLE `tb_apt_bill_direct_debits_agreement` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_profile_id` varchar(32) NOT NULL DEFAULT '' COMMENT 'KIP profile ID',
  `psp_logon_id` varchar(64) NOT NULL DEFAULT '' COMMENT '支付平台账号名称',
  `user_phone_number` varchar(45) NOT NULL DEFAULT '' COMMENT '用户手机号',
  `psp_name` varchar(32) NOT NULL DEFAULT '' COMMENT '支付平台名称',
  `agreement_no` varchar(45) NOT NULL DEFAULT '' COMMENT 'KIP协议号',
  `agreement_status` tinyint NOT NULL DEFAULT '0' COMMENT '协议状态：UNKNOWN(0), INACTIVE(1), ACTIVE(2), TERMINATED(3)',
  `order_no` varchar(45) NOT NULL DEFAULT '' COMMENT '支付并签约的订单号',
  `project_id` varchar(50) NOT NULL DEFAULT '' COMMENT '项目ID',
  `project_name` varchar(32) NOT NULL DEFAULT '' COMMENT '项目名称',
  `building_id` varchar(32) NOT NULL DEFAULT '' COMMENT '楼盘ID',
  `building_name` varchar(32) NOT NULL DEFAULT '' COMMENT '楼盘',
  `floor_id` varchar(32) NOT NULL DEFAULT '' COMMENT '楼层ID',
  `floor_name` varchar(32) NOT NULL DEFAULT '' COMMENT '楼层',
  `room_id` varchar(32) NOT NULL DEFAULT '' COMMENT '单元id',
  `room_name` varchar(32) NOT NULL DEFAULT '' COMMENT '单元',
  `invalid_time` datetime NOT NULL DEFAULT '3000-01-01 00:00:00' COMMENT '协议失效日期',
  `sign_type` tinyint NOT NULL DEFAULT '0' COMMENT 'PAY_AND_SIGN(0), DIRECT_SIGN(1)',
  `sign_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '签约时间',
  `unsign_type` tinyint NOT NULL DEFAULT '0' COMMENT '解约方式：UNKNOWN(0), MERCHANT_UNSIGN(1), USER_UNSIGN(2)',
  `unsign_time` datetime NOT NULL DEFAULT '3000-01-01 00:00:00' COMMENT '解约时间',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_del` tinyint NOT NULL DEFAULT '0' COMMENT '记录是否逻辑删除',
  `personal_product_code` varchar(64) NOT NULL DEFAULT '' COMMENT '支付宝的签约产品code',
  PRIMARY KEY (`id`),
  KEY `idx_agreement_no` (`agreement_no`),
  KEY `idx_room_id` (`room_id`),
  KEY `idx_project_id` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT '签约单元表';


DROP TABLE IF EXISTS `tb_apt_bill_direct_debits_batch`;
CREATE TABLE `tb_apt_bill_direct_debits_batch` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `batch_no` varchar(32) NOT NULL COMMENT '批次号',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT 'UNKNOWN(0) , AWAIT(1) , SENT(2) , LAPSED(3)',
  `project_id` varchar(50) NOT NULL DEFAULT '' COMMENT '项目id',
  `project_name` varchar(50) NOT NULL DEFAULT '' COMMENT '项目名',
  `psp_name` varchar(32) NOT NULL DEFAULT '' COMMENT '支付平台',
  `closing_month` varchar(10) NOT NULL DEFAULT '' COMMENT '账单截止日期',
  `bill_count` int NOT NULL DEFAULT '0' COMMENT '批次包含账单数',
  `bill_sent_count` int NOT NULL DEFAULT '0' COMMENT '批次中账单已发起代扣数',
  `payment_confirmed_count` int NOT NULL DEFAULT '0' COMMENT '账单代扣确认数',
  `created_by` varchar(32) NOT NULL DEFAULT '' COMMENT 'S端人员',
  `last_modified_by` varchar(32) NOT NULL DEFAULT '' COMMENT '修改S端人员',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_del` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标记位',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_batch_no` (`batch_no`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_psp_name` (`psp_name`),
  KEY `idx_closing_month` (`closing_month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT '代扣批次表';


DROP TABLE IF EXISTS `tb_apt_direct_debits_batch_bill`;
CREATE TABLE `tb_apt_direct_debits_batch_bill` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `batch_no` varchar(32) NOT NULL COMMENT '批次号',
  `bill_id` bigint NOT NULL DEFAULT '0' COMMENT '账单id',
  `bill_no` varchar(32) NOT NULL DEFAULT '' COMMENT '账单号',
  `building_id` varchar(32) NOT NULL DEFAULT '' COMMENT '楼栋id',
  `room_id` varchar(32) NOT NULL DEFAULT '' COMMENT '单元id',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `payment_info_id` varchar(32) NOT NULL DEFAULT '' COMMENT 'payment info主键id',
  `company_code` varchar(16) NOT NULL DEFAULT '' COMMENT '公司code',
  `psp_name` varchar(32) NOT NULL DEFAULT 'alipay' COMMENT '支付平台',
  PRIMARY KEY (`id`),
  KEY `idx_batch_no` (`batch_no`),
  KEY `idx_payment_id` (`payment_info_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT '代扣批次和账单关系表';



CREATE TABLE `kerry_bill_send_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `doco` varchar(32) NOT NULL DEFAULT '' COMMENT 'JDE合同号',
  `phone_number` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '手机号码',
  `tenant_manager_id` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '租户账号数据库表主键',
  `email_username` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '电子邮箱的用户姓名',
  `email` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '电子邮箱',
  `login_no` VARCHAR(64) NOT NULL DEFAULT '' COMMENT 'B端登录账号',
  `project_id` varchar(32) NOT NULL DEFAULT '' COMMENT '楼盘id',
  `building_id` varchar(32) NOT NULL DEFAULT '' COMMENT '楼栋id',
  `building_name` varchar(32) NOT NULL DEFAULT '' COMMENT '楼栋名称',
  `mcu` text NOT NULL COMMENT '建筑物编号',
  `unit` text NOT NULL COMMENT '账单单元',
  `is_del` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标记位',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_doco` (`doco`) COMMENT '合同号索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='账单发送配置';

CREATE TABLE `kerry_bill_send_config_an8_link` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `config_id` bigint NOT NULL COMMENT '账单发送配置ID',
  `alph` varchar(255) NOT NULL DEFAULT '' COMMENT '用户名称',
  `an8` varchar(32) NOT NULL DEFAULT '' COMMENT '用户编号',
  `is_del` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标记位',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_relation` (`config_id`, `alph`, `an8`) USING BTREE,
  KEY `idx_an8` (`an8`) COMMENT '购方公司an8索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='账单发送配置 | 合同与付款人关系表';


CREATE TABLE `tb_apt_bill_operation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'primary key',
  `bill_id` bigint DEFAULT NULL COMMENT 'apt bill ID',
  `bill_no` varchar(32) DEFAULT NULL COMMENT 'apt bill no',
  `operator` tinyint DEFAULT NULL COMMENT 'operation type',
  `operation_name` varchar(255) DEFAULT NULL COMMENT 'operation name',
  `comment` varchar(255) DEFAULT NULL COMMENT 'comment',
  `diff_count` int DEFAULT NULL COMMENT 'operation change count',
  `operate_user_id` varchar(32) DEFAULT NULL COMMENT 'operate user id',
  `operate_user_name` varchar(32) DEFAULT NULL COMMENT 'operate user name',
  `operate_user_email` varchar(128) DEFAULT NULL COMMENT 'operate user email',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT 'created time',
  `category` varchar(64) DEFAULT NULL COMMENT '费项',
  `project_id` varchar(32) DEFAULT NULL COMMENT 'project id',
  `building_id` varchar(32) DEFAULT NULL COMMENT 'building id',
  `floor_id` varchar(32) DEFAULT NULL COMMENT 'floor id',
  `room_id` varchar(32) DEFAULT NULL COMMENT 'room id',
  `project_name` varchar(45) DEFAULT NULL COMMENT 'project name',
  `building_name` varchar(45) DEFAULT NULL COMMENT 'building name',
  `floor_name` varchar(45) DEFAULT NULL COMMENT 'floor name',
  `room_name` varchar(45) DEFAULT NULL COMMENT 'room name',
  `operation_status` TINYINT NOT NULL DEFAULT -1 COMMENT '操作结果',
  PRIMARY KEY (`id`),
  KEY `idx_bill_no` (`bill_no`),
  KEY `idx_building_id` (`building_id`),
  KEY `idx_room_id` (`room_id`),
  KEY `idx_bill_id` (`bill_id`) COMMENT 'apt bill id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='S operator audit log';

CREATE TABLE `tb_apt_bill_operation_content` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'primary key',
  `operation_id` bigint DEFAULT NULL COMMENT 'operation table primary key',
  `field_type` varchar(32) DEFAULT NULL COMMENT 'data type',
  `field_name` varchar(128) DEFAULT NULL COMMENT 'field name',
  `field_alias` varchar(64) DEFAULT NULL COMMENT 'alias for display',
  `field_old_value` varchar(255) DEFAULT NULL COMMENT 'old value',
  `field_new_value` varchar(255) DEFAULT NULL COMMENT 'updated new value',
  PRIMARY KEY (`id`),
  KEY `idx_operation_id` (`operation_id`) COMMENT 'operation id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='S operator audit log detail';

CREATE TABLE `bill_common_setting` (
`id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
`type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '设定类型名称',
`content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '设定内容',
`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
PRIMARY KEY (`id`) USING BTREE,
UNIQUE KEY `uk_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='共通设定表'


-- ----------------------------
-- Table structure for tb_e_fapiao_send_record
-- ----------------------------
CREATE TABLE `tb_e_fapiao_send_record` (
 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
 `bill_fapiao_id` bigint NOT NULL COMMENT '电子账单发票id',
 `batch_no` bigint NOT NULL COMMENT '发送批次号',
 `notify_type` tinyint NOT NULL COMMENT '1: 邮件；2:短信；3站内信',
 `request_id` varchar(64) NOT NULL COMMENT '请求id',
 `send_status` tinyint NOT NULL COMMENT '发送状态',
 `send_desc` varchar(128) NOT NULL DEFAULT '' COMMENT '发送状况描述',
 `user_id` varchar(64) NOT NULL COMMENT '用户ID',
 `user_name` varchar(64) NOT NULL COMMENT '用户昵称',
 `send_time` datetime NOT NULL COMMENT '发送时间',
 `email` varchar(128) NOT NULL DEFAULT '' COMMENT '邮箱地址',
 `sms` varchar(32) NOT NULL COMMENT '电话号码',
 `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
 `operate_user_id` varchar(32) NOT NULL COMMENT 'S端用户ID',
 `operate_user_email` varchar(128) NOT NULL COMMENT 'S端用户email',
 PRIMARY KEY (`id`) USING BTREE,
 INDEX `idx_bill_fapiao_id`(`bill_fapiao_id` ASC) USING BTREE COMMENT '电子账单发票id',
 INDEX `idx_request_id`(`request_id` ASC) USING BTREE COMMENT 'requestId',
 INDEX `idx_batch`(`batch_no` ASC) USING BTREE COMMENT '批次号索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'E-fapiao发票发送记录表';





-- ----------------------------
-- Table structure for tb_kerry_bill_e_fapiao
-- 【注】请先手动删除已有tb_kerry_bill_e_fapiao表，再用以下语句创建新的tb_kerry_bill_e_fapiao表
-- ----------------------------
CREATE TABLE `tb_kerry_bill_e_fapiao` (
 `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
 `kco` varchar(5) NOT NULL DEFAULT '' COMMENT '单据公司',
 `bill_type` varchar(2) NOT NULL DEFAULT '' COMMENT '账单类型｜示例：RN',
 `doc` int NOT NULL DEFAULT 0 COMMENT '单据号',
 `payment_item` varchar(3) NOT NULL DEFAULT '' COMMENT '单据付款项',
 `company_code` varchar(30) NOT NULL DEFAULT '' COMMENT '销方公司编号(根据编号匹配票易通的销项主数据)',
 `an8` int NOT NULL DEFAULT 0 COMMENT 'an8，可代表楼栋、单元号等等 | 示例：********',
 `purchaser_name` varchar(100) NOT NULL DEFAULT '' COMMENT '购方抬头（专票必填）(按编号，该字段可以不填)，50个汉字、100个字符，允许输入汉字、大小写英文字母、数字、特殊字符~！@#￥%……&*（）',
 `purchaser_tax_no` varchar(20) NOT NULL DEFAULT '' COMMENT '购方税号（专票必填，个人购方传身份证信息）(按编号，该字段可以不填)，20个字符。只能为数字、大写英文字母',
 `purchaser_address` varchar(100) NOT NULL DEFAULT '' COMMENT '公司地址 （专票必填）',
 `purchaser_bank_name` varchar(100) NOT NULL DEFAULT '' COMMENT '购方银行名称｜花旗银行北京分行**********',
 `invoice_type` varchar(5) NOT NULL DEFAULT '' COMMENT '发票类型：gvat-增值税普通发票 svat-增值税专用发票 gvate-增值税电子普票发票 svate-增值税电子专用发票',
 `mailing_address` varchar(500) NOT NULL DEFAULT '' COMMENT '邮寄地址',
 `mcu` varchar(200) NOT NULL DEFAULT '' COMMENT '业态，如：********、********',
 `doco` varchar(6) NOT NULL DEFAULT '' COMMENT 'JDE合同号｜示例：250644',
 `bill_code` varchar(4) NOT NULL DEFAULT '' COMMENT '缴费票据码｜示例：U02M',
 `goods_tax_no` varchar(30) NOT NULL DEFAULT '' COMMENT '商品货物税收分类编码(19位末级代码才可以开票）（使用税编转换码时，此字段可不填）',
 `item_name` varchar(100) NOT NULL DEFAULT '' COMMENT '货物或应税劳务名称 ，禁止特殊字符（使用税编转换码时，此字段可不填）',
 `item_spec` varchar(40) NOT NULL DEFAULT '' COMMENT '规格型号， 禁止特殊字符，如果对方填写则传过来',
 `amount_without_tax` decimal(18, 6) NOT NULL DEFAULT 0.000000 COMMENT '不含税金额（如果priceMethod 为0 则传该字段必传）',
 `tax_rate` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '税率',
 `tax_amount` decimal(18, 6) NOT NULL DEFAULT 0.000000 COMMENT '税额',
 `amount_with_tax` decimal(18, 6) NOT NULL DEFAULT 0.000000 COMMENT '含税金额',
 `tax_discount` varchar(255) NOT NULL DEFAULT '' COMMENT '是否享受税收优惠政策 0-不1-享受',
 `tax_discount_desc` varchar(255) NOT NULL DEFAULT '' COMMENT '税收优惠',
 `extended_doc` varchar(30) NOT NULL DEFAULT '' COMMENT '单据号（加长版），内容上是TOKCO&TODCT&TODOC',
 `bill_draw_date` varchar(30) NOT NULL DEFAULT '' COMMENT '发票日期（可理解为JDE账单日期）',
 `second_name` varchar(40) NOT NULL DEFAULT '' COMMENT '第二字母名称',
 `formated_doco` varchar(8) NOT NULL DEFAULT '' COMMENT '合同号，doco经过格式转化，内容不变',
 `jde_unit` varchar(100) NOT NULL DEFAULT '' COMMENT 'JDE单元号',
 `is_contact_person` varchar(3) NOT NULL DEFAULT '' COMMENT '是否主要联系人',
 `is_tax_payer` varchar(1) NOT NULL DEFAULT '' COMMENT '是否一般纳税人',
 `tax_classify_desc` varchar(30) NOT NULL DEFAULT '' COMMENT '税收分类说明',
 `user_name_info` varchar(10) NOT NULL DEFAULT '' COMMENT '用户',
 `program_id` varchar(10) NOT NULL DEFAULT '' COMMENT '程序号',
 `dscrp1` varchar(50) NOT NULL DEFAULT '' COMMENT '描述1',
 `dscrp2` varchar(30) NOT NULL DEFAULT '' COMMENT '描述2',
 `dscrp3` varchar(255) NOT NULL DEFAULT '' COMMENT '描述3',
 `constant1` varchar(1) NOT NULL DEFAULT '' COMMENT '常量',
 `constant2` varchar(30) NOT NULL DEFAULT '' COMMENT '常量',
 `constant3` decimal(15, 2) NOT NULL DEFAULT 0.00 COMMENT '常量',
 `constant4` decimal(15, 4) NOT NULL DEFAULT 0.0000 COMMENT '常量',
 `constant5` decimal(15, 4) NOT NULL DEFAULT 0.0000 COMMENT '常量',
 `constant6` varchar(10) NOT NULL DEFAULT '' COMMENT '常量',
 `create_date_jde` int NOT NULL DEFAULT 0 COMMENT 'JDE入表日期',
 `create_time_jde` int NOT NULL DEFAULT 0 COMMENT 'JDE入表时间',
 `event1` varchar(1) NOT NULL DEFAULT '' COMMENT '事件点1',
 `event2` varchar(1) NOT NULL DEFAULT '' COMMENT '事件点2',
 `event3` varchar(1) NOT NULL DEFAULT '' COMMENT '事件点3',
 `sales_bill_no` varchar(100) NOT NULL DEFAULT '' COMMENT '订单号，内容为TOKCO&TODCT&TODOC&TOSFX',
 `seller_name` varchar(255) NOT NULL DEFAULT '' COMMENT '销方名称',
 `pdf_url` varchar(150) NOT NULL COMMENT '发票PDF地址',
 `xml_url` varchar(1500) NOT NULL DEFAULT '' COMMENT '全电xml地址',
 `ofd_url` varchar(1500) NOT NULL DEFAULT '' COMMENT '发票ofd地址',
 `making_reason` varchar(255) NOT NULL DEFAULT '' COMMENT '全电场景：红冲发票场景，开具原因', 
 `invoice_no` varchar(64) NOT NULL COMMENT '发票号',
 `invoice_code` varchar(128) NOT NULL COMMENT '发票代码',
 `biz_type` varchar(3) NOT NULL COMMENT '业务类型，固定为JDE',
 `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
 `email_send_status` tinyint NOT NULL DEFAULT 0 COMMENT '邮件发送状态',
 `sms_send_status` tinyint NOT NULL DEFAULT 0 COMMENT '短信发送状态',
 `message_send_status` tinyint NOT NULL DEFAULT 0 COMMENT '站内信发送状态',
 `invoice_status` tinyint NOT NULL DEFAULT 1 COMMENT '开票状态：PROCESSING, COMPLETED, FAILED',
 `state` tinyint NOT NULL DEFAULT 0 COMMENT '发票状态：0未知；1正常；2作废；3红冲',
 `error_message` varchar(255) NOT NULL COMMENT '失败信息描述',
 `paper_drew_date` date NOT NULL DEFAULT '1900-01-01' COMMENT '发票开票日期',
 `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
 PRIMARY KEY (`id`) USING BTREE,
 UNIQUE INDEX `uniq_kco_bill_type_doc_payment_item_invoice_no`(`kco` ASC, `bill_type` ASC, `doc` ASC, `payment_item` ASC, `invoice_no` ASC) USING BTREE,
 INDEX `idx_sale_bill_no`(`sales_bill_no` ASC) USING BTREE COMMENT '单号',
 INDEX `idx_mcu`(`mcu` ASC) USING BTREE COMMENT 'BU',
 INDEX `idx_doco`(`doco` ASC) USING BTREE COMMENT '合同号',
 INDEX `idx_an8`(`an8` ASC) USING BTREE COMMENT 'an8',
 INDEX `idx_co`(`company_code` ASC) USING BTREE COMMENT '公司',
 INDEX `idx_invoice_no`(`invoice_no` ASC) USING BTREE COMMENT '发票号',
 INDEX `idx_drew_date`(`paper_drew_date` ASC) USING BTREE COMMENT '出票时间'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'E-fapiao发票信息表';





-- ----------------------------
-- Table structure for tb_kerry_bill_sync_e_fapiao
-- ----------------------------
CREATE TABLE `tb_kerry_bill_sync_e_fapiao` (
 `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
 `kco` varchar(5) NOT NULL DEFAULT '' COMMENT '单据公司',
 `bill_type` varchar(2) NOT NULL DEFAULT '' COMMENT '账单类型｜示例：RN',
 `doc` int NOT NULL DEFAULT 0 COMMENT '单据号',
 `payment_item` varchar(3) NOT NULL DEFAULT '' COMMENT '单据付款项',
 `company_code` varchar(30) NOT NULL DEFAULT '' COMMENT '销方公司编号(根据编号匹配票易通的销项主数据)',
 `an8` int NOT NULL DEFAULT 0 COMMENT 'an8，可代表楼栋、单元号等等 | 示例：********',
 `purchaser_name` varchar(100) NOT NULL DEFAULT '' COMMENT '购方抬头（专票必填）(按编号，该字段可以不填)',
 `purchaser_tax_no` varchar(20) NOT NULL DEFAULT '' COMMENT '购方税号（专票必填，个人购方传身份证信息）',
 `purchaser_address` varchar(100) NOT NULL DEFAULT '' COMMENT '公司地址 （专票必填）',
 `purchaser_bank_name` varchar(100) NOT NULL DEFAULT '' COMMENT '购方银行名称｜花旗银行北京分行**********',
 `invoice_type` varchar(5) NOT NULL DEFAULT '' COMMENT '发票类型：gvat-增值税普通发票 svat-增值税专用发票 gvate-增值税电子普票发票 svate-增值税电子专用发票',
 `mailing_address` varchar(500) NOT NULL DEFAULT '' COMMENT '邮寄地址',
 `mcu` varchar(12) NOT NULL DEFAULT '' COMMENT '业态，如：********、********',
 `doco` varchar(6) NOT NULL DEFAULT '' COMMENT 'JDE合同号｜示例：250644',
 `bill_code` varchar(4) NOT NULL DEFAULT '' COMMENT '缴费票据码｜示例：U02M',
 `goods_tax_no` varchar(30) NOT NULL DEFAULT '' COMMENT '商品货物税收分类编码(19位末级代码才可以开票）（使用税编转换码时，此字段可不填）',
 `item_name` varchar(100) NOT NULL DEFAULT '' COMMENT '货物或应税劳务名称 ，禁止特殊字符（使用税编转换码时，此字段可不填）',
 `item_spec` varchar(40) NOT NULL DEFAULT '' COMMENT '规格型号， 禁止特殊字符，如果对方填写则传过来',
 `amount_without_tax` decimal(18, 6) NOT NULL DEFAULT 0.000000 COMMENT '不含税金额（如果priceMethod 为0 则传该字段必传）',
 `tax_rate` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '税率',
 `tax_amount` decimal(18, 6) NOT NULL DEFAULT 0.000000 COMMENT '税额',
 `amount_with_tax` decimal(18, 6) NOT NULL DEFAULT 0.000000 COMMENT '含税金额',
 `tax_discount` varchar(255) NOT NULL DEFAULT '' COMMENT '是否享受税收优惠政策 0-不1-享受',
 `tax_discount_desc` varchar(255) NOT NULL DEFAULT '' COMMENT '税收优惠',
 `jde_unit` varchar(8) NOT NULL DEFAULT '' COMMENT 'JDE单元号',
 `tax_classify_desc` varchar(30) NOT NULL DEFAULT '' COMMENT '税收分类说明',
 `sales_bill_no` varchar(100) NOT NULL DEFAULT '' COMMENT '订单号，内容为TOKCO&TODCT&TODOC&TOSFX',
 `invoice_upload_status` tinyint NOT NULL DEFAULT 0 COMMENT '开票请求状态，0-开票请求中，1-请求成功，2-请求失败,3-正常发票,4-红冲,5-作废,6-开票失败',
 `real_estate_no` varchar(40) NOT NULL DEFAULT '' COMMENT '不动产销售类/不动产租赁类:房屋产权证书/不动产权证号码',
 `real_estate_place` varchar(120) NOT NULL DEFAULT '' COMMENT '不动产销售类/不动产租赁类:不动产地址（省市区）',
 `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
 PRIMARY KEY (`id`) USING BTREE,
 KEY `idx_sales_bill_no` (`sales_bill_no`) COMMENT '业务单号普通索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'E-fapiao账单信息表';

CREATE TABLE `tb_biz_content_read` (
 `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
 `type` tinyint NOT NULL COMMENT '类型：1-B端发票，2-B端账单',
 `content_id` bigint NOT NULL COMMENT '内容id（账单或发票等）',
 `user_id` bigint NOT NULL COMMENT '用户id',
 `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
 PRIMARY KEY (`id`) USING BTREE,
 UNIQUE INDEX `uniq_type_content_id_user_id`(`type` ASC, `content_id` DESC, `user_id` ASC) USING BTREE COMMENT '唯一键',
 INDEX `idx_type_user_id`(`type` ASC, `user_id` ASC) USING BTREE COMMENT '类型、用户ID查询索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'B端：内容已读表';




SET FOREIGN_KEY_CHECKS = 1;


