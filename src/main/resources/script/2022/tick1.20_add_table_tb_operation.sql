

CREATE TABLE `tb_apt_bill_operation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'primary key',
  `bill_id` bigint DEFAULT NULL COMMENT 'apt bill ID',
  `bill_no` varchar(32) DEFAULT NULL COMMENT 'apt bill no',
  `operator` tinyint DEFAULT NULL COMMENT 'operation type',
  `operation_name` varchar(255) DEFAULT NULL COMMENT 'operation name',
  `comment` varchar(255) DEFAULT NULL COMMENT 'comment',
  `diff_count` int DEFAULT NULL COMMENT 'operation change count',
  `operate_user_id` varchar(32) DEFAULT NULL COMMENT 'operate user id',
  `operate_user_name` varchar(32) DEFAULT NULL COMMENT 'operate user name',
  `operate_user_email` varchar(128) DEFAULT NULL COMMENT 'operate user email',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT 'created time',
  `category` varchar(64) DEFAULT NULL COMMENT '费项',
  `project_id` varchar(32) DEFAULT NULL COMMENT 'project id',
  `building_id` varchar(32) DEFAULT NULL COMMENT 'building id',
  `floor_id` varchar(32) DEFAULT NULL COMMENT 'floor id',
  `room_id` varchar(32) DEFAULT NULL COMMENT 'room id',
  `project_name` varchar(45) DEFAULT NULL COMMENT 'project name',
  `building_name` varchar(45) DEFAULT NULL COMMENT 'building name',
  `floor_name` varchar(45) DEFAULT NULL COMMENT 'floor name',
  `room_name` varchar(45) DEFAULT NULL COMMENT 'room name',
  PRIMARY KEY (`id`),
  KEY `idx_bill_no` (`bill_no`),
  KEY `idx_building_id` (`building_id`),
  KEY `idx_room_id` (`room_id`),
  KEY `idx_bill_id` (`bill_id`) COMMENT 'apt bill id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='S operator audit log';

CREATE TABLE `tb_apt_bill_operation_content` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'primary key',
  `operation_id` bigint DEFAULT NULL COMMENT 'operation table primary key',
  `field_type` varchar(32) DEFAULT NULL COMMENT 'data type',
  `field_name` varchar(128) DEFAULT NULL COMMENT 'field name',
  `field_alias` varchar(64) DEFAULT NULL COMMENT 'alias for display',
  `field_old_value` varchar(255) DEFAULT NULL COMMENT 'old value',
  `field_new_value` varchar(255) DEFAULT NULL COMMENT 'updated new value',
  PRIMARY KEY (`id`),
  KEY `idx_operation_id` (`operation_id`) COMMENT 'operation id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='S operator audit log detail';

CREATE TABLE `bill_common_setting` (
`id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
`type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '设定类型名称',
`content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '设定内容',
`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
PRIMARY KEY (`id`) USING BTREE,
UNIQUE KEY `uk_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='共通设定表'
