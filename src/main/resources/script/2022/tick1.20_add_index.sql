
ALTER TABLE `apt_bill` ADD INDEX `idx_bill_no` (`bill_no`) comment 'index for biz bill number';
ALTER TABLE `apt_bill` ADD INDEX `idx_building_id` (`building_id`) comment 'index for building id';
ALTER TABLE `apt_bill` ADD INDEX `idx_room_id` (`room_id`) comment 'index for room ID';

ALTER TABLE `apt_pay` ADD INDEX `idx_pay_date` (`pay_date`) comment 'index for pay_date';
ALTER TABLE `apt_pay` ADD INDEX `idx_building_id` (`building_id`) comment 'index for building ID';

ALTER TABLE `apt_pay_bill` ADD INDEX `idx_pay_id` (`pay_id`) comment 'pay_id index'
, ADD INDEX `idx_bill_id` (`bill_id`) comment 'bill_id index';

ALTER TABLE `apt_sync_bills` ADD INDEX `idx_bill_no` (`bill_number`) comment 'index for bill_no'
, ADD INDEX `idx_rd_doc` (`rd_doc`) comment 'index for rd_doc';

ALTER TABLE `apt_bill_send_history` ADD INDEX `idx_bill_no` (`bill_no`) comment 'index for bill no'
, ADD INDEX `idx_bill_id` (`bill_id`) comment 'index for bill id';

ALTER TABLE `apt_payment_bill` ADD INDEX `idx_bill_id` (`bill_id`) comment 'index for bill id'
, ADD INDEX `idx_pay_info` (`payment_info_id`) comment 'index for payment id';

ALTER TABLE `apt_sync_pay_to_jde` ADD INDEX `idx_bill_no` (`RCCKNU`) comment 'index for bill no'
, ADD INDEX `idx_doc` (`RCDOC`) comment 'index for doc no';

ALTER TABLE `apt_sync_paid_bill_to_jde` ADD INDEX `idx_bill_no` (`RBCKNU`) comment 'index for bill no'
, ADD INDEX `idx_doc_no` (`RCDOC`) comment 'index for doc no';

-- not
ALTER TABLE `apt_bill_log` ADD INDEX `idx_bill_no` (`bill_no`) comment 'index for bill no'
, ADD INDEX `idx_bill_id` (`bill_id`) comment 'index for bill id';
