
ALTER TABLE `apt_payment_info`
ADD COLUMN `psp_trans_no` varchar(64) NOT NULL DEFAULT '' COMMENT '支付平台流水号' AFTER `invoice_url`
, ADD COLUMN `payment_trans_no` varchar(45) NOT NULL DEFAULT '' COMMENT 'KIP支付单号' AFTER `psp_trans_no`
, ADD COLUMN `failed_reason` varchar(128) NOT NULL DEFAULT '' COMMENT '支付失败原因' AFTER `payment_trans_no`
, ADD COLUMN `agreement_no` varchar(45) NOT NULL DEFAULT '' COMMENT 'KIP支付协议号' AFTER `failed_reason`
, ADD COLUMN `user_profile_id` varchar(32) NOT NULL DEFAULT '' COMMENT 'KIP profile userID' AFTER `agreement_no`
, ADD INDEX `idx_room_id` (`room_id`)
;


DROP TABLE IF EXISTS `tb_apt_bill_direct_debits_agreement`;
CREATE TABLE `tb_apt_bill_direct_debits_agreement` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_profile_id` varchar(32) NOT NULL DEFAULT '' COMMENT 'KIP profile ID',
  `psp_logon_id` varchar(64) NOT NULL DEFAULT '' COMMENT '支付平台账号名称',
  `user_phone_number` varchar(45) NOT NULL DEFAULT '' COMMENT '用户手机号',
  `psp_name` varchar(32) NOT NULL DEFAULT '' COMMENT '支付平台名称',
  `agreement_no` varchar(45) NOT NULL DEFAULT '' COMMENT 'KIP协议号',
  `agreement_status` tinyint NOT NULL DEFAULT '0' COMMENT '协议状态：UNKNOWN(0), INACTIVE(1), ACTIVE(2), TERMINATED(3)',
  `order_no` varchar(45) NOT NULL DEFAULT '' COMMENT '支付并签约的订单号',
  `project_id` varchar(50) NOT NULL DEFAULT '' COMMENT '项目ID',
  `project_name` varchar(32) NOT NULL DEFAULT '' COMMENT '项目名称',
  `building_id` varchar(32) NOT NULL DEFAULT '' COMMENT '楼盘ID',
  `building_name` varchar(32) NOT NULL DEFAULT '' COMMENT '楼盘',
  `floor_id` varchar(32) NOT NULL DEFAULT '' COMMENT '楼层ID',
  `floor_name` varchar(32) NOT NULL DEFAULT '' COMMENT '楼层',
  `room_id` varchar(32) NOT NULL DEFAULT '' COMMENT '单元id',
  `room_name` varchar(32) NOT NULL DEFAULT '' COMMENT '单元',
  `invalid_time` datetime NOT NULL DEFAULT '3000-01-01 00:00:00' COMMENT '协议失效日期',
  `sign_type` tinyint NOT NULL DEFAULT '0' COMMENT 'PAY_AND_SIGN(0), DIRECT_SIGN(1)',
  `sign_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '签约时间',
  `unsign_type` tinyint NOT NULL DEFAULT '0' COMMENT '解约方式：UNKNOWN(0), MERCHANT_UNSIGN(1), USER_UNSIGN(2)',
  `unsign_time` datetime NOT NULL DEFAULT '3000-01-01 00:00:00' COMMENT '解约时间',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_del` tinyint NOT NULL DEFAULT '0' COMMENT '记录是否逻辑删除',
  `personal_product_code` varchar(64) NOT NULL DEFAULT '' COMMENT '支付宝的签约产品code',
  PRIMARY KEY (`id`),
  KEY `idx_agreement_no` (`agreement_no`),
  KEY `idx_room_id` (`room_id`),
  KEY `idx_project_id` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT '签约单元表';


DROP TABLE IF EXISTS `tb_apt_bill_direct_debits_batch`;
CREATE TABLE `tb_apt_bill_direct_debits_batch` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `batch_no` varchar(32) NOT NULL COMMENT '批次号',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT 'UNKNOWN(0) , AWAIT(1) , SENT(2) , LAPSED(3)',
  `project_id` varchar(50) NOT NULL DEFAULT '' COMMENT '项目id',
  `project_name` varchar(50) NOT NULL DEFAULT '' COMMENT '项目名',
  `psp_name` varchar(32) NOT NULL DEFAULT '' COMMENT '支付平台',
  `closing_month` varchar(10) NOT NULL DEFAULT '' COMMENT '账单截止日期',
  `bill_count` int NOT NULL DEFAULT '0' COMMENT '批次包含账单数',
  `bill_sent_count` int NOT NULL DEFAULT '0' COMMENT '批次中账单已发起代扣数',
  `payment_confirmed_count` int NOT NULL DEFAULT '0' COMMENT '账单代扣确认数',
  `created_by` varchar(32) NOT NULL DEFAULT '' COMMENT 'S端人员',
  `last_modified_by` varchar(32) NOT NULL DEFAULT '' COMMENT '修改S端人员',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_del` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标记位',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_batch_no` (`batch_no`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_psp_name` (`psp_name`),
  KEY `idx_closing_month` (`closing_month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT '代扣批次表';


DROP TABLE IF EXISTS `tb_apt_direct_debits_batch_bill`;
CREATE TABLE `tb_apt_direct_debits_batch_bill` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `batch_no` varchar(32) NOT NULL COMMENT '批次号',
  `bill_id` bigint NOT NULL DEFAULT '0' COMMENT '账单id',
  `bill_no` varchar(32) NOT NULL DEFAULT '' COMMENT '账单号',
  `building_id` varchar(32) NOT NULL DEFAULT '' COMMENT '楼栋id',
  `room_id` varchar(32) NOT NULL DEFAULT '' COMMENT '单元id',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `payment_info_id` varchar(32) NOT NULL DEFAULT '' COMMENT 'payment info主键id',
  `company_code` varchar(16) NOT NULL DEFAULT '' COMMENT '公司code',
  PRIMARY KEY (`id`),

  KEY `idx_batch_no` (`batch_no`),
  KEY `idx_payment_id` (`payment_info_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT '代扣批次和账单关系表';
