CREATE TABLE `kerry_bill_send_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `doco` varchar(32) NOT NULL DEFAULT '' COMMENT 'JDE合同号',
  `phone_number` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '手机号码',
  `tenant_manager_id` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '租户账号数据库表主键',
  `email_username` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '电子邮箱的用户姓名',
  `email` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '电子邮箱',
  `login_no` VARCHAR(64) NOT NULL DEFAULT '' COMMENT 'B端登录账号',
  `mcu` text NOT NULL COMMENT '建筑物编号',
  `unit` text NOT NULL COMMENT '账单单元',
  `is_del` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标记位',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='账单发送配置';

CREATE TABLE `kerry_bill_send_config_an8_link` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `config_id` bigint NOT NULL COMMENT '账单发送配置ID',
  `alph` varchar(255) NOT NULL DEFAULT '' COMMENT '用户名称',
  `an8` varchar(32) NOT NULL DEFAULT '' COMMENT '用户编号',
  `is_del` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标记位',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_relation` (`config_id`, `alph`, `an8`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='账单发送配置 | 合同与付款人关系表';