-- ----------------------------
-- Table structure for tb_biz_content_read
-- ----------------------------
CREATE TABLE `tb_biz_content_read`  (
 `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
 `type` tinyint NOT NULL COMMENT '类型：1-B端发票，2-B端账单',
 `content_id` bigint NOT NULL COMMENT '内容id（账单或发票等）',
 `user_id` bigint NOT NULL COMMENT '用户id',
 `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
 PRIMARY KEY (`id`) USING BTREE,
 UNIQUE INDEX `uniq_type_content_id_user_id`(`type` ASC, `content_id` DESC, `user_id` ASC) USING BTREE COMMENT '唯一键',
 INDEX `idx_type_user_id`(`type` ASC, `user_id` ASC) USING BTREE COMMENT '类型、用户ID查询索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'B端：内容已读表';