-- ----------------------------
-- Table structure for tb_e_fapiao_send_record
-- ----------------------------
CREATE TABLE `tb_e_fapiao_send_record` (
 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
 `bill_fapiao_id` bigint NOT NULL COMMENT '电子账单发票id',
 `batch_no` bigint NOT NULL COMMENT '发送批次号',
 `notify_type` tinyint NOT NULL COMMENT '1: 邮件；2:短信；3站内信',
 `request_id` varchar(64) NOT NULL COMMENT '请求id',
 `send_status` tinyint NOT NULL COMMENT '发送状态',
 `send_desc` varchar(128) NOT NULL DEFAULT '' COMMENT '发送状况描述',
 `user_id` varchar(64) NOT NULL COMMENT '用户ID',
 `user_name` varchar(64) NOT NULL COMMENT '用户昵称',
 `send_time` datetime NOT NULL COMMENT '发送时间',
 `email` varchar(128) NOT NULL DEFAULT '' COMMENT '邮箱地址',
 `sms` varchar(32) NOT NULL COMMENT '电话号码',
 `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
 `operate_user_id` varchar(32) NOT NULL COMMENT 'S端用户ID',
 `operate_user_email` varchar(128) NOT NULL COMMENT 'S端用户email',
 PRIMARY KEY (`id`) USING BTREE,
 INDEX `idx_bill_fapiao_id`(`bill_fapiao_id` ASC) USING BTREE COMMENT '电子账单发票id',
 INDEX `idx_request_id`(`request_id` ASC) USING BTREE COMMENT 'requestId',
 INDEX `idx_batch`(`batch_no` ASC) USING BTREE COMMENT '批次号索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'E-fapiao发票发送记录表';





-- ----------------------------
-- Table structure for tb_kerry_bill_e_fapiao
-- 【注】请先手动删除已有tb_kerry_bill_e_fapiao表，再用以下语句创建新的tb_kerry_bill_e_fapiao表
-- ----------------------------
CREATE TABLE `tb_kerry_bill_e_fapiao` (
 `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
 `kco` varchar(5) NOT NULL DEFAULT '' COMMENT '单据公司',
 `bill_type` varchar(2) NOT NULL DEFAULT '' COMMENT '账单类型｜示例：RN',
 `doc` int NOT NULL DEFAULT 0 COMMENT '单据号',
 `payment_item` varchar(3) NOT NULL DEFAULT '' COMMENT '单据付款项',
 `company_code` varchar(30) NOT NULL DEFAULT '' COMMENT '销方公司编号(根据编号匹配票易通的销项主数据)',
 `an8` int NOT NULL DEFAULT 0 COMMENT 'an8，可代表楼栋、单元号等等 | 示例：********',
 `purchaser_name` varchar(100)  NOT NULL DEFAULT '' COMMENT '购方抬头（专票必填）(按编号，该字段可以不填)，50个汉字、100个字符，允许输入汉字、大小写英文字母、数字、特殊字符~！@#￥%……&*（）',
 `purchaser_tax_no` varchar(20)  NOT NULL DEFAULT '' COMMENT '购方税号（专票必填，个人购方传身份证信息）(按编号，该字段可以不填)，20个字符。只能为数字、大写英文字母',
 `purchaser_address` varchar(100)  NOT NULL DEFAULT '' COMMENT '公司地址  （专票必填）',
 `purchaser_bank_name` varchar(100)  NOT NULL DEFAULT '' COMMENT '购方银行名称｜花旗银行北京分行**********',
 `invoice_type` varchar(5) NOT NULL DEFAULT '' COMMENT '发票类型：gvat-增值税普通发票  svat-增值税专用发票  gvate-增值税电子普票发票  svate-增值税电子专用发票',
 `mailing_address` varchar(500) NOT NULL DEFAULT '' COMMENT '邮寄地址',
 `mcu` varchar(12) NOT NULL DEFAULT '' COMMENT '业态，如：********、********',
 `doco` varchar(6) NOT NULL DEFAULT '' COMMENT 'JDE合同号｜示例：250644',
 `bill_code` varchar(4) NOT NULL DEFAULT '' COMMENT '缴费票据码｜示例：U02M',
 `goods_tax_no` varchar(30)  NOT NULL DEFAULT '' COMMENT '商品货物税收分类编码(19位末级代码才可以开票）（使用税编转换码时，此字段可不填）',
 `item_name` varchar(100)  NOT NULL DEFAULT '' COMMENT '货物或应税劳务名称 ，禁止特殊字符（使用税编转换码时，此字段可不填）',
 `item_spec` varchar(40)  NOT NULL DEFAULT '' COMMENT '规格型号， 禁止特殊字符，如果对方填写则传过来',
 `amount_without_tax` decimal(18, 6) NOT NULL DEFAULT 0.000000 COMMENT '不含税金额（如果priceMethod 为0 则传该字段必传）',
 `tax_rate` decimal(6, 2) NOT NULL DEFAULT 0.00 COMMENT '税率',
 `tax_amount` decimal(18, 6) NOT NULL DEFAULT 0.000000 COMMENT '税额',
 `amount_with_tax` decimal(18, 6) NOT NULL DEFAULT 0.000000 COMMENT '含税金额',
 `tax_discount` varchar(255) NOT NULL DEFAULT '' COMMENT '是否享受税收优惠政策 0-不1-享受',
 `tax_discount_desc` varchar(255) NOT NULL DEFAULT '' COMMENT '税收优惠',
 `extended_doc` varchar(30) NOT NULL DEFAULT '' COMMENT '单据号（加长版），内容上是TOKCO&TODCT&TODOC',
 `bill_draw_date` varchar(30) NOT NULL DEFAULT '' COMMENT '发票日期（可理解为JDE账单日期）',
 `second_name` varchar(40) NOT NULL DEFAULT '' COMMENT '第二字母名称',
 `formated_doco` varchar(8)  NOT NULL DEFAULT '' COMMENT '合同号，doco经过格式转化，内容不变',
 `jde_unit` varchar(8)  NOT NULL DEFAULT '' COMMENT 'JDE单元号',
 `is_contact_person` varchar(3)  NOT NULL DEFAULT '' COMMENT '是否主要联系人',
 `is_tax_payer` varchar(1)  NOT NULL DEFAULT '' COMMENT '是否一般纳税人',
 `tax_classify_desc` varchar(30)  NOT NULL DEFAULT '' COMMENT '税收分类说明',
 `user_name_info` varchar(10)  NOT NULL DEFAULT '' COMMENT '用户',
 `program_id` varchar(10)  NOT NULL DEFAULT '' COMMENT '程序号',
 `dscrp1` varchar(50)  NOT NULL DEFAULT '' COMMENT '描述1',
 `dscrp2` varchar(30)  NOT NULL DEFAULT '' COMMENT '描述2',
 `dscrp3` varchar(255)  NOT NULL DEFAULT '' COMMENT '描述3',
 `constant1` varchar(1)  NOT NULL DEFAULT '' COMMENT '常量',
 `constant2` varchar(30)  NOT NULL DEFAULT '' COMMENT '常量',
 `constant3` decimal(15, 2) NOT NULL DEFAULT 0.00 COMMENT '常量',
 `constant4` decimal(15, 4) NOT NULL DEFAULT 0.0000 COMMENT '常量',
 `constant5` decimal(15, 4) NOT NULL DEFAULT 0.0000 COMMENT '常量',
 `constant6` varchar(10)  NOT NULL DEFAULT '' COMMENT '常量',
 `create_date_jde` int NOT NULL DEFAULT 0 COMMENT 'JDE入表日期',
 `create_time_jde` int NOT NULL DEFAULT 0 COMMENT 'JDE入表时间',
 `event1` varchar(1)  NOT NULL DEFAULT '' COMMENT '事件点1',
 `event2` varchar(1)  NOT NULL DEFAULT '' COMMENT '事件点2',
 `event3` varchar(1)  NOT NULL DEFAULT '' COMMENT '事件点3',
 `sales_bill_no` varchar(100)  NOT NULL DEFAULT '' COMMENT '订单号，内容为TOKCO&TODCT&TODOC&TOSFX',
 `seller_name` varchar(255)  NOT NULL DEFAULT '' COMMENT '销方名称',
 `pdf_url` varchar(150)  NOT NULL COMMENT '发票PDF地址',
 `invoice_no` varchar(64)  NOT NULL COMMENT '发票号',
 `invoice_code` varchar(128)  NOT NULL COMMENT '发票代码',
 `biz_type` varchar(3)  NOT NULL COMMENT '业务类型，固定为JDE',
 `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
 `email_send_status` tinyint NOT NULL DEFAULT 0 COMMENT '邮件发送状态',
 `sms_send_status` tinyint NOT NULL DEFAULT 0 COMMENT '短信发送状态',
 `message_send_status` tinyint NOT NULL DEFAULT 0 COMMENT '站内信发送状态',
 `invoice_status` tinyint NOT NULL DEFAULT 1 COMMENT '开票状态：1-PROCESSING, 2-COMPLETED, 3-FAILED',
 `state` tinyint NOT NULL DEFAULT 0 COMMENT '发票状态：0未知；1正常；2作废；3已红冲；4红冲发票',
 `error_message` varchar(255)  NOT NULL COMMENT '失败信息描述',
 `paper_drew_date` date NOT NULL DEFAULT '1900-01-01' COMMENT '发票开票日期',
 `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
 PRIMARY KEY (`id`) USING BTREE,
 UNIQUE INDEX `uniq_kco_bill_type_doc_payment_item_invoice_no`(`kco` ASC, `bill_type` ASC, `doc` ASC, `payment_item` ASC, `invoice_no` ASC) USING BTREE,
 INDEX `idx_sale_bill_no`(`sales_bill_no` ASC) USING BTREE COMMENT '单号',
 INDEX `idx_mcu`(`mcu` ASC) USING BTREE COMMENT 'BU',
 INDEX `idx_doco`(`doco` ASC) USING BTREE COMMENT '合同号',
 INDEX `idx_an8`(`an8` ASC) USING BTREE COMMENT 'an8',
 INDEX `idx_co`(`company_code` ASC) USING BTREE COMMENT '公司',
 INDEX `idx_invoice_no`(`invoice_no` ASC) USING BTREE COMMENT '发票号',
 INDEX `idx_drew_date`(`paper_drew_date` ASC) USING BTREE COMMENT '出票时间'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'E-fapiao发票信息表';





-- ----------------------------
-- Table structure for tb_kerry_bill_sync_e_fapiao
-- ----------------------------
CREATE TABLE `tb_kerry_bill_sync_e_fapiao` (
 `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
 `kco` varchar(5) NOT NULL DEFAULT '' COMMENT '单据公司',
 `bill_type` varchar(2) NOT NULL DEFAULT '' COMMENT '账单类型｜示例：RN',
 `doc` int NOT NULL DEFAULT 0 COMMENT '单据号',
 `payment_item` varchar(3) NOT NULL DEFAULT '' COMMENT '单据付款项',
 `company_code` varchar(30) NOT NULL DEFAULT '' COMMENT '销方公司编号(根据编号匹配票易通的销项主数据)',
 `an8` int NOT NULL DEFAULT 0 COMMENT 'an8，可代表楼栋、单元号等等 | 示例：********',
 `purchaser_name` varchar(100) NOT NULL DEFAULT '' COMMENT '购方抬头（专票必填）(按编号，该字段可以不填)',
 `purchaser_tax_no` varchar(20) NOT NULL DEFAULT '' COMMENT '购方税号（专票必填，个人购方传身份证信息）',
 `purchaser_address` varchar(100) NOT NULL DEFAULT '' COMMENT '公司地址  （专票必填）',
 `purchaser_bank_name` varchar(100) NOT NULL DEFAULT '' COMMENT '购方银行名称｜花旗银行北京分行**********',
 `invoice_type` varchar(5) NOT NULL DEFAULT '' COMMENT '发票类型：gvat-增值税普通发票  svat-增值税专用发票  gvate-增值税电子普票发票  svate-增值税电子专用发票',
 `mailing_address` varchar(500) NOT NULL DEFAULT '' COMMENT '邮寄地址',
 `mcu` varchar(12) NOT NULL DEFAULT '' COMMENT '业态，如：********、********',
 `doco` varchar(6) NOT NULL DEFAULT '' COMMENT 'JDE合同号｜示例：250644',
 `bill_code` varchar(4) NOT NULL DEFAULT '' COMMENT '缴费票据码｜示例：U02M',
 `goods_tax_no` varchar(30) NOT NULL DEFAULT '' COMMENT '商品货物税收分类编码(19位末级代码才可以开票）（使用税编转换码时，此字段可不填）',
 `item_name` varchar(100) NOT NULL DEFAULT '' COMMENT '货物或应税劳务名称 ，禁止特殊字符（使用税编转换码时，此字段可不填）',
 `item_spec` varchar(40) NOT NULL DEFAULT '' COMMENT '规格型号， 禁止特殊字符，如果对方填写则传过来',
 `amount_without_tax` decimal(18, 6) NOT NULL DEFAULT 0.000000 COMMENT '不含税金额（如果priceMethod 为0 则传该字段必传）',
 `tax_rate` decimal(6, 2) NOT NULL DEFAULT 0.00 COMMENT '税率',
 `tax_amount` decimal(18, 6) NOT NULL DEFAULT 0.000000 COMMENT '税额',
 `amount_with_tax` decimal(18, 6) NOT NULL DEFAULT 0.000000 COMMENT '含税金额',
 `tax_discount` varchar(255) NOT NULL DEFAULT '' COMMENT '是否享受税收优惠政策 0-不1-享受',
 `tax_discount_desc` varchar(255) NOT NULL DEFAULT '' COMMENT '税收优惠',
 `jde_unit` varchar(8) NOT NULL DEFAULT '' COMMENT 'JDE单元号',
 `tax_classify_desc` varchar(30) NOT NULL DEFAULT '' COMMENT '税收分类说明',
 `sales_bill_no` varchar(100) NOT NULL DEFAULT '' COMMENT '订单号，内容为TOKCO&TODCT&TODOC&TOSFX',
 `invoice_upload_status` tinyint NOT NULL DEFAULT 0 COMMENT '开票请求状态，0-开票请求中，1-请求成功，2-请求失败,3-正常发票,4-已红冲,5-红冲票,6-作废,7-开票失败',
 `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
 PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'E-fapiao账单信息表';

