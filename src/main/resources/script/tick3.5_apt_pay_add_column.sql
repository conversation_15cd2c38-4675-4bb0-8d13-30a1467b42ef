-- user-story: https://kerryprops.atlassian.net/browse/KIP-15194
-- task-sql:   https://kerryprops.atlassian.net/browse/KIP-15238

alter table `apt_payment_info`
    add column `invoiced_status` varchar(15) not null default 'TO_BE_SYNC' comment '是否已开票状态';

alter table `apt_pay`
    add column `invoiced_status` varchar(15) not null default 'TO_BE_SYNC' comment '是否已开票状态';

create index idx_project_id using btree on apt_payment_info (project_id);
create index idx_payment_info_id using btree on apt_pay (payment_info_id);
