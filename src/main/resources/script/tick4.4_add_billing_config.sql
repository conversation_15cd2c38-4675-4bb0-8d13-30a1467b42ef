create table `tb_c_bill_selection_config`
(
    `id`               bigint unsigned not null auto_increment comment '主键',
    `project_id`       varchar(32)     not null comment '项目唯一标识',
    `building_id`      varchar(32)     not null default '' comment '楼栋id',
    `building_name`    varchar(45)     not null default '' comment '楼栋名',
    `room_id`          varchar(64)     not null default '' comment '单元id',
    `room_name`        varchar(45)     not null default '' comment '单元名',
    `bill_select_mode` varchar(20)     not null default 'ANY_BILLS' comment '账单选择模式',
    `operator_name`    varchar(50)     not null default '' comment '最后操作人姓名',
    `create_time`      datetime        not null default current_timestamp comment '创建时间',
    `update_time`      datetime        not null default current_timestamp on update current_timestamp comment '更新时间',
    primary key (`id`),
    unique key `uniq_room_bill_select_mode` (`room_id`, `building_id`, `project_id`),
    key `idx_project_id` (`project_id`),
    key `idx_building_id` (`building_id`)
) engine = InnoDB
  default charset = utf8mb4 comment ='单元的账单选择模式配置表';
