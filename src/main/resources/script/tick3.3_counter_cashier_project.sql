-- user-story: https://kerryprops.atlassian.net/browse/KIP-11124
-- task-sql:   https://kerryprops.atlassian.net/browse/KIP-13967

alter table `apt_payment_info`
    add column `description`     varchar(36)    not null default '' comment '描述',
    add column `pay_type_info`   varchar(20)    not null default '' comment '支付方式-中文值',
    add column `advance_amount`  decimal(32, 2) not null default 0.00 comment '预收金额',
    add column `payment_cate`    varchar(32)    not null default 'UNKNOWN' comment '预收科目[A003 : 管理费预收 | A004 : 其他预收]',
    add column `bill_pay_module` varchar(32)    not null default 'KERRY' comment '支付账单来源模块',
    add column `pay_act`         varchar(64)    not null default '' comment '收款号',
    add column `fee_id`          bigint         not null default 0 comment '杂费配置id',
    add column `fee_name`        varchar(30)    not null default '' comment '杂费配置名',
    add column `fee_tax`         decimal(32, 2) not null default 0.00 comment '杂费税率',
    add column `fee_tax_amount`  decimal(32, 2) not null default 0.00 comment '杂费税额',
    add column `create_by`       varchar(64)    not null default '' comment '操作人',
    add column `pay_channel`     varchar(32)    not null default 'ONLINE' comment '费用类型说明',
    add column `bu`              varchar(32)    not null default '' comment '运营单位',
    add column `unit`            varchar(32)    not null default '' comment '账单单元',
    add column `an8`             varchar(32)    not null default '' comment 'an8，可代表楼栋、单元号等等 | 示例：10023780',
    add column `alph`            varchar(255)   not null default '' comment '用户名称',
    add column `doco`            varchar(6)     not null default '' comment 'JDE合同号｜示例：250644';

alter table `apt_pay`
    add column `advance_amount`  decimal(32, 2) not null default 0.00 comment '预收金额',
    add column `bill_pay_module` varchar(32)    not null default 'KERRY' comment '支付账单来源模块',
    add column `fee_id`          bigint         not null default 0 comment '杂费配置id',
    add column `fee_name`        varchar(30)    not null default '' comment '杂费配置名',
    add column `fee_tax`         decimal(32, 2) not null default 0.00 comment '杂费税率',
    add column `fee_tax_amount`  decimal(32, 2) not null default 0.00 comment '杂费税额';

update apt_payment_info set pay_type_info='微信支付' where pay_type = 'WECHAT';
update apt_payment_info set pay_type_info='现金支付' where pay_type = 'CASH';
update apt_payment_info set pay_type_info='支付宝支付' where pay_type = 'ALIPAY';
