server:
  port: 8080

spring:
  datasource:
    url: *********************************************************************************************************************************************************************************************************************************
    username: qa_kip_test1
    password: xHr2zu@7HUMzm8R#0QxA%fNhX5@C44L9
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 25
      auto-commit: true
      idle-timeout: 30000
      pool-name: HikariDataSource-pool
      max-lifetime: 1800000
      connection-timeout: 120000
      connection-test-query: select 1
  redis:
    host: r-uf6t42cej1xcsxqcqn.redis.rds.aliyuncs.com
    port: 6379
    password: 8vCP1U$168vCP1U$16
    database: 0
    timeout: 5s
    lettuce:
      pool:
        max-active: 50
        max-idle: 20
        min-idle: 10
        max-wait: 5s
      shutdown-timeout: 200ms
  servlet:
    multipart:
      enabled: true
      file-size-threshold: 0
      max-request-size: 50MB
      max-file-size: 50MB
  jpa:
    database: mysql
    show-sql: true
mybatis:
  configuration:
    map-underscore-to-camel-case: true
logging:
  config: classpath:log4j2.xml
  level:
    com:
      kerryprops:
        kip:
          service:
            accelerator:
              feign: debug
feign:
  hystrix:
    enabled: true
  client:
    enabled: true
    config:
      default:
        connectTimeout: 30000
        readTimeout: 30000
        logger-level: BASIC
hystrix:
  threadPool:
    default:
      coreSize: 50
  command:
    default:
      execution:
        isolation:
          strategy: SEMAPHORE
          thread:
            timeoutInMilliseconds: 30000
# 项目相关配置
kerry:
  # 名称
  name: KerryLinks
  # 版本
  version: 2.3.0
  # 版权年份
  copyrightYear: 2019
  # 实例演示开关
  demoEnabled: false
  # 文件路径 示例（ Windows配置D:/kerry/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /home/<USER>/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 进行发送账号密码的方式 0 按照已创建就进行发送，1按照task任务进行发送
  userNameEmailSendMethod: 1
  projectRootUrl: http://tsstest.kerryprops.com.cn:9090/vue/
  #关于空气质量需要进行重定向的 链接地址
  airReturnUrl: https://kerrylinks.kerryprops.com.cn/weather/Landscape?

jde:
  dataBase: DATATRANSFER4
  viewName: VCONTRACT
  an8Table: DATATRANSFER4.VEXTRAAN8J
  billTable: CRPDTA.F5515014
  siteView: V_REPAIR_WO_AB
  # jde派单同步接口
  publicDispatch: https://**************:82/WeixinMaster/GenerateWHFromWeiXinServlet.do
  companyDispatch: https://**************:82/WeixinMaster/WeixinMaster.do
  #同步jde评价的表
  F00022: CRPDTA.F00022
  F00092: CRPDTA.F00092
  #同步jde工单状态
  jdeDbPrefix: CRPDTA
  #同步jde工单费用
  jdeRepairBillingFee: DATATRANSFER8.WORKORDERFEE
  # 同步故障类型
  vwoFailtype: DATATRANSFER4.v_wo_failtype
  #同步jeb设备
  F1201: CRPDTA.F1201
  F1217: CRPDTA.F1217
  #同步JDE公寓小区收款
  F560313: CRPDTA.F560313

# 文件上传服务相关配置
kerryfile:
  uploadUrl: https://kerrylinks.kerryprops.com.cn/kerryFileServer/files/upload
  loginUrl: https://kerrylinks.kerryprops.com.cn/kerryFileServer/user/login
  userName: kerry_links03
  password: 5J67N789f3hp5J90N
  oldFilePath: /usr/local/kencery/testTomcat/webapps/Renter/
  #多文件上传接口
  uploadFilesUrl: https://kerrylinks.kerryprops.com.cn/kerryFileServer/files/newUploadFiles

application:
  services:
    hiveAs: https://qa-kip-service-internal.kerryplus.com//hive-view-assembler-service
    bUser: https://qa-kip-service-internal.kerryplus.com//tenant-admin-service
    cUser: https://qa-kip-service-internal.kerryplus.com//profile-service
    sUser: https://qa-kip-service-internal.kerryplus.com//kerry-staff-service
    message: https://qa-kip-service-internal.kerryplus.com//unified-messaging-service
    messageCenter: https://qa-kip-service-internal.kerryplus.com//message-center-service
    file: https://qa-kip-service-internal.kerryplus.com//file-service
    smart-travel: http://ks-smart-travel
    flexible-space: http://ks-flexible-space
    auth: https://dev-gateway-kip.kerryonvip.com
    support: https://dev-gateway-kip.kerryonvip.com
    klClient: https://kerrylinks.kerryprops.com.cn

kip:
  payment:
    host: http://payment-middleware-service/services
    keystorePath: classpath:/store_client.jks
    keystorePassword: password003
    aliasName: pmw_client
    keyPassword: password002
    connectionTimeout: 3000
    pmwPublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsUoakaogSefZWK5lH70SrFV3xtyGsUGGyMuUj3XkWbWtziSN3QYBkDeo+FHGANNLWbV7i0hVsHC7cHjVZ/OOe4YdCF6KlpNTWfEjSJkfrxFtyoJhpW7PPXYu9HvCRstZ65L5j0EhSK2bg5KlkY4oBv9XEzOiDNe3wnxvqQCd6Q+pQybQpTIJLFNQppoyqgSzNTCtxceZbR+OY6xwA3b1OesvcksutZtp6XkVsvUiClYIaVWnyE14bmoROQPNskeURVFOSu/ITquWHANINNHF3bQ8HPsdBvtyPt4Rug2zuSKKhnpcDpbnqjZY4a7F0wQkGLPf0LdkvgqnG2T+g/GuyQIDAQAB
    ttl: 15
    notifyUrl: http://kip-billing/c/apt/pay/callback
server-auth:
  appCode:
    accept: accelerator
dm:
  projects: QHKC,BKC,TKC,SZXP
  kl:
    url: **********************************************
    username: root
    password: Kerry_links2021
scheduler:
  scheduleBBill: "0 0 0/1 * * ?"
  scheduleCBill: "0 0 2 * * ?"
  scheduleWriterPaytoJde: "0 0 3 * * ?"
  confirmFromJde: "0 0 4 * * ?"