package com.kerryprops.kip.bill.integration;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.dao.BillRepository;
import com.kerryprops.kip.bill.dao.BillSendConfigAn8LinkRepository;
import com.kerryprops.kip.bill.dao.BillSendConfigRepository;
import com.kerryprops.kip.bill.dao.entity.BillEntity;
import com.kerryprops.kip.bill.dao.entity.BillSendConfig;
import com.kerryprops.kip.bill.dao.entity.BillSendConfigAn8Link;
import com.kerryprops.kip.bill.dao.entity.QBillSendConfig;
import com.kerryprops.kip.bill.feign.entity.TenantManagerItemResponse;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.impl.BillSendConfigServiceImpl;
import com.kerryprops.kip.bill.webservice.vo.req.BillPayerVo;
import com.kerryprops.kip.bill.webservice.vo.req.BillSendConfigReqVo;
import com.kerryprops.kip.bill.webservice.vo.req.StaffBillSendConfigInsertVo;
import com.kerryprops.kip.bill.webservice.vo.req.StaffBillSendConfigUpdateVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillSendConfigResource;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.ArgumentMatchers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doReturn;

@DisplayName("账单发送配置Service-集成测试")
class BillSendConfigServiceImplTest extends BaseIntegrationTest {

    @Autowired
    private BillRepository billRepository;

    @Autowired
    private BillSendConfigServiceImpl billSendConfigService;

    @Autowired
    private BillSendConfigRepository configRepository;

    @Autowired
    private BillSendConfigAn8LinkRepository linkRepository;

    @Test
    @DisplayName("保存账单发送配置，正常场景")
    void _01_saveBillSendConfig_success() {
        BillSendConfig billSendConfig = generateRandomSendConfig();
        StaffBillSendConfigInsertVo billSendConfigInsertVo = BeanUtil.copy(billSendConfig, StaffBillSendConfigInsertVo.class);

        BillPayerVo billPayerVo1 = new BillPayerVo("有限公司1", "20156075");
        BillPayerVo billPayerVo2 = new BillPayerVo("有限公司2", "20156076");
        billSendConfigInsertVo.setBillPayerVos(List.of(billPayerVo1, billPayerVo2));

        BillEntity billEntity1 = BillEntity.builder()
                .tpDoco(billSendConfigInsertVo.getDoco())
                .tpMcu("mcu_test_1")
                .tpUnit("unit_test_1")
                .delFlag(DeleteFlagEnum.SAVED.code.toString())
                .build();
        BillEntity billEntity2 = BillEntity.builder()
                .tpDoco(billSendConfigInsertVo.getDoco())
                .tpMcu("mcu_test_2")
                .tpUnit("unit_test_2")
                .delFlag(DeleteFlagEnum.SAVED.code.toString())
                .build();
        billRepository.saveAll(List.of(billEntity1, billEntity2));

        List<TenantManagerItemResponse> responses = new ArrayList<>();
        TenantManagerItemResponse tenantManagerItemResponse = new TenantManagerItemResponse();
        tenantManagerItemResponse.setLoginNo(random.nextInt() + StringUtils.EMPTY);
        tenantManagerItemResponse.setId(random.nextLong());
        tenantManagerItemResponse.setUserName(random.nextInt() + StringUtils.EMPTY);
        responses.add(tenantManagerItemResponse);
        doReturn(responses).when(sUserClient).queryLoginAccounts(ArgumentMatchers.anyString());

        // Act
        billSendConfigService.saveBillSendConfig(billSendConfigInsertVo);

        // Assert
        BillSendConfig billSendConfigCheck = configRepository.findAll().get(0);

        assertEquals(billSendConfigInsertVo.getDoco(), billSendConfigCheck.getDoco());
        assertEquals(billSendConfigInsertVo.getPhoneNumber(), billSendConfigCheck.getPhoneNumber());
        assertEquals(tenantManagerItemResponse.getLoginNo(), billSendConfigCheck.getLoginNo());
        assertEquals(String.valueOf(tenantManagerItemResponse.getId()), billSendConfigCheck.getTenantManagerId());
        assertEquals(tenantManagerItemResponse.getUserName(), billSendConfigCheck.getEmailUsername());
        assertEquals(billSendConfigInsertVo.getEmail(), billSendConfigCheck.getEmail());
        assertEquals(billSendConfigInsertVo.getProjectId(), billSendConfigCheck.getProjectId());
        assertEquals(billSendConfigInsertVo.getBuildingId(), billSendConfigCheck.getBuildingId());
        assertEquals(billSendConfigInsertVo.getBuildingName(), billSendConfigCheck.getBuildingName());

        List<BillSendConfigAn8Link> billSendConfigAn8LinksCheck = linkRepository.selectAn8Links(billSendConfigCheck.getId());
        assertEquals(billPayerVo1.getAlph(), billSendConfigAn8LinksCheck.get(0).getAlph());
        assertEquals(billPayerVo1.getAn8(), billSendConfigAn8LinksCheck.get(0).getAn8());
        assertEquals(billPayerVo2.getAlph(), billSendConfigAn8LinksCheck.get(1).getAlph());
        assertEquals(billPayerVo2.getAn8(), billSendConfigAn8LinksCheck.get(1).getAn8());
    }

    @Test
    @DisplayName("更新账单发送配置，正常场景")
    void _02_updateBillSendConfig_success() {
        BillSendConfig billSendConfigTarget = configRepository.findAll().get(0);
        long billSendConfigTargetId = billSendConfigTarget.getId();

        BillSendConfig billSendConfig = generateRandomSendConfig();
        StaffBillSendConfigUpdateVo billSendConfigUpdate = BeanUtil.copy(billSendConfig, StaffBillSendConfigUpdateVo.class);
        billSendConfigUpdate.setId(billSendConfigTargetId);

        BillPayerVo billPayerVo1 = new BillPayerVo("有限公司1", "20156075");
        BillPayerVo billPayerVo2 = new BillPayerVo("有限公司2_new", "20156076_new");
        billSendConfigUpdate.setBillPayerVos(List.of(billPayerVo1, billPayerVo2));

        List<TenantManagerItemResponse> responses = new ArrayList<>();
        TenantManagerItemResponse tenantManagerItemResponse = new TenantManagerItemResponse();
        tenantManagerItemResponse.setLoginNo(random.nextInt() + StringUtils.EMPTY);
        tenantManagerItemResponse.setId(random.nextLong());
        tenantManagerItemResponse.setUserName(random.nextInt() + StringUtils.EMPTY);
        responses.add(tenantManagerItemResponse);
        doReturn(responses).when(sUserClient).queryLoginAccounts(ArgumentMatchers.anyString());

        // Act
        billSendConfigService.updateBillSendConfig(billSendConfigUpdate);

        // Assert
        BillSendConfig billSendConfigCheck = configRepository.findById(billSendConfigTargetId).get();

        assertEquals(billSendConfigUpdate.getPhoneNumber(), billSendConfigCheck.getPhoneNumber());
        assertEquals(tenantManagerItemResponse.getLoginNo(), billSendConfigCheck.getLoginNo());
        assertEquals(String.valueOf(tenantManagerItemResponse.getId()), billSendConfigCheck.getTenantManagerId());
        assertEquals(tenantManagerItemResponse.getUserName(), billSendConfigCheck.getEmailUsername());
        assertEquals(billSendConfigUpdate.getEmail(), billSendConfigCheck.getEmail());
        assertEquals(billSendConfigUpdate.getProjectId(), billSendConfigCheck.getProjectId());
        assertEquals(billSendConfigUpdate.getBuildingId(), billSendConfigCheck.getBuildingId());
        assertEquals(billSendConfigUpdate.getBuildingName(), billSendConfigCheck.getBuildingName());

        List<BillSendConfigAn8Link> billSendConfigAn8LinksCheck = linkRepository.selectAn8Links(billSendConfigTargetId)
                .stream().filter(e -> DeleteFlagEnum.SAVED.code.equals(e.getIsDel())).collect(Collectors.toList());
        assertEquals(billPayerVo1.getAlph(), billSendConfigAn8LinksCheck.get(0).getAlph());
        assertEquals(billPayerVo1.getAn8(), billSendConfigAn8LinksCheck.get(0).getAn8());
        assertEquals(billPayerVo2.getAlph(), billSendConfigAn8LinksCheck.get(1).getAlph());
        assertEquals(billPayerVo2.getAn8(), billSendConfigAn8LinksCheck.get(1).getAn8());
    }

    @Test
    @DisplayName("查询账单发送配置，正常场景")
    void _03_searchBillSendConfig_success() {
        BillSendConfig billSendConfig1 = generateRandomSendConfig();
        billSendConfig1.setIsDel(1);
        configRepository.save(billSendConfig1);

        BillSendConfig billSendConfig2 = generateRandomSendConfig();
        billSendConfig2.setDoco("doco_test2");
        configRepository.save(billSendConfig2);

        BillSendConfig billSendConfig3 = generateRandomSendConfig();
        billSendConfig3.setProjectId("project_id_test2");
        configRepository.save(billSendConfig3);

        BillSendConfig billSendConfig4 = generateRandomSendConfig();
        billSendConfig4.setBuildingId("building_id_test2");
        billSendConfig4.setBuildingName("building_name_test2");
        configRepository.save(billSendConfig4);

        BillSendConfig billSendConfig5 = generateRandomSendConfig();
        configRepository.save(billSendConfig5);

        BillSendConfigAn8Link billSendConfigAn8Link2 = BillSendConfigAn8Link.builder()
                .configId(billSendConfig5.getId()).alph("有限公司1").an8("20156076")
                .isDel(DeleteFlagEnum.SAVED.code).build();
        linkRepository.saveAll(List.of(billSendConfigAn8Link2));

        // Act
        Pageable pageable = PageRequest.of(0, 5);

        String conditionDoco = "doco_test";
        String conditionProjectId = "project_id_test";
        String conditionBuildingId = "building_id_test";

        BillSendConfigReqVo billSendConfigReqVo = new BillSendConfigReqVo();
        billSendConfigReqVo.setDoco(conditionDoco);
        billSendConfigReqVo.setProjectId(conditionProjectId);
        billSendConfigReqVo.setBuildingIds(List.of(conditionBuildingId));
        billSendConfigReqVo.setAlph("有限公司2");
        billSendConfigReqVo.setAn8("20156076");

        Page<BillSendConfigResource> resourcePage = billSendConfigService.searchBillSendConfig(pageable, billSendConfigReqVo);

        // Assert
        BillSendConfigResource billSendConfigResourceCheck = resourcePage.get().findFirst().get();

        assertEquals(conditionDoco, billSendConfigResourceCheck.getDoco());
        assertEquals(conditionProjectId, billSendConfigResourceCheck.getProjectId());
        assertEquals(conditionBuildingId, billSendConfigResourceCheck.getBuildingId());
        assertEquals("building_name_test", billSendConfigResourceCheck.getBuildingName());

        assertEquals(2, billSendConfigResourceCheck.getAn8LinkList().size());

        List<BillSendConfigAn8Link> billSendConfigAn8Links = billSendConfigResourceCheck.getAn8LinkList();
        assertEquals("有限公司1", billSendConfigAn8Links.get(0).getAlph());
        assertEquals("20156075", billSendConfigAn8Links.get(0).getAn8());
        assertEquals("有限公司2_new", billSendConfigAn8Links.get(1).getAlph());
        assertEquals("20156076_new", billSendConfigAn8Links.get(1).getAn8());
    }

    @Test
    @DisplayName("删除账单发送配置，正常场景")
    void _04_deleteBillSendConfig_success() {
        BillSendConfig billSendConfig = generateRandomSendConfig();
        configRepository.save(billSendConfig);

        BillSendConfigAn8Link billSendConfigAn8Link1 = generateRandomSendConfigAn8Link();
        billSendConfigAn8Link1.setConfigId(billSendConfig.getId());

        BillSendConfigAn8Link billSendConfigAn8Link2 = generateRandomSendConfigAn8Link();
        billSendConfigAn8Link2.setConfigId(billSendConfig.getId());
        linkRepository.saveAll(List.of(billSendConfigAn8Link1, billSendConfigAn8Link2));

        // Act
        boolean result = billSendConfigService.deleteBillSendConfig(List.of(billSendConfig.getId()));

        // Assert
        assertTrue(result);

        BillSendConfig billSendConfigActual = configRepository.findById(billSendConfig.getId()).get();
        assertEquals(DeleteFlagEnum.DELETED.code, billSendConfigActual.getIsDel());

        List<BillSendConfigAn8Link> billSendConfigAn8Links = linkRepository.selectAn8Links(billSendConfig.getId());
        billSendConfigAn8Links.forEach(billSendConfigAn8Link ->
                assertEquals(DeleteFlagEnum.DELETED.code, billSendConfigAn8Link.getIsDel()));
    }

    /**
     * KIP-22856 新增变更时间&操作人字段
     */
    @Test
    @DisplayName("新增变更时间&操作人字段：保存数据，正常场景")
    void _05_addManualUpdateField_saveBillSendConfig_success() {
        // Arrange
        clearBillSendConfigData();
        String mockNickName = "test_nick_name_save";
        setMockLoginUser(mockNickName);

        BillSendConfig billSendConfig = generateRandomSendConfig();
        StaffBillSendConfigInsertVo billSendConfigInsertVo = BeanUtil.copy(billSendConfig, StaffBillSendConfigInsertVo.class);

        billSendConfigInsertVo.setBillPayerVos(List.of(new BillPayerVo("有限公司3", "20156077")));
        
        // Act
        billSendConfigService.saveBillSendConfig(billSendConfigInsertVo);

        // Assert
        BillSendConfig billSendConfigCheck = getLastestBillSendConfig();
        
        assertEquals(billSendConfigInsertVo.getDoco(), billSendConfigCheck.getDoco());
        assertEquals(billSendConfigInsertVo.getPhoneNumber(), billSendConfigCheck.getPhoneNumber());
        assertEquals(billSendConfigInsertVo.getProjectId(), billSendConfigCheck.getProjectId());

        assertEquals(mockNickName, billSendConfigCheck.getManualUpdateOperator());
        assertNotNull(billSendConfigCheck.getManualUpdateTime());
    }

    @ParameterizedTest
    @CsvSource({
            "0",
            "1"
    })
    @DisplayName("新增变更时间&操作人字段：更新数据，正常场景")
    void _06_addManualUpdateField_updateBillSendConfig_success(String condition) {
        // Arrange
        if ("0".equals(condition)) {
            UserInfoUtils.setUser((LoginUser) null);
        } else {
            var mockLoginUser = new LoginUser();
            mockLoginUser.setNickName(null);
            UserInfoUtils.setUser(mockLoginUser);
        }

        BillSendConfig billSendConfigTarget = getLastestBillSendConfig();
        long billSendConfigTargetId = billSendConfigTarget.getId();

        BillSendConfig billSendConfig = generateRandomSendConfig();
        StaffBillSendConfigUpdateVo billSendConfigUpdate = BeanUtil.copy(billSendConfig, StaffBillSendConfigUpdateVo.class);
        billSendConfigUpdate.setId(billSendConfigTargetId);

        billSendConfigUpdate.setBillPayerVos(Collections.emptyList());

        // Act
        billSendConfigService.updateBillSendConfig(billSendConfigUpdate);

        // Assert
        BillSendConfig billSendConfigCheck = configRepository.findById(billSendConfigTargetId).get();

        assertEquals(billSendConfigUpdate.getPhoneNumber(), billSendConfigCheck.getPhoneNumber());
        assertEquals(billSendConfigUpdate.getEmail(), billSendConfigCheck.getEmail());

        assertNotNull(billSendConfigCheck.getManualUpdateOperator());
        assertNotNull(billSendConfigCheck.getManualUpdateTime());
    }

    @Test
    @DisplayName("新增变更时间&操作人字段：更新数据，正常场景")
    void _07_addManualUpdateField_updateBillSendConfig_success() {
        // Arrange
        String mockNickName = "test_nick_name_update";
        setMockLoginUser(mockNickName);

        BillSendConfig billSendConfigTarget = getLastestBillSendConfig();
        long billSendConfigTargetId = billSendConfigTarget.getId();

        BillSendConfig billSendConfig = generateRandomSendConfig();
        StaffBillSendConfigUpdateVo billSendConfigUpdate = BeanUtil.copy(billSendConfig, StaffBillSendConfigUpdateVo.class);
        billSendConfigUpdate.setId(billSendConfigTargetId);

        billSendConfigUpdate.setBillPayerVos(List.of(new BillPayerVo("有限公司4", "20156079")));

        // Act
        billSendConfigService.updateBillSendConfig(billSendConfigUpdate);

        // Assert
        BillSendConfig billSendConfigCheck = configRepository.findById(billSendConfigTargetId).get();

        assertEquals(billSendConfigUpdate.getPhoneNumber(), billSendConfigCheck.getPhoneNumber());
        assertEquals(billSendConfigUpdate.getEmail(), billSendConfigCheck.getEmail());
        assertEquals(mockNickName, billSendConfigCheck.getManualUpdateOperator());
        assertNotNull(billSendConfigCheck.getManualUpdateTime());
    }

    @Test
    @DisplayName("新增变更时间&操作人字段：根据id查询数据，正常场景")
    void _08_addManualUpdateField_queryById_success() {
        // Arrange
        BillSendConfig billSendConfigTarget = getLastestBillSendConfig();
        long billSendConfigTargetId = billSendConfigTarget.getId();

        // Act
        BillSendConfigResource resourceActual = billSendConfigService.queryById(billSendConfigTargetId);

        // Assert
        BillSendConfig billSendConfigCheck = configRepository.findById(billSendConfigTargetId).get();

        assertEquals(resourceActual.getPhoneNumber(), billSendConfigCheck.getPhoneNumber());
        assertEquals(resourceActual.getEmail(), billSendConfigCheck.getEmail());
        assertEquals(resourceActual.getProjectId(), billSendConfigCheck.getProjectId());

        assertTrue(StringUtils.isNotEmpty(resourceActual.getManualUpdateOperator()));
        assertNotNull(resourceActual.getManualUpdateTime());
        assertEquals("yyyy-MM-dd HH:mm:ss".length(), resourceActual.getManualUpdateTime().length());
    }

    @Test
    @DisplayName("新增变更时间&操作人字段：分页查询数据，正常场景")
    void _09_addManualUpdateField_searchBillSendConfig_success() {
        // Arrange
        BillSendConfig billSendConfigTarget = getLastestBillSendConfig();

        BillSendConfigReqVo billSendConfigReqVo = new BillSendConfigReqVo();
        billSendConfigReqVo.setProjectId(billSendConfigTarget.getProjectId());

        Pageable pageable = PageRequest.of(0, 5);

        // Act
        Page<BillSendConfigResource> resourcePage = billSendConfigService.searchBillSendConfig(pageable, billSendConfigReqVo);

        // Assert
        BillSendConfigResource billSendConfigResourceCheck = resourcePage.get().findFirst().get();

        assertTrue(StringUtils.isNotEmpty(billSendConfigResourceCheck.getManualUpdateOperator()));
        assertTrue(StringUtils.isNotEmpty(billSendConfigResourceCheck.getManualUpdateTime()));
        assertEquals("yyyy-MM-dd HH:mm:ss".length(), billSendConfigResourceCheck.getManualUpdateTime().length());
    }

    @Test
    @DisplayName("新增变更时间&操作人字段：删除账单发送配置，正常场景")
    void _10_addManualUpdateField_deleteBillSendConfig_success() {
        // Arrange
        String mockNickName = "test_nick_name_delete";
        setMockLoginUser(mockNickName);

        BillSendConfig billSendConfigTarget = getLastestBillSendConfig();

        // Act
        boolean result = billSendConfigService.deleteBillSendConfig(List.of(billSendConfigTarget.getId()));

        // Assert
        assertTrue(result);

        BillSendConfig billSendConfigActual = configRepository.findById(billSendConfigTarget.getId()).get();
        assertEquals(mockNickName, billSendConfigActual.getManualUpdateOperator());
        assertEquals(DeleteFlagEnum.DELETED.code, billSendConfigActual.getIsDel());
        assertNotNull(billSendConfigActual.getManualUpdateTime());

        List<BillSendConfigAn8Link> billSendConfigAn8Links = linkRepository.selectAn8Links(billSendConfigTarget.getId());
        billSendConfigAn8Links.forEach(billSendConfigAn8Link ->
                                               assertEquals(DeleteFlagEnum.DELETED.code, billSendConfigAn8Link.getIsDel()));
    }


    private BillSendConfig generateRandomSendConfig() {
        BillSendConfig billSendConfig = new BillSendConfig();
        billSendConfig.setDoco("doco_test");
        billSendConfig.setPhoneNumber(random.nextInt() + StringUtils.EMPTY);
        billSendConfig.setEmail(random.nextInt() + StringUtils.EMPTY);
        billSendConfig.setProjectId("project_id_test");
        billSendConfig.setBuildingId("building_id_test");
        billSendConfig.setBuildingName("building_name_test");
        billSendConfig.setManualUpdateOperator(random.nextInt() + StringUtils.EMPTY);
        billSendConfig.setManualUpdateTime(ZonedDateTime.now());
        billSendConfig.setIsDel(0);
        return billSendConfig;
    }

    private BillSendConfigAn8Link generateRandomSendConfigAn8Link() {
        return BillSendConfigAn8Link.builder()
                .alph(StringUtils.EMPTY)
                .an8(StringUtils.EMPTY)
                .isDel(DeleteFlagEnum.SAVED.code)
                .build();
    }

    private void setMockLoginUser(String nickName) {
        var mockLoginUser = new LoginUser();
        mockLoginUser.setNickName(nickName);
        UserInfoUtils.setUser(mockLoginUser);
    }

    private BillSendConfig getLastestBillSendConfig() {
        return configRepository.getJpaQueryFactory().select(QBillSendConfig.billSendConfig)
                               .from(QBillSendConfig.billSendConfig)
                               .orderBy(QBillSendConfig.billSendConfig.createdTime.desc())
                               .fetchFirst();
    }
    
    private void clearBillSendConfigData() {
        configRepository.deleteAll();
        linkRepository.deleteAll();
    }

    @Getter
    private enum DeleteFlagEnum {
        DELETED(1, "已删除的"),
        SAVED(0, "未删除的");

        private final Integer code;

        private final String info;

        DeleteFlagEnum(Integer code, String info) {
            this.code = code;
            this.info = info;
        }
    }

}
