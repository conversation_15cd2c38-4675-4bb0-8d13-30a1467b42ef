package com.kerryprops.kip.bill.integration;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.enums.ContentTypeEnum;
import com.kerryprops.kip.bill.common.enums.InvoiceRecordStatus;
import com.kerryprops.kip.bill.common.enums.InvoiceState;
import com.kerryprops.kip.bill.common.enums.SendStatus;
import com.kerryprops.kip.bill.dao.BizContentReadRepository;
import com.kerryprops.kip.bill.dao.EFapiaoBillInvoiceRepository;
import com.kerryprops.kip.bill.dao.entity.BizContentReadEntity;
import com.kerryprops.kip.bill.dao.entity.EFapiaoBillInvoice;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.IBillService;
import com.kerryprops.kip.bill.service.impl.KerryBillEFapiaoServiceImpl;
import com.kerryprops.kip.bill.webservice.vo.resp.BillPayer;
import com.kerryprops.kip.bill.webservice.vo.resp.ContentUnreadInfoVo;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;

class KerryBillEFapiaoServiceImplTest extends BaseIntegrationTest {

    @Autowired
    private EFapiaoBillInvoiceRepository eFapiaoBillInvoiceRepository;

    @Autowired
    private KerryBillEFapiaoServiceImpl kerryBillEFapiaoServiceImpl;

    @Autowired
    private BizContentReadRepository bizContentReadRepository;

    @Autowired
    private IBillService billService;

    /**
     * 根据楼盘id模糊查询购方公司：正常场景，且入参中bu（经营单元）列表为空
     */
    @Test
    void _01_fuzzyQueryPurchaserNames_bus_empty_success() {
        List<BillPayer> result = kerryBillEFapiaoServiceImpl.fuzzyQueryPurchaserNames(Collections.emptyList(), null);
        assertEquals(0, result.size());
    }

    /**
     * 根据楼盘id模糊查询付款人：正常场景
     */
    @ParameterizedTest
    @CsvSource({
            "31194100, TestPurchaserName, TestPurchaserName2",
            "31194100;31194101, Name2, TestPurchaserName2",
            "31194100;31194101, TestPurchaserName, TestPurchaserName1;TestPurchaserName2",
            "31194100;31194101, TestPurchaserName-test22, TestPurchaserName2"
    })
    void _02_fuzzyQueryPurchaserNames_success(String busString, String payer, String resultCheck) {


        List<String> bus = List.of(busString.split(";"));

        // 执行待测部分
        List<BillPayer> result = kerryBillEFapiaoServiceImpl.fuzzyQueryPurchaserNames(bus, payer);

        // 执行结果校验
        String[] resultCheckArr = resultCheck.split(";");
        for (int i = 0; i < resultCheckArr.length; i++) {
            assertEquals(resultCheckArr[i], result.get(i).getTpAlph());
        }
    }

    /**
     * 当前用户是否全部已读：正常场景
     */
    @ParameterizedTest
    @CsvSource({
            " , true, 3",
            "3, true, 2",
            "3;4, true, 1",
            "3;4;5, false, 0",
    })
    void _03_userInvoiceReadStatus_success(String contentIdsStr, boolean hasUnreadContent, long unreadContentCount) {
        Long randomUserId = random.nextLong();
        setUserInfo(randomUserId);

        BillPayer billPayer1 = BillPayer.builder().tpAn8("test2222").build();
        BillPayer billPayer2 = BillPayer.builder().tpAn8("12345678").build();
        Mockito.doReturn(Set.of(billPayer1, billPayer2)).when(billService).getInvoiceBillPayers();

        Mockito.doReturn(Set.of("250644")).when(billService).getDocos();

        // id可能不从0开始，获取偏移量
        long idOffset = eFapiaoBillInvoiceRepository.findAll().stream()
                .min(Comparator.comparing(EFapiaoBillInvoice::getId)).get().getId() - 1;

        // 设置已读信息
        List<Long> contentIds = StringUtils.isEmpty(contentIdsStr) ? Collections.emptyList()
                : Arrays.stream(contentIdsStr.split(";")).map(Long::parseLong)
                .map(id -> id + idOffset).toList();

        for (Long contentId : contentIds) {
            BizContentReadEntity bizContentReadEntity1 = new BizContentReadEntity();
            bizContentReadEntity1.setType(ContentTypeEnum.E_FAPIAO);
            bizContentReadEntity1.setContentId(contentId);
            bizContentReadEntity1.setUserId(randomUserId);
            bizContentReadRepository.save(bizContentReadEntity1);
        }

        // 执行待测部分
        ContentUnreadInfoVo result = kerryBillEFapiaoServiceImpl.userInvoiceReadStatus();

        // 执行结果校验
        assertEquals(hasUnreadContent, result.isHasUnreadContent());
        assertEquals(unreadContentCount, result.getUnreadContentCount());
    }

    /**
     * 设置该发票对当前用户已读：正常场景
     */
    @Test
    void _04_setUserInvoiceReaded_success() {
        Long randomUserId = random.nextLong();
        setUserInfo(randomUserId);

        // 执行待测部分
        Boolean result = kerryBillEFapiaoServiceImpl.setUserInvoiceReaded(100L);

        // 执行结果校验
        BizContentReadEntity bizContentReadEntity = bizContentReadRepository.findAll().stream()
                .max(Comparator.comparing(BizContentReadEntity::getCreatedTime)).get();
        assertEquals(Boolean.TRUE, result);
        assertEquals(100L, bizContentReadEntity.getContentId());
        assertEquals(randomUserId, bizContentReadEntity.getUserId());
    }

    /**
     * 标记发票信息当前用户全部已读：正常场景
     */
    @Test
    void _05_setUserInvoiceAllReaded_success() {
        long randomUserId = random.nextLong();
        setUserInfo(randomUserId);

        BillPayer billPayer1 = BillPayer.builder().tpAn8("test2222").build();
        BillPayer billPayer2 = BillPayer.builder().tpAn8("12345678").build();
        Mockito.doReturn(Set.of(billPayer1, billPayer2)).when(billService).getInvoiceBillPayers();

        Mockito.doReturn(Set.of("250644")).when(billService).getDocos();

        // 执行待测部分
        Boolean result = kerryBillEFapiaoServiceImpl.setUserInvoiceAllReaded();

        // 执行结果校验
        Collection<Long> billInvoiceIds = eFapiaoBillInvoiceRepository.findAll().stream().map(EFapiaoBillInvoice::getId).collect(Collectors.toSet());
        List<Long> billIds = bizContentReadRepository
                .findContentIdsByTypeAndUserIdAndContentIds(ContentTypeEnum.E_FAPIAO, randomUserId, billInvoiceIds);
        assertEquals(Boolean.TRUE, result);
        assertEquals(3, billIds.size());
    }

    @BeforeEach
    void initBills() {
        eFapiaoBillInvoiceRepository.deleteAll();

        EFapiaoBillInvoice eFapiaoBillInvoice1 = buildEFapiaoBillInvoice();
        eFapiaoBillInvoice1.setMcu("NOT_EXIST");
        eFapiaoBillInvoice1.setDoco("NOT_EXIST");
        eFapiaoBillInvoice1.setPurchaserName("TestPurchaserName0");
        eFapiaoBillInvoiceRepository.save(eFapiaoBillInvoice1);

        EFapiaoBillInvoice eFapiaoBillInvoice2 = buildEFapiaoBillInvoice();
        eFapiaoBillInvoice2.setMcu("31194100");
        eFapiaoBillInvoice2.setAn8("test1111");
        eFapiaoBillInvoice2.setPurchaserName("TestPurchaserName2");
        eFapiaoBillInvoiceRepository.save(eFapiaoBillInvoice2);

        EFapiaoBillInvoice eFapiaoBillInvoice3 = buildEFapiaoBillInvoice();
        eFapiaoBillInvoice3.setMcu("31194100");
        eFapiaoBillInvoice3.setAn8("test2222");
        eFapiaoBillInvoice3.setPurchaserName("TestPurchaserName2");
        eFapiaoBillInvoiceRepository.save(eFapiaoBillInvoice3);

        EFapiaoBillInvoice eFapiaoBillInvoice4 = buildEFapiaoBillInvoice();
        eFapiaoBillInvoice4.setMcu("31194100");
        eFapiaoBillInvoice4.setPurchaserName("xxx");
        eFapiaoBillInvoiceRepository.save(eFapiaoBillInvoice4);

        EFapiaoBillInvoice eFapiaoBillInvoice5 = buildEFapiaoBillInvoice();
        eFapiaoBillInvoice5.setMcu("31194101");
        eFapiaoBillInvoice5.setPurchaserName("TestPurchaserName1");
        eFapiaoBillInvoiceRepository.save(eFapiaoBillInvoice5);

        EFapiaoBillInvoice eFapiaoBillInvoice6 = buildEFapiaoBillInvoice();
        eFapiaoBillInvoice6.setMcu("31194101");
        eFapiaoBillInvoice6.setPurchaserName("TestPurchaserName3");
        eFapiaoBillInvoice6.setIsDelete(1);
        eFapiaoBillInvoiceRepository.save(eFapiaoBillInvoice6);
    }

    private void setUserInfo(Long userId) {
        UserInfoUtils.setUser("{\"nickName\":\"SuperAdmin\",\"roles\":\"SUPER_ADMIN\"" +
                ",\"userId\":" + userId + ",\"phoneNumber\":\"13661600321\",\"fromType\":\"B\"}");
    }

    private EFapiaoBillInvoice buildEFapiaoBillInvoice() {
        EFapiaoBillInvoice eFapiaoBillInvoice = EFapiaoBillInvoice.builder()
                .kco(getRandomTestString())
                .billType("RD")
                .doc(random.nextInt())
                .paymentItem("009")
                .an8("12345678")
                .doco("250644")
                .billCode("U02M")
                .amountWithoutTax(BigDecimal.ZERO)
                .taxRate(BigDecimal.ZERO)
                .taxAmount(BigDecimal.ZERO)
                .amountWithTax(BigDecimal.ZERO)
                .constant3(BigDecimal.ZERO)
                .constant4(BigDecimal.ZERO)
                .constant5(BigDecimal.ZERO)
                .createDateJde(random.nextInt())
                .createTimeJde(random.nextInt())
                .isDelete(0)
                .emailSendStatus(SendStatus.MSG_SUCCESS)
                .smsSendStatus(SendStatus.MSG_SUCCESS)
                .messageSendStatus(SendStatus.MSG_FAILED)
                .invoiceRecordStatus(InvoiceRecordStatus.COMPLETED)
                .state(InvoiceState.NORMAL)
                .paperDrewDate("1900-01-01")
                .build();

        return noNullStringAttr(eFapiaoBillInvoice);
    }

    /**
     * 把对象中的 String 类型的null字段，转换为空字符串
     *
     * @param <T> 待转化对象类型
     * @param cls 待转化对象
     * @return 转化好的对象
     */
    private <T> T noNullStringAttr(T cls) {
        Field[] fields = cls.getClass().getDeclaredFields();
        if (0 == fields.length) {
            return cls;
        }

        for (Field field : fields) {
            if ("String".equals(field.getType().getSimpleName())) {
                field.setAccessible(true);
                try {
                    Object value = field.get(cls);
                    if (Objects.isNull(value)) {
                        field.set(cls, StringUtils.EMPTY);
                    }
                } catch (IllegalArgumentException | IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        }
        return cls;
    }

    private String getRandomTestString() {
        return "t" + random.nextInt(999);
    }

}
