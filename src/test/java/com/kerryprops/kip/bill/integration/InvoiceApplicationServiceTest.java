package com.kerryprops.kip.bill.integration;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.enums.InvoiceCustomerType;
import com.kerryprops.kip.bill.common.enums.InvoiceRecordStatus;
import com.kerryprops.kip.bill.common.enums.OrderType;
import com.kerryprops.kip.bill.dao.InvoiceRecordRepository;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.dao.entity.InvoiceRecord;
import com.kerryprops.kip.bill.service.impl.AptInvoiceSmsService;
import com.kerryprops.kip.bill.service.impl.InvoiceApplicationService;
import com.kerryprops.kip.bill.webservice.vo.req.CallBackKerryInvoiceMainVo;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;

class InvoiceApplicationServiceTest extends BaseIntegrationTest {

    private final String SALES_BILL_NO = "P20220519182730000001a";

    private final ObjectMapper mapper = new ObjectMapper();

    @MockBean
    private AptInvoiceSmsService aptInvoiceSmsService;

    @Autowired
    private InvoiceRecordRepository invoiceRecordRepository;

    @Autowired
    private InvoiceApplicationService invoiceApplicationService;

    private InvoiceRecord invoiceRecord = null;

    @Test
    void _01_updateInvoiceRecord_nonExistenceOrderNo() {
        CallBackKerryInvoiceMainVo mainVo = new CallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo("abctest");
        invoiceApplicationService.updateInvoiceRecord(new AptPaymentInfo(), mainVo);
    }

    @Test
    void _02_updateInvoiceRecord_invoiceFailed() {
        final String errorMessage = "业务单号为：P20220831094312000001失败, 原因:[购方税号【测似乎】只能是数字和大写字母]";
        CallBackKerryInvoiceMainVo mainVo = new CallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo(SALES_BILL_NO);
        mainVo.setInvoiceStatus(0);
        mainVo.setStatus(1);
        mainVo.setMessage(errorMessage);

        invoiceApplicationService.updateInvoiceRecord(new AptPaymentInfo(), mainVo);

        InvoiceRecord updatedInvoiceRecord = invoiceRecordRepository.getByOrderNo(SALES_BILL_NO);
        assertEquals(InvoiceRecordStatus.FAILED, updatedInvoiceRecord.getStatus());
        assertEquals(updatedInvoiceRecord.getErrorMessage(), errorMessage);
    }

    @Test
    void _03_updateInvoiceRecord_invoiceSuccess() throws JsonProcessingException {
        doNothing().when(aptInvoiceSmsService).sendSms(any(AptPaymentInfo.class), any(InvoiceRecord.class),
                any(CallBackKerryInvoiceMainVo.class), any(String.class), any(String.class), any(String.class));

        String INVOICE_SUCCESS_DATA = """
                {
                \t"amountWithTax": 0.030000,
                \t"amountWithoutTax": 0.030000,
                \t"businessType": "",
                \t"cashierName": "王静",
                \t"checkCode": "79809457063786317219",
                \t"checkerName": "孙明",
                \t"detailVos": [{
                \t\t"amountWithTax": 0.03000000,
                \t\t"goodsTaxNo": "3040502020200000000",
                \t\t"itemName": "电费",
                \t\t"quantity": 1.0000000000,
                \t\t"salesbillItemNo": "B2022280440419112215180",
                \t\t"taxRate": 0.0600000000,
                \t\t"unitPriceWithTax": 0.03000000
                \t}],
                \t"ext1": "物业管理费",
                \t"ext2": "",
                \t"invoiceCode": "************",
                \t"invoiceNo": "32093202a",
                \t"invoiceStatus": 1,
                \t"invoiceType": "ce",
                \t"invoicerName": "石佳妮K+Auto",
                \t"paperDrewDate": "********",
                \t"pdfUrl": "http://www.baidu.com/invoice2.pdf",
                \t"ofdUrl": "http://www.baidu.com/invoice2.ofd",
                \t"xmlUrl": "http://www.baidu.com/invoice2.xml",
                \t"purchaserAddress": "",
                \t"purchaserBankAccount": "",
                \t"purchaserBankName": "",
                \t"purchaserName": "Test",
                \t"purchaserNo": "",
                \t"purchaserTaxNo": "",
                \t"purchaserTel": "",
                \t"receiveUserEmail": "",
                \t"remark": "P20220829141631000001 Test",
                \t"salesbillNo": "P20220519182730000001a",
                \t"sellerAddress": "北京市朝阳区光华路1号北京嘉里中心北楼2301单元",
                \t"sellerBankAccount": "************",
                \t"sellerBankName": "中国银行股份有限公司北京国际贸易中心支行",
                \t"sellerName": "北京嘉奥房地产开发有限公司",
                \t"sellerNo": "31007",
                \t"sellerTaxNo": "91110000600034917G",
                \t"sellerTel": "010-********",
                \t"status": 1,
                \t"taxAmount": 0.000000
                }
                """;
        CallBackKerryInvoiceMainVo mainVo = mapper.readValue(INVOICE_SUCCESS_DATA, CallBackKerryInvoiceMainVo.class);

        invoiceApplicationService.updateInvoiceRecord(new AptPaymentInfo(), mainVo);

        //verify
        InvoiceRecord updatedInvoiceRecord = invoiceRecordRepository.getByOrderNo(SALES_BILL_NO);
        assertEquals(InvoiceRecordStatus.COMPLETED, updatedInvoiceRecord.getStatus());
        assertTrue(updatedInvoiceRecord.getIsInvoiceInvoked());
        assertEquals("32093202a,32093202a", updatedInvoiceRecord.getInvoiceNo());
        assertEquals("北京嘉奥房地产开发有限公司", updatedInvoiceRecord.getSellerName());
        assertEquals("http://www.baidu.com/invoice1.pdf,http://www.baidu.com/invoice2.pdf",
                updatedInvoiceRecord.getPdfUrl());
        assertEquals("http://www.baidu.com/invoice1.xml,http://www.baidu.com/invoice2.xml",
                updatedInvoiceRecord.getXmlUrl());
        assertEquals("http://www.baidu.com/invoice1.ofd,http://www.baidu.com/invoice2.ofd",
                updatedInvoiceRecord.getOfdUrl());
        assertEquals("abc,************", updatedInvoiceRecord.getInvoiceCode());
        assertEquals("0.030000", updatedInvoiceRecord.getTotalAmountAfterSplit());
    }

    @BeforeEach
    void prepareData() {
        invoiceRecord = InvoiceRecord.builder()
                .invoiceNo("32093202a").customerName("测试").sellerName("物业test").invoiceCode("abc")
                .email("<EMAIL>").orderNo("P20220519182730000001a").orderType(OrderType.REPAIR_ORDER)
                .userId("12345999").applyTime(LocalDateTime.now()).status(InvoiceRecordStatus.PROCESSING)
                .isInvoiceInvoked(false).amount(BigDecimal.valueOf(0.01)).taxAmount(BigDecimal.valueOf(0.01))
                .totalAmount(BigDecimal.valueOf(0.01)).issuer("A2").invoiceType("增值税电子普通发票")
                .pdfUrl("http://www.baidu.com/invoice1.pdf")
                .xmlUrl("http://www.baidu.com/invoice1.xml")
                .ofdUrl("http://www.baidu.com/invoice1.ofd")
                .customerType(InvoiceCustomerType.COMPANY)
                .errorMessage(StringUtils.EMPTY)
                .build();
        invoiceRecord.setCreateTime(new Date());
        invoiceRecord.setUpdateTime(new Date());
        invoiceRecord = invoiceRecordRepository.save(invoiceRecord);
    }

    @AfterEach
    void clearData() {
        invoiceRecordRepository.delete(invoiceRecord);
    }

}
