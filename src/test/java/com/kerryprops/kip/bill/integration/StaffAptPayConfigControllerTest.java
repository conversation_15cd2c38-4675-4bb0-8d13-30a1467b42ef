package com.kerryprops.kip.bill.integration;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.AptPayConfigRepository;
import com.kerryprops.kip.bill.dao.entity.AptPayConfig;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.webservice.impl.StaffAptPayConfigController;
import com.kerryprops.kip.bill.webservice.vo.req.AptPayConfigSaveVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptPayConfigSearchVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptPayConfigUpdateVo;
import com.kerryprops.kip.bill.webservice.vo.resp.AptPayConfigVo;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;
import java.util.Optional;

class StaffAptPayConfigControllerTest extends BaseIntegrationTest {

    @Autowired
    private AptPayConfigRepository aptPayConfigRepository;

    @Autowired
    private StaffAptPayConfigController staffAptPayConfigController;

    @BeforeEach
    public void prepareTestData() {
        LoginUser loginUser = LoginUser.builder()
                .fromType("S")
                .cId(random.nextInt() + StringUtils.EMPTY)
                .projectIds("192,BKC")
                .build();
        UserInfoUtils.setUser(loginUser);
    }

    @Test
    void _01_staffAptPayConfigController_save_success() {
        aptPayConfigRepository.deleteAll();

        AptPayConfig aptPayConfig = generateAptPayConfig();
        AptPayConfigSaveVo saveReqVo = BeanUtil.copy(aptPayConfig, AptPayConfigSaveVo.class);

        // 执行待验证部分
        staffAptPayConfigController.save(saveReqVo);

        // 验证执行情况
        Long idObj = aptPayConfigRepository.findAll().get(0).getId();
        Optional<AptPayConfig> aptPayConfigOptional = aptPayConfigRepository.findById(idObj);
        AptPayConfig aptPayConfigCheck = aptPayConfigOptional.get();

        Assertions.assertEquals(aptPayConfig.getTax(), aptPayConfigCheck.getTax());
        Assertions.assertEquals(aptPayConfig.getMax(), aptPayConfigCheck.getMax());
        executeAssertions(aptPayConfig, aptPayConfigCheck);
    }

    @Test
    void _02_staffAptPayConfigController_update_success() {
        AptPayConfig aptPayConfig = generateAptPayConfig();
        AptPayConfigUpdateVo updateReqVo = BeanUtil.copy(aptPayConfig, AptPayConfigUpdateVo.class);
        Long idObj = aptPayConfigRepository.findAll().get(0).getId();
        updateReqVo.setId(idObj);

        // 执行待验证部分
        staffAptPayConfigController.update(updateReqVo);

        // 验证执行情况
        Optional<AptPayConfig> aptPayConfigOptional = aptPayConfigRepository.findById(idObj);
        AptPayConfig aptPayConfigCheck = aptPayConfigOptional.get();

        Assertions.assertEquals(aptPayConfig.getTax(), aptPayConfigCheck.getTax());
        Assertions.assertEquals(aptPayConfig.getMax(), aptPayConfigCheck.getMax());
        executeAssertions(aptPayConfig, aptPayConfigCheck);
    }

    @Test
    void _03_staffAptPayConfigController_getById_success() {
        // 执行待验证部分
        Long idObj = aptPayConfigRepository.findAll().get(0).getId();
        RespWrapVo<AptPayConfigVo> aptPayConfigVoRespWrapVo = staffAptPayConfigController.getById(idObj);
        AptPayConfigVo aptPayConfigVo = aptPayConfigVoRespWrapVo.getData();

        // 验证执行情况
        Assertions.assertEquals(idObj, aptPayConfigVo.getId());
    }

    @Test
    void _04_staffAptPayConfigController_disableById_success() {
        // 执行待验证部分
        Long idObj = aptPayConfigRepository.findAll().get(0).getId();
        staffAptPayConfigController.disableById(idObj);

        // 验证执行情况
        final Integer DISABLED_STATUS = 0;
        Optional<AptPayConfig> aptPayConfigOptional = aptPayConfigRepository.findById(idObj);
        AptPayConfig aptPayConfigCheck = aptPayConfigOptional.get();
        Assertions.assertEquals(DISABLED_STATUS, aptPayConfigCheck.getEnabledStatus());
    }

    @Test
    void _05_staffAptPayConfigController_enableById_success() {
        // 执行待验证部分
        Long idObj = aptPayConfigRepository.findAll().get(0).getId();
        staffAptPayConfigController.enableById(idObj);

        // 验证执行情况
        final Integer ENABLED_STATUS = 1;
        Optional<AptPayConfig> aptPayConfigOptional = aptPayConfigRepository.findById(idObj);
        AptPayConfig aptPayConfigCheck = aptPayConfigOptional.get();
        Assertions.assertEquals(ENABLED_STATUS, aptPayConfigCheck.getEnabledStatus());
    }

    @Test
    void _06_staffAptPayConfigController_deleteById_success() {
        // 执行待验证部分
        Long idObj = aptPayConfigRepository.findAll().get(0).getId();
        staffAptPayConfigController.deleteById(idObj);

        // 验证执行情况
        Optional<AptPayConfig> aptPayConfigOptional = aptPayConfigRepository.findById(idObj);
        Assertions.assertFalse(aptPayConfigOptional.isPresent());
    }

    @Test
    void _07_staffAptPayConfigController_search_success() {
        aptPayConfigRepository.save(generateAptPayConfig());
        aptPayConfigRepository.save(generateAptPayConfig());

        AptPayConfig aptPayConfig = generateAptPayConfig();
        aptPayConfig.setDeleteAt(1L);
        aptPayConfigRepository.save(aptPayConfig);

        // 执行待验证部分
        AptPayConfigSearchVo aptPayConfigSearchVo = new AptPayConfigSearchVo();
        aptPayConfigSearchVo.setProjectId("192");
        RespWrapVo<Page<AptPayConfigVo>> pageRespWrapVo = staffAptPayConfigController
                .search(PageRequest.of(0, 3), aptPayConfigSearchVo);

        // 验证执行情况
        Page<AptPayConfigVo> aptPayConfigVoPage = pageRespWrapVo.getData();
        Assertions.assertEquals(2, aptPayConfigVoPage.get().toList().size());
    }

    @Test
    void _08_staffAptPayConfigController_getPaymentType_success() {
        AptPayConfig aptPayConfig1 = generateAptPayConfig();
        aptPayConfig1.setPaymentType("支付宝代扣");
        aptPayConfigRepository.save(aptPayConfig1);

        AptPayConfig aptPayConfig2 = generateAptPayConfig();
        aptPayConfig2.setPaymentType("微信代扣");
        aptPayConfigRepository.save(aptPayConfig2);

        // 执行待验证部分
        RespWrapVo<List<String>> pageRespWrapVo = staffAptPayConfigController
                .getPaymentType("192");

        // 验证执行情况
        List<String> pagePaymentType = pageRespWrapVo.getData();
        Assertions.assertEquals("[微信代扣, 微信支付, 支付宝代扣]", pagePaymentType.stream().sorted()
                .toList().toString());
    }

    private AptPayConfig generateAptPayConfig() {
        AptPayConfig aptPayConfig = new AptPayConfig();
        aptPayConfig.setChannel(BillPayChannel.OFFLINE);
        aptPayConfig.setPaymentType("微信支付");
        aptPayConfig.setPaymentCate(randomString());
        aptPayConfig.setPaymentDetail(randomString());
        aptPayConfig.setBankAccount(randomString());
        aptPayConfig.setMcu(randomString());
        aptPayConfig.setProjectId("192");
        aptPayConfig.setBuildingId(randomString());
        aptPayConfig.setPositionItem(PositionItemResponse.builder()
                .projectName(randomString()).build());
        aptPayConfig.setTax(Double.valueOf("0.5"));
        aptPayConfig.setMax(Double.valueOf("5"));
        aptPayConfig.setComments(randomString());
        aptPayConfig.setCompanyCode("31007");
        aptPayConfig.setDeleteAt(0L);
        aptPayConfig.setEnabledStatus(1);
        return aptPayConfig;
    }

    private void executeAssertions(AptPayConfig aptPayConfigExpected, AptPayConfig aptPayConfigActual) {
        Assertions.assertEquals(aptPayConfigExpected.getChannel(), aptPayConfigActual.getChannel());
        Assertions.assertEquals(aptPayConfigExpected.getPaymentType(), aptPayConfigActual.getPaymentType());
        Assertions.assertEquals(aptPayConfigExpected.getPaymentCate(), aptPayConfigActual.getPaymentCate());
        Assertions.assertEquals(aptPayConfigExpected.getPaymentDetail(), aptPayConfigActual.getPaymentDetail());
        Assertions.assertEquals(aptPayConfigExpected.getBankAccount(), aptPayConfigActual.getBankAccount());
        Assertions.assertEquals(aptPayConfigExpected.getMcu(), aptPayConfigActual.getMcu());
        Assertions.assertEquals(aptPayConfigExpected.getProjectId(), aptPayConfigActual.getProjectId());
        Assertions.assertEquals(aptPayConfigExpected.getPositionItem().getBuildingName(), aptPayConfigActual.getPositionItem().getBuildingName());
        Assertions.assertEquals(aptPayConfigExpected.getComments(), aptPayConfigActual.getComments());
        Assertions.assertEquals(aptPayConfigExpected.getCompanyCode(), aptPayConfigActual.getCompanyCode());
        Assertions.assertEquals(aptPayConfigExpected.getDeleteAt(), aptPayConfigActual.getDeleteAt());
        Assertions.assertEquals(aptPayConfigExpected.getEnabledStatus(), aptPayConfigActual.getEnabledStatus());
    }

}
