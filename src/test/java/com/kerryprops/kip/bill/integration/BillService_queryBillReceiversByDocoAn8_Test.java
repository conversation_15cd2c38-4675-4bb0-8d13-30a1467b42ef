package com.kerryprops.kip.bill.integration;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.dao.BillSendConfigAn8LinkRepository;
import com.kerryprops.kip.bill.dao.BillSendConfigRepository;
import com.kerryprops.kip.bill.dao.entity.BillSendConfig;
import com.kerryprops.kip.bill.dao.entity.BillSendConfigAn8Link;
import com.kerryprops.kip.bill.service.impl.BillServiceImpl;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffBillReceiverRespVo;
import io.jsonwebtoken.lang.Assert;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Set;

class BillService_queryBillReceiversByDocoAn8_Test extends BaseIntegrationTest {

    private static final String DOCO = "274786";

    private static final String AN8 = "20000686";

    @Autowired
    private BillSendConfigRepository configRepository;

    @Autowired
    private BillSendConfigAn8LinkRepository an8LinkRepository;

    @Autowired
    private BillServiceImpl billService;

    @Test
    void _01_queryBillReceiversByDocoAn8_1InvalidParam_jdeDoco() {
        Set<StaffBillReceiverRespVo> receiverRespVoSet = billService.queryBillReceiversByDocoAn8(null, AN8);
        Assertions.assertTrue(CollectionUtils.isEmpty(receiverRespVoSet));
    }

    @Test
    void _02_queryBillReceiversByDocoAn8_1InvalidParam_an8() {
        Set<StaffBillReceiverRespVo> receiverRespVoSet = billService.queryBillReceiversByDocoAn8(DOCO, null);
        Assertions.assertTrue(CollectionUtils.isEmpty(receiverRespVoSet));
    }

    @Test
    void _03_queryBillReceiversByDocoAn8_2InvalidParams_same() {
        Set<StaffBillReceiverRespVo> receiverRespVoSet = billService.queryBillReceiversByDocoAn8(null, null);
        Assertions.assertTrue(CollectionUtils.isEmpty(receiverRespVoSet));
    }

    @Test
    void _04_queryBillReceiversByDocoAn8_2InvalidParams_diff() {
        Set<StaffBillReceiverRespVo> receiverRespVoSet = billService.queryBillReceiversByDocoAn8(null, StringUtils.EMPTY);
        Assertions.assertTrue(CollectionUtils.isEmpty(receiverRespVoSet));
    }

    @Test
    void _02_normal() {
        List<BillSendConfig> oldConfigs = buildBillSendConfigs();
        List<BillSendConfig> newConfigs = configRepository.saveAll(oldConfigs);
        List<BillSendConfigAn8Link> oldAn8Links = buildBillSendConfigAn8Links(newConfigs);
        List<BillSendConfigAn8Link> newAn8Links = an8LinkRepository.saveAll(oldAn8Links);

        Set<StaffBillReceiverRespVo> receiverRespVoSet = billService.queryBillReceiversByDocoAn8(newConfigs.get(3).getDoco()
                , newAn8Links.get(4).getAn8());
        Assert.notEmpty(receiverRespVoSet);
        StaffBillReceiverRespVo vo = receiverRespVoSet.iterator().next();
        Assertions.assertEquals(newConfigs.get(3).getEmail(), vo.getEmail());
        Assertions.assertEquals(newConfigs.get(3).getTenantManagerId(), vo.getUserId());
        Assertions.assertEquals(newConfigs.get(3).getEmailUsername(), vo.getUserName());

        receiverRespVoSet = billService.queryBillReceiversByDocoAn8(newConfigs.get(3).getDoco()
                , newAn8Links.get(5).getAn8());
        Assertions.assertTrue(CollectionUtils.isEmpty(receiverRespVoSet));
        configRepository.deleteAll();
        an8LinkRepository.deleteAll();
    }

    private List<BillSendConfig> buildBillSendConfigs() {
        return List.of(
                BillSendConfig.builder().doco("112233").email("<EMAIL>")
                        .phoneNumber(StringUtils.EMPTY).loginNo(StringUtils.EMPTY)
                        .mcu("mcu1").unit("unit1").isDel(0)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .build(),
                BillSendConfig.builder().doco("224411").email("<EMAIL>")
                        .phoneNumber("13764912345").loginNo("60939742")
                        .mcu("mcu1").unit("unit2").isDel(1)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .build(),
                BillSendConfig.builder().doco("112255").email("<EMAIL>")
                        .phoneNumber("13764912346").loginNo("12345678")
                        .mcu("mcu1").unit("unit2").isDel(0)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .build(),
                BillSendConfig.builder().doco("112244").email("<EMAIL>")
                        .phoneNumber("13764912345").loginNo("60939742").emailUsername("" + random.nextLong())
                        .mcu("mcu1").unit("unit2").isDel(0).tenantManagerId("" + random.nextLong())
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .build());
    }


    private List<BillSendConfigAn8Link> buildBillSendConfigAn8Links(List<BillSendConfig> configs) {
        return List.of(
                BillSendConfigAn8Link.builder().configId(configs.get(0).getId()).isDel(0).alph("alph_1").an8("an8_1").build(),

                BillSendConfigAn8Link.builder().configId(configs.get(1).getId()).isDel(1).alph("用户名称2").an8("用户编号2").build(),
                BillSendConfigAn8Link.builder().configId(configs.get(1).getId()).isDel(1).alph("用户名称3").an8("用户编号3").build(),

                BillSendConfigAn8Link.builder().configId(configs.get(2).getId()).isDel(0).alph("alph_2").an8("an8_2").build(),

                BillSendConfigAn8Link.builder().configId(configs.get(3).getId()).isDel(0).alph("用户名称4").an8("用户编号4").build(),
                BillSendConfigAn8Link.builder().configId(configs.get(3).getId()).isDel(1).alph("用户名称1").an8("用户编号1").build()
        );
    }

}
