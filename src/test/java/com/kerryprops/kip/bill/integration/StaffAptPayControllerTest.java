package com.kerryprops.kip.bill.integration;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.current.LoginUtils;
import com.kerryprops.kip.bill.common.enums.AptPayVerifyStatus;
import com.kerryprops.kip.bill.common.enums.AptSyncJobStatus;
import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import com.kerryprops.kip.bill.common.enums.BillPayModule;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.FeeTypeEnum;
import com.kerryprops.kip.bill.common.enums.PayCancelTypeEnum;
import com.kerryprops.kip.bill.common.enums.PaymentCateEnum;
import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.utils.DateUtils;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.AptPayRepository;
import com.kerryprops.kip.bill.dao.AptPaymentInfoRepository;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPayConfig;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.feign.entity.TaxClassifyCodesResource;
import com.kerryprops.kip.bill.webservice.vo.req.AptPaySaveVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptPaySearchVo;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import com.kerryprops.kip.hiveas.feign.dto.resp.RoomRespDto;
import com.kerryprops.kip.hiveas.webservice.resource.resp.BuildingSimple;
import com.kerryprops.kip.hiveas.webservice.resource.resp.FloorSimple;
import com.kerryprops.kip.hiveas.webservice.resource.resp.ProjectSimple;
import com.kerryprops.kip.hiveas.webservice.resource.resp.RoomResp;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.common.enums.PayCancelTypeEnum.ADMIN_CANCELLED;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;

@SuppressWarnings("rawtypes")
@DisplayName("C端-用户支付-单元测试")
class StaffAptPayControllerTest extends BaseIntegrationTest {
    private static final long CONFIG_ID_1 = 1L;

    private static final long CONFIG_ID_2 = 2L;

    private static final long CONFIG_ID_3 = 3L;

    private final String SUPER_ADMIN_JSON = "{\"nickName\":\"SuperAdmin\",\"roles\":\"SUPER_ADMIN\",\"staff\":true," +
            "\"userId\":123," + "\"uniqueUserId\":\"123\",\"phoneNumber\":\"13661600321\",\"fromType\":\"S\"," +
            "\"client\":false," + "\"superAdmin\":true,\"tenant\":false}";

    private final String STAFF_JSON = "{\"buildingIds\":\"P1-B1,P2-B1\",\"nickName\":\"JAKCAdmin\"," +
            "\"staff\":true,\"userId\":345,\"uniqueUserId\":\"345\",\"fromType\":\"S\",\"client\":false," +
            "\"superAdmin\":false,\"tenant\":false}";

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private AptPayRepository aptPayRepository;

    @Autowired
    private AptPaymentInfoRepository aptPaymentInfoRepository;

    @AfterEach
    public void cleanTestData() {
        aptPayRepository.deleteAll();
    }

    /**
     * 搜索接口：用户登录状态场景
     */
    @Test
    void _01_user_not_login_exception() {
        ResponseEntity<RespWrapVo> resp = call_search_api(null, StringUtils.EMPTY);
        assertEquals(HttpStatus.OK, resp.getStatusCode());
        assertEquals("not login.", resp.getBody().getMessage());
        assertEquals(RespCodeEnum.UNKNOWN_ERROR.getCode(), resp.getBody().getCode());
    }

    /**
     * 搜索接口：正常查询场景
     */
    @ParameterizedTest
    @CsvSource({
            "false,?, P2;P6;P7",
            "true,?deletedAt=1, P1;P3",
            "true,?id=5, P2;P4;P5;P6;P7;P8;P9;P10;P11",
            "true,?payDateStart=2021-02-01 12:12:12&payDateEnd=2021-02-11 10:10:10, P10",
            "true,?payAct=P7, P7",
            "true,?roomId=P2-B2-R1, P8",
            "true,?buildingId=P2-B1, P6;P7",
            "false,?buildingId=P2-B1, P6;P7",
            "true,?projectId=P1, P2;P4;P5",
            "false,?projectId=P1, P2",
            "true,?payConfigId=2, P4;P5;P6",
            "true,?payType=POS机, P9;P10",
            "true,?sendJdeStatus=4, P4;P5;P8;P9",
            "true,?verifyStatus=0, P9;P10;P11",
            "true,?verifyStatus=1, P2;P4;P5;P6;P7;P8"
    })
    void _02_query_one_param_(boolean isAdmin, String param, String payActs) {
        TaxClassifyCodesResource taxClassifyCodesResource = new TaxClassifyCodesResource();
        taxClassifyCodesResource.setFeeType(FeeTypeEnum.PROPERTY_MANAGEMENT.name());
        taxClassifyCodesResource.setBillCode(randomString());
        doReturn(List.of(taxClassifyCodesResource)).when(kipInvoiceClient).listTaxClassifyConfig(any());
        doReturn("").when(hiveAsClient).getPropertyManagementCo(anyString());

        String xUserJson = isAdmin ? SUPER_ADMIN_JSON : STAFF_JSON;
        ResponseEntity<RespWrapVo> response = call_search_api(xUserJson, param);

        //verification
        String[] payActArray = StringUtils.split(payActs, ";");
        List<String> expectedPayActList = Arrays.asList(payActArray);
        verify(response, expectedPayActList);
    }

    @ParameterizedTest
    @CsvSource({
            "false,?",
            "true,?deletedAt=1",
            "true,?id=5",
            "true,?payDateStart=2021-02-01 12:12:12&payDateEnd=2021-02-11 10:10:10",
            "true,?payAct=P7",
            "true,?roomId=P2-B2-R1",
            "true,?buildingId=P2-B1",
            "false,?buildingId=P2-B1",
            "true,?projectId=P1",
            "false,?projectId=P1",
            "true,?payConfigId=2",
            "true,?payType=POS机",
            "true,?sendJdeStatus=4",
            "true,?verifyStatus=0",
            "true,?verifyStatus=1"
    })
    @DisplayName("正常导出场景")
    void _03_export_success(boolean isAdmin, String param) {
        // Arrange
        String xUserJson = isAdmin ? SUPER_ADMIN_JSON : STAFF_JSON;

        // Act and assert
        Assertions.assertDoesNotThrow(() -> call_export_api(xUserJson, param));
    }

    /**
     * 按条件批量审核：按条件过滤后待审核数据为空场景
     */
    @Test
    void _03_batchVerify_no_data_fail() {
        AptPaySearchVo aptPaySearchVo = new AptPaySearchVo();
        aptPaySearchVo.setPayAct(getRandomStr());

        ResponseEntity<Integer> responseEntity = call_batchVerify_api(STAFF_JSON, aptPaySearchVo);
        assertNull(responseEntity.getBody());
    }

    /**
     * 按条件批量审核：正常场景
     */
    @ParameterizedTest
    @CsvSource({
            "false,{}, 0",
            "true,{\"deletedAt\": 1}, 0",
            "true,{\"payDateStart\": \"2021-02-01 12:12:12\";\"payDateEnd\": \"2021-02-11 10:10:10\"}, 1",
            "true,{\"payAct\": \"P7\"}, 0",
            "true,{\"roomId\": \"P2-B2-R1\"}, 0",
            "true,{\"buildingId\": \"P2-B1\"}, 0",
            "false,{\"buildingId\": \"P2-B1\"}, 0",
            "true,{\"projectId\": \"P2\"}, 2",
            "false,{\"projectId\": \"P2\"}, 0",
            "true,{\"payConfigId\": 2}, 0",
            "true,{\"payConfigId\": 3}, 3",
            "false,{\"payConfigId\": 3}, 0",
            "true,{\"payType\": \"POS机\"}, 2",
            "true,{\"sendJdeStatus\": 4}, 1",
            "true,{\"verifyStatus\": 0}, 3"
    })
    void _04_batchVerify_success(boolean isAdmin, String requestBodyStr, Integer expectNum) throws JsonProcessingException {
        String xUserJson = isAdmin ? SUPER_ADMIN_JSON : STAFF_JSON;

        requestBodyStr = requestBodyStr.replace(';', ',');
        AptPaySearchVo aptPaySearchVo = objectMapper.readValue(requestBodyStr, AptPaySearchVo.class);

        ResponseEntity<Integer> responseEntity = call_batchVerify_api(xUserJson, aptPaySearchVo);
        int response = responseEntity.getBody();

        assertEquals(expectNum, response);
    }

    /**
     * 公寓小区-支付审核状态切换：待修改数据为空场景
     */
    @Test
    void _05_switchVerifyById_no_data_fail() {
        ResponseEntity<Boolean> responseEntity = call_switchVerifyById_api(STAFF_JSON, 1L);
        assertNull(responseEntity.getBody());
    }

    /**
     * 公寓小区-支付审核状态切换：待修改数据已回写场景
     */
    @Test
    void _06_switchVerifyById_data_already_write_back_fail() {
        ResponseEntity<Boolean> responseEntity = call_switchVerifyById_api(SUPER_ADMIN_JSON, 8L);
        assertNull(responseEntity.getBody());
    }

    /**
     * 公寓小区-支付审核状态切换：正常场景
     */
    @Test
    void _07_switchVerifyById_success() {
        long id = getIdStart() + 10L;

        ResponseEntity<Boolean> responseEntity1 = call_switchVerifyById_api(SUPER_ADMIN_JSON, id);
        assertEquals(Boolean.TRUE, responseEntity1.getBody());

        Optional<AptPay> aptPayOptional1 = aptPayRepository.findById(id);
        assertEquals(AptPayVerifyStatus.VERIFIED, aptPayOptional1.get().getVerifyStatus());

        ResponseEntity<Boolean> responseEntity2 = call_switchVerifyById_api(SUPER_ADMIN_JSON, id);
        assertEquals(Boolean.TRUE, responseEntity2.getBody());

        Optional<AptPay> aptPayOptional2 = aptPayRepository.findById(id);
        assertEquals(AptPayVerifyStatus.TO_BE_VERIFIED, aptPayOptional2.get().getVerifyStatus());
    }

    /**
     * 公寓小区-保存对象：正常场景
     */
    @Test
    void _08_save_success() {
        RoomRespDto roomResp = new RoomRespDto();
        roomResp.setId(getRandomStr());
        roomResp.setRoomNo(getRandomStr());

        BuildingSimple buildingSimple = new BuildingSimple();
        buildingSimple.setId(getRandomStr());
        buildingSimple.setName(getRandomStr());

        RoomResp roomRespVo = new RoomResp();
        roomRespVo.setRoom(roomResp);
        roomRespVo.setBuilding(buildingSimple);
        roomRespVo.setFloor(new FloorSimple());
        roomRespVo.setProject(new ProjectSimple());

        RespWrapVo<RoomResp> roomRespVoRespWrapVo = new RespWrapVo<>();
        roomRespVoRespWrapVo.setData(roomRespVo);
        roomRespVoRespWrapVo.setCode(RespCodeEnum.SUCCESS.getCode());
        doReturn(roomRespVoRespWrapVo).when(hiveAsClient).getRoomById(any());

        AptPayConfig payConfig = new AptPayConfig();
        payConfig.setChannel(BillPayChannel.OFFLINE);
        payConfig.setTax(random.nextDouble());
        payConfig.setMax(random.nextDouble());
        doReturn(Optional.of(payConfig)).when(aptPayConfigRepository).findById(any());

        AptPaySaveVo aptPaySaveVo = new AptPaySaveVo();
        aptPaySaveVo.setRoomId(getRandomStr());
        aptPaySaveVo.setBu(getRandomStr());
        aptPaySaveVo.setUnit(getRandomStr());
        aptPaySaveVo.setAn8(getRandomStr());
        aptPaySaveVo.setAlph(getRandomStr());
        aptPaySaveVo.setDoco(getRandomStr());
        aptPaySaveVo.setPayDesc(getRandomStr());
        aptPaySaveVo.setPayDate(new Date());
        aptPaySaveVo.setPayConfigId(random.nextLong());
        aptPaySaveVo.setPayType(getRandomStr());
        aptPaySaveVo.setPaymentCate(getRandomStr());
        aptPaySaveVo.setPayTranx(getRandomStr());
        aptPaySaveVo.setTotalAmt(random.nextDouble());
        aptPaySaveVo.setComments(getRandomStr());
        aptPaySaveVo.setPayTranx(getRandomStr());

        ResponseEntity<RespWrapVo> responseEntity1 = call_save_api(SUPER_ADMIN_JSON, aptPaySaveVo);
        assertEquals(Boolean.TRUE, Boolean.valueOf(responseEntity1.getBody().getData().toString()));

        Optional<AptPay> aptPayOptional = aptPayRepository.findById(getIdStart() + 11L);
        AptPay newAptPay = aptPayOptional.get();

        assertEquals(buildingSimple.getId(), newAptPay.getBuildingId());
        assertEquals(roomResp.getId(), newAptPay.getRoomId());

        PositionItemResponse newPositionItem = newAptPay.getPositionItem();
        assertEquals(buildingSimple.getName(), newPositionItem.getBuildingName());
        assertEquals(roomResp.getRoomNo(), newPositionItem.getRoomName());

        assertEquals(payConfig.getChannel(), newAptPay.getPayChannel());
        assertEquals(payConfig.getTax(), newAptPay.getTax());
    }

    /**
     * 公寓小区-支付审核：目标数据已审核，无实际操作
     */
    @Test
    void _09_verify_already_verified_success() {
        long id = getIdStart() + 7L;

        ResponseEntity<RespWrapVo> responseEntity1 = call_verify_api(SUPER_ADMIN_JSON, id);
        assertEquals(Boolean.TRUE, Boolean.valueOf(responseEntity1.getBody().getData().toString()));

        Optional<AptPay> aptPayOptional = aptPayRepository.findById(id);
        assertEquals(AptPayVerifyStatus.VERIFIED, aptPayOptional.get().getVerifyStatus());
    }

    /**
     * 公寓小区-支付审核：目标数据未审核，正常审核
     */
    @Test
    void _10_verify_success() {
        long id = getIdStart() + 10L;

        ResponseEntity<RespWrapVo> responseEntity1 = call_verify_api(SUPER_ADMIN_JSON, id);
        assertEquals(Boolean.TRUE, Boolean.valueOf(responseEntity1.getBody().getData().toString()));

        Optional<AptPay> aptPayOptional = aptPayRepository.findById(id);
        assertEquals(AptPayVerifyStatus.VERIFIED, aptPayOptional.get().getVerifyStatus());
    }

    /**
     * 公寓小区-支付审核：删除线下收款
     */
    @Test
    void _11_deleteOfflinePay_success() {
        final int CANCEL_INVOICE = -1;
        final int APPLIED_INVOICE_INITIAL = 0;
        final int APT_DELETED_AT = 1;

        long id = getIdStart() + 10L;

        AptPay aptPayObj = aptPayRepository.findById(id).get();
        aptPayObj.setPaymentInfoId(String.valueOf(random.nextLong()));
        aptPayRepository.save(aptPayObj);

        AptPaymentInfo aptPaymentInfo = AptPaymentInfo.builder()
                .id(aptPayObj.getPaymentInfoId())
                .paymentStatus(BillPaymentStatus.PAID)
                .cancelType(PayCancelTypeEnum.UNKNOWN.name())
                .appliedInvoice(APPLIED_INVOICE_INITIAL)
                .build();
        aptPaymentInfoRepository.save(aptPaymentInfo);

        // 执行待验证部分
        ResponseEntity<RespWrapVo> responseEntity = call_deleteOfflinePay_api(SUPER_ADMIN_JSON, id);
        assertEquals(Boolean.TRUE, Boolean.valueOf(responseEntity.getBody().getData().toString()));

        // 验证执行结果
        AptPay aptPayActual = aptPayRepository.findById(id).get();
        assertEquals(APT_DELETED_AT, aptPayActual.getDeletedAt());

        AptPaymentInfo aptPaymentInfoActual = aptPaymentInfoRepository.findTopById(aptPayObj.getPaymentInfoId());
        assertEquals(BillPaymentStatus.CANCEL, aptPaymentInfoActual.getPaymentStatus());
        assertEquals(ADMIN_CANCELLED.name(), aptPaymentInfoActual.getCancelType());
        assertEquals(CANCEL_INVOICE, aptPaymentInfoActual.getAppliedInvoice());
    }

    @BeforeEach
    void prepareTestData() {
        AptPaymentInfo aptPaymentInfoMock1 = AptPaymentInfo.builder().id("1").cancelType(PayCancelTypeEnum.UNKNOWN.name()).build();
        AptPaymentInfo aptPaymentInfoMock2 = AptPaymentInfo.builder().id("2").cancelType(PayCancelTypeEnum.UNKNOWN.name()).build();
        AptPaymentInfo aptPaymentInfoMock3 = AptPaymentInfo.builder().id("3").cancelType(PayCancelTypeEnum.UNKNOWN.name()).build();
        aptPaymentInfoRepository.saveAll(List.of(aptPaymentInfoMock1, aptPaymentInfoMock2, aptPaymentInfoMock3));

        final int MAX_ADVANCE_AMOUNT = 9999;
        AptPay[] aptPayArr = {
                AptPay.builder().deletedAt(1).deletedBy("jane.dong").deletedTime(new Date())
                        .projectId("P1").buildingId("P1-B1").roomId("P1-B1-R1")
                        .payConfigId(CONFIG_ID_1).payType("微信支付").payAct("P1")
                        .payDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2021-01-01 10:11:12"))
                        .advanceAmount(BigDecimal.valueOf(random.nextInt(MAX_ADVANCE_AMOUNT)))
                        .paymentCate(PaymentCateEnum.A003.name())
                        .sendJdeStatus(AptSyncJobStatus.CREATED.getStatus())
                        .verifyStatus(AptPayVerifyStatus.VERIFIED)
                        .billPayModule(BillPayModule.KERRY)
                        .paymentInfoId(aptPaymentInfoMock1.getId()).build(),
                AptPay.builder().projectId("P1").buildingId("P1-B1").roomId("P1-B1-R2")
                        .payConfigId(CONFIG_ID_1).payType("微信支付").payAct("P2")
                        .payDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2021-01-02 15:16:12"))
                        .advanceAmount(BigDecimal.valueOf(random.nextInt(MAX_ADVANCE_AMOUNT)))
                        .paymentCate(PaymentCateEnum.A003.name())
                        .sendJdeStatus(AptSyncJobStatus.PROCESSING.getStatus())
                        .verifyStatus(AptPayVerifyStatus.VERIFIED)
                        .billPayModule(BillPayModule.KERRY)
                        .paymentInfoId(aptPaymentInfoMock2.getId()).build(),
                AptPay.builder().deletedAt(1).deletedBy("jane.dong").deletedTime(new Date())
                        .projectId("P1").buildingId("P1-B2").roomId("P1-B2-R1")
                        .payConfigId(CONFIG_ID_1).payType("微信支付").payAct("P3")
                        .advanceAmount(BigDecimal.valueOf(random.nextInt(MAX_ADVANCE_AMOUNT)))
                        .paymentCate(PaymentCateEnum.A003.name())
                        .payDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2021-01-03 14:11:12"))
                        .sendJdeStatus(AptSyncJobStatus.CREATED.getStatus())
                        .verifyStatus(AptPayVerifyStatus.VERIFIED)
                        .billPayModule(BillPayModule.KERRY)
                        .paymentInfoId(aptPaymentInfoMock3.getId()).build(),
                AptPay.builder().projectId("P1").buildingId("P1-B2").roomId("P1-B2-R2")
                        .payConfigId(CONFIG_ID_2).payType("支付宝支付").payAct("P4")
                        .payDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2021-02-01 10:11:12"))
                        .advanceAmount(BigDecimal.valueOf(random.nextInt(MAX_ADVANCE_AMOUNT)))
                        .paymentCate(PaymentCateEnum.A003.name())
                        .sendJdeStatus(AptSyncJobStatus.DONE.getStatus())
                        .verifyStatus(AptPayVerifyStatus.VERIFIED)
                        .billPayModule(BillPayModule.KERRY)
                        .paymentInfoId(aptPaymentInfoMock1.getId()).build(),
                AptPay.builder().projectId("P1").buildingId("P1-B3").roomId("P1-B3-R1")
                        .payConfigId(CONFIG_ID_2).payType("支付宝支付").payAct("P5")
                        .payDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2021-01-10 13:11:12"))
                        .advanceAmount(BigDecimal.valueOf(random.nextInt(MAX_ADVANCE_AMOUNT)))
                        .paymentCate(PaymentCateEnum.A003.name())
                        .sendJdeStatus(AptSyncJobStatus.DONE.getStatus())
                        .verifyStatus(AptPayVerifyStatus.VERIFIED)
                        .billPayModule(BillPayModule.KERRY)
                        .paymentInfoId(aptPaymentInfoMock1.getId()).build(),
                AptPay.builder().projectId("P2").buildingId("P2-B1").roomId("P2-B1-R1")
                        .payConfigId(CONFIG_ID_2).payType("支付宝支付").payAct("P6")
                        .payDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2021-01-08 10:11:12"))
                        .advanceAmount(BigDecimal.valueOf(random.nextInt(MAX_ADVANCE_AMOUNT)))
                        .paymentCate(PaymentCateEnum.A004.name())
                        .sendJdeStatus(AptSyncJobStatus.PART_COMPLETED.getStatus())
                        .verifyStatus(AptPayVerifyStatus.VERIFIED)
                        .billPayModule(BillPayModule.CASHIER)
                        .paymentInfoId(aptPaymentInfoMock2.getId()).build(),
                AptPay.builder().projectId("P2").buildingId("P2-B1").roomId("P2-B1-R2")
                        .payConfigId(CONFIG_ID_3).payType("现金").payAct("P7")
                        .payDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2021-02-11 10:11:12"))
                        .advanceAmount(BigDecimal.valueOf(random.nextInt(MAX_ADVANCE_AMOUNT)))
                        .paymentCate(PaymentCateEnum.A004.name())
                        .sendJdeStatus(AptSyncJobStatus.FAILED.getStatus())
                        .verifyStatus(AptPayVerifyStatus.VERIFIED)
                        .billPayModule(BillPayModule.CASHIER)
                        .paymentInfoId(aptPaymentInfoMock3.getId()).build(),
                AptPay.builder().projectId("P2").buildingId("P2-B2").roomId("P2-B2-R1")
                        .payConfigId(CONFIG_ID_3).payType("现金").payAct("P8")
                        .payDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2021-04-01 10:11:12"))
                        .advanceAmount(BigDecimal.valueOf(random.nextInt(MAX_ADVANCE_AMOUNT)))
                        .paymentCate(PaymentCateEnum.A004.name())
                        .sendJdeStatus(AptSyncJobStatus.DONE.getStatus())
                        .verifyStatus(AptPayVerifyStatus.VERIFIED)
                        .billPayModule(BillPayModule.CASHIER)
                        .paymentInfoId(aptPaymentInfoMock1.getId()).build(),
                AptPay.builder().projectId("P2").buildingId("P2-B2").roomId("P2-B2-R2")
                        .payConfigId(CONFIG_ID_3).payType("POS机").payAct("P9")
                        .payDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2021-03-01 10:11:12"))
                        .advanceAmount(BigDecimal.valueOf(random.nextInt(MAX_ADVANCE_AMOUNT)))
                        .paymentCate(PaymentCateEnum.A004.name())
                        .sendJdeStatus(AptSyncJobStatus.DONE.getStatus())
                        .verifyStatus(AptPayVerifyStatus.TO_BE_VERIFIED)
                        .billPayModule(BillPayModule.CASHIER)
                        .paymentInfoId(aptPaymentInfoMock2.getId()).build(),
                AptPay.builder().projectId("P2").buildingId("P2-B3").roomId("P2-B3-R1")
                        .payConfigId(CONFIG_ID_3).payType("POS机").payAct("P10")
                        .payDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2021-02-02 10:11:12"))
                        .advanceAmount(BigDecimal.valueOf(random.nextInt(MAX_ADVANCE_AMOUNT)))
                        .paymentCate(PaymentCateEnum.UNKNOWN.name())
                        .sendJdeStatus(AptSyncJobStatus.VERIFIED.getStatus())
                        .verifyStatus(AptPayVerifyStatus.TO_BE_VERIFIED)
                        .billPayModule(BillPayModule.CASHIER)
                        .paymentInfoId(aptPaymentInfoMock3.getId()).build(),
                AptPay.builder().projectId("P3").buildingId("P3-B1").roomId("P3-B1-R1")
                        .payConfigId(CONFIG_ID_3).payType("微信支付").payAct("P11")
                        .payDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2021-05-02 10:11:12"))
                        .advanceAmount(BigDecimal.valueOf(random.nextInt(MAX_ADVANCE_AMOUNT)))
                        .paymentCate(PaymentCateEnum.UNKNOWN.name())
                        .sendJdeStatus(AptSyncJobStatus.CREATED.getStatus())
                        .verifyStatus(AptPayVerifyStatus.TO_BE_VERIFIED)
                        .payChannel(BillPayChannel.OFFLINE)
                        .billPayModule(BillPayModule.CASHIER)
                        .paymentInfoId(aptPaymentInfoMock1.getId()).build()
        };
        aptPayRepository.saveAll(Lists.newArrayList(aptPayArr));
    }

    private void verify(ResponseEntity<RespWrapVo> response, List<String> expectedPayActList) {
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(RespCodeEnum.SUCCESS.getCode(), response.getBody().getCode());
        assertEquals(RespCodeEnum.SUCCESS.getMessage(), response.getBody().getMessage());

        Map<String, Object> map = (Map<String, Object>) response.getBody().getData();
        assertEquals(map.get("totalElements"), expectedPayActList.size());
        assertEquals(map.get("totalPages"), 1);

        Set<String> actualPayActList = new HashSet<>();
        List<Map<String, Object>> contentList = (List<Map<String, Object>>) map.get("content");
        contentList.forEach(stringObjectMap -> {
            actualPayActList.add(String.valueOf(stringObjectMap.get("payAct")));
        });
        assertTrue(expectedPayActList.stream().sorted().collect(Collectors.joining())
                .equals(actualPayActList.stream().sorted().collect(Collectors.joining())));
    }

    /**
     * 生成随机字符串
     */
    private String getRandomStr() {
        return random.nextLong() + StringUtils.EMPTY;
    }

    /**
     * 获取表中数据的起始id
     */
    private long getIdStart() {
        List<AptPay> aptPays = aptPayRepository.findAll();
        return aptPays.get(0).getId();
    }

    /**
     * 生成Header
     */
    private HttpHeaders getHeaders(String xUserJson) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set(LoginUtils.USER_HTTP_HEADER, xUserJson);
        return headers;
    }

    private ResponseEntity<RespWrapVo> call_search_api(String xUserJson, String urlParam) {
        // headers
        HttpHeaders headers = getHeaders(xUserJson);

        HttpEntity request = new HttpEntity<>(null, headers);
        String url = LOCAL_HOST_DOMAIN + serverPort + "/s/apt/pay/search" + urlParam;

        return restTemplate.exchange(url, HttpMethod.GET, request, RespWrapVo.class);
    }

    private void call_export_api(String xUserJson, String urlParam) {
        // headers
        HttpHeaders headers = getHeaders(xUserJson);

        HttpEntity request = new HttpEntity<>(null, headers);
        String url = LOCAL_HOST_DOMAIN + serverPort + "/s/apt/pay/export" + urlParam;

        restTemplate.exchange(url, HttpMethod.GET, request, Object.class);
    }

    private ResponseEntity<RespWrapVo> call_save_api(String xUserJson, AptPaySaveVo saveReqVo) {
        // headers
        HttpHeaders headers = getHeaders(xUserJson);

        // body
        HttpEntity<AptPaySaveVo> request = new HttpEntity<>(saveReqVo, headers);

        String url = LOCAL_HOST_DOMAIN + serverPort + "/s/apt/pay/save";
        return restTemplate.exchange(url, HttpMethod.POST, request, RespWrapVo.class);
    }

    private ResponseEntity<Integer> call_batchVerify_api(String xUserJson, AptPaySearchVo aptPaySearchVo) {
        // headers
        HttpHeaders headers = getHeaders(xUserJson);

        // body
        HttpEntity<AptPaySearchVo> request = new HttpEntity<>(aptPaySearchVo, headers);

        String url = LOCAL_HOST_DOMAIN + serverPort + "/s/apt/pay/batch_verify";
        return restTemplate.exchange(url, HttpMethod.POST, request, Integer.class);
    }

    private ResponseEntity<Boolean> call_switchVerifyById_api(String xUserJson, Long id) {
        // headers
        HttpHeaders headers = getHeaders(xUserJson);

        // body
        HttpEntity<AptPaySearchVo> request = new HttpEntity<>(null, headers);

        String url = LOCAL_HOST_DOMAIN + serverPort + String.format("/s/apt/pay/%s/switch_verify", id);
        return restTemplate.exchange(url, HttpMethod.POST, request, Boolean.class);
    }

    private ResponseEntity<RespWrapVo> call_verify_api(String xUserJson, Long id) {
        // headers
        HttpHeaders headers = getHeaders(xUserJson);

        // body
        HttpEntity<AptPaySearchVo> request = new HttpEntity<>(null, headers);

        String url = LOCAL_HOST_DOMAIN + serverPort + String.format("/s/apt/pay/offline/verify/%s", id);
        return restTemplate.exchange(url, HttpMethod.PUT, request, RespWrapVo.class);
    }

    private ResponseEntity<RespWrapVo> call_deleteOfflinePay_api(String xUserJson, Long id) {
        // headers
        HttpHeaders headers = getHeaders(xUserJson);

        // body
        HttpEntity<AptPaySearchVo> request = new HttpEntity<>(null, headers);

        String url = LOCAL_HOST_DOMAIN + serverPort + String.format("/s/apt/pay/delete/%s", id);
        return restTemplate.exchange(url, HttpMethod.DELETE, request, RespWrapVo.class);
    }

}
