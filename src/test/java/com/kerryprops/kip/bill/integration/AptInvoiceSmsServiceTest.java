package com.kerryprops.kip.bill.integration;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.dao.entity.InvoiceRecord;
import com.kerryprops.kip.bill.feign.entity.SendSmsV2Command;
import com.kerryprops.kip.bill.feign.entity.SmsResultVo;
import com.kerryprops.kip.bill.service.impl.AptInvoiceSmsService;
import com.kerryprops.kip.bill.webservice.vo.req.CallBackKerryInvoiceMainVo;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;

class AptInvoiceSmsServiceTest extends BaseIntegrationTest {

    @Autowired
    private AptInvoiceSmsService aptInvoiceSmsService;

    @Test
    void _01_sendSms_emailMatch() {
        InvoiceRecord record = InvoiceRecord.builder().email("<EMAIL>").build();
        CallBackKerryInvoiceMainVo mainVo = new CallBackKerryInvoiceMainVo();
        AptPaymentInfo aptPaymentInfo = new AptPaymentInfo();
        aptInvoiceSmsService.sendSms(aptPaymentInfo, record, mainVo, "abc,abc"
                , "32093202a,32093202a", null);
    }

    @Test
    void _02_sendSms_duplicatedSend() {
        Mockito.doReturn(new SmsResultVo()).when(messageClient)
                .sendSmsV2(ArgumentMatchers.any(SendSmsV2Command.class));

        InvoiceRecord record = InvoiceRecord.builder().email("13788991100").build();

        CallBackKerryInvoiceMainVo mainVo = new CallBackKerryInvoiceMainVo();
        mainVo.setInvoiceNo("32093202a");
        mainVo.setInvoiceCode("abc");

        AptPaymentInfo aptPaymentInfo = new AptPaymentInfo();
        aptInvoiceSmsService.sendSms(aptPaymentInfo, record, mainVo, "abc,abc"
                , "32093202a,32093202a", null);
    }

    // 旧数据中可能存在invoiceCodes,invoiceNo不一样的情况
    @Test
    void _03_sendSms_invokedInvoiceCodesOrNoLost() {
        Mockito.doReturn(new SmsResultVo()).when(messageClient)
                .sendSmsV2(ArgumentMatchers.any(SendSmsV2Command.class));

        InvoiceRecord record = InvoiceRecord.builder().email("13788991100").build();

        CallBackKerryInvoiceMainVo mainVo = new CallBackKerryInvoiceMainVo();
        mainVo.setInvoiceNo("32093202a");
        mainVo.setInvoiceCode("abc");

        AptPaymentInfo aptPaymentInfo = new AptPaymentInfo();
        aptInvoiceSmsService.sendSms(aptPaymentInfo, record, mainVo, "abc"
                , "32093202a,32093202a", null);
    }

    @Test
    void _04_sendSms_sendSuccuss() {
        Mockito.doReturn(new SmsResultVo()).when(messageClient)
                .sendSmsV2(ArgumentMatchers.any(SendSmsV2Command.class));

        InvoiceRecord record = InvoiceRecord.builder().email("13788991100").phone("17701890634").build();

        CallBackKerryInvoiceMainVo mainVo = new CallBackKerryInvoiceMainVo();
        mainVo.setInvoiceNo("32093202b");
        mainVo.setInvoiceCode("efg");

        AptPaymentInfo aptPaymentInfo = new AptPaymentInfo();
        aptPaymentInfo.setProjectId("192");
        aptPaymentInfo.setBuildingId("192-QHKC-R3");
        aptInvoiceSmsService.sendSms(aptPaymentInfo, record, mainVo, "abc"
                , "32093202a,32093202a", null);
    }

}
