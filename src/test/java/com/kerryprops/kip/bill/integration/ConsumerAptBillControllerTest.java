package com.kerryprops.kip.bill.integration;

import com.alibaba.fastjson.JSONObject;
import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillPushStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.common.enums.DirectDebitAgreementStatus;
import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.AptBillDirectDebitsAgreementRepository;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptBillDirectDebitsAgreement;
import com.kerryprops.kip.bill.feign.entity.LbsResp;
import com.kerryprops.kip.bill.feign.entity.TenantStaffResource;
import com.kerryprops.kip.bill.feign.entity.UserWithRoomsResponse;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.webservice.impl.ConsumerAptBillController;
import com.kerryprops.kip.bill.webservice.vo.resp.PayableStaffAptBillRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffAptBillRespVo;
import com.kerryprops.kip.hiveas.feign.dto.resp.BuildingRespDto;
import com.kerryprops.kip.hiveas.webservice.vo.resp.BuildingResponseVo;
import com.kerryprops.kip.hiveas.webservice.vo.resp.response.BuildingResponse;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.ArgumentMatchers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Random;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doReturn;

class ConsumerAptBillControllerTest extends BaseIntegrationTest {

    // 未被删除，即isDel标记位为false
    private final int SAVED = 0;

    private final String OBJ_BUILDING_ID = "TEST_BUILDING_ID";

    private final String OBJ_ROOM_ID = "TEST_ROOM_ID";

    private final String OBJ_PSP_NAME = "TEST_PSP_NAME";

    private final Random random = new Random();

    @Autowired
    private AptBillRepository billRepository;

    @Autowired
    private AptBillDirectDebitsAgreementRepository agreementRepository;

    @Autowired
    private ConsumerAptBillController consumerAptBillController;

    @BeforeEach
    void prepareTestData() {
        LoginUser loginUser = LoginUser.builder()
                .fromType("C")
                .cId(random.nextInt() + StringUtils.EMPTY)
                .build();
        UserInfoUtils.setUser(loginUser);
    }

    @Test
    void testNull() {
        RespWrapVo<UserWithRoomsResponse> respWrapVo = null;
        List<String> roomIds = consumerAptBillController.parseUserWithRoomResponse(respWrapVo);
        assertTrue(roomIds.isEmpty());
    }

    @Test
    void testResponse_DataIsNull() {
        RespWrapVo<UserWithRoomsResponse> respWrapVo = new RespWrapVo<>();
        respWrapVo.setData(null);
        List<String> roomIds = consumerAptBillController.parseUserWithRoomResponse(respWrapVo);
        assertTrue(roomIds.isEmpty());
    }

    @ParameterizedTest
    @CsvSource({
            "templates/consumer_apt_bill/UserWithRoomsResponse_NoRoom.json, 0",
            "templates/consumer_apt_bill/UserWithRoomsResponse_NullRoom.json, 0",
            "templates/consumer_apt_bill/UserWithRoomsResponse_AuthroizerIs0.json,0",
            "templates/consumer_apt_bill/UserWithRoomsResponse_AuthroizerIs2.json,1",
            "templates/consumer_apt_bill/UserWithRoomsResponse_AuthroizerIs3.json,0",
            "templates/consumer_apt_bill/UserWithRoomsResponse_NoRoomNumber.json,0",
            "templates/consumer_apt_bill/UserWithRoomsResponse_NullRoomNumber.json, 0",
            "templates/consumer_apt_bill/UserWithRoomsResponse_OK.json,1",
    })
    void test(String filepath, int expectedCount) throws URISyntaxException, IOException {
        String json = Files.readString(Path.of(Objects.requireNonNull(this.getClass().getClassLoader()
                .getResource(filepath)).toURI()));
        UserWithRoomsResponse userWithRoomsResponse = JSONObject.parseObject(json, UserWithRoomsResponse.class);

        RespWrapVo<UserWithRoomsResponse> respWrapVo = new RespWrapVo<>();
        respWrapVo.setData(userWithRoomsResponse);
        List<String> roomIds = consumerAptBillController.parseUserWithRoomResponse(respWrapVo);
        assertEquals(roomIds.size(), expectedCount);
    }

    @Test
    void _01_userBillInfoList_empty() {
        RespWrapVo<UserWithRoomsResponse> userWithRoomsRespWrapVo = generateUserWithRoomsRespWrap();
        doReturn(userWithRoomsRespWrapVo).when(cUserClient).userAssociateRooms(ArgumentMatchers.any());

        // 执行待验证部分
        Pageable pageable = PageRequest.of(0, 5);
        RespWrapVo<List<PayableStaffAptBillRespVo>> respWrapVo = consumerAptBillController
                .userBillInfoList(pageable, "NOT_EXIST_BUILDING_ID", "NOT_EXIST_ROOM_ID");

        // 验证执行情况
        assertTrue(respWrapVo.getData().isEmpty());
    }

    @Test
    void _02_userBillInfoList_success() {
        AptBill aptBill1 = generateRandomAptBill();
        aptBill1.setPaymentStatus(BillPaymentStatus.TO_BE_PAID);

        AptBill aptBill2 = generateRandomAptBill();
        aptBill2.setPaymentStatus(BillPaymentStatus.TO_BE_PAID);
        aptBill2.setYear(2023);
        aptBill2.setMonth(7);
        aptBill2.setBuildingId(OBJ_BUILDING_ID);
        aptBill2.setRoomId(OBJ_ROOM_ID);

        AptBill aptBill3 = generateRandomAptBill();
        aptBill3.setPaymentStatus(BillPaymentStatus.PART_PAID);
        aptBill3.setYear(2023);
        aptBill3.setMonth(8);
        aptBill3.setBuildingId(OBJ_BUILDING_ID);
        aptBill3.setRoomId(OBJ_ROOM_ID);

        AptBill aptBill4 = generateRandomAptBill();
        aptBill4.setPaymentStatus(BillPaymentStatus.PART_PAID);
        billRepository.saveAll(List.of(aptBill1, aptBill2, aptBill3, aptBill4));

        TenantStaffResource tenantStaffResource3 = generateTenantStaffResource();
        tenantStaffResource3.setRoomNumber(aptBill4.getRoomId());
        List<TenantStaffResource> tenantStaffResources = new ArrayList<>(generateTenantStaffResources());
        tenantStaffResources.add(tenantStaffResource3);

        RespWrapVo<UserWithRoomsResponse> userWithRoomsRespWrapVo = generateUserWithRoomsRespWrap();
        userWithRoomsRespWrapVo.getData().setRooms(tenantStaffResources);
        doReturn(userWithRoomsRespWrapVo).when(cUserClient).userAssociateRooms(ArgumentMatchers.any());

        AptBillDirectDebitsAgreement aptBillDirectDebitsAgreement1
                = generateDirectDebitsAgreement();
        AptBillDirectDebitsAgreement aptBillDirectDebitsAgreement2
                = generateDirectDebitsAgreement();
        agreementRepository.saveAll(List.of(aptBillDirectDebitsAgreement1, aptBillDirectDebitsAgreement2));

        // 执行待验证部分
        Pageable pageable = PageRequest.of(0, 5);
        RespWrapVo<List<PayableStaffAptBillRespVo>> respWrapVo
                = consumerAptBillController.userBillInfoList(pageable, OBJ_BUILDING_ID, OBJ_ROOM_ID);

        // 验证执行情况
        List<PayableStaffAptBillRespVo> payableStaffAptBillRespVos = respWrapVo.getData();
        assertEquals(2, payableStaffAptBillRespVos.size());

        PayableStaffAptBillRespVo payableStaffAptBillRespVo1 = payableStaffAptBillRespVos.get(0);
        verifyStaffAptBillResp(aptBill2, payableStaffAptBillRespVo1);
        verifyPayableStaffAptBillResp(payableStaffAptBillRespVo1);

        PayableStaffAptBillRespVo payableStaffAptBillRespVo2 = payableStaffAptBillRespVos.get(1);
        verifyStaffAptBillResp(aptBill3, payableStaffAptBillRespVo2);
        verifyPayableStaffAptBillResp(payableStaffAptBillRespVo2);
    }

    /**
     * 查询lbs下待缴费账单，异常场景：捕获到异常
     */
    @Test
    void _03_getLbsPayableBill_exception() {
        // 构造空指针异常
        UserInfoUtils.setUser((LoginUser) null);

        // 执行待验证部分
        Pageable pageable = PageRequest.of(0, 5);
        RespWrapVo<List<StaffAptBillRespVo>> respWrapVo
                = consumerAptBillController.getLbsPayableBill(pageable, StringUtils.EMPTY);

        // 验证执行情况
        assertEquals(RespCodeEnum.SUCCESS.getCode(), respWrapVo.getCode());
        assertEquals(0, respWrapVo.getData().size());
    }

    @Test
    void _04_getLbsPayableBill_success() {
        String OBJ_BUILDING_ID_V2 = "TEST_BUILDING_ID_V2";

        AptBill aptBill = generateRandomAptBill();
        aptBill.setPaymentStatus(BillPaymentStatus.PART_PAID);
        aptBill.setYear(2023);
        aptBill.setMonth(9);
        aptBill.setBuildingId(OBJ_BUILDING_ID_V2);
        aptBill.setRoomId(OBJ_ROOM_ID);
        billRepository.save(aptBill);

        RespWrapVo<UserWithRoomsResponse> userWithRoomsRespWrapVo = generateUserWithRoomsRespWrap();
        doReturn(userWithRoomsRespWrapVo).when(cUserClient).userAssociateRooms(ArgumentMatchers.any());

        BuildingRespDto building1 = new BuildingRespDto();
        building1.setId(OBJ_BUILDING_ID);
        BuildingResponse buildingResponse1 = BuildingResponse.builder().building(building1).build();

        BuildingRespDto building2 = new BuildingRespDto();
        building2.setId(OBJ_BUILDING_ID_V2);
        BuildingResponse buildingResponse2 = BuildingResponse.builder().building(building2).build();

        List<BuildingResponse> buildingResponses = List.of(buildingResponse1, buildingResponse2);
        LbsResp lbsResp = LbsResp.builder().buildings(buildingResponses).build();

        BuildingResponseVo buildingResponseVo1 = BuildingResponseVo.builder().building(building1).build();
        BuildingResponseVo buildingResponseVo2 = BuildingResponseVo.builder().building(building2).build();
        doReturn(List.of(buildingResponseVo1, buildingResponseVo2))
                .when(hiveAsClient).getLbs(ArgumentMatchers.anyString());

        // 执行待验证部分
        Pageable pageable = PageRequest.of(0, 5);
        RespWrapVo<List<StaffAptBillRespVo>> respWrapVo
                = consumerAptBillController.getLbsPayableBill(pageable, random.nextInt() + StringUtils.EMPTY);

        // 验证执行情况
        List<StaffAptBillRespVo> staffAptBillRespVos = respWrapVo.getData();
        assertEquals(3, staffAptBillRespVos.size());

        StaffAptBillRespVo objStaffAptBillRespVo = staffAptBillRespVos.get(2);
        verifyStaffAptBillResp(aptBill, objStaffAptBillRespVo);
    }

    @Test
    void _05_historyBill_success() {
        final String PAYMENT_RESULT_JDE = "JDE核销";

        AptBill aptBill1 = generateRandomAptBill();
        aptBill1.setPaymentStatus(BillPaymentStatus.PAID);
        aptBill1.setYear(2023);
        aptBill1.setMonth(10);
        aptBill1.setBuildingId(OBJ_BUILDING_ID);
        aptBill1.setRoomId(OBJ_ROOM_ID);
        aptBill1.setPaymentResult(PAYMENT_RESULT_JDE);

        AptBill aptBill2 = generateRandomAptBill();
        aptBill2.setPaymentStatus(BillPaymentStatus.DIRECT_DEBIT_PAID);
        aptBill2.setYear(2023);
        aptBill2.setMonth(11);
        aptBill2.setBuildingId(OBJ_BUILDING_ID);
        aptBill2.setRoomId(OBJ_ROOM_ID);
        aptBill2.setPaymentResult(PAYMENT_RESULT_JDE);

        billRepository.saveAll(List.of(aptBill1, aptBill2));

        RespWrapVo<UserWithRoomsResponse> userWithRoomsRespWrapVo = generateUserWithRoomsRespWrap();
        doReturn(userWithRoomsRespWrapVo).when(cUserClient).userAssociateRooms(ArgumentMatchers.any());

        // 执行待验证部分
        Pageable pageable = PageRequest.of(0, 5);
        RespWrapVo<List<StaffAptBillRespVo>> respWrapVo
                = consumerAptBillController.historyBill(pageable, OBJ_BUILDING_ID, OBJ_ROOM_ID);

        // 验证执行情况
        List<StaffAptBillRespVo> staffAptBillRespVos = respWrapVo.getData();
        assertEquals(2, staffAptBillRespVos.size());

        final String PAYMENT_RESULT_DISPLAY = "线下冲销";

        StaffAptBillRespVo staffAptBillRespVo1 = staffAptBillRespVos.get(0);
        verifyStaffAptBillResp(aptBill1, staffAptBillRespVo1);
        assertEquals(PAYMENT_RESULT_DISPLAY, staffAptBillRespVo1.getPaymentResult());

        StaffAptBillRespVo staffAptBillRespVo2 = staffAptBillRespVos.get(1);
        verifyStaffAptBillResp(aptBill2, staffAptBillRespVo2);
        assertEquals(PAYMENT_RESULT_DISPLAY, staffAptBillRespVo2.getPaymentResult());
    }

    private AptBill generateRandomAptBill() {
        return AptBill.builder()
                .billNo(random.nextInt() + StringUtils.EMPTY)
                .bu(random.nextInt() + StringUtils.EMPTY)
                .an8(random.nextInt() + StringUtils.EMPTY)
                .alph(random.nextInt() + StringUtils.EMPTY)
                .rdGlc(random.nextInt() + StringUtils.EMPTY)
                .category(random.nextInt() + StringUtils.EMPTY)
                .doco(random.nextInt() + StringUtils.EMPTY)
                .projectId(random.nextInt() + StringUtils.EMPTY)
                .buildingId(random.nextInt() + StringUtils.EMPTY)
                .roomId(random.nextInt() + StringUtils.EMPTY)
                .status(BillStatus.TO_BE_PAID)
                .paymentStatus(BillPaymentStatus.TO_BE_PAID)
                .pushStatus(BillPushStatus.TO_BE_PUSHED)
                .unit(random.nextInt() + StringUtils.EMPTY)
                .amt(random.nextDouble())
                .deletedAt(SAVED)
                .build();
    }

    private AptBillDirectDebitsAgreement generateDirectDebitsAgreement() {
        AptBillDirectDebitsAgreement aptBillDirectDebitsAgreement = new AptBillDirectDebitsAgreement();
        aptBillDirectDebitsAgreement.setRoomId(OBJ_ROOM_ID);
        aptBillDirectDebitsAgreement.setPspName(OBJ_PSP_NAME);
        aptBillDirectDebitsAgreement.setIsDel(SAVED);
        aptBillDirectDebitsAgreement.setAgreementStatus(DirectDebitAgreementStatus.ACTIVE);
        return aptBillDirectDebitsAgreement;
    }

    private TenantStaffResource generateTenantStaffResource() {
        final int AUTHORIZER_BOARDER = 1; // 授权用户
        final int AUTHORIZER_HOUSE_HOLDER = 2; // 业主

        TenantStaffResource tenantStaffResource = new TenantStaffResource();
        tenantStaffResource.setAuthorizer(random.nextBoolean() ? AUTHORIZER_BOARDER : AUTHORIZER_HOUSE_HOLDER);
        tenantStaffResource.setRoomNumber(OBJ_ROOM_ID);
        return tenantStaffResource;
    }

    private List<TenantStaffResource> generateTenantStaffResources() {
        return List.of(generateTenantStaffResource(), generateTenantStaffResource());
    }

    private RespWrapVo<UserWithRoomsResponse> generateUserWithRoomsRespWrap() {
        List<TenantStaffResource> tenantStaffResources = generateTenantStaffResources();

        UserWithRoomsResponse userWithRoomsResponse = new UserWithRoomsResponse();
        userWithRoomsResponse.setRooms(tenantStaffResources);
        return new RespWrapVo<>(userWithRoomsResponse);
    }

    private void verifyStaffAptBillResp(AptBill expectAptBillInfo, StaffAptBillRespVo actualAptBillRespVo) {
        assertEquals(expectAptBillInfo.getBillNo(), actualAptBillRespVo.getBillNo());
        assertEquals(expectAptBillInfo.getBu(), actualAptBillRespVo.getBu());
        assertEquals(expectAptBillInfo.getAn8(), actualAptBillRespVo.getAn8());
        assertEquals(expectAptBillInfo.getAlph(), actualAptBillRespVo.getAlph());
        assertEquals(expectAptBillInfo.getRdGlc(), actualAptBillRespVo.getRdGlc());
        assertEquals(expectAptBillInfo.getBuildingId(), actualAptBillRespVo.getBuildingId());
        assertEquals(expectAptBillInfo.getRoomId(), actualAptBillRespVo.getRoomId());
        assertEquals(expectAptBillInfo.getBillNo(), actualAptBillRespVo.getBillNo());
        assertEquals(expectAptBillInfo.getPaymentStatus(), actualAptBillRespVo.getPaymentStatus());
        assertEquals(expectAptBillInfo.getYear(), actualAptBillRespVo.getYear());
        assertEquals(expectAptBillInfo.getMonth(), actualAptBillRespVo.getMonth());
    }

    private void verifyPayableStaffAptBillResp(PayableStaffAptBillRespVo actualPayableStaffAptBillRespVo) {
        assertTrue(actualPayableStaffAptBillRespVo.isSignedDirectDebits());
        assertFalse(actualPayableStaffAptBillRespVo.isDirectDebitsSignatory());
        assertTrue(actualPayableStaffAptBillRespVo.isDuplicateSigned());
        assertEquals(OBJ_PSP_NAME, actualPayableStaffAptBillRespVo.getSignedPsp());
    }

}
