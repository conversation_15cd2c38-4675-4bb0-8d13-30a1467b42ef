package com.kerryprops.kip.bill.integration;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.constants.BillConstants;
import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.BillRepository;
import com.kerryprops.kip.bill.dao.entity.BillEntity;
import com.kerryprops.kip.bill.webservice.vo.resp.BillPayer;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.mockito.ArgumentMatchers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doReturn;

class BizBillControllerTest extends BaseIntegrationTest {

    private static final String OK_B_ACCOUNT = "********";

    private static final String OK_X_USER = "{\"userId\":1012,\"fromType\":\"B\",\"loginAccount\":\"********\"," +
            "\"nickName\":\"%E8%91%A3%E9%9D%99%E8%8C%B9\",\"phoneNumber\":\"***********\"," +
            "\"companyIds\":\"4028e3817c7229a7017c722e870e0000,,8aaa82257cb6de27017cb6e723aa0000\"," +
            "\"projectIds\":\"HKC,TKC\",\"buildingIds\":\"HKC-T1,HKC-R1,BKC-N1,TKC-R\",\"source\":\"web\"}";

    private static final String NG_X_USER = "{\"userId\":1012,\"fromType\":\"B\",\"loginAccount\":\"********\"," +
            "\"nickName\":\"%E8%91%A3%E9%9D%99%E8%8C%B9\",\"phoneNumber\":\"***********\"," +
            "\"companyIds\":\"4028e3817c7229a7017c722e870e0000,,8aaa82257cb6de27017cb6e723aa0000\"," +
            "\"projectIds\":\"HKC,TKC\",\"buildingIds\":\"HKC-T1,HKC-R1,BKC-N1,TKC-R\",\"source\":\"web\"}";

    private static final String ADMIN_X_USER = "{\"nickName\":\"SuperAdmin\",\"roles\":\"SUPER_ADMIN\",\"staff\":true," +
            "\"userId\":123,\"uniqueUserId\":\"123\",\"phoneNumber\":\"***********\",\"fromType\":\"S\",\"client\":false," +
            "\"superAdmin\":true,\"tenant\":false}";

    private static final String C_X_USER = "{\"cId\":\"123455\",\"client\":true,\"companyIds\":\"015\",\"fromType\":\"C\"," +
            "\"nickName\":\"David\",\"phoneNumber\":\"***********\",\"roles\":\"OFFICE_AUTHORIZER,APARTMENT_AUTHORIZER\"," +
            "\"staff\":false,\"superAdmin\":false,\"tenant\":false,\"uniqueUserId\":\"123455\"}";

    private static final String B_X_USER = "{\"companyIds\":null,\"nickName\":\"jane.dong\",\"staff\":false," +
            "\"userId\":1012,\"uniqueUserId\":\"26\",\"phoneNumber\":\"13764912345\",\"fromType\":\"B\",\"client\":false," +
            "\"superAdmin\":false,\"tenant\":true}";

    private final String OK_DOCO_1 = "271992";

    private final String OK_DOCO_2 = "654321";

    private final String NG_DOCO = "123456";

    private final String OK_AN8_1 = "20152474";

    private final String OK_AN8_2 = "********";

    private final String NG_AN8 = "********";

    private final int OK_TP_STATUS = 5;

    private final int NG_TP_STATUS = 1;

    @Autowired
    private BillRepository billRepository;

    private List<BillEntity> billEntities;

    @BeforeEach
    void prepareData() {
        //precondition - mock data
        BillEntity[] requestBillEntities = {
                buildBillEntity(NG_DOCO, "DEV公司", OK_AN8_1, OK_TP_STATUS, 22, 1), //NG - illegal doco

                buildBillEntity(OK_DOCO_1, "TEST公司", NG_AN8, OK_TP_STATUS, 22, 1),   //NG - illegal an8
                buildBillEntity(OK_DOCO_1, "DEV公司", OK_AN8_1, NG_TP_STATUS, 22, 1), //NG - illegal status
                buildBillEntity(OK_DOCO_1, "DEV公司", OK_AN8_1, OK_TP_STATUS, 23, 1), //NG - illegal tpFyr
                buildBillEntity(OK_DOCO_1, "DEV公司", OK_AN8_1, OK_TP_STATUS, 18, 1), //NG - illegal tpPn
                buildBillEntity(OK_DOCO_1, "DEV公司", OK_AN8_1, OK_TP_STATUS, 22, 1), //OK
                buildBillEntity(OK_DOCO_1, "DEV_TEST", OK_AN8_2, OK_TP_STATUS, 22, 1), //OK

                buildBillEntity(OK_DOCO_2, "TEST公司", NG_AN8, OK_TP_STATUS, 22, 1),   //NG - illegal an8
                buildBillEntity(OK_DOCO_2, "DEV公司", OK_AN8_1, NG_TP_STATUS, 22, 1), //NG - illegal status
                buildBillEntity(OK_DOCO_2, "DEV公司", OK_AN8_1, OK_TP_STATUS, 23, 1), //NG - illegal tpFyr
                buildBillEntity(OK_DOCO_2, "DEV公司", OK_AN8_1, OK_TP_STATUS, 18, 1), //NG - illegal tpPn
                buildBillEntity(OK_DOCO_2, "DEV公司", OK_AN8_1, OK_TP_STATUS, 22, 1), //OK
                buildBillEntity(OK_DOCO_2, "DEV_TEST", OK_AN8_2, OK_TP_STATUS, 22, 1), //OK
        };
        billEntities = billRepository.saveAll(Arrays.asList(requestBillEntities));
    }

    @AfterEach
    void clearTestData() {
        billRepository.deleteAll(billEntities);
    }

    @ParameterizedTest
    @NullAndEmptySource
    void _01_userInfoList_null_empty_xUser(String xUserJson) {
        ResponseEntity<RespWrapVo> resp = call_bill_list_api(xUserJson, StringUtils.EMPTY);
        assertEquals(HttpStatus.OK, resp.getStatusCode());
        assertNull(resp.getBody());
    }

    @Test
    void _02_userInfoList_wrong_fromType_S() {
        ResponseEntity<RespWrapVo> resp = call_bill_list_api(ADMIN_X_USER, StringUtils.EMPTY);
        assertEquals(HttpStatus.OK, resp.getStatusCode());
        assertNull(resp.getBody());
    }

    @Test
    void _03_userInfoList_wrong_fromType_C() {
        ResponseEntity<RespWrapVo> resp = call_bill_list_api(C_X_USER, StringUtils.EMPTY);
        assertEquals(HttpStatus.OK, resp.getStatusCode());
        assertNull(resp.getBody());
    }

    @Test
    void _04_userInfoList_xuser_has_no_companyIds() {
        ResponseEntity<RespWrapVo> resp = call_bill_list_api(B_X_USER, StringUtils.EMPTY);
        assertEquals(HttpStatus.OK, resp.getStatusCode());

        assertEquals(RespCodeEnum.AN8_ERROR.getCode(), resp.getBody().getCode());
        assertEquals(RespCodeEnum.AN8_ERROR.getMessage(), resp.getBody().getMessage());
    }

    @Test
    void _05_userInfoList_queryBillpayers_return_null() {
        //precondition
        doReturn(null).when(billSendConfigService).queryBillPayers(ArgumentMatchers.anyString());

        //execute
        ResponseEntity<RespWrapVo> resp = call_bill_list_api(NG_X_USER, StringUtils.EMPTY);

        //verification
        assertEquals(HttpStatus.OK, resp.getStatusCode());
        assertEquals(RespCodeEnum.AN8_ERROR.getCode(), resp.getBody().getCode());
        assertEquals(RespCodeEnum.AN8_ERROR.getMessage(), resp.getBody().getMessage());
    }

    @Test
    void _06_userInfoList_queryBillpayers_return_empty() {
        //precondition
        doReturn(Collections.EMPTY_LIST).when(billSendConfigService).queryBillPayers(ArgumentMatchers.anyString());

        //execute
        ResponseEntity<RespWrapVo> resp = call_bill_list_api(NG_X_USER, StringUtils.EMPTY);

        //verification
        assertEquals(HttpStatus.OK, resp.getStatusCode());
        assertEquals(RespCodeEnum.AN8_ERROR.getCode(), resp.getBody().getCode());
        assertEquals(RespCodeEnum.AN8_ERROR.getMessage(), resp.getBody().getMessage());
    }

    @Test
    void _07_userInfoList_querydocos_return_null() {
        //precondition
        List<BillPayer> billPayers = List.of(
                BillPayer.builder().tpAlph("DEV公司").tpAn8(OK_AN8_1).build(),
                BillPayer.builder().tpAlph("DEV_TEST").tpAn8(OK_AN8_2).build()
        );
        doReturn(billPayers).when(billSendConfigService).queryBillPayers(ArgumentMatchers.anyString());
        doReturn(null).when(billSendConfigService).queryDocos(ArgumentMatchers.anyString());

        //execute
        ResponseEntity<RespWrapVo> resp = call_bill_list_api(NG_X_USER, StringUtils.EMPTY);

        //verification
        assertEquals(HttpStatus.OK, resp.getStatusCode());
        assertEquals(RespCodeEnum.UNKNOWN_ERROR.getCode(), resp.getBody().getCode());
        assertEquals("Tenant is not binding contract", resp.getBody().getMessage());
    }

    @Test
    void _08_userInfoList_queryDocos_return_empty() {
        //precondition
        List<BillPayer> billPayers = List.of(
                BillPayer.builder().tpAlph("DEV公司").tpAn8(OK_AN8_1).build(),
                BillPayer.builder().tpAlph("DEV_TEST").tpAn8(OK_AN8_2).build()
        );
        doReturn(billPayers).when(billSendConfigService).queryBillPayers(ArgumentMatchers.anyString());
        doReturn(Collections.EMPTY_LIST).when(billSendConfigService).queryDocos(ArgumentMatchers.anyString());

        //execute
        ResponseEntity<RespWrapVo> resp = call_bill_list_api(NG_X_USER, StringUtils.EMPTY);

        //verification
        assertEquals(HttpStatus.OK, resp.getStatusCode());
        assertEquals(RespCodeEnum.UNKNOWN_ERROR.getCode(), resp.getBody().getCode());
        assertEquals("Tenant is not binding contract", resp.getBody().getMessage());
    }

    @ParameterizedTest
    @CsvSource({
            "271992, ,2, 271992, ********;20152474",
            "271992;654321,,4, 271992;654321, ********;20152474",
            "271992;654321,&tpAn8s=20152474,2, 271992;654321, 20152474",
            "271992;654321,&tpAlphs=DEV_TEST,2, 271992;654321, ********",
            "654321,&tpAlphs=DEV_TEST&tpAn8s=********,1, 654321, ********",

            //多选择
            "271992;654321,&tpAn8s=20152474&tpAn8s=********,4, 271992;654321, ********;20152474",
            "271992;654321,&tpAn8s=20152474&tpAn8s=********&tpAn8s=********,4, 271992;654321, ********;20152474",
            "271992;654321,&tpAn8s=20152474&tpAn8s=********,2, 271992;654321, 20152474",
            "271992;654321,&tpAn8s=a********,0, , ",

            "271992;654321,&tpAlphs=DEV_TEST&tpAlphs=DEV公司,4, 271992;654321, ********;20152474",
            "271992;654321,&tpAlphs=DEV_TEST&tpAlphs=DEV公司&tpAlphs=TEST公司,4, 271992;654321, ********;20152474",
            "271992;654321,&tpAlphs=DEV_TEST&tpAlphs=TEST公司,2, 271992;654321, ********",
            "271992;654321,&tpAlphs=abc,0, , ",

            "271992;654321,&tpAlphs=DEV_TEST&tpAn8s=********&tpAlphs=DEV公司&tpAn8s=20152474,4, 271992;654321, ********;20152474",
            "271992;654321,&tpAlphs=DEV_TEST&tpAn8s=********&tpAlphs=DEV公司&tpAn8s=20152474&tpAlphs=abc&tpAn8s=********,4, 271992;654321, ********;20152474",
            "271992;654321,&tpAlphs=DEV_TEST&tpAn8s=********&tpAlphs=abc&tpAn8s=********,2, 271992;654321, ********",
            "271992;654321,&tpAlphs=abc&tpAn8s=********,0, , ",
    })
    void _09_userBillInfoList_verify_response(String searchDocos, String filter, int totalElements,
                                              String expectDocos, String expectAn8s) {
        //precondition
        List<BillPayer> billPayers = List.of(
                BillPayer.builder().tpAlph("DEV公司").tpAn8(OK_AN8_1).build(),
                BillPayer.builder().tpAlph("DEV_TEST").tpAn8(OK_AN8_2).build());
        doReturn(billPayers).when(billSendConfigService).queryBillPayers(OK_B_ACCOUNT);
        doReturn(Stream.of(searchDocos.split(";")).collect(Collectors.toList())).when(billSendConfigService).queryDocos(OK_B_ACCOUNT);

        expectAn8s = StringUtils.isEmpty(expectAn8s) ? StringUtils.EMPTY : expectAn8s;
        expectDocos = StringUtils.isEmpty(expectDocos) ? StringUtils.EMPTY : expectDocos;

        //execute
        ResponseEntity<RespWrapVo> resp = call_bill_list_api(OK_X_USER, filter);

        //verification
        assertEquals(HttpStatus.OK, resp.getStatusCode());
        assertEquals(RespCodeEnum.SUCCESS.getCode(), resp.getBody().getCode());
        assertEquals(RespCodeEnum.SUCCESS.getMessage(), resp.getBody().getMessage());

        Map<String, Object> map = (Map<String, Object>) resp.getBody().getData();
        assertEquals(map.get("totalElements"), totalElements);
        assertEquals(map.get("totalPages"), totalElements == 0 ? 0 : 1);

        Set<String> docoList = new TreeSet<>();
        Set<String> an8List = new TreeSet<>();
        List<Map<String, Object>> contentList = (List<Map<String, Object>>) map.get("content");
        contentList.forEach(stringObjectMap -> {
            docoList.add(String.valueOf(stringObjectMap.get("tpDoco")));
            an8List.add(String.valueOf(stringObjectMap.get("tpAn8")));
        });
        String actualDocoStr = docoList.stream().sorted().collect(Collectors.joining(";"));
        String actualAn8Str = an8List.stream().sorted().collect(Collectors.joining(";"));
        assertTrue(expectDocos.equalsIgnoreCase(actualDocoStr));
        assertTrue(expectAn8s.equalsIgnoreCase(actualAn8Str));
    }

    @ParameterizedTest
    @NullAndEmptySource
    void _20_getInfo_null_empty_xUser(String xUserJson) {
        ResponseEntity<RespWrapVo> resp = call_detail_api(xUserJson, 1);
        assertEquals(HttpStatus.OK, resp.getStatusCode());
        assertNull(resp.getBody());
    }

    @Test
    void _21_getInfo_wrong_fromType_S() {
        ResponseEntity<RespWrapVo> resp = call_detail_api(ADMIN_X_USER, 1);
        assertEquals(HttpStatus.OK, resp.getStatusCode());
        assertNull(resp.getBody());
    }

    @Test
    void _22_getInfo_wrong_fromType_C() {
        ResponseEntity<RespWrapVo> resp = call_detail_api(C_X_USER, 1);
        assertEquals(HttpStatus.OK, resp.getStatusCode());
        assertNull(resp.getBody());
    }

    @Test
    void _23_getInfo_wrong_xUser_has_no_companyId() {
        ResponseEntity<RespWrapVo> resp = call_detail_api(B_X_USER, 1);
        assertEquals(HttpStatus.OK, resp.getStatusCode());

        RespWrapVo respWrapVo = resp.getBody();
        Assertions.assertEquals(RespCodeEnum.AN8_ERROR.getCode(), respWrapVo.getCode());
        Assertions.assertEquals(RespCodeEnum.AN8_ERROR.getMessage(), respWrapVo.getMessage());
    }

    @Test
    void _24_getInfo_notFound() {
        //precondition
        List<BillPayer> billPayers = List.of(
                BillPayer.builder().tpAlph("DEV公司").tpAn8(OK_AN8_1).build(),
                BillPayer.builder().tpAlph("DEV_TEST").tpAn8(OK_AN8_2).build()
        );
        doReturn(billPayers).when(billSendConfigService).queryBillPayers(OK_B_ACCOUNT);
        doReturn(List.of(OK_DOCO_1, OK_DOCO_2)).when(billSendConfigService).queryDocos(OK_B_ACCOUNT);
        BillEntity billEntity = billEntities.stream().max(Comparator.comparing(BillEntity::getId)).get();
        long maxId = billEntity.getId() + 100;

        //execute
        ResponseEntity<RespWrapVo> resp = call_detail_api(OK_X_USER, (int) maxId);

        //verification
        assertEquals(HttpStatus.OK, resp.getStatusCode());
        assertEquals(RespCodeEnum.SUCCESS.getCode(), resp.getBody().getCode());
        assertEquals(RespCodeEnum.SUCCESS.getMessage(), resp.getBody().getMessage());
        assertNull(resp.getBody().getData());
    }

    @ParameterizedTest
    @CsvSource({
            "6, 271992, ********",
            "12, 654321, ********",
    })
    void _25_getInfo_found(int billIndex, String expectDoco, String expectAn8) {
        //precondition
        List<BillPayer> billPayers = List.of(
                BillPayer.builder().tpAlph("DEV公司").tpAn8(OK_AN8_1).build(),
                BillPayer.builder().tpAlph("DEV_TEST").tpAn8(OK_AN8_2).build()
        );
        doReturn(billPayers).when(billSendConfigService).queryBillPayers(OK_B_ACCOUNT);
        doReturn(List.of(OK_DOCO_1, OK_DOCO_2)).when(billSendConfigService).queryDocos(OK_B_ACCOUNT);

        //execute
        BillEntity oldBillEntity = billEntities.get(billIndex);
        long billId = oldBillEntity.getId();
        ResponseEntity<RespWrapVo> resp = call_detail_api(OK_X_USER, billId);

        //verification
        assertEquals(HttpStatus.OK, resp.getStatusCode());
        assertEquals(RespCodeEnum.SUCCESS.getCode(), resp.getBody().getCode());
        assertEquals(RespCodeEnum.SUCCESS.getMessage(), resp.getBody().getMessage());

        Map<String, Object> map = (Map<String, Object>) resp.getBody().getData();
        assertEquals(map.get("id"), (int) billId);
        assertEquals(map.get("tpDoco"), expectDoco);
        assertEquals(map.get("tpAn8"), expectAn8);

        BillEntity billEntity = billRepository.findById(billId).get();
        assertEquals(billEntity.getReadStatus(), 1);
    }

    @ParameterizedTest
    @NullAndEmptySource
    void _30_queryBillPayers_null_empty_xUser(String xUserJson) {
        ResponseEntity<RespWrapVo> resp = call_queryBillPayers_api(xUserJson);
        assertEquals(HttpStatus.OK, resp.getStatusCode());
        assertNull(resp.getBody());
    }

    @Test
    void _31_getInfoqueryBillPayers_wrong_fromType_S() {
        ResponseEntity<RespWrapVo> resp = call_queryBillPayers_api(ADMIN_X_USER);
        assertEquals(HttpStatus.OK, resp.getStatusCode());
        assertNull(resp.getBody());
    }

    @Test
    void _32_queryBillPayers_wrong_fromType_C() {
        ResponseEntity<RespWrapVo> resp = call_queryBillPayers_api(C_X_USER);
        assertEquals(HttpStatus.OK, resp.getStatusCode());
        assertNull(resp.getBody());
    }

    @Test
    void _33_queryBillPayers_wrong_xUser_has_no_companyId() {
        ResponseEntity<RespWrapVo> resp = call_queryBillPayers_api(B_X_USER);
        assertEquals(HttpStatus.OK, resp.getStatusCode());

        RespWrapVo<List<String>> respWrapVo = (RespWrapVo<List<String>>) resp.getBody();
        Assertions.assertEquals(RespCodeEnum.SUCCESS.getCode(), respWrapVo.getCode());
        Assertions.assertEquals(RespCodeEnum.SUCCESS.getMessage(), respWrapVo.getMessage());
    }

    @Test
    void _34_queryBillPayers_empty_data() {
        doReturn(Collections.EMPTY_LIST).when(billSendConfigService).queryBillPayers(OK_B_ACCOUNT);

        ResponseEntity<RespWrapVo> resp = call_queryBillPayers_api(OK_X_USER);
        RespWrapVo<List<String>> respWrapVo = (RespWrapVo<List<String>>) resp.getBody();

        Assertions.assertEquals(RespCodeEnum.SUCCESS.getCode(), respWrapVo.getCode());
        Assertions.assertEquals(RespCodeEnum.SUCCESS.getMessage(), respWrapVo.getMessage());
        Assertions.assertTrue(CollectionUtils.isEmpty(respWrapVo.getData()));
    }

    @Test
    void _35_queryBillPayers_data_found() {
        //precondition
        List<BillPayer> billPayers = List.of(
                BillPayer.builder().tpAlph("DEV公司").tpAn8(OK_AN8_1).build(),
                BillPayer.builder().tpAlph("DEV_TEST").tpAn8(OK_AN8_2).build()
        );
        doReturn(billPayers).when(billSendConfigService).queryBillPayers(OK_B_ACCOUNT);

        doReturn(List.of(OK_DOCO_1, OK_DOCO_2)).when(billSendConfigService).queryDocos(OK_B_ACCOUNT);

        //execute
        ResponseEntity<RespWrapVo> resp = call_queryBillPayers_api(OK_X_USER);
        RespWrapVo<List<String>> respWrapVo = (RespWrapVo<List<String>>) resp.getBody();
        List<String> actualBillPayers = respWrapVo.getData();

        //verify
        List<String> expectBillPayers = List.of("DEV公司" + "-" + OK_AN8_1, "DEV_TEST" + "-" + OK_AN8_2);

        Assertions.assertEquals(RespCodeEnum.SUCCESS.getCode(), respWrapVo.getCode());
        Assertions.assertEquals(RespCodeEnum.SUCCESS.getMessage(), respWrapVo.getMessage());
        Assertions.assertEquals(
                expectBillPayers.stream().sorted().collect(Collectors.joining(",")),
                actualBillPayers.stream().sorted().collect(Collectors.joining(",")));
    }

    private ResponseEntity<RespWrapVo> call_bill_list_api(String xUserJson, String filter) {
        // headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("x-user", xUserJson);

        HttpEntity request = new HttpEntity<>(null, headers);

        String path = "/b/bill/userBillInfoList?tpFyrEnd=22&tpFyrStart=18&tpPnEnd=12&tpPnStart=2";
        String url = "http://localhost:" + serverPort + path + (StringUtils.isNotEmpty(filter) ? filter : StringUtils.EMPTY);

        return restTemplate.exchange(url, HttpMethod.GET, request, RespWrapVo.class);
    }


    private BillEntity buildBillEntity(String doco, String alph, String an8, int tpStatus, int tpFyr, int tpPn) {
        BillEntity entity = BillEntity.builder()
                .tpDct("DN").tpDl01("付款通知书").tpGtitnm("Debit Note").tpGtfilenm("RD001.PDF")
                .fileUrl("https://kip-private-dev.oss-cn-shanghai.aliyuncs.com/abc_RD001.PDF")
                .tpCo("31019").tpDl03("嘉里(沈阳)房地产开发有限公司").tpMcu("31194100").tpUnit("208")
                .tpAlph(alph).tpDc("嘉里沈阳，租赁，商场").tpCrtutime(new Date())
                .tpEv01("Y").tpEv02(StringUtils.EMPTY).tpEv03("Y").delFlag("0").formatDate("2018-11-20")
                .tpAn8(an8).tpDoco(doco)
                .tpStatus(tpStatus).tpFyr(tpFyr).tpPn(tpPn).billMonth(100 * tpFyr + tpPn)
                .mailStatus(tpStatus == 5 ? BillConstants.MSG_SUCCESS : BillConstants.MSG_NOT_SEND)
                .build();
        return entity;
    }

    private ResponseEntity<RespWrapVo> call_detail_api(String xUserJson, long id) {
        // headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("x-user", xUserJson);

        HttpEntity request = new HttpEntity<>(null, headers);
        String url = "http://localhost:" + serverPort + String.format("/b/bill/%s", String.valueOf(id));

        return restTemplate.exchange(url, HttpMethod.GET, request, RespWrapVo.class);
    }

    private ResponseEntity<RespWrapVo> call_queryBillPayers_api(String xUserJson) {
        // headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("x-user", xUserJson);

        HttpEntity request = new HttpEntity<>(null, headers);
        String url = "http://localhost:" + serverPort + "/b/bill/billpayers";

        return restTemplate.exchange(url, HttpMethod.GET, request, RespWrapVo.class);
    }

}
