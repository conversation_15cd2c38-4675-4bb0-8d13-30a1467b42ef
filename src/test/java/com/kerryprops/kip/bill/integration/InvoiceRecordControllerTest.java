package com.kerryprops.kip.bill.integration;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.enums.InvoiceState;
import com.kerryprops.kip.bill.common.enums.InvoiceTypeEnum;
import com.kerryprops.kip.bill.common.utils.InvoiceUtils;
import com.kerryprops.kip.bill.config.SyncJdeConfig;
import com.kerryprops.kip.bill.dao.EFapiaoBillInvoiceRepository;
import com.kerryprops.kip.bill.dao.EFapiaoSyncRepository;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.dao.entity.EFapiaoBillInvoice;
import com.kerryprops.kip.bill.dao.entity.EFapiaoJDEBill;
import com.kerryprops.kip.bill.dao.entity.EFapiaoSyncBill;
import com.kerryprops.kip.bill.service.PaymentBillService;
import com.kerryprops.kip.bill.service.impl.EFapiaoJdeBillServiceImpl;
import com.kerryprops.kip.bill.utils.RandomUtil;
import com.kerryprops.kip.bill.webservice.impl.InvoiceRecordController;
import com.kerryprops.kip.bill.webservice.vo.req.CallBackKerryInvoiceDetailVo;
import com.kerryprops.kip.bill.webservice.vo.req.CallBackKerryInvoiceMainVo;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.kerryprops.kip.bill.common.enums.InvoiceRecordStatus.COMPLETED;
import static com.kerryprops.kip.bill.common.enums.InvoiceRecordStatus.ENTOURAGE_COMPLETED;
import static com.kerryprops.kip.bill.common.enums.InvoiceRedflagStatusEnum.RED_FLAG_DEFAULT;
import static com.kerryprops.kip.bill.common.enums.InvoiceRedflagStatusEnum.RED_FLAG_RED_INVOICE;
import static com.kerryprops.kip.bill.common.enums.InvoiceRedflagStatusEnum.RED_FLAG_RED_STATUS;
import static com.kerryprops.kip.bill.common.enums.InvoiceState.NORMAL;
import static com.kerryprops.kip.bill.common.enums.InvoiceState.RED_LETTER;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.UNKNOWN_ERROR;
import static com.kerryprops.kip.bill.common.enums.SystemOrigTypeEnum.INTERFACE;
import static com.kerryprops.kip.bill.common.enums.SystemOrigTypeEnum.MANUAL;
import static com.kerryprops.kip.bill.common.enums.SystemOrigTypeEnum.PAGE_IMPORT;
import static com.kerryprops.kip.bill.common.utils.InvoiceUtils.INVOICE_STATUS_SUCCESS;
import static com.kerryprops.kip.bill.common.utils.InvoiceUtils.STATUS_CANCEL;
import static com.kerryprops.kip.bill.common.utils.InvoiceUtils.STATUS_NORMAL;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

@DisplayName("开票功能-单元测试类")
class InvoiceRecordControllerTest extends BaseIntegrationTest {

    private static final String SALES_BILL_NO_MOCK = "ZJDE31007RD24000030010xnuYjK";

    private static final String STRING_FLAG = "'";

    private static final String JDE_STATUS_CANCEL = "0";

    private static final String JDE_STATUS_NORMAL = "1";

    private static final String JDE_STATUS_RED_FLAG = "2";

    private static final String SELECT_SQL_FORMAT_JDE =
                    """ 
                    SELECT IFDOC, IFDCT, IFKCO, IFSFX
                    , IFLINN, IFMCU, IFGUI, IFTAX, IFAG, IFATXA, IFSTAM,IFURRF, IFEV01, IFEV02
                    , IFUSER, IFUPMJ, IFUPMT, IFCINV, IFPCM
                    FROM %s WHERE IFDOC=%s AND IFDCT=%s AND IFKCO=%s AND IFSFX=%s AND IFGUI=%s
                    """;

    // 手工开票（预开票）
    private static final String SALES_BILL_TYPE_PRE_INVOICE = "通用单据";

    private static final String IF_DOC_PRE_INVOICE = "23000012";

    private static final String IF_DCT_PRE_INVOICE = "RD";

    private static final String IF_KCO_PRE_INVOICE = "31007";

    private static final String IF_SFX_PRE_INVOICE = "001";

    private static final String SALES_BILL_NO_PRE_INVOICE = IF_KCO_PRE_INVOICE + IF_DCT_PRE_INVOICE + IF_DOC_PRE_INVOICE + IF_SFX_PRE_INVOICE;

    private static final String INVOICE_CODE_PRE_INVOICE = "112233";

    private static final String INVOICE_NO_PRE_INVOICE = "99242000000002956666";

    private static final String INVOICE_NO_PRE_INVOICE_SPLIT = "99242000000002956667";

    // JDE同步
    private static final String SALES_BILL_TYPE_JDE_SYNC = "JDE-Manual";

    private static final String SYSTEM_ORIG_JDE_SYNC = "JDE";

    private static final String IF_DOC_JDE_SYNC = "23000033";

    private static final String IF_DCT_JDE_SYNC = "RE";

    private static final String IF_KCO_JDE_SYNC = "31099";

    private static final String IF_SFX_JDE_SYNC = "002";

    private static final String SALES_BILL_NO_JDE_SYNC = IF_KCO_JDE_SYNC + IF_DCT_JDE_SYNC + IF_DOC_JDE_SYNC + IF_SFX_JDE_SYNC;

    private static final String INVOICE_CODE_JDE_SYNC = "445566";

    private static final String INVOICE_NO_JDE_SYNC = "99242000000002958888";

    private static final String INVOICE_NO_JDE_SYNC_SPLIT = "99242000000002958889";

    private static final String INVOICE_NO_JDE_SYNC_RED_FLG = "99242000000002958890";

    // JDE同步-合并
    private static final String IF_DOC_JDE_SYNC_MERGE_1 = "23000033";

    private static final String IF_DCT_JDE_SYNC_MERGE_1 = "RF";

    private static final String IF_KCO_JDE_SYNC_MERGE_1 = "31010";

    private static final String IF_SFX_JDE_SYNC_MERGE_1 = "001";

    private static final String SALES_BILL_NO_MERGE_1 = IF_KCO_JDE_SYNC_MERGE_1 + IF_DCT_JDE_SYNC_MERGE_1 + IF_DOC_JDE_SYNC_MERGE_1 + IF_SFX_JDE_SYNC_MERGE_1;

    private static final String IF_DOC_JDE_SYNC_MERGE_2 = "23000033";

    private static final String IF_DCT_JDE_SYNC_MERGE_2 = "RF";

    private static final String IF_KCO_JDE_SYNC_MERGE_2 = "31010";

    private static final String IF_SFX_JDE_SYNC_MERGE_2 = "002";

    private static final String SALES_BILL_NO_MERGE_2 = IF_KCO_JDE_SYNC_MERGE_2 + IF_DCT_JDE_SYNC_MERGE_2 + IF_DOC_JDE_SYNC_MERGE_2 + IF_SFX_JDE_SYNC_MERGE_2;

    private static final String INVOICE_CODE_JDE_SYNC_MERGE = "556677";

    private static final String INVOICE_NO_JDE_SYNC_MERGE = "99242000000002959999";

    // JDE同步-组合
    private static final String IF_DOC_JDE_SYNC_COMBINE_1 = "23000044";

    private static final String IF_DCT_JDE_SYNC_COMBINE_1 = "RG";

    private static final String IF_KCO_JDE_SYNC_COMBINE_1 = "31011";

    private static final String IF_SFX_JDE_SYNC_COMBINE_1 = "001";

    private static final String SALES_BILL_NO_COMBINE_1 = IF_KCO_JDE_SYNC_COMBINE_1 + IF_DCT_JDE_SYNC_COMBINE_1 + IF_DOC_JDE_SYNC_COMBINE_1 + IF_SFX_JDE_SYNC_COMBINE_1;

    private static final String IF_DOC_JDE_SYNC_COMBINE_2 = "23000044";

    private static final String IF_DCT_JDE_SYNC_COMBINE_2 = "RG";

    private static final String IF_KCO_JDE_SYNC_COMBINE_2 = "31011";

    private static final String IF_SFX_JDE_SYNC_COMBINE_2 = "002";

    private static final String SALES_BILL_NO_COMBINE_2 = IF_KCO_JDE_SYNC_COMBINE_2 + IF_DCT_JDE_SYNC_COMBINE_2 + IF_DOC_JDE_SYNC_COMBINE_2 + IF_SFX_JDE_SYNC_COMBINE_2;

    private static final String INVOICE_CODE_JDE_SYNC_COMBINE = "778899";

    private static final String INVOICE_NO_JDE_SYNC_COMBINE = "99242000000002964444";

    private static final String INVOICE_CODE_RED_FLAG_JDE_SYNC_COMBINE = "889900";

    private static final String INVOICE_NO_RED_FLAG_JDE_SYNC_COMBINE = "99242000000002965555";

    // excel导入场景
    private static final String SYSTEM_ORIG_EXCEL_IMPORT = "import";

    private static final String SALES_BILL_TYPE_EXCEL_IMPORT = "通用单据";

    private static final String SALES_BILL_NO_IMPORT_MOCK = "import_bill_no_mock_001";

    private static final String INVOICE_CODE_EXCEL_IMPORT = "667788";

    private static final String INVOICE_NO_EXCEL_IMPORT = "99242000000002952222";

    private static final String INVOICE_NO_EXCEL_IMPORT_SPLIT = "99242000000002952223";

    private static final String INVOICE_NO_EXCEL_IMPORT_RED_FLG = "99242000000002952224";

    @Value("${e-fapiao.system-orig}")
    protected String eFapiaoSystemOrig;

    @Value("${e-fapiao.sales-bill-type}")
    protected String eFapiaoSalesBillType;

    private final ObjectMapper mapper = new ObjectMapper();

    @SpyBean
    protected EFapiaoSyncRepository eFapiaoSyncRepository;

    @Autowired
    protected EFapiaoBillInvoiceRepository eFapiaoBillInvoiceRepository;

    @Autowired
    @Qualifier("jdeJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    @TestConfiguration
    class App {

        @Bean
        EFapiaoJdeBillServiceImpl eFapiaoJdeBillService() {
            return Mockito.mock(EFapiaoJdeBillServiceImpl.class);
        }

        @Bean
        PaymentBillService paymentBillService() {
            return Mockito.mock(PaymentBillService.class);
        }

    }

    @Autowired
    private EFapiaoJdeBillServiceImpl eFapiaoJdeBillService;

    @Autowired
    protected PaymentBillService paymentBillService;

    @Test
    @DisplayName("发票信息回调-开票失败，接口响应正常")
    void _01_1_invoiceCallback_error() throws JsonProcessingException {
        doReturn(RandomUtil.randomObject(AptPaymentInfo.class)).
                when(paymentBillService).getBillPaymentInfoById(any());

        CallBackKerryInvoiceMainVo vo = new CallBackKerryInvoiceMainVo();
        vo.setInvoiceStatus(0);
        runAndVerifyInvoiceCallback(vo);
    }

    @Test
    @DisplayName("发票信息回调-pdfUrl为null，接口响应正常")
    void _01_2_invoiceCallback_success_pdfUrlIsNull() throws JsonProcessingException {
        CallBackKerryInvoiceMainVo vo = new CallBackKerryInvoiceMainVo();
        vo.setInvoiceStatus(1);
        vo.setPdfUrl(null);

        runAndVerifyInvoiceCallback(vo);
    }

    @Test
    @DisplayName("发票信息回调-pdfUrl为空，接口响应正常")
    void _01_3_invoiceCallback_success_with_pdfUrlIsEmpty() throws JsonProcessingException {
        CallBackKerryInvoiceMainVo vo = new CallBackKerryInvoiceMainVo();
        vo.setInvoiceStatus(1);
        vo.setPdfUrl(StringUtils.EMPTY);

        runAndVerifyInvoiceCallback(vo);
    }

    @Test
    @DisplayName("发票信息回调-pdfUrl合法，接口响应正常")
    void _01_4_invoiceCallback_success_with_validPdfUrl() throws JsonProcessingException {
        doReturn(RandomUtil.randomObject(AptPaymentInfo.class)).
                when(paymentBillService).getBillPaymentInfoById(any());

        CallBackKerryInvoiceMainVo vo = new CallBackKerryInvoiceMainVo();
        vo.setInvoiceStatus(1);
        vo.setPdfUrl("https://www.baidu.com");

        runAndVerifyInvoiceCallback(vo);
    }

    @Test
    @DisplayName("手工开票（预开票）-JDE账单未查询到，无法填充mcu、jdeUnit")
    void _01_5_invoiceCallback_pre_invoice_jde_bill_not_found_error() throws JsonProcessingException {
        CallBackKerryInvoiceMainVo mainVo = new CallBackKerryInvoiceMainVo();
        mainVo.setSystemOrigType(MANUAL.getCode());
        mainVo.setSystemOrig(eFapiaoSystemOrig);
        mainVo.setSalesbillNo(SALES_BILL_NO_MOCK);

        doReturn(null).when(eFapiaoJdeBillService).queryBySalesBillNo(anyString());

        ResponseEntity<InvoiceRecordController.XForceResponse> responseEntity = call_invoiceCallback_api(mainVo);
        InvoiceRecordController.XForceResponse response = responseEntity.getBody();
        assertEquals(UNKNOWN_ERROR.getCode(), String.valueOf(response.getCode()));
    }

    @Test
    @DisplayName("手工开票（预开票）-JDE账单查询到，填充mcu、jdeUnit正常")
    void _01_6_invoiceCallback_pre_invoice_jde_bill_found_success() throws JsonProcessingException {
        // given
        CallBackKerryInvoiceMainVo mainVoMock = new CallBackKerryInvoiceMainVo();
        mainVoMock.setSystemOrigType(MANUAL.getCode());
        mainVoMock.setSystemOrig(eFapiaoSystemOrig);
        mainVoMock.setSalesbillNo(SALES_BILL_NO_MOCK);

        EFapiaoJDEBill eFapiaoJDEBillMock = EFapiaoJDEBill.builder()
                .an8(randomString(8))
                .mcu(randomString())
                .jdeUnit(randomString())
                .doco(randomString())
                .build();
        ArgumentCaptor<CallBackKerryInvoiceMainVo> callBackKerryInvoiceMainVoCaptor = ArgumentCaptor.forClass(CallBackKerryInvoiceMainVo.class);
        doNothing().when(eFapiaoJdeBillService).writeBackInvoice2Jde(callBackKerryInvoiceMainVoCaptor.capture());

        doReturn(eFapiaoJDEBillMock).when(eFapiaoJdeBillService).queryBySalesBillNo(anyString());

        // then
        call_invoiceCallback_api(mainVoMock);

        // assert
        assertEquals(eFapiaoJDEBillMock.getMcu(), callBackKerryInvoiceMainVoCaptor.getValue().getBusinessType());
        assertEquals(eFapiaoJDEBillMock.getJdeUnit(), callBackKerryInvoiceMainVoCaptor.getValue().getExt1());
        assertEquals(eFapiaoJDEBillMock.getDoco(), callBackKerryInvoiceMainVoCaptor.getValue().getExt2());
        assertEquals(eFapiaoJDEBillMock.getAn8(), callBackKerryInvoiceMainVoCaptor.getValue().getExt3());
    }

    @Test
    @DisplayName("JDE同步场景-JDE账单未查询到，无法填充mcu、jdeUnit")
    void _01_7_invoiceCallback_e_fapiao_jde_bill_not_found_error() throws JsonProcessingException {
        CallBackKerryInvoiceMainVo mainVo = new CallBackKerryInvoiceMainVo();
        mainVo.setSalesbillType(eFapiaoSalesBillType);
        mainVo.setSalesbillNo(SALES_BILL_NO_MOCK);

        ResponseEntity<InvoiceRecordController.XForceResponse> responseEntity = call_invoiceCallback_api(mainVo);
        InvoiceRecordController.XForceResponse response = responseEntity.getBody();
        assertEquals(UNKNOWN_ERROR.getCode(), String.valueOf(response.getCode()));
    }

    @Test
    @DisplayName("JDE同步场景-JDE账单查询到，填充mcu、jdeUnit正常")
    void _01_8_invoiceCallback_e_fapiao_jde_bill_found_success() throws JsonProcessingException {
        // given
        CallBackKerryInvoiceMainVo mainVoMock = new CallBackKerryInvoiceMainVo();
        mainVoMock.setSalesbillType(eFapiaoSalesBillType);
        mainVoMock.setSalesbillNo(SALES_BILL_NO_MOCK);

        EFapiaoSyncBill eFapiaoSyncBillMock = new EFapiaoSyncBill();
        eFapiaoSyncBillMock.setAn8(randomString(8));
        eFapiaoSyncBillMock.setMcu(randomString());
        eFapiaoSyncBillMock.setJdeUnit(randomString());
        eFapiaoSyncBillMock.setDoco(randomString());

        ArgumentCaptor<CallBackKerryInvoiceMainVo> callBackKerryInvoiceMainVoCaptor = ArgumentCaptor.forClass(CallBackKerryInvoiceMainVo.class);
        doNothing().when(eFapiaoJdeBillService).writeBackInvoice2Jde(callBackKerryInvoiceMainVoCaptor.capture());

        when(eFapiaoSyncRepository.findTopBySalesBillNo(any())).thenReturn(eFapiaoSyncBillMock);

        // then
        call_invoiceCallback_api(mainVoMock);

        // assert
        assertEquals(eFapiaoSyncBillMock.getMcu(), callBackKerryInvoiceMainVoCaptor.getValue().getBusinessType());
        assertEquals(eFapiaoSyncBillMock.getJdeUnit(), callBackKerryInvoiceMainVoCaptor.getValue().getExt1());
        assertEquals(eFapiaoSyncBillMock.getDoco(), callBackKerryInvoiceMainVoCaptor.getValue().getExt2());
        assertEquals(eFapiaoSyncBillMock.getAn8(), callBackKerryInvoiceMainVoCaptor.getValue().getExt3());
    }

    @Test
    @DisplayName("手工开票（预开票）-正常开票（非拆票、非合并/组合）- 第一次回调")
    void _02_1_invoiceCallback_pre_invoice_ordinary_first_callback() throws JsonProcessingException {
        // given
        CallBackKerryInvoiceMainVo mainVo = generateCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo("PYT********104331772");
        mainVo.setSalesbillNumber(1);
        mainVo.setSalesbillType(SALES_BILL_TYPE_PRE_INVOICE);
        mainVo.setRedFlag(RED_FLAG_DEFAULT.getIndexStr());
        mainVo.setInvoiceStatus(INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(STATUS_NORMAL);
        mainVo.setSystemOrig("5");
        mainVo.setSystemOrigType(MANUAL.getCode());
        mainVo.setTaxRate("0.050");

        // then
        ResponseEntity<InvoiceRecordController.XForceResponse> responseEntity = call_invoiceCallback_api(mainVo);

        // assert
        InvoiceRecordController.XForceResponse response = responseEntity.getBody();
        assertEquals(UNKNOWN_ERROR.getCode(), String.valueOf(response.getCode()));
        assertTrue(response.getMessage().contains("aptPaymentInfo"));
    }


    @Test
    @DisplayName("手工开票（预开票）-正常开票（非拆票、非合并/组合）- 第二次回调")
    void _02_2_invoiceCallback_pre_invoice_ordinary_second_callback() throws JsonProcessingException {
        // given
        CallBackKerryInvoiceMainVo mainVo = generateCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo("ZJDE" + SALES_BILL_NO_PRE_INVOICE + "VW097Q");
        mainVo.setInvoiceCode(INVOICE_CODE_PRE_INVOICE);
        mainVo.setInvoiceNo(INVOICE_NO_PRE_INVOICE);
        mainVo.setPurchaserTaxNo(randomString());
        mainVo.setSalesbillNumber(1);
        mainVo.setSalesbillType(SALES_BILL_TYPE_PRE_INVOICE);
        mainVo.setRedFlag(RED_FLAG_DEFAULT.getIndexStr());
        mainVo.setInvoiceStatus(INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(STATUS_NORMAL);
        mainVo.setSystemOrig(SYSTEM_ORIG_JDE_SYNC);
        mainVo.setSystemOrigType(MANUAL.getCode());
        mainVo.setTaxRate("0.050");

        EFapiaoJDEBill eFapiaoJDEBill = EFapiaoJDEBill.builder()
                .mcu(randomString(8))
                .jdeUnit(randomString())
                .doco("271758")
                .an8(randomString(8))
                .build();
        doReturn(eFapiaoJDEBill).when(jdbcJdeBillOriginRepository).queryBySalesBillNo("31007RD23000012001");

        // then
        runAndVerifyInvoiceCallback(mainVo);

        // assert jde
        String selectSql = String.format(SELECT_SQL_FORMAT_JDE, SyncJdeConfig.getJdeBillInvoiceWriteBack()
                , IF_DOC_PRE_INVOICE, STRING_FLAG + IF_DCT_PRE_INVOICE + STRING_FLAG, STRING_FLAG + IF_KCO_PRE_INVOICE + STRING_FLAG
                , STRING_FLAG + IF_SFX_PRE_INVOICE + STRING_FLAG, mainVo.getInvoiceNo());
        Map<String, Object> jdeInvoiceWriteBackActual = jdbcTemplate.queryForMap(selectSql);
        verifyJdeInvoiceWriteBackJde(mainVo, jdeInvoiceWriteBackActual, IF_DOC_PRE_INVOICE, IF_DCT_PRE_INVOICE
                , IF_KCO_PRE_INVOICE, IF_SFX_PRE_INVOICE, JDE_STATUS_NORMAL);

        // assert Kerry+
        List<EFapiaoBillInvoice> eFapiaoBillInvoicesActual = eFapiaoBillInvoiceRepository
                .findAllBySalesBillNo(SALES_BILL_NO_PRE_INVOICE);
        assertFalse(eFapiaoBillInvoicesActual.isEmpty());

        EFapiaoBillInvoice eFapiaoBillInvoiceActual = eFapiaoBillInvoicesActual.stream()
                .filter(e -> INVOICE_NO_PRE_INVOICE.equals(e.getInvoiceNo()))
                .sorted(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime))
                .findFirst().get();

        assertEquals(SALES_BILL_NO_PRE_INVOICE, eFapiaoBillInvoiceActual.getSalesBillNo());
        assertEquals(0, mainVo.getAmountWithoutTax().compareTo(eFapiaoBillInvoiceActual.getAmountWithoutTax()));
        assertEquals(0, mainVo.getAmountWithTax().compareTo(eFapiaoBillInvoiceActual.getAmountWithTax()));
        assertEquals(0, mainVo.getTaxAmount().compareTo(eFapiaoBillInvoiceActual.getTaxAmount()));
        assertEquals(0, new BigDecimal(mainVo.getTaxRate()).compareTo(eFapiaoBillInvoiceActual.getTaxRate()));

        verifyJdeInvoiceInKerryPlus(mainVo, eFapiaoBillInvoiceActual);

        assertEquals(eFapiaoJDEBill.getDoco(), eFapiaoBillInvoiceActual.getDoco());
        assertEquals(eFapiaoJDEBill.getAn8(), eFapiaoBillInvoiceActual.getAn8());
        assertEquals(eFapiaoJDEBill.getMcu(), eFapiaoBillInvoiceActual.getMcu());
        assertEquals(eFapiaoJDEBill.getJdeUnit(), eFapiaoBillInvoiceActual.getJdeUnit());
        assertEquals(IF_KCO_PRE_INVOICE, eFapiaoBillInvoiceActual.getKco());
        assertEquals(IF_DCT_PRE_INVOICE, eFapiaoBillInvoiceActual.getBillType());
        assertEquals(IF_DOC_PRE_INVOICE, String.valueOf(eFapiaoBillInvoiceActual.getDoc()));
        assertEquals(IF_SFX_PRE_INVOICE, eFapiaoBillInvoiceActual.getPaymentItem());

        assertEquals(COMPLETED, eFapiaoBillInvoiceActual.getInvoiceRecordStatus());
        assertEquals(NORMAL, eFapiaoBillInvoiceActual.getState());
    }

    @Test
    @DisplayName("手工开票（预开票）-正常开票（非拆票、非合并/组合）- 第二次回调-拆票")
    void _02_3_invoiceCallback_pre_invoice_ordinary_split_second_callback() throws JsonProcessingException {
        // given
        CallBackKerryInvoiceMainVo mainVo = generateCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo("ZJDE" + SALES_BILL_NO_PRE_INVOICE + "VW097Q");
        mainVo.setInvoiceCode(INVOICE_CODE_PRE_INVOICE);
        mainVo.setInvoiceNo(INVOICE_NO_PRE_INVOICE_SPLIT);
        mainVo.setPurchaserTaxNo(randomString());
        mainVo.setSalesbillNumber(1);
        mainVo.setSalesbillType(SALES_BILL_TYPE_PRE_INVOICE);
        mainVo.setRedFlag(RED_FLAG_DEFAULT.getIndexStr());
        mainVo.setInvoiceStatus(INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(STATUS_NORMAL);
        mainVo.setSystemOrig(SYSTEM_ORIG_JDE_SYNC);
        mainVo.setSystemOrigType(MANUAL.getCode());
        mainVo.setTaxRate("0.050");

        EFapiaoJDEBill eFapiaoJDEBill = EFapiaoJDEBill.builder()
                .mcu(randomString(8))
                .jdeUnit(randomString())
                .doco("271758")
                .an8(randomString(8))
                .build();
        doReturn(eFapiaoJDEBill).when(jdbcJdeBillOriginRepository).queryBySalesBillNo("31007RD23000012001");

        // then
        runAndVerifyInvoiceCallback(mainVo);

        // assert jde
        String selectSql = String.format(SELECT_SQL_FORMAT_JDE, SyncJdeConfig.getJdeBillInvoiceWriteBack()
                , IF_DOC_PRE_INVOICE, STRING_FLAG + IF_DCT_PRE_INVOICE + STRING_FLAG, STRING_FLAG + IF_KCO_PRE_INVOICE + STRING_FLAG
                , STRING_FLAG + IF_SFX_PRE_INVOICE + STRING_FLAG, mainVo.getInvoiceNo());
        Map<String, Object> jdeInvoiceWriteBackActual = jdbcTemplate.queryForMap(selectSql);
        verifyJdeInvoiceWriteBackJde(mainVo, jdeInvoiceWriteBackActual, IF_DOC_PRE_INVOICE, IF_DCT_PRE_INVOICE
                , IF_KCO_PRE_INVOICE, IF_SFX_PRE_INVOICE, JDE_STATUS_NORMAL);

        // assert Kerry+
        List<EFapiaoBillInvoice> eFapiaoBillInvoicesActual = eFapiaoBillInvoiceRepository
                .findAllBySalesBillNo(SALES_BILL_NO_PRE_INVOICE);
        assertFalse(eFapiaoBillInvoicesActual.isEmpty());

        EFapiaoBillInvoice eFapiaoBillInvoiceActual = eFapiaoBillInvoicesActual.stream()
                .filter(e -> INVOICE_NO_PRE_INVOICE_SPLIT.equals(e.getInvoiceNo()))
                .sorted(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime))
                .findFirst().get();

        assertEquals(SALES_BILL_NO_PRE_INVOICE, eFapiaoBillInvoiceActual.getSalesBillNo());
        assertEquals(0, mainVo.getAmountWithoutTax().compareTo(eFapiaoBillInvoiceActual.getAmountWithoutTax()));
        assertEquals(0, mainVo.getAmountWithTax().compareTo(eFapiaoBillInvoiceActual.getAmountWithTax()));
        assertEquals(0, mainVo.getTaxAmount().compareTo(eFapiaoBillInvoiceActual.getTaxAmount()));
        assertEquals(0, new BigDecimal(mainVo.getTaxRate()).compareTo(eFapiaoBillInvoiceActual.getTaxRate()));

        verifyJdeInvoiceInKerryPlus(mainVo, eFapiaoBillInvoiceActual);

        assertEquals(eFapiaoJDEBill.getDoco(), eFapiaoBillInvoiceActual.getDoco());
        assertEquals(eFapiaoJDEBill.getAn8(), eFapiaoBillInvoiceActual.getAn8());
        assertEquals(eFapiaoJDEBill.getMcu(), eFapiaoBillInvoiceActual.getMcu());
        assertEquals(eFapiaoJDEBill.getJdeUnit(), eFapiaoBillInvoiceActual.getJdeUnit());
        assertEquals(IF_KCO_PRE_INVOICE, eFapiaoBillInvoiceActual.getKco());
        assertEquals(IF_DCT_PRE_INVOICE, eFapiaoBillInvoiceActual.getBillType());
        assertEquals(IF_DOC_PRE_INVOICE, String.valueOf(eFapiaoBillInvoiceActual.getDoc()));
        assertEquals(IF_SFX_PRE_INVOICE, eFapiaoBillInvoiceActual.getPaymentItem());

        assertEquals(COMPLETED, eFapiaoBillInvoiceActual.getInvoiceRecordStatus());
        assertEquals(NORMAL, eFapiaoBillInvoiceActual.getState());
    }

    @Test
    @DisplayName("手工开票（预开票）-正常开票（非拆票、非合并/组合）- 作废场景")
    void _02_4_invoiceCallback_pre_invoice_ordinary_cancel_callback() throws JsonProcessingException {
        // given
        CallBackKerryInvoiceMainVo mainVo = generateCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo("ZJDE" + SALES_BILL_NO_PRE_INVOICE + "VW097Q");
        mainVo.setInvoiceCode(randomString());
        mainVo.setInvoiceNo(INVOICE_NO_PRE_INVOICE);
        mainVo.setPurchaserTaxNo(randomString());
        mainVo.setSalesbillNumber(1);
        mainVo.setSalesbillType(SALES_BILL_TYPE_PRE_INVOICE);
        mainVo.setRedFlag(RED_FLAG_DEFAULT.getIndexStr());
        mainVo.setInvoiceStatus(INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(STATUS_CANCEL);
        mainVo.setSystemOrig(SYSTEM_ORIG_JDE_SYNC);
        mainVo.setSystemOrigType(MANUAL.getCode());
        mainVo.setTaxRate("0.050");

        EFapiaoJDEBill eFapiaoJDEBill = EFapiaoJDEBill.builder()
                .mcu(randomString(8))
                .jdeUnit(randomString())
                .doco("271758")
                .an8(randomString(8))
                .build();
        doReturn(eFapiaoJDEBill).when(jdbcJdeBillOriginRepository).queryBySalesBillNo(SALES_BILL_NO_PRE_INVOICE);

        // then
        runAndVerifyInvoiceCallback(mainVo);

        // assert jde
        String selectSql = String.format(SELECT_SQL_FORMAT_JDE, SyncJdeConfig.getJdeBillInvoiceWriteBack()
                , IF_DOC_PRE_INVOICE, STRING_FLAG + IF_DCT_PRE_INVOICE + STRING_FLAG, STRING_FLAG + IF_KCO_PRE_INVOICE + STRING_FLAG
                , STRING_FLAG + IF_SFX_PRE_INVOICE + STRING_FLAG, mainVo.getInvoiceNo());
        Map<String, Object> jdeInvoiceWriteBackActual = jdbcTemplate.queryForMap(selectSql);
        assertEquals("0", jdeInvoiceWriteBackActual.get("IFEV01"));

        // assert Kerry+
        List<EFapiaoBillInvoice> eFapiaoBillInvoicesActual = eFapiaoBillInvoiceRepository
                .findAllBySalesBillNo(SALES_BILL_NO_PRE_INVOICE);
        assertFalse(eFapiaoBillInvoicesActual.isEmpty());

        EFapiaoBillInvoice eFapiaoBillInvoiceActual = eFapiaoBillInvoicesActual.stream()
                .filter(e -> INVOICE_NO_PRE_INVOICE.equals(e.getInvoiceNo()))
                .sorted(Comparator.comparing(EFapiaoBillInvoice::getUpdatedTime))
                .findFirst().get();

        assertEquals(InvoiceState.CANCELLED, eFapiaoBillInvoiceActual.getState());
    }

    @Test
    @DisplayName("JDE同步-正常开票（非拆票、非合并/组合")
    void _03_1_invoiceCallback_jde_sync_ordinary_callback() throws JsonProcessingException {
        // given
        CallBackKerryInvoiceMainVo mainVo = generateCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo(SALES_BILL_NO_JDE_SYNC);
        mainVo.setInvoiceCode(INVOICE_CODE_JDE_SYNC);
        mainVo.setInvoiceNo(INVOICE_NO_JDE_SYNC);
        mainVo.setPurchaserTaxNo(randomString());
        mainVo.setSalesbillNumber(1);
        mainVo.setSalesbillType(SALES_BILL_TYPE_JDE_SYNC);
        mainVo.setRedFlag(RED_FLAG_DEFAULT.getIndexStr());
        mainVo.setInvoiceStatus(INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(STATUS_NORMAL);
        mainVo.setSystemOrig(SYSTEM_ORIG_JDE_SYNC);
        mainVo.setSystemOrigType(INTERFACE.getCode());
        mainVo.setTaxRate("0.060");

        EFapiaoSyncBill eFapiaoSyncBill = new EFapiaoSyncBill();
        eFapiaoSyncBill.setMcu(randomString(8));
        eFapiaoSyncBill.setJdeUnit(randomString());
        eFapiaoSyncBill.setDoco("271799");
        eFapiaoSyncBill.setAn8(randomString(8));
        eFapiaoSyncBill.setSalesBillNo(SALES_BILL_NO_JDE_SYNC);
        eFapiaoSyncRepository.save(eFapiaoSyncBill);

        // then
        runAndVerifyInvoiceCallback(mainVo);

        // assert jde
        String selectSql = String.format(SELECT_SQL_FORMAT_JDE, SyncJdeConfig.getJdeBillInvoiceWriteBack()
                , IF_DOC_JDE_SYNC, STRING_FLAG + IF_DCT_JDE_SYNC + STRING_FLAG, STRING_FLAG + IF_KCO_JDE_SYNC + STRING_FLAG
                , STRING_FLAG + IF_SFX_JDE_SYNC + STRING_FLAG, mainVo.getInvoiceNo());
        Map<String, Object> jdeInvoiceWriteBackActual = jdbcTemplate.queryForMap(selectSql);
        verifyJdeInvoiceWriteBackJde(mainVo, jdeInvoiceWriteBackActual, IF_DOC_JDE_SYNC, IF_DCT_JDE_SYNC
                , IF_KCO_JDE_SYNC, IF_SFX_JDE_SYNC, JDE_STATUS_NORMAL);

        // assert Kerry+
        List<EFapiaoBillInvoice> eFapiaoBillInvoicesActual = eFapiaoBillInvoiceRepository
                .findAllBySalesBillNo(SALES_BILL_NO_JDE_SYNC);
        assertFalse(eFapiaoBillInvoicesActual.isEmpty());

        EFapiaoBillInvoice eFapiaoBillInvoiceActual = eFapiaoBillInvoicesActual.stream()
                .filter(e -> INVOICE_NO_JDE_SYNC.equals(e.getInvoiceNo()))
                .sorted(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime))
                .findFirst().get();

        assertEquals(SALES_BILL_NO_JDE_SYNC, eFapiaoBillInvoiceActual.getSalesBillNo());
        assertEquals(0, mainVo.getAmountWithoutTax().compareTo(eFapiaoBillInvoiceActual.getAmountWithoutTax()));
        assertEquals(0, mainVo.getAmountWithTax().compareTo(eFapiaoBillInvoiceActual.getAmountWithTax()));
        assertEquals(0, mainVo.getTaxAmount().compareTo(eFapiaoBillInvoiceActual.getTaxAmount()));
        assertEquals(0, new BigDecimal(mainVo.getTaxRate()).compareTo(eFapiaoBillInvoiceActual.getTaxRate()));

        verifyJdeInvoiceInKerryPlus(mainVo, eFapiaoBillInvoiceActual);

        assertEquals(eFapiaoSyncBill.getDoco(), eFapiaoBillInvoiceActual.getDoco());
        assertEquals(eFapiaoSyncBill.getAn8(), eFapiaoBillInvoiceActual.getAn8());
        assertEquals(eFapiaoSyncBill.getMcu(), eFapiaoBillInvoiceActual.getMcu());
        assertEquals(eFapiaoSyncBill.getJdeUnit(), eFapiaoBillInvoiceActual.getJdeUnit());
        assertEquals(IF_KCO_JDE_SYNC, eFapiaoBillInvoiceActual.getKco());
        assertEquals(IF_DCT_JDE_SYNC, eFapiaoBillInvoiceActual.getBillType());
        assertEquals(IF_DOC_JDE_SYNC, String.valueOf(eFapiaoBillInvoiceActual.getDoc()));
        assertEquals(IF_SFX_JDE_SYNC, eFapiaoBillInvoiceActual.getPaymentItem());

        assertEquals(COMPLETED, eFapiaoBillInvoiceActual.getInvoiceRecordStatus());
        assertEquals(NORMAL, eFapiaoBillInvoiceActual.getState());
    }

    @Test
    @DisplayName("JDE同步-正常开票（非拆票、非合并/组合-拆票")
    void _03_2_invoiceCallback_jde_sync_ordinary_split_callback() throws JsonProcessingException {
        // given
        CallBackKerryInvoiceMainVo mainVo = generateCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo(SALES_BILL_NO_JDE_SYNC);
        mainVo.setInvoiceCode(INVOICE_CODE_JDE_SYNC);
        mainVo.setInvoiceNo(INVOICE_NO_JDE_SYNC_SPLIT);
        mainVo.setPurchaserTaxNo(randomString());
        mainVo.setSalesbillNumber(1);
        mainVo.setSalesbillType(SALES_BILL_TYPE_JDE_SYNC);
        mainVo.setRedFlag(RED_FLAG_DEFAULT.getIndexStr());
        mainVo.setInvoiceStatus(INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(STATUS_NORMAL);
        mainVo.setSystemOrig(SYSTEM_ORIG_JDE_SYNC);
        mainVo.setSystemOrigType(INTERFACE.getCode());
        mainVo.setTaxRate("0.060");

        List<EFapiaoSyncBill> eFapiaoSyncBills = eFapiaoSyncRepository.findAll();
        EFapiaoSyncBill eFapiaoSyncBill = eFapiaoSyncBills.stream()
                .filter(e -> mainVo.getSalesbillNo().equals(e.getSalesBillNo())).findFirst().get();

        // then
        runAndVerifyInvoiceCallback(mainVo);

        // assert jde
        String selectSql = String.format(SELECT_SQL_FORMAT_JDE, SyncJdeConfig.getJdeBillInvoiceWriteBack()
                , IF_DOC_JDE_SYNC, STRING_FLAG + IF_DCT_JDE_SYNC + STRING_FLAG, STRING_FLAG + IF_KCO_JDE_SYNC + STRING_FLAG
                , STRING_FLAG + IF_SFX_JDE_SYNC + STRING_FLAG, mainVo.getInvoiceNo());
        Map<String, Object> jdeInvoiceWriteBackActual = jdbcTemplate.queryForMap(selectSql);
        verifyJdeInvoiceWriteBackJde(mainVo, jdeInvoiceWriteBackActual, IF_DOC_JDE_SYNC, IF_DCT_JDE_SYNC
                , IF_KCO_JDE_SYNC, IF_SFX_JDE_SYNC, JDE_STATUS_NORMAL);

        // assert Kerry+
        List<EFapiaoBillInvoice> eFapiaoBillInvoicesActual = eFapiaoBillInvoiceRepository
                .findAllBySalesBillNo(SALES_BILL_NO_JDE_SYNC);
        assertFalse(eFapiaoBillInvoicesActual.isEmpty());

        EFapiaoBillInvoice eFapiaoBillInvoiceActual = eFapiaoBillInvoicesActual.stream()
                .filter(e -> INVOICE_NO_JDE_SYNC_SPLIT.equals(e.getInvoiceNo()))
                .sorted(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime))
                .findFirst().get();

        assertEquals(SALES_BILL_NO_JDE_SYNC, eFapiaoBillInvoiceActual.getSalesBillNo());
        assertEquals(0, mainVo.getAmountWithoutTax().compareTo(eFapiaoBillInvoiceActual.getAmountWithoutTax()));
        assertEquals(0, mainVo.getAmountWithTax().compareTo(eFapiaoBillInvoiceActual.getAmountWithTax()));
        assertEquals(0, mainVo.getTaxAmount().compareTo(eFapiaoBillInvoiceActual.getTaxAmount()));
        assertEquals(0, new BigDecimal(mainVo.getTaxRate()).compareTo(eFapiaoBillInvoiceActual.getTaxRate()));

        verifyJdeInvoiceInKerryPlus(mainVo, eFapiaoBillInvoiceActual);

        assertEquals(eFapiaoSyncBill.getDoco(), eFapiaoBillInvoiceActual.getDoco());
        assertEquals(eFapiaoSyncBill.getAn8(), eFapiaoBillInvoiceActual.getAn8());
        assertEquals(eFapiaoSyncBill.getMcu(), eFapiaoBillInvoiceActual.getMcu());
        assertEquals(eFapiaoSyncBill.getJdeUnit(), eFapiaoBillInvoiceActual.getJdeUnit());
        assertEquals(IF_KCO_JDE_SYNC, eFapiaoBillInvoiceActual.getKco());
        assertEquals(IF_DCT_JDE_SYNC, eFapiaoBillInvoiceActual.getBillType());
        assertEquals(IF_DOC_JDE_SYNC, String.valueOf(eFapiaoBillInvoiceActual.getDoc()));
        assertEquals(IF_SFX_JDE_SYNC, eFapiaoBillInvoiceActual.getPaymentItem());

        assertEquals(COMPLETED, eFapiaoBillInvoiceActual.getInvoiceRecordStatus());
        assertEquals(NORMAL, eFapiaoBillInvoiceActual.getState());
    }

    @Test
    @DisplayName("JDE同步-蓝票标记红冲")
    void _03_3_invoiceCallback_jde_sync_set_red_flag_callback() throws JsonProcessingException {
        // given
        CallBackKerryInvoiceMainVo mainVo = generateCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo(SALES_BILL_NO_JDE_SYNC);
        mainVo.setInvoiceCode(INVOICE_CODE_JDE_SYNC);
        mainVo.setInvoiceNo(INVOICE_NO_JDE_SYNC);
        mainVo.setPurchaserTaxNo(randomString());
        mainVo.setSalesbillNumber(1);
        mainVo.setSalesbillType(SALES_BILL_TYPE_JDE_SYNC);
        mainVo.setRedFlag(RED_FLAG_RED_STATUS.getIndexStr());
        mainVo.setInvoiceStatus(INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(STATUS_NORMAL);
        mainVo.setSystemOrig(SYSTEM_ORIG_JDE_SYNC);
        mainVo.setSystemOrigType(INTERFACE.getCode());
        mainVo.setTaxRate("0.060");

        // then
        runAndVerifyInvoiceCallback(mainVo);

        // assert Kerry+
        List<EFapiaoBillInvoice> eFapiaoBillInvoicesActual = eFapiaoBillInvoiceRepository
                .findAllBySalesBillNo(SALES_BILL_NO_JDE_SYNC);
        assertFalse(eFapiaoBillInvoicesActual.isEmpty());

        EFapiaoBillInvoice eFapiaoBillInvoiceActual = eFapiaoBillInvoicesActual.stream()
                .filter(e -> INVOICE_NO_JDE_SYNC.equals(e.getInvoiceNo()))
                .sorted(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime))
                .findFirst().get();

        assertEquals(InvoiceState.RED_STATUS, eFapiaoBillInvoiceActual.getState());
    }

    @Test
    @DisplayName("JDE同步-开红冲票")
    void _03_4_invoiceCallback_jde_sync_red_flag_invoice_callback() throws JsonProcessingException {
        // given
        CallBackKerryInvoiceMainVo mainVo = generateCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo(SALES_BILL_NO_JDE_SYNC);
        mainVo.setInvoiceCode(randomString());
        mainVo.setInvoiceNo(INVOICE_NO_JDE_SYNC_RED_FLG);
        mainVo.setPurchaserTaxNo(randomString());
        mainVo.setSalesbillNumber(1);
        mainVo.setSalesbillType(SALES_BILL_TYPE_JDE_SYNC);
        mainVo.setRedFlag(RED_FLAG_RED_INVOICE.getIndexStr());
        mainVo.setInvoiceStatus(INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(STATUS_NORMAL);
        mainVo.setSystemOrig(SYSTEM_ORIG_JDE_SYNC);
        mainVo.setSystemOrigType(INTERFACE.getCode());
        mainVo.setTaxRate("0.060");
        mainVo.setTaxAmount(BigDecimal.valueOf(-11.32));
        mainVo.setAmountWithoutTax(BigDecimal.valueOf(-188.68));
        mainVo.setAmountWithTax(BigDecimal.valueOf(-200.0));
        mainVo.setMakingReason(randomString());

        List<CallBackKerryInvoiceDetailVo> callBackKerryInvoiceDetailVos = mainVo.getDetailVos();
        callBackKerryInvoiceDetailVos.forEach(callBackKerryInvoiceDetailVo -> {
            callBackKerryInvoiceDetailVo.setAmountWithTax(mainVo.getAmountWithTax());
            callBackKerryInvoiceDetailVo.setAmountWithoutTax(mainVo.getAmountWithoutTax());
            callBackKerryInvoiceDetailVo.setTaxAmount(mainVo.getTaxAmount());
            callBackKerryInvoiceDetailVo.setQuantity(BigDecimal.valueOf(-1.0));
        });

        List<EFapiaoSyncBill> eFapiaoSyncBills = eFapiaoSyncRepository.findAll();
        EFapiaoSyncBill eFapiaoSyncBill = eFapiaoSyncBills.stream()
                .filter(e -> mainVo.getSalesbillNo().equals(e.getSalesBillNo())).findFirst().get();

        // then
        runAndVerifyInvoiceCallback(mainVo);

        // assert jde
        String selectSql = String.format(SELECT_SQL_FORMAT_JDE, SyncJdeConfig.getJdeBillInvoiceWriteBack()
                , IF_DOC_JDE_SYNC, STRING_FLAG + IF_DCT_JDE_SYNC + STRING_FLAG, STRING_FLAG + IF_KCO_JDE_SYNC + STRING_FLAG
                , STRING_FLAG + IF_SFX_JDE_SYNC + STRING_FLAG, mainVo.getInvoiceNo());
        Map<String, Object> jdeInvoiceWriteBackActual = jdbcTemplate.queryForMap(selectSql);
        verifyJdeInvoiceWriteBackJde(mainVo, jdeInvoiceWriteBackActual, IF_DOC_JDE_SYNC, IF_DCT_JDE_SYNC
                , IF_KCO_JDE_SYNC, IF_SFX_JDE_SYNC, JDE_STATUS_RED_FLAG);

        // assert Kerry+
        List<EFapiaoBillInvoice> eFapiaoBillInvoicesActual = eFapiaoBillInvoiceRepository
                .findAllBySalesBillNo(SALES_BILL_NO_JDE_SYNC);
        assertFalse(eFapiaoBillInvoicesActual.isEmpty());

        EFapiaoBillInvoice eFapiaoBillInvoiceActual = eFapiaoBillInvoicesActual.stream()
                .filter(e -> INVOICE_NO_JDE_SYNC_RED_FLG.equals(e.getInvoiceNo()))
                .sorted(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime))
                .findFirst().get();

        assertEquals(SALES_BILL_NO_JDE_SYNC, eFapiaoBillInvoiceActual.getSalesBillNo());
        assertEquals(0, mainVo.getAmountWithoutTax().compareTo(eFapiaoBillInvoiceActual.getAmountWithoutTax()));
        assertEquals(0, mainVo.getAmountWithTax().compareTo(eFapiaoBillInvoiceActual.getAmountWithTax()));
        assertEquals(0, mainVo.getTaxAmount().compareTo(eFapiaoBillInvoiceActual.getTaxAmount()));
        assertEquals(0, new BigDecimal(mainVo.getTaxRate()).compareTo(eFapiaoBillInvoiceActual.getTaxRate()));

        verifyJdeInvoiceInKerryPlus(mainVo, eFapiaoBillInvoiceActual);

        assertEquals(eFapiaoSyncBill.getDoco(), eFapiaoBillInvoiceActual.getDoco());
        assertEquals(eFapiaoSyncBill.getAn8(), eFapiaoBillInvoiceActual.getAn8());
        assertEquals(eFapiaoSyncBill.getMcu(), eFapiaoBillInvoiceActual.getMcu());
        assertEquals(eFapiaoSyncBill.getJdeUnit(), eFapiaoBillInvoiceActual.getJdeUnit());
        assertEquals(IF_KCO_JDE_SYNC, eFapiaoBillInvoiceActual.getKco());
        assertEquals(IF_DCT_JDE_SYNC, eFapiaoBillInvoiceActual.getBillType());
        assertEquals(IF_DOC_JDE_SYNC, String.valueOf(eFapiaoBillInvoiceActual.getDoc()));
        assertEquals(IF_SFX_JDE_SYNC, eFapiaoBillInvoiceActual.getPaymentItem());

        assertEquals(COMPLETED, eFapiaoBillInvoiceActual.getInvoiceRecordStatus());
        assertEquals(RED_LETTER, eFapiaoBillInvoiceActual.getState());
    }

    /**
     * 合并开票：回调时主要是业务单号和业务单金额不同
     */
    @Test
    @DisplayName("JDE同步-合并开票-第一次回调")
    void _04_1_invoiceCallback_jde_sync_merge_invoice_first_callback() throws JsonProcessingException {
        // given
        CallBackKerryInvoiceMainVo mainVo = generateCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo(SALES_BILL_NO_MERGE_1);
        mainVo.setInvoiceCode(INVOICE_CODE_JDE_SYNC_MERGE);
        mainVo.setInvoiceNo(INVOICE_NO_JDE_SYNC_MERGE);
        mainVo.setPurchaserTaxNo(randomString());
        mainVo.setSalesbillAmountWithTax(BigDecimal.TEN);
        mainVo.setSalesbillAmountWithoutTax(BigDecimal.ONE);
        mainVo.setSalesbillTaxAmount(BigDecimal.ZERO);
        mainVo.setSalesbillNumber(1);
        mainVo.setSalesbillType(SALES_BILL_TYPE_JDE_SYNC);
        mainVo.setRedFlag(RED_FLAG_DEFAULT.getIndexStr());
        mainVo.setInvoiceStatus(INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(STATUS_NORMAL);
        mainVo.setSystemOrig(SYSTEM_ORIG_JDE_SYNC);
        mainVo.setSystemOrigType(INTERFACE.getCode());
        mainVo.setTaxRate("0.060");

        EFapiaoSyncBill eFapiaoSyncBillMerge1 = new EFapiaoSyncBill();
        eFapiaoSyncBillMerge1.setMcu(randomString(8));
        eFapiaoSyncBillMerge1.setJdeUnit(randomString());
        eFapiaoSyncBillMerge1.setDoco("271800");
        eFapiaoSyncBillMerge1.setAn8(randomString(8));
        eFapiaoSyncBillMerge1.setSalesBillNo(SALES_BILL_NO_MERGE_1);
        eFapiaoSyncRepository.save(eFapiaoSyncBillMerge1);

        // then
        runAndVerifyInvoiceCallback(mainVo);

        // assert jde
        String selectSql = String.format(SELECT_SQL_FORMAT_JDE, SyncJdeConfig.getJdeBillInvoiceWriteBack()
                , IF_DOC_JDE_SYNC_MERGE_1, STRING_FLAG + IF_DCT_JDE_SYNC_MERGE_1 + STRING_FLAG, STRING_FLAG + IF_KCO_JDE_SYNC_MERGE_1 + STRING_FLAG
                , STRING_FLAG + IF_SFX_JDE_SYNC_MERGE_1 + STRING_FLAG, mainVo.getInvoiceNo());
        Map<String, Object> jdeInvoiceWriteBackActual = jdbcTemplate.queryForMap(selectSql);
        verifyJdeInvoiceWriteBackJde(mainVo, jdeInvoiceWriteBackActual, IF_DOC_JDE_SYNC_MERGE_1, IF_DCT_JDE_SYNC_MERGE_1
                , IF_KCO_JDE_SYNC_MERGE_1, IF_SFX_JDE_SYNC_MERGE_1, JDE_STATUS_NORMAL);

        // assert Kerry+
        List<EFapiaoBillInvoice> eFapiaoBillInvoicesActual = eFapiaoBillInvoiceRepository
                .findAllBySalesBillNo(SALES_BILL_NO_MERGE_1);
        assertFalse(eFapiaoBillInvoicesActual.isEmpty());

        EFapiaoBillInvoice eFapiaoBillInvoiceActual = eFapiaoBillInvoicesActual.stream()
                .filter(e -> INVOICE_NO_JDE_SYNC_MERGE.equals(e.getInvoiceNo()))
                .sorted(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime))
                .findFirst().get();

        assertEquals(SALES_BILL_NO_MERGE_1, eFapiaoBillInvoiceActual.getSalesBillNo());
        assertEquals(0, mainVo.getAmountWithoutTax().compareTo(eFapiaoBillInvoiceActual.getAmountWithoutTax()));
        assertEquals(0, mainVo.getAmountWithTax().compareTo(eFapiaoBillInvoiceActual.getAmountWithTax()));
        assertEquals(0, mainVo.getTaxAmount().compareTo(eFapiaoBillInvoiceActual.getTaxAmount()));
        assertEquals(0, new BigDecimal(mainVo.getTaxRate()).compareTo(eFapiaoBillInvoiceActual.getTaxRate()));

        verifyJdeInvoiceInKerryPlus(mainVo, eFapiaoBillInvoiceActual);

        assertEquals(eFapiaoSyncBillMerge1.getDoco(), eFapiaoBillInvoiceActual.getDoco());
        assertEquals(eFapiaoSyncBillMerge1.getAn8(), eFapiaoBillInvoiceActual.getAn8());
        assertEquals(eFapiaoSyncBillMerge1.getMcu(), eFapiaoBillInvoiceActual.getMcu());
        assertEquals(eFapiaoSyncBillMerge1.getJdeUnit(), eFapiaoBillInvoiceActual.getJdeUnit());
        assertEquals(IF_KCO_JDE_SYNC_MERGE_1, eFapiaoBillInvoiceActual.getKco());
        assertEquals(IF_DCT_JDE_SYNC_MERGE_1, eFapiaoBillInvoiceActual.getBillType());
        assertEquals(IF_DOC_JDE_SYNC_MERGE_1, String.valueOf(eFapiaoBillInvoiceActual.getDoc()));
        assertEquals(IF_SFX_JDE_SYNC_MERGE_1, eFapiaoBillInvoiceActual.getPaymentItem());

        assertEquals(COMPLETED, eFapiaoBillInvoiceActual.getInvoiceRecordStatus());
        assertEquals(NORMAL, eFapiaoBillInvoiceActual.getState());
    }

    @Test
    @DisplayName("JDE同步-合并开票-第二次回调")
    void _04_2_invoiceCallback_jde_sync_merge_invoice_second_callback() throws JsonProcessingException {
        // given
        CallBackKerryInvoiceMainVo mainVo = generateCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo(SALES_BILL_NO_MERGE_2);
        mainVo.setInvoiceCode(INVOICE_CODE_JDE_SYNC_MERGE);
        mainVo.setInvoiceNo(INVOICE_NO_JDE_SYNC_MERGE);
        mainVo.setPurchaserTaxNo(randomString());
        mainVo.setSalesbillAmountWithTax(BigDecimal.ONE);
        mainVo.setSalesbillAmountWithoutTax(BigDecimal.ZERO);
        mainVo.setSalesbillTaxAmount(BigDecimal.TEN);
        mainVo.setSalesbillNumber(2);
        mainVo.setSalesbillType(SALES_BILL_TYPE_JDE_SYNC);
        mainVo.setRedFlag(RED_FLAG_DEFAULT.getIndexStr());
        mainVo.setInvoiceStatus(INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(STATUS_NORMAL);
        mainVo.setSystemOrig(SYSTEM_ORIG_JDE_SYNC);
        mainVo.setSystemOrigType(INTERFACE.getCode());
        mainVo.setTaxRate("0.060");

        EFapiaoSyncBill eFapiaoSyncBillMerge2 = new EFapiaoSyncBill();
        eFapiaoSyncBillMerge2.setMcu(randomString(8));
        eFapiaoSyncBillMerge2.setJdeUnit(randomString());
        eFapiaoSyncBillMerge2.setDoco("271801");
        eFapiaoSyncBillMerge2.setAn8(randomString(8));
        eFapiaoSyncBillMerge2.setSalesBillNo(SALES_BILL_NO_MERGE_2);
        eFapiaoSyncRepository.save(eFapiaoSyncBillMerge2);

        // then
        runAndVerifyInvoiceCallback(mainVo);

        // assert jde
        String selectSql = String.format(SELECT_SQL_FORMAT_JDE, SyncJdeConfig.getJdeBillInvoiceWriteBack()
                , IF_DOC_JDE_SYNC_MERGE_2, STRING_FLAG + IF_DCT_JDE_SYNC_MERGE_2 + STRING_FLAG, STRING_FLAG + IF_KCO_JDE_SYNC_MERGE_2 + STRING_FLAG
                , STRING_FLAG + IF_SFX_JDE_SYNC_MERGE_2 + STRING_FLAG, mainVo.getInvoiceNo());
        Map<String, Object> jdeInvoiceWriteBackActual = jdbcTemplate.queryForMap(selectSql);
        verifyJdeInvoiceWriteBackJde(mainVo, jdeInvoiceWriteBackActual, IF_DOC_JDE_SYNC_MERGE_2, IF_DCT_JDE_SYNC_MERGE_2
                , IF_KCO_JDE_SYNC_MERGE_2, IF_SFX_JDE_SYNC_MERGE_2, JDE_STATUS_NORMAL);

        // assert Kerry+
        List<EFapiaoBillInvoice> eFapiaoBillInvoicesActual = eFapiaoBillInvoiceRepository
                .findAllBySalesBillNo(SALES_BILL_NO_MERGE_2);
        assertFalse(eFapiaoBillInvoicesActual.isEmpty());

        EFapiaoBillInvoice eFapiaoBillInvoiceActual = eFapiaoBillInvoicesActual.stream()
                .filter(e -> INVOICE_NO_JDE_SYNC_MERGE.equals(e.getInvoiceNo()))
                .sorted(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime))
                .findFirst().get();

        assertEquals(SALES_BILL_NO_MERGE_2, eFapiaoBillInvoiceActual.getSalesBillNo());
        assertEquals(0, mainVo.getAmountWithoutTax().compareTo(eFapiaoBillInvoiceActual.getAmountWithoutTax()));
        assertEquals(0, mainVo.getAmountWithTax().compareTo(eFapiaoBillInvoiceActual.getAmountWithTax()));
        assertEquals(0, mainVo.getTaxAmount().compareTo(eFapiaoBillInvoiceActual.getTaxAmount()));
        assertEquals(0, new BigDecimal(mainVo.getTaxRate()).compareTo(eFapiaoBillInvoiceActual.getTaxRate()));

        verifyJdeInvoiceInKerryPlus(mainVo, eFapiaoBillInvoiceActual);

        assertEquals(eFapiaoSyncBillMerge2.getDoco(), eFapiaoBillInvoiceActual.getDoco());
        assertEquals(eFapiaoSyncBillMerge2.getAn8(), eFapiaoBillInvoiceActual.getAn8());
        assertEquals(eFapiaoSyncBillMerge2.getMcu(), eFapiaoBillInvoiceActual.getMcu());
        assertEquals(eFapiaoSyncBillMerge2.getJdeUnit(), eFapiaoBillInvoiceActual.getJdeUnit());
        assertEquals(IF_KCO_JDE_SYNC_MERGE_2, eFapiaoBillInvoiceActual.getKco());
        assertEquals(IF_DCT_JDE_SYNC_MERGE_2, eFapiaoBillInvoiceActual.getBillType());
        assertEquals(IF_DOC_JDE_SYNC_MERGE_2, String.valueOf(eFapiaoBillInvoiceActual.getDoc()));
        assertEquals(IF_SFX_JDE_SYNC_MERGE_2, eFapiaoBillInvoiceActual.getPaymentItem());

        assertEquals(ENTOURAGE_COMPLETED, eFapiaoBillInvoiceActual.getInvoiceRecordStatus());
        assertEquals(NORMAL, eFapiaoBillInvoiceActual.getState());
    }

    @Test
    @DisplayName("JDE同步-合并开票-第一次回调-作废")
    void _04_3_invoiceCallback_jde_sync_merge_invoice_fisrt_cancel_callback() throws JsonProcessingException {
        // given
        CallBackKerryInvoiceMainVo mainVo = generateCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo(SALES_BILL_NO_MERGE_1);
        mainVo.setInvoiceCode(INVOICE_CODE_JDE_SYNC_MERGE);
        mainVo.setInvoiceNo(INVOICE_NO_JDE_SYNC_MERGE);
        mainVo.setPurchaserTaxNo(randomString());
        mainVo.setSalesbillAmountWithTax(BigDecimal.TEN);
        mainVo.setSalesbillAmountWithoutTax(BigDecimal.ONE);
        mainVo.setSalesbillTaxAmount(BigDecimal.ZERO);
        mainVo.setSalesbillNumber(1);
        mainVo.setSalesbillType(SALES_BILL_TYPE_JDE_SYNC);
        mainVo.setRedFlag(RED_FLAG_DEFAULT.getIndexStr());
        mainVo.setInvoiceStatus(INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(STATUS_CANCEL);
        mainVo.setSystemOrig(SYSTEM_ORIG_JDE_SYNC);
        mainVo.setSystemOrigType(INTERFACE.getCode());
        mainVo.setTaxRate("0.060");

        // then
        runAndVerifyInvoiceCallback(mainVo);

        // assert jde
        String selectSql = String.format(SELECT_SQL_FORMAT_JDE, SyncJdeConfig.getJdeBillInvoiceWriteBack()
                , IF_DOC_JDE_SYNC_MERGE_1, STRING_FLAG + IF_DCT_JDE_SYNC_MERGE_1 + STRING_FLAG, STRING_FLAG + IF_KCO_JDE_SYNC_MERGE_1 + STRING_FLAG
                , STRING_FLAG + IF_SFX_JDE_SYNC_MERGE_1 + STRING_FLAG, mainVo.getInvoiceNo());
        Map<String, Object> jdeInvoiceWriteBackActual = jdbcTemplate.queryForMap(selectSql);
        assertEquals("0", jdeInvoiceWriteBackActual.get("IFEV01"));

        // assert Kerry+
        List<EFapiaoBillInvoice> eFapiaoBillInvoicesActual = eFapiaoBillInvoiceRepository
                .findAllBySalesBillNo(SALES_BILL_NO_MERGE_1);
        assertFalse(eFapiaoBillInvoicesActual.isEmpty());

        EFapiaoBillInvoice eFapiaoBillInvoiceActual = eFapiaoBillInvoicesActual.stream()
                .filter(e -> INVOICE_NO_JDE_SYNC_MERGE.equals(e.getInvoiceNo()))
                .sorted(Comparator.comparing(EFapiaoBillInvoice::getUpdatedTime))
                .findFirst().get();

        assertEquals(InvoiceState.CANCELLED, eFapiaoBillInvoiceActual.getState());
    }

    @Test
    @DisplayName("JDE同步-合并开票-第二次回调-作废")
    void _04_4_invoiceCallback_jde_sync_merge_invoice_second_cancel_callback() throws JsonProcessingException {
        // given
        CallBackKerryInvoiceMainVo mainVo = generateCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo(SALES_BILL_NO_MERGE_2);
        mainVo.setInvoiceCode(INVOICE_CODE_JDE_SYNC_MERGE);
        mainVo.setInvoiceNo(INVOICE_NO_JDE_SYNC_MERGE);
        mainVo.setPurchaserTaxNo(randomString());
        mainVo.setSalesbillAmountWithTax(BigDecimal.ONE);
        mainVo.setSalesbillAmountWithoutTax(BigDecimal.ZERO);
        mainVo.setSalesbillTaxAmount(BigDecimal.TEN);
        mainVo.setSalesbillNumber(2);
        mainVo.setSalesbillType(SALES_BILL_TYPE_JDE_SYNC);
        mainVo.setRedFlag(RED_FLAG_DEFAULT.getIndexStr());
        mainVo.setInvoiceStatus(INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(STATUS_CANCEL);
        mainVo.setSystemOrig(SYSTEM_ORIG_JDE_SYNC);
        mainVo.setSystemOrigType(INTERFACE.getCode());
        mainVo.setTaxRate("0.060");

        // then
        runAndVerifyInvoiceCallback(mainVo);

        // assert jde
        String selectSql = String.format(SELECT_SQL_FORMAT_JDE, SyncJdeConfig.getJdeBillInvoiceWriteBack()
                , IF_DOC_JDE_SYNC_MERGE_2, STRING_FLAG + IF_DCT_JDE_SYNC_MERGE_2 + STRING_FLAG, STRING_FLAG + IF_KCO_JDE_SYNC_MERGE_2 + STRING_FLAG
                , STRING_FLAG + IF_SFX_JDE_SYNC_MERGE_2 + STRING_FLAG, mainVo.getInvoiceNo());
        Map<String, Object> jdeInvoiceWriteBackActual = jdbcTemplate.queryForMap(selectSql);
        assertEquals("0", jdeInvoiceWriteBackActual.get("IFEV01"));

        // assert Kerry+
        List<EFapiaoBillInvoice> eFapiaoBillInvoicesActual = eFapiaoBillInvoiceRepository
                .findAllBySalesBillNo(SALES_BILL_NO_MERGE_2);
        assertFalse(eFapiaoBillInvoicesActual.isEmpty());

        EFapiaoBillInvoice eFapiaoBillInvoiceActual = eFapiaoBillInvoicesActual.stream()
                .filter(e -> INVOICE_NO_JDE_SYNC_MERGE.equals(e.getInvoiceNo()))
                .sorted(Comparator.comparing(EFapiaoBillInvoice::getUpdatedTime))
                .findFirst().get();

        assertEquals(InvoiceState.CANCELLED, eFapiaoBillInvoiceActual.getState());
    }

    /**
     * 组合开票：回调时主要是业务单号和业务单金额不同
     */
    @Test
    @DisplayName("JDE同步-组合开票-第一次回调")
    void _05_1_invoiceCallback_jde_sync_combine_invoice_first_callback() throws JsonProcessingException {
        // given
        CallBackKerryInvoiceMainVo mainVo = generateCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo(SALES_BILL_NO_COMBINE_1);
        mainVo.setInvoiceCode(INVOICE_CODE_JDE_SYNC_COMBINE);
        mainVo.setInvoiceNo(INVOICE_NO_JDE_SYNC_COMBINE);
        mainVo.setPurchaserTaxNo(randomString());
        mainVo.setSalesbillAmountWithTax(BigDecimal.TEN);
        mainVo.setSalesbillAmountWithoutTax(BigDecimal.ONE);
        mainVo.setSalesbillTaxAmount(BigDecimal.ZERO);
        mainVo.setSalesbillNumber(1);
        mainVo.setSalesbillType(SALES_BILL_TYPE_JDE_SYNC);
        mainVo.setRedFlag(RED_FLAG_DEFAULT.getIndexStr());
        mainVo.setInvoiceStatus(INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(STATUS_NORMAL);
        mainVo.setSystemOrig(SYSTEM_ORIG_JDE_SYNC);
        mainVo.setSystemOrigType(INTERFACE.getCode());
        mainVo.setTaxRate("0.060,0.130");

        EFapiaoSyncBill eFapiaoSyncBillCombine1 = new EFapiaoSyncBill();
        eFapiaoSyncBillCombine1.setMcu("11112222");
        eFapiaoSyncBillCombine1.setJdeUnit("501");
        eFapiaoSyncBillCombine1.setDoco("271800");
        eFapiaoSyncBillCombine1.setAn8(randomString(8));
        eFapiaoSyncBillCombine1.setSalesBillNo(SALES_BILL_NO_COMBINE_1);
        eFapiaoSyncRepository.save(eFapiaoSyncBillCombine1);

        // then
        runAndVerifyInvoiceCallback(mainVo);

        // assert jde
        String selectSql = String.format(SELECT_SQL_FORMAT_JDE, SyncJdeConfig.getJdeBillInvoiceWriteBack()
                , IF_DOC_JDE_SYNC_COMBINE_1, STRING_FLAG + IF_DCT_JDE_SYNC_COMBINE_1 + STRING_FLAG, STRING_FLAG + IF_KCO_JDE_SYNC_COMBINE_1 + STRING_FLAG
                , STRING_FLAG + IF_SFX_JDE_SYNC_COMBINE_1 + STRING_FLAG, mainVo.getInvoiceNo());
        Map<String, Object> jdeInvoiceWriteBackActual = jdbcTemplate.queryForMap(selectSql);
        verifyJdeInvoiceWriteBackJde(mainVo, jdeInvoiceWriteBackActual, IF_DOC_JDE_SYNC_COMBINE_1, IF_DCT_JDE_SYNC_COMBINE_1
                , IF_KCO_JDE_SYNC_COMBINE_1, IF_SFX_JDE_SYNC_COMBINE_1, JDE_STATUS_NORMAL);

        // assert Kerry+
        List<EFapiaoBillInvoice> eFapiaoBillInvoicesActual = eFapiaoBillInvoiceRepository
                .findAllBySalesBillNo(SALES_BILL_NO_COMBINE_1);
        assertFalse(eFapiaoBillInvoicesActual.isEmpty());

        EFapiaoBillInvoice eFapiaoBillInvoiceActual = eFapiaoBillInvoicesActual.stream()
                .filter(e -> INVOICE_NO_JDE_SYNC_COMBINE.equals(e.getInvoiceNo()))
                .sorted(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime))
                .findFirst().get();

        assertEquals(0, new BigDecimal("-0.01").compareTo(eFapiaoBillInvoiceActual.getTaxRate()));

        assertEquals(SALES_BILL_NO_COMBINE_1, eFapiaoBillInvoiceActual.getSalesBillNo());
        assertEquals(0, mainVo.getAmountWithoutTax().compareTo(eFapiaoBillInvoiceActual.getAmountWithoutTax()));
        assertEquals(0, mainVo.getAmountWithTax().compareTo(eFapiaoBillInvoiceActual.getAmountWithTax()));
        assertEquals(0, mainVo.getTaxAmount().compareTo(eFapiaoBillInvoiceActual.getTaxAmount()));

        verifyJdeInvoiceInKerryPlus(mainVo, eFapiaoBillInvoiceActual);

        assertEquals(eFapiaoSyncBillCombine1.getMcu(), eFapiaoBillInvoiceActual.getMcu());
        assertEquals(eFapiaoSyncBillCombine1.getJdeUnit(), eFapiaoBillInvoiceActual.getJdeUnit());
        assertEquals(eFapiaoSyncBillCombine1.getDoco(), eFapiaoBillInvoiceActual.getDoco());
        assertEquals(eFapiaoSyncBillCombine1.getAn8(), eFapiaoBillInvoiceActual.getAn8());
        assertEquals(IF_KCO_JDE_SYNC_COMBINE_1, eFapiaoBillInvoiceActual.getKco());
        assertEquals(IF_DCT_JDE_SYNC_COMBINE_1, eFapiaoBillInvoiceActual.getBillType());
        assertEquals(IF_DOC_JDE_SYNC_COMBINE_1, String.valueOf(eFapiaoBillInvoiceActual.getDoc()));
        assertEquals(IF_SFX_JDE_SYNC_COMBINE_1, eFapiaoBillInvoiceActual.getPaymentItem());

        assertEquals(COMPLETED, eFapiaoBillInvoiceActual.getInvoiceRecordStatus());
        assertEquals(NORMAL, eFapiaoBillInvoiceActual.getState());
    }

    @Test
    @DisplayName("JDE同步-组合开票-第二次回调")
    void _05_2_invoiceCallback_jde_sync_combine_invoice_second_callback() throws JsonProcessingException {
        // given
        CallBackKerryInvoiceMainVo mainVo = generateCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo(SALES_BILL_NO_COMBINE_2);
        mainVo.setInvoiceCode(INVOICE_CODE_JDE_SYNC_COMBINE);
        mainVo.setInvoiceNo(INVOICE_NO_JDE_SYNC_COMBINE);
        mainVo.setPurchaserTaxNo(randomString());
        mainVo.setSalesbillAmountWithTax(BigDecimal.ONE);
        mainVo.setSalesbillAmountWithoutTax(BigDecimal.ZERO);
        mainVo.setSalesbillTaxAmount(BigDecimal.TEN);
        mainVo.setSalesbillNumber(2);
        mainVo.setSalesbillType(SALES_BILL_TYPE_JDE_SYNC);
        mainVo.setRedFlag(RED_FLAG_DEFAULT.getIndexStr());
        mainVo.setInvoiceStatus(INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(STATUS_NORMAL);
        mainVo.setSystemOrig(SYSTEM_ORIG_JDE_SYNC);
        mainVo.setSystemOrigType(INTERFACE.getCode());
        mainVo.setTaxRate("0.060,0.130");

        EFapiaoSyncBill eFapiaoSyncBillCombine1 = new EFapiaoSyncBill();
        eFapiaoSyncBillCombine1.setMcu("33334444");
        eFapiaoSyncBillCombine1.setJdeUnit("502");
        eFapiaoSyncBillCombine1.setDoco("271800");
        eFapiaoSyncBillCombine1.setAn8(randomString(8));
        eFapiaoSyncBillCombine1.setSalesBillNo(SALES_BILL_NO_COMBINE_2);
        eFapiaoSyncRepository.save(eFapiaoSyncBillCombine1);

        // then
        runAndVerifyInvoiceCallback(mainVo);

        // assert jde
        String selectSql = String.format(SELECT_SQL_FORMAT_JDE, SyncJdeConfig.getJdeBillInvoiceWriteBack()
                , IF_DOC_JDE_SYNC_COMBINE_2, STRING_FLAG + IF_DCT_JDE_SYNC_COMBINE_2 + STRING_FLAG, STRING_FLAG + IF_KCO_JDE_SYNC_COMBINE_2 + STRING_FLAG
                , STRING_FLAG + IF_SFX_JDE_SYNC_COMBINE_2 + STRING_FLAG, mainVo.getInvoiceNo());
        Map<String, Object> jdeInvoiceWriteBackActual = jdbcTemplate.queryForMap(selectSql);
        verifyJdeInvoiceWriteBackJde(mainVo, jdeInvoiceWriteBackActual, IF_DOC_JDE_SYNC_COMBINE_2, IF_DCT_JDE_SYNC_COMBINE_2
                , IF_KCO_JDE_SYNC_COMBINE_2, IF_SFX_JDE_SYNC_COMBINE_2, JDE_STATUS_NORMAL);

        // assert Kerry+
        List<EFapiaoBillInvoice> eFapiaoBillInvoicesActual = eFapiaoBillInvoiceRepository
                .findAllBySalesBillNo(SALES_BILL_NO_COMBINE_2);
        assertFalse(eFapiaoBillInvoicesActual.isEmpty());

        EFapiaoBillInvoice eFapiaoBillInvoiceActual = eFapiaoBillInvoicesActual.stream()
                .filter(e -> INVOICE_NO_JDE_SYNC_COMBINE.equals(e.getInvoiceNo()))
                .sorted(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime))
                .findFirst().get();

        assertEquals(0, new BigDecimal("-0.01").compareTo(eFapiaoBillInvoiceActual.getTaxRate()));

        assertEquals(SALES_BILL_NO_COMBINE_2, eFapiaoBillInvoiceActual.getSalesBillNo());
        assertEquals(0, mainVo.getAmountWithoutTax().compareTo(eFapiaoBillInvoiceActual.getAmountWithoutTax()));
        assertEquals(0, mainVo.getAmountWithTax().compareTo(eFapiaoBillInvoiceActual.getAmountWithTax()));
        assertEquals(0, mainVo.getTaxAmount().compareTo(eFapiaoBillInvoiceActual.getTaxAmount()));

        verifyJdeInvoiceInKerryPlus(mainVo, eFapiaoBillInvoiceActual);

        assertEquals(eFapiaoSyncBillCombine1.getMcu(), eFapiaoBillInvoiceActual.getMcu());
        assertEquals(eFapiaoSyncBillCombine1.getJdeUnit(), eFapiaoBillInvoiceActual.getJdeUnit());
        assertEquals(eFapiaoSyncBillCombine1.getDoco(), eFapiaoBillInvoiceActual.getDoco());
        assertEquals(eFapiaoSyncBillCombine1.getAn8(), eFapiaoBillInvoiceActual.getAn8());
        assertEquals(IF_KCO_JDE_SYNC_COMBINE_2, eFapiaoBillInvoiceActual.getKco());
        assertEquals(IF_DCT_JDE_SYNC_COMBINE_2, eFapiaoBillInvoiceActual.getBillType());
        assertEquals(IF_DOC_JDE_SYNC_COMBINE_2, String.valueOf(eFapiaoBillInvoiceActual.getDoc()));
        assertEquals(IF_SFX_JDE_SYNC_COMBINE_2, eFapiaoBillInvoiceActual.getPaymentItem());

        assertEquals(ENTOURAGE_COMPLETED, eFapiaoBillInvoiceActual.getInvoiceRecordStatus());
        assertEquals(NORMAL, eFapiaoBillInvoiceActual.getState());

        /**
         * 验证mcu、jdeUnit字段连接场景
         */
        List<EFapiaoBillInvoice> eFapiaoBillInvoicesActual1 = eFapiaoBillInvoiceRepository
                .findAllBySalesBillNo(SALES_BILL_NO_COMBINE_1);
        assertFalse(eFapiaoBillInvoicesActual1.isEmpty());

        EFapiaoBillInvoice eFapiaoBillInvoiceActual1 = eFapiaoBillInvoicesActual1.stream()
                .filter(e -> INVOICE_NO_JDE_SYNC_COMBINE.equals(e.getInvoiceNo()))
                .sorted(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime))
                .findFirst().get();

        assertEquals(0, new BigDecimal("-0.01").compareTo(eFapiaoBillInvoiceActual.getTaxRate()));
        assertEquals("11112222,33334444", eFapiaoBillInvoiceActual1.getMcu());
        assertEquals("501,502", eFapiaoBillInvoiceActual1.getJdeUnit());
    }

    @Test
    @DisplayName("JDE同步-组合开票-第一次回调-蓝票标记被红冲")
    void _05_3_invoiceCallback_jde_sync_combine_invoice_first_set_red_flag_callback() throws JsonProcessingException {
        CallBackKerryInvoiceMainVo mainVo = generateCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo(SALES_BILL_NO_COMBINE_1);
        mainVo.setInvoiceCode(INVOICE_CODE_JDE_SYNC_COMBINE);
        mainVo.setInvoiceNo(INVOICE_NO_JDE_SYNC_COMBINE);
        mainVo.setPurchaserTaxNo(randomString());
        mainVo.setSalesbillAmountWithTax(BigDecimal.TEN);
        mainVo.setSalesbillAmountWithoutTax(BigDecimal.ONE);
        mainVo.setSalesbillTaxAmount(BigDecimal.ZERO);
        mainVo.setSalesbillNumber(1);
        mainVo.setSalesbillType(SALES_BILL_TYPE_JDE_SYNC);
        mainVo.setRedFlag(RED_FLAG_RED_STATUS.getIndexStr());
        mainVo.setInvoiceStatus(INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(STATUS_NORMAL);
        mainVo.setSystemOrig(SYSTEM_ORIG_JDE_SYNC);
        mainVo.setSystemOrigType(INTERFACE.getCode());
        mainVo.setTaxRate("0.060,0.130");

        // then
        runAndVerifyInvoiceCallback(mainVo);

        // assert Kerry+
        List<EFapiaoBillInvoice> eFapiaoBillInvoicesActual = eFapiaoBillInvoiceRepository
                .findAllBySalesBillNo(SALES_BILL_NO_COMBINE_1);
        assertFalse(eFapiaoBillInvoicesActual.isEmpty());

        EFapiaoBillInvoice eFapiaoBillInvoiceActual = eFapiaoBillInvoicesActual.stream()
                .filter(e -> INVOICE_NO_JDE_SYNC_COMBINE.equals(e.getInvoiceNo()))
                .sorted(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime))
                .findFirst().get();

        assertEquals(InvoiceState.RED_STATUS, eFapiaoBillInvoiceActual.getState());
    }

    @Test
    @DisplayName("JDE同步-组合开票-第二次回调-蓝票标记被红冲")
    void _05_4_invoiceCallback_jde_sync_combine_invoice_second_set_red_flag_callback() throws JsonProcessingException {
        CallBackKerryInvoiceMainVo mainVo = generateCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo(SALES_BILL_NO_COMBINE_2);
        mainVo.setInvoiceCode(INVOICE_CODE_JDE_SYNC_COMBINE);
        mainVo.setInvoiceNo(INVOICE_NO_JDE_SYNC_COMBINE);
        mainVo.setPurchaserTaxNo(randomString());
        mainVo.setSalesbillAmountWithTax(BigDecimal.ONE);
        mainVo.setSalesbillAmountWithoutTax(BigDecimal.ZERO);
        mainVo.setSalesbillTaxAmount(BigDecimal.TEN);
        mainVo.setSalesbillNumber(2);
        mainVo.setSalesbillType(SALES_BILL_TYPE_JDE_SYNC);
        mainVo.setRedFlag(RED_FLAG_RED_STATUS.getIndexStr());
        mainVo.setInvoiceStatus(INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(STATUS_NORMAL);
        mainVo.setSystemOrig(SYSTEM_ORIG_JDE_SYNC);
        mainVo.setSystemOrigType(INTERFACE.getCode());
        mainVo.setTaxRate("0.060,0.130");

        // then
        runAndVerifyInvoiceCallback(mainVo);

        // assert Kerry+
        List<EFapiaoBillInvoice> eFapiaoBillInvoicesActual = eFapiaoBillInvoiceRepository
                .findAllBySalesBillNo(SALES_BILL_NO_COMBINE_2);
        assertFalse(eFapiaoBillInvoicesActual.isEmpty());

        EFapiaoBillInvoice eFapiaoBillInvoiceActual = eFapiaoBillInvoicesActual.stream()
                .filter(e -> INVOICE_NO_JDE_SYNC_COMBINE.equals(e.getInvoiceNo()))
                .sorted(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime))
                .findFirst().get();

        assertEquals(InvoiceState.RED_STATUS, eFapiaoBillInvoiceActual.getState());
    }

    /**
     * 组合开票-开红冲票：回调时主要是业务单号和业务单金额不同
     */
    @Test
    @DisplayName("JDE同步-组合开票-第一次回调-开红冲票")
    void _05_5_invoiceCallback_jde_sync_combine_red_flag_invoice_first_callback() throws JsonProcessingException {
        // given
        CallBackKerryInvoiceMainVo mainVo = generateCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo(SALES_BILL_NO_COMBINE_1);
        mainVo.setInvoiceCode(INVOICE_CODE_RED_FLAG_JDE_SYNC_COMBINE);
        mainVo.setInvoiceNo(INVOICE_NO_RED_FLAG_JDE_SYNC_COMBINE);
        mainVo.setPurchaserTaxNo(randomString());
        mainVo.setSalesbillAmountWithTax(BigDecimal.ZERO.subtract(BigDecimal.TEN));
        mainVo.setSalesbillAmountWithoutTax(BigDecimal.ZERO.subtract(BigDecimal.ONE));
        mainVo.setSalesbillTaxAmount(BigDecimal.ZERO.subtract(BigDecimal.ZERO));
        mainVo.setSalesbillNumber(1);
        mainVo.setSalesbillType(SALES_BILL_TYPE_JDE_SYNC);
        mainVo.setRedFlag(RED_FLAG_RED_INVOICE.getIndexStr());
        mainVo.setInvoiceStatus(INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(STATUS_NORMAL);
        mainVo.setSystemOrig(SYSTEM_ORIG_JDE_SYNC);
        mainVo.setSystemOrigType(INTERFACE.getCode());
        mainVo.setTaxRate("0.060,0.130");
        mainVo.setMakingReason(randomString());

        List<CallBackKerryInvoiceDetailVo> callBackKerryInvoiceDetailVos = mainVo.getDetailVos();
        callBackKerryInvoiceDetailVos.forEach(callBackKerryInvoiceDetailVo -> {
            callBackKerryInvoiceDetailVo.setQuantity(BigDecimal.valueOf(-1.0));
            callBackKerryInvoiceDetailVo.setAmountWithTax(mainVo.getAmountWithTax());
            callBackKerryInvoiceDetailVo.setAmountWithoutTax(mainVo.getAmountWithoutTax());
            callBackKerryInvoiceDetailVo.setTaxAmount(mainVo.getTaxAmount());
        });

        List<EFapiaoSyncBill> eFapiaoSyncBills = eFapiaoSyncRepository.findAll();
        EFapiaoSyncBill eFapiaoSyncBill = eFapiaoSyncBills.stream().filter(e -> (SALES_BILL_NO_COMBINE_1)
                .equals(e.getSalesBillNo())).findFirst().get();

        // then
        runAndVerifyInvoiceCallback(mainVo);

        // assert jde
        String selectSql = String.format(SELECT_SQL_FORMAT_JDE, SyncJdeConfig.getJdeBillInvoiceWriteBack()
                , IF_DOC_JDE_SYNC_COMBINE_1, STRING_FLAG + IF_DCT_JDE_SYNC_COMBINE_1 + STRING_FLAG, STRING_FLAG + IF_KCO_JDE_SYNC_COMBINE_1 + STRING_FLAG
                , STRING_FLAG + IF_SFX_JDE_SYNC_COMBINE_1 + STRING_FLAG, mainVo.getInvoiceNo());
        Map<String, Object> jdeInvoiceWriteBackActual = jdbcTemplate.queryForMap(selectSql);
        verifyJdeInvoiceWriteBackJde(mainVo, jdeInvoiceWriteBackActual, IF_DOC_JDE_SYNC_COMBINE_1, IF_DCT_JDE_SYNC_COMBINE_1
                , IF_KCO_JDE_SYNC_COMBINE_1, IF_SFX_JDE_SYNC_COMBINE_1, JDE_STATUS_RED_FLAG);

        // assert Kerry+
        List<EFapiaoBillInvoice> eFapiaoBillInvoicesActual = eFapiaoBillInvoiceRepository
                .findAllBySalesBillNo(SALES_BILL_NO_COMBINE_1);
        assertFalse(eFapiaoBillInvoicesActual.isEmpty());

        EFapiaoBillInvoice eFapiaoBillInvoiceActual = eFapiaoBillInvoicesActual.stream()
                .filter(e -> INVOICE_NO_RED_FLAG_JDE_SYNC_COMBINE.equals(e.getInvoiceNo()))
                .sorted(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime))
                .findFirst().get();

        assertEquals(0, new BigDecimal("-0.01").compareTo(eFapiaoBillInvoiceActual.getTaxRate()));

        assertEquals(SALES_BILL_NO_COMBINE_1, eFapiaoBillInvoiceActual.getSalesBillNo());
        assertEquals(0, mainVo.getAmountWithoutTax().compareTo(eFapiaoBillInvoiceActual.getAmountWithoutTax()));
        assertEquals(0, mainVo.getAmountWithTax().compareTo(eFapiaoBillInvoiceActual.getAmountWithTax()));
        assertEquals(0, mainVo.getTaxAmount().compareTo(eFapiaoBillInvoiceActual.getTaxAmount()));

        verifyJdeInvoiceInKerryPlus(mainVo, eFapiaoBillInvoiceActual);

        assertEquals(eFapiaoSyncBill.getMcu(), eFapiaoBillInvoiceActual.getMcu());
        assertEquals(eFapiaoSyncBill.getJdeUnit(), eFapiaoBillInvoiceActual.getJdeUnit());
        assertEquals(eFapiaoSyncBill.getDoco(), eFapiaoBillInvoiceActual.getDoco());
        assertEquals(eFapiaoSyncBill.getAn8(), eFapiaoBillInvoiceActual.getAn8());
        assertEquals(IF_KCO_JDE_SYNC_COMBINE_1, eFapiaoBillInvoiceActual.getKco());
        assertEquals(IF_DCT_JDE_SYNC_COMBINE_1, eFapiaoBillInvoiceActual.getBillType());
        assertEquals(IF_DOC_JDE_SYNC_COMBINE_1, String.valueOf(eFapiaoBillInvoiceActual.getDoc()));
        assertEquals(IF_SFX_JDE_SYNC_COMBINE_1, eFapiaoBillInvoiceActual.getPaymentItem());

        assertEquals(COMPLETED, eFapiaoBillInvoiceActual.getInvoiceRecordStatus());
        assertEquals(RED_LETTER, eFapiaoBillInvoiceActual.getState());
    }

    @Test
    @DisplayName("JDE同步-组合开票-开红冲票-第二次回调")
    void _05_6_invoiceCallback_jde_sync_combine_red_flag_invoice_second_callback() throws JsonProcessingException {
        // given
        CallBackKerryInvoiceMainVo mainVo = generateCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo(SALES_BILL_NO_COMBINE_2);
        mainVo.setInvoiceCode(INVOICE_CODE_RED_FLAG_JDE_SYNC_COMBINE);
        mainVo.setInvoiceNo(INVOICE_NO_RED_FLAG_JDE_SYNC_COMBINE);
        mainVo.setPurchaserTaxNo(randomString());
        mainVo.setSalesbillAmountWithTax(BigDecimal.ZERO.subtract(BigDecimal.ONE));
        mainVo.setSalesbillAmountWithoutTax(BigDecimal.ZERO.subtract(BigDecimal.ZERO));
        mainVo.setSalesbillTaxAmount(BigDecimal.ZERO.subtract(BigDecimal.TEN));
        mainVo.setSalesbillNumber(2);
        mainVo.setSalesbillType(SALES_BILL_TYPE_JDE_SYNC);
        mainVo.setRedFlag(RED_FLAG_RED_INVOICE.getIndexStr());
        mainVo.setInvoiceStatus(INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(STATUS_NORMAL);
        mainVo.setSystemOrig(SYSTEM_ORIG_JDE_SYNC);
        mainVo.setSystemOrigType(INTERFACE.getCode());
        mainVo.setTaxRate("0.060,0.130");

        List<EFapiaoSyncBill> eFapiaoSyncBills = eFapiaoSyncRepository.findAll();
        EFapiaoSyncBill eFapiaoSyncBill = eFapiaoSyncBills.stream().filter(e -> (SALES_BILL_NO_COMBINE_2)
                .equals(e.getSalesBillNo())).findFirst().get();

        // then
        runAndVerifyInvoiceCallback(mainVo);

        // assert jde
        String selectSql = String.format(SELECT_SQL_FORMAT_JDE, SyncJdeConfig.getJdeBillInvoiceWriteBack()
                , IF_DOC_JDE_SYNC_COMBINE_2, STRING_FLAG + IF_DCT_JDE_SYNC_COMBINE_2 + STRING_FLAG, STRING_FLAG + IF_KCO_JDE_SYNC_COMBINE_2 + STRING_FLAG
                , STRING_FLAG + IF_SFX_JDE_SYNC_COMBINE_2 + STRING_FLAG, mainVo.getInvoiceNo());
        Map<String, Object> jdeInvoiceWriteBackActual = jdbcTemplate.queryForMap(selectSql);
        verifyJdeInvoiceWriteBackJde(mainVo, jdeInvoiceWriteBackActual, IF_DOC_JDE_SYNC_COMBINE_2, IF_DCT_JDE_SYNC_COMBINE_2
                , IF_KCO_JDE_SYNC_COMBINE_2, IF_SFX_JDE_SYNC_COMBINE_2, JDE_STATUS_RED_FLAG);

        // assert Kerry+
        List<EFapiaoBillInvoice> eFapiaoBillInvoicesActual = eFapiaoBillInvoiceRepository
                .findAllBySalesBillNo(SALES_BILL_NO_COMBINE_2);
        assertFalse(eFapiaoBillInvoicesActual.isEmpty());

        EFapiaoBillInvoice eFapiaoBillInvoiceActual = eFapiaoBillInvoicesActual.stream()
                .filter(e -> INVOICE_NO_RED_FLAG_JDE_SYNC_COMBINE.equals(e.getInvoiceNo()))
                .sorted(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime))
                .findFirst().get();

        assertEquals(0, new BigDecimal("-0.01").compareTo(eFapiaoBillInvoiceActual.getTaxRate()));

        assertEquals(SALES_BILL_NO_COMBINE_2, eFapiaoBillInvoiceActual.getSalesBillNo());
        assertEquals(0, mainVo.getAmountWithoutTax().compareTo(eFapiaoBillInvoiceActual.getAmountWithoutTax()));
        assertEquals(0, mainVo.getAmountWithTax().compareTo(eFapiaoBillInvoiceActual.getAmountWithTax()));
        assertEquals(0, mainVo.getTaxAmount().compareTo(eFapiaoBillInvoiceActual.getTaxAmount()));

        verifyJdeInvoiceInKerryPlus(mainVo, eFapiaoBillInvoiceActual);

        assertEquals(eFapiaoSyncBill.getMcu(), eFapiaoBillInvoiceActual.getMcu());
        assertEquals(eFapiaoSyncBill.getJdeUnit(), eFapiaoBillInvoiceActual.getJdeUnit());
        assertEquals(eFapiaoSyncBill.getDoco(), eFapiaoBillInvoiceActual.getDoco());
        assertEquals(eFapiaoSyncBill.getAn8(), eFapiaoBillInvoiceActual.getAn8());
        assertEquals(IF_KCO_JDE_SYNC_COMBINE_2, eFapiaoBillInvoiceActual.getKco());
        assertEquals(IF_DCT_JDE_SYNC_COMBINE_2, eFapiaoBillInvoiceActual.getBillType());
        assertEquals(IF_DOC_JDE_SYNC_COMBINE_2, String.valueOf(eFapiaoBillInvoiceActual.getDoc()));
        assertEquals(IF_SFX_JDE_SYNC_COMBINE_2, eFapiaoBillInvoiceActual.getPaymentItem());

        assertEquals(ENTOURAGE_COMPLETED, eFapiaoBillInvoiceActual.getInvoiceRecordStatus());
        assertEquals(RED_LETTER, eFapiaoBillInvoiceActual.getState());

        /**
         * 验证mcu、jdeUnit字段连接场景
         */
        List<EFapiaoBillInvoice> eFapiaoBillInvoicesActual1 = eFapiaoBillInvoiceRepository
                .findAllBySalesBillNo(SALES_BILL_NO_COMBINE_1);
        assertFalse(eFapiaoBillInvoicesActual1.isEmpty());

        EFapiaoBillInvoice eFapiaoBillInvoiceActual1 = eFapiaoBillInvoicesActual1.stream()
                .filter(e -> INVOICE_NO_RED_FLAG_JDE_SYNC_COMBINE.equals(e.getInvoiceNo()))
                .sorted(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime))
                .findFirst().get();

        assertEquals(0, new BigDecimal("-0.01").compareTo(eFapiaoBillInvoiceActual.getTaxRate()));
        assertEquals("11112222,33334444", eFapiaoBillInvoiceActual1.getMcu());
        assertEquals("501,502", eFapiaoBillInvoiceActual1.getJdeUnit());
    }

    @Test
    @DisplayName("excel导入-正常开票（非拆票、非合并/组合)")
    void _06_1_invoiceCallback_excel_import_ordinary_callback() throws JsonProcessingException {
        // given
        CallBackKerryInvoiceMainVo mainVo = generateCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo(SALES_BILL_NO_IMPORT_MOCK);
        mainVo.setInvoiceCode(INVOICE_CODE_EXCEL_IMPORT);
        mainVo.setInvoiceNo(INVOICE_NO_EXCEL_IMPORT);
        mainVo.setPurchaserTaxNo(randomString());
        mainVo.setSalesbillNumber(1);
        mainVo.setSalesbillType(SALES_BILL_TYPE_EXCEL_IMPORT);
        mainVo.setRedFlag(RED_FLAG_DEFAULT.getIndexStr());
        mainVo.setInvoiceStatus(INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(STATUS_NORMAL);
        mainVo.setSystemOrig(SYSTEM_ORIG_EXCEL_IMPORT);
        mainVo.setSystemOrigType(PAGE_IMPORT.getCode());
        mainVo.setTaxRate("0.060");
        mainVo.setBusinessType(randomString()); // 票易通ex1是bu
        mainVo.setExt2(randomString()); // 票易通ex3是合同号
        mainVo.setExt3(randomString()); // 票易通ex4是付款人

        // then
        runAndVerifyInvoiceCallback(mainVo);

        // assert Kerry+
        List<EFapiaoBillInvoice> eFapiaoBillInvoicesActual = eFapiaoBillInvoiceRepository
                .findAllBySalesBillNo(SALES_BILL_NO_IMPORT_MOCK);
        assertFalse(eFapiaoBillInvoicesActual.isEmpty());

        EFapiaoBillInvoice eFapiaoBillInvoiceActual = eFapiaoBillInvoicesActual.stream()
                .filter(e -> INVOICE_NO_EXCEL_IMPORT.equals(e.getInvoiceNo()))
                .sorted(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime))
                .findFirst().get();

        assertEquals(SALES_BILL_NO_IMPORT_MOCK, eFapiaoBillInvoiceActual.getSalesBillNo());
        verifyImportInvoice(mainVo, eFapiaoBillInvoiceActual);

        assertEquals(COMPLETED, eFapiaoBillInvoiceActual.getInvoiceRecordStatus());
        assertEquals(NORMAL, eFapiaoBillInvoiceActual.getState());
    }

    @Test
    @DisplayName("excel导入-正常开票（非拆票、非合并/组合-拆票")
    void _06_2_invoiceCallback_excel_import_ordinary_split_callback() throws JsonProcessingException {
        // given
        CallBackKerryInvoiceMainVo mainVo = generateCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo(SALES_BILL_NO_IMPORT_MOCK);
        mainVo.setInvoiceCode(INVOICE_CODE_EXCEL_IMPORT);
        mainVo.setInvoiceNo(INVOICE_NO_EXCEL_IMPORT_SPLIT);
        mainVo.setPurchaserTaxNo(randomString());
        mainVo.setSalesbillNumber(1);
        mainVo.setSalesbillType(SALES_BILL_TYPE_EXCEL_IMPORT);
        mainVo.setRedFlag(RED_FLAG_DEFAULT.getIndexStr());
        mainVo.setInvoiceStatus(INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(STATUS_NORMAL);
        mainVo.setSystemOrig(SYSTEM_ORIG_EXCEL_IMPORT);
        mainVo.setSystemOrigType(PAGE_IMPORT.getCode());
        mainVo.setTaxRate("0.060");
        mainVo.setBusinessType(randomString());
        mainVo.setExt2(randomString());
        mainVo.setExt3(randomString());

        // then
        runAndVerifyInvoiceCallback(mainVo);

        // assert Kerry+
        List<EFapiaoBillInvoice> eFapiaoBillInvoicesActual = eFapiaoBillInvoiceRepository
                .findAllBySalesBillNo(SALES_BILL_NO_IMPORT_MOCK);
        assertFalse(eFapiaoBillInvoicesActual.isEmpty());

        EFapiaoBillInvoice eFapiaoBillInvoiceActual = eFapiaoBillInvoicesActual.stream()
                .filter(e -> INVOICE_NO_EXCEL_IMPORT_SPLIT.equals(e.getInvoiceNo()))
                .sorted(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime))
                .findFirst().get();

        assertEquals(SALES_BILL_NO_IMPORT_MOCK, eFapiaoBillInvoiceActual.getSalesBillNo());
        verifyImportInvoice(mainVo, eFapiaoBillInvoiceActual);

        assertEquals(COMPLETED, eFapiaoBillInvoiceActual.getInvoiceRecordStatus());
        assertEquals(NORMAL, eFapiaoBillInvoiceActual.getState());
    }

    @Test
    @DisplayName("excel导入-作废")
    void _06_3_invoiceCallback_excel_import_cancel_callback() throws JsonProcessingException {
        // given
        CallBackKerryInvoiceMainVo mainVo = generateCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo(SALES_BILL_NO_IMPORT_MOCK);
        mainVo.setInvoiceCode(INVOICE_CODE_EXCEL_IMPORT);
        mainVo.setInvoiceNo(INVOICE_NO_EXCEL_IMPORT_SPLIT);
        mainVo.setPurchaserTaxNo(randomString());
        mainVo.setSalesbillNumber(1);
        mainVo.setSalesbillType(SALES_BILL_TYPE_EXCEL_IMPORT);
        mainVo.setRedFlag(RED_FLAG_DEFAULT.getIndexStr());
        mainVo.setInvoiceStatus(INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(STATUS_CANCEL);
        mainVo.setSystemOrig(SYSTEM_ORIG_EXCEL_IMPORT);
        mainVo.setSystemOrigType(PAGE_IMPORT.getCode());
        mainVo.setTaxRate("0.060");
        mainVo.setBusinessType(randomString());
        mainVo.setExt2(randomString());
        mainVo.setExt3(randomString());

        // then
        runAndVerifyInvoiceCallback(mainVo);

        // assert Kerry+
        List<EFapiaoBillInvoice> eFapiaoBillInvoicesActual = eFapiaoBillInvoiceRepository
                .findAllBySalesBillNo(SALES_BILL_NO_IMPORT_MOCK);
        assertFalse(eFapiaoBillInvoicesActual.isEmpty());

        EFapiaoBillInvoice eFapiaoBillInvoiceActual = eFapiaoBillInvoicesActual.stream()
                .filter(e -> INVOICE_NO_EXCEL_IMPORT_SPLIT.equals(e.getInvoiceNo()))
                .sorted(Comparator.comparing(EFapiaoBillInvoice::getUpdatedTime))
                .findFirst().get();

        assertEquals(InvoiceState.CANCELLED, eFapiaoBillInvoiceActual.getState());
    }

    @Test
    @DisplayName("excel-蓝票标记红冲")
    void _06_4_invoiceCallback_jde_sync_set_red_flag_callback() throws JsonProcessingException {
        // given
        CallBackKerryInvoiceMainVo mainVo = generateCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo(SALES_BILL_NO_IMPORT_MOCK);
        mainVo.setInvoiceCode(INVOICE_CODE_EXCEL_IMPORT);
        mainVo.setInvoiceNo(INVOICE_NO_EXCEL_IMPORT);
        mainVo.setPurchaserTaxNo(randomString());
        mainVo.setSalesbillNumber(1);
        mainVo.setSalesbillType(SALES_BILL_TYPE_EXCEL_IMPORT);
        mainVo.setRedFlag(RED_FLAG_RED_STATUS.getIndexStr());
        mainVo.setInvoiceStatus(INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(STATUS_NORMAL);
        mainVo.setSystemOrig(SYSTEM_ORIG_EXCEL_IMPORT);
        mainVo.setSystemOrigType(PAGE_IMPORT.getCode());
        mainVo.setTaxRate("0.060");
        mainVo.setBusinessType(randomString());
        mainVo.setExt2(randomString());
        mainVo.setExt3(randomString());

        // then
        runAndVerifyInvoiceCallback(mainVo);

        // assert
        List<EFapiaoBillInvoice> eFapiaoBillInvoicesActual = eFapiaoBillInvoiceRepository
                .findAllBySalesBillNo(SALES_BILL_NO_IMPORT_MOCK);
        assertFalse(eFapiaoBillInvoicesActual.isEmpty());

        EFapiaoBillInvoice eFapiaoBillInvoiceActual = eFapiaoBillInvoicesActual.stream()
                .filter(e -> INVOICE_NO_EXCEL_IMPORT.equals(e.getInvoiceNo()))
                .sorted(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime))
                .findFirst().get();

        assertEquals(InvoiceState.RED_STATUS, eFapiaoBillInvoiceActual.getState());
    }

    @Test
    @DisplayName("excel导入-开红冲票")
    void _06_5_invoiceCallback_excel_import_red_flag_invoice_callback() throws JsonProcessingException {
        // given
        CallBackKerryInvoiceMainVo mainVo = generateCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo(SALES_BILL_NO_IMPORT_MOCK);
        mainVo.setInvoiceCode(INVOICE_CODE_EXCEL_IMPORT);
        mainVo.setInvoiceNo(INVOICE_NO_EXCEL_IMPORT_RED_FLG);
        mainVo.setPurchaserTaxNo(randomString());
        mainVo.setSalesbillNumber(1);
        mainVo.setSalesbillType(SALES_BILL_TYPE_EXCEL_IMPORT);
        mainVo.setRedFlag(RED_FLAG_RED_INVOICE.getIndexStr());
        mainVo.setInvoiceStatus(INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(STATUS_NORMAL);
        mainVo.setSystemOrig(SYSTEM_ORIG_EXCEL_IMPORT);
        mainVo.setSystemOrigType(PAGE_IMPORT.getCode());
        mainVo.setTaxRate("0.060");
        mainVo.setTaxAmount(BigDecimal.valueOf(-11.32));
        mainVo.setAmountWithoutTax(BigDecimal.valueOf(-188.68));
        mainVo.setAmountWithTax(BigDecimal.valueOf(-200.0));
        mainVo.setMakingReason(randomString());
        mainVo.setBusinessType(randomString());
        mainVo.setExt2(randomString());
        mainVo.setExt3(randomString());

        List<CallBackKerryInvoiceDetailVo> callBackKerryInvoiceDetailVos = mainVo.getDetailVos();
        callBackKerryInvoiceDetailVos.forEach(callBackKerryInvoiceDetailVo -> {
            callBackKerryInvoiceDetailVo.setAmountWithTax(mainVo.getAmountWithTax());
            callBackKerryInvoiceDetailVo.setAmountWithoutTax(mainVo.getAmountWithoutTax());
            callBackKerryInvoiceDetailVo.setTaxAmount(mainVo.getTaxAmount());
            callBackKerryInvoiceDetailVo.setQuantity(BigDecimal.valueOf(-1.0));
        });

        // then
        runAndVerifyInvoiceCallback(mainVo);

        // assert Kerry+
        List<EFapiaoBillInvoice> eFapiaoBillInvoicesActual = eFapiaoBillInvoiceRepository
                .findAllBySalesBillNo(SALES_BILL_NO_IMPORT_MOCK);
        assertFalse(eFapiaoBillInvoicesActual.isEmpty());

        EFapiaoBillInvoice eFapiaoBillInvoiceActual = eFapiaoBillInvoicesActual.stream()
                .filter(e -> INVOICE_NO_EXCEL_IMPORT_RED_FLG.equals(e.getInvoiceNo()))
                .sorted(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime))
                .findFirst().get();

        assertEquals(SALES_BILL_NO_IMPORT_MOCK, eFapiaoBillInvoiceActual.getSalesBillNo());
        verifyImportInvoice(mainVo, eFapiaoBillInvoiceActual);

        assertEquals(COMPLETED, eFapiaoBillInvoiceActual.getInvoiceRecordStatus());
        assertEquals(RED_LETTER, eFapiaoBillInvoiceActual.getState());
    }

    private void verifyJdeInvoiceInKerryPlus(CallBackKerryInvoiceMainVo mainVo, EFapiaoBillInvoice eFapiaoBillInvoiceActual) {
        assertEquals(mainVo.getSellerName(), eFapiaoBillInvoiceActual.getSellerName());
        assertEquals(mainVo.getPdfUrl(), eFapiaoBillInvoiceActual.getPdfUrl());
        assertEquals(mainVo.getXmlUrl(), eFapiaoBillInvoiceActual.getXmlUrl());
        assertEquals(mainVo.getOfdUrl(), eFapiaoBillInvoiceActual.getOfdUrl());
        assertEquals(mainVo.getMakingReason(), eFapiaoBillInvoiceActual.getMakingReason());
        assertEquals(mainVo.getInvoiceCode(), eFapiaoBillInvoiceActual.getInvoiceCode());
        assertEquals(mainVo.getInvoiceNo(), eFapiaoBillInvoiceActual.getInvoiceNo());
        assertEquals(InvoiceUtils.transDateFormat(mainVo.getPaperDrewDate()), eFapiaoBillInvoiceActual.getPaperDrewDate());
        assertEquals(Optional.ofNullable(InvoiceTypeEnum.getByCode(mainVo.getInvoiceType()))
                .map(InvoiceTypeEnum::getKipCode).get(), eFapiaoBillInvoiceActual.getInvoiceType());
        assertEquals(mainVo.getSellerNo(), eFapiaoBillInvoiceActual.getCompanyCode());
        assertEquals(mainVo.getPurchaserAddress(), eFapiaoBillInvoiceActual.getPurchaserAddress());
    }

    private void verifyJdeInvoiceWriteBackJde(CallBackKerryInvoiceMainVo mainVo, Map<String, Object> jdeInvoiceWriteBackActual
            , String ifDoc, String ifDct, String ifKco, String ifSfx, String jdeInvoiceStatus) {
        assertEquals(ifDoc, String.valueOf(jdeInvoiceWriteBackActual.get("IFDOC")));
        assertEquals(ifDct, jdeInvoiceWriteBackActual.get("IFDCT"));
        assertEquals(ifKco, jdeInvoiceWriteBackActual.get("IFKCO"));
        assertEquals(ifSfx, jdeInvoiceWriteBackActual.get("IFSFX"));
        assertTrue(String.valueOf(jdeInvoiceWriteBackActual.get("IFMCU")).contains(mainVo.getBusinessType()));
        assertEquals(mainVo.getInvoiceType(), jdeInvoiceWriteBackActual.get("IFPCM"));
        assertEquals(mainVo.getInvoiceCode(), jdeInvoiceWriteBackActual.get("IFCINV"));
        assertEquals(mainVo.getInvoiceNo(), jdeInvoiceWriteBackActual.get("IFGUI"));
        assertEquals(mainVo.getPurchaserTaxNo(), jdeInvoiceWriteBackActual.get("IFTAX"));
        assertEquals(InvoiceUtils.transDateFormat(mainVo.getPaperDrewDate()), jdeInvoiceWriteBackActual.get("IFURRF"));
        assertEquals(jdeInvoiceStatus, String.valueOf(jdeInvoiceWriteBackActual.get("IFEV01")));
    }

    private void verifyImportInvoice(CallBackKerryInvoiceMainVo mainVo, EFapiaoBillInvoice eFapiaoBillInvoiceActual) {
        assertEquals(mainVo.getSellerName(), eFapiaoBillInvoiceActual.getSellerName());
        assertEquals(0, mainVo.getAmountWithoutTax().compareTo(eFapiaoBillInvoiceActual.getAmountWithoutTax()));
        assertEquals(0, mainVo.getAmountWithTax().compareTo(eFapiaoBillInvoiceActual.getAmountWithTax()));
        assertEquals(0, mainVo.getTaxAmount().compareTo(eFapiaoBillInvoiceActual.getTaxAmount()));
        assertEquals(mainVo.getPdfUrl(), eFapiaoBillInvoiceActual.getPdfUrl());
        assertEquals(mainVo.getXmlUrl(), eFapiaoBillInvoiceActual.getXmlUrl());
        assertEquals(mainVo.getOfdUrl(), eFapiaoBillInvoiceActual.getOfdUrl());
        assertEquals(mainVo.getMakingReason(), eFapiaoBillInvoiceActual.getMakingReason());
        assertEquals(mainVo.getInvoiceCode(), eFapiaoBillInvoiceActual.getInvoiceCode());
        assertEquals(mainVo.getInvoiceNo(), eFapiaoBillInvoiceActual.getInvoiceNo());
        assertEquals(InvoiceUtils.transDateFormat(mainVo.getPaperDrewDate()), eFapiaoBillInvoiceActual.getPaperDrewDate());

        assertEquals(Optional.ofNullable(InvoiceTypeEnum.getByCode(mainVo.getInvoiceType()))
                .map(InvoiceTypeEnum::getKipCode).get(), eFapiaoBillInvoiceActual.getInvoiceType());
        assertEquals(mainVo.getSellerNo(), eFapiaoBillInvoiceActual.getCompanyCode());
        assertEquals(mainVo.getBusinessType(), eFapiaoBillInvoiceActual.getMcu());
        assertEquals(mainVo.getExt2(), eFapiaoBillInvoiceActual.getDoco());
        assertEquals(mainVo.getExt3(), eFapiaoBillInvoiceActual.getAn8());
        assertEquals(mainVo.getExt1(), eFapiaoBillInvoiceActual.getJdeUnit());

        assertEquals(0, new BigDecimal(mainVo.getTaxRate()).compareTo(eFapiaoBillInvoiceActual.getTaxRate()));
        assertTrue(StringUtils.isEmpty(eFapiaoBillInvoiceActual.getKco()));
        assertTrue(StringUtils.isEmpty(eFapiaoBillInvoiceActual.getBillType()));
        assertEquals("0", String.valueOf(eFapiaoBillInvoiceActual.getDoc()));
        assertTrue(StringUtils.isEmpty(eFapiaoBillInvoiceActual.getPaymentItem()));
        assertEquals(mainVo.getPurchaserAddress(), eFapiaoBillInvoiceActual.getPurchaserAddress());
    }

    private void runAndVerifyInvoiceCallback(CallBackKerryInvoiceMainVo vo)
            throws JsonProcessingException {
        ResponseEntity<InvoiceRecordController.XForceResponse> responseEntity = call_invoiceCallback_api(vo);
        InvoiceRecordController.XForceResponse response = responseEntity.getBody();
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());
    }

    private ResponseEntity<InvoiceRecordController.XForceResponse> call_invoiceCallback_api(CallBackKerryInvoiceMainVo vo)
            throws JsonProcessingException {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.parseMediaType(MediaType.APPLICATION_JSON_VALUE));

        String json = mapper.writeValueAsString(vo);
        Map<String, Object> hashMap = mapper.readValue(json, HashMap.class);

        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(hashMap, httpHeaders);

        String url = LOCAL_HOST_DOMAIN + serverPort + "/c/bill/invoice/callback";
        return restTemplate.postForEntity(url, httpEntity, InvoiceRecordController.XForceResponse.class);
    }

    private CallBackKerryInvoiceMainVo generateCallBackKerryInvoiceMainVo() {
        String jsonMock =
                        """
                                        {
                                        	"amountWithTax": 15000.0,
                                        	"amountWithoutTax": 14285.71,
                                        	"businessType": "",
                                        	"cashierName": "",
                                        	"checkCode": "",
                                        	"checkerName": "",
                                        	"detailVos": [
                                        		{
                                        			"amountWithTax": 15000.0,
                                        			"amountWithoutTax": 14285.71,
                                        			"goodsTaxNo": "1100301010000000000",
                                        			"itemName": "",
                                        			"itemSpec": "1",
                                        			"quantity": 1.0,
                                        			"quantityUnit": "1",
                                        			"salesbillItemNo": "",
                                        			"taxAmount": 714.29,
                                        			"taxPre": "",
                                        			"taxPreCon": "",
                                        			"taxRate": 0.05,
                                        			"unitPrice": 14285.7142857143,
                                        			"zeroTax": ""
                                        		}
                                        	],
                                        	"ext1": "",
                                        	"ext2": "",
                                        	"ext3": "",
                                        	"invoiceCode": "",
                                        	"invoiceNo": "",
                                        	"invoiceStatus": -1,
                                        	"invoiceType": "qc",
                                        	"invoicerName": "吴*******",
                                        	"makingReason": "",
                                        	"ofdUrl": "https://kip-public-dev.oss-cn-shanghai.aliyuncs.com/95144a10ac2a4e80af5e9cd6c75fc00f_99242000000002955697.ofd",
                                        	"paperDrewDate": "********",
                                        	"pdfUrl": "https://kip-public-dev.oss-cn-shanghai.aliyuncs.com/2674b07529cb48d1b1aceb86da747354_99242000000002955697.pdf",
                                        	"purchaserAddress": "测试地址",
                                        	"purchaserBankAccount": "415***214",
                                        	"purchaserBankName": "**********",
                                        	"purchaserName": "英***********************",
                                        	"purchaserNo": "",
                                        	"purchaserTaxNo": "311100007178345043",
                                        	"purchaserTel": "*********",
                                        	"receiveUserEmail": "byron***********ops.com",
                                        	"receiveUserTel": "",
                                        	"redFlag": "",
                                        	"remark": "购方地址:测试地址; 电话:*********;\\\\n购方开户银行:**********; 银行账号:*********;\\\\n销方地址:; 电话:;\\\\n销方开户银行:; 银行账号:;",
                                        	"salesbillAmountWithTax": 15000.0,
                                        	"salesbillAmountWithoutTax": 14285.71,
                                        	"salesbillNo": "",
                                        	"salesbillNumber": -1,
                                        	"salesbillType": "",
                                        	"sellerAddress": "",
                                        	"sellerBankAccount": "",
                                        	"sellerBankName": "",
                                        	"sellerName": "北************",
                                        	"sellerNo": "",
                                        	"sellerTaxNo": "91110000600034917G",
                                        	"sellerTel": "",
                                        	"status": -1,
                                        	"systemOrig": "",
                                        	"systemOrigType": "",
                                        	"taxAmount": 714.29,
                                        	"taxRate": "0.050",
                                        	"xmlUrl": "https://kip-public-dev.oss-cn-shanghai.aliyuncs.com/7173ff625648400baad4406b56a987ab_99242000000002955697.xml"
                                        }
                        """;

        return JSONObject.parseObject(jsonMock, CallBackKerryInvoiceMainVo.class);
    }

}
