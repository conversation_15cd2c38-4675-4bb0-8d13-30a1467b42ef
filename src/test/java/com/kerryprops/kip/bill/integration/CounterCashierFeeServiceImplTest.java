package com.kerryprops.kip.bill.integration;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.enums.AptPayDeleteStatusEnum;
import com.kerryprops.kip.bill.common.enums.AptPaySendJdeStatusEnum;
import com.kerryprops.kip.bill.common.enums.AptPayVerifyStatus;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.common.enums.PayCancelTypeEnum;
import com.kerryprops.kip.bill.common.enums.PaymentCateEnum;
import com.kerryprops.kip.bill.common.exceptions.CounterCashierBizException;
import com.kerryprops.kip.bill.common.utils.DateUtils;
import com.kerryprops.kip.bill.config.SyncJdeConfig;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptPayRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPayConfig;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.dao.entity.QAptPay;
import com.kerryprops.kip.bill.feign.entity.FeeConfigVo;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.impl.CounterCashierFeeServiceImpl;
import com.kerryprops.kip.bill.webservice.vo.req.CashierFeePaysVerifyRequest;
import com.kerryprops.kip.hiveas.feign.dto.resp.BuildingRespDto;
import com.kerryprops.kip.hiveas.webservice.vo.resp.BuildingResponseVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.Date;
import java.util.List;

import static com.kerryprops.kip.bill.common.enums.BillPayModule.CASHIER_FEE;
import static com.kerryprops.kip.bill.common.enums.BillStatus.ONLINE_PAID;
import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * 杂费管理-集成测试类
 *
 * <AUTHOR> Yan
 * @date 2024-1-2
 */
class CounterCashierFeeServiceImplTest extends BaseIntegrationTest {

    private final int COMPANY_CODE_LENGTH = 5;

    @Autowired
    @Qualifier("jdeJdbcTemplate")
    JdbcTemplate jdbcTemplate;

    @Autowired
    AptPayRepository aptPayRepository;

    @Autowired
    AptBillRepository aptBillRepository;

    @Autowired
    CounterCashierFeeServiceImpl counterCashierFeeService;

    @Test
    void _01_switchFeePayVerifyStatus_data_empty_fail() {
        aptPayRepository.deleteAll();
        Assertions.assertThrows(CounterCashierBizException.class
                , () -> counterCashierFeeService.switchFeePayVerifyStatus(1L));
    }

    @Test
    void _02_switchFeePayVerifyStatus_already_sent_jde_fail() {
        AptPay aptPay = generateRandomAptPay();
        final Integer SEND_JDE_STATUS_SUCCESS = 4;
        aptPay.setSendJdeStatus(SEND_JDE_STATUS_SUCCESS);
        aptPayRepository.save(aptPay);

        // 执行待验证部分
        Assertions.assertThrows(CounterCashierBizException.class
                , () -> counterCashierFeeService.switchFeePayVerifyStatus(1L));
    }

    @Test
    void _03_switchFeePayVerifyStatus_success() {
        AptPay aptPay = aptPayRepository.save(generateRandomAptPay());
        Long id = aptPay.getId();

        // 执行待验证部分
        counterCashierFeeService.switchFeePayVerifyStatus(id);

        // 验证执行结果
        AptPay aptPayNew1 = aptPayRepository.findById(id).get();
        assertEquals(AptPayVerifyStatus.VERIFIED, aptPayNew1.getVerifyStatus());

        // 执行待验证部分
        counterCashierFeeService.switchFeePayVerifyStatus(id);

        // 验证执行结果
        AptPay aptPayNew2 = aptPayRepository.findById(id).get();
        assertEquals(AptPayVerifyStatus.TO_BE_VERIFIED, aptPayNew2.getVerifyStatus());
    }

    @Test
    void _04_conditionalVerifyFeePay_success() {
        final String NOT_EXIST_CONTENT = "xxx";
        final String OBJECT_PAY_ACT = "123456";

        AptPay aptPay1 = generateRandomAptPay();
        aptPay1.setProjectId(NOT_EXIST_CONTENT);

        AptPay aptPay2 = generateRandomAptPay();

        AptPay aptPay3 = generateRandomAptPay();
        aptPay3.setDeletedAt(AptPayDeleteStatusEnum.DELETED.getCode());

        AptPay aptPay4 = generateRandomAptPay();
        aptPay4.setVerifyStatus(AptPayVerifyStatus.VERIFIED);

        AptPay aptPay5 = generateRandomAptPay();
        aptPay5.setPayAct(OBJECT_PAY_ACT);

        AptPay aptPay6 = generateRandomAptPay();
        aptPay6.setPayAct(OBJECT_PAY_ACT);

        List<AptPay> aptPays = List.of(aptPay1, aptPay2, aptPay3, aptPay4, aptPay5, aptPay6);
        aptPayRepository.saveAll(aptPays);

        CashierFeePaysVerifyRequest cashierFeePaysVerifyRequest = new CashierFeePaysVerifyRequest();
        cashierFeePaysVerifyRequest.setProjectId(aptPay3.getProjectId());

        // 执行待验证部分
        LoginUser loginUser = LoginUser.builder().buildingIds(aptPay3.getBuildingId()).build();
        UserInfoUtils.setUser(loginUser);
        counterCashierFeeService.conditionalVerifyFeePay(cashierFeePaysVerifyRequest);

        // 验证执行结果
        List<AptPay> objApyPays = aptPayRepository.getJpaQueryFactory()
                .select(QAptPay.aptPay)
                .from(QAptPay.aptPay)
                .where(QAptPay.aptPay.payAct.eq(OBJECT_PAY_ACT))
                .fetch();
        objApyPays.forEach(objApyPay -> assertEquals(AptPayVerifyStatus.VERIFIED, objApyPay.getVerifyStatus()));
    }

    @Test
    void _05_cancelFeePay_already_canceled_error() {
        AptPay aptPay = aptPayRepository.getJpaQueryFactory()
                .select(QAptPay.aptPay)
                .from(QAptPay.aptPay)
                .orderBy(QAptPay.aptPay.createTime.desc())
                .fetchFirst();
        aptPay.setDeletedAt(AptPayDeleteStatusEnum.DELETED.getCode());
        aptPayRepository.save(aptPay);

        // 执行待验证部分
        Assertions.assertThrows(CounterCashierBizException.class
                , () -> counterCashierFeeService.cancelFeePay(aptPay.getId()));
    }

    @Test
    void _06_cancelFeePay_success() {
        AptPay aptPay = aptPayRepository.getJpaQueryFactory()
                .select(QAptPay.aptPay)
                .from(QAptPay.aptPay)
                .orderBy(QAptPay.aptPay.createTime.desc())
                .fetchFirst();
        aptPay.setDeletedAt(AptPayDeleteStatusEnum.NOT_DELETED.getCode());
        aptPay.setPaymentInfoId(randomString());
        aptPayRepository.save(aptPay);

        AptBill aptBill = randomAptBill();
        aptBill.setBillNo(aptPay.getPaymentInfoId());
        aptBill.setStatus(ONLINE_PAID);
        aptBill.setPaymentStatus(BillPaymentStatus.PAID);
        aptBill.setDeletedAt(0);
        aptBillRepository.save(aptBill);

        AptPaymentInfo aptPaymentInfo = new AptPaymentInfo();
        aptPaymentInfo.setId(aptPay.getPaymentInfoId());
        aptPaymentInfo.setPaymentStatus(BillPaymentStatus.PAID);
        aptPaymentInfo.setCancelType(PayCancelTypeEnum.UNKNOWN.name());
        aptPaymentInfo.setXmlUrl(randomString());
        aptPaymentInfo.setOfdUrl(randomString());
        aptPaymentInfoRepository.save(aptPaymentInfo);

        // 执行待验证部分
        counterCashierFeeService.cancelFeePay(aptPay.getId());

        // 验证执行结果
        AptPay aptPayUpdated = aptPayRepository.findById(aptPay.getId()).get();
        assertEquals(AptPayDeleteStatusEnum.DELETED.getCode(), aptPayUpdated.getDeletedAt());

        List<AptBill> aptBills = aptBillRepository.findAllByBillNo(aptPay.getPaymentInfoId());
        if (CollectionUtils.isNotEmpty(aptBills)) {
            aptBills.forEach(aptBillTemp -> {
                assertEquals(BillStatus.TO_BE_PAID, aptBillTemp.getStatus());
                assertEquals(BillPaymentStatus.TO_BE_PAID, aptBillTemp.getPaymentStatus());
            });
        }

        AptPaymentInfo aptPaymentInfoNew = aptPaymentInfoRepository.findTopById(aptPay.getPaymentInfoId());
        assertEquals(BillPaymentStatus.CANCEL, aptPaymentInfoNew.getPaymentStatus());
        assertEquals(PayCancelTypeEnum.ADMIN_CANCELLED.name(), aptPaymentInfoNew.getCancelType());
    }

    @Test
    void _07_cancelFeePay_throw_exception_and_rollback_success() {
        AptPay aptPay = aptPayRepository.getJpaQueryFactory()
                .select(QAptPay.aptPay)
                .from(QAptPay.aptPay)
                .orderBy(QAptPay.aptPay.createTime.desc())
                .fetchFirst();
        aptPay.setDeletedAt(AptPayDeleteStatusEnum.NOT_DELETED.getCode());
        aptPay.setPaymentInfoId(randomString());
        aptPayRepository.save(aptPay);

        AptBill aptBill = randomAptBill();
        aptBill.setBillNo(aptPay.getPaymentInfoId());
        aptBill.setStatus(ONLINE_PAID);
        aptBill.setPaymentStatus(BillPaymentStatus.PAID);
        aptBill.setDeletedAt(0);
        aptBillRepository.save(aptBill);

        AptPaymentInfo aptPaymentInfo = new AptPaymentInfo();
        aptPaymentInfo.setId(aptPay.getPaymentInfoId());
        aptPaymentInfo.setPaymentStatus(BillPaymentStatus.PAID);
        aptPaymentInfo.setCancelType(PayCancelTypeEnum.UNKNOWN.name());
        aptPaymentInfo.setXmlUrl(randomString());
        aptPaymentInfo.setOfdUrl(randomString());
        aptPaymentInfoRepository.save(aptPaymentInfo);

        Mockito.doThrow(new RuntimeException()).when(aptPaymentInfoRepository).save(ArgumentMatchers.any());

        // 执行待验证部分
        Assertions.assertThrows(RuntimeException.class
                , () -> counterCashierFeeService.cancelFeePay(aptPay.getId()));

        AptPay aptPayNew = aptPayRepository.findById(aptPay.getId()).get();
        assertEquals(AptPayDeleteStatusEnum.NOT_DELETED.getCode(), aptPayNew.getDeletedAt());

        List<AptBill> aptBills = aptBillRepository.findAllByBillNo(aptPay.getPaymentInfoId());
        if (CollectionUtils.isNotEmpty(aptBills)) {
            aptBills.forEach(aptBillTemp -> {
                assertEquals(ONLINE_PAID, aptBillTemp.getStatus());
                assertEquals(BillPaymentStatus.PAID, aptBillTemp.getPaymentStatus());
            });
        }

        AptPaymentInfo aptPaymentInfoNew = aptPaymentInfoRepository.findTopById(aptPay.getPaymentInfoId());
        assertEquals(BillPaymentStatus.PAID, aptPaymentInfoNew.getPaymentStatus());
        assertEquals(PayCancelTypeEnum.UNKNOWN.name(), aptPaymentInfoNew.getCancelType());
    }

    @Test
    void _08_writeBackFee2JDE_payAct_null_fail() {
        prepareJdeSchema();

        AptPay aptPay = generateRandomAptPay();
        aptPay.setProjectId(random.nextInt() + StringUtils.EMPTY);
        aptPay.setVerifyStatus(AptPayVerifyStatus.VERIFIED);
        aptPay.setFeeId(random.nextLong());
        aptPay.setPayAct(null);
        aptPayRepository.save(aptPay);

        LoginUser loginUser = LoginUser.builder().buildingIds(aptPay.getBuildingId()).build();
        UserInfoUtils.setUser(loginUser);

        FeeConfigVo feeConfig = generateFeeConfig();
        Mockito.doReturn(feeConfig).when(kipInvoiceClient).getFeeConfig(ArgumentMatchers.anyLong());

        // 执行待验证部分
        Integer result = counterCashierFeeService.writeBackFee2JDE(aptPay.getProjectId());

        // 验证执行结果
        assertEquals(0, result.intValue());
        assertEquals(0, countJdeCashierFeeNumber(feeConfig.getCompanyCode()));

        AptPay aptPayNewest = aptPayRepository.findById(aptPay.getId()).get();
        assertEquals(AptPaySendJdeStatusEnum.NOT_YET.getCode(), aptPayNewest.getSendJdeStatus());
    }

    @Test
    void _09_writeBackFee2JDE_payAct_empty_fail() {
        AptPay aptPay = generateRandomAptPay();
        aptPay.setProjectId(random.nextInt() + StringUtils.EMPTY);
        aptPay.setVerifyStatus(AptPayVerifyStatus.VERIFIED);
        aptPay.setFeeId(random.nextLong());
        aptPay.setPayAct(StringUtils.EMPTY);
        aptPayRepository.save(aptPay);

        LoginUser loginUser = LoginUser.builder().buildingIds(aptPay.getBuildingId()).build();
        UserInfoUtils.setUser(loginUser);

        FeeConfigVo feeConfig = generateFeeConfig();
        Mockito.doReturn(feeConfig).when(kipInvoiceClient).getFeeConfig(ArgumentMatchers.anyLong());

        // 执行待验证部分
        Integer result = counterCashierFeeService.writeBackFee2JDE(aptPay.getProjectId());

        // 验证执行结果
        assertEquals(0, result.intValue());
        assertEquals(0, countJdeCashierFeeNumber(feeConfig.getCompanyCode()));

        AptPay aptPayNewest = aptPayRepository.findById(aptPay.getId()).get();
        assertEquals(AptPaySendJdeStatusEnum.NOT_YET.getCode(), aptPayNewest.getSendJdeStatus());
    }

    @Test
    void _10_writeBackFee2JDE_payAct_length_less_than_max_length_success() {
        final int RANDOM_INT_BOUND = 99999;

        AptPay aptPay = generateRandomAptPay();
        aptPay.setProjectId(random.nextInt() + StringUtils.EMPTY);
        aptPay.setVerifyStatus(AptPayVerifyStatus.VERIFIED);
        aptPay.setFeeId(random.nextLong());
        aptPay.setPayAct(random.nextInt(RANDOM_INT_BOUND) + StringUtils.EMPTY);
        aptPayRepository.save(aptPay);

        LoginUser loginUser = LoginUser.builder()
                .buildingIds(aptPay.getBuildingId())
                .loginAccount("<EMAIL>").build();
        UserInfoUtils.setUser(loginUser);

        FeeConfigVo feeConfig = generateFeeConfig();
        Mockito.doReturn(feeConfig).when(kipInvoiceClient).getFeeConfig(ArgumentMatchers.anyLong());

        String companyCodeTest = randomString(COMPANY_CODE_LENGTH);
        mockHiveAsClient(companyCodeTest);
        mockAptPayConfigRepository();

        // 执行待验证部分
        Integer result = counterCashierFeeService.writeBackFee2JDE(aptPay.getProjectId());

        // 验证执行结果
        assertEquals(1, result.intValue());
        assertEquals(4, countJdeCashierFeeNumber(companyCodeTest));

        AptPay aptPayNewest = aptPayRepository.findById(aptPay.getId()).get();
        assertEquals(AptPaySendJdeStatusEnum.WRITE_TO_JDE_SUCCESS.getCode(), aptPayNewest.getSendJdeStatus());
    }

    @Test
    void _11_writeBackFee2JDE_payAct_length_greater_than_max_length_success() {
        final int RANDOM_INT_BOUND = 99999;

        AptPay aptPay = generateRandomAptPay();
        aptPay.setProjectId(random.nextInt() + StringUtils.EMPTY);
        aptPay.setVerifyStatus(AptPayVerifyStatus.VERIFIED);
        aptPay.setFeeId(random.nextLong());
        aptPay.setPayAct("**********" + random.nextInt(RANDOM_INT_BOUND) + StringUtils.EMPTY);
        aptPayRepository.save(aptPay);

        LoginUser loginUser = LoginUser.builder()
                .buildingIds(aptPay.getBuildingId())
                .loginAccount("<EMAIL>").build();
        UserInfoUtils.setUser(loginUser);

        FeeConfigVo feeConfig = generateFeeConfig();
        Mockito.doReturn(feeConfig).when(kipInvoiceClient).getFeeConfig(ArgumentMatchers.anyLong());

        String companyCodeTest = randomString(COMPANY_CODE_LENGTH);
        mockHiveAsClient(companyCodeTest);
        mockAptPayConfigRepository();

        // 执行待验证部分
        Integer result = counterCashierFeeService.writeBackFee2JDE(aptPay.getProjectId());

        // 验证执行结果
        assertEquals(1, result.intValue());
        assertEquals(4, countJdeCashierFeeNumber(companyCodeTest));

        AptPay aptPayNewest = aptPayRepository.findById(aptPay.getId()).get();
        assertEquals(AptPaySendJdeStatusEnum.WRITE_TO_JDE_SUCCESS.getCode(), aptPayNewest.getSendJdeStatus());
    }

    @Test
    void _12_writeBackFee2JDE_loginAccount_null_fail() {
        final int RANDOM_INT_BOUND = 99999;

        AptPay aptPay = generateRandomAptPay();
        aptPay.setProjectId(random.nextInt() + StringUtils.EMPTY);
        aptPay.setVerifyStatus(AptPayVerifyStatus.VERIFIED);
        aptPay.setFeeId(random.nextLong());
        aptPay.setPayAct(random.nextInt(RANDOM_INT_BOUND) + StringUtils.EMPTY);
        aptPayRepository.save(aptPay);

        LoginUser loginUser = LoginUser.builder().buildingIds(aptPay.getBuildingId()).build();
        loginUser.setLoginAccount(null);
        UserInfoUtils.setUser(loginUser);

        FeeConfigVo feeConfig = generateFeeConfig();
        Mockito.doReturn(feeConfig).when(kipInvoiceClient).getFeeConfig(ArgumentMatchers.anyLong());

        String companyCodeTest = randomString(COMPANY_CODE_LENGTH);
        mockHiveAsClient(companyCodeTest);
        mockAptPayConfigRepository();

        // 执行待验证部分
        Integer result = counterCashierFeeService.writeBackFee2JDE(aptPay.getProjectId());

        // 验证执行结果
        assertEquals(0, result.intValue());
        assertEquals(0, countJdeCashierFeeNumber(companyCodeTest));

        AptPay aptPayNewest = aptPayRepository.findById(aptPay.getId()).get();
        assertEquals(AptPaySendJdeStatusEnum.NOT_YET.getCode(), aptPayNewest.getSendJdeStatus());
    }

    @Test
    void _13_writeBackFee2JDE_loginAccount_empty_success() {
        final int RANDOM_INT_BOUND = 99999;

        AptPay aptPay = generateRandomAptPay();
        aptPay.setProjectId(random.nextInt() + StringUtils.EMPTY);
        aptPay.setVerifyStatus(AptPayVerifyStatus.VERIFIED);
        aptPay.setFeeId(random.nextLong());
        aptPay.setPayAct(random.nextInt(RANDOM_INT_BOUND) + StringUtils.EMPTY);
        aptPayRepository.save(aptPay);

        LoginUser loginUser = LoginUser.builder().buildingIds(aptPay.getBuildingId()).build();
        loginUser.setLoginAccount(StringUtils.EMPTY);
        UserInfoUtils.setUser(loginUser);

        FeeConfigVo feeConfig = generateFeeConfig();
        Mockito.doReturn(feeConfig).when(kipInvoiceClient).getFeeConfig(ArgumentMatchers.anyLong());

        String companyCodeTest = randomString(COMPANY_CODE_LENGTH);
        mockHiveAsClient(companyCodeTest);
        mockAptPayConfigRepository();

        // 执行待验证部分
        Integer result = counterCashierFeeService.writeBackFee2JDE(aptPay.getProjectId());

        // 验证执行结果
        assertEquals(1, result.intValue());
        assertEquals(4, countJdeCashierFeeNumber(companyCodeTest));

        AptPay aptPayNewest = aptPayRepository.findById(aptPay.getId()).get();
        assertEquals(AptPaySendJdeStatusEnum.WRITE_TO_JDE_SUCCESS.getCode(), aptPayNewest.getSendJdeStatus());
    }

    @Test
    void _14_writeBackFee2JDE_loginAccount_length_less_than_max_length_success() {
        final int RANDOM_INT_BOUND = 99999;

        AptPay aptPay = generateRandomAptPay();
        aptPay.setProjectId(random.nextInt() + StringUtils.EMPTY);
        aptPay.setVerifyStatus(AptPayVerifyStatus.VERIFIED);
        aptPay.setFeeId(random.nextLong());
        aptPay.setPayAct(random.nextInt(RANDOM_INT_BOUND) + StringUtils.EMPTY);
        aptPayRepository.save(aptPay);

        LoginUser loginUser = LoginUser.builder().buildingIds(aptPay.getBuildingId()).build();
        loginUser.setLoginAccount(random.nextInt(RANDOM_INT_BOUND) + StringUtils.EMPTY);
        UserInfoUtils.setUser(loginUser);

        FeeConfigVo feeConfig = generateFeeConfig();
        Mockito.doReturn(feeConfig).when(kipInvoiceClient).getFeeConfig(ArgumentMatchers.anyLong());

        String companyCodeTest = randomString(COMPANY_CODE_LENGTH);
        mockHiveAsClient(companyCodeTest);
        mockAptPayConfigRepository();

        // 执行待验证部分
        Integer result = counterCashierFeeService.writeBackFee2JDE(aptPay.getProjectId());

        // 验证执行结果
        assertEquals(1, result.intValue());
        assertEquals(4, countJdeCashierFeeNumber(companyCodeTest));

        AptPay aptPayNewest = aptPayRepository.findById(aptPay.getId()).get();
        assertEquals(AptPaySendJdeStatusEnum.WRITE_TO_JDE_SUCCESS.getCode(), aptPayNewest.getSendJdeStatus());
    }

    @Test
    void _15_writeBackFee2JDE_loginAccount_length_greater_than_max_length_success() {
        final int RANDOM_INT_BOUND = 99999;

        AptPay aptPay = generateRandomAptPay();
        aptPay.setProjectId(random.nextInt() + StringUtils.EMPTY);
        aptPay.setVerifyStatus(AptPayVerifyStatus.VERIFIED);
        aptPay.setFeeId(random.nextLong());
        aptPay.setPayAct(random.nextInt(RANDOM_INT_BOUND) + StringUtils.EMPTY);
        aptPayRepository.save(aptPay);

        LoginUser loginUser = LoginUser.builder().buildingIds(aptPay.getBuildingId()).build();
        loginUser.setLoginAccount("aaabbbcccddd" + random.nextInt(RANDOM_INT_BOUND) + StringUtils.EMPTY);
        UserInfoUtils.setUser(loginUser);

        FeeConfigVo feeConfig = generateFeeConfig();
        Mockito.doReturn(feeConfig).when(kipInvoiceClient).getFeeConfig(ArgumentMatchers.anyLong());

        String companyCodeTest = randomString(COMPANY_CODE_LENGTH);
        mockHiveAsClient(companyCodeTest);
        mockAptPayConfigRepository();

        // 执行待验证部分
        Integer result = counterCashierFeeService.writeBackFee2JDE(aptPay.getProjectId());

        // 验证执行结果
        assertEquals(1, result.intValue());
        assertEquals(4, countJdeCashierFeeNumber(companyCodeTest));

        AptPay aptPayNewest = aptPayRepository.findById(aptPay.getId()).get();
        assertEquals(AptPaySendJdeStatusEnum.WRITE_TO_JDE_SUCCESS.getCode(), aptPayNewest.getSendJdeStatus());
    }

    private void prepareJdeSchema() {
        // 初始化JDE的SCHEMA
        jdbcTemplate.execute("CREATE SCHEMA IF NOT EXISTS " + SyncJdeConfig.getJdeDbPrefix() + ";");

        // 初始化JDE回写表
        jdbcTemplate.execute("CREATE TABLE " +
                SyncJdeConfig.getJdeCashierFee() +
                " (MSUKID NUMBER NOT NULL," +
                "  MSEDLN NUMBER NOT NULL," +
                "  MSKCO NCHAR(5)," +
                "  MSMCU NCHAR(12)," +
                "  MSOBJ NCHAR(6)," +
                "  MSSUB NCHAR(8)," +
                "  MSSBL NCHAR(8)," +
                "  MSSBLT NCHAR(1)," +
                "  MSEXA NCHAR(30)," +
                "  MSAA NUMBER," +
                "  MSHDBU NCHAR(12)," +
                "  MSDMBU NCHAR(12) NOT NULL," +
                "  MSMSMT NUMBER(6,0) NOT NULL," +
                "  MSUSER NCHAR(10)," +
                "  MSPID NCHAR(10)," +
                "  MSPOST NCHAR(1));");

        jdbcTemplate.execute("CREATE UNIQUE INDEX CRPDTA.F550911M_0 ON "
                + SyncJdeConfig.getJdeCashierFee()
                + " (MSUKID ASC, MSEDLN ASC, MSDMBU ASC, MSMSMT ASC)");
    }

    private AptPay generateRandomAptPay() {
        return AptPay.builder()
                .deletedAt(0)
                .deletedBy("Zihan.Yan")
                .deletedTime(new Date())
                .projectId("P1")
                .buildingId("P1-B1")
                .roomId("P1-B1-R1")
                .payConfigId(2L)
                .payType("微信支付")
                .payAct("P1")
                .payDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2024-01-02 17:15:12"))
                .billPayModule(CASHIER_FEE)
                .deletedAt(AptPayDeleteStatusEnum.NOT_DELETED.getCode())
                .sendJdeStatus(AptPaySendJdeStatusEnum.NOT_YET.getCode())
                .verifyStatus(AptPayVerifyStatus.TO_BE_VERIFIED).build();
    }

    private FeeConfigVo generateFeeConfig() {
        final int RANDOM_INT_BOUND = 99999;
        FeeConfigVo feeConfig = new FeeConfigVo();
        feeConfig.setTaxRate(random.nextInt() + StringUtils.EMPTY);
        feeConfig.setCompanyCode(random.nextInt(RANDOM_INT_BOUND) + StringUtils.EMPTY);
        feeConfig.setBu(random.nextInt(RANDOM_INT_BOUND) + StringUtils.EMPTY);
        feeConfig.setPaymentCate(random.nextInt(RANDOM_INT_BOUND) + StringUtils.EMPTY);
        feeConfig.setPaymentDetail(random.nextInt(RANDOM_INT_BOUND) + StringUtils.EMPTY);
        return feeConfig;
    }

    private int countJdeCashierFeeNumber(String companyCode) {
        String selectSqlFormat = "SELECT count(*) FROM %s WHERE MSKCO = ? ";
        String selectSql = String.format(selectSqlFormat, SyncJdeConfig.getJdeCashierFee());
        return jdbcTemplate.query(selectSql,
                pstmt -> {
                    int paramIndex = 1;
                    pstmt.setString(paramIndex, companyCode);
                }, rs -> {
                    rs.next();
                    return rs.getInt(1);
                });
    }

    private void mockAptPayConfigRepository() {
        double taxTest = random.nextInt();

        AptPayConfig aptPayConfigTest = new AptPayConfig();
        aptPayConfigTest.setTax(taxTest);
        aptPayConfigTest.setMax(taxTest + random.nextDouble());
        aptPayConfigTest.setMcu(randomString(8));
        aptPayConfigTest.setPaymentCate(PaymentCateEnum.A004.getCode());
        aptPayConfigTest.setPaymentDetail(randomString(8));
        Mockito.doReturn(aptPayConfigTest).when(aptPayConfigRepository)
                .findTopByProjectIdAndCompanyCodeAndPaymentType(ArgumentMatchers.anyString()
                        , ArgumentMatchers.anyString(), ArgumentMatchers.anyString());
    }

    private void mockHiveAsClient(String companyCodeTest) {
        Mockito.doReturn(companyCodeTest).when(hiveAsClient).getPropertyManagementCo(ArgumentMatchers.anyString());

        BuildingRespDto building = new BuildingRespDto();
        building.setPropertyManagementBU("41225302");

        BuildingResponseVo buildingResponseVo = new BuildingResponseVo();
        buildingResponseVo.setBuilding(building);
        Mockito.doReturn(buildingResponseVo).when(hiveAsClient).getBuildingById(ArgumentMatchers.anyString());
    }

}