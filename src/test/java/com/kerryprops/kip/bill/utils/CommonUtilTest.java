package com.kerryprops.kip.bill.utils;

import com.kerryprops.kip.bill.common.utils.CommonUtil;
import lombok.Data;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static com.kerryprops.kip.bill.utils.RandomUtil.randomObject;
import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * CommonUtil测试类
 *
 * <AUTHOR>
 * @date 2024-12-12
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("CommonUtil测试类")
public class CommonUtilTest {

    @Test
    @DisplayName("comparePossiblyNull方法-正常场景")
    void _01_comparePossiblyNull_success() {
        final int THIS_GREATER_THAN_THAT = 1;
        final int THAT_GREATER_THAN_THIS = -1;

        assertEquals(THIS_GREATER_THAN_THAT, CommonUtil.comparePossiblyNull(BigDecimal.TEN, BigDecimal.ZERO));
        assertEquals(THAT_GREATER_THAN_THIS, CommonUtil.comparePossiblyNull(BigDecimal.ZERO, BigDecimal.TEN));
        assertEquals(THIS_GREATER_THAN_THAT, CommonUtil.comparePossiblyNull(null, null));
        assertEquals(THAT_GREATER_THAN_THIS, CommonUtil.comparePossiblyNull(null, BigDecimal.ZERO));
        assertEquals(THIS_GREATER_THAN_THAT, CommonUtil.comparePossiblyNull(BigDecimal.ZERO, null));
    }

    @Test
    @DisplayName("parseLongQuietly方法-正常场景")
    void _02_parseLongQuietly_success() {
        long numberMock = randomObject(Long.class);
        assertEquals(numberMock, CommonUtil.parseLongQuietly(String.valueOf(numberMock)));
    }

    @Test
    @DisplayName("parseLongQuietly方法-异常场景")
    void _03_parseLongQuietly_error() {
        String errorStrMock = "xxx";
        assertEquals(0L, CommonUtil.parseLongQuietly(errorStrMock));
    }

    @Test
    @DisplayName("copyPropertiesIgnoreNull方法-异常场景")
    void _03_copyPropertiesIgnoreNull_success() {
        ClassForTest classForTestSource = new ClassForTest();
        classForTestSource.setNumberTest1(randomObject(Integer.class));
        classForTestSource.setNumberTest2(randomObject(Integer.class));
        ClassForTest classForTestTarget = new ClassForTest();

        CommonUtil.copyPropertiesIgnoreNull(classForTestSource, classForTestTarget);

        assertEquals(classForTestSource.getNumberTest1(), classForTestTarget.getNumberTest1());
        assertEquals(classForTestSource.getNumberTest2(), classForTestTarget.getNumberTest2());
    }

    @Data
    private static class ClassForTest {

        private int numberTest1;

        private int numberTest2;

    }

}
