package com.kerryprops.kip.bill.utils;

import com.kerryprops.kip.bill.common.current.LoginUser;
import org.apache.commons.lang3.RandomStringUtils;
import org.jeasy.random.EasyRandom;
import org.jeasy.random.EasyRandomParameters;
import org.springframework.data.annotation.Id;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 随机工具类.
 *
 * <AUTHOR> 2024-03-30 15:23:05
 **/
public final class RandomUtil {

    private static final EasyRandom RANDOM = buildRandom();

    private RandomUtil() {
    }

    public static <T> T randomObject(Class<T> clazz) {
        return RANDOM.nextObject(clazz);
    }

    private static EasyRandom buildRandom() {
        long now = System.currentTimeMillis() / 1000L;
        EasyRandomParameters parameters = new EasyRandomParameters();
        parameters.setCollectionSizeRange(new EasyRandomParameters.Range<>(1, 2));
        parameters.excludeField(v -> MultipartFile.class.equals(v.getType()));
        parameters.excludeField(v -> v.isAnnotationPresent(Id.class));
        parameters.randomize(String.class, () -> RandomStringUtils.randomAlphanumeric(3, 10));
        parameters.randomize(Float.class, () -> {
            ThreadLocalRandom current = ThreadLocalRandom.current();
            return (float) current.nextInt(10000) / 1.0e2f;
        });
        parameters.randomize(Double.class, () -> {
            ThreadLocalRandom current = ThreadLocalRandom.current();
            return (double) current.nextInt(10000) / 1.0e2;
        });
        parameters.randomize(Long.class, () -> {
            ThreadLocalRandom current = ThreadLocalRandom.current();
            return current.nextLong(now);
        });
        parameters.randomize(Integer.class, () -> {
            ThreadLocalRandom current = ThreadLocalRandom.current();
            return current.nextInt(127);
        });
        parameters.randomize(BigDecimal.class, () -> {
            ThreadLocalRandom current = ThreadLocalRandom.current();
            return BigDecimal.valueOf(current.nextLong(10000), 2);
        });
        return new EasyRandom(parameters);
    }

    /**
     * 生成固定长度的随机字符串
     */
    public static String randomString() {
        final int RANDOM_STRING_LENGTH = 10;
        return RandomStringUtils.randomAlphanumeric(RANDOM_STRING_LENGTH);
    }

    /**
     * 生成指定长度的随机字符串
     */
    public static String randomString(int length) {
        return RandomStringUtils.randomAlphanumeric(length);
    }

    /**
     * 生成随机的登录用户
     */
    public static LoginUser randomLoginUser() {
        return LoginUser.builder()
                        .nickName(randomString())
                        .fromType("C")
                        .userId(RANDOM.nextLong())
                        .cid(randomString())
                        .projectIds(randomString())
                        .build();
    }

}
