package com.kerryprops.kip.bill.utils;

import com.kerryprops.kip.bill.webservice.vo.req.CallBackKerryInvoiceDetailVo;
import com.kerryprops.kip.bill.webservice.vo.req.CallBackKerryInvoiceMainVo;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Random;

import static com.kerryprops.kip.bill.common.utils.InvoiceUtils.splitJdeSalesbillNo;

/**
 * 发票业务测试工具类
 *
 * <AUTHOR>
 * @Date 2023-04-13
 */
public class InvoiceTestUtils {

    private static final Random random = new Random();

    /**
     * 生成随机发票回写数据
     *
     * @return 随机发票回写数据
     */
    public static CallBackKerryInvoiceMainVo getCallBackKerryInvoiceMainVo() {
        CallBackKerryInvoiceMainVo callBackKerryInvoiceMainVo = new CallBackKerryInvoiceMainVo();
        callBackKerryInvoiceMainVo.setSalesbillNo(generateRandomSalesbillNo());
        callBackKerryInvoiceMainVo.setSellerName(getRandomString());
        callBackKerryInvoiceMainVo.setSellerNo(getRandomString());
        callBackKerryInvoiceMainVo.setPurchaserAddress(getRandomString());
        callBackKerryInvoiceMainVo.setPurchaserBankName(getRandomString());
        callBackKerryInvoiceMainVo.setAmountWithoutTax(BigDecimal.valueOf(random.nextInt(100000)));
        callBackKerryInvoiceMainVo.setAmountWithTax(BigDecimal.valueOf(random.nextInt(100000)));
        callBackKerryInvoiceMainVo.setTaxAmount(BigDecimal.valueOf(random.nextInt(100000)));
        callBackKerryInvoiceMainVo.setSalesbillAmountWithTax(callBackKerryInvoiceMainVo.getAmountWithTax());
        callBackKerryInvoiceMainVo.setSalesbillAmountWithoutTax(callBackKerryInvoiceMainVo.getAmountWithoutTax());
        callBackKerryInvoiceMainVo.setSalesbillTaxAmount(callBackKerryInvoiceMainVo.getTaxAmount());
        callBackKerryInvoiceMainVo.setSalesbillNumber(1);
        callBackKerryInvoiceMainVo.setTaxRate("0.090");
        callBackKerryInvoiceMainVo.setPdfUrl(getRandomString());
        callBackKerryInvoiceMainVo.setXmlUrl(getRandomString());
        callBackKerryInvoiceMainVo.setOfdUrl(getRandomString());
        callBackKerryInvoiceMainVo.setMakingReason(getRandomString());
        callBackKerryInvoiceMainVo.setInvoiceNo(getRandomString());
        callBackKerryInvoiceMainVo.setInvoiceCode(getRandomString());
        callBackKerryInvoiceMainVo.setPaperDrewDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        callBackKerryInvoiceMainVo.setBusinessType("49990000");
        callBackKerryInvoiceMainVo.setExt1(getRandomString());
        callBackKerryInvoiceMainVo.setExt2(getRandomString());
        callBackKerryInvoiceMainVo.setExt3(getRandomString());

        CallBackKerryInvoiceDetailVo detailVo = new CallBackKerryInvoiceDetailVo();
        detailVo.setGoodsTaxNo(getRandomString());
        detailVo.setTaxPre(getRandomString());
        detailVo.setTaxPreCon(getRandomString());
        detailVo.setItemName(getRandomString());
        detailVo.setItemSpec(getRandomString());
        detailVo.setLeaseTermStart("20240101");
        detailVo.setLeaseTermEnd(new SimpleDateFormat("yyyyMMdd")
                .format(new Date()));
        callBackKerryInvoiceMainVo.setDetailVos(List.of(detailVo));

        return callBackKerryInvoiceMainVo;
    }

    private static String getRandomString() {
        return random.nextInt() + StringUtils.EMPTY;
    }

    // 生成随机开票业务单号，例：[43025, RD, 24006318, 001] -> 43025RD24006318001
    private static String generateRandomSalesbillNo() {
        return (40000 + random.nextInt(10000)) + "RX"
                + (24000000 + random.nextInt(1000)) + ("00" + random.nextInt(10));
    }

}
