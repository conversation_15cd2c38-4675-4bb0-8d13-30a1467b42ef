package com.kerryprops.kip.bill;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillPushStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.dao.AptJdeBillRepository;
import com.kerryprops.kip.bill.dao.AptPayConfigRepository;
import com.kerryprops.kip.bill.dao.AptPaymentInfoRepository;
import com.kerryprops.kip.bill.dao.BillRepository;
import com.kerryprops.kip.bill.dao.BillSendConfigRepository;
import com.kerryprops.kip.bill.dao.JDEBillRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptJdeBill;
import com.kerryprops.kip.bill.dao.entity.BillEntity;
import com.kerryprops.kip.bill.dao.entity.JDEAptBill;
import com.kerryprops.kip.bill.dao.impl.JDBCJdeEFapiaoBillOriginRepository;
import com.kerryprops.kip.bill.feign.clients.CUserClient;
import com.kerryprops.kip.bill.feign.clients.FileClient;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.clients.KipInvoiceClient;
import com.kerryprops.kip.bill.feign.clients.MessageCenterClient;
import com.kerryprops.kip.bill.feign.clients.MessageClient;
import com.kerryprops.kip.bill.feign.clients.SUserClient;
import com.kerryprops.kip.bill.service.IBillSendConfigService;
import com.kerryprops.kip.bill.service.IBillService;
import com.kerryprops.kip.bill.service.PaymentBillService;
import com.kerryprops.kip.bill.service.StaffManualSyncAptBillService;
import com.kerryprops.kip.bill.service.impl.EFapiaoBillServiceImpl;
import com.kerryprops.kip.bill.service.impl.EFapiaoJdeBillServiceImpl;
import com.kerryprops.kip.bill.service.impl.InvoiceApplicationService;
import com.kerryprops.kip.pmw.client.resource.AsynPaymentResultResource;
import com.kerryprops.kip.pmw.client.resource.CombinedPaymentTxOutputResource;
import com.kerryprops.kip.pmw.client.resource.DirectPayResource;
import com.kerryprops.kip.pmw.client.resource.SessionOutputResource;
import com.kerryprops.kip.pmw.client.service.PaymentClientService;
import com.kerryprops.kip.pmw.variables.PspName;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.core.env.Environment;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Random;

/**
 * 集成测试，尽量依赖真实组件，测试真实运行情况。
 * 在子类中，使用mockbean、spybean、TestPropertySource 等注解，会导致spring容器重新加载，大幅度降低执行效率.
 * 必须mock的，推荐修改：
 * 1、在BaseIntegrationTest中定义，避免导致spring容器重新加载
 * 2、修改成单元测试
 */
@Slf4j
@ActiveProfiles("test")
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.MethodName.class)
@SpringBootTest(classes = {Application.class}
        , webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public abstract class BaseIntegrationTest {

    protected static final String LOCAL_HOST_DOMAIN = "http://localhost:";

    protected static final Random random = new Random();

    static {
        RedisHolder.start();
    }

    @LocalServerPort
    protected int serverPort;

    protected TestRestTemplate restTemplate;

    @Resource
    protected ObjectMapper objectMapper;

    @SpyBean
    protected PaymentClientService paymentClientService;

    @SpyBean
    protected AptPayConfigRepository aptPayConfigRepository;

    @SpyBean
    protected KipInvoiceClient kipInvoiceClient;

    @SpyBean
    protected MessageClient messageClient;

    @SpyBean
    protected JDBCJdeEFapiaoBillOriginRepository jdbcJdeBillOriginRepository;

    @SpyBean
    protected SUserClient sUserClient;

    @SpyBean
    protected CUserClient cUserClient;

    @SpyBean
    protected HiveAsClient hiveAsClient;

    @SpyBean
    protected IBillSendConfigService billSendConfigService;

    @SpyBean
    protected StaffManualSyncAptBillService manualSyncService;

    @SpyBean
    protected AptJdeBillRepository aptJdeBillRepository;

    @SpyBean
    protected IBillService billService;

    @SpyBean
    protected EFapiaoBillServiceImpl eFapiaoBillService;

    @SpyBean
    protected EFapiaoJdeBillServiceImpl eFapiaoJdeBillService;

    @SpyBean
    protected BillRepository billRepository;

    @SpyBean
    protected JDEBillRepository jdeBillRepository;

    @SpyBean
    protected BillSendConfigRepository sendConfigRepository;

    @SpyBean
    protected MessageCenterClient messageCenterClient;

    @SpyBean
    protected FileClient fileClient;

    @SpyBean
    protected AptPaymentInfoRepository aptPaymentInfoRepository;

    @SpyBean
    protected PaymentBillService paymentBillService;

    @SpyBean
    protected InvoiceApplicationService invoiceApplicationService;

    /**
     * default status=TO_BE_PAY
     */
    protected AptBill randomAptBill() {
        AptBill aptBill = new AptBill();
        aptBill.setPaymentResult(String.valueOf(random.nextInt()));
        aptBill.setBillMonth(202201);
        aptBill.setBillNo(String.valueOf(random.nextInt()));
        aptBill.setBeginDate(new Date());
        aptBill.setEndDate(new Date());
        aptBill.setAmt(BigDecimal.valueOf(Math.abs(random.nextDouble()))
                .setScale(2, RoundingMode.HALF_UP).doubleValue());
        aptBill.setBuildingId(String.valueOf(random.nextInt()));
        aptBill.setFloorId(String.valueOf(random.nextInt()));
        aptBill.setProjectId(String.valueOf(random.nextInt()));
        aptBill.setRoomId(String.valueOf(random.nextInt()));
        aptBill.setUnit(String.valueOf(random.nextInt()));

        aptBill.setBu(String.valueOf(random.nextInt()));
        aptBill.setDoco(String.valueOf(random.nextInt()));
        aptBill.setRdGlc(String.valueOf(random.nextInt()));
        aptBill.setAlph(String.valueOf(random.nextInt()));
        aptBill.setAn8(String.valueOf(random.nextInt()));
        aptBill.setPaySession(String.valueOf(random.nextInt()));

        aptBill.setStatus(BillStatus.TO_BE_PAID);
        aptBill.setPaymentStatus(BillPaymentStatus.TO_BE_PAID);
        aptBill.setPushStatus(BillPushStatus.PUSHING);
        return aptBill;
    }

    protected BillEntity randomBill() {
        BillEntity billEntity = new BillEntity();
        billEntity.setDelFlag("0");
        billEntity.setTpAn8(random.nextInt() + "");
        billEntity.setTpDoco(random.nextInt() + "");
        billEntity.setTpAlph(random.nextInt() + "");
        billEntity.setTpDct(random.nextInt() + "");
        billEntity.setTpDc(random.nextInt() + "");
        billEntity.setTpEv01((random.nextInt() + "").substring(2));
        billEntity.setTpEv02((random.nextInt() + "").substring(4));
        billEntity.setTpEv03((random.nextInt() + "").substring(2));
        billEntity.setBillMonth(Integer.valueOf(ZonedDateTime.now().format(DateTimeFormatter.ofPattern("yyMM"))));
        billEntity.setTpGtfilenm(random.nextInt() + "");
        billEntity.setTpCo(random.nextInt() + "");
        billEntity.setFileUrl(random.nextInt() + "");
        billEntity.setTpDl01(random.nextInt() + "");
        billEntity.setTpDl03(random.nextInt() + "");
        billEntity.setTpMcu(random.nextInt() + "");
        billEntity.setTpDc(random.nextInt() + "");
        billEntity.setTpUnit(random.nextInt() + "");
        billEntity.setExt1(random.nextInt());
        return billEntity;
    }

    /**
     * default verification=0
     */
    protected AptJdeBill randomAptJdeBill(AptBill aptBill) {
        return AptJdeBill.builder().rdAg(Math.abs(random.nextDouble()))
                .rdAlph(aptBill.getAlph())
                .rdAn8(aptBill.getAn8())
                .rdAg(aptBill.getAmt() * 100)
                .rdDoco(aptBill.getDoco())
                .billNumber(aptBill.getBillNo())
                .rdEdbt("EDI批号")
                .rdDoc((long) random.nextInt(28888888))
                .rdDct("RD")
                .rdKco((random.nextInt() + "companyCode").substring(0, 5))
                .rdSfx("001")
                .rdGlc("U01M")
                .rdDl01("水费")
                .rdDl02("4")
                .rdDate01("122182")
                .rdDate02("122212")
                .rdMcu((Math.abs(random.nextInt()) + "_JDEBU").substring(0, 5))
                .rdUnit((Math.abs(random.nextInt()) + "_Unit").substring(0, 8))
                .rdUds1("20220701")
                .rdUds2("20220731")
                .rdUds3("2022-02-14 18:16:14")
                .jdeVerification(0)
                .rdUpmj((long) random.nextInt())
                .rdUpmt(String.valueOf(random.nextInt()))
                .build();
    }

    protected JDEAptBill randomJDEBill(AptJdeBill aptJdeBill) {
        return JDEAptBill.builder()
                .unpaidAmount(aptJdeBill.getRdAg().longValue())
                .paymentStatus("A")
                .updatedDate(aptJdeBill.getRdUpmj())
                .updatedTime(random.nextInt())
                .documentCode(aptJdeBill.getRdDoc())
                .build();
    }

    protected CombinedPaymentTxOutputResource randomPayResult() {
        CombinedPaymentTxOutputResource.CombinedPaymentTxOutputBodyResource body
                = new CombinedPaymentTxOutputResource.CombinedPaymentTxOutputBodyResource();
        CombinedPaymentTxOutputResource response = new CombinedPaymentTxOutputResource(null, body, null);
        DirectPayResource directPayResource = new DirectPayResource();
        body.setDirectPayResource(directPayResource);
        directPayResource.setPayUnitId(String.valueOf(random.nextInt()));
        directPayResource.setAmount(BigDecimal.valueOf(Math.abs(random.nextDouble()))
                .setScale(2, RoundingMode.HALF_UP));
        directPayResource.setBuyerPspAccount(String.valueOf(random.nextInt()));
        directPayResource.setConfirmedPspTradeNo(String.valueOf(random.nextInt()));
        directPayResource.setPspName(PspName.WECHATPAY);
        directPayResource.setPspTransId(String.valueOf(random.nextInt()));
        directPayResource.setResponseDate(ZonedDateTime.now());
        directPayResource.setResultMessage(String.valueOf(random.nextInt()));

        SessionOutputResource.SessionOutputBodyResource sessionDetail = new SessionOutputResource.SessionOutputBodyResource();
        body.setSessionDetail(sessionDetail);
        SessionOutputResource.SessionInfoBodyResource sessionInfoBodyResource = new SessionOutputResource.SessionInfoBodyResource();
        sessionInfoBodyResource.setSessionId(String.valueOf(random.nextInt()));
        sessionDetail.setSessionInfo(sessionInfoBodyResource);

        return response;
    }

    protected AsynPaymentResultResource randomPayCallback(CombinedPaymentTxOutputResource.CombinedPaymentTxOutputBodyResource combineBody) {
        AsynPaymentResultResource.AsynPaymentResultBodyResource body = new AsynPaymentResultResource.AsynPaymentResultBodyResource();
        AsynPaymentResultResource paymentCallbackResult = new AsynPaymentResultResource(null, body, null);
        body.setCreatedDate(ZonedDateTime.now().format(DateTimeFormatter.ISO_OFFSET_DATE_TIME));
        body.setCurrency("CNY");
        body.setFinishedDate(ZonedDateTime.now().format(DateTimeFormatter.ISO_OFFSET_DATE_TIME));
        body.setMerchantAccount(String.valueOf(random.nextInt()));
        body.setOrderAmount(combineBody.getDirectPayResource().getAmount().toString());
        body.setOrderSource(String.valueOf(random.nextInt()));
        body.setPayOption("WECHATPAY");
        body.setPayUnitId(String.valueOf(random.nextInt()));
        body.setPaymentSessionId(combineBody.getSessionDetail().getSessionInfo().getSessionId());
        body.setPspName("wechatpay");
        body.setPspTin(combineBody.getDirectPayResource().getPspTransId());
        body.setState("SUCCESS");
        body.setUniquePaymentId(combineBody.getDirectPayResource().getConfirmedPspTradeNo());
        return paymentCallbackResult;
    }

    /**
     * 生成固定长度的随机字符串
     */
    protected String randomString() {
        final int RANDOM_STRING_LENGTH = 10;
        return RandomStringUtils.randomAlphanumeric(RANDOM_STRING_LENGTH);
    }

    /**
     * 生成指定长度的随机字符串
     */
    protected String randomString(int length) {
        return RandomStringUtils.randomAlphanumeric(length);
    }

    @DynamicPropertySource
    private static void properties(DynamicPropertyRegistry registry) {
        registry.add("spring.data.redis.port", RedisHolder::getRedisPort);
    }

    @Autowired
    private void setRestTemplate(Environment environment) {
        RestTemplateBuilder builder = new RestTemplateBuilder();
        builder = builder.rootUri("http://127.0.0.1:" + environment.getProperty("local.server.port"));
        this.restTemplate = new TestRestTemplate(builder);
    }

}
