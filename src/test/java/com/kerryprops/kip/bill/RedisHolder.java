package com.kerryprops.kip.bill;

import org.springframework.test.util.TestSocketUtils;
import org.springframework.util.function.SingletonSupplier;
import redis.embedded.RedisServer;

import java.io.IOException;
import java.io.UncheckedIOException;

/**
 * RedisHolder.
 *
 * <AUTHOR> 2024-08-07 13:52:02
 **/
public class RedisHolder {

    private static final SingletonSupplier<RedisServer> redisServerSupplier = SingletonSupplier.of(() -> createRedis());

    public static void start() {
        try {
            redisServerSupplier.obtain().start();
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    public static int getRedisPort() {
        return redisServerSupplier.obtain().ports().get(0);
    }

    private static RedisServer createRedis() {
        try {
            return new RedisServer(TestSocketUtils.findAvailableTcpPort());
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

}
