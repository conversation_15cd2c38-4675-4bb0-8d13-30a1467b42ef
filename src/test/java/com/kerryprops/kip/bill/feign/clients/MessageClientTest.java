package com.kerryprops.kip.bill.feign.clients;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.feign.entity.EmailReplyVo;
import com.kerryprops.kip.bill.feign.entity.EmailSendCommand;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.SUCCESS;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.UNKNOWN_ERROR;
import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * MessageClientTest.
 *
 * <AUTHOR>
 * @date 2024-11-15
 */
@DisplayName("ums-FeignClient验证")
class MessageClientTest extends BaseIntegrationTest {

    @Autowired
    MessageClient messageClient;

    @Test
    @DisplayName("阿里云接口发送邮件-正常场景")
    void _01_sendWithReplyAlicloud_success() {
        final String SUCCESS_FLAG = "success";
        EmailSendCommand emailSendCommand = new EmailSendCommand();
        emailSendCommand.setText(SUCCESS_FLAG);
        emailSendCommand.setSubject(SUCCESS_FLAG);

        RespWrapVo<EmailReplyVo> replyVoRespWrapVo = messageClient.sendWithReplyAlicloud(emailSendCommand);

        assertEquals(SUCCESS.getCode(), replyVoRespWrapVo.getCode());
        assertEquals(SUCCESS.getMessage(), replyVoRespWrapVo.getMessage());
        assertEquals("5514439437b9491a80b6fcb04576dd6b", replyVoRespWrapVo.getData().getRequestId());
    }

    @Test
    @DisplayName("阿里云接口发送邮件-异常场景")
    void _02_sendWithReplyAlicloud_error() {
        final String ERROR_FLAG = "error";
        EmailSendCommand emailSendCommand = new EmailSendCommand();
        emailSendCommand.setText(ERROR_FLAG);
        emailSendCommand.setSubject(ERROR_FLAG);
        emailSendCommand.setSendTo(randomString());

        RespWrapVo<EmailReplyVo> replyVoRespWrapVo = messageClient.sendWithReplyAlicloud(emailSendCommand);

        assertEquals(UNKNOWN_ERROR.getCode(), replyVoRespWrapVo.getCode());
    }

}