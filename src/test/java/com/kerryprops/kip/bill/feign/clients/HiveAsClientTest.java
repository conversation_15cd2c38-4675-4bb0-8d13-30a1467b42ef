package com.kerryprops.kip.bill.feign.clients;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.dao.entity.HiveContextAware;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.hiveas.webservice.vo.resp.BuildingResponseVo;
import com.kerryprops.kip.hiveas.webservice.vo.resp.ProjectBuildingVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

/**
 * HiveAsClientTest.
 *
 * <AUTHOR> 2024-08-22 09:52:43
 **/
@DisplayName("HiveAsClient单元测试")
class HiveAsClientTest extends BaseIntegrationTest {

    private static final List<String> BUILDING_ID_LIST_MOCK = List.of("QHKC-A1"
            , "QHKC-A2", "QHKC-A3", "QHKC-A4", "QHKC-A5", "QHKC-A6", "QHKC-A7", "QHKC-A8", "QHKC-A9", "QHKC-A10"
            , "QHKC-P1", "QHKC-P2", "QHKC-P3", "QHKC-P4", "QHKC-P5", "QHKC-P6", "QHKC-P7", "QHKC-P8", "QHKC-P9"
            , "QHKC-P10", "FZHYF-H1", "FZHYF-H2", "FZHYF-H3", "FZHYF-H4", "FZHYF-H6", "FZHYF-H1", "FZHYF-H1", "FZJLZX-A6");

    @Autowired
    HiveAsClient hiveAsClient;

    @Test
    @DisplayName("getBuildingByIds-入参为空场景")
    void _01_01_getBuildingByIds_empty_null_success() {
        // 执行待验证部分
        List<BuildingResponseVo> buildingResponseVosNull = hiveAsClient.getBuildingByIds(null);
        List<BuildingResponseVo> buildingResponseVosEmpty = hiveAsClient.getBuildingByIds(Collections.emptyList());

        // 验证执行结果
        Assertions.assertTrue(CollectionUtils.isEmpty(buildingResponseVosNull));
        Assertions.assertTrue(CollectionUtils.isEmpty(buildingResponseVosEmpty));
    }

    @Test
    @DisplayName("getBuildingByIds-正常场景")
    void _01_02_getBuildingByIds_success() {
        // 执行待验证部分
        List<BuildingResponseVo> buildingResponseVos = hiveAsClient.getBuildingByIds(BUILDING_ID_LIST_MOCK);

        // 验证执行结果
        Assertions.assertFalse(CollectionUtils.isEmpty(buildingResponseVos));
        Assertions.assertEquals(9, buildingResponseVos.size());
        Assertions.assertEquals(3, buildingResponseVos.stream().filter(buildingResponseVo
                -> "192".equals(buildingResponseVo.getBuilding().getProjectId())).count());
        Assertions.assertEquals(1, buildingResponseVos.stream().filter(buildingResponseVo
                -> "QHKC-P1".equals(buildingResponseVo.getBuilding().getId())).count());
        Assertions.assertEquals(1, buildingResponseVos.stream().filter(buildingResponseVo
                -> "QHKC-A2".equals(buildingResponseVo.getBuilding().getId())).count());
    }

    @Test
    @DisplayName("getBuildingById-正常场景")
    void _01_03_getBuildingById_success() {
        // 执行待验证部分
        final String BUILDING_ID = "QHKC-A2";
        BuildingResponseVo buildingResponseVos = hiveAsClient.getBuildingById(BUILDING_ID);

        // 验证执行结果
        Assertions.assertEquals("192", buildingResponseVos.getBuilding().getProjectId());
    }

    @Test
    @DisplayName("getPropertyManagementCo-正常场景")
    void _01_04_getPropertyManagementCo_success() {
        // 执行待验证部分
        final String BUILDING_ID = "QHKC-A2";
        String propertyManagementCo = hiveAsClient.getPropertyManagementCo(BUILDING_ID);

        // 验证执行结果
        Assertions.assertEquals("31007", propertyManagementCo);
    }

    @Test
    @DisplayName("convertToJdeBus-入参为空场景")
    void _02_01_convertToJdeBus_success() {
        // 执行待验证部分
        List<String> jdeBusEmpty = hiveAsClient.convertToJdeBus(Collections.emptyList());

        // 验证执行结果
        Assertions.assertTrue(CollectionUtils.isEmpty(jdeBusEmpty));
    }

    @Test
    @DisplayName("convertToJdeBus-正常场景")
    void _02_02_convertToJdeBus_success() {
        // 执行待验证部分
        List<String> jdeBus = hiveAsClient.convertToJdeBus(BUILDING_ID_LIST_MOCK);

        // 验证执行结果
        Assertions.assertFalse(CollectionUtils.isEmpty(jdeBus));
        Assertions.assertEquals(19, jdeBus.size());
        Assertions.assertEquals(1, jdeBus.stream().filter("41225302"::equals).count());
        Assertions.assertEquals(1, jdeBus.stream().filter("31384300"::equals).count());
    }

    @Test
    @DisplayName("getCenterByIds-入参为空场景")
    void _03_01_getCenterByIds_empty_success() {
        // 执行待验证部分
        List<ProjectBuildingVO> projectBuildingVOSNull = hiveAsClient.getCenterByIds(null);
        List<ProjectBuildingVO> projectBuildingVOSEmpty = hiveAsClient.getCenterByIds(null);

        // 验证执行结果
        Assertions.assertTrue(CollectionUtils.isEmpty(projectBuildingVOSNull));
        Assertions.assertTrue(CollectionUtils.isEmpty(projectBuildingVOSEmpty));
    }

    @Test
    @DisplayName("getCenterByIds-正常场景")
    void _03_02_getCenterByIds_success() {
        // 执行待验证部分
        List<ProjectBuildingVO> projectBuildingVOS = hiveAsClient.getCenterByIds(new String[]{"192", "181", "FZHYF"
                , "FZJLZX", "190", "HNZZ", "SYYSJ", "SYYSDY", "SYYSG", "185", "QHDHBT", "HKPFYT"});

        // 验证执行结果
        Assertions.assertFalse(CollectionUtils.isEmpty(projectBuildingVOS));
        Assertions.assertEquals(6, projectBuildingVOS.size());
        Assertions.assertEquals("[SYYSJ, FZHYF, SYYSDY, 190, 192, FZJLZX]", projectBuildingVOS.stream()
                .map(e -> e.getProject().getId()).toList().toString());
    }

    @Test
    @DisplayName("getRoomHiveContext-正常场景")
    void _04_01_getRoomHiveContext_success() {
        // 执行待验证部分
        final String ROOM_ID = "8aaa84897bc9925e017bc9b1307100df";
        HiveContextAware hiveContextAware = hiveAsClient.getRoomHiveContext(ROOM_ID);

        // 验证执行结果
        Assertions.assertEquals("192", hiveContextAware.getProjectId());
        Assertions.assertEquals("QHKC-A2", hiveContextAware.getBuildingId());
        Assertions.assertEquals("8aaa8a0b7bc94053017bc949bc1300e4", hiveContextAware.getFloorId());
        Assertions.assertEquals(ROOM_ID, hiveContextAware.getRoomId());
        Assertions.assertEquals("B", hiveContextAware.getPositionItem().getRoomName());
    }

    @Test
    @DisplayName("convertToJdeBus-正常场景")
    void _05_01_convertToJdeBus_success() {
        // 执行待验证部分
        List<String> jdeBus = hiveAsClient.convertToJdeBus(new String[]{"192", "181", "FZHYF"
                , "FZJLZX", "190", "HNZZ", "SYYSJ", "SYYSDY", "SYYSG", "185", "QHDHBT", "HKPFYT"});

        // 验证执行结果
        Assertions.assertFalse(CollectionUtils.isEmpty(jdeBus));
        Assertions.assertEquals(51, jdeBus.size());
    }

    @Test
    @DisplayName("populateDataFields-buildingIds不为空-正常场景")
    void _06_01_populateDataFields_buildingIds_not_empty_success() {
        LoginUser loginUser = new LoginUser();
        loginUser.setRoles("SUPER_ADMIN");
        UserInfoUtils.setUser(loginUser);

        // 执行待验证部分
        List<String> jdeBus = hiveAsClient.populateDataFields("192", BUILDING_ID_LIST_MOCK);

        // 验证执行结果
        Assertions.assertFalse(CollectionUtils.isEmpty(jdeBus));
        Assertions.assertEquals(19, jdeBus.size());
    }

    @Test
    @DisplayName("populateDataFields-超级管理员场景-正常场景")
    void _06_02_populateDataFields_super_admin_success() {
        LoginUser loginUser = new LoginUser();
        loginUser.setRoles("SUPER_ADMIN");
        UserInfoUtils.setUser(loginUser);

        // 执行待验证部分
        List<String> jdeBus = hiveAsClient.populateDataFields("192", Collections.emptyList());

        // 验证执行结果
        Assertions.assertNull(jdeBus);
    }

    @Test
    @DisplayName("populateDataFields-projectId非空场景-正常场景")
    void _06_03_populateDataFields_projectId_not_blank_success() {
        LoginUser loginUser = new LoginUser();
        loginUser.setRoles("NOT_SUPER_ADMIN");
        loginUser.setBuildingIds(StringUtils.join(BUILDING_ID_LIST_MOCK, ","));
        UserInfoUtils.setUser(loginUser);

        // 执行待验证部分
        List<String> jdeBus = hiveAsClient.populateDataFields("192", Collections.emptyList());

        // 验证执行结果
        Assertions.assertTrue(CollectionUtils.isNotEmpty(jdeBus));
        jdeBus.forEach(jdeBu -> {
            Assertions.assertTrue(List.of("31384400", "41225302", "31384300", "41225303", "41225400", "43285400")
                    .contains(jdeBu));
        });
    }

}