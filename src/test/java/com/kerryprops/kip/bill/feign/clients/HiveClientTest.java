package com.kerryprops.kip.bill.feign.clients;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;

/**
 * <AUTHOR> 2024-03-19 17:09:46
 **/
class HiveClientTest extends BaseIntegrationTest {

    @Resource
    private HiveClient hiveClient;

    @Test
    void getBuildingByBuAndJdeRoomNo_ok() {
        var request = new HiveClient.HiveBuBuildingRequest();
        request.setBu(List.of("31124200"));
        request.setJdeRoomNo(List.of("1001"));
        var vos = hiveClient.getBuildingByBuAndJdeRoomNo(List.of(request));
        Assertions.assertNotNull(vos);
    }

}