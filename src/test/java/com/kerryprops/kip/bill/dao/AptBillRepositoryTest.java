package com.kerryprops.kip.bill.dao;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.enums.BillPushStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.utils.RandomUtil;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * AptBillRepositoryTest.
 *
 * <AUTHOR> Yu 2024-08-02 11:51:05
 **/
class AptBillRepositoryTest extends BaseIntegrationTest {

    @Resource
    AptBillRepository aptBillRepository;

    @Test
    void updateBillStatus4Push_ok() {
        AptBill bill = RandomUtil.randomObject(AptBill.class);
        aptBillRepository.save(bill);

        Long id = bill.getId();
        boolean flag = aptBillRepository.updateBillStatus4Push(id, BillPushStatus.PUSH_FAILED, null);
        Assertions.assertTrue(flag);

        AptBill vo = aptBillRepository.findById(id).orElseThrow();
        Assertions.assertEquals(BillPushStatus.PUSH_FAILED, vo.getPushStatus());

        aptBillRepository.updateBillStatus4Push(id, BillPushStatus.PUSHED, BillStatus.TO_BE_PAID);
        AptBill vo2 = aptBillRepository.findById(id).orElseThrow();
        Assertions.assertEquals(BillPushStatus.PUSHED, vo2.getPushStatus());
        Assertions.assertEquals(BillStatus.TO_BE_PAID, vo2.getStatus());
    }

}