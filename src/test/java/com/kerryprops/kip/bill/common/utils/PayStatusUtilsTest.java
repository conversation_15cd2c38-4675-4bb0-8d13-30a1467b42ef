package com.kerryprops.kip.bill.common.utils;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * PayStatusUtils测试类
 *
 * <AUTHOR>
 * @date 2024-10-12
 */
public class PayStatusUtilsTest extends BaseIntegrationTest {

    @Test
    void _01_getPayStatusPredicate_success() {
        List<BillPaymentStatus> billPaymentStatusesTest = List.of(BillPaymentStatus.PAID
                , BillPaymentStatus.PAYING, BillPaymentStatus.TO_BE_PAID);

        billPaymentStatusesTest.forEach(billPaymentStatus -> {
            Assertions.assertInstanceOf(Optional.class, PayStatusUtils.getPayStatusPredicate(billPaymentStatus));
        });
    }

    @Test
    void _02_transPaymentStatus_success() {
        BillPaymentStatus[] billPaymentStatusesTest = BillPaymentStatus.values();

        Arrays.stream(billPaymentStatusesTest).forEach(billPaymentStatus -> {
            String billPaymentStatusStr = billPaymentStatus.toString();
            Assertions.assertTrue(StringUtils.isNotEmpty(PayStatusUtils.transPaymentStatus(billPaymentStatusStr)));
        });
    }

}
