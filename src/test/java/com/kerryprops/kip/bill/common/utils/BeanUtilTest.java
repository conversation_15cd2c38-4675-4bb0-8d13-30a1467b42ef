package com.kerryprops.kip.bill.common.utils;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.dao.entity.AptBillDirectDebitsAgreement;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> 2024-03-08 12:17:15
 **/
class BeanUtilTest extends BaseIntegrationTest {

    @Test
    void copy_ok() {
        Map<String, Object> map = new HashMap<>();
        map.put("signTime", new Date());

        var agr = BeanUtil.copy(map, AptBillDirectDebitsAgreement.class);
        Assertions.assertNotNull(agr.getSignTime());
    }

}