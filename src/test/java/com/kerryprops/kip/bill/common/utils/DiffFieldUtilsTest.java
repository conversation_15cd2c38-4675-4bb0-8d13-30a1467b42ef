package com.kerryprops.kip.bill.common.utils;

import com.kerryprops.kip.bill.webservice.vo.resp.OperationChangedFiledRespVo;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DiffFieldUtilsTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @since - 2025-5-8
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("类字段对比工具类-单元测试")
class DiffFieldUtilsTest {

    @Test
    @DisplayName("正常场景：两个对象类型不同差异")
    void diffFields_WithDifferences() {
        // Act
        List<OperationChangedFiledRespVo> result = DiffFieldUtils.diffFields("", 1L, "1");

        // Assert
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    @DisplayName("异常场景：两个对象为null")
    void diffFields_BothObjectsNull() {
        // Arrange
        Object obj1 = null;
        Object obj2 = null;

        // Act
        List<OperationChangedFiledRespVo> result = DiffFieldUtils.diffFields("", obj1, obj2);

        // Assert
        assertEquals(0, result.size());
    }

    @Test
    @DisplayName("异常场景：一个对象为null")
    void diffFields_OneObjectNull() {
        // Arrange
        Object obj1 = new Object();

        Object obj2 = null;

        // Act
        List<OperationChangedFiledRespVo> result = DiffFieldUtils.diffFields("", obj1, obj2);

        // Assert
        assertEquals(0, result.size());
    }

    @Test
    @DisplayName("正常场景：两个对象字段无差异")
    void diffFields_NoDifferences() {
        // Arrange
        Object object = new Object();

        // Act
        List<OperationChangedFiledRespVo> result = DiffFieldUtils.diffFields("", List.of(object), List.of(object));

        // Assert
        assertEquals(0, result.size());
    }


}