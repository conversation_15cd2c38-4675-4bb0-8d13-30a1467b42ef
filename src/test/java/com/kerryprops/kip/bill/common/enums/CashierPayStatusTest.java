package com.kerryprops.kip.bill.common.enums;

import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * CashierPayStatusTest.
 *
 * <AUTHOR> 2025-01-21 15:40:30
 **/
class CashierPayStatusTest {

    @Test
    @DisplayName("支付记录为空时的状态处理")
    void of_should_null() {
        AptPaymentInfo aptPaymentInfo = null;

        CashierPayStatus cashierPayStatus = CashierPayStatus.of(aptPaymentInfo);

        Assertions.assertThat(cashierPayStatus).isNull();
    }

}