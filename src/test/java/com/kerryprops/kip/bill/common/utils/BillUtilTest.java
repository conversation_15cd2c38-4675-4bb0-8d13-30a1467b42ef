package com.kerryprops.kip.bill.common.utils;

import com.kerryprops.kip.bill.utils.BillUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

/**
 * BillUtilTest.
 *
 * <AUTHOR> 2024-07-31 16:49:56
 **/
class BillUtilTest {

    @Test
    void calcFeeTaxAmount_ok() {
        BigDecimal decimal = BillUtil.calcFeeTaxAmount(BigDecimal.valueOf(1050), new BigDecimal("0.05"));
        Assertions.assertEquals(50, decimal.intValue());
    }

}