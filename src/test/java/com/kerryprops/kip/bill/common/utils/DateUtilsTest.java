package com.kerryprops.kip.bill.common.utils;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import static com.kerryprops.kip.bill.common.utils.DateUtils.YYYY_MM_DD;

/**
 * DateUtils测试类
 *
 * <AUTHOR>
 * @date 2024-10-12
 */
public class DateUtilsTest extends BaseIntegrationTest {

    private final static String DATE_STR_YEAR_DAY_TEST = "124032"; // 儒日历

    private final static String DATE_STR_YYYYMMDD_TEST = "2024-02-01";

    @Test
    void _01_convertDateYYYYMMDD_success() {
        String dateYYYYMMDD = DateUtils.convertDateYYYYMMDD(DATE_STR_YEAR_DAY_TEST);

        Assertions.assertEquals(DATE_STR_YYYYMMDD_TEST, dateYYYYMMDD);
    }

    @Test
    void _02_convertDateToJuLian_success() throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD);
        Date dateTest = sdf.parse(DATE_STR_YYYYMMDD_TEST);

        String dateStr = DateUtils.convertDateToJuLian(dateTest);

        Assertions.assertEquals(DATE_STR_YEAR_DAY_TEST, dateStr);
    }

    @Test
    void _03_parseDate_success() {
        Date dateActual = DateUtils.parseDate(YYYY_MM_DD, DATE_STR_YYYYMMDD_TEST);

        Assertions.assertEquals(DATE_STR_YYYYMMDD_TEST, new SimpleDateFormat(YYYY_MM_DD).format(dateActual));
    }

}
