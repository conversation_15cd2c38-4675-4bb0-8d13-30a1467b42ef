package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.common.enums.BillPayModule;
import com.kerryprops.kip.bill.common.enums.BillPayStateEnum;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.PaymentPayType;
import com.kerryprops.kip.bill.common.exceptions.AppException;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptJdeBillRepository;
import com.kerryprops.kip.bill.dao.AptPayConfigRepository;
import com.kerryprops.kip.bill.dao.AptPaymentBillRepository;
import com.kerryprops.kip.bill.dao.AptPaymentInfoRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptJdeBill;
import com.kerryprops.kip.bill.dao.entity.AptPayConfig;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.service.AptBillDirectDebitsBatchBillService;
import com.kerryprops.kip.bill.service.AptBillWxTemplateMsgService;
import com.kerryprops.kip.bill.service.AptPayService;
import com.kerryprops.kip.pmw.client.resource.AsynPaymentResultResource;
import com.kerryprops.kip.pmw.client.resource.AsyncPaymentFailedResource;
import com.kerryprops.kip.pmw.client.security.CipherUtil;
import com.querydsl.core.types.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import static com.kerryprops.kip.bill.common.enums.BillPaymentStatus.DIRECT_DEBIT_PAYING;
import static com.kerryprops.kip.bill.common.enums.BillPaymentStatus.PAID;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomString;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;

/**
 * AptBillPaymentCallbackServiceTest单元测试
 *
 * <AUTHOR>
 * @date 2024-12-16
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("C端-支付回调-支付部分-测试用例")
class AptBillPaymentCallbackServiceTest {

    @Mock
    private CipherUtil cipherUtil;

    @Mock
    private AptPayService aptPayService;

    @Mock
    private AptBillRepository aptBillRepository;

    @Mock
    private AptPaymentBillRepository aptPaymentBillRepository;

    @Mock
    private AptJdeBillRepository jdeBillRepository;

    @Mock
    private AptPayConfigRepository payConfigRepository;

    @Mock
    private AptPaymentInfoRepository paymentInfoRepository;

    @Mock
    private AptBillWxTemplateMsgService templateMsgService;

    @Mock
    private AptBillDirectDebitsBatchBillService batchBillService;

    @InjectMocks
    private AptBillPaymentCallbackServiceImpl aptBillPaymentCallbackService;

    @Test
    @DisplayName("C端-支付回调-支付部分-异常场景：支付信息为空")
    void _01_handlePaymentCallback_paymentInfo_not_found_error() {
        doReturn(Optional.empty()).when(paymentInfoRepository).findById(any());
        assertThrows(AppException.class, () -> aptBillPaymentCallbackService
                .handlePaymentCallback(new AsynPaymentResultResource.AsynPaymentResultBodyResource()));
    }

    @Test
    @DisplayName("C端-支付回调-支付部分-正常场景：杂费")
    void _02_handlePaymentCallback_cashier_fee_success() {
        var paymentInfoMock = new AptPaymentInfo();
        paymentInfoMock.setBillPayModule(BillPayModule.CASHIER_FEE);
        paymentInfoMock.setPayType(PaymentPayType.WECHAT);
        paymentInfoMock.setProjectId(randomString());
        doReturn(Optional.of(paymentInfoMock)).when(paymentInfoRepository).findById(any());

        doReturn(List.of(new AptPayConfig())).when(payConfigRepository).findAll((Predicate) any());

        var asynPaymentResultBodyResourceMock = new AsynPaymentResultResource.AsynPaymentResultBodyResource();
        asynPaymentResultBodyResourceMock.setOrderNo(randomString());
        asynPaymentResultBodyResourceMock.setPayOption("WECHATPAY");
        asynPaymentResultBodyResourceMock.setPspName(PaymentPayType.ALIPAY.getPspName());
        asynPaymentResultBodyResourceMock.setState(BillPayStateEnum.SUCCESS.name());
        asynPaymentResultBodyResourceMock.setFinishedDate("2024-12-16T10:15:30+01:00");
        asynPaymentResultBodyResourceMock.setOrderAmount("1.25");
        aptBillPaymentCallbackService.handlePaymentCallback(asynPaymentResultBodyResourceMock);
    }

    @Test
    @DisplayName("C端-支付回调-支付部分-支付失败场景：收银台")
    void _03_handlePaymentCallback_cashier_pay_fail() {
        var paymentInfoMock = new AptPaymentInfo();
        paymentInfoMock.setBillPayModule(BillPayModule.CASHIER);
        paymentInfoMock.setPayType(PaymentPayType.WECHAT);
        paymentInfoMock.setProjectId(randomString());
        paymentInfoMock.setAdvanceAmount(BigDecimal.TEN);
        paymentInfoMock.setAmt(paymentInfoMock.getAdvanceAmount().doubleValue());
        paymentInfoMock.setPaymentStatus(BillPaymentStatus.TO_BE_PAID);
        doReturn(Optional.of(paymentInfoMock)).when(paymentInfoRepository).findById(any());

        var asynPaymentResultBodyResourceMock = new AsynPaymentResultResource.AsynPaymentResultBodyResource();
        asynPaymentResultBodyResourceMock.setOrderNo(randomString());
        asynPaymentResultBodyResourceMock.setPayOption("WECHATPAY");
        asynPaymentResultBodyResourceMock.setPspName(PaymentPayType.ALIPAY.getPspName());
        asynPaymentResultBodyResourceMock.setState(BillPayStateEnum.EXPIRED.name());
        asynPaymentResultBodyResourceMock.setFinishedDate("2024-12-16T10:15:30+01:00");
        asynPaymentResultBodyResourceMock.setOrderAmount("1.25");
        aptBillPaymentCallbackService.handlePaymentCallback(asynPaymentResultBodyResourceMock);
    }


    @Test
    @DisplayName("C端-支付回调-支付部分-正常场景：代扣场景")
    void _04_handlePaymentCallback_direct_debit_success() {
        var paymentInfoMock = new AptPaymentInfo();
        paymentInfoMock.setPaymentStatus(DIRECT_DEBIT_PAYING);
        paymentInfoMock.setPayType(PaymentPayType.WECHAT);
        paymentInfoMock.setProjectId(randomString());
        doReturn(Optional.of(paymentInfoMock)).when(paymentInfoRepository).findById(any());

        doReturn(List.of(new AptPayConfig())).when(payConfigRepository).findAll((Predicate) any());

        AptBill aptBillMock1 = new AptBill();
        aptBillMock1.setBillNo(randomString());

        AptBill aptBillMock2 = new AptBill();
        aptBillMock2.setBillNo(randomString());
        doReturn(List.of(aptBillMock1, aptBillMock2)).when(batchBillService).getAptBillsByPaymentOrderNo(anyString());

        doReturn(List.of(new AptJdeBill())).when(jdeBillRepository).findAll((Predicate) any());

        var asynPaymentResultBodyResourceMock = new AsynPaymentResultResource.AsynPaymentResultBodyResource();
        asynPaymentResultBodyResourceMock.setOrderNo(randomString());
        asynPaymentResultBodyResourceMock.setPayOption("ALI_WITHHOLD");
        asynPaymentResultBodyResourceMock.setPspName(PaymentPayType.ALI_WITHHOLD.getPspName());
        asynPaymentResultBodyResourceMock.setState(BillPayStateEnum.SUCCESS.name());
        asynPaymentResultBodyResourceMock.setFinishedDate("2024-12-16T10:15:30+01:00");
        asynPaymentResultBodyResourceMock.setOrderAmount("1.25");
        aptBillPaymentCallbackService.handlePaymentCallback(asynPaymentResultBodyResourceMock);
    }

    @Test
    @DisplayName("C端-支付回调-支付部分-回调支付失败场景-账单号为空异常")
    void _05_handlePaymentFailedCallback_payment_info_id_empty_error() {
        var bodyResourceMock = new AsyncPaymentFailedResource.AsyncPaymentFailedBodyResource();
        bodyResourceMock.setOrderNo(StringUtils.EMPTY);
        var paymentFailedResourceMock = new AsyncPaymentFailedResource(null, bodyResourceMock, null);

        assertDoesNotThrow(() -> aptBillPaymentCallbackService.handlePaymentFailedCallback(paymentFailedResourceMock));
    }

    @Test
    @DisplayName("C端-支付回调-支付部分-回调支付失败场景-错误信息为空异常")
    void _06_handlePaymentFailedCallback_error_msg_empty_error() {
        var bodyResourceMock = new AsyncPaymentFailedResource.AsyncPaymentFailedBodyResource();
        bodyResourceMock.setOrderNo(randomString());
        bodyResourceMock.setErrorMsg(null);
        var paymentFailedResourceMock = new AsyncPaymentFailedResource(null, bodyResourceMock, null);

        assertDoesNotThrow(() -> aptBillPaymentCallbackService.handlePaymentFailedCallback(paymentFailedResourceMock));
    }

    @Test
    @DisplayName("C端-支付回调-支付部分-回调支付失败场景：代扣场景")
    void _07_handlePaymentFailedCallback_success() {
        var paymentInfoMock = new AptPaymentInfo();
        paymentInfoMock.setPaymentStatus(DIRECT_DEBIT_PAYING);
        paymentInfoMock.setPayType(PaymentPayType.WECHAT);
        paymentInfoMock.setFailedReason(randomString());
        doReturn(Optional.of(paymentInfoMock)).when(paymentInfoRepository).findById(any());

        var bodyResourceMock = new AsyncPaymentFailedResource.AsyncPaymentFailedBodyResource();
        bodyResourceMock.setOrderNo(randomString());
        bodyResourceMock.setErrorMsg(randomString());
        var paymentFailedResourceMock = new AsyncPaymentFailedResource(null, bodyResourceMock, null);

        assertDoesNotThrow(() -> aptBillPaymentCallbackService.handlePaymentFailedCallback(paymentFailedResourceMock));
    }

    @Test
    @DisplayName("C端-支付回调-支付部分-正常场景：普通回调")
    void _08_handlePaymentCallback_cashier_ordinary_success() {
        var paymentInfoMock = new AptPaymentInfo();
        paymentInfoMock.setBillPayModule(BillPayModule.KERRY);
        paymentInfoMock.setPayType(PaymentPayType.WECHAT);
        paymentInfoMock.setProjectId(randomString());
        paymentInfoMock.setPaymentStatus(PAID);
        doReturn(Optional.of(paymentInfoMock)).when(paymentInfoRepository).findById(any());

        doReturn(List.of(new AptPayConfig())).when(payConfigRepository).findAll((Predicate) any());

        var asynPaymentResultBodyResourceMock = new AsynPaymentResultResource.AsynPaymentResultBodyResource();
        asynPaymentResultBodyResourceMock.setOrderNo(randomString());
        asynPaymentResultBodyResourceMock.setPayOption("WECHATPAY");
        asynPaymentResultBodyResourceMock.setPspName(PaymentPayType.ALIPAY.getPspName());
        asynPaymentResultBodyResourceMock.setState(BillPayStateEnum.SUCCESS.name());
        asynPaymentResultBodyResourceMock.setFinishedDate("2024-12-16T10:15:30+01:00");
        asynPaymentResultBodyResourceMock.setOrderAmount("1.25");
        aptBillPaymentCallbackService.handlePaymentCallback(asynPaymentResultBodyResourceMock);
    }

    @Test
    @DisplayName("C端-支付回调-支付部分-正常场景：普通回调")
    void _09_handlePaymentCallback_cashier_ordinary_success() {
        var paymentInfoMock = new AptPaymentInfo();
        paymentInfoMock.setBillPayModule(BillPayModule.KERRY);
        paymentInfoMock.setPayType(PaymentPayType.WECHAT);
        paymentInfoMock.setProjectId(randomString());
        paymentInfoMock.setPaymentStatus(DIRECT_DEBIT_PAYING);
        doReturn(Optional.of(paymentInfoMock)).when(paymentInfoRepository).findById(any());

        doReturn(List.of(new AptPayConfig())).when(payConfigRepository).findAll((Predicate) any());

        var asynPaymentResultBodyResourceMock = new AsynPaymentResultResource.AsynPaymentResultBodyResource();
        asynPaymentResultBodyResourceMock.setOrderNo(randomString());
        asynPaymentResultBodyResourceMock.setPayOption("WECHATPAY");
        asynPaymentResultBodyResourceMock.setPspName(PaymentPayType.ALIPAY.getPspName());
        asynPaymentResultBodyResourceMock.setState(BillPayStateEnum.SUCCESS.name());
        asynPaymentResultBodyResourceMock.setFinishedDate("2024-12-16T10:15:30+01:00");
        asynPaymentResultBodyResourceMock.setOrderAmount("1.25");
        aptBillPaymentCallbackService.handlePaymentCallback(asynPaymentResultBodyResourceMock);
    }

}
