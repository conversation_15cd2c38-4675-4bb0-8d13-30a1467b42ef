package com.kerryprops.kip.bill.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kerryprops.kip.bill.common.enums.BillPushStatus;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.config.DataMigrationConfig;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptBillOperator;
import com.kerryprops.kip.bill.feign.clients.BUserClient;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.clients.MessageCenterClient;
import com.kerryprops.kip.bill.feign.clients.MessageClient;
import com.kerryprops.kip.bill.feign.entity.IdentityAuditType;
import com.kerryprops.kip.bill.feign.entity.MessageDto;
import com.kerryprops.kip.bill.feign.entity.TenantStaffResponse;
import com.kerryprops.kip.bill.feign.entity.WxTemplateMsgRequestCommand;
import com.kerryprops.kip.bill.service.AptBillOperationService;
import com.kerryprops.kip.hiveas.webservice.resource.resp.RoomResp;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.time.Month;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.kerryprops.kip.bill.common.enums.BillPaymentStatus.DIRECT_DEBIT_PAID;
import static com.kerryprops.kip.bill.common.enums.BillPaymentStatus.PAYING;
import static com.kerryprops.kip.bill.common.enums.BillPaymentStatus.TO_BE_PAID;
import static com.kerryprops.kip.bill.common.enums.BillPushStatus.PUSHED;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomObject;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * AptBillPushServiceImplTest.
 *
 * <AUTHOR> Yu 2024-08-21 18:10:44
 **/
@ExtendWith(MockitoExtension.class)
@DisplayName("公寓小区-账单推送-单元测试")
class AptBillPushServiceImplTest {

    @Mock
    ObjectMapper objectMapper;

    @Mock
    AptBillService aptBillService;

    @Mock
    HiveAsClient hiveAsClient;

    @Mock
    BUserClient bUserClient;

    @Mock
    MessageClient messageClient;

    @Mock
    MessageCenterClient messageCenterClient;

    @Mock
    DataMigrationConfig dm;

    @Mock
    AptBillOperationService operationService;

    @InjectMocks
    AptBillPushServiceImpl aptBillPushServiceImpl;

    @Test
    @DisplayName("公寓小区-账单推送：正常场景")
    void pushAptBill() {
        AptBill aptBill = randomObject(AptBill.class);
        aptBill.setPaymentStatus(TO_BE_PAID);
        when(aptBillService.getBillById(anyLong())).thenReturn(aptBill);
        RoomResp roomResp = randomObject(RoomResp.class);
        when(hiveAsClient.getRoomById(anyString())).thenReturn(new RespWrapVo<>(roomResp));
        when(bUserClient.getStaffList(any(), anyInt(), anyString())).thenReturn(new RespWrapVo<>(List.of(new TenantStaffResponse(0, "userId", IdentityAuditType.OFFICE, Integer.valueOf(0), "userName", "mobile", "buildingName", "projectId", "buildingNumber", "buildingUnitNumber", "companyName", "companyNumber", "companyUnitNumber", "roomNumber", "jdeBill", Integer.valueOf(0), "position", Integer.valueOf(0), Integer.valueOf(0), "remark", "email", LocalDateTime.of(2024, Month.AUGUST, 21, 18, 10, 44), new GregorianCalendar(2024, Calendar.AUGUST, 21, 18, 10).getTime(), new GregorianCalendar(2024, Calendar.AUGUST, 21, 18, 10).getTime()))));
        when(messageClient.sendWxTemplateMessage(any())).thenReturn(new RespWrapVo<>(Boolean.TRUE));
        when(messageCenterClient.sendMessage(any())).thenReturn(new RespWrapVo<>("data"));
        when(dm.getWxMsgUrl()).thenReturn("getWxMsgUrlResponse");
        when(operationService.saveOperationLog(any(), any(), any(), anyString())).thenReturn(true);

        String roomId = aptBill.getRoomId();
        aptBillPushServiceImpl.pushAptBill(Map.of(roomId, List.of(aptBill)), Set.of(roomId), AptBillOperator.UNKNOWN, "projectId");

        verify(aptBillService, times(1)).updateBillStatus4Push(anyLong(), any(), any());
    }

    @Test
    @DisplayName("公寓小区-账单推送-单个room场景：异常场景，站内信和微信模板消息均发送失败")
    void pushOneRoomBill_messageAndWechatMeassageAllSendFailed() {
        // Arrange
        mockPushInitialData();
        AptBill aptBill = randomObject(AptBill.class);
        aptBill.setPaymentStatus(PAYING);
        when(aptBillService.getBillById(anyLong())).thenReturn(aptBill);

        when(messageCenterClient.sendMessage(any(MessageDto.class))).thenReturn(null);
        when(messageClient.sendWxTemplateMessage(any(
                WxTemplateMsgRequestCommand.class))).thenReturn(null);

        // Act
        BillPushStatus result = aptBillPushServiceImpl.pushOneRoomBill("roomId", List.of(aptBill), AptBillOperator.UNKNOWN);

        // Assert
        Assertions.assertEquals(BillPushStatus.PUSH_FAILED, result);
    }

    @Test
    @DisplayName("公寓小区-账单推送-单个room场景：异常场景，站内信和微信模板消息均发送报异常")
    void pushOneRoomBill_messageAndWechatMeassageAllSendException() {
        // Arrange
        mockPushInitialData();
        AptBill aptBill = randomObject(AptBill.class);
        aptBill.setPaymentStatus(PAYING);
        when(aptBillService.getBillById(anyLong())).thenReturn(aptBill);

        when(messageCenterClient.sendMessage(any(MessageDto.class))).thenThrow(new RuntimeException("站内信发送失败"));
        when(messageClient.sendWxTemplateMessage(any(
                WxTemplateMsgRequestCommand.class))).thenThrow(new RuntimeException("微信模板消息发送失败"));

        // Act
        BillPushStatus result = aptBillPushServiceImpl.pushOneRoomBill("roomId", List.of(aptBill), AptBillOperator.UNKNOWN);

        // Assert
        Assertions.assertEquals(BillPushStatus.PUSH_FAILED, result);
    }

    @Test
    @DisplayName("公寓小区-账单推送-单个room场景：正常场景,已支付跳过")
    void pushOneRoomBill_skipPaidBill_success() {
        // Arrange
        mockPushInitialData();
        AptBill aptBill = randomObject(AptBill.class);
        aptBill.setPaymentStatus(DIRECT_DEBIT_PAID);
        when(aptBillService.getBillById(anyLong())).thenReturn(aptBill);

        // Act
        BillPushStatus result = aptBillPushServiceImpl.pushOneRoomBill("roomId", List.of(aptBill), AptBillOperator.UNKNOWN);

        // Assert
        Assertions.assertEquals(PUSHED, result);
    }

    @Test
    @DisplayName("公寓小区-账单推送-单个room场景：正常场景")
    void pushOneRoomBill_success() {
        // Arrange
        mockPushInitialData();
        AptBill aptBill = randomObject(AptBill.class);
        aptBill.setPaymentStatus(PAYING);
        when(aptBillService.getBillById(anyLong())).thenReturn(aptBill);

        when(messageCenterClient.sendMessage(any(MessageDto.class))).thenReturn(new RespWrapVo<>());
        when(messageClient.sendWxTemplateMessage(any(
                WxTemplateMsgRequestCommand.class))).thenReturn(new RespWrapVo<>());

        // Act
        BillPushStatus result = aptBillPushServiceImpl.pushOneRoomBill("roomId", List.of(aptBill), AptBillOperator.UNKNOWN);

        // Assert
        Assertions.assertEquals(PUSHED, result);
    }

    @Test
    @DisplayName("公寓小区-账单推送-单个room场景：hives数据缺失记录日志，正常场景")
    void pushOneRoomBill_logFailedPush() {
        // Arrange
        AptBill aptBill = randomObject(AptBill.class);
        aptBill.setPaymentStatus(PAYING);
        when(hiveAsClient.getRoomById(anyString())).thenReturn(new RespWrapVo<>(new RoomResp()));

        // Act
        BillPushStatus result = aptBillPushServiceImpl.pushOneRoomBill("roomId", List.of(aptBill), AptBillOperator.UNKNOWN);

        // Assert
        Assertions.assertEquals(BillPushStatus.PUSH_FAILED, result);
    }

    @Test
    @DisplayName("正常场景：房间账单推送失败")
    void pushAptBill_pushFailed() {
        // Arrange
        AptBill aptBill = randomObject(AptBill.class);
        Map<String, List<AptBill>> billMap = Map.of("room1", List.of(aptBill));
        Set<String> roomIds = Set.of("room1");
        AptBillOperator aptBillOperator = AptBillOperator.ADD;
        String projectId = "project1";

        // Act and Assert
        assertDoesNotThrow(() -> aptBillPushServiceImpl.pushAptBill(billMap, roomIds, aptBillOperator, projectId));
    }

    @Test
    @DisplayName("正常场景：房间账单推送报异常")
    void pushAptBill_pushException() {
        // Arrange
        AptBill aptBill = randomObject(AptBill.class);
        Map<String, List<AptBill>> billMap = Map.of("room1", List.of(aptBill));
        Set<String> roomIds = Set.of("room1");
        AptBillOperator aptBillOperator = AptBillOperator.ADD;
        String projectId = "project1";

        when(hiveAsClient.getRoomById(anyString())).thenThrow(new RuntimeException("异常"));

        // Act and Assert
        assertDoesNotThrow(() -> aptBillPushServiceImpl.pushAptBill(billMap, roomIds, aptBillOperator,
                                                                               projectId));
    }

    private void mockPushInitialData() {
        RoomResp roomResp = randomObject(RoomResp.class);
        when(hiveAsClient.getRoomById(anyString())).thenReturn(new RespWrapVo<>(roomResp));
        TenantStaffResponse staffResponse = new TenantStaffResponse(0, "userId", IdentityAuditType.OFFICE, Integer.valueOf(0), "userName", "mobile", "buildingName", "projectId", "buildingNumber", "buildingUnitNumber", "companyName", "companyNumber", "companyUnitNumber", "roomNumber", "jdeBill", Integer.valueOf(0), "position", Integer.valueOf(0), Integer.valueOf(0), "remark", "email", LocalDateTime.of(2024, Month.AUGUST, 21, 18, 10, 44), new GregorianCalendar(2024, Calendar.AUGUST, 21, 18, 10).getTime(), new GregorianCalendar(2024, Calendar.AUGUST, 21, 18, 10).getTime());
        when(bUserClient.getStaffList(any(), anyInt(), anyString())).thenReturn(new RespWrapVo<>(List.of(staffResponse)));
    }
}