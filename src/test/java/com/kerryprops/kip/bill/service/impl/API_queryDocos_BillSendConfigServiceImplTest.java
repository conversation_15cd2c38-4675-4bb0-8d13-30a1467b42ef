package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.dao.BillSendConfigRepository;
import com.kerryprops.kip.bill.dao.entity.BillSendConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
class API_queryDocos_BillSendConfigServiceImplTest extends BaseIntegrationTest {

    private static final String B_ACCOUNT = "********";

    @Autowired
    private BillSendConfigServiceImpl impl;

    @Autowired
    private BillSendConfigRepository configRepository;

    @Test
    public void _01_queryDocos_no_data() {
        List<String> docos = impl.queryDocos(B_ACCOUNT);
        Assertions.assertTrue(CollectionUtils.isEmpty(docos));
    }

    @Test
    public void _02_queryDocos_all_deleted_data() {
        //prepare test data
        List<BillSendConfig> oldConfigs = List.of(
                BillSendConfig.builder().doco("112233").email("<EMAIL>")
                        .phoneNumber(StringUtils.EMPTY).loginNo(StringUtils.EMPTY)
                        .mcu("mcu1").unit("unit1").isDel(1)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .build(),
                BillSendConfig.builder().doco("224411").email("<EMAIL>")
                        .phoneNumber("***********").loginNo("********")
                        .mcu("mcu1").unit("unit2").isDel(1)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .build());
        List<BillSendConfig> newConfigs = configRepository.saveAll(oldConfigs);

        //execute & verify
        List<String> docos = impl.queryDocos(B_ACCOUNT);
        Assertions.assertTrue(CollectionUtils.isEmpty(docos));

        //clear test data
        configRepository.deleteAll(newConfigs);
    }

    @ParameterizedTest
    @NullAndEmptySource
    public void _03_queryDocos_special_bAccount(String bAccount) {
        //prepare test data
        List<BillSendConfig> oldConfigs = buildBillSendConfigs();
        List<BillSendConfig> newConfigs = configRepository.saveAll(oldConfigs);

        //execute & verify
        List<String> docos = impl.queryDocos(bAccount);
        Assertions.assertTrue(CollectionUtils.isEmpty(docos));

        //clear test data
        configRepository.deleteAll(newConfigs);
    }

    @Test
    public void _04_queryDocos_data_not_found() {
        //prepare test data
        List<BillSendConfig> oldConfigs = buildBillSendConfigs();
        List<BillSendConfig> newConfigs = configRepository.saveAll(oldConfigs);

        //execute & verify
        List<String> docos = impl.queryDocos("test1234567890");
        Assertions.assertTrue(CollectionUtils.isEmpty(docos));

        //clear test data
        configRepository.deleteAll(newConfigs);
    }

    @Test
    public void _05_queryDocos_data_found() {
        //prepare test data
        List<BillSendConfig> oldConfigs = buildBillSendConfigs();
        List<BillSendConfig> newConfigs = configRepository.saveAll(oldConfigs);

        //execute & verify
        List<String> actualDocos = impl.queryDocos(B_ACCOUNT);
        List<String> expectDocos = List.of("112244", "224411");
        Assertions.assertEquals(
                expectDocos.stream().collect(Collectors.joining(",")),
                actualDocos.stream().collect(Collectors.joining(",")));

        //clear test data
        configRepository.deleteAll(newConfigs);
    }

    private List<BillSendConfig> buildBillSendConfigs() {
        List<BillSendConfig> configs = List.of(
                BillSendConfig.builder().doco("112233").email("<EMAIL>")
                        .phoneNumber(StringUtils.EMPTY).loginNo(StringUtils.EMPTY)
                        .mcu("mcu1").unit("unit1").isDel(1)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .build(),
                BillSendConfig.builder().doco("224411").email("<EMAIL>")
                        .phoneNumber("***********").loginNo("********")
                        .mcu("mcu1").unit("unit2").isDel(0)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .build(),
                BillSendConfig.builder().doco("112255").email("<EMAIL>")
                        .phoneNumber("13764912346").loginNo("12345678")
                        .mcu("mcu1").unit("unit2").isDel(0)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .build(),
                BillSendConfig.builder().doco("112244").email("<EMAIL>")
                        .phoneNumber("***********").loginNo("********")
                        .mcu("mcu1").unit("unit2").isDel(0)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .build());
        return configs;
    }

}
