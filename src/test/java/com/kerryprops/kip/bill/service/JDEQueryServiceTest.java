package com.kerryprops.kip.bill.service;

import com.kerryprops.kip.bill.common.utils.jde.OracleJdbc;
import com.kerryprops.kip.bill.service.JDEQueryService.JDEQueryException;
import com.kerryprops.kip.bill.webservice.impl.JDEQueryController.QueryResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit tests for {@link JDEQueryService}.
 */
@ExtendWith(MockitoExtension.class)
class JDEQueryServiceTest {

    @InjectMocks
    private JDEQueryService jdeQueryService;

    private Connection mockConnection;
    private java.sql.Statement mockStatement;
    private ResultSet mockResultSet;
    private ResultSetMetaData mockMetaData;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        mockConnection = mock(Connection.class);
        mockStatement = mock(java.sql.Statement.class);
        mockResultSet = mock(ResultSet.class);
        mockMetaData = mock(ResultSetMetaData.class);
    }

    @Test
    void shouldExecuteQuerySuccessfullyWithNoParameters() throws SQLException {
        // Arrange
        String sql = "SELECT * FROM test_table";
        int timeout = 30;

        try (MockedStatic<OracleJdbc> mockedStatic = mockStatic(OracleJdbc.class)) {
            mockedStatic.when(OracleJdbc::getOracleConnection).thenReturn(mockConnection);
            when(mockConnection.createStatement()).thenReturn(mockStatement);
            when(mockStatement.executeQuery(anyString())).thenReturn(mockResultSet);
            when(mockResultSet.getMetaData()).thenReturn(mockMetaData);
            when(mockMetaData.getColumnCount()).thenReturn(2);
            when(mockMetaData.getColumnLabel(1)).thenReturn("column1");
            when(mockMetaData.getColumnLabel(2)).thenReturn("column2");
            
            // Mock result set iteration
            when(mockResultSet.next()).thenReturn(true, true, false); // Two rows
            when(mockResultSet.getObject(1)).thenReturn("value1_1", "value2_1");
            when(mockResultSet.getObject(2)).thenReturn("value1_2", "value2_2");

            // Act
            QueryResult result = jdeQueryService.executeQuery(sql, timeout);

            // Assert
            assertNotNull(result);
            List<Map<String, Object>> resultData = result.data();
            assertEquals(2, resultData.size());
            assertEquals("value1_1", resultData.get(0).get("COLUMN1"));
            assertEquals("value1_2", resultData.get(0).get("COLUMN2"));
            assertEquals("value2_1", resultData.get(1).get("COLUMN1"));
            assertEquals("value2_2", resultData.get(1).get("COLUMN2"));
            
            verify(mockStatement).setQueryTimeout(timeout);
        }
    }

    @Test
    void shouldExecuteQuerySuccessfullyWithDirectValues() throws SQLException {
        // Arrange
        String sql = "SELECT * FROM test_table WHERE id = 123";
        int timeout = 30;

        try (MockedStatic<OracleJdbc> mockedStatic = mockStatic(OracleJdbc.class)) {
            mockedStatic.when(OracleJdbc::getOracleConnection).thenReturn(mockConnection);
            when(mockConnection.createStatement()).thenReturn(mockStatement);
            when(mockStatement.executeQuery(anyString())).thenReturn(mockResultSet);
            when(mockResultSet.getMetaData()).thenReturn(mockMetaData);
            when(mockMetaData.getColumnCount()).thenReturn(2);
            when(mockMetaData.getColumnLabel(1)).thenReturn("id");
            when(mockMetaData.getColumnLabel(2)).thenReturn("name");
            
            // Mock result set iteration
            when(mockResultSet.next()).thenReturn(true, false); // One row
            when(mockResultSet.getObject(1)).thenReturn(123);
            when(mockResultSet.getObject(2)).thenReturn("Test Name");

            // Act
            QueryResult result = jdeQueryService.executeQuery(sql, timeout);

            // Assert
            assertNotNull(result);
            List<Map<String, Object>> resultData = result.data();
            assertEquals(1, resultData.size());
            assertEquals(123, resultData.get(0).get("ID"));
            assertEquals("Test Name", resultData.get(0).get("NAME"));
            
            verify(mockStatement).setQueryTimeout(timeout);
        }
    }

    @Test
    void shouldHandleEmptyResultSet() throws SQLException {
        // Arrange
        String sql = "SELECT * FROM empty_table";
        int timeout = 30;

        try (MockedStatic<OracleJdbc> mockedStatic = mockStatic(OracleJdbc.class)) {
            mockedStatic.when(OracleJdbc::getOracleConnection).thenReturn(mockConnection);
            when(mockConnection.createStatement()).thenReturn(mockStatement);
            when(mockStatement.executeQuery(anyString())).thenReturn(mockResultSet);
            when(mockResultSet.getMetaData()).thenReturn(mockMetaData);
            when(mockMetaData.getColumnCount()).thenReturn(2);
            
            // Mock empty result set
            when(mockResultSet.next()).thenReturn(false);

            // Act
            QueryResult result = jdeQueryService.executeQuery(sql, timeout);

            // Assert
            assertNotNull(result);
            List<Map<String, Object>> resultData = result.data();
            assertTrue(resultData.isEmpty());
        }
    }

    @Test
    void shouldThrowJDEQueryExceptionWhenParameterSettingFails() throws SQLException {
        // Arrange
        String sql = "SELECT * FROM test_table WHERE col1 = 'param1' AND col2 = 'param2'";
        int timeout = 30;
        SQLException sqlException = new SQLException("Parameter error");

        try (MockedStatic<OracleJdbc> mockedStatic = mockStatic(OracleJdbc.class)) {
            mockedStatic.when(OracleJdbc::getOracleConnection).thenReturn(mockConnection);
            when(mockConnection.createStatement()).thenReturn(mockStatement);
            // Wrap the exception in a JDEQueryException in the implementation
            // In JDEQueryService, the executeQuery method should catch SQLExceptions 
            // and wrap them in JDEQueryException
            when(mockStatement.executeQuery(anyString())).thenThrow(new JDEQueryException("Error setting query parameters", sqlException));

            // Act & Assert
            JDEQueryException exception = assertThrows(JDEQueryException.class, () -> jdeQueryService.executeQuery(sql,
                                                                                                                   timeout));
            
            assertEquals("Error setting query parameters", exception.getMessage());
            assertEquals(sqlException, exception.getCause());
        }
    }

    @Test
    void shouldThrowSQLExceptionWhenDatabaseConnectionFails() {
        // Arrange
        String sql = "SELECT * FROM test_table";
        int timeout = 30;
        RuntimeException connectionException = new RuntimeException("Connection failed");

        try (MockedStatic<OracleJdbc> mockedStatic = mockStatic(OracleJdbc.class)) {
            mockedStatic.when(OracleJdbc::getOracleConnection).thenThrow(connectionException);

            // Act & Assert
            Exception exception = assertThrows(RuntimeException.class, () -> jdeQueryService.executeQuery(sql,
                                                                                                          timeout));
            
            assertEquals("Connection failed", exception.getMessage());
        }
    }

    @Test
    void shouldHandleMultipleParameters() throws SQLException {
        // Arrange
        String sql = "SELECT * FROM test_table WHERE column1 = 'value' AND column2 = 123";
        int timeout = -10; // Negative timeout to trigger default

        try (MockedStatic<OracleJdbc> mockedStatic = mockStatic(OracleJdbc.class)) {
            mockedStatic.when(OracleJdbc::getOracleConnection).thenReturn(mockConnection);
            when(mockConnection.createStatement()).thenReturn(mockStatement);
            when(mockStatement.executeQuery(anyString())).thenReturn(mockResultSet);
            when(mockResultSet.getMetaData()).thenReturn(mockMetaData);
            when(mockMetaData.getColumnCount()).thenReturn(2);
            when(mockResultSet.next()).thenReturn(false); // Empty result

            // Act
            QueryResult result = jdeQueryService.executeQuery(sql, timeout);

            // Assert
            assertNotNull(result);
            assertTrue(result.data().isEmpty());
        }
    }

    @Test
    void shouldHandleEmptyParametersList() throws SQLException {
        // Arrange
        String sql = "SELECT * FROM test_table";
        int timeout = 30;

        try (MockedStatic<OracleJdbc> mockedStatic = mockStatic(OracleJdbc.class)) {
            mockedStatic.when(OracleJdbc::getOracleConnection).thenReturn(mockConnection);
            when(mockConnection.createStatement()).thenReturn(mockStatement);
            when(mockStatement.executeQuery(anyString())).thenReturn(mockResultSet);
            when(mockResultSet.getMetaData()).thenReturn(mockMetaData);
            when(mockMetaData.getColumnCount()).thenReturn(1);
            when(mockResultSet.next()).thenReturn(false);

            // Act
            QueryResult result = jdeQueryService.executeQuery(sql, timeout);

            // Assert
            assertNotNull(result);
            verify(mockStatement).setQueryTimeout(timeout);
        }
    }
}
