package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.common.enums.PaymentCateEnum;
import com.kerryprops.kip.bill.common.exceptions.CounterCashierBizException;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptPayConfigRepository;
import com.kerryprops.kip.bill.dao.AptPayRepository;
import com.kerryprops.kip.bill.dao.AptPaymentInfoRepository;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.dao.entity.HiveContextAware;
import com.kerryprops.kip.bill.dao.entity.QAptPay;
import com.kerryprops.kip.bill.dao.entity.QAptPaymentInfo;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.PaymentBillService;
import com.kerryprops.kip.bill.service.model.s.AptPayInvoiceBo;
import com.kerryprops.kip.bill.webservice.vo.req.CashierFeePaysRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierFeeQRCodePayRequest;
import com.kerryprops.kip.bill.webservice.vo.resp.AptPayDetailVo;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierFeePayReceptionExportVo;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierFeePayStaffExportVo;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierFeePaysResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierQRCodePaymentResource;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffAptBillRespVo;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import org.aspectj.lang.annotation.Before;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static com.kerryprops.kip.bill.common.enums.AptPayVerifyStatus.TO_BE_VERIFIED;
import static com.kerryprops.kip.bill.common.enums.PaymentCateEnum.A003;
import static com.kerryprops.kip.bill.common.enums.PaymentCateEnum.A004;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomLoginUser;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomObject;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomString;
import static com.kerryprops.kip.pmw.variables.PayOption.WECHATPAY;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * CounterCashierFeeServiceImplTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-04-28
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("收银台杂费付款-单元测试")
class CounterCashierFeeServiceImplTest {

    @Mock
    private AptPayRepository aptPayRepository;

    @Mock
    private AptBillRepository aptBillRepository;

    @Mock
    private AptPayConfigRepository aptPayConfigRepository;

    @Mock
    private AptPaymentInfoRepository aptPaymentInfoRepository;

    @Mock
    private PaymentBillService paymentBillService;

    @InjectMocks
    private CounterCashierFeeServiceImpl counterCashierFeeService;

    @Test
    @DisplayName("非财务人员场景：正常场景：返回分页数据")
    public void queryFeePays_shouldReturnPageWhenIsFinanceStaffNotOne() throws Exception {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);

        var aptPaymentInfo = new AptPaymentInfo();
        aptPaymentInfo.setId("1");
        aptPaymentInfo.setPayAct("PAY123");
        aptPaymentInfo.setProjectId("PROJECT1");
        aptPaymentInfo.setPaymentCate(A004);
        aptPaymentInfo.setAmt(1.0d);

        List<AptPaymentInfo> aptPaymentInfoList = List.of(aptPaymentInfo);
        Page<AptPaymentInfo> aptPaymentInfoListPage =
                new PageImpl<>(aptPaymentInfoList, pageable, aptPaymentInfoList.size());

        var resource = new CashierFeePaysResource();
        resource.setPayAct("PAY123");

        setUpRepository();
        when(aptPaymentInfoRepository.findAll(any(Predicate.class), any(Pageable.class))).thenReturn(
                aptPaymentInfoListPage);

        when(paymentBillService.calcBillInvoice(any())).thenReturn(new AptPayInvoiceBo());

        var request = randomObject(CashierFeePaysRequest.class);
        request.setIsFinanceStaff(0);
        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());

            // Act
            Page<CashierFeePaysResource> result = counterCashierFeeService.queryFeePays(request, pageable);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getTotalElements());
            assertEquals("PAY123", result.getContent()
                                         .get(0)
                                         .getPayAct());
        }
    }

    @Test
    @DisplayName("非财务人员场景：异常场景：返回空分页数据")
    public void queryFeePays_shouldReturnEmptyPageWhenNoDataFound() throws Exception {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        Page<AptPaymentInfo> emptyPage = new PageImpl<>(Collections.emptyList(), pageable, 0);

        setUpRepository();
        when(aptPaymentInfoRepository.findAll(any(Predicate.class), any(Pageable.class))).thenReturn(emptyPage);

        var request = randomObject(CashierFeePaysRequest.class);
        request.setIsFinanceStaff(0);
        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());

            // Act
            Page<CashierFeePaysResource> result = counterCashierFeeService.queryFeePays(request, pageable);

            // Assert
            assertNotNull(result);
            assertEquals(0, result.getTotalElements());
        }
    }

    @Test
    @DisplayName("财务人员场景：正常场景：导出成功")
    public void exportFeePays_shouldExportSuccessfullyForFinanceStaff() throws Exception {
        // Arrange
        CashierFeePaysRequest request = randomObject(CashierFeePaysRequest.class);
        request.setIsFinanceStaff(1); // 财务人员

        HttpServletResponse response = mock(HttpServletResponse.class);
        ServletOutputStream outputStream = mock(ServletOutputStream.class);
        when(response.getOutputStream()).thenReturn(outputStream);

        QAptPay t1 = QAptPay.aptPay;
        QAptPaymentInfo t2 = QAptPaymentInfo.aptPaymentInfo;


        // Mock JPAQueryFactory
        JPAQueryFactory mockJpaQueryFactory = mock(JPAQueryFactory.class);
        JPAQuery<AptPay> mockJPAQuery = mock(JPAQuery.class);

        // Mock repository
        when(aptPayRepository.getJpaQueryFactory()).thenReturn(mockJpaQueryFactory);

        // Mock chain calls
        when(mockJpaQueryFactory.selectFrom(t1)).thenReturn(mockJPAQuery);
        when(mockJPAQuery.leftJoin(t2)).thenReturn(mockJPAQuery);
        when(mockJPAQuery.on(any(Predicate.class))).thenReturn(mockJPAQuery);
        when(mockJPAQuery.where(any(Predicate.class))).thenReturn(mockJPAQuery);
        when(mockJPAQuery.orderBy(t1.createTime.desc(), t1.updateTime.desc())).thenReturn(mockJPAQuery);

        // Mock fetch result
        var aptPay = new AptPay();
        aptPay.setId(1L);
        aptPay.setPayAct("PAY123");
        aptPay.setProjectId("PROJECT1");
        aptPay.setVerifyStatus(TO_BE_VERIFIED);
        aptPay.setCreateTime(new Date());
        aptPay.setUpdateTime(new Date());

        List<AptPay> aptPayList = List.of(aptPay);
        when(mockJPAQuery.fetch()).thenReturn(aptPayList);


        var exportVo = new CashierFeePayStaffExportVo();
        exportVo.setPayAct("PAY123");

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), eq(CashierFeePayStaffExportVo.class)))
                                .thenReturn(exportVo);
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());

            // Act
            counterCashierFeeService.exportFeePays(request, response);

            // Assert
            verify(response, times(1)).getOutputStream();
        }
    }

    @Test
    @DisplayName("非财务人员场景：正常场景：导出成功")
    public void exportFeePays_shouldExportSuccessfullyForNonFinanceStaff() throws Exception {
        // Arrange
        var request = randomObject(CashierFeePaysRequest.class);
        request.setIsFinanceStaff(0); // 非财务人员

        HttpServletResponse response = mock(HttpServletResponse.class);
        ServletOutputStream outputStream = mock(ServletOutputStream.class);
        when(response.getOutputStream()).thenReturn(outputStream);

        AptPaymentInfo aptPaymentInfo = new AptPaymentInfo();
        aptPaymentInfo.setId("1");
        aptPaymentInfo.setPayAct("PAY123");
        aptPaymentInfo.setProjectId("PROJECT1");
        aptPaymentInfo.setPaymentCate(A003);
        aptPaymentInfo.setAmt(1.2d);
        aptPaymentInfo.setAdvanceAmount(BigDecimal.ONE);
        aptPaymentInfo.setPaymentCate(A003);
        List<AptPaymentInfo> aptPaymentInfoList = List.of(aptPaymentInfo);

        setUpRepository();
        when(aptPaymentInfoRepository.findAll(any(Predicate.class))).thenReturn(aptPaymentInfoList);

        var cashierFeePayReceptionExportVo = new CashierFeePayReceptionExportVo();
        cashierFeePayReceptionExportVo.setPayAct("PAY123");

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(cashierFeePayReceptionExportVo);
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());

            // Act
            counterCashierFeeService.exportFeePays(request, response);

            // Assert
            verify(response, times(1)).getOutputStream();
        }
    }

    @Test
    @DisplayName("非财务人员场景：异常场景：无数据时导出空文件")
    public void exportFeePays_shouldExportEmptyFileForNonFinanceStaffWhenNoData() throws Exception {
        // Arrange
        var request = randomObject(CashierFeePaysRequest.class);
        request.setIsFinanceStaff(0); // 非财务人员

        HttpServletResponse response = mock(HttpServletResponse.class);
        ServletOutputStream outputStream = mock(ServletOutputStream.class);
        when(response.getOutputStream()).thenReturn(outputStream);

        setUpRepository();
        when(aptPaymentInfoRepository.findAll(any(Predicate.class))).thenReturn(Collections.emptyList());

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new CashierFeePaysResource());
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());

            // Act
            counterCashierFeeService.exportFeePays(request, response);

            // Assert
            verify(response, times(1)).getOutputStream();
        }
    }


    @Test
    @DisplayName("正常场景：生成二维码支付资源成功")
    void counterCashierQRCodePayment_shouldGenerateQRCodePaymentResourceSuccessfully() {
        // Arrange
        var request = randomObject(CashierFeeQRCodePayRequest.class);
        request.setProjectId("P1");
        request.setBuildingId("B1");
        request.setRoomId("R1");
        request.setFeeId(100L);
        request.setPayOption(WECHATPAY.name());


        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {

            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new AptPayDetailVo(), new StaffAptBillRespVo(), new StaffAptBillRespVo());
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());


            // Act and Assert
            assertThrows(NullPointerException.class,
                         () -> counterCashierFeeService.counterCashierQRCodePayment(request));
        }
    }

    private void setUpRepository() throws Exception {
        // 手动注入父类的私有字
        Field field = AbstractCounterCashierService.class.getDeclaredField("aptPaymentInfoRepository");
        field.setAccessible(true); // 允许访问私有字段
        field.set(counterCashierFeeService, aptPaymentInfoRepository); // 设置Mock对象到父类字段
    }

}