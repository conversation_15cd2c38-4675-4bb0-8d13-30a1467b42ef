package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.dao.BillSendConfigRepository;
import com.kerryprops.kip.bill.dao.entity.BillSendConfig;
import com.kerryprops.kip.bill.feign.entity.TenantManagerItemResponse;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class API_syncEnterpriseAccounts_BillSendConfigServiceImplTest extends BaseIntegrationTest {

    @Autowired
    private BillSendConfigRepository configRepository;

    @Autowired
    private BillSendConfigServiceImpl impl;

    @Test
    void _03_syncEnterpriseAccounts_01_config_not_found() {
        boolean result = impl.syncEnterpriseAccounts();
        assertFalse(result);
    }

    @Test
    void _03_syncEnterpriseAccounts_02_sUserClient_queryLoginAccounts_return_null() {
        Mockito.doReturn(null).when(sUserClient).queryLoginAccounts(ArgumentMatchers.anyList());
        test_sUserClient_queryLoginAccounts_return_empty();
    }

    @Test
    void _03_syncEnterpriseAccounts_02_sUserClient_queryLoginAccounts_return_empty() {
        Mockito.doReturn(Collections.EMPTY_MAP).when(sUserClient).queryLoginAccounts(ArgumentMatchers.anyList());
        test_sUserClient_queryLoginAccounts_return_empty();
    }

    @Test
    void _03_syncEnterpriseAccounts_03_complex_data() {
        //prepare test data
        Map<String, TenantManagerItemResponse> mobileMap = new HashMap<>();
        mobileMap.put("M1", buildTenantManagerItemResponse("B1"));
        mobileMap.put("M2", buildTenantManagerItemResponse("B3"));
        Mockito.doReturn(mobileMap).when(sUserClient).queryLoginAccounts(ArgumentMatchers.anyList());

        List<BillSendConfig> oldBillSendConfigs = List.of(
                buildBillSendConfig(false, "***********", "********"), //已删除的账单发送配置
                buildBillSendConfig(true, StringUtils.EMPTY, StringUtils.EMPTY),            //手机号为空的账单发送配置
                buildBillSendConfig(true, "M1", "B1"),      //有手机号M1、B端账号未变(B1)
                buildBillSendConfig(true, "M2", "B2"),      //有手机号M2、B端账号已变化且不为空(B2 -> B3)
                buildBillSendConfig(true, "M2", StringUtils.EMPTY), //有手机号M2、B端账号为空字符(空字符串 -> B3)
                buildBillSendConfig(true, "M3", "B4"),      //有手机号M3、B端账号为B4(B4 -> B端返回null -> 空字符串)
                buildBillSendConfig(true, "M4", "B5"),      //有手机号M4、B端账号为B5(B5 -> B端返回空字符串 -> 空字符串)
                buildBillSendConfig(true, "M5", StringUtils.EMPTY), //有手机号M5、B端账号未变(空字符串 -> B端返回null -> 空字符串)
                buildBillSendConfig(true, "M6", StringUtils.EMPTY)  //有手机号M6、B端账号未变(空字符串 -> B端返回空字符串 -> 空字符串)
        );
        List<BillSendConfig> newBillSendConfigs = configRepository.saveAll(oldBillSendConfigs);

        //execute
        boolean result = impl.syncEnterpriseAccounts();

        //verify
        assertTrue(result);
        BillSendConfig billSendConfig2 = configRepository.findById(newBillSendConfigs.get(2).getId()).get();
        assertEquals(billSendConfig2.getLoginNo(), mobileMap.get(billSendConfig2.getPhoneNumber()).getLoginNo());//M1, B1
        BillSendConfig billSendConfig3 = configRepository.findById(newBillSendConfigs.get(3).getId()).get();
        assertEquals(billSendConfig3.getLoginNo(), mobileMap.get(billSendConfig3.getPhoneNumber()).getLoginNo());//M2, B3                              //M2, 空
        BillSendConfig billSendConfig4 = configRepository.findById(newBillSendConfigs.get(4).getId()).get();
        assertEquals(billSendConfig4.getLoginNo(), mobileMap.get(billSendConfig4.getPhoneNumber()).getLoginNo());//M2, B3                              //M2，空
        BillSendConfig billSendConfig5 = configRepository.findById(newBillSendConfigs.get(5).getId()).get();
        assertEquals(billSendConfig5.getLoginNo(), StringUtils.EMPTY);                              //M3，空
        BillSendConfig billSendConfig6 = configRepository.findById(newBillSendConfigs.get(6).getId()).get();
        assertEquals(billSendConfig6.getLoginNo(), StringUtils.EMPTY);                              //M4，空
        BillSendConfig billSendConfig7 = configRepository.findById(newBillSendConfigs.get(7).getId()).get();
        assertEquals(billSendConfig7.getLoginNo(), StringUtils.EMPTY);                              //M5，空
        BillSendConfig billSendConfig8 = configRepository.findById(newBillSendConfigs.get(8).getId()).get();
        assertEquals(billSendConfig8.getLoginNo(), StringUtils.EMPTY);                              //M6，空

        //clear test data
        configRepository.deleteAll(newBillSendConfigs);
    }

    private void test_sUserClient_queryLoginAccounts_return_empty() {
        List<BillSendConfig> oldBillSendConfigs = List.of(
                buildBillSendConfig(false, "***********", "********"),
                buildBillSendConfig(true, StringUtils.EMPTY, StringUtils.EMPTY)
        );
        List<BillSendConfig> newBillSendConfigs = configRepository.saveAll(oldBillSendConfigs);

        //execute
        boolean result = impl.syncEnterpriseAccounts();

        //verify
        assertFalse(result);

        //clear test data
        configRepository.deleteAll(newBillSendConfigs);
    }

    private BillSendConfig buildBillSendConfig(boolean active, String phoneNumber, String loginNo) {
        BillSendConfig billSendConfig = BillSendConfig.builder()
                .doco("112201").isDel(active ? 0 : 1).phoneNumber(phoneNumber).loginNo(loginNo)
                .email("<EMAIL>").mcu("mcu1").unit("unit1")
                .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                .build();
        return billSendConfig;
    }

    private TenantManagerItemResponse buildTenantManagerItemResponse(String accountId) {
        TenantManagerItemResponse response = new TenantManagerItemResponse();
        response.setLoginNo(accountId);
        response.setUserName("tester");
        response.setId(1101l);
        return response;
    }


}
