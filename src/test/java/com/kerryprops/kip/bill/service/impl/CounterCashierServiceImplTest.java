package com.kerryprops.kip.bill.service.impl;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.bill.common.enums.AptPayVerifyStatus;
import com.kerryprops.kip.bill.common.enums.PaymentCateEnum;
import com.kerryprops.kip.bill.common.exceptions.CounterCashierBizException;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.config.PaymentConfigProps;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptPayBillRepository;
import com.kerryprops.kip.bill.dao.AptPayRepository;
import com.kerryprops.kip.bill.dao.AptPaymentBillRepository;
import com.kerryprops.kip.bill.dao.AptPaymentInfoRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptJdeBill;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.dao.entity.HiveContextAware;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.entity.EmailSendCommand;
import com.kerryprops.kip.bill.service.PaymentBillService;
import com.kerryprops.kip.bill.service.model.s.AptPayInvoiceBo;
import com.kerryprops.kip.bill.service.model.s.BillInvoiceCalcDto;
import com.kerryprops.kip.bill.webservice.vo.req.AptBillPaymentRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierQRCodePaymentRequest;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierAptPaymentInfoResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierPaymentReceiptResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierQRCodePaymentResource;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import com.kerryprops.kip.hiveas.feign.dto.resp.RoomRespDto;
import com.kerryprops.kip.hiveas.webservice.resource.resp.BuildingSimple;
import com.kerryprops.kip.hiveas.webservice.resource.resp.FloorSimple;
import com.kerryprops.kip.hiveas.webservice.resource.resp.ProjectSimple;
import com.kerryprops.kip.hiveas.webservice.resource.resp.RoomResp;
import com.kerryprops.kip.pmw.client.resource.LoopQueryPspOutputResource;
import com.kerryprops.kip.pmw.client.service.PaymentClientService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static com.kerryprops.kip.bill.common.enums.BillPaymentStatus.PAID;
import static com.kerryprops.kip.bill.common.enums.BillPaymentStatus.PAYING;
import static com.kerryprops.kip.bill.common.enums.PaymentCateEnum.A003;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomObject;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomString;
import static com.kerryprops.kip.pmw.variables.PayOption.WECHATPAY;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@DisplayName("收银台杂费-单元测试")
class CounterCashierServiceImplTest {

    @Mock
    private Mapper mapperCache;

    @InjectMocks
    private BeanUtil beanUtil;

    @Mock
    private HiveAsClient hiveAsClient;

    @Mock
    private AptBillService aptBillService;

    @Mock
    private AptPayRepository aptPayRepository;

    @Mock
    private AptBillRepository aptBillRepository;

    @Mock
    private PaymentBillService paymentBillService;

    @Mock
    private AptPaymentBillRepository aptPaymentBillRepository;

    @Mock
    private AptPayBillRepository payBillRepository;

    @Mock
    private PaymentConfigProps paymentConfigProps;

    @Mock
    private AptPaymentInfoRepository aptPaymentInfoRepository;

    @Mock
    private PaymentClientService paymentClientService;

    @InjectMocks
    private CounterCashierServiceImpl counterCashierService;

    @Test
    @DisplayName("收银台杂费-查询收银台支付信息-异常场景：支付信息为空")
    void _01_queryCashierPaymentInfo_payment_info_empty_error() {
        String paymentInfoIdMock = randomString();
        assertThrows(CounterCashierBizException.class, () -> counterCashierService.queryCashierPaymentInfo(paymentInfoIdMock));
    }

    @Test
    @DisplayName("收银台杂费-查询收银台支付信息-正常场景：正在支付，被过滤场景")
    void _02_queryCashierPaymentInfo_status_paying_success() {
        // given
        AptPaymentInfo aptPaymentInfoMock = new AptPaymentInfo();
        String paymentInfoIdMock = randomString();
        doReturn(aptPaymentInfoMock).when(paymentBillService).getBillPaymentInfoById(paymentInfoIdMock);

        var resourceMock = new CashierAptPaymentInfoResource();
        resourceMock.setPaymentStatus(PAID);
        doReturn(resourceMock).when(mapperCache).map(any(AptPaymentInfo.class), any());

        // assert
        assertDoesNotThrow(() -> counterCashierService.queryCashierPaymentInfo(paymentInfoIdMock));
    }

    @Test
    @DisplayName("收银台杂费-查询收银台支付信息-正常场景：超时取消支付")
    void _03_queryCashierPaymentInfo_expired_scene_success() {
        // given
        var aptPaymentInfoMock = new AptPaymentInfo();

        String paymentInfoIdMock = randomString();
        doReturn(aptPaymentInfoMock).when(paymentBillService).getBillPaymentInfoById(paymentInfoIdMock);

        var resourceMock = new CashierAptPaymentInfoResource();
        resourceMock.setPaymentStatus(PAYING);
        resourceMock.setCreateTime(ZonedDateTime.now().minusSeconds(61L));
        doReturn(resourceMock).when(mapperCache).map(any(AptPaymentInfo.class), any());

        doReturn(60).when(paymentConfigProps).getUserQRCodePaymentTtlSeconds();
        ReflectionTestUtils.setField(counterCashierService, "paymentConfigProps", paymentConfigProps);

        // assert
        assertDoesNotThrow(() -> counterCashierService.queryCashierPaymentInfo(paymentInfoIdMock));
    }

    @Test
    @DisplayName("收银台杂费-查询收银台支付信息-正常场景")
    void _04_queryCashierPaymentInfo_success() {
        // given
        var aptPaymentInfoMock = new AptPaymentInfo();

        String paymentInfoIdMock = randomString();
        doReturn(aptPaymentInfoMock).when(paymentBillService).getBillPaymentInfoById(paymentInfoIdMock);

        var resourceMock = new CashierAptPaymentInfoResource();
        resourceMock.setPaymentStatus(PAYING);
        resourceMock.setCreateTime(ZonedDateTime.now());
        doReturn(resourceMock).when(mapperCache).map(any(AptPaymentInfo.class), any());

        doReturn(60).when(paymentConfigProps).getUserQRCodePaymentTtlSeconds();
        ReflectionTestUtils.setField(counterCashierService, "paymentConfigProps", paymentConfigProps);

        var loopQueryPspOutputBodyResource = new LoopQueryPspOutputResource.LoopQueryPspOutputBodyResource();
        loopQueryPspOutputBodyResource.setState(randomString());
        loopQueryPspOutputBodyResource.setPspName(randomString());
        var outputResource = new LoopQueryPspOutputResource(null, loopQueryPspOutputBodyResource, null);
        doReturn(outputResource).when(paymentClientService).loopQueryPspOrder(any());
        ReflectionTestUtils.setField(counterCashierService, "paymentClientService", paymentClientService);

        // assert
        assertDoesNotThrow(() -> counterCashierService.queryCashierPaymentInfo(paymentInfoIdMock));
    }

    @Test
    @DisplayName("收银台杂费-查询收银台支付信息-正常场景")
    void _05_queryCashierPaymentInfo_success() {
        // given
        var aptPaymentInfoMock = new AptPaymentInfo();

        String paymentInfoIdMock = randomString();
        doReturn(aptPaymentInfoMock).when(paymentBillService).getBillPaymentInfoById(paymentInfoIdMock);

        var resourceMock = new CashierAptPaymentInfoResource();
        resourceMock.setPaymentStatus(PAYING);
        resourceMock.setCreateTime(ZonedDateTime.now());
        doReturn(resourceMock).when(mapperCache).map(any(AptPaymentInfo.class), any());

        doReturn(60).when(paymentConfigProps).getUserQRCodePaymentTtlSeconds();
        ReflectionTestUtils.setField(counterCashierService, "paymentConfigProps", paymentConfigProps);

        var loopQueryPspOutputBodyResource = new LoopQueryPspOutputResource.LoopQueryPspOutputBodyResource();
        loopQueryPspOutputBodyResource.setState(randomString());
        loopQueryPspOutputBodyResource.setPspName(randomString());
        var outputResource = new LoopQueryPspOutputResource(null, loopQueryPspOutputBodyResource, null);
        doReturn(outputResource).when(paymentClientService).loopQueryPspOrder(any());
        ReflectionTestUtils.setField(counterCashierService, "paymentClientService", paymentClientService);

        // assert
        assertDoesNotThrow(() -> counterCashierService.queryCashierPaymentInfo(paymentInfoIdMock));
    }

    @Test
    @DisplayName("收银台杂费-查询缴费记录详情||收据-正常场景")
    void _06_acquirePaymentReceipt_success() {
        // given
        var aptPaymentInfoMock = new AptPaymentInfo();
        String paymentInfoIdMock = randomString();
        aptPaymentInfoMock.setId(paymentInfoIdMock);
        aptPaymentInfoMock.setAlph(randomString());
        aptPaymentInfoMock.setPaymentCate(PaymentCateEnum.A004);
        aptPaymentInfoMock.setAmt(randomObject(Double.class));
        doReturn(aptPaymentInfoMock).when(aptPaymentInfoRepository).findTopById(paymentInfoIdMock);
        ReflectionTestUtils.setField(counterCashierService, "aptPaymentInfoRepository", aptPaymentInfoRepository);

        var aptPayMock = new AptPay();
        aptPayMock.setVerifyStatus(AptPayVerifyStatus.VERIFIED);
        aptPayMock.setStatus(1);
        aptPayMock.setDeletedAt(0);
        doReturn(aptPayMock).when(aptPayRepository).findTopByPaymentInfoId(paymentInfoIdMock);

        var aptBillMock1 = new AptBill();
        aptBillMock1.setId(randomObject(Long.class));
        aptBillMock1.setBeginDate(randomObject(Date.class));
        aptBillMock1.setEndDate(randomObject(Date.class));
        aptBillMock1.setAmt(randomObject(Double.class));
        var aptBillMock2 = new AptBill();
        aptBillMock2.setId(randomObject(Long.class));
        aptBillMock2.setBeginDate(randomObject(Date.class));
        aptBillMock2.setEndDate(randomObject(Date.class));
        aptBillMock2.setAmt(randomObject(Double.class));
        List<AptBill> aptBillsMock = List.of(aptBillMock1, aptBillMock2);

        final String BILL_CAN_INVOICE = "1";
        Map<Long, String> canBillInvoiceMock = Map.of(aptBillMock1.getId(), BILL_CAN_INVOICE, aptBillMock2.getId(), BILL_CAN_INVOICE);

        var aptPayInvoiceBoMock = new AptPayInvoiceBo();
        aptPayInvoiceBoMock.setCanBillInvoice(canBillInvoiceMock);
        aptPayInvoiceBoMock.setCanInvoiceAmount(randomObject(BigDecimal.class));
        aptPayInvoiceBoMock.setBillAmount(randomObject(BigDecimal.class));
        aptPayInvoiceBoMock.setBills(aptBillsMock);
        doReturn(aptPayInvoiceBoMock).when(paymentBillService).calcBillInvoice(any(BillInvoiceCalcDto.class));

        // then
        CashierPaymentReceiptResource resourceActual = counterCashierService.acquirePaymentReceipt(paymentInfoIdMock);

        // assert
        assertEquals(resourceActual.getPayer(), aptPaymentInfoMock.getAlph());
        assertEquals(resourceActual.getCanInvoiceBillAmount(), aptPayInvoiceBoMock.getCanInvoiceAmount());
        assertEquals(resourceActual.getBillAmount(), aptPayInvoiceBoMock.getBillAmount());

        resourceActual.getBills().forEach(bill -> {
            assertEquals(BILL_CAN_INVOICE, bill.getIsBilling());
        });
    }

    @DisplayName("异常场景：预收金额信息有误")
    @Test
    public void testCounterCashierQRCodePayment_withInvalidAdvanceAmount() {
        // Arrange
        var request = new CashierQRCodePaymentRequest();
        request.setAdvanceAmount(BigDecimal.valueOf(100.00));
        request.setActualAmount(BigDecimal.valueOf(50.00));
        request.setPaymentCate(A003);
        request.setBills(List.of(new AptBillPaymentRequest()));
        request.setRoomId(null);

        // Act & Assert
        assertThrows(CounterCashierBizException.class,
                     () -> counterCashierService.counterCashierQRCodePayment(request));
    }

    @DisplayName("异常场景：账单为空")
    @Test
    public void testCounterCashierQRCodePayment_withEmptyBills() {
        // Arrange
        CashierQRCodePaymentRequest request = new CashierQRCodePaymentRequest();
        request.setAdvanceAmount(BigDecimal.valueOf(100.00));
        request.setActualAmount(request.getAdvanceAmount());
        request.setPaymentCate(A003);
        request.setRoomId("123456");
        request.setBills(Collections.emptyList());

        // Act & Assert
        assertThrows(NullPointerException.class,
                     () -> counterCashierService.counterCashierQRCodePayment(request));
    }

    @DisplayName("正常场景：根据房间ID获取位置信息")
    @Test
    public void testGetPositionByRoomId_success() throws Exception {
        // Arrange
        Field hiveAsClientField = AbstractCounterCashierService.class.getDeclaredField("hiveAsClient");
        hiveAsClientField.setAccessible(true);
        hiveAsClientField.set(counterCashierService, hiveAsClient);

        String roomId = "ROOM123";
        RoomResp roomResp = new RoomResp();
        ProjectSimple projectSimple = new ProjectSimple("projectId", "projectName");
        BuildingSimple buildingSimple = new BuildingSimple("buildId", "buildName", projectSimple.getId());
        FloorSimple floorSimple = new FloorSimple("floorId", "floorIdName", buildingSimple.getId());
        RoomRespDto roomRespDto = new RoomRespDto();
        roomRespDto.setRoomNo(randomString());

        roomResp.setProject(projectSimple);
        roomResp.setBuilding(buildingSimple);
        roomResp.setFloor(floorSimple);
        roomResp.setRoom(roomRespDto);

        RespWrapVo<RoomResp> respWrapVo = new RespWrapVo<>();
        respWrapVo.setData(roomResp);
        when(hiveAsClient.getRoomById(eq(roomId))).thenReturn(respWrapVo);

        Method method = CounterCashierServiceImpl.class.getDeclaredMethod("getPositionByRoomId", String.class);
        method.setAccessible(true);

        // Act
        PositionItemResponse result = (PositionItemResponse) method.invoke(counterCashierService, roomId);

        // Assert
        assertNotNull(result);
        assertTrue(StringUtils.isNotEmpty(result.getProjectName()));
        assertTrue(StringUtils.isNotEmpty(result.getBuildingName()));
        assertTrue(StringUtils.isNotEmpty(result.getFloorName()));
        assertTrue(StringUtils.isNotEmpty(result.getRoomName()));
        verify(hiveAsClient, times(1)).getRoomById(eq(roomId));
    }

    @Test
    @DisplayName("正常场景：AptBillWithPreCollectionQRCodePaymentHandler内部类初始化正常")
    public void testAptBillWithPreCollectionQRCodePaymentHandler_init_success() throws Exception {
        // Arrange
        CashierQRCodePaymentRequest request = new CashierQRCodePaymentRequest();
        request.setPayOption(WECHATPAY.name());
        request.setAdvanceAmount(BigDecimal.valueOf(100.00));
        request.setActualAmount(BigDecimal.valueOf(300.00));
        request.setBills(List.of(new AptBillPaymentRequest()));
        request.setPaymentCate(PaymentCateEnum.A003);

        // Access the private inner class
        Class<?> handlerClass = Class.forName("com.kerryprops.kip.bill.service.impl.CounterCashierServiceImpl$AptBillWithPreCollectionQRCodePaymentHandler");
        Constructor<?> constructor = handlerClass.getDeclaredConstructor(CounterCashierServiceImpl.class, CashierQRCodePaymentRequest.class);
        constructor.setAccessible(true);

        // Pass the enclosing class instance (counterCashierService) as the first argument
        Object handler = constructor.newInstance(counterCashierService, request);

        Method validateAdvanceAmountMethod = handlerClass.getDeclaredMethod("validateAdvanceAmount");
        validateAdvanceAmountMethod.setAccessible(true);

        // Act and Assert
        assertThrows(InvocationTargetException.class, () -> validateAdvanceAmountMethod.invoke(handler));
    }

    @Test
    @DisplayName("正常场景：PurePreCollectionQRCodePaymentHandler内部类初始化正常")
    public void testPurePreCollectionQRCodePaymentHandler_init_success() throws Exception {
        // Arrange
        Field hiveAsClientField = AbstractCounterCashierService.class.getDeclaredField("hiveAsClient");
        hiveAsClientField.setAccessible(true);
        hiveAsClientField.set(counterCashierService, hiveAsClient);

        HiveContextAware hiveContextAware = mock(HiveContextAware.class);
        when(hiveAsClient.getRoomHiveContext(any())).thenReturn(hiveContextAware);

        CashierQRCodePaymentRequest request = new CashierQRCodePaymentRequest();
        request.setPayOption(WECHATPAY.name());
        request.setAdvanceAmount(BigDecimal.valueOf(100.00));
        request.setActualAmount(BigDecimal.valueOf(300.00));
        request.setBills(List.of(new AptBillPaymentRequest()));
        request.setPaymentCate(PaymentCateEnum.A003);

        // Access the private inner class
        Class<?> handlerClass = Class.forName("com.kerryprops.kip.bill.service.impl.CounterCashierServiceImpl$PurePreCollectionQRCodePaymentHandler");
        Constructor<?> constructor = handlerClass.getDeclaredConstructor(CounterCashierServiceImpl.class, CashierQRCodePaymentRequest.class);
        constructor.setAccessible(true);

        // Pass the enclosing class instance (counterCashierService) as the first argument
        Object handler = constructor.newInstance(counterCashierService, request);

        Method beforePaymentInfoSaved = handlerClass.getDeclaredMethod("beforePaymentInfoSaved");
        beforePaymentInfoSaved.setAccessible(true);

        // Act and Assert
        assertDoesNotThrow(() -> beforePaymentInfoSaved.invoke(handler));
    }


}
