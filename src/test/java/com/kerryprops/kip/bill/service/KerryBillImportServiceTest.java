package com.kerryprops.kip.bill.service;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.enums.JDEDocType;
import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.exceptions.ImportBillException;
import com.kerryprops.kip.bill.dao.BillRepository;
import com.kerryprops.kip.bill.dao.entity.QBillEntity;
import com.kerryprops.kip.bill.feign.entity.UploadPublicFileResponse;
import com.kerryprops.kip.bill.service.model.leg.Bill;
import com.kerryprops.kip.bill.webservice.vo.req.ImportKerryBillRequest;
import com.kerryprops.kip.bill.webservice.vo.req.ImportKerryBillRequest.ImportKerryBill;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;

@Slf4j
class KerryBillImportServiceTest extends BaseIntegrationTest {

    @Autowired
    private KerryBillImportService kerryBillImportService;

    @Autowired
    private IBillService billService;

    @Autowired
    private BillRepository billRepository;

    @Test
    void _01_importBill_doctype_not_VC() {
        ImportKerryBillRequest req = constructImportReqSample();
        req.getBills().get(0).setDocumentType("00");
        ImportBillException exception = assertThrows(ImportBillException.class
                , () -> kerryBillImportService.importBill(req));
        assertEquals(exception.getCode(), RespCodeEnum.IMPORT_DOC_TYPE_ERROR.getCode());
        assertEquals(exception.getMessage(), RespCodeEnum.IMPORT_DOC_TYPE_ERROR.getMessage());
    }

    @Test
    void _02_importBill_file_not_pdf() {
        ImportKerryBillRequest req = constructImportReqSample();
        req.getBills().get(0).setBillFilename(random.nextLong() + ".png");
        ImportBillException exception = assertThrows(ImportBillException.class
                , () -> kerryBillImportService.importBill(req));
        assertEquals(exception.getCode(), RespCodeEnum.IMPORT_FILE_FORMAT_ERROR.getCode());
        assertEquals(exception.getMessage(), RespCodeEnum.IMPORT_FILE_FORMAT_ERROR.getMessage());
    }

    @Test
    void _03_importBill_upload_file_error() {
        doThrow(new RuntimeException("not valid file")).when(fileClient).uploadPrivateFile(ArgumentMatchers.any());
        ImportKerryBillRequest req = constructImportReqSample();
        req.getBills().get(0).setBillFileUrl("http://localhost:8080/xxx.pdf");
        ImportBillException exception = assertThrows(ImportBillException.class
                , () -> kerryBillImportService.importBill(req));
        assertEquals(exception.getCode(), RespCodeEnum.IMPORT_BILL_PDF_FAILED.getCode());
        assertEquals(exception.getMessage(), RespCodeEnum.IMPORT_BILL_PDF_FAILED.getMessage());
    }

    @Test
    void _04_importBill_duplicate() {
        UploadPublicFileResponse uploadPublicFileResponse = new UploadPublicFileResponse();
        uploadPublicFileResponse.setUrl("http://localhost:8080/" + random.nextLong() + ".pdf");
        doReturn(uploadPublicFileResponse).when(fileClient).uploadPrivateFile(ArgumentMatchers.any());
        ImportKerryBillRequest req = constructImportReqSample();
        int count = kerryBillImportService.importBill(req);
        assertEquals(1, count);
        req.getBills().get(0).getGenerateTime().setTime(req.getBills().get(0).getGenerateTime().getTime() - 1000L);
        ImportBillException exception = assertThrows(ImportBillException.class
                , () -> kerryBillImportService.importBill(req));
        assertEquals(exception.getCode(), RespCodeEnum.IMPORT_BILL_DUPLICATED.getCode());
        assertEquals(exception.getMessage(), RespCodeEnum.IMPORT_BILL_DUPLICATED.getMessage());
        billRepository.deleteAll();
    }

    @Test
    void _05_importBill_import_update() {
        UploadPublicFileResponse uploadPublicFileResponse = new UploadPublicFileResponse();
        uploadPublicFileResponse.setUrl("https://kip.oss-cn-shanghai.aliyuncs.com/" + random.nextLong() + ".pdf");
        doReturn(uploadPublicFileResponse).when(fileClient).uploadPrivateFile(ArgumentMatchers.any());
        ImportKerryBillRequest req = constructImportReqSample();
        int count = kerryBillImportService.importBill(req);
        assertEquals(1, count);
        req.getBills().get(0).setAlph(String.valueOf(random.nextLong()));
        req.getBills().get(0).setGenerateTime(Date.from(Instant.now().plusSeconds(10L)));
        count = kerryBillImportService.importBill(req);
        assertEquals(1, count);
        List<Long> ids = billRepository.getJpaQueryFactory().select(QBillEntity.billEntity.id.max())
                .from(QBillEntity.billEntity).fetch();
        Bill billEntity = billService.selectBillById(ids.get(0));
        assertEquals(billEntity.getTpAlph(), req.getBills().get(0).getAlph());
        billRepository.deleteAll();
    }

    @Test
    void _06_importBill_success() {
        UploadPublicFileResponse uploadPublicFileResponse = new UploadPublicFileResponse();
        uploadPublicFileResponse.setUrl("http://localhost:8080/" + random.nextLong() + ".pdf");
        doReturn(uploadPublicFileResponse).when(fileClient).uploadPrivateFile(ArgumentMatchers.any());
        ImportKerryBillRequest req = constructImportReqSample();
        ImportKerryBill importBill = req.getBills().get(0);
        int count = kerryBillImportService.importBill(req);
        assertEquals(1, count);
        List<Long> ids = billRepository.getJpaQueryFactory().select(QBillEntity.billEntity.id.max())
                .from(QBillEntity.billEntity).fetch();
        Bill billEntity = billService.selectBillById(ids.get(0));
        assertEquals(billEntity.getTpAn8(), importBill.getAn8());
        assertEquals(billEntity.getTpDct(), importBill.getDocumentType());
        assertEquals(billEntity.getTpDl01(), importBill.getDocumentTextDesc());
        assertEquals(billEntity.getTpGtfilenm(), importBill.getBillFilename());
        assertEquals(billEntity.getTpCo(), importBill.getCompanyCode());
        assertEquals(billEntity.getTpDl03(), importBill.getCompanyName());
        assertEquals(billEntity.getTpAlph(), importBill.getAlph());
        assertEquals(billEntity.getTpDoco(), importBill.getDoco());
        assertEquals(billEntity.getTpMcu(), importBill.getMcu());
        assertEquals(billEntity.getTpDc(), importBill.getMcuDesc());
        assertEquals(billEntity.getTpCrtutime(), importBill.getGenerateTime());
        assertEquals(billEntity.getTpFyr(), importBill.getBillYear());
        assertEquals(billEntity.getTpPn(), importBill.getBillMonth());
        assertEquals(billEntity.getTpEv01(), importBill.getDisplayWebsite());
        assertEquals(billEntity.getTpEv02(), importBill.getBillSource());
        assertEquals(billEntity.getFormatDate(), importBill.getPrintTime());
        assertEquals(billEntity.getTpGtitnm(), importBill.getDocumentEngDesc());
        assertEquals(billEntity.getTpUnit(), importBill.getUnit());
        billRepository.deleteAll();
    }

    @Test
    void _07_importBill_duplicate_bill_success() {
        UploadPublicFileResponse uploadPublicFileResponse = new UploadPublicFileResponse();
        uploadPublicFileResponse.setUrl("http://localhost:8080/" + random.nextLong() + ".pdf");
        doReturn(uploadPublicFileResponse).when(fileClient).uploadPrivateFile(ArgumentMatchers.any());
        ImportKerryBillRequest req = constructImportReqSample();
        ImportKerryBill importBill = req.getBills().get(0);

        req.setBills(List.of(importBill, importBill));

        assertThrows(ImportBillException.class, () -> kerryBillImportService.importBill(req));
        billRepository.deleteAll();
    }

    private ImportKerryBillRequest constructImportReqSample() {
        ImportKerryBillRequest request = new ImportKerryBillRequest();
        ImportKerryBill bill = new ImportKerryBill();
        bill.setDocumentType(JDEDocType.VC.name());
        bill.setDocumentTextDesc(JDEDocType.VC.getDesc());
        bill.setDocumentEngDesc(JDEDocType.VC.getEngDesc());
        bill.setBillFilename(random.nextLong() + ".pdf");
        bill.setCompanyCode("31019");
        bill.setCompanyName("嘉里(沈阳)房地产开发有限公司");
        bill.setBillFileUrl("https://kip.oss-cn-shanghai.aliyuncs.com/" + random.nextInt() + ".pdf");
        bill.setAn8(String.valueOf((int) (Math.random() * 9 + 1) * 100000 * 100));
        bill.setMcu(String.valueOf((int) (Math.random() * 9 + 1) * 100000 * 100));
        bill.setAlph(String.valueOf(random.nextLong()));
        bill.setDoco("279684");
        bill.setMcuDesc("嘉里沈阳，租赁，商场");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        bill.setGenerateTime((new Date()));
        bill.setBillYear(23);
        bill.setBillMonth(5);
        bill.setDisplayWebsite("Y");
        bill.setBillSource("C");
        bill.setPrintTime(sdf.format(new Date()));
        bill.setUnit("2703");
        request.setBills(List.of(bill));
        return request;
    }


}