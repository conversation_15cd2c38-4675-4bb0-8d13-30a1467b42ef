package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.common.enums.BillSelectMode;
import com.kerryprops.kip.bill.dao.BillSelectConfigRepository;
import com.kerryprops.kip.bill.dao.entity.BillSelectConfig;
import com.kerryprops.kip.bill.webservice.vo.req.BillSelectConfigAddDto;
import com.kerryprops.kip.bill.webservice.vo.req.BillSelectConfigExistDto;
import com.kerryprops.kip.bill.webservice.vo.req.BillSelectConfigListDto;
import com.kerryprops.kip.bill.webservice.vo.req.BillSelectConfigPutDto;
import com.querydsl.core.BooleanBuilder;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.NoSuchElementException;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * BillSelectConfigServiceTest.
 *
 * <AUTHOR> Yu
 */
@SuppressWarnings("unchecked")
@ExtendWith(MockitoExtension.class)
class BillSelectConfigServiceTest {

    @Mock
    private BillSelectConfigRepository billSelectConfigRepository;

    @InjectMocks
    private BillSelectConfigService billSelectConfigService;

    @Test
    @DisplayName("判断配置存在时返回 true")
    void shouldReturnTrueWhenConfigExists() {
        // Arrange
        BillSelectConfigExistDto dto = new BillSelectConfigExistDto();
        dto.setProjectId("P001");
        dto.setBuildingId("B002");
        dto.setRoomId("R003");

        when(billSelectConfigRepository.existsConfig("P001", "B002", "R003")).thenReturn(true);

        // Act
        boolean exists = billSelectConfigService.existConfig(dto);

        // Assert
        assertThat(exists).isTrue();
        verify(billSelectConfigRepository, times(1)).existsConfig("P001", "B002", "R003");
    }

    @Test
    @DisplayName("正常查询账单选择模式")
    void shouldReturnBillSelectModeWhenDataExists() {
        // Arrange
        String projectId = "P001";
        String buildingId = "B001";
        String roomId = "R001";
        BillSelectMode expectedMode = BillSelectMode.ANY_BILLS;

        when(billSelectConfigRepository.findBillSelectMode(projectId, buildingId, roomId)).thenReturn(expectedMode);

        // Act
        BillSelectMode actualMode = billSelectConfigService.getBillSelectMode(projectId, buildingId, roomId);

        // Assert
        assertThat(actualMode).isEqualTo(expectedMode);
        verify(billSelectConfigRepository, times(1)).findBillSelectMode(projectId, buildingId, roomId);
    }


    @Test
    @DisplayName("成功新增账单选择配置")
    void shouldAddConfigSuccessfully() {
        // Arrange
        BillSelectConfigAddDto dto = new BillSelectConfigAddDto();
        dto.setProjectId("P001");
        dto.setBuildingId("B002");
        dto.setRoomId("R003");
        dto.setBillSelectMode(BillSelectMode.ANY_BILLS);

        // Act
        billSelectConfigService.addConfig(dto);

        // Assert
        ArgumentCaptor<BillSelectConfig> captor = ArgumentCaptor.forClass(BillSelectConfig.class);
        verify(billSelectConfigRepository, times(1)).save(captor.capture());
        BillSelectConfig savedConfig = captor.getValue();

        assertThat(savedConfig).isNotNull();
        assertThat(savedConfig.getProjectId()).isEqualTo("P001");
        assertThat(savedConfig.getBuildingId()).isEqualTo("B002");
        assertThat(savedConfig.getRoomId()).isEqualTo("R003");
        assertThat(savedConfig.getBillSelectMode()).isEqualTo(BillSelectMode.ANY_BILLS);
    }

    @Test
    @DisplayName("成功更新账单选择配置")
    void shouldUpdateConfigSuccessfully() {
        // Arrange
        Long id = 1L;
        BillSelectConfigPutDto putDto = new BillSelectConfigPutDto();
        putDto.setBuildingId("B002");
        putDto.setRoomId("R003");
        putDto.setBillSelectMode(BillSelectMode.ANY_BILLS);

        BillSelectConfig updatedConfig = new BillSelectConfig();
        updatedConfig.setId(id);
        updatedConfig.setBuildingId("B002");
        updatedConfig.setRoomId("R003");
        updatedConfig.setBillSelectMode(BillSelectMode.ANY_BILLS);

        when(billSelectConfigRepository.saveDiff(id, putDto)).thenReturn(updatedConfig);

        // Act
        BillSelectConfig result = billSelectConfigService.updateConfig(id, putDto);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(id);
        assertThat(result.getBuildingId()).isEqualTo("B002");
        assertThat(result.getRoomId()).isEqualTo("R003");
        assertThat(result.getBillSelectMode()).isEqualTo(BillSelectMode.ANY_BILLS);

        ArgumentCaptor<Long> idCaptor = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<BillSelectConfigPutDto> dtoCaptor = ArgumentCaptor.forClass(BillSelectConfigPutDto.class);

        verify(billSelectConfigRepository, times(1)).saveDiff(idCaptor.capture(), dtoCaptor.capture());

        assertThat(idCaptor.getValue()).isEqualTo(id);
        assertThat(dtoCaptor.getValue()).isEqualTo(putDto);
    }

    @Test
    @DisplayName("成功删除账单选择配置")
    void shouldDeleteConfigSuccessfully() {
        // Arrange
        Long id = 1L;
        BillSelectConfig existingConfig = new BillSelectConfig();
        existingConfig.setId(id);
        existingConfig.setProjectId("P001");
        existingConfig.setBuildingId("B001");
        existingConfig.setRoomId("R001");
        existingConfig.setBillSelectMode(BillSelectMode.ANY_BILLS);

        when(billSelectConfigRepository.findById(id)).thenReturn(java.util.Optional.of(existingConfig));

        // Act
        BillSelectConfig deletedConfig = billSelectConfigService.deleteConfig(id);

        // Assert
        assertThat(deletedConfig).isNotNull();
        assertThat(deletedConfig.getId()).isEqualTo(id);
        verify(billSelectConfigRepository, times(1)).findById(id);
        verify(billSelectConfigRepository, times(1)).deleteById(id);
    }

    @Test
    @DisplayName("删除不存在的账单选择配置时抛出异常")
    void shouldThrowExceptionWhenDeletingNonExistentConfig() {
        // Arrange
        Long id = 1L;

        when(billSelectConfigRepository.findById(id)).thenReturn(java.util.Optional.empty());

        // Act & Assert
        assertThatThrownBy(() -> billSelectConfigService.deleteConfig(id))
                .isInstanceOf(NoSuchElementException.class);
        verify(billSelectConfigRepository, times(1)).findById(id);
        verify(billSelectConfigRepository, times(0)).deleteById(id);
    }

    @Test
    @DisplayName("成功返回分页配置列表")
    void shouldReturnPagedConfigList() {
        // Arrange
        BillSelectConfigListDto dto = new BillSelectConfigListDto();
        dto.setProjectId("P001");
        dto.setBuildingId("B001");
        dto.setRoomId("R001");
        dto.setBillSelectMode(BillSelectMode.ANY_BILLS);

        Pageable pageable = Pageable.ofSize(10);
        Page<BillSelectConfig> mockPage = mock(Page.class);

        when(billSelectConfigRepository.findAll(any(BooleanBuilder.class), eq(pageable)))
                .thenReturn(mockPage);

        // Act
        Page<BillSelectConfig> result = billSelectConfigService.listConfig(dto, pageable);

        // Assert
        assertThat(result).isNotNull();
        verify(billSelectConfigRepository, times(1))
                .findAll(any(BooleanBuilder.class), eq(pageable));
    }

    @Test
    @DisplayName("当没有匹配的配置时返回空分页")
    void shouldReturnEmptyPageWhenNoConfigMatches() {
        // Arrange
        BillSelectConfigListDto dto = new BillSelectConfigListDto();
        dto.setProjectId("P001");
        dto.setBuildingId("B002");

        Pageable pageable = Pageable.ofSize(5);
        Page<BillSelectConfig> emptyPage = Page.empty();

        when(billSelectConfigRepository.findAll(any(BooleanBuilder.class), eq(pageable)))
                .thenReturn(emptyPage);

        // Act
        Page<BillSelectConfig> result = billSelectConfigService.listConfig(dto, pageable);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isEmpty()).isTrue();
        verify(billSelectConfigRepository, times(1))
                .findAll(any(BooleanBuilder.class), eq(pageable));
    }

}