package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.enums.BillPushStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.utils.jde.OracleJdbc;
import com.kerryprops.kip.bill.config.DataMigrationConfig;
import com.kerryprops.kip.bill.config.SyncJdeConfig;
import com.kerryprops.kip.bill.dao.AptBillLinkRepository;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptJdeBillRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptJdeBill;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.clients.MessageClient;
import com.kerryprops.kip.bill.feign.entity.EmailSendCommand;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.webservice.vo.resp.AptPayDetailVo;
import com.kerryprops.kip.hiveas.feign.dto.BuildingDTO;
import com.kerryprops.kip.hiveas.feign.dto.resp.RoomRespDto;
import com.kerryprops.kip.hiveas.webservice.resource.resp.BuildingSimple;
import com.kerryprops.kip.hiveas.webservice.resource.resp.FloorSimple;
import com.kerryprops.kip.hiveas.webservice.resource.resp.ProjectSimple;
import com.kerryprops.kip.hiveas.webservice.resource.resp.RoomResp;
import com.kerryprops.kip.hiveas.webservice.vo.resp.ProjectBuildingVO;
import com.querydsl.core.types.Predicate;
import jakarta.validation.constraints.AssertTrue;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;

import java.lang.reflect.Method;
import java.sql.BatchUpdateException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.random.RandomGenerator;

import static com.kerryprops.kip.bill.utils.RandomUtil.randomLoginUser;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomObject;
import static java.sql.Statement.SUCCESS_NO_INFO;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@DisplayName("C端账单Service-单元测试")
class AptBillServiceTest {

    @Mock
    private AptBillRepository billRepository;

    @Mock
    private AptBillLinkRepository billLinkRepository;

    @Mock
    private HiveAsClient hiveAsClient;

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    private MessageClient messageClient;

    @Mock
    private AptJdeBillRepository jdeBillRepository;

    @Mock
    private DataMigrationConfig dataMigrationConfig;

    @InjectMocks
    private AptBillService aptBillService;


    @Test
    @DisplayName("同步hive数据，正常场景，测试空数据")
    void syncHive_shouldDoNothingWhenNoBills() {
        // Arrange
        when(billRepository.findAll(any(Predicate.class))).thenReturn(Collections.emptyList());

        // Act
        aptBillService.syncHive();

        // Assert
        verify(billRepository).findAll(any(Predicate.class));
        verifyNoInteractions(hiveAsClient);
    }

    @Test
    @DisplayName("同步hive数据，正常场景，AptBills的bu为空")
    void syncHive_aptBillsBuEmpty_success() {
        // Arrange
        AptBill aptBill1 = AptBill.builder()
                                  .bu(StringUtils.EMPTY)
                                  .unit("unit-1")
                                  .build();

        AptBill aptBill2 = AptBill.builder()
                                  .bu(StringUtils.EMPTY)
                                  .unit("unit-2")
                                  .build();

        List<AptBill> mockBills = List.of(aptBill1, aptBill2);
        when(billRepository.findAll(any(Predicate.class))).thenReturn(mockBills);

        // Act
        aptBillService.syncHive();

        // Assert
        verify(billRepository).findAll(any(Predicate.class));
        verify(hiveAsClient, never()).getBatchRoom(anyString(), anyList());
    }

    @Test
    @DisplayName("同步hive数据，正常场景，AptBills为空")
    void syncHive_aptBillsEmpty_success() {
        // Arrange
        AptBill aptBill1 = AptBill.builder()
                                  .bu("bu-1")
                                  .unit("unit-1")
                                  .build();

        AptBill aptBill2 = AptBill.builder()
                                  .bu("bu-2")
                                  .unit("unit-2")
                                  .build();

        List<AptBill> mockBills = List.of(aptBill1, aptBill2);
        when(billRepository.findAll(any(Predicate.class))).thenReturn(mockBills);

        // Act
        aptBillService.syncHive();

        // Assert
        verify(billRepository).findAll(any(Predicate.class));
        verify(hiveAsClient, times(2)).getBatchRoom(anyString(), anyList());
    }

    @Test
    @DisplayName("同步hive数据，正常场景，测试完整数据流")
    void syncHive_shouldProcessValidBills() {
        try (MockedStatic<BeanUtil> beanUtilMock = mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> userInfoMock = mockStatic(UserInfoUtils.class)) {
            // Arrange
            var aptBill1 = new AptBill();
            aptBill1.setId(1L);
            aptBill1.setBu("bu1");
            aptBill1.setUnit("unit");

            var aptBill2 = new AptBill();
            aptBill2.setId(2L);
            aptBill2.setBu("bu2");
            aptBill2.setUnit(aptBill1.getUnit());
            when(billRepository.findAll(any(Predicate.class))).thenReturn(List.of(aptBill1, aptBill2));

            // 2. 模拟静态方法
            beanUtilMock.when(() -> BeanUtil.copy(any(), any()))
                        .thenReturn(new AptPayDetailVo()); // 按需返回模拟对象
            userInfoMock.when(UserInfoUtils::getUser)
                        .thenReturn(generateLoginUser());

            // 3. 模拟 Hive 服务响应
            var roomRespDto = new RoomRespDto();
            roomRespDto.setJdeRoomNo(aptBill1.getUnit());
            RoomResp mockRoomResp = RoomResp.builder()
                                            .project(new ProjectSimple("projId", "projName"))
                                            .building(new BuildingSimple("buildingId", "buildingName", "projId"))
                                            .floor(new FloorSimple("floorId", "floorName", "buildingId"))
                                            .room(roomRespDto)
                                            .build();
            when(hiveAsClient.getBatchRoom(anyString(), anyList())).thenReturn(List.of(mockRoomResp));

            when(jdbcTemplate.batchUpdate(anyString(), any(BatchPreparedStatementSetter.class))).thenReturn(
                    new int[]{SUCCESS_NO_INFO});

            // Act
            aptBillService.syncHive();

            // Assert
            verify(jdbcTemplate).batchUpdate(anyString(), any(BatchPreparedStatementSetter.class));
        }
    }

    @Test
    @DisplayName("从JDE同步C端账单到Kerry+，异常场景，hive中bu为空")
    void syncAptBill_shouldSkipWhenHiveBuListEmpty() {
        // Arrange
        when(dataMigrationConfig.getCBillProjects()).thenReturn("project1,project2");

        // Act
        aptBillService.syncAptBill(null);

        // Assert
        verifyNoInteractions(jdbcTemplate);
    }

    @Test
    @DisplayName("从JDE同步C端账单到Kerry+，正常场景")
    void syncAptBill_shouldWriteBackSyncFlag() throws SQLException {
        // Arrange
        try (MockedStatic<SyncJdeConfig> mockedConfig = mockStatic(SyncJdeConfig.class);
             MockedStatic<OracleJdbc> mockedJdbc = mockStatic(OracleJdbc.class)) {
            when(dataMigrationConfig.getCBillProjects()).thenReturn("project1,project2");

            var buildingDTO = new BuildingDTO();
            buildingDTO.setPropertyDeveloperBU("BU1");
            buildingDTO.setPropertyManagementBU("BU2");

            var projectBuildingVO = new ProjectBuildingVO();
            projectBuildingVO.setBuildings(List.of(buildingDTO));
            when(hiveAsClient.getCenterByIds(any(String[].class))).thenReturn(List.of(projectBuildingVO));

            mockedConfig.when(SyncJdeConfig::getJdeDbPrefix).thenReturn("TEST_DB");

            // 模拟数据库
            Connection mockConn = mock(Connection.class);
            PreparedStatement mockPstmt = mock(PreparedStatement.class);
            when(mockConn.prepareStatement(anyString())).thenReturn(mockPstmt);
            when(mockPstmt.executeBatch()).thenReturn(new int[]{1});
            mockedJdbc.when(OracleJdbc::getOracleConnection).thenReturn(mockConn);

            Statement mockStmt = mock(Statement.class);
            ResultSet resultSet = mock(ResultSet.class);
            when(resultSet.next()).thenReturn(true, false);
            when(resultSet.getString(any(Integer.class))).thenReturn(RandomGenerator.getDefault().nextInt() +
                                                                             StringUtils.EMPTY);
            when(resultSet.getLong(any(Integer.class))).thenReturn(1L);
            when(resultSet.getDouble(any(Integer.class))).thenReturn(1.0d);

            mockedJdbc.when(OracleJdbc::getOracleConnection).thenReturn(mockConn);
            when(mockConn.createStatement()).thenReturn(mockStmt);
            when(mockStmt.executeQuery(anyString())).thenReturn(resultSet);

            // 准备待回写数据
            AptJdeBill syncedBill = new AptJdeBill();
            syncedBill.setId(1L);
            syncedBill.setRdDoc(1001L);
            syncedBill.setBillNumber("BILL001");
            when(jdeBillRepository.findAll(any(Predicate.class))).thenReturn(List.of(syncedBill));

            when(jdeBillRepository.saveAll(anyList())).thenAnswer(invocation -> invocation.getArgument(0));
            when(billRepository.save(any(AptBill.class))).thenAnswer(invocation -> invocation.getArgument(0));

            // 模拟Hive服务
            var roomRespDto = new RoomRespDto();
            RoomResp mockRoomResp = RoomResp.builder()
                                            .project(new ProjectSimple("projId", "projName"))
                                            .building(new BuildingSimple("buildingId", "buildingName", "projId"))
                                            .floor(new FloorSimple("floorId", "floorName", "buildingId"))
                                            .room(roomRespDto)
                                            .build();
            when(hiveAsClient.getBatchRoom(anyString(), anyList())).thenReturn(List.of(mockRoomResp));

            when(jdbcTemplate.batchUpdate(anyString(), any(BatchPreparedStatementSetter.class))).thenReturn(
                    new int[]{SUCCESS_NO_INFO});

            // Act
            aptBillService.syncAptBill(null);

            // Assert
            verify(mockPstmt).addBatch();
            verify(jdbcTemplate).batchUpdate(anyString(), any(BatchPreparedStatementSetter.class));
        }
    }

    @Test
    @DisplayName("buildHiveBuString方法，异常场景：")
    void buildHiveBuString_HiveBuEmptySuccess() {
        // Arrange
        when(hiveAsClient.getCenterByIds(any(String[].class))).thenReturn(Collections.emptyList());

        // Act
        String bus = aptBillService.buildHiveBuString(List.of("192","187"));

        // Assert
        assertNull(bus);
    }

    @Test
    @DisplayName("buildHiveBuString方法，正常场景")
    void buildHiveBuString_success() {
        // Arrange
        var buildingDTO = new BuildingDTO();
        buildingDTO.setPropertyDeveloperBU("BU1");
        buildingDTO.setPropertyManagementBU("BU2");

        var projectBuildingVO = new ProjectBuildingVO();
        projectBuildingVO.setBuildings(List.of(buildingDTO));
        when(hiveAsClient.getCenterByIds(any(String[].class))).thenReturn(List.of(projectBuildingVO));

        // Act
        String bus = aptBillService.buildHiveBuString(List.of("192","187"));

        // Assert
        assertTrue(StringUtils.isNotEmpty(bus));
    }

    @Test
    @DisplayName("queryAptBillsByBillNos方法，正常场景")
    void queryAptBillsByBillNos_success() {
        // Arrange
        when(billRepository.findAll(any(Predicate.class))).thenReturn(List.of(new AptBill()));

        // Act
        List<AptBill> result = aptBillService.queryAptBillsByBillNos(List.of("192","187"));

        // Assert
        assertTrue(CollectionUtils.isNotEmpty(result));
    }

    @DisplayName("异常场景：保存JDE账单时抛出异常")
    @Test
    public void testHandleNewJdeBills_saveJdeBillsException() throws Exception {
        // Arrange
        List<AptJdeBill> jdeBills = new LinkedList<>();
        AptJdeBill jdeBill = new AptJdeBill();
        jdeBill.setRdDoc(21000024L);
        jdeBill.setRdDct("RD");
        jdeBill.setRdKco("41022");
        jdeBill.setRdSfx("003");
        jdeBill.setRdAg(100.0);
        jdeBills.add(jdeBill);

        when(jdeBillRepository.saveAll(anyList())).thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        Method method = AptBillService.class.getDeclaredMethod("handleNewJdeBills", List.class);
        method.setAccessible(true);
        assertDoesNotThrow(() -> method.invoke(aptBillService, jdeBills));

        verify(jdeBillRepository, times(1)).saveAll(anyList());
        verify(billRepository, times(0)).save(any(AptBill.class));
    }

    @DisplayName("异常场景：保存C端账单时抛出异常")
    @Test
    public void testHandleNewJdeBills_saveAptBillsException() throws Exception {
        // Arrange
        List<AptJdeBill> jdeBills = new LinkedList<>();
        AptJdeBill jdeBill = new AptJdeBill();
        jdeBill.setRdDoc(21000024L);
        jdeBill.setRdDct("RD");
        jdeBill.setRdKco("41022");
        jdeBill.setRdSfx("003");
        jdeBill.setRdAg(100.0);
        jdeBills.add(jdeBill);

        // Act & Assert
        Method method = AptBillService.class.getDeclaredMethod("handleNewJdeBills", List.class);
        method.setAccessible(true);
        assertDoesNotThrow(() -> method.invoke(aptBillService, jdeBills));

        verify(messageClient, times(1)).sendWithReplyAlicloud(any(EmailSendCommand.class));
        verify(jdeBillRepository, times(1)).saveAll(anyList());
        verify(billRepository, times(0)).save(any(AptBill.class));
    }

    @Test
    @DisplayName("正常场景：更新账单状态成功")
    void updateBillStatus4Push_success() {
        // Arrange
        when(billRepository.updateBillStatus4Push(any(), any(), any())).thenReturn(false);

        // Act
        aptBillService.updateBillStatus4Push(randomObject(Long.class), randomObject(BillPushStatus.class), randomObject(BillStatus.class));

        // Assert
        verify(billRepository, times(1)).updateBillStatus4Push(any(), any(), any());
    }

    @Test
    @DisplayName("正常场景：根据ID获取账单成功")
    void getBillById_success() {
        // Arrange
        Optional<AptBill> optionalBill = mock(Optional.class);
        when(optionalBill.orElse(any())).thenReturn(new AptBill());
        when(billRepository.findById(any())).thenReturn(optionalBill);

        // Act
        AptBill result = aptBillService.getBillById(1L);

        // Assert
        assertNotNull(result);
        verify(billRepository, times(1)).findById(anyLong());
    }


    @Test
    @DisplayName("异常场景：账单ID为null")
    void getBillById_billIdNull() {
        // Act
        AptBill result = aptBillService.getBillById(null);

        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("异常场景：BatchUpdateException异常")
    void writeBackJDESyncFlag_BatchUpdateException() throws Exception {
        // Arrange
        List<AptJdeBill> aptJdeBills = List.of(createAptJdeBill(1L), createAptJdeBill(2L));
        Connection mockConnection = mock(Connection.class);
        PreparedStatement mockPreparedStatement = mock(PreparedStatement.class);

        when(mockConnection.prepareStatement(anyString())).thenReturn(mockPreparedStatement);
        doThrow(new BatchUpdateException("Batch update failed", new int[0])).when(mockPreparedStatement).executeBatch();

        try (MockedStatic<OracleJdbc> mockedStatic = mockStatic(OracleJdbc.class)) {
            mockedStatic.when(OracleJdbc::getOracleConnection).thenReturn(mockConnection);

            Method method = AptBillService.class.getDeclaredMethod("writeBackJDESyncFlag", List.class);
            method.setAccessible(true);

            // Act & Assert
            assertDoesNotThrow(() -> method.invoke(aptBillService, aptJdeBills));
            verify(mockConnection, times(1)).rollback();
        }
    }

    @Test
    @DisplayName("异常场景：SQLException异常")
    void writeBackJDESyncFlag_SQLException() throws Exception {
        // Arrange
        List<AptJdeBill> aptJdeBills = List.of(createAptJdeBill(1L), createAptJdeBill(2L));
        Connection mockConnection = mock(Connection.class);
        PreparedStatement mockPreparedStatement = mock(PreparedStatement.class);

        when(mockConnection.prepareStatement(anyString())).thenReturn(mockPreparedStatement);
        doThrow(new SQLException("SQL execution failed")).when(mockPreparedStatement).executeBatch();

        try (MockedStatic<OracleJdbc> mockedStatic = mockStatic(OracleJdbc.class)) {
            mockedStatic.when(OracleJdbc::getOracleConnection).thenReturn(mockConnection);

            AptBillService aptBillService = new AptBillService();
            Method method = AptBillService.class.getDeclaredMethod("writeBackJDESyncFlag", List.class);
            method.setAccessible(true);

            // Act & Assert
            assertDoesNotThrow(() -> method.invoke(aptBillService, aptJdeBills));
            verify(mockConnection, never()).rollback(); // SQLException should not trigger rollback
        }
    }

    @Test
    @DisplayName("异常场景：空的HiveRoomRequest列表")
    void getRooms_emptyRequestList() throws Exception {
        // Arrange
         Method method = AptBillService.class.getDeclaredMethod("getRooms", List.class);
         method.setAccessible(true);

         // Act
         @SuppressWarnings("unchecked") Map<String, List<RoomResp>>
                 result = (Map<String, List<RoomResp>>) method.invoke(aptBillService, Collections.emptyList());

         // Assert
         assertNull(result);
    }

    @Test
    @DisplayName("convertDate，异常场景：空的入参")
    void convertDate_emptyDate() throws Exception {
        // Arrange
        Method method = AptBillService.class.getDeclaredMethod("convertDate", String.class);
        method.setAccessible(true);

        // Act
        Date result = (Date) method.invoke(aptBillService, StringUtils.EMPTY);

        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("convertYear，异常场景：空的入参")
    void convertYear_emptyDate() throws Exception {
        // Arrange
        Method method = AptBillService.class.getDeclaredMethod("convertYear", String.class);
        method.setAccessible(true);

        // Act
        Date result = (Date) method.invoke(aptBillService, StringUtils.EMPTY);

        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("convertMonth，异常场景：空的入参")
    void convertMonth_emptyDate() throws Exception {
        // Arrange
        Method method = AptBillService.class.getDeclaredMethod("convertMonth", String.class);
        method.setAccessible(true);

        // Act
        Date result = (Date) method.invoke(aptBillService, StringUtils.EMPTY);

        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("getNextBillNo，正常场景")
    void getNextBillNo_success() throws Exception {
        // Arrange
        Method method = AptBillService.class.getDeclaredMethod("getNextBillNo");
        method.setAccessible(true);

        // Act
        String result = (String) method.invoke(aptBillService);

        // Assert
        assertNotNull(result);
    }

    @Test
    @DisplayName("正常场景：保存账单成功")
    void saveBills_success() throws Exception {
        // Arrange
        List<AptJdeBill> billsTemp = List.of(createAptJdeBill(1L), createAptJdeBill(2L));
        List<AptJdeBill> bills = new LinkedList<>(billsTemp);
        Method method = AptBillService.class.getDeclaredMethod("saveBills", List.class);
        method.setAccessible(true);

        // Act
        method.invoke(aptBillService, bills);

        // Assert
        verify(jdeBillRepository, times(1)).saveAll(any());
    }

    @Test
    @DisplayName("异常场景：空账单列表")
    void saveBills_emptyList() throws Exception {
        // Arrange
        List<AptJdeBill> bills = Collections.emptyList();
        Method method = AptBillService.class.getDeclaredMethod("saveBills", List.class);
        method.setAccessible(true);

        // Act
        method.invoke(aptBillService, bills);

        // Assert
        verify(jdeBillRepository, never()).saveAll(anyList());
    }

    @Test
    @DisplayName("回写JDe标记位，异常场景：空账单列表")
    void batchWriteBackSyncFlag_emptyList() throws Exception {
        // Arrange
        List<AptJdeBill> bills = Collections.emptyList();
        Method method = AptBillService.class.getDeclaredMethod("batchWriteBackSyncFlag", List.class);
        method.setAccessible(true);

        // Act
        method.invoke(aptBillService, bills);

        // Assert
        verify(jdbcTemplate, never()).batchUpdate(anyString(), any(BatchPreparedStatementSetter.class));
    }

    @Test
    @DisplayName("批量更新Hive信息，异常场景：空账单列表")
    void batchUpdateHiveInfo_emptyList() throws Exception {
        // Arrange
        List<AptBill> aptBills = Collections.emptyList();
        Method method = AptBillService.class.getDeclaredMethod("batchUpdateHiveInfo", List.class);
        method.setAccessible(true);

        // Act
        method.invoke(aptBillService, aptBills);

        // Assert
        verify(jdbcTemplate, never()).batchUpdate(anyString(), any(BatchPreparedStatementSetter.class));
    }

    private AptJdeBill createAptJdeBill(Long id) {
        AptJdeBill aptJdeBill = new AptJdeBill();
        aptJdeBill.setId(id);
        aptJdeBill.setRdDoc(id);
        aptJdeBill.setRdDct("DCT");
        aptJdeBill.setRdKco("KCO");
        aptJdeBill.setRdSfx("SFX");
        return aptJdeBill;
    }

    private LoginUser generateLoginUser() {
        return LoginUser.builder()
                        .nickName("testUser")
                        .fromType("C")
                        .cid("testCid")
                        .build();
    }

}