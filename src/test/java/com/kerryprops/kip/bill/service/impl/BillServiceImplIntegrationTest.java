package com.kerryprops.kip.bill.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.constants.BillConstants;
import com.kerryprops.kip.bill.common.enums.ContentTypeEnum;
import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.BillRepository;
import com.kerryprops.kip.bill.dao.BillSendConfigRepository;
import com.kerryprops.kip.bill.dao.BizContentReadRepository;
import com.kerryprops.kip.bill.dao.entity.BillEntity;
import com.kerryprops.kip.bill.dao.entity.BizContentReadEntity;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.clients.MessageCenterClient;
import com.kerryprops.kip.bill.feign.clients.SUserClient;
import com.kerryprops.kip.bill.feign.entity.TenantBillConfigResponse;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.model.b.BizBillSearchBo;
import com.kerryprops.kip.bill.service.model.leg.Bill;
import com.kerryprops.kip.bill.webservice.vo.req.DocoAn8;
import com.kerryprops.kip.bill.webservice.vo.resp.BillPayer;
import com.kerryprops.kip.bill.webservice.vo.resp.ContentUnreadInfoVo;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffBillRespVo;
import com.kerryprops.kip.hiveas.feign.dto.resp.TenantRespDto;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.mockito.ArgumentMatchers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.ActiveProfiles;

import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.common.constants.AppConstants.STAFF_BILL_SEND_BILL_LIST_REDIS_KEY;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.doReturn;

@ActiveProfiles("test")
class BillServiceImplIntegrationTest extends BaseIntegrationTest {

    private static final String DOCO = "274786";

    private static final String AN8 = "20000686";

    private static final String HIVE_VALID_RESPONSE_DEFAULT_PATH = "HiveAsClient_getTenantIdByDoCo_5_ValidResp.json";

    private static final String USER_VALID_RESPONSE_DEFAULT_PATH = "sUserClient_queryJdeNotifyPersons_5_ValidResp.json";

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private SUserClient sUserClient;

    @Autowired
    private HiveAsClient hiveAsService;

    @Autowired
    private MessageCenterClient messageCenterClient;

    @Autowired
    private BillServiceImpl impl;

    @Autowired
    private BillRepository billRepository;

    @Autowired
    private BillSendConfigRepository sendConfigRepository;

    @Autowired
    private BizContentReadRepository bizContentReadRepository;

    /*@ParameterizedTest
    @NullAndEmptySource
    void _01_selectBillReceiverByDoco_1InvalidParam_jdeDoco(String jdeDoco) {
        Set<StaffBillReceiverRespVo> receiverRespVoSet = impl.selectBillReceiverByDoco(jdeDoco, AN8);
        Assert.isNull(receiverRespVoSet);
    }

    @ParameterizedTest
    @NullAndEmptySource
    void _02_selectBillReceiverByDoco_1InvalidParam_an8(String an8) {
        Set<StaffBillReceiverRespVo> receiverRespVoSet = impl.selectBillReceiverByDoco(DOCO, an8);
        Assert.isNull(receiverRespVoSet);
    }

    @ParameterizedTest
    @NullAndEmptySource
    void _03_selectBillReceiverByDoco_2InvalidParams_same(String param) {
        Set<StaffBillReceiverRespVo> receiverRespVoSet = impl.selectBillReceiverByDoco(param, param);
        Assert.isNull(receiverRespVoSet);
    }

    @ParameterizedTest
    @NullAndEmptySource
    void _04_selectBillReceiverByDoco_2InvalidParams_diff(String jdeDoco) {
        String an8 = Objects.isNull(jdeDoco) ? StringUtils.EMPTY : null;

        Set<StaffBillReceiverRespVo> receiverRespVoSet = impl.selectBillReceiverByDoco(jdeDoco, an8);
        Assert.isNull(receiverRespVoSet);
    }

    @ParameterizedTest
    @NullSource
    @CsvSource({
            "HiveAsClient_getTenantIdByDoCo_2_CodeFailed.json",
            "HiveAsClient_getTenantIdByDoCo_3_NoData.json",
            "HiveAsClient_getTenantIdByDoCo_4_ListIsEmpty.json",
    })
    void _05_selectBillReceiverByDoco_HiveResponse_invalid(String hiveResponsePath)
            throws URISyntaxException, IOException {
        RespWrapVo<Set<TenantRespDto>> respWrapVo = buildInvalidHiveResponse(hiveResponsePath);
        doReturn(buildInvalidHiveResponse(hiveResponsePath)).when(hiveAsService).getTenantIdByDoCo(ArgumentMatchers.any());

        Set<StaffBillReceiverRespVo> receiverRespVoSet = impl.selectBillReceiverByDoco(DOCO, AN8);
        Assert.isNull(receiverRespVoSet);
    }

    @Test
    void _06_selectBillReceiverByDoco_sUserClient_invalid() throws URISyntaxException, IOException {
        doReturn(buildHiveResponse(HIVE_VALID_RESPONSE_DEFAULT_PATH)).when(hiveAsService).getTenantIdByDoCo(ArgumentMatchers.any());
        doReturn(null).when(sUserClient).queryJdeNotifyPersons(ArgumentMatchers.any());
        assertThrows(RuntimeException.class, () -> impl.selectBillReceiverByDoco(DOCO, AN8));
    }

    @Test
    void _07_selectBillReceiverByDoco_sUserClient_CodeFailed() throws URISyntaxException, IOException {
        doReturn(buildHiveResponse(HIVE_VALID_RESPONSE_DEFAULT_PATH))
                .when(hiveAsService).getTenantIdByDoCo(ArgumentMatchers.any());
        doReturn(new RespWrapVo<>(RespCodeEnum.NOT_FOUND)).when(sUserClient).queryJdeNotifyPersons(ArgumentMatchers.any());
        assertThrows(RuntimeException.class, () -> impl.selectBillReceiverByDoco(DOCO, AN8));
    }

    @Test
    void _08_selectBillReceiverByDoco_sUserClient_nullData() throws URISyntaxException, IOException {
        doReturn(buildHiveResponse(HIVE_VALID_RESPONSE_DEFAULT_PATH))
                .when(hiveAsService).getTenantIdByDoCo(ArgumentMatchers.any());
        doReturn(buildSUserClientResponse(USER_VALID_RESPONSE_DEFAULT_PATH))
                .when(sUserClient).queryJdeNotifyPersons(ArgumentMatchers.any());

        Set<StaffBillReceiverRespVo> set = impl.selectBillReceiverByDoco(DOCO, AN8);
        Assert.notNull(set);
        assertEquals(1, set.size());
    }

    @ParameterizedTest
    @NullAndEmptySource
    void _20_selectBillReceiverByDoco2_param(Map<DocoAn8, List<StaffBillRespVo>> an8Map) {
        Map<DocoAn8, Set<StaffBillReceiverRespVo>> resp = impl.selectBillReceiverByDoco2(an8Map, null);
        Assert.isNull(resp);
    }

    @ParameterizedTest
    @NullSource
    @CsvSource({
            "HiveAsClient_getTenantIdByDoCo_2_CodeFailed.json",
            "HiveAsClient_getTenantIdByDoCo_3_NoData.json",
            "HiveAsClient_getTenantIdByDoCo_4_ListIsEmpty.json"
    })
    void _21_selectBillReceiverByDoco2_HiveResponse_invalid(String hiveResponsePath)
            throws URISyntaxException, IOException {
        RespWrapVo<Set<TenantRespDto>> respWrapVo = buildInvalidHiveResponse(hiveResponsePath);
        doReturn(buildInvalidHiveResponse(hiveResponsePath)).when(hiveAsService).getTenantIdByDoCo(ArgumentMatchers.any());

        Map<DocoAn8, Set<StaffBillReceiverRespVo>> map = impl.selectBillReceiverByDoco2(buildAn8Map(), null);
        Assert.isNull(map);
    }

    void _22_selectBillReceiverByDoco2_HiveResponse_noContracts()
            throws URISyntaxException, IOException {
        doReturn(buildHiveResponse("HiveAsClient_getTenantIdByDoCo_6_NoContracts.json"))
                .when(hiveAsService).getTenantIdByDoCo(ArgumentMatchers.any());

        Map<DocoAn8, Set<StaffBillReceiverRespVo>> map = impl.selectBillReceiverByDoco2(buildAn8Map(), null);
        Assert.isNull(map);
    }

    @ParameterizedTest
    @NullSource
    @CsvSource({
            "sUserClient_queryJdeNotifyPersons_2_CodeFailed.json",
            "sUserClient_queryJdeNotifyPersons_3_NoData.json",
            "sUserClient_queryJdeNotifyPersons_4_ListIsEmpty.json"
    })
    void _23_selectBillReceiverByDoco2_sUserClient_invalid(String sUserClientPath)
            throws URISyntaxException, IOException {
        doReturn(buildHiveResponse(HIVE_VALID_RESPONSE_DEFAULT_PATH)).when(hiveAsService).getTenantIdByDoCo(ArgumentMatchers.any());
        doReturn(buildInvalidSUserClientResponse(sUserClientPath)).when(sUserClient).queryJdeNotifyPersons(ArgumentMatchers.any());

        Map<DocoAn8, Set<StaffBillReceiverRespVo>> map = impl.selectBillReceiverByDoco2(buildAn8Map(), null);
        Assert.isNull(map);
    }

    void _24_selectBillReceiverByDoco2_success() throws URISyntaxException, IOException {
        doReturn(buildHiveResponse(HIVE_VALID_RESPONSE_DEFAULT_PATH))
                .when(hiveAsService).getTenantIdByDoCo(ArgumentMatchers.any());
        doReturn(buildSUserClientResponse(USER_VALID_RESPONSE_DEFAULT_PATH))
                .when(sUserClient).queryJdeNotifyPersons(ArgumentMatchers.any());

        Map<DocoAn8, TenantRespDto> tenantCache = new HashMap<>();
        Map<DocoAn8, Set<StaffBillReceiverRespVo>> map = impl.selectBillReceiverByDoco2(buildAn8Map(), tenantCache);
        Assert.notNull(map);
        assertEquals(map.size(), 1);
        assertEquals(1, tenantCache.size());
    } */

    @ParameterizedTest
    @NullAndEmptySource
    void _30_sendBillList_1InvalidParam_billList(List<Bill> billList) {
        redisTemplate.delete(STAFF_BILL_SEND_BILL_LIST_REDIS_KEY);
        assertDoesNotThrow(() -> impl.sendBillList(billList, 1, StringUtils.EMPTY, StringUtils.EMPTY));
    }

    @Test
    void _31_sendBillList_1InvalidParam_sendType() {
        redisTemplate.delete(STAFF_BILL_SEND_BILL_LIST_REDIS_KEY);

        List<Bill> billList = Lists.newArrayList(buildBill(DOCO, AN8));
        assertDoesNotThrow(() -> impl.sendBillList(billList, 3, StringUtils.EMPTY, StringUtils.EMPTY));
    }

    @Test
    void _35_sendBillList_sendMail_emptyUserInfoList() {
        redisTemplate.delete(STAFF_BILL_SEND_BILL_LIST_REDIS_KEY);
        doReturn(Collections.EMPTY_LIST)
                .when(sendConfigRepository).queryActiveConfigsByDoco(ArgumentMatchers.any());

        List<Bill> billList = Lists.newArrayList(buildBill(DOCO, AN8));
        assertDoesNotThrow(() -> impl.sendBillList(billList, 1, StringUtils.EMPTY, StringUtils.EMPTY));
    }

    /**
     * 根据楼盘id模糊查询付款人：正常场景，且入参中bu（经营单元）列表为空
     */
    @Test
    void _36_fuzzySelectPayers_bus_empty_success() {
        List<BillPayer> result = impl.fuzzySelectPayers(Collections.emptyList(), "0", null);
        assertEquals(0, result.size());
    }

    /**
     * 根据楼盘id模糊查询付款人：正常场景
     */
    @ParameterizedTest
    @CsvSource({
            "31194100, 0, DEV测试, DEV测试",
            "31194100;31194101, 0, 测试2, DEV测试2",
            "31194100;31194101, 0, 测试, DEV测试;DEV测试2;DEV测试3",
            "31194100;31194101, 0, 测试-an8222, DEV测试3",
            "31194100;31194101, 1, 测试, DEV测试3"
    })
    void _37_fuzzySelectPayers_success(String busString, String delFlag, String payer, String resultCheck) {
        List<String> bus = List.of(busString.split(";"));

        // 执行待测部分
        List<BillPayer> result = impl.fuzzySelectPayers(bus, delFlag, payer);

        // 执行结果校验
        String[] resultCheckArr = resultCheck.split(";");
        for (int i = 0; i < resultCheckArr.length; i++) {
            assertEquals(resultCheckArr[i], result.get(i).getTpAlph());
        }
    }

    /**
     * B端App中，当前用户，对数据范围内的账单，是否全部已读：正常场景
     */
    @ParameterizedTest
    @CsvSource({
            ", true, 5",
            "0;1, true, 3",
            "0;1;2, true, 2",
            "0;1;2;3, true, 1",
            "0;1;2;3;4, false, 0"
    })
    void _36_userBillReadStatus_success(String contentOrderStr, boolean hasUnreadContent, long unreadContentCount) {
        long randomUserId = random.nextLong();
        setUserInfo(randomUserId);

        // 更改账单状态为“已发送”，匹配用例场景
        List<BillEntity> billEntities = billRepository.findAll();
        billEntities = billEntities.stream()
                .peek(billEntity -> billEntity.setTpStatus(BillConstants.MSG_SENTED)).collect(Collectors.toList());
        billRepository.saveAll(billEntities);

        // 设置过滤条件
        Set<String> docos = billEntities.stream().map(BillEntity::getTpDoco).collect(Collectors.toSet());
        Set<String> an8s = billEntities.stream().map(BillEntity::getTpAn8).collect(Collectors.toSet());
        BizBillSearchBo searchBo = new BizBillSearchBo();
        searchBo.setDocos(docos);
        searchBo.setAn8s(an8s);

        List<Long> billIds = billEntities.stream().map(BaseEntity::getId).collect(Collectors.toList());

        List<Long> contentOrders = StringUtils.isEmpty(contentOrderStr) ? Collections.emptyList() :
                Arrays.stream(contentOrderStr.split(";")).map(Long::parseLong).collect(Collectors.toList());
        for (int i = 0; i < contentOrders.size(); i++) {
            BizContentReadEntity bizContentReadEntity = new BizContentReadEntity();
            bizContentReadEntity.setType(ContentTypeEnum.B_BILL);
            bizContentReadEntity.setContentId(billIds.get(i));
            bizContentReadEntity.setUserId(randomUserId);
            bizContentReadRepository.save(bizContentReadEntity);
        }

        // 执行待验证部分
        ContentUnreadInfoVo result = impl.userBillReadStatus(searchBo.toPredicates());

        // 功能验证
        assertEquals(hasUnreadContent, result.isHasUnreadContent());
        assertEquals(unreadContentCount, result.getUnreadContentCount());
    }

    /**
     * B端App中，当前用户，新增已读账单记录：正常场景
     */
    @Test
    void _37_userBillReadStatus_success() {
        Long randomUserId = random.nextLong();
        setUserInfo(randomUserId);

        // 执行待验证部分
        Boolean result = impl.setUserBillRead(100L);

        // 功能验证
        BizContentReadEntity bizContentReadEntity = bizContentReadRepository.findAll().stream()
                .max(Comparator.comparing(BizContentReadEntity::getCreatedTime)).get();
        assertEquals(Boolean.TRUE, result);
        assertEquals(100L, bizContentReadEntity.getContentId());
        assertEquals(randomUserId, bizContentReadEntity.getUserId());
    }

    /**
     * B端App中，标记账单信息当前用户全部已读：正常场景
     */
    @Test
    void _38_setUserBillAllReaded_success() {
        long randomUserId = random.nextLong();
        setUserInfo(randomUserId);

        // 更改账单状态为“已发送”，匹配用例场景
        List<BillEntity> billEntities = billRepository.findAll();
        billEntities = billEntities.stream()
                .peek(billEntity -> billEntity.setTpStatus(BillConstants.MSG_SENTED)).collect(Collectors.toList());
        billRepository.saveAll(billEntities);

        // 设置过滤条件
        Set<String> docos = billEntities.stream().map(BillEntity::getTpDoco).collect(Collectors.toSet());
        Set<String> an8s = billEntities.stream().map(BillEntity::getTpAn8).collect(Collectors.toSet());
        BizBillSearchBo searchBo = new BizBillSearchBo();
        searchBo.setDocos(docos);
        searchBo.setAn8s(an8s);

        // 执行待验证部分
        Boolean result = impl.setUserBillAllReaded(searchBo.toPredicates());

        // 功能验证
        Collection<Long> billIds = billEntities.stream().map(BillEntity::getId).collect(Collectors.toSet());
        List<Long> readedBillIds = bizContentReadRepository
                .findContentIdsByTypeAndUserIdAndContentIds(ContentTypeEnum.B_BILL, randomUserId, billIds);
        assertEquals(Boolean.TRUE, result);
        assertEquals(5, readedBillIds.size());
    }

    @BeforeAll
    void initData() {
        billRepository.deleteAll();

        BillEntity billEntity1 = buildBillEntity();
        billEntity1.setTpMcu("NOT_EXIST_MCU");
        billRepository.save(billEntity1);

        BillEntity billEntity2 = buildBillEntity();
        billRepository.save(billEntity2);

        BillEntity billEntity3 = buildBillEntity();
        billRepository.save(billEntity3);

        BillEntity billEntity4 = buildBillEntity();
        billEntity4.setTpMcu("31194101");
        billEntity4.setTpAlph("DEV测试2");
        billEntity4.setTpAn8("an811111");
        billRepository.save(billEntity4);

        BillEntity billEntity5 = buildBillEntity();
        billEntity5.setTpMcu("31194100");
        billEntity5.setTpAlph("DEV测试3");
        billEntity5.setTpAn8("an822222");
        billRepository.save(billEntity5);

        BillEntity billEntity6 = buildBillEntity();
        billEntity6.setDelFlag("1");
        billEntity6.setTpAlph("DEV测试3");
        billRepository.save(billEntity6);
    }

    private RespWrapVo<Set<TenantRespDto>> buildInvalidHiveResponse
            (String hiveAsClientPath) throws URISyntaxException, IOException {
        if (hiveAsClientPath == null) {
            return null;
        }

        String path = "templates/staff_bill/doco/" + hiveAsClientPath;
        String json = Files.readString(Path.of(this.getClass().getClassLoader().getResource(path).toURI()));
        RespWrapVo<Set<TenantRespDto>> tenantRespDtoList = JSONObject.parseObject(json, RespWrapVo.class);
        if (tenantRespDtoList.getData() != null) {
            tenantRespDtoList.setData(new HashSet<>());
        }

        return tenantRespDtoList;
    }

    private RespWrapVo<Set<TenantRespDto>> buildHiveResponse(String hiveAsClientPath)
            throws URISyntaxException, IOException {
        String path = "templates/staff_bill/doco/" + hiveAsClientPath;
        String json = Files.readString(Path.of(this.getClass().getClassLoader().getResource(path).toURI()));

        List<TenantRespDto> tenantRespDtoList = JSONArray.parseArray(json, TenantRespDto.class);
        Set<TenantRespDto> set = new HashSet<>(tenantRespDtoList);
        RespWrapVo<Set<TenantRespDto>> respWrapVo = new RespWrapVo<>(set);
        return respWrapVo;
    }

    private RespWrapVo<List<TenantBillConfigResponse>> buildSUserClientResponse(String sUserClientPath)
            throws URISyntaxException, IOException {
        String path = "templates/staff_bill/doco/" + sUserClientPath;
        String json = Files.readString(Path.of(this.getClass().getClassLoader().getResource(path).toURI()));

        List<TenantBillConfigResponse> tenantBillConfigResponses = JSONArray.parseArray(json, TenantBillConfigResponse.class);
        RespWrapVo<List<TenantBillConfigResponse>> respWrapVo = new RespWrapVo<>(tenantBillConfigResponses);
        return respWrapVo;
    }

    private RespWrapVo<List<TenantBillConfigResponse>> buildInvalidSUserClientResponse(String sUserClientPath)
            throws URISyntaxException, IOException {
        if (sUserClientPath == null) {
            return null;
        }

        String path = "templates/staff_bill/doco/" + sUserClientPath;
        String json = Files.readString(Path.of(this.getClass().getClassLoader().getResource(path).toURI()));

        RespWrapVo respWrapVo = JSONObject.parseObject(json, RespWrapVo.class);
        return respWrapVo;
    }

    private Map<DocoAn8, List<StaffBillRespVo>> buildAn8Map() {
        Map<DocoAn8, List<StaffBillRespVo>> an8ListMap = new HashMap<>();

        List<DocoAn8> docoAn8List = Lists.newArrayList(
                DocoAn8.builder().doco("263869").an8("20087263").build(),
                DocoAn8.builder().doco(DOCO).an8(AN8).build()
        );
        for (DocoAn8 element : docoAn8List) {
            an8ListMap.put(element, new ArrayList<>());
        }

        return an8ListMap;
    }

    private RespWrapVo<String> buildMessageCenterClientResponse(String filepath)
            throws URISyntaxException, IOException {
        if (filepath == null) {
            return null;
        }

        String path = "templates/staff_bill/doco/" + filepath;
        String json = Files.readString(Path.of(this.getClass().getClassLoader().getResource(path).toURI()));

        RespWrapVo<String> respWrapVo = JSONObject.parseObject(json, RespWrapVo.class);
        return respWrapVo;
    }

    private Bill buildBill(String doco, String an8) {
        Bill bill = new Bill();
        bill.setTpDct("DN");
        bill.setTpDl01("付款通知书");
        bill.setTpGtitnm("Debit Note");
        bill.setTpGtfilenm("RD001.PDF");
        bill.setFileUrl("https://kip-private-dev.oss-cn-shanghai.aliyuncs.com/abc_RD001.PDF");
        bill.setTpCo("31019");
        bill.setTpDl03("嘉里(沈阳)房地产开发有限公司");
        bill.setTpMcu("31194100");
        bill.setTpUnit("208");
        bill.setTpAlph("DEV测试");
        bill.setTpDc("嘉里沈阳，租赁，商场");
        bill.setTpCrtutime(new Date());
        bill.setTpEv01("Y");
        bill.setTpEv02(StringUtils.EMPTY);
        bill.setDelFlag("0");
        bill.setFormatDate("2022-08-17");
        bill.setTpStatus(BillConstants.MSG_NOT_SEND);
        bill.setMailStatus(BillConstants.MSG_NOT_SEND);
        bill.setEmailStatus(BillConstants.MSG_NOT_SEND);
        bill.setTpFyr(22);
        bill.setTpPn(8);
        bill.setBillMonth(2208);
        bill.setTpDoco(doco);
        bill.setTpAn8(an8);
        return bill;
    }

    private BillEntity buildBillEntity() {
        var billEntity = new BillEntity();
        billEntity.setTpDct("DN");
        billEntity.setTpDl01("付款通知书");
        billEntity.setTpGtitnm("Debit Note");
        billEntity.setTpGtfilenm("RD001.PDF");
        billEntity.setFileUrl("https://kip-private-dev.oss-cn-shanghai.aliyuncs.com/abc_RD001.PDF");
        billEntity.setTpCo("31019");
        billEntity.setTpDl03("嘉里(沈阳)房地产开发有限公司");
        billEntity.setTpMcu("31194100");
        billEntity.setTpUnit("208");
        billEntity.setTpAlph("DEV测试");
        billEntity.setTpDc("嘉里沈阳，租赁，商场");
        billEntity.setTpCrtutime(new Date());
        billEntity.setTpEv01("Y");
        billEntity.setTpEv02(StringUtils.EMPTY);
        billEntity.setDelFlag("0");
        billEntity.setFormatDate("2022-08-17");
        billEntity.setTpStatus(BillConstants.MSG_NOT_SEND);
        billEntity.setMailStatus(BillConstants.MSG_NOT_SEND);
        billEntity.setEmailStatus(BillConstants.MSG_NOT_SEND);
        billEntity.setTpFyr(22);
        billEntity.setTpPn(8);
        billEntity.setBillMonth(2208);
        billEntity.setTpDoco("doco-456");
        billEntity.setTpAn8("12345678"); // 需要为8位数字
        return billEntity;
    }

    private void setUserInfo(Long userId) {
        UserInfoUtils.setUser("{\"nickName\":\"SuperAdmin\",\"roles\":\"SUPER_ADMIN\"" +
                ",\"userId\":" + userId + ",\"phoneNumber\":\"13661600321\",\"fromType\":\"B\"}");
    }

}
