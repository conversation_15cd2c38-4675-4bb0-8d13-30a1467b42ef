package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.constants.BillConstants;
import com.kerryprops.kip.bill.dao.BillSendConfigAn8LinkRepository;
import com.kerryprops.kip.bill.dao.BillSendConfigRepository;
import com.kerryprops.kip.bill.dao.entity.BillSendConfig;
import com.kerryprops.kip.bill.dao.entity.BillSendConfigAn8Link;
import com.kerryprops.kip.bill.webservice.vo.req.DocoBillPayer;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffBillReceiverRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffBillRespVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
public class API_selectBatchBillReceivers_BillServiceImplTest extends BaseIntegrationTest {

    //temp data set
    private List<BillSendConfig> configs;

    private List<BillSendConfigAn8Link> an8Links;

    @Autowired
    private BillServiceImpl impl;

    @Autowired
    private BillSendConfigRepository configRepository;

    @Autowired
    private BillSendConfigAn8LinkRepository an8LinkRepository;

    @Test
    public void _01_selectBatchBillReceivers_null_param() {
        Map<DocoBillPayer, Set<StaffBillReceiverRespVo>> map = impl.selectBatchBillReceivers(null);
        Assertions.assertTrue(map.isEmpty());
    }

    @Test
    public void _02_selectBatchBillReceivers_empty_param() {
        Map<DocoBillPayer, Set<StaffBillReceiverRespVo>> map = impl.selectBatchBillReceivers(Collections.emptySet());
        Assertions.assertNotNull(map);
        Assertions.assertTrue(CollectionUtils.sizeIsEmpty(map));
    }

    @Test
    public void _03_selectBatchBillReceivers_no_config() {
        //build param
        DocoBillPayer docoBillPayer1 = DocoBillPayer.builder().doco("111101").an8("11223301").alph("开发公司1").build();
        DocoBillPayer docoBillPayer2 = DocoBillPayer.builder().doco("111102").an8("11223302").alph("开发公司2").build();
        Map<DocoBillPayer, List<StaffBillRespVo>> inputMap = Map.of(
                docoBillPayer1, List.of(buildStaffBillRespVo(1l, docoBillPayer1)),
                docoBillPayer2, List.of(buildStaffBillRespVo(2l, docoBillPayer2)));

        //execute & verify
        Map<DocoBillPayer, Set<StaffBillReceiverRespVo>> map = impl.selectBatchBillReceivers(inputMap.keySet());
        Assertions.assertTrue(CollectionUtils.sizeIsEmpty(map));
    }

    @Test
    public void _04_selectBatchBillReceivers_query_deleted_config() {
        prepareTestData();

        //build param
        DocoBillPayer docoBillPayer = DocoBillPayer.builder().doco("112233").an8("an8_1").alph("alph_1").build();
        Map<DocoBillPayer, List<StaffBillRespVo>> inputMap = Map.of(
                docoBillPayer, List.of(buildStaffBillRespVo(1l, docoBillPayer)));

        //execute & verify
        Map<DocoBillPayer, Set<StaffBillReceiverRespVo>> map = impl.selectBatchBillReceivers(inputMap.keySet());
        Assertions.assertTrue(CollectionUtils.sizeIsEmpty(map));

        clearTestData();
    }

    @Test
    public void _05_selectBatchBillReceivers_query_deleted_config() {
        prepareTestData();

        //build param
        DocoBillPayer docoBillPayer = DocoBillPayer.builder().doco("112233").an8("an8_1").alph("alph_1").build();
        Map<DocoBillPayer, List<StaffBillRespVo>> inputMap = Map.of(
                docoBillPayer, List.of(buildStaffBillRespVo(1l, docoBillPayer)));

        //execute & verify
        Map<DocoBillPayer, Set<StaffBillReceiverRespVo>> map = impl.selectBatchBillReceivers(inputMap.keySet());
        Assertions.assertTrue(CollectionUtils.sizeIsEmpty(map));

        clearTestData();
    }

    @Test
    public void _06_selectBatchBillReceivers_query_deleted_payer() {
        prepareTestData();

        //build param
        DocoBillPayer docoBillPayer = DocoBillPayer.builder().doco("224411").an8("用户编号3").alph("用户名称3").build();
        Map<DocoBillPayer, List<StaffBillRespVo>> inputMap = Map.of(
                docoBillPayer, List.of(buildStaffBillRespVo(1l, docoBillPayer)));

        //execute & verify
        Map<DocoBillPayer, Set<StaffBillReceiverRespVo>> map = impl.selectBatchBillReceivers(inputMap.keySet());
        Assertions.assertTrue(CollectionUtils.sizeIsEmpty(map));

        clearTestData();
    }

    @Test
    public void _07_selectBatchBillReceivers_config_not_found() {
        prepareTestData();

        //build param
        DocoBillPayer docoBillPayer = DocoBillPayer.builder().doco("123456").an8("用户编号3").alph("用户名称3").build();
        Map<DocoBillPayer, List<StaffBillRespVo>> inputMap = Map.of(
                docoBillPayer, List.of(buildStaffBillRespVo(1l, docoBillPayer)));

        //execute & verify
        Map<DocoBillPayer, Set<StaffBillReceiverRespVo>> map = impl.selectBatchBillReceivers(inputMap.keySet());
        Assertions.assertTrue(CollectionUtils.sizeIsEmpty(map));

        clearTestData();
    }

    @Test
    public void _08_selectBatchBillReceivers_config_found() {
        prepareTestData();

        //build param
        DocoBillPayer docoBillPayer1 = DocoBillPayer.builder().doco("112244").an8("用户编号1").alph("用户名称1").build();
        DocoBillPayer docoBillPayer2 = DocoBillPayer.builder().doco("112255").an8("an8_2").alph("alph_2").build();
        Map<DocoBillPayer, List<StaffBillRespVo>> inputMap = Map.of(
                docoBillPayer1, List.of(buildStaffBillRespVo(1l, docoBillPayer1)),
                docoBillPayer2, List.of(buildStaffBillRespVo(2l, docoBillPayer2)));

        //execute & verify
        Map<DocoBillPayer, Set<StaffBillReceiverRespVo>> map = impl.selectBatchBillReceivers(inputMap.keySet());
        Assertions.assertEquals(2, map.size());

        Set<StaffBillReceiverRespVo> receiverRespVoSet1 = map.get(docoBillPayer1);
        Assertions.assertEquals(1, receiverRespVoSet1.size());
        StaffBillReceiverRespVo vo1 = receiverRespVoSet1.iterator().next();

        BillSendConfig billSendConfig1 = configs.get(3);
        Assertions.assertEquals(billSendConfig1.getEmail(), vo1.getEmail());
        Assertions.assertEquals(billSendConfig1.getTenantManagerId(), vo1.getUserId());
        Assertions.assertEquals(billSendConfig1.getEmailUsername(), vo1.getUserName());
        Assertions.assertEquals(billSendConfig1.getPhoneNumber(), vo1.getPhoneNumber());
        Assertions.assertEquals(billSendConfig1.getLoginNo(), vo1.getLoginAccount());

        Set<StaffBillReceiverRespVo> receiverRespVoSet2 = map.get(docoBillPayer2);
        Assertions.assertEquals(1, receiverRespVoSet2.size());
        StaffBillReceiverRespVo vo2 = receiverRespVoSet2.iterator().next();
        BillSendConfig billSendConfig2 = configs.get(2);
        Assertions.assertEquals(billSendConfig2.getEmail(), vo2.getEmail());
        Assertions.assertEquals(billSendConfig2.getTenantManagerId(), vo2.getUserId());
        Assertions.assertEquals(billSendConfig2.getEmailUsername(), vo2.getUserName());
        Assertions.assertEquals(billSendConfig2.getPhoneNumber(), vo2.getPhoneNumber());
        Assertions.assertEquals(billSendConfig2.getLoginNo(), vo2.getLoginAccount());
        clearTestData();
    }

    @Test
    void _09_selectBatchBillReceivers_random_alph_still_has_result() {
        prepareTestData();
        //build param
        DocoBillPayer docoBillPayer1 = DocoBillPayer.builder().doco("112244").alph(random.nextInt() + "").an8("用户编号4").build();
        Map<DocoBillPayer, List<StaffBillRespVo>> inputMap = Map.of(
                docoBillPayer1, List.of(buildStaffBillRespVo(1l, docoBillPayer1))
        );

        //execute & verify
        Map<DocoBillPayer, Set<StaffBillReceiverRespVo>> map = impl.selectBatchBillReceivers(inputMap.keySet());
        Assertions.assertTrue(CollectionUtils.isNotEmpty(map.keySet()));
        Assertions.assertTrue(CollectionUtils.isNotEmpty(map.get(map.keySet().iterator().next())));
        clearTestData();
    }

    private StaffBillRespVo buildStaffBillRespVo(long id, DocoBillPayer docoBillPayer) {
        StaffBillRespVo vo = new StaffBillRespVo();
        vo.setId(Long.valueOf(id));
        vo.setTpDoco(docoBillPayer.getDoco());
        vo.setTpAn8(docoBillPayer.getAn8());
        vo.setTpAlph(docoBillPayer.getAlph());
        vo.setTpDct("DN");
        vo.setTpDl01("付款通知书");
        vo.setFormatDate("2022-08-17");
        vo.setTpMcu("31194100");
        vo.setTpUnit("208");
        vo.setTpFyr(22);
        vo.setTpPn(8);
        vo.setFileUrl("https://kip-private-dev.oss-cn-shanghai.aliyuncs.com/abc_RD001.PDF");
        vo.setTpGtfilenm("RD001.PDF");
        vo.setTpStatus(BillConstants.MSG_NOT_SEND);
        vo.setMailStatus(BillConstants.MSG_NOT_SEND);
        vo.setEmailStatus(BillConstants.MSG_NOT_SEND);
        vo.setEmailErr(StringUtils.EMPTY);
        return vo;
    }


    private void prepareTestData() {
        List<BillSendConfig> oldConfigs = buildBillSendConfigs();
        configs = configRepository.saveAll(oldConfigs);

        List<BillSendConfigAn8Link> oldAn8Links = buildBillSendConfigAn8Links(configs);
        an8Links = an8LinkRepository.saveAll(oldAn8Links);
    }

    private void clearTestData() {
        configRepository.deleteAll(configs);
        an8LinkRepository.deleteAll(an8Links);
    }

    private List<BillSendConfig> buildBillSendConfigs() {
        List<BillSendConfig> configs = List.of(
                BillSendConfig.builder().doco("112233").email("<EMAIL>")
                        .phoneNumber(StringUtils.EMPTY).loginNo(StringUtils.EMPTY)
                        .mcu("mcu1").unit("unit1").isDel(1)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .build(),
                BillSendConfig.builder().doco("224411").email("<EMAIL>")
                        .phoneNumber("13764912345").loginNo("60939742")
                        .mcu("mcu1").unit("unit2").isDel(0)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .build(),
                BillSendConfig.builder().doco("112255").email("<EMAIL>")
                        .phoneNumber("13764912346").loginNo("12345678")
                        .mcu("mcu1").unit("unit2").isDel(0)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .build(),
                BillSendConfig.builder().doco("112244").email("<EMAIL>")
                        .phoneNumber("13764912345").loginNo("60939742")
                        .mcu("mcu1").unit("unit2").isDel(0)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .build());
        return configs;
    }

    private List<BillSendConfigAn8Link> buildBillSendConfigAn8Links(List<BillSendConfig> configs) {
        List<BillSendConfigAn8Link> an8Links = List.of(
                BillSendConfigAn8Link.builder().configId(configs.get(0).getId()).isDel(1).alph("alph_1").an8("an8_1").build(),

                BillSendConfigAn8Link.builder().configId(configs.get(1).getId()).isDel(0).alph("用户名称2").an8("用户编号2").build(),
                BillSendConfigAn8Link.builder().configId(configs.get(1).getId()).isDel(1).alph("用户名称3").an8("用户编号3").build(),

                BillSendConfigAn8Link.builder().configId(configs.get(2).getId()).isDel(0).alph("alph_2").an8("an8_2").build(),

                BillSendConfigAn8Link.builder().configId(configs.get(3).getId()).isDel(0).alph("用户名称4").an8("用户编号4").build(),
                BillSendConfigAn8Link.builder().configId(configs.get(3).getId()).isDel(0).alph("用户名称1").an8("用户编号1").build()
        );

        return an8Links;
    }

}
