package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.dao.BillSendConfigAn8LinkRepository;
import com.kerryprops.kip.bill.dao.BillSendConfigRepository;
import com.kerryprops.kip.bill.dao.entity.BillSendConfig;
import com.kerryprops.kip.bill.dao.entity.BillSendConfigAn8Link;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.webservice.vo.resp.BillPayer;
import com.kerryprops.kip.hiveas.feign.dto.resp.BuildingRespDto;
import com.kerryprops.kip.hiveas.webservice.resource.resp.ProjectResp;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.mockito.ArgumentMatchers;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static org.mockito.Mockito.doReturn;

class API_queryBillPayers_BillSendConfigServiceImplTest extends BaseIntegrationTest {

    private static final String B_ACCOUNT = "********";

    @Autowired
    private BillSendConfigServiceImpl impl;

    @Autowired
    private BillSendConfigRepository configRepository;

    @Autowired
    private BillSendConfigAn8LinkRepository an8LinkRepository;

    @Autowired
    private HiveAsClient hiveAsClient;

    @Test
    void _02_queryBillPayers_no_data() {
        List<BillPayer> billPayers = impl.queryBillPayers(B_ACCOUNT + "1");
        Assertions.assertTrue(CollectionUtils.isEmpty(billPayers));
    }

    @Test
    void _03_queryBillPayers_all_deleted_data() {
        //prepare test data
        List<BillSendConfig> oldConfigs = List.of(
                BillSendConfig.builder().doco("112233").email("<EMAIL>")
                        .phoneNumber(StringUtils.EMPTY).loginNo(StringUtils.EMPTY)
                        .mcu("mcu1").unit("unit1").isDel(1)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .build(),
                BillSendConfig.builder().doco("224411").email("<EMAIL>")
                        .phoneNumber("***********").loginNo("********")
                        .mcu("mcu1").unit("unit2").isDel(1)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .build());
        List<BillSendConfig> newConfigs = configRepository.saveAll(oldConfigs);

        //execute & verify
        List<BillPayer> billPayers = impl.queryBillPayers(B_ACCOUNT);
        Assertions.assertTrue(CollectionUtils.isEmpty(billPayers));

        //clear test data
        configRepository.deleteAll(newConfigs);
    }

    @Test
    void _04_queryBillPayers_data_not_found() {
        //prepare test data
        List<BillSendConfig> oldConfigs = buildBillSendConfigs();
        List<BillSendConfig> newConfigs = configRepository.saveAll(oldConfigs);

        //execute & verify
        List<BillPayer> billPayers = impl.queryBillPayers("test1234567890");
        Assertions.assertTrue(CollectionUtils.isEmpty(billPayers));

        //clear test data
        configRepository.deleteAll(newConfigs);
    }

    @Test
    void _05_queryBillPayers_data_found() {
        //prepare test data
        List<BillSendConfig> oldConfigs = buildBillSendConfigs();
        List<BillSendConfig> newConfigs = configRepository.saveAll(oldConfigs);
        List<BillSendConfigAn8Link> oldAn8Links = buildBillSendConfigAn8Links(newConfigs);
        List<BillSendConfigAn8Link> newAn8Links = an8LinkRepository.saveAll(oldAn8Links);

        //execute & verify
        List<BillPayer> billPayers = impl.queryBillPayers(B_ACCOUNT);
        String actualAlph = billPayers.stream().map(BillPayer::getTpAlph).collect(Collectors.joining(","));
        String actualAn8 = billPayers.stream().map(BillPayer::getTpAn8).collect(Collectors.joining(","));
        Assertions.assertEquals("用户名称1,用户名称2,用户名称4", actualAlph);
        Assertions.assertEquals("用户编号1,用户编号2,用户编号4", actualAn8);

        ProjectResp projectResp = new ProjectResp();
        BuildingRespDto building_1 = new BuildingRespDto();
        building_1.setPropertyDeveloperBU(oldConfigs.get(0).getMcu());
        projectResp.setBuildings(List.of(building_1));
        doReturn(projectResp).when(hiveAsClient).getProjectByID(ArgumentMatchers.anyString());
        billPayers = impl.fuzzyQueryPayers("192", oldAn8Links.get(1).getAlph());
        Assertions.assertTrue(CollectionUtils.isNotEmpty(billPayers));
        Assertions.assertEquals(1, billPayers.size());
        Assertions.assertEquals(oldAn8Links.get(1).getAlph(), billPayers.get(0).getTpAlph());
        Assertions.assertEquals(oldAn8Links.get(1).getAn8(), billPayers.get(0).getTpAn8());

        ProjectResp projectResp2 = new ProjectResp();
        BuildingRespDto building_2 = new BuildingRespDto();
        building_2.setPropertyDeveloperBU(oldConfigs.get(0).getMcu());
        projectResp2.setBuildings(List.of(building_2));
        doReturn(projectResp2).when(hiveAsClient).getProjectByID(ArgumentMatchers.anyString());
        billPayers = impl.fuzzyQueryPayers("192", oldAn8Links.get(1).getAlph()
                + "-" + oldAn8Links.get(1).getAn8());
        Assertions.assertTrue(CollectionUtils.isNotEmpty(billPayers));
        Assertions.assertEquals(1, billPayers.size());
        Assertions.assertEquals(oldAn8Links.get(1).getAlph(), billPayers.get(0).getTpAlph());
        Assertions.assertEquals(oldAn8Links.get(1).getAn8(), billPayers.get(0).getTpAn8());

        billPayers = impl.fuzzyQueryPayers("192", "" + random.nextLong());
        Assertions.assertTrue(CollectionUtils.isEmpty(billPayers));
        //clear test data
        configRepository.deleteAll(newConfigs);
        an8LinkRepository.deleteAll(newAn8Links);
    }

    @ParameterizedTest
    @NullAndEmptySource
    void _01_queryBillPayers_special_bAccount(String bAccount) {
        List<BillPayer> billPayers = impl.queryBillPayers(bAccount);
        Assertions.assertTrue(CollectionUtils.isEmpty(billPayers));
    }

    private List<BillSendConfig> buildBillSendConfigs() {
        return List.of(
                BillSendConfig.builder().doco("112233").email("<EMAIL>")
                        .phoneNumber(StringUtils.EMPTY).loginNo(StringUtils.EMPTY)
                        .mcu("mcu1").unit("unit1").isDel(1)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .projectId("192")
                        .build(),
                BillSendConfig.builder().doco("224411").email("<EMAIL>")
                        .phoneNumber("***********").loginNo("********")
                        .mcu("mcu1").unit("unit2").isDel(0)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .projectId("192")
                        .build(),
                BillSendConfig.builder().doco("112255").email("<EMAIL>")
                        .phoneNumber("13764912346").loginNo("12345678")
                        .mcu("mcu1").unit("unit2").isDel(0)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .projectId("192")
                        .build(),
                BillSendConfig.builder().doco("112244").email("<EMAIL>")
                        .phoneNumber("***********").loginNo("********")
                        .mcu("mcu1").unit("unit2").isDel(0)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .projectId("192")
                        .build());
    }

    private List<BillSendConfigAn8Link> buildBillSendConfigAn8Links(List<BillSendConfig> configs) {
        return List.of(
                BillSendConfigAn8Link.builder().configId(configs.get(0).getId()).isDel(1).alph("alph_1").an8("an8_1").build(),

                BillSendConfigAn8Link.builder().configId(configs.get(1).getId()).isDel(0).alph("用户名称2").an8("用户编号2").build(),
                BillSendConfigAn8Link.builder().configId(configs.get(1).getId()).isDel(1).alph("用户名称3").an8("用户编号3").build(),

                BillSendConfigAn8Link.builder().configId(configs.get(2).getId()).isDel(0).alph("alph_2").an8("an8_2").build(),

                BillSendConfigAn8Link.builder().configId(configs.get(3).getId()).isDel(0).alph("用户名称4").an8("用户编号4").build(),
                BillSendConfigAn8Link.builder().configId(configs.get(3).getId()).isDel(0).alph("用户名称1").an8("用户编号1").build()
        );
    }


}
