package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.dao.BillRepository;
import com.kerryprops.kip.bill.dao.entity.QBillEntity;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.service.model.s.DocoPayerBo;
import com.kerryprops.kip.hiveas.feign.dto.BuildingDTO;
import com.kerryprops.kip.hiveas.webservice.vo.resp.ProjectBuildingVO;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.StringPath;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * BillSendConfigServiceImplTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-04-16
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("账单发送配置-Service-单元测试")
public class BillSendConfigServiceImplTest {

    @Mock
    private HiveAsClient hiveAsClient;

    @Mock
    private BillRepository billRepository;

    @InjectMocks
    private BillSendConfigServiceImpl billSendConfigService;

    @Test
    @DisplayName("异常场景：项目ID列表为空")
    public void testBuildJdeBusFromProjects_emptyProjectIds() throws Exception {
        // Arrange
        List<String> projectIds = Collections.emptyList();

        // 使用反射访问私有方法
        Method method = BillSendConfigServiceImpl.class.getDeclaredMethod("buildJdeBusFromProjects", List.class);
        method.setAccessible(true);

        // Act
        @SuppressWarnings("unchecked") List<String> result =
                (List<String>) method.invoke(billSendConfigService, projectIds);

        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("异常场景：Hive服务返回空数据")
    public void testBuildJdeBusFromProjects_hiveReturnsEmpty() throws Exception {
        // Arrange
        List<String> projectIds = List.of("project1", "project2");

        when(hiveAsClient.getCenterByIds(any(String[].class))).thenReturn(Collections.emptyList());

        // 使用反射访问私有方法
        Method method = BillSendConfigServiceImpl.class.getDeclaredMethod("buildJdeBusFromProjects", List.class);
        method.setAccessible(true);

        // Act
        @SuppressWarnings("unchecked") List<String> result =
                (List<String>) method.invoke(billSendConfigService, projectIds);

        // Assert
        assertNull(result);
        verify(hiveAsClient, times(1)).getCenterByIds(any(String[].class));
    }

    @Test
    @DisplayName("正常场景：查询合同付款人成功（已包含buildJdeBusFromProjects方法正常场景）")
    public void testQueryDocoPayers_success() throws Exception {
        // Arrange
        String projectId = "project1";

        List<String> buList = List.of("BU1", "BU2");
        List<Tuple> mockTuples = List.of(createMockTuple("doco1", "payer1"), createMockTuple("doco2", "payer2"));

        BuildingDTO buildingDTO1 = new BuildingDTO();
        buildingDTO1.setPropertyDeveloperBU("propertyDeveloperBU1");
        buildingDTO1.setPropertyManagementBU("propertyManagementBU1");

        BuildingDTO buildingDTO2 = new BuildingDTO();
        buildingDTO2.setPropertyDeveloperBU("propertyDeveloperBU2");
        buildingDTO2.setPropertyManagementBU("propertyManagementBU2");

        ProjectBuildingVO project1 = new ProjectBuildingVO();
        project1.setBuildings(List.of(buildingDTO1, buildingDTO2));

        ProjectBuildingVO project2 = new ProjectBuildingVO();
        project2.setBuildings(List.of(buildingDTO1));

        List<ProjectBuildingVO> mockCenters = List.of(project1, project2);
        when(hiveAsClient.getCenterByIds(any(String[].class))).thenReturn(mockCenters);


        // Mock JPAQueryFactory behavior
        JPAQueryFactory mockQueryFactory = mock(JPAQueryFactory.class);
        JPAQuery mockJPAQuery = mock(JPAQuery.class);
        when(billRepository.getJpaQueryFactory()).thenReturn(mockQueryFactory);
        when(mockQueryFactory.select(any(StringPath.class), any(StringPath.class), any(StringPath.class))).thenReturn(
                mockJPAQuery);
        when(mockJPAQuery.from(any(QBillEntity.class))).thenReturn(mockJPAQuery);
        when(mockJPAQuery.where(any(Predicate.class))).thenReturn(mockJPAQuery);
        when(mockJPAQuery.groupBy(any(StringPath.class), any(StringPath.class), any(StringPath.class))).thenReturn(
                mockJPAQuery);
        when(mockJPAQuery.orderBy(any(OrderSpecifier.class), any(OrderSpecifier.class),
                                  any(OrderSpecifier.class))).thenReturn(mockJPAQuery);
        when(mockJPAQuery.fetch()).thenReturn(mockTuples);

        // Access the private method
        Method method = BillSendConfigServiceImpl.class.getDeclaredMethod("queryDocoPayers", String.class);
        method.setAccessible(true);

        // Act
        @SuppressWarnings("unchecked") List<DocoPayerBo> result =
                (List<DocoPayerBo>) method.invoke(billSendConfigService, projectId);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(billRepository, times(1)).getJpaQueryFactory();
    }

    private Tuple createMockTuple(String doco, String an8) {
        Tuple tuple = mock(Tuple.class);
        when(tuple.get(0, String.class)).thenReturn(doco);
        when(tuple.get(1, String.class)).thenReturn(an8);
        return tuple;
    }

}