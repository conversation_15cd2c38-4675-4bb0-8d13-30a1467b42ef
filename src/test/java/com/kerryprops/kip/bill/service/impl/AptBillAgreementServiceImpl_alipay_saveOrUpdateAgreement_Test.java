package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.enums.DirectDebitAgreementSignType;
import com.kerryprops.kip.bill.common.enums.DirectDebitAgreementStatus;
import com.kerryprops.kip.bill.dao.AptBillDirectDebitsAgreementRepository;
import com.kerryprops.kip.bill.dao.entity.AptBillDirectDebitsAgreement;
import com.kerryprops.kip.bill.service.AptBillAgreementService;
import com.kerryprops.kip.pmw.client.resource.AsyncAgreementStatusChangedResource;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

class AptBillAgreementServiceImpl_alipay_saveOrUpdateAgreement_Test extends BaseIntegrationTest {

    @Autowired
    AptBillAgreementService aptBillAgreementService;

    @Autowired
    AptBillDirectDebitsAgreementRepository agreementRepository;

    @Test
    void _01_saveOrUpdateAgreement_alipay_normal_signed() {
        AsyncAgreementStatusChangedResource.AsyncAgreementStatusChangedBodyResource bodyResource
                = normalAgreementCallback();
        aptBillAgreementService.saveOrUpdateAgreement(bodyResource);
        AptBillDirectDebitsAgreement agreement = agreementRepository.findTopByAgreementNo(bodyResource.getAgreementNo());
        assertNotNull(agreement);
        assertEquals(DirectDebitAgreementStatus.ACTIVE, agreement.getAgreementStatus());
        assertEquals("ALIPAY", agreement.getPspName());
        assertEquals(bodyResource.getPersonalProductCode(), agreement.getRoomId());
        assertEquals(DirectDebitAgreementSignType.DIRECT_SIGN, agreement.getSignType());
        assertEquals(bodyResource.getPspLogonId(), agreement.getPspLogonId());
    }

    private AsyncAgreementStatusChangedResource.AsyncAgreementStatusChangedBodyResource normalAgreementCallback() {
        AsyncAgreementStatusChangedResource.AsyncAgreementStatusChangedBodyResource bodyResource
                = new AsyncAgreementStatusChangedResource.AsyncAgreementStatusChangedBodyResource();
        bodyResource.setAgreementNo("20231213041100000007");
        bodyResource.setAgreementStatus("ACTIVE");
        bodyResource.setCompanyCode("31007");
        bodyResource.setInvalidTime(ZonedDateTime.now().plusYears(1));
        bodyResource.setNotifyUrl("https://dev-kip-service-internal.kerryonvip.com/kip-billing/s/apt/bill/direct_debits/agreement/20231213041100000007/callback");
        bodyResource.setOrderSource("KIP_BILLING");
        bodyResource.setPersonalProductCode("8aaa84897bc9925e017bc9b1307100df");
        bodyResource.setProductType("BILLING");
        bodyResource.setProjectId("192");
        bodyResource.setPspLogonId("buz***@126.com");
        bodyResource.setPspName("ALIPAY");
        bodyResource.setPspUserId("2088702386719162");
        bodyResource.setSignTime(ZonedDateTime.now());
        bodyResource.setUnsignTime(ZonedDateTime.now().plusYears(100L));
        bodyResource.setUnsignType("UNKNOWN");
        bodyResource.setOrderNo("");
        bodyResource.setUserPhoneNumber("");
        bodyResource.setUserProfileId("");
        return bodyResource;
    }

}