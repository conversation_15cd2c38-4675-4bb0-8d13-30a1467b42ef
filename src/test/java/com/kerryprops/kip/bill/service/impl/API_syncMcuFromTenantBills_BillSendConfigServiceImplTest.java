package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.constants.BillConstants;
import com.kerryprops.kip.bill.common.utils.DateUtils;
import com.kerryprops.kip.bill.dao.BillRepository;
import com.kerryprops.kip.bill.dao.BillSendConfigAn8LinkRepository;
import com.kerryprops.kip.bill.dao.BillSendConfigRepository;
import com.kerryprops.kip.bill.dao.entity.BillEntity;
import com.kerryprops.kip.bill.dao.entity.BillSendConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;

@Slf4j
class API_syncMcuFromTenantBills_BillSendConfigServiceImplTest extends BaseIntegrationTest {

    @Autowired
    private BillRepository billRepository;

    @Autowired
    private BillSendConfigRepository configRepository;

    @Autowired
    private BillSendConfigAn8LinkRepository an8LinkRepository;

    @Autowired
    private BillSendConfigServiceImpl impl;

    @Test
    public void _02_syncMcuFromTenantBills_01_config_not_found() {
        configRepository.deleteAll();
        configRepository.flush();
        boolean result = impl.syncMcuFromTenantBills();
        Assertions.assertFalse(result);
    }

    @Test
    public void _02_syncMcuFromTenantBills_04_config_updated() {
        billRepository.deleteAll();
        billRepository.flush();
        configRepository.deleteAll();
        configRepository.flush();
        //prepare test data for BillSendConfig
        final String EXPECT_MCU1 = "mcu2";
        final String EXPECT_MCU2 = "mcu2";
        final String EXPECT_UNIT1 = "unit2";
        final String EXPECT_UNIT2 = "unit3,unit5";
        List<BillSendConfig> configList = List.of(
                BillSendConfig.builder().doco("112233").email("<EMAIL>")
                        .phoneNumber(StringUtils.EMPTY).loginNo(StringUtils.EMPTY)
                        .mcu("EXPECT_MCU1").unit("EXPECT_UNIT1").isDel(1)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .build(),
                BillSendConfig.builder().doco("112244").email("<EMAIL>")
                        .phoneNumber("13764912345").loginNo("60939742")
                        .mcu(EXPECT_MCU1).unit(EXPECT_UNIT1).isDel(0)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .build(),
                BillSendConfig.builder().doco("112255").email("<EMAIL>")
                        .phoneNumber(StringUtils.EMPTY).loginNo(StringUtils.EMPTY)
                        .mcu("mcu2").unit("unit3").isDel(0)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .build());
        List<BillSendConfig> newConfigList = configRepository.saveAll(configList);

        //prepare test data for tenant bills
        List<BillEntity> billEntities = buildBillDataSet();
        List<BillEntity> newBillEntities = billRepository.saveAll(billEntities);

        //execute
        boolean result = impl.syncMcuFromTenantBills();

        //verify
        Assertions.assertTrue(result);

        BillSendConfig billSendConfig1 = configRepository.findById(newConfigList.get(1).getId()).orElse(null);
        Assertions.assertNotNull(billSendConfig1);
        Assertions.assertEquals(billSendConfig1.getMcu(), EXPECT_MCU1);
        Assertions.assertEquals(billSendConfig1.getUnit(), EXPECT_UNIT1);

        BillSendConfig billSendConfig2 = configRepository.findById(newConfigList.get(2).getId()).orElse(null);
        Assertions.assertNotNull(billSendConfig2);
        Assertions.assertEquals(billSendConfig2.getMcu(), EXPECT_MCU2);
        Assertions.assertEquals(billSendConfig2.getUnit(), EXPECT_UNIT2);

        //clear test data
        configRepository.deleteAll(newConfigList);
        billRepository.deleteAll(newBillEntities);
    }

    @Test
    void _02_syncMcuFromTenantBills_03_no_config_updated() {
        billRepository.deleteAll();
        billRepository.flush();
        configRepository.deleteAll();
        configRepository.flush();
        //prepare test data for BillSendConfig
        final String EXPECT_MCU = "mcu2";
        final String EXPECT_UNIT = "unit2";
        List<BillSendConfig> configList = List.of(
                BillSendConfig.builder().doco("112233").email("<EMAIL>")
                        .phoneNumber(StringUtils.EMPTY).loginNo(StringUtils.EMPTY)
                        .mcu(EXPECT_MCU).unit(EXPECT_UNIT).isDel(1)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .build(),
                BillSendConfig.builder().doco("112244").email("<EMAIL>")
                        .phoneNumber("13764912345").loginNo("60939742")
                        .mcu(EXPECT_MCU).unit(EXPECT_UNIT).isDel(0)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .build());
        List<BillSendConfig> newConfigList = configRepository.saveAll(configList);

        //prepare test data for tenant bills
        List<BillEntity> billEntities = buildBillDataSet();
        List<BillEntity> newBillEntities = billRepository.saveAll(billEntities);

        //execute
        boolean result = impl.syncMcuFromTenantBills();

        //verify
        Assertions.assertTrue(result);

        BillSendConfig billSendConfig = configRepository.findById(newConfigList.get(1).getId()).orElse(null);
        Assertions.assertNotNull(billSendConfig);
        Assertions.assertEquals(billSendConfig.getMcu(), EXPECT_MCU);
        Assertions.assertEquals(billSendConfig.getUnit(), EXPECT_UNIT);

        //clear test data
        configRepository.deleteAll(newConfigList);
        billRepository.deleteAll(newBillEntities);
    }

    @Test
    void _02_syncMcuFromTenantBills_02_active_config_not_found() {
        configRepository.deleteAll();
        configRepository.flush();
        //prepare test data for BillSendConfig
        List<BillSendConfig> configList = List.of(
                BillSendConfig.builder().doco("112233").email("<EMAIL>")
                        .phoneNumber(StringUtils.EMPTY).loginNo(StringUtils.EMPTY)
                        .mcu("mcu1").unit("unit1").isDel(1)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .build(),
                BillSendConfig.builder().doco("112244").email("<EMAIL>")
                        .phoneNumber("13764912345").loginNo("60939742")
                        .mcu("mcu2").unit("unit2").isDel(1)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .build()
        );
        List<BillSendConfig> newConfigList = configRepository.saveAll(configList);

        //execute & verify
        boolean result = impl.syncMcuFromTenantBills();
        Assertions.assertFalse(result);

        //clear test data
        configRepository.deleteAll(newConfigList);
    }

    private List<BillEntity> buildBillDataSet() {
        List<BillEntity> billEntities = Arrays.asList(
                BillEntity.builder().tpEv01("Y").tpDct("D1").tpMcu("mcu1").tpUnit("unit1").tpDoco("112233")
                        .tpAn8("12345678").tpAlph("A开发公司").tpFyr(22).tpPn(1).formatDate("2022-01-05").delFlag("0")
                        .tpStatus(5).mailStatus(BillConstants.MSG_SUCCESS).emailStatus(BillConstants.MSG_NOT_SEND)
                        .mailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-05 20:23:58"))
                        .readStatus(0).tpGtfilenm("R1.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("D1").tpMcu("mcu2").tpUnit("unit2").tpDoco("112244")
                        .tpAn8("12345678").tpAlph("A开发公司").tpFyr(22).tpPn(2).formatDate("2022-02-04").delFlag("0")
                        .tpStatus(5).mailStatus(BillConstants.MSG_FAILURE).emailStatus(BillConstants.MSG_SUCCESS)
                        .mailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-02-04 10:11:22"))
                        .emailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-02-04 11:30:20"))
                        .emailErr("[{\"email\":\"<EMAIL>\",\"sendStatus\":\"发送成功\",\"sendTime\":\"2022-02-04 11:41:41\"}]")
                        .readStatus(0).tpGtfilenm("R2.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("D1").tpMcu("mcu2").tpUnit("unit4").tpDoco("112255")
                        .tpAn8("12345678").tpAlph("A开发公司").tpFyr(22).tpPn(1).formatDate("2022-01-07").delFlag("1")
                        .deleteBy("jane.dong")
                        .deleteTime(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-08 11:22:59"))
                        .tpStatus(BillConstants.MSG_NOT_SEND).mailStatus(BillConstants.MSG_NOT_SEND)
                        .emailStatus(BillConstants.MSG_NOT_SEND).readStatus(0).tpGtfilenm("R3.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("D2").tpMcu("mcu2").tpUnit("unit3").tpDoco("112255")
                        .tpAn8("12345678").tpAlph("A开发公司").tpFyr(22).tpPn(1).formatDate("2022-01-08").delFlag("0")
                        .tpStatus(5).mailStatus(BillConstants.MSG_NOT_SEND).emailStatus(BillConstants.MSG_SUCCESS)
                        .emailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-08 16:10:20"))
                        .emailErr("[{\"email\":\"<EMAIL>\",\"sendStatus\":\"发送成功\",\"sendTime\":\"2022-01-08 16:31:41\"}]")
                        .readStatus(1).tpGtfilenm("R4.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("D2").tpMcu("mcu2").tpUnit("unit5").tpDoco("112255")
                        .tpAn8("12345678").tpAlph("A开发公司").tpFyr(22).tpPn(1).formatDate("2022-01-18").delFlag("0")
                        .tpStatus(5).mailStatus(BillConstants.MSG_NOT_SEND).emailStatus(BillConstants.MSG_PARTIAL_SUCCESS)
                        .emailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-18 14:00:05"))
                        .emailErr("[{\"email\":\"<EMAIL>\",\"sendStatus\":\"发送成功\",\"sendTime\":\"2022-01-18 14:31:41\"}]")
                        .readStatus(0).tpGtfilenm("R5.PDF").build()
        );
        return billEntities;
    }

}
