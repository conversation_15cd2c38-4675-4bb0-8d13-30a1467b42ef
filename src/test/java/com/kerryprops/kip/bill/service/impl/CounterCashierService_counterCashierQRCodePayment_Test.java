package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import com.kerryprops.kip.bill.common.enums.BillPayModule;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptPayConfigRepository;
import com.kerryprops.kip.bill.dao.AptPayRepository;
import com.kerryprops.kip.bill.dao.AptPaymentBillRepository;
import com.kerryprops.kip.bill.dao.AptPaymentInfoRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPayConfig;
import com.kerryprops.kip.bill.dao.entity.AptPaymentBill;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.dao.entity.QAptPay;
import com.kerryprops.kip.bill.dao.entity.QAptPaymentInfo;
import com.kerryprops.kip.bill.service.CounterCashierService;
import com.kerryprops.kip.bill.webservice.vo.req.AptBillPaymentRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierQRCodePaymentRequest;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierQRCodePaymentResource;
import com.kerryprops.kip.hiveas.feign.dto.resp.BuildingRespDto;
import com.kerryprops.kip.hiveas.webservice.resource.resp.BuildingResp;
import com.kerryprops.kip.pmw.client.resource.AsynPaymentResultResource;
import com.kerryprops.kip.pmw.client.resource.CombinedPaymentTxOutputResource;
import com.kerryprops.kip.pmw.variables.PayOption;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Date;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.nullable;
import static org.mockito.Mockito.doReturn;

@Slf4j
class CounterCashierService_counterCashierQRCodePayment_Test extends BaseIntegrationTest {

    @Autowired
    CounterCashierService counterCashierService;

    @Autowired
    AptBillRepository aptBillRepository;

    @Autowired
    AptPayRepository aptPayRepository;

    @Autowired
    AptPaymentBillRepository aptPaymentBillRepository;

    @Autowired
    AptPaymentInfoRepository paymentInfoRepository;

    @Autowired
    AptPayConfigRepository aptPayConfigRepository;

    @Test
    void _01_counterCashierQRCodePayment_normal() {

        AptBill aptBill = randomAptBill();
        aptBill = aptBillRepository.save(aptBill);
        initPayConfig(aptBill.getProjectId(), "");

        CashierQRCodePaymentRequest request = new CashierQRCodePaymentRequest();
        request.setAuthCode(String.valueOf(random.nextInt()));
        request.setPaymentDesc(String.valueOf(random.nextInt()));
        request.setProjectId("192");
        request.setPayOption(PayOption.WECHATPAY.name());
        AptBillPaymentRequest billReq = new AptBillPaymentRequest();
        billReq.setBillNo(aptBill.getBillNo());
        billReq.setAmount(BigDecimal.valueOf(aptBill.getAmt()));
        request.setBills(List.of(billReq));
        Instant instant = Instant.now().minus(10, ChronoUnit.HOURS).truncatedTo(ChronoUnit.SECONDS);
        request.setPaymentTime(Date.from(instant));

        CombinedPaymentTxOutputResource randomPayResult = randomPayResult();
        randomPayResult.getBody().getDirectPayResource().setAmount(billReq.getAmount());
        randomPayResult.getBody().getDirectPayResource().setState("SUCCESS");

        doReturn(randomPayResult).when(paymentClientService).createCombinedPaymentTransaction(anyString()
                , nullable(String.class), nullable(String.class), nullable(String.class), any());

        RespWrapVo<BuildingResp> wrapVo = new RespWrapVo<>();
        BuildingRespDto hiveBuilding = new BuildingRespDto();
        hiveBuilding.setPropertyManagementCo("?");
        BuildingResp hiveBuildingResp = new BuildingResp();
        hiveBuildingResp.setBuilding(hiveBuilding);
        wrapVo.setData(hiveBuildingResp);
        doReturn("?").when(hiveAsClient).getPropertyManagementCo(anyString());

        CashierQRCodePaymentResource response = counterCashierService.counterCashierQRCodePayment(request);
        // TODO response.paymentDesc = resultDesc
        assertEquals(0, billReq.getAmount().compareTo(response.getPaymentAmount()));
        assertEquals(BillPaymentStatus.PAID, response.getPaymentStatus());
        assertNotNull(response.getOrderNo());
        assertNotNull(response.getPaymentTime());

        String paymentInfoNo = response.getOrderNo();
        // apt_pay validity
        List<AptPay> dbAptPays = aptPayRepository.getJpaQueryFactory().selectFrom(QAptPay.aptPay)
                .where(QAptPay.aptPay.paymentInfoId.eq(paymentInfoNo)).fetch();
        assertNotNull(dbAptPays);
        assertEquals(0, dbAptPays.size());
        // apt_payment_info validity
        AptPaymentInfo dbAptPaymentInfo = paymentInfoRepository.getJpaQueryFactory().selectFrom(QAptPaymentInfo.aptPaymentInfo)
                .where(QAptPaymentInfo.aptPaymentInfo.id.eq(paymentInfoNo)).fetchFirst();
        assertNotNull(dbAptPaymentInfo);
        assertEquals(billReq.getAmount().doubleValue(), dbAptPaymentInfo.getAmt());
        assertEquals(BillPaymentStatus.PAYING, dbAptPaymentInfo.getPaymentStatus());
        assertEquals(randomPayResult.getBody().getSessionDetail().getSessionInfo().getSessionId(), dbAptPaymentInfo.getPaySession());
        assertEquals(aptBill.getProjectId(), dbAptPaymentInfo.getProjectId());
        assertEquals(aptBill.getBuildingId(), dbAptPaymentInfo.getBuildingId());
        assertEquals(aptBill.getProjectId(), dbAptPaymentInfo.getProjectId());
        assertEquals(aptBill.getFloorId(), dbAptPaymentInfo.getFloorId());
        assertEquals(0, dbAptPaymentInfo.getDeleted());
        assertEquals(randomPayResult.getBody().getDirectPayResource().getPspTransId(), dbAptPaymentInfo.getPspTransNo());
        assertEquals(randomPayResult.getBody().getDirectPayResource().getConfirmedPspTradeNo(), dbAptPaymentInfo.getPaymentTransNo());
        assertEquals(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP), dbAptPaymentInfo.getAdvanceAmount());
        assertEquals(BillPayModule.CASHIER, dbAptPaymentInfo.getBillPayModule());
        assertEquals(instant, dbAptPaymentInfo.getPaymentTime().toInstant());
        // apt_payment_bill validity
        List<AptPaymentBill> dbPayBills = aptPaymentBillRepository.findAllByBillIdIn(List.of(aptBill.getId()));
        assertNotNull(dbPayBills);
        assertEquals(1, dbPayBills.size());
        AptPaymentBill dbPayBill1 = dbPayBills.get(0);
        assertEquals(paymentInfoNo, dbPayBill1.getPaymentInfoId());
    }

    private void initPayConfig(String projectId, String co) {
        AptPayConfig payConfig = new AptPayConfig();
        payConfig.setChannel(BillPayChannel.OFFLINE);
        payConfig.setPaymentType("微信支付");
        payConfig.setPaymentCate("180312");
        payConfig.setPaymentDetail("********");
        payConfig.setBankAccount("111");
        payConfig.setProjectId(projectId);
        payConfig.setBuildingId(null);
        payConfig.setPositionItem(null);
        payConfig.setTax(0.500000);
        payConfig.setMax(0.00);
        payConfig.setComments("null");
        payConfig.setCompanyCode(co);
        aptPayConfigRepository.save(payConfig);
    }


}