package com.kerryprops.kip.bill.service.impl;

import com.google.common.collect.Lists;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillPushStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.common.utils.DateUtils;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptJdeBillRepository;
import com.kerryprops.kip.bill.dao.JDEBillRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptJdeBill;
import com.kerryprops.kip.bill.dao.entity.JDEAptBill;
import com.kerryprops.kip.bill.dao.entity.QAptJdeBill;
import com.kerryprops.kip.bill.service.model.AptPaymentTimestamp;
import com.kerryprops.kip.bill.service.model.JdeConfirmResult;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import com.querydsl.core.types.Predicate;
import org.apache.commons.collections4.IteratorUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AptSyncPayStatusServiceImplTest {

    private final String JDE_BU = "    41225302";

    private final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    private final Long PAID_UNJDEVERIFIED_DOC = Long.valueOf(21000024);

    private final String PAID_UNJDEVERIFIED_DCT = "RD";

    private final String PAID_UNJDEVERIFIED_KCO = "41022";

    private final String PAID_UNJDEVERIFIED_SFX = "004";

    private final long PAID_UNJDEVERIFIED_UPDATED_DATE = 122059;

    private final long PAID_UNJDEVERIFIED_UPDATED_TIME = 235959;

    private final Long PAID_JDEVERIFIED_DOC = Long.valueOf(21000024);

    private final String PAID_JDEVERIFIED_DCT = "RD";

    private final String PAID_JDEVERIFIED_KCO = "41022";

    private final String PAID_JDEVERIFIED_SFX = "005";

    private final String UNPAID1_DCT = "RD";

    private final Long UNPAID1_DOC = Long.valueOf(21000024);

    private final String UNPAID1_KCO = "41022";

    private final String UNPAID1_SFX = "003";

    private final String UNPAID2_DCT = "RD";

    private final Long UNPAID2_DOC = Long.valueOf(21000024);

    private final String UNPAID2_KCO = "41022";

    private final String UNPAID2_SFX = "004";

    private final String OTHER_DCT = "RD";

    private final Long OTHER_DOC = Long.valueOf(21000024);

    private final String OTHER_KCO = "41022";

    private final String OTHER_SFX = "003";

    private final long JDE_MIN_RPUPMJ = 122059;

    private final long JDE_MIN_RPUPMT = 112451;

    @Mock
    private AptBillRepository aptBillRepository;

    @Mock
    private AptJdeBillRepository aptJdeBillRepository;

    @Mock
    private JDEBillRepository jdeBillRepository;

    @InjectMocks
    private AptSyncPayStatusServiceImpl impl;

    @Test
    void confirmAllPaidJdeItems_nullBuString() {
        JdeConfirmResult jdeConfirmResult = impl.confirmAllPaidJdeItems(null);

        assertNotNull(jdeConfirmResult);
        assertNull(jdeConfirmResult.getTimestamp());
        assertNull(jdeConfirmResult.getAptJdeBills());
    }

    @Test
    void confirmAllJdeItems_emptyJdeResult() {
        given(jdeBillRepository.queryPaymentStatus(any())).willReturn(new LinkedList<>());

        JdeConfirmResult jdeConfirmResult = impl.confirmAllPaidJdeItems(JDE_BU);

        assertNotNull(jdeConfirmResult);
        assertNull(jdeConfirmResult.getTimestamp());
        assertNull(jdeConfirmResult.getAptJdeBills());
    }

    @Test
    void confirmAllJdeItems_nullAptBill() {
        given(jdeBillRepository.queryPaymentStatus(any())).willReturn(buildJdeBillList());
        given(aptBillRepository.findAll((Predicate) any())).willReturn(null);

        JdeConfirmResult jdeConfirmResult = impl.confirmAllPaidJdeItems(JDE_BU);

        assertNotNull(jdeConfirmResult);
        assertNull(jdeConfirmResult.getTimestamp());
        assertNull(jdeConfirmResult.getAptJdeBills());
    }

    @Test
    void confirmAllJdeItems_nullAptJdeBill() {
        given(jdeBillRepository.queryPaymentStatus(any())).willReturn(buildJdeBillList());
        given(aptBillRepository.findAll((Predicate) any())).willReturn(buildAptBill());
        given(aptJdeBillRepository.findAll((Predicate) any())).willReturn(null);

        JdeConfirmResult jdeConfirmResult = impl.confirmAllPaidJdeItems(JDE_BU);

        assertNotNull(jdeConfirmResult);
        assertNull(jdeConfirmResult.getTimestamp());
        assertNull(jdeConfirmResult.getAptJdeBills());
    }

    @Test
    void confirmAllJdeItems_noIntersection() {
        given(jdeBillRepository.queryPaymentStatus(any())).willReturn(buildJdeBillList());
        given(aptBillRepository.findAll((Predicate) any())).willReturn(buildAptBill());
        given(aptJdeBillRepository.findAll((Predicate) any())).willReturn(buildAptJdeBill());

        JdeConfirmResult jdeConfirmResult = impl.confirmAllPaidJdeItems(JDE_BU);

        assertEquals(jdeConfirmResult.getAptJdeBills().size(), 0);
        assertEquals(jdeConfirmResult.getTimestamp().getDate(), JDE_MIN_RPUPMJ);
        assertEquals(jdeConfirmResult.getTimestamp().getTime(), JDE_MIN_RPUPMT);
    }

    @Test
    void confirmAllJdeItems_paid_unJdeVerified() {
        //准备测试数据
        List<JDEAptBill> jdeBills = buildJdeBillList();
        jdeBills.add(JDEAptBill.builder()
                .documentType(PAID_UNJDEVERIFIED_DCT).documentCode(PAID_UNJDEVERIFIED_DOC)
                .companyCode(PAID_UNJDEVERIFIED_KCO).paymentSfx(PAID_UNJDEVERIFIED_SFX)
                .paymentStatus("P").unpaidAmount(0)
                .updatedDate(PAID_UNJDEVERIFIED_UPDATED_DATE)
                .updatedTime(PAID_UNJDEVERIFIED_UPDATED_TIME)
                .build());

        given(jdeBillRepository.queryPaymentStatus(any())).willReturn(jdeBills);
        given(aptBillRepository.findAll((Predicate) any())).willReturn(buildAptBill());
        given(aptJdeBillRepository.findAll((Predicate) any())).willReturn(buildAptJdeBill());

        //调用测试函数
        JdeConfirmResult jdeConfirmResult = impl.confirmAllPaidJdeItems(JDE_BU);

        //验证功能
        assertEquals(jdeConfirmResult.getAptJdeBills().size(), 1);
        assertEquals(jdeConfirmResult.getTimestamp().getDate(), PAID_UNJDEVERIFIED_UPDATED_DATE);
        assertEquals(jdeConfirmResult.getTimestamp().getTime(), PAID_UNJDEVERIFIED_UPDATED_TIME);
    }

    @Test
    void confirmAllJdeItems_paid_jdeVerifiedAlready() {
        //准备测试数据
        given(jdeBillRepository.queryPaymentStatus(any())).willReturn(buildJdeBillList());
        given(aptBillRepository.findAll((Predicate) any())).willReturn(buildAptBill());
        given(aptJdeBillRepository.findAll((Predicate) any())).willReturn(buildAptJdeBill());

        //调用测试函数
        JdeConfirmResult jdeConfirmResult = impl.confirmAllPaidJdeItems(JDE_BU);

        //验证功能
        assertEquals(jdeConfirmResult.getAptJdeBills().size(), 0);
        assertEquals(jdeConfirmResult.getTimestamp().getDate(), JDE_MIN_RPUPMJ);
        assertEquals(jdeConfirmResult.getTimestamp().getTime(), JDE_MIN_RPUPMT);
    }

    @Test
    void confirmAllJdeItems_unpaid_changed() {
        //准备测试数据
        List<JDEAptBill> jdeBills = buildJdeBillList();
        jdeBills.add(JDEAptBill.builder()
                .documentType(UNPAID1_DCT).documentCode(UNPAID1_DOC).companyCode(UNPAID1_KCO).paymentSfx(UNPAID1_SFX)
                .paymentStatus("A").unpaidAmount(10).updatedDate(122062).updatedTime(155911).build());
        jdeBills.add(JDEAptBill.builder()
                .documentType(UNPAID2_DCT).documentCode(UNPAID2_DOC).companyCode(UNPAID2_KCO).paymentSfx(UNPAID2_SFX)
                .paymentStatus("A").unpaidAmount(20).updatedDate(122059).updatedTime(120803).build());

        given(jdeBillRepository.queryPaymentStatus(any())).willReturn(jdeBills);
        given(aptBillRepository.findAll((Predicate) any())).willReturn(buildAptBill());
        given(aptJdeBillRepository.findAll((Predicate) any())).willReturn(buildAptJdeBill());

        //调用测试函数
        JdeConfirmResult jdeConfirmResult = impl.confirmAllPaidJdeItems(JDE_BU);

        //验证功能
        List<AptJdeBill> aptJdeBillList = jdeConfirmResult.getAptJdeBills();
        assertEquals(aptJdeBillList.size(), 2);
        AptJdeBill aptJdeBill1 = "003".equalsIgnoreCase(aptJdeBillList.get(0).getRdSfx()) ? aptJdeBillList.get(0) : aptJdeBillList.get(1);
        AptJdeBill aptJdeBill2 = "004".equalsIgnoreCase(aptJdeBillList.get(0).getRdSfx()) ? aptJdeBillList.get(0) : aptJdeBillList.get(1);
        assertEquals(aptJdeBill1.getRdAg().doubleValue(), 10d, 0.0000001);
        assertEquals(aptJdeBill2.getRdAg().doubleValue(), 20d, 0.0000001);

        assertEquals(jdeConfirmResult.getTimestamp().getDate(), 122062);
        assertEquals(jdeConfirmResult.getTimestamp().getTime(), 155911);
    }

    @Test
    void confirmAllJdeItems_unpaid_unchanged() {
        //准备测试数据
        Iterable<AptJdeBill> aptJdeBillIterable = buildAptJdeBill();
        List<AptJdeBill> aptJdeBills = IteratorUtils.toList(aptJdeBillIterable.iterator());
        List<AptJdeBill> aptJdeBillList = aptJdeBills.stream().filter(
                aptJdeBill -> aptJdeBill.getRdDct().equalsIgnoreCase(UNPAID1_DCT)
                        && aptJdeBill.getRdDoc().longValue() == UNPAID1_DOC.longValue()
                        && aptJdeBill.getRdKco().equalsIgnoreCase(UNPAID1_KCO)
                        && aptJdeBill.getRdSfx().equalsIgnoreCase(UNPAID1_SFX)
        ).collect(Collectors.toList());
        AptJdeBill aptJdeBill = aptJdeBillList.get(0);

        List<JDEAptBill> jdeBills = buildJdeBillList();
        jdeBills.add(JDEAptBill.builder()
                .documentType(UNPAID1_DCT).documentCode(UNPAID1_DOC).companyCode(UNPAID1_KCO).paymentSfx(UNPAID1_SFX)
                .paymentStatus("A").unpaidAmount(10).updatedDate(aptJdeBill.getRdUpmj())
                .updatedTime(Long.parseLong(aptJdeBill.getRdUpmt()))
                .build());

        given(jdeBillRepository.queryPaymentStatus(any())).willReturn(jdeBills);
        given(aptBillRepository.findAll((Predicate) any())).willReturn(buildAptBill());
        given(aptJdeBillRepository.findAll((Predicate) any())).willReturn(aptJdeBillIterable);

        //调用测试函数
        JdeConfirmResult jdeConfirmResult = impl.confirmAllPaidJdeItems(JDE_BU);

        //验证功能
        List<AptJdeBill> jdeBillList = jdeConfirmResult.getAptJdeBills();
        assertEquals(jdeBillList.size(), 0);

        assertEquals(jdeConfirmResult.getTimestamp().getDate(), JDE_MIN_RPUPMJ);
        assertEquals(jdeConfirmResult.getTimestamp().getTime(), JDE_MIN_RPUPMT);
    }

    @Test
    void confirmAllJdeItems_other() {
        //准备测试数据
        List<JDEAptBill> jdeBills = buildJdeBillList();
        jdeBills.add(JDEAptBill.builder()
                .documentType(OTHER_DCT).documentCode(OTHER_DOC).companyCode(OTHER_KCO)
                .paymentSfx(OTHER_SFX).paymentStatus("%").unpaidAmount(10).updatedDate(122062).updatedTime(155911)
                .build());

        given(jdeBillRepository.queryPaymentStatus(any())).willReturn(jdeBills);
        given(aptBillRepository.findAll((Predicate) any())).willReturn(buildAptBill());
        given(aptJdeBillRepository.findAll((Predicate) any())).willReturn(buildAptJdeBill());

        //调用测试函数
        JdeConfirmResult jdeConfirmResult = impl.confirmAllPaidJdeItems(JDE_BU);

        //验证功能
        assertEquals(jdeConfirmResult.getAptJdeBills().size(), 0);
        assertEquals(jdeConfirmResult.getTimestamp().getDate(), JDE_MIN_RPUPMJ);
        assertEquals(jdeConfirmResult.getTimestamp().getTime(), JDE_MIN_RPUPMT);
    }

    @Test
    void confirmIncrementalJdeItems_nullTimestamp() {
        JdeConfirmResult jdeConfirmResult = impl.confirmIncrementalJdeItems(JDE_BU, null);

        assertNull(jdeConfirmResult.getAptJdeBills());
        assertNull(jdeConfirmResult.getTimestamp());
    }

    @Test
    void confirmIncrementalJdeItems_emptyJdePayStatus() {
        //准备测试数据
        given(jdeBillRepository.queryPaymentStatus(any())).willReturn(null);
        given(aptBillRepository.findAll((Predicate) any())).willReturn(null);

        //调用测试函数
        AptPaymentTimestamp timestamp = new AptPaymentTimestamp(122059, 112451);
        JdeConfirmResult jdeConfirmResult = impl.confirmIncrementalJdeItems(JDE_BU, timestamp);

        //验证功能
        assertNull(jdeConfirmResult.getAptJdeBills());
        assertNull(jdeConfirmResult.getTimestamp());
    }

    @Test
    void confirmIncrementalJdeItems_containPayingOrPaidOnlineBill() {
        //准备测试数据
        List<JDEAptBill> jdeBills = new LinkedList<>();
        jdeBills.add(JDEAptBill.builder()
                .documentType("RD").documentCode(Long.valueOf(21000024)).companyCode("41022")
                .paymentSfx("003").paymentStatus("A").unpaidAmount(10).updatedDate(122062).updatedTime(155911)
                .build());

        given(jdeBillRepository.queryPaymentStatus(any())).willReturn(jdeBills);
        given(aptBillRepository.findAll((Predicate) any())).willReturn(buildAptBill());
        given(aptJdeBillRepository.findAll((Predicate) any())).willReturn(buildAptJdeBill());

        //调用测试函数
        AptPaymentTimestamp timestamp = new AptPaymentTimestamp(122059, 112451);
        JdeConfirmResult jdeConfirmResult = impl.confirmIncrementalJdeItems(JDE_BU, timestamp);

        //验证功能
        assertEquals(jdeConfirmResult.getAptJdeBills().size(), 0);
        assertEquals(jdeConfirmResult.getTimestamp().getDate(), timestamp.getDate());
        assertEquals(jdeConfirmResult.getTimestamp().getTime(), timestamp.getTime());
    }

    @Test
    void confirmIncrementalJdeItems_noIntersection() {
        //准备测试数据
        given(jdeBillRepository.queryPaymentStatus(any())).willReturn(buildJdeBillList());
        given(aptBillRepository.findAll((Predicate) any())).willReturn(null);
        given(aptJdeBillRepository.findAll((Predicate) any())).willReturn(null);

        //调用测试函数
        AptPaymentTimestamp timestamp = new AptPaymentTimestamp(122059, 112451);
        JdeConfirmResult jdeConfirmResult = impl.confirmIncrementalJdeItems(JDE_BU, timestamp);

        //验证功能
        assertEquals(jdeConfirmResult.getAptJdeBills().size(), 0);
        assertEquals(jdeConfirmResult.getTimestamp().getDate(), timestamp.getDate());
        assertEquals(jdeConfirmResult.getTimestamp().getTime(), timestamp.getTime());
    }

    @Test
    void confirmIncrementalJdeItems_paid_unJdeVerified() {
        //准备测试数据
        List<JDEAptBill> jdeBills = buildJdeBillList();
        jdeBills.add(JDEAptBill.builder()
                .documentType(PAID_UNJDEVERIFIED_DCT).documentCode(PAID_UNJDEVERIFIED_DOC)
                .companyCode(PAID_UNJDEVERIFIED_KCO).paymentSfx(PAID_UNJDEVERIFIED_SFX)
                .paymentStatus("P").unpaidAmount(0)
                .updatedDate(PAID_UNJDEVERIFIED_UPDATED_DATE)
                .updatedTime(PAID_UNJDEVERIFIED_UPDATED_TIME)
                .build());

        Iterable<AptJdeBill> aptJdeBillIterable = buildAptJdeBill();
        List<AptJdeBill> aptJdeBillList = IteratorUtils.toList(aptJdeBillIterable.iterator());
        Iterable<AptJdeBill> aptJdeBills = aptJdeBillList.stream().filter(
                aptJdeBill -> aptJdeBill.getRdDct().equalsIgnoreCase(PAID_UNJDEVERIFIED_DCT)
                        && aptJdeBill.getRdDoc().longValue() == PAID_UNJDEVERIFIED_DOC.longValue()
                        && aptJdeBill.getRdKco().equalsIgnoreCase(PAID_UNJDEVERIFIED_KCO)
                        && aptJdeBill.getRdSfx().equalsIgnoreCase(PAID_UNJDEVERIFIED_SFX)
        ).collect(Collectors.toList());
        doReturn(aptJdeBills).when(aptJdeBillRepository)
                .findAll(QAptJdeBill.aptJdeBill.rdDct.eq(PAID_UNJDEVERIFIED_DCT)
                        .and(QAptJdeBill.aptJdeBill.rdDoc.eq(PAID_UNJDEVERIFIED_DOC))
                        .and(QAptJdeBill.aptJdeBill.rdKco.eq(PAID_UNJDEVERIFIED_KCO))
                        .and(QAptJdeBill.aptJdeBill.rdSfx.eq(PAID_UNJDEVERIFIED_SFX)));
        mockReturnValue_confirmIncrementalJdeItems(jdeBills);

        //调用测试函数
        AptPaymentTimestamp timestamp = new AptPaymentTimestamp(122059, 112451);
        JdeConfirmResult jdeConfirmResult = impl.confirmIncrementalJdeItems(JDE_BU, timestamp);

        //验证功能
        assertEquals(jdeConfirmResult.getAptJdeBills().size(), 1);
        assertEquals(jdeConfirmResult.getTimestamp().getDate(), PAID_UNJDEVERIFIED_UPDATED_DATE);
        assertEquals(jdeConfirmResult.getTimestamp().getTime(), PAID_UNJDEVERIFIED_UPDATED_TIME);
    }

    @Test
    void confirmIncrementalJdeItems_paid_JdeVerifiedAlready() {
        //准备测试数据
        List<JDEAptBill> jdeBills = buildJdeBillList();
        jdeBills.add(JDEAptBill.builder()
                .documentType(PAID_JDEVERIFIED_DCT).documentCode(PAID_JDEVERIFIED_DOC)
                .companyCode(PAID_JDEVERIFIED_KCO).paymentSfx(PAID_JDEVERIFIED_SFX)
                .paymentStatus("P").unpaidAmount(0).updatedDate(122066).updatedTime(164354)
                .build());

        Iterable<AptJdeBill> aptJdeBillIterable = buildAptJdeBill();
        List<AptJdeBill> aptJdeBillList = IteratorUtils.toList(aptJdeBillIterable.iterator());
        Iterable<AptJdeBill> aptJdeBills = aptJdeBillList.stream().filter(
                aptJdeBill -> aptJdeBill.getRdDct().equalsIgnoreCase(PAID_JDEVERIFIED_DCT)
                        && aptJdeBill.getRdDoc().longValue() == PAID_JDEVERIFIED_DOC.longValue()
                        && aptJdeBill.getRdKco().equalsIgnoreCase(PAID_JDEVERIFIED_KCO)
                        && aptJdeBill.getRdSfx().equalsIgnoreCase(PAID_JDEVERIFIED_SFX)
        ).collect(Collectors.toList());
        doReturn(aptJdeBills).when(aptJdeBillRepository)
                .findAll(QAptJdeBill.aptJdeBill.rdDct.eq(PAID_JDEVERIFIED_DCT)
                        .and(QAptJdeBill.aptJdeBill.rdDoc.eq(PAID_JDEVERIFIED_DOC))
                        .and(QAptJdeBill.aptJdeBill.rdKco.eq(PAID_JDEVERIFIED_KCO))
                        .and(QAptJdeBill.aptJdeBill.rdSfx.eq(PAID_JDEVERIFIED_SFX)));
        mockReturnValue_confirmIncrementalJdeItems(jdeBills);

        //调用测试函数
        AptPaymentTimestamp timestamp = new AptPaymentTimestamp(122059, 112451);
        JdeConfirmResult jdeConfirmResult = impl.confirmIncrementalJdeItems(JDE_BU, timestamp);

        //验证功能
        assertEquals(jdeConfirmResult.getAptJdeBills().size(), 0);
        assertEquals(jdeConfirmResult.getTimestamp().getDate(), timestamp.getDate());
        assertEquals(jdeConfirmResult.getTimestamp().getTime(), timestamp.getTime());
    }

    @Test
    void confirmIncrementalJdeItems_unpaid_changed() {
        //准备测试数据
        List<JDEAptBill> jdeBills = buildJdeBillList();
        jdeBills.add(JDEAptBill.builder()
                .documentType(UNPAID1_DCT).documentCode(UNPAID1_DOC).companyCode(UNPAID1_KCO).paymentSfx(UNPAID1_SFX)
                .paymentStatus("A").unpaidAmount(10).updatedDate(122062).updatedTime(155911)
                .build());
        jdeBills.add(JDEAptBill.builder()
                .documentType(UNPAID2_DCT).documentCode(UNPAID2_DOC).companyCode(UNPAID2_KCO).paymentSfx(UNPAID2_SFX)
                .paymentStatus("A").unpaidAmount(20).updatedDate(122059).updatedTime(120803)
                .build());

        Iterable<AptJdeBill> aptJdeBillIterable = buildAptJdeBill();
        List<AptJdeBill> aptJdeBillList = IteratorUtils.toList(aptJdeBillIterable.iterator());
        Iterable<AptJdeBill> aptJdeBills1 = aptJdeBillList.stream().filter(
                aptJdeBill -> aptJdeBill.getRdDct().equalsIgnoreCase(UNPAID1_DCT)
                        && aptJdeBill.getRdDoc().longValue() == UNPAID1_DOC.longValue()
                        && aptJdeBill.getRdKco().equalsIgnoreCase(UNPAID1_KCO)
                        && (aptJdeBill.getRdSfx().equalsIgnoreCase(UNPAID1_SFX))
        ).collect(Collectors.toList());
        doReturn(aptJdeBills1).when(aptJdeBillRepository)
                .findAll(QAptJdeBill.aptJdeBill.rdDct.eq(UNPAID1_DCT)
                        .and(QAptJdeBill.aptJdeBill.rdDoc.eq(UNPAID1_DOC))
                        .and(QAptJdeBill.aptJdeBill.rdKco.eq(UNPAID1_KCO))
                        .and(QAptJdeBill.aptJdeBill.rdSfx.eq(UNPAID1_SFX)));

        Iterable<AptJdeBill> aptJdeBills2 = aptJdeBillList.stream().filter(
                aptJdeBill -> aptJdeBill.getRdDct().equalsIgnoreCase(UNPAID2_DCT)
                        && aptJdeBill.getRdDoc().longValue() == UNPAID2_DOC.longValue()
                        && aptJdeBill.getRdKco().equalsIgnoreCase(UNPAID2_KCO)
                        && (aptJdeBill.getRdSfx().equalsIgnoreCase(UNPAID2_SFX))
        ).collect(Collectors.toList());
        doReturn(aptJdeBills2).when(aptJdeBillRepository)
                .findAll(QAptJdeBill.aptJdeBill.rdDct.eq(UNPAID2_DCT)
                        .and(QAptJdeBill.aptJdeBill.rdDoc.eq(UNPAID2_DOC))
                        .and(QAptJdeBill.aptJdeBill.rdKco.eq(UNPAID2_KCO))
                        .and(QAptJdeBill.aptJdeBill.rdSfx.eq(UNPAID2_SFX)));
        mockReturnValue_confirmIncrementalJdeItems(jdeBills);

        //调用测试函数
        AptPaymentTimestamp timestamp = new AptPaymentTimestamp(122059, 112451);
        JdeConfirmResult jdeConfirmResult = impl.confirmIncrementalJdeItems(JDE_BU, timestamp);

        //验证功能
        List<AptJdeBill> jdeBillList = jdeConfirmResult.getAptJdeBills();
        assertEquals(jdeBillList.size(), 2);
        AptJdeBill aptJdeBill1 = "003".equalsIgnoreCase(jdeBillList.get(0).getRdSfx()) ? jdeBillList.get(0) : jdeBillList.get(1);
        AptJdeBill aptJdeBill2 = "004".equalsIgnoreCase(jdeBillList.get(0).getRdSfx()) ? jdeBillList.get(0) : jdeBillList.get(1);
        assertEquals(aptJdeBill1.getRdAg().doubleValue(), 10d, 0.0000001);
        assertEquals(aptJdeBill2.getRdAg().doubleValue(), 20d, 0.0000001);

        assertEquals(jdeConfirmResult.getTimestamp().getDate(), 122062);
        assertEquals(jdeConfirmResult.getTimestamp().getTime(), 155911);
    }

    @Test
    void confirmIncrementalJdeItems_unpaid_unchanged() {
        //准备测试数据
        Iterable<AptJdeBill> aptJdeBillIterable = buildAptJdeBill();
        List<AptJdeBill> aptJdeBillList = IteratorUtils.toList(aptJdeBillIterable.iterator());
        Iterable<AptJdeBill> aptJdeBills = aptJdeBillList.stream().filter(
                aptJdeBill -> aptJdeBill.getRdDct().equalsIgnoreCase(UNPAID1_DCT)
                        && aptJdeBill.getRdDoc().longValue() == UNPAID1_DOC.longValue()
                        && aptJdeBill.getRdKco().equalsIgnoreCase(UNPAID1_KCO)
                        && (aptJdeBill.getRdSfx().equalsIgnoreCase(UNPAID1_SFX))
        ).collect(Collectors.toList());
        doReturn(aptJdeBills).when(aptJdeBillRepository)
                .findAll(QAptJdeBill.aptJdeBill.rdDct.eq(UNPAID1_DCT)
                        .and(QAptJdeBill.aptJdeBill.rdDoc.eq(UNPAID1_DOC))
                        .and(QAptJdeBill.aptJdeBill.rdKco.eq(UNPAID1_KCO))
                        .and(QAptJdeBill.aptJdeBill.rdSfx.eq(UNPAID1_SFX)));

        AptJdeBill aptJdeBill = (AptJdeBill) IteratorUtils.toList(aptJdeBills.iterator()).get(0);
        List<JDEAptBill> jdeBills = buildJdeBillList();
        jdeBills.add(JDEAptBill.builder()
                .documentType(UNPAID1_DCT).documentCode(UNPAID1_DOC).companyCode(UNPAID1_KCO).paymentSfx(UNPAID1_SFX)
                .paymentStatus("A").unpaidAmount(10).updatedDate(aptJdeBill.getRdUpmj())
                .updatedTime(Long.parseLong(aptJdeBill.getRdUpmt()))
                .build());
        mockReturnValue_confirmIncrementalJdeItems(jdeBills);

        //调用测试函数
        AptPaymentTimestamp timestamp = new AptPaymentTimestamp(122059, 112451);
        JdeConfirmResult jdeConfirmResult = impl.confirmIncrementalJdeItems(JDE_BU, timestamp);

        //验证功能
        List<AptJdeBill> jdeBillList = jdeConfirmResult.getAptJdeBills();
        assertEquals(jdeBillList.size(), 0);
        assertEquals(jdeConfirmResult.getTimestamp().getDate(), JDE_MIN_RPUPMJ);
        assertEquals(jdeConfirmResult.getTimestamp().getTime(), JDE_MIN_RPUPMT);
    }

    @Test
    void confirmIncrementalJdeItems_other() {
        //准备测试数据
        List<JDEAptBill> jdeBills = buildJdeBillList();
        jdeBills.add(JDEAptBill.builder()
                .documentType(OTHER_DCT).documentCode(OTHER_DOC).companyCode(OTHER_KCO).paymentSfx(OTHER_SFX)
                .paymentStatus("%").unpaidAmount(10).updatedDate(122062).updatedTime(155911)
                .build());

        Iterable<AptJdeBill> aptJdeBillIterable = buildAptJdeBill();
        List<AptJdeBill> aptJdeBillList = IteratorUtils.toList(aptJdeBillIterable.iterator());
        Iterable<AptJdeBill> aptJdeBills1 = aptJdeBillList.stream().filter(
                aptJdeBill -> aptJdeBill.getRdDct().equalsIgnoreCase(OTHER_DCT)
                        && aptJdeBill.getRdDoc().longValue() == OTHER_DOC.longValue()
                        && aptJdeBill.getRdKco().equalsIgnoreCase(OTHER_KCO)
                        && (aptJdeBill.getRdSfx().equalsIgnoreCase(OTHER_SFX))
        ).collect(Collectors.toList());
        doReturn(aptJdeBills1).when(aptJdeBillRepository)
                .findAll(QAptJdeBill.aptJdeBill.rdDct.eq(OTHER_DCT)
                        .and(QAptJdeBill.aptJdeBill.rdDoc.eq(OTHER_DOC))
                        .and(QAptJdeBill.aptJdeBill.rdKco.eq(OTHER_KCO))
                        .and(QAptJdeBill.aptJdeBill.rdSfx.eq(OTHER_SFX)));
        mockReturnValue_confirmIncrementalJdeItems(jdeBills);

        //调用测试函数
        AptPaymentTimestamp timestamp = new AptPaymentTimestamp(122059, 112451);
        JdeConfirmResult jdeConfirmResult = impl.confirmIncrementalJdeItems(JDE_BU, timestamp);

        //验证功能
        assertEquals(jdeConfirmResult.getAptJdeBills().size(), 0);
        assertEquals(jdeConfirmResult.getTimestamp().getDate(), timestamp.getDate());
        assertEquals(jdeConfirmResult.getTimestamp().getTime(), timestamp.getTime());
    }

    @Test
    void confirmPayingBillTimestamp_returnNullWhenQueryAptBill() {
        given(aptBillRepository.findAll((Predicate) any())).willReturn(null);

        AptPaymentTimestamp timestamp = impl.confirmPayingBillTimestamp();

        assertNull(timestamp);
    }

    @Test
    void confirmPayingBillTimestamp_returnNullWhenQueryAptJdeBill() {
        given(aptBillRepository.findAll((Predicate) any())).willReturn(null);

        AptPaymentTimestamp timestamp = impl.confirmPayingBillTimestamp();

        assertNull(timestamp);
    }

    @Test
    void confirmPayingBillTimestamp_emptyOracleService() {
        given(aptBillRepository.findAll((Predicate) any())).willReturn(buildAptBill());
        given(aptJdeBillRepository.findAll((Predicate) any())).willReturn(buildAptJdeBill());

        AptPaymentTimestamp timestamp = impl.confirmPayingBillTimestamp();

        assertNull(timestamp);
    }

    @Test
    void confirmPayingBillTimestamp_1Bill() {
        //准备测试数据
        List<AptJdeBill> aptJdeBillList = IteratorUtils.toList(buildAptJdeBill().iterator());
        AptJdeBill aptJdeBill = aptJdeBillList.get(0);
        long upmj = aptJdeBill.getRdUpmj();
        long upmt = Long.parseLong(aptJdeBill.getRdUpmt());

        List<JDEAptBill> jdeBills = new LinkedList<>();
        jdeBills.add(JDEAptBill.builder()
                .documentType(aptJdeBill.getRdDct()).documentCode(aptJdeBill.getRdDoc())
                .companyCode(aptJdeBill.getRdKco()).paymentSfx(aptJdeBill.getRdSfx())
                .paymentStatus("P").unpaidAmount(0).updatedDate(upmj).updatedTime(upmt)
                .build());

        given(aptBillRepository.findAll((Predicate) any())).willReturn(buildAptBill());
        given(aptJdeBillRepository.findAll((Predicate) any())).willReturn(Lists.newArrayList(aptJdeBill));
        given(jdeBillRepository.findAllByCombineKeys(anyLong(), anyString(), anyString(), anyString())).willReturn(jdeBills);

        //调用测试函数
        AptPaymentTimestamp timestamp = impl.confirmPayingBillTimestamp();

        //验证功能
        assertEquals(timestamp.getDate(), upmj);
        assertEquals(timestamp.getTime(), upmt);
    }

    @Test
    void confirmPayingBillTimestamp_2Bills() {
        //准备测试数据
        List<AptJdeBill> aptJdeBillList = IteratorUtils.toList(buildAptJdeBill().iterator());
        AptJdeBill aptJdeBill1 = aptJdeBillList.get(0);
        AptJdeBill aptJdeBill2 = aptJdeBillList.get(1);
        AptPaymentTimestamp timestamp1 = new AptPaymentTimestamp(
                aptJdeBill1.getRdUpmj(), Long.parseLong(aptJdeBill1.getRdUpmt()));
        AptPaymentTimestamp timestamp2 = new AptPaymentTimestamp(
                aptJdeBill2.getRdUpmj(), Long.parseLong(aptJdeBill2.getRdUpmt()));
        AptPaymentTimestamp minTimestamp = timestamp1.compareTo(timestamp2) > 0 ? timestamp2 : timestamp1;

        List<JDEAptBill> jdeBills = new LinkedList<>();
        jdeBills.add(JDEAptBill.builder()
                .documentType(aptJdeBill1.getRdDct()).documentCode(aptJdeBill1.getRdDoc())
                .companyCode(aptJdeBill1.getRdKco()).paymentSfx(aptJdeBill1.getRdSfx())
                .paymentStatus("P").unpaidAmount(0).updatedDate(timestamp1.getDate()).updatedTime(timestamp1.getTime())
                .build());
        jdeBills.add(JDEAptBill.builder()
                .documentType(aptJdeBill2.getRdDct()).documentCode(aptJdeBill2.getRdDoc())
                .companyCode(aptJdeBill2.getRdKco()).paymentSfx(aptJdeBill2.getRdSfx())
                .paymentStatus("A").unpaidAmount(10).updatedDate(timestamp2.getDate()).updatedTime(timestamp2.getTime())
                .build());


        given(aptBillRepository.findAll((Predicate) any())).willReturn(buildAptBill());
        given(aptJdeBillRepository.findAll((Predicate) any())).willReturn(Lists.newArrayList(aptJdeBill1, aptJdeBill2));
        given(jdeBillRepository.findAllByCombineKeys(anyLong(), anyString(), anyString(), anyString())).willReturn(jdeBills);

        //调用测试函数
        AptPaymentTimestamp timestamp = impl.confirmPayingBillTimestamp();

        //验证功能
        assertEquals(timestamp.getDate(), minTimestamp.getDate());
        assertEquals(timestamp.getTime(), minTimestamp.getTime());
    }

    @DisplayName("正常场景：查询所有未支付的JDE账单成功")
    @Test
    void testConfirmAllUnpaidJdeItems_success() {
        // Arrange
        String buString = "41225302";

        // Act and Assert
        assertDoesNotThrow(() -> impl.confirmAllUnpaidJdeItems(buString));
    }

    @BeforeEach
    void mockFeignClient() throws NoSuchFieldException, IllegalAccessException {
        MockitoAnnotations.initMocks(this);
    }

    private void mockReturnValue_confirmIncrementalJdeItems(List<JDEAptBill> jdeBills) {
        given(jdeBillRepository.queryPaymentStatus(any())).willReturn(jdeBills);

        JDEAptBill jdeBill1 = jdeBills.get(0);
        doReturn(null).when(aptJdeBillRepository)
                .findAll(QAptJdeBill.aptJdeBill.rdDct.eq(jdeBill1.getDocumentType())
                        .and(QAptJdeBill.aptJdeBill.rdDoc.eq(jdeBill1.getDocumentCode()))
                        .and(QAptJdeBill.aptJdeBill.rdKco.eq(jdeBill1.getCompanyCode()))
                        .and(QAptJdeBill.aptJdeBill.rdSfx.eq(jdeBill1.getPaymentSfx())));

        JDEAptBill jdeBill2 = jdeBills.get(1);
        doReturn(null).when(aptJdeBillRepository)
                .findAll(QAptJdeBill.aptJdeBill.rdDct.eq(jdeBill2.getDocumentType())
                        .and(QAptJdeBill.aptJdeBill.rdDoc.eq(jdeBill2.getDocumentCode()))
                        .and(QAptJdeBill.aptJdeBill.rdKco.eq(jdeBill2.getCompanyCode()))
                        .and(QAptJdeBill.aptJdeBill.rdSfx.eq(jdeBill2.getPaymentSfx())));
    }

    private Iterable<AptBill> buildAptBill() {
        List<AptBill> aptBills = new LinkedList<>();
        long[] ids = {3254, 3257, 3263, 3274};
        String[] billNos = {"B20229937793173040405", "B20226969283173040961", "B2022468928317304238", "B202246892831730423833"};
        String[] categories = {"日常专项维修资金", "管理费", "水费", "电费"};
        String[] beginDates = {"2021-01-01 07:30:40", "2021-01-01 07:30:41", "2021-01-01 07:30:42", "2021-01-01 07:30:42"};
        String[] endDates = {"2021-01-31 07:30:40", "2021-01-31 07:30:41", "2021-01-31 07:30:42", "2021-01-31 07:30:43"};
        double[] amtList = {17.68, 1024.53, 0.02, 0.01};
        BillStatus[] billStatusList = {BillStatus.TO_BE_PAID, BillStatus.JDE_VERIFIED, BillStatus.ONLINE_PAID, BillStatus.TO_BE_PAID};
        BillPaymentStatus[] billPaymentStatusList = {
                BillPaymentStatus.TO_BE_PAID, BillPaymentStatus.PAID, BillPaymentStatus.PAID, BillPaymentStatus.PAYING};
        String[] operationTime = {"2022-03-01 07:30:41", "2022-03-01 07:30:42", "2022-03-01 07:30:43", "2022-03-01 07:30:44"};
        String[] rdGlcList = {"Z16M", "M01M", "U01M", "U02M"};

        for (int i = 0; i < billNos.length; i++) {
            AptBill bill = AptBill.builder()
                    .billNo(billNos[i]).category(categories[i])
                    .bu("41225302").unit("2B").an8("20224142").alph("崔岚").doco("279258")
                    .beginDate(DateUtils.parseDate(YYYY_MM_DD_HH_MM_SS, beginDates[i]))
                    .endDate(DateUtils.parseDate(YYYY_MM_DD_HH_MM_SS, endDates[i]))
                    .pushStatus(BillPushStatus.PUSHED)
                    .projectId("192").buildingId("QHKC-A2").floorId("8aaa8a0b7bc94053017bc949bc1300e4")
                    .roomId("8aaa84897bc9925e017bc9b1307100df")
                    .positionItem(PositionItemResponse.builder().projectName("前海嘉里中心").buildingName("A2").floorName("2").roomName("B").build())
                    .year(2021).month(1).billMonth(202101)
                    .amt(amtList[i]).status(billStatusList[i]).paymentStatus(billPaymentStatusList[i]).rdGlc(rdGlcList[i])
                    .build();
            bill.setId(ids[i]);
            bill.setCreateTime(DateUtils.parseDate(YYYY_MM_DD_HH_MM_SS, operationTime[i]));
            bill.setUpdateTime(DateUtils.parseDate(YYYY_MM_DD_HH_MM_SS, operationTime[i]));

            aptBills.add(bill);
        }

        return (Iterable<AptBill>) aptBills;
    }

    private Iterable<AptJdeBill> buildAptJdeBill() {
        List<AptJdeBill> aptJdeBills = new LinkedList<>();
        String[] rdSfxes = {"003", "004", "005", "006", "001", "002", "007", "008"};
        String[] rdGlcs = {"Z16M", "Z16M", "M01M", "M01M", "U01M", "U01M", "U02M", "U02M"};
        String[] rdDl01s = {"日常专项维修资金", "日常专项维修资金", "管理费", "管理费", "水费", "水费", "电费", "电费"};
        String[] billNos = {"B20229937793173040405", "B20229937793173040405", "B20226969283173040961", "B20226969283173040961",
                "B2022468928317304238", "B2022468928317304238", "B202246892831730423833", "B202246892831730423833"};
        String[] rdUpmts = {"120802", "120802", "120930", "120930", "120802", "120802", "120802", "120802"};
        int[] jdeVerifications = {0, 0, 1, 1, 0, 0, 0, 0};

        for (int i = 0; i < 8; i++) {
            AptJdeBill aptJdeBill = AptJdeBill.builder()
                    .rdEdbt("999").billNumber(billNos[i]).rdAg(100d)
                    .jdeVerification(jdeVerifications[i])
                    .jdeVerificationTime(DateUtils.parseDate(YYYY_MM_DD_HH_MM_SS, "2022-05-16 12:13:14"))
                    .rdDoc(Long.valueOf(21000024)).rdDct("RD").rdKco("41022").rdSfx(rdSfxes[i]).rdGlc(rdGlcs[i])
                    .rdMcu("41225302").rdUnit("2B")
                    .rdDl01(rdDl01s[i]).rdDl02("4").rdDl03("前海物业.物业管理.住宅-A2")
                    .rdAn8("20224142").rdAlph("崔岚").rdDoco("279258")
                    .rdUds1("20210101").rdUds2("20210131").rdUds3("2022-02-28 12:08:45")
                    .rdUser("SHAYHH1").rdUpmj(Long.valueOf(122059)).rdUpmt(rdUpmts[i]).rdPid("R560311A")
                    .build();

            aptJdeBills.add(aptJdeBill);
        }

        return (Iterable<AptJdeBill>) aptJdeBills;
    }

    private List<JDEAptBill> buildJdeBillList() {
        //no intersection
        List<JDEAptBill> jdeBills = new LinkedList<>();
        jdeBills.add(JDEAptBill.builder()
                .documentType("RD").documentCode(Long.valueOf(21000031)).companyCode("41022").paymentSfx("001")
                .paymentStatus("A").unpaidAmount(1).updatedDate(122059).updatedTime(120802)
                .build());

        jdeBills.add(JDEAptBill.builder()
                .documentType("RD").documentCode(Long.valueOf(21000031)).companyCode("41022").paymentSfx("003")
                .paymentStatus("P").unpaidAmount(1).updatedDate(JDE_MIN_RPUPMJ).updatedTime(JDE_MIN_RPUPMT)
                .build());
        return jdeBills;
    }

}
