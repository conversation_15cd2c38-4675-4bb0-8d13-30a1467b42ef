package com.kerryprops.kip.bill.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.enums.EmailStatus;
import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.exceptions.AppException;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.utils.DateUtils;
import com.kerryprops.kip.bill.common.utils.jde.OracleJdbc;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.BillEmailTraceRepository;
import com.kerryprops.kip.bill.dao.BillRepository;
import com.kerryprops.kip.bill.dao.BillSendConfigAn8LinkRepository;
import com.kerryprops.kip.bill.dao.BillSendConfigRepository;
import com.kerryprops.kip.bill.dao.BizContentReadRepository;
import com.kerryprops.kip.bill.dao.EFapiaoBillInvoiceRepository;
import com.kerryprops.kip.bill.dao.MessageTemplateRepository;
import com.kerryprops.kip.bill.dao.entity.BillEmailTrace;
import com.kerryprops.kip.bill.dao.entity.BillEntity;
import com.kerryprops.kip.bill.dao.entity.BillSendConfig;
import com.kerryprops.kip.bill.dao.entity.BillSendConfigAn8Link;
import com.kerryprops.kip.bill.dao.entity.BizContentReadEntity;
import com.kerryprops.kip.bill.dao.entity.MessageTemplateEntity;
import com.kerryprops.kip.bill.dao.entity.QBillEntity;
import com.kerryprops.kip.bill.feign.clients.FileClient;
import com.kerryprops.kip.bill.feign.clients.MessageCenterClient;
import com.kerryprops.kip.bill.feign.clients.MessageClient;
import com.kerryprops.kip.bill.feign.entity.EmailReplyVo;
import com.kerryprops.kip.bill.feign.entity.EmailSendCommand;
import com.kerryprops.kip.bill.feign.entity.NotificationPushRequest;
import com.kerryprops.kip.bill.feign.entity.OssPreSignedUrlResponse;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.IBillSendConfigService;
import com.kerryprops.kip.bill.service.model.leg.Bill;
import com.kerryprops.kip.bill.service.model.leg.BillSelectVo;
import com.kerryprops.kip.bill.service.model.leg.BillUserSelectVo;
import com.kerryprops.kip.bill.service.model.s.BillSendHistoryReqDto;
import com.kerryprops.kip.bill.webservice.vo.req.BizBillReadReqVo;
import com.kerryprops.kip.bill.webservice.vo.req.EmailResultVo;
import com.kerryprops.kip.bill.webservice.vo.req.EmailStatusDto;
import com.kerryprops.kip.bill.webservice.vo.resp.BillPayer;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffBillReceiverRespVo;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.impl.JPAUpdateClause;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.sql.BatchUpdateException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.kerryprops.kip.bill.common.constants.BillConstants.MSG_SUCCESS;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomLoginUser;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomObject;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyIterable;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

/**
 * BillServiceImplTest.
 *
 * <AUTHOR> Yu 2025-02-12 10:06:26
 **/
@ExtendWith(MockitoExtension.class)
class BillServiceImplTest {

    @Mock
    FileClient fileClient;

    @Mock
    ObjectMapper objectMapper;

    @Mock
    JdbcTemplate jdbcTemplate;

    @Mock
    MessageClient messageClient;

    @Mock
    BillRepository billRepository;

    @Mock
    private JPAQueryFactory jpaQueryFactory;

    @Mock
    private MessageTemplateRepository messageTemplateRepository;

    @Mock
    MessageCenterClient messageCenterClient;

    @Mock
    BillEmailTraceRepository emailTraceRepository;

    @Mock
    BillSendConfigRepository sendConfigRepository;

    @Mock
    BillSendConfigAn8LinkRepository linkRepository;

    @Mock
    IBillSendConfigService billSendConfigService;

    @Mock
    EFapiaoBillInvoiceRepository eFapiaoBillInvoiceRepository;

    @Mock
    BizContentReadRepository bizContentReadRepository;

    @InjectMocks
    BillServiceImpl billService;

    @Test
    @DisplayName("成功发送站内信和app消息推送")
    void sendBillList_mail_and_app_push_ok() {
        var billSendConfig = randomObject(BillSendConfig.class);
        billSendConfig.setTenantManagerId("1");
        when(sendConfigRepository.queryActiveConfigsByDoco(anyString())).thenReturn(List.of(billSendConfig));
        var billSendConfigAn8Link = randomObject(BillSendConfigAn8Link.class);
        billSendConfigAn8Link.setConfigId(billSendConfig.getId());
        when(linkRepository.selectActiveAn8Links(anyList())).thenReturn(List.of(billSendConfigAn8Link));
        RespWrapVo<String> vo = new RespWrapVo<>();
        vo.setCode("000000");
        vo.setData("1");
        when(messageCenterClient.sendMessage(any())).thenReturn(vo);
        var bill = randomObject(Bill.class);
        bill.setTpAn8(billSendConfigAn8Link.getAn8());
        List<Bill> bills = List.of(bill);

        billService.sendBillList(bills, 1, "1", "1");

        verify(messageCenterClient, times(1)).sendMessage(any());
        var captor = ArgumentCaptor.forClass(NotificationPushRequest.class);
        verify(messageClient, times(1)).pushNotification(captor.capture());
        var pushRequest = captor.getValue();
        String content = bill.getTpFyr() + "年" + bill.getTpPn() + "月  " + bill.getTpDl01() + "已送达，敬请查收";
        assertThat(pushRequest.getContent()).isEqualTo(content);
    }

    @Test
    @DisplayName("发送站内信和消息推送，异常场景：接收人为空")
    void sendBillList_mail_receiverNotFoundError() {
        // Arrange
        var bill = randomObject(Bill.class);
        bill.setTpDoco(StringUtils.EMPTY);
        List<Bill> bills = List.of(bill);

        // Act
        billService.sendBillList(bills, 1, "1", "1");

        // Assert
        verify(messageCenterClient, never()).sendMessage(any());
        verify(messageClient, never()).pushNotification(any());
    }

    @Test
    @DisplayName("发送站内信和消息推送，异常场景：数据库交互报错")
    void sendBillList_mail_dataBaseOperateError() {
        // Arrange
        var bill = randomObject(Bill.class);
        bill.setTpDoco(StringUtils.EMPTY);
        List<Bill> bills = List.of(bill);

        when(billRepository.updateSendMailInfo(any(), any(), any(), any(), any())).thenThrow(new RuntimeException());

        // Act
        billService.sendBillList(bills, 1, "1", "1");

        // Assert
        verify(messageCenterClient, never()).sendMessage(any());
        verify(messageClient, never()).pushNotification(any());
    }

    @Test
    @DisplayName("发送站内信和消息推送，异常场景：接收人ID不合法")
    void sendBillList_mail_receiverUserIdInvalidError() {
        // Arrange
        var billSendConfig = randomObject(BillSendConfig.class);
        billSendConfig.setTenantManagerId(StringUtils.EMPTY);
        when(sendConfigRepository.queryActiveConfigsByDoco(anyString())).thenReturn(List.of(billSendConfig));

        var billSendConfigAn8Link = randomObject(BillSendConfigAn8Link.class);
        billSendConfigAn8Link.setConfigId(billSendConfig.getId());
        when(linkRepository.selectActiveAn8Links(anyList())).thenReturn(List.of(billSendConfigAn8Link));

        var bill = randomObject(Bill.class);
        bill.setTpAn8(billSendConfigAn8Link.getAn8());
        List<Bill> bills = List.of(bill);

        // Act
        billService.sendBillList(bills, 1, "1", "1");

        // Assert
        verify(messageCenterClient, never()).sendMessage(any());
        verify(messageClient, never()).pushNotification(any());
    }


    @Test
    @DisplayName("发送账单-邮件发送正常场景")
    void sendBillList_WhenSendType2_ShouldProcessEmailSending() {
        try (MockedStatic<BeanUtil> beanUtil = mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> userInfo = mockStatic(UserInfoUtils.class)) {

            // Arrange
            beanUtil.when(() -> BeanUtil.copy(any(), eq(StaffBillReceiverRespVo.class)))
                    .thenReturn(new StaffBillReceiverRespVo());
            beanUtil.when(() -> BeanUtil.copy(any(), eq(MessageTemplateEntity.class)))
                    .thenReturn(new MessageTemplateEntity());
            userInfo.when(UserInfoUtils::getUser)
                    .thenReturn(new LoginUser());

            // 配置测试数据
            Bill bill1 = new Bill();
            bill1.setId(1L);
            bill1.setTpDoco("DOC1");
            bill1.setTpAn8("AN81");
            bill1.setFileUrl("test-url");
            bill1.setEmailStatus(MSG_SUCCESS);

            // 配置模板查询
            var messageTemplateEntity = new MessageTemplateEntity();
            messageTemplateEntity.setForeachContent("test-html-{xxx}{xxx}{xxx}");
            when(messageTemplateRepository.findById(1L)).thenReturn(Optional.of(messageTemplateEntity));

            // 配置文件服务响应
            when(fileClient.getPreSignedUrls(anyList())).thenReturn(List.of(new OssPreSignedUrlResponse()));

            // 配置邮件发送响应
            EmailReplyVo reply = new EmailReplyVo();
            reply.setRequestId("REQ123");
            when(messageClient.sendWithReply(any())).thenReturn(
                    new RespWrapVo<>(RespCodeEnum.SUCCESS.getCode(), "success", reply));

            var billSendConfig = new BillSendConfig();
            billSendConfig.setId(1L);
            billSendConfig.setTenantManagerId("Tenant1");
            billSendConfig.setPhoneNumber("1234567890");
            billSendConfig.setEmailUsername("EmailUsername");
            billSendConfig.setEmail("Email1");
            when(sendConfigRepository.queryActiveConfigsByDoco(any())).thenReturn(List.of(billSendConfig));

            var billSendConfigAn8Link = new BillSendConfigAn8Link();
            billSendConfigAn8Link.setConfigId(billSendConfig.getId());
            billSendConfigAn8Link.setAn8(bill1.getTpAn8());
            when(linkRepository.selectActiveAn8Links(anyList())).thenReturn(List.of(billSendConfigAn8Link));

            var staffBillReceiverRespVo1 = new StaffBillReceiverRespVo();
            staffBillReceiverRespVo1.setBatchNo("123");
            var staffBillReceiverRespVo2 = new StaffBillReceiverRespVo();
            staffBillReceiverRespVo2.setBatchNo("456");
            var billEntity = new BillEntity();
            billEntity.setEmailErr(
                    JSONObject.toJSONString(List.of(staffBillReceiverRespVo1, staffBillReceiverRespVo2)));
            when(billRepository.findById(any())).thenReturn(Optional.of(billEntity));


            // Act
            billService.sendBillList(List.of(bill1), 2, "test", "nick");

            // Assert
            verify(messageClient).sendWithReply(any(EmailSendCommand.class));

        }
    }

    // 测试无邮件模板情况
    @Test
    @DisplayName("发送账单-邮件发送异常场景，模板异常")
    void sendEmail_WhenNoTemplateFound_ShouldThrowException() {
        // Act & Assert
        assertThrows(NoSuchElementException.class, () -> {
            // 通过反射调用私有方法（示例使用public方法触发）
            billService.sendBillList(Collections.singletonList(new Bill()), 2, "test", "nick");
        });
    }

    @Test
    @DisplayName("删除B端账单，正常场景：账单已发送，无法删除")
    void testDeleteBill_BillAlreadySent_ReturnsFalse() {
        try (MockedStatic<UserInfoUtils> userUtilsMock = mockStatic(UserInfoUtils.class)) {
            // Arrange
            userUtilsMock.when(UserInfoUtils::getUser)
                         .thenReturn(generateLoginUser());

            BillEntity sentBill = buildTestBill(1L, 5);
            Iterable<BillEntity> bills = List.of(sentBill);
            when(billRepository.findAll(any(Predicate.class))).thenReturn(bills);

            // Act
            Boolean result = billService.deleteBill(QBillEntity.billEntity.id.eq(1L));

            // Assert
            assertFalse(result);
            verify(billRepository, never()).save(any());
        }
    }

    @Test
    @DisplayName("删除B端账单，正常场景")
    void testDeleteBill_SuccessfulDeletion_ReturnsTrue() {
        try (MockedStatic<UserInfoUtils> userUtilsMock = mockStatic(UserInfoUtils.class)) {
            // Arrange
            userUtilsMock.when(UserInfoUtils::getUser)
                         .thenReturn(generateLoginUser());

            BillEntity validBill = buildTestBill(2L, 3);
            List<BillEntity> bills = List.of(validBill);
            when(billRepository.findAll(any(Predicate.class))).thenReturn(bills);
            when(billRepository.save(any())).thenReturn(validBill);

            // Act
            Boolean result = billService.deleteBill(QBillEntity.billEntity.id.eq(2L));

            // Assert
            assertTrue(result);
            ArgumentCaptor<BillEntity> captor = ArgumentCaptor.forClass(BillEntity.class);
            verify(billRepository).save(captor.capture());

            BillEntity savedBill = captor.getValue();
            assertEquals("1", savedBill.getDelFlag());
        }
    }

    @Test
    @DisplayName("根据ID查询B端账单，异常场景，未查询到")
    void testSelectBillListByIds_WhenNoDataFound_ReturnNull() {
        try (MockedStatic<BeanUtil> beanUtilMock = mockStatic(BeanUtil.class)) {
            // Arrange
            List<Long> ids = List.of(1L, 2L);
            when(billRepository.findAllById(anyIterable())).thenReturn(Collections.emptyList());

            // Act
            List<Bill> result = billService.selelctBillListByIds(ids);

            // Assert
            assertNull(result);
            beanUtilMock.verifyNoInteractions();
        }
    }

    @Test
    @DisplayName("根据ID查询B端账单，正常场景")
    void testSelectBillListByIds_WhenDataExists_ReturnConvertedList() {
        try (MockedStatic<BeanUtil> beanUtilMock = mockStatic(BeanUtil.class)) {
            // Arrange
            List<Long> ids = List.of(1L, 2L);

            // 设置静态方法mock
            beanUtilMock.when(() -> BeanUtil.copy(any(BillEntity.class), eq(Bill.class)))
                        .thenReturn(new Bill());

            when(billRepository.findAllById(anyIterable())).thenReturn(List.of(new BillEntity(), new BillEntity()));

            // Act
            List<Bill> result = billService.selelctBillListByIds(ids);

            // Assert
            assertNotNull(result);
            assertEquals(2, result.size());
        }
    }

    @Test
    @DisplayName("邮件状态回调，正常场景，处于发送中，返回")
    void testEmailCallback_IntermediateStatus_ReturnTrue() {
        // Arrange
        EmailResultVo vo = new EmailResultVo();
        vo.setSendStatus(EmailStatus.LOCAL_SUCCESS);

        // Act
        Boolean result = billService.emailCallBack(vo);

        // Assert
        assertTrue(result);
        verifyNoInteractions(emailTraceRepository);
    }

    @Test
    @DisplayName("邮件状态回调，正常场景，无追踪记录场景，返回null")
    void testEmailCallback_TracesFoundNull_ReturnTrue() {
        // Arrange
        EmailResultVo vo = buildValidEmailResult();
        vo.setSendStatus(EmailStatus.SUPPLIER_SEND);
        when(emailTraceRepository.findAll(any(Predicate.class))).thenReturn(null);

        // Act
        Boolean result = billService.emailCallBack(vo);

        // Assert
        assertTrue(result);
    }

    @Test
    @DisplayName("邮件状态回调，正常场景，无追踪记录场景，返回空列表")
    void testEmailCallback_NoTracesFound_ReturnTrue() {
        // Arrange
        EmailResultVo vo = buildValidEmailResult();
        vo.setSendStatus(EmailStatus.SUPPLIER_SEND);
        when(emailTraceRepository.findAll(any(Predicate.class))).thenReturn(Collections.emptyList());

        // Act
        Boolean result = billService.emailCallBack(vo);

        // Assert
        assertTrue(result);
    }

    @Test
    @DisplayName("邮件状态回调，正常场景，无目标账单id追踪记录场景")
    void testEmailCallback_TracesNotFoundByBillId_ReturnTrue() {
        // Arrange
        EmailResultVo vo = buildValidEmailResult();
        vo.setSendStatus(EmailStatus.SUPPLIER_SEND);

        BillEmailTrace billEmailTrace = buildEmailTrace(vo.getRequestId());
        when(emailTraceRepository.findAll(any(Predicate.class))).thenReturn(List.of(billEmailTrace)).thenReturn(null);

        // Act
        Boolean result = billService.emailCallBack(vo);

        // Assert
        assertTrue(result);
    }

    @Test
    @DisplayName("邮件状态回调，正常场景，测试完整处理流程")
    void testEmailCallback_FullProcess_Success() {
        try (MockedStatic<DateUtils> dateUtilsMock = mockStatic(DateUtils.class)) {
            // Arrange
            String fixedTime = "2025-01-01 12:00:00";
            dateUtilsMock.when(DateUtils::getTime)
                         .thenReturn(fixedTime);

            var emailStatusDto = new EmailStatusDto();
            emailStatusDto.setEmail("<EMAIL>");
            emailStatusDto.setState(true);

            EmailResultVo vo = new EmailResultVo();
            vo.setResults(List.of(emailStatusDto));
            vo.setSendStatus(EmailStatus.SUPPLIER_SEND);
            vo.setRequestId("req_123");

            BillEmailTrace trace = buildEmailTrace(vo.getRequestId());
            trace.setBatchNo("123");
            trace.setEmail(emailStatusDto.getEmail());

            var staffBillReceiverRespVo = new StaffBillReceiverRespVo();
            staffBillReceiverRespVo.setRequestId(vo.getRequestId());

            BillEntity billEntity = new BillEntity();
            billEntity.setId(1L);
            billEntity.setEmailErr(JSONObject.toJSONString(List.of(staffBillReceiverRespVo)));

            // 配置mock数据
            when(emailTraceRepository.findAll(any(Predicate.class))).thenReturn(List.of(trace));
            when(billRepository.findById(any())).thenReturn(Optional.of(billEntity));

            // Act
            Boolean result = billService.emailCallBack(vo);

            // Assert
            assertTrue(result);
            verify(billRepository).updateEmailErr(anyLong(), anyInt(), anyString());
            verify(emailTraceRepository).save(any(BillEmailTrace.class));
        }
    }

    @Test
    @DisplayName("邮件状态回调，异常场景，邮件发送状态为null")
    void testEmailCallback_SendStatusNull_UpdateErrorMsg() {
        try (MockedStatic<DateUtils> dateUtilsMock = mockStatic(DateUtils.class)) {
            // Arrange
            String fixedTime = "2025-01-01 12:00:00";
            dateUtilsMock.when(DateUtils::getTime)
                         .thenReturn(fixedTime);

            var emailStatusDto = new EmailStatusDto();
            emailStatusDto.setEmail("<EMAIL>");
            emailStatusDto.setState(false);

            EmailResultVo vo = new EmailResultVo();
            vo.setResults(null);
            vo.setSendStatus(EmailStatus.SUPPLIER_SEND);
            vo.setRequestId("req_123");

            BillEmailTrace trace = buildEmailTrace(vo.getRequestId());
            trace.setBatchNo("123");
            trace.setEmail(emailStatusDto.getEmail());

            var staffBillReceiverRespVo = new StaffBillReceiverRespVo();
            staffBillReceiverRespVo.setRequestId(vo.getRequestId());

            BillEntity billEntity = new BillEntity();
            billEntity.setId(1L);
            billEntity.setEmailErr(JSONObject.toJSONString(List.of(staffBillReceiverRespVo)));

            // 配置mock数据
            when(emailTraceRepository.findAll(any(Predicate.class))).thenReturn(List.of(trace));
            when(billRepository.findById(any())).thenReturn(Optional.of(billEntity));

            // Act
            Boolean result = billService.emailCallBack(vo);

            // Assert
            assertTrue(result);
            verify(billRepository).updateEmailErr(anyLong(), any(), anyString());
        }
    }

    @Test
    @DisplayName("邮件状态回调，异常场景，查询追踪记录报错")
    void testEmailCallback_getEmailTraceError() {
        try (MockedStatic<DateUtils> dateUtilsMock = mockStatic(DateUtils.class)) {
            // Arrange
            String fixedTime = "2025-01-01 12:00:00";
            dateUtilsMock.when(DateUtils::getTime)
                         .thenReturn(fixedTime);

            var emailStatusDto = new EmailStatusDto();
            emailStatusDto.setEmail("<EMAIL>");
            emailStatusDto.setState(false);

            EmailResultVo vo = new EmailResultVo();
            vo.setResults(null);
            vo.setSendStatus(EmailStatus.SUPPLIER_SEND);
            vo.setRequestId("req_123");

            BillEmailTrace trace = buildEmailTrace(vo.getRequestId());
            trace.setBatchNo("123");
            trace.setEmail(emailStatusDto.getEmail());

            var staffBillReceiverRespVo = new StaffBillReceiverRespVo();
            staffBillReceiverRespVo.setRequestId(vo.getRequestId());

            // 配置mock数据
            when(emailTraceRepository.findAll(any(Predicate.class))).thenReturn(List.of(trace)).thenThrow(new RuntimeException());

            // Act
            Boolean result = billService.emailCallBack(vo);

            // Assert
            assertTrue(result);
            verify(billRepository, never()).updateEmailErr(anyLong(), any(), anyString());
        }
    }

    @Test
    @DisplayName("邮件状态回调，正常场景，测试完整处理流程,当前批次号为0")
    void testEmailCallback_FullProcess_currentBatchNoIs0_Success() {
        try (MockedStatic<DateUtils> dateUtilsMock = mockStatic(DateUtils.class)) {
            // Arrange
            String fixedTime = "2025-01-01 12:00:00";
            dateUtilsMock.when(DateUtils::getTime)
                         .thenReturn(fixedTime);

            var emailStatusDto = new EmailStatusDto();
            emailStatusDto.setEmail("<EMAIL>");
            emailStatusDto.setState(true);

            EmailResultVo vo = new EmailResultVo();
            vo.setResults(List.of(emailStatusDto));
            vo.setSendStatus(EmailStatus.SUPPLIER_SEND);
            vo.setRequestId("req_123");

            BillEmailTrace trace1 = buildEmailTrace(vo.getRequestId());
            trace1.setBatchNo("123");
            trace1.setEmail(emailStatusDto.getEmail());

            BillEmailTrace trace2 = buildEmailTrace(vo.getRequestId() + 3); // 构造不相等场景
            trace2.setBatchNo("456");
            trace2.setEmail(emailStatusDto.getEmail());

            var staffBillReceiverRespVo = new StaffBillReceiverRespVo();
            staffBillReceiverRespVo.setRequestId(vo.getRequestId());

            BillEntity billEntity = new BillEntity();
            billEntity.setId(1L);
            billEntity.setEmailStatus(10);
            billEntity.setEmailErr(JSONObject.toJSONString(List.of(staffBillReceiverRespVo)));

            // 配置mock数据
            when(emailTraceRepository.findAll(any(Predicate.class))).thenReturn(List.of(trace1));
            when(billRepository.findById(any())).thenReturn(Optional.of(billEntity));

            // Act
            Boolean result = billService.emailCallBack(vo);

            // Assert
            assertTrue(result);
            verify(billRepository).updateEmailErr(anyLong(), anyInt(), anyString());
            verify(emailTraceRepository).save(any(BillEmailTrace.class));
        }
    }

    @Test
    @DisplayName("邮件状态回调，正常场景，测试完整处理流程，测试邮件发送失败处理")
    void testEmailCallback_SendFailed_UpdateErrorMsg() {
        try (MockedStatic<DateUtils> dateUtilsMock = mockStatic(DateUtils.class)) {
            // Arrange
            String fixedTime = "2025-01-01 12:00:00";
            dateUtilsMock.when(DateUtils::getTime)
                         .thenReturn(fixedTime);

            var emailStatusDto = new EmailStatusDto();
            emailStatusDto.setEmail("<EMAIL>");
            emailStatusDto.setState(false);

            EmailResultVo vo = new EmailResultVo();
            vo.setResults(List.of(emailStatusDto));
            vo.setSendStatus(EmailStatus.LOCAL_FAILED);
            vo.setRequestId("req_123");

            BillEmailTrace trace = buildEmailTrace(vo.getRequestId());
            trace.setBatchNo("123");
            trace.setEmail(emailStatusDto.getEmail());

            var staffBillReceiverRespVo = new StaffBillReceiverRespVo();
            staffBillReceiverRespVo.setRequestId(vo.getRequestId());

            BillEntity billEntity = new BillEntity();
            billEntity.setId(1L);
            billEntity.setEmailErr(JSONObject.toJSONString(List.of(staffBillReceiverRespVo)));

            // 配置mock数据
            when(emailTraceRepository.findAll(any(Predicate.class))).thenReturn(List.of(trace));
            when(billRepository.findById(any())).thenReturn(Optional.of(billEntity));

            // Act
            Boolean result = billService.emailCallBack(vo);

            // Assert
            assertTrue(result);
            verify(billRepository).updateEmailErr(anyLong(), anyInt(), anyString());
            verify(emailTraceRepository).save(any(BillEmailTrace.class));
        }
    }

    @Test
    @DisplayName("测试第一个searchPagedBill方法，正常场景，数据为空")
    void testSearchPagedBill_WithSpecification_EmptyResult() {
        // Arrange
        Specification<BillEntity> spec = mock(Specification.class);
        when(billRepository.findAll(spec)).thenReturn(Collections.emptyList());

        // Act
        List<BillUserSelectVo> result = billService.searchPagedBill(spec);

        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("测试第一个searchPagedBill方法，正常场景，数据不为空")
    void testSearchPagedBill_WithSpecification_Success() {
        try (MockedStatic<BeanUtil> beanUtilMock = mockStatic(BeanUtil.class)) {
            // Arrange
            Specification<BillEntity> spec = mock(Specification.class);
            List<BillEntity> mockData = List.of(new BillEntity(), new BillEntity());

            when(billRepository.findAll(spec)).thenReturn(mockData);
            beanUtilMock.when(() -> BeanUtil.copy(any(BillEntity.class), eq(BillUserSelectVo.class)))
                        .thenReturn(new BillUserSelectVo());

            // Act
            List<BillUserSelectVo> result = billService.searchPagedBill(spec);

            // Assert
            assertEquals(2, result.size());
        }
    }

    @Test
    @DisplayName("测试第二个searchPagedBill方法，正常场景")
    void testSearchPagedBill_WithDto_BusProcessing() {
        // Arrange
        try (MockedStatic<BeanUtil> beanUtilMock = mockStatic(BeanUtil.class)) {
            BillSendHistoryReqDto dto = new BillSendHistoryReqDto();
            dto.setBus("BU1,BU2,BU3");
            dto.setTpAlph("TP002");

            List<BillEntity> mockData = List.of(new BillEntity(), new BillEntity());

            when(billRepository.queryList(any(), any(), any())).thenReturn(mockData);
            beanUtilMock.when(() -> BeanUtil.copy(any(BillEntity.class), eq(BillUserSelectVo.class)))
                        .thenReturn(new BillUserSelectVo());

            // Act
            billService.searchPagedBill(dto);

            // Assert
            verify(billRepository).queryList(eq(dto), any(), any());
        }
    }

    @Test
    @DisplayName("向JDE数据库回写标志位：测试空账单列表")
    void testEmptyBillList_ShouldLogAndExit() {
        // Act and Assert
        assertDoesNotThrow(() -> billService.writeBackJDESyncFlag(Collections.emptyList()));
    }

    @Test
    @DisplayName("向JDE数据库回写标志位：异常场景：账单信息不合法")
    void testBatchUpdate_billInvalidError() {
        try (MockedStatic<BeanUtil> beanUtilMock = mockStatic(BeanUtil.class)) {
            // Arrange
            var bill = new Bill();
            List<Bill> bills = IntStream.range(0, 10)
                                        .mapToObj(i -> bill)
                                        .collect(Collectors.toList());

            var billEntity = new BillEntity();
            billEntity.setId(0L);
            billEntity.setTpFyr(2025);
            billEntity.setTpPn(2);
            billEntity.setTpCrtutime(new Date());
            beanUtilMock.when(() -> BeanUtil.copy(any(), eq(BillEntity.class)))
                        .thenReturn(billEntity);

            // Act
            assertDoesNotThrow(() -> billService.writeBackJDESyncFlag(bills));

        }
    }

    @Test
    @DisplayName("向JDE数据库回写标志位：异常场景，oracle数据库交互BatchUpdateException异常")
    void testBatchUpdate_jdeOracleBatchUpdateException() throws Exception {
        try (MockedStatic<OracleJdbc> oracleMock = mockStatic(OracleJdbc.class);
             MockedStatic<BeanUtil> beanUtilMock = mockStatic(BeanUtil.class)) {
            // Arrange
            var bill = new Bill();
            List<Bill> bills = IntStream.range(0, 10)
                                        .mapToObj(i -> bill)
                                        .collect(Collectors.toList());

            Connection connection = mock(Connection.class);
            oracleMock.when(OracleJdbc::getOracleConnection)
                      .thenReturn(connection);
            when(connection.prepareStatement(anyString())).thenThrow(new BatchUpdateException());

            var billEntity = new BillEntity();
            billEntity.setId(1L);
            billEntity.setTpFyr(2025);
            billEntity.setTpPn(2);
            billEntity.setTpCrtutime(new Date());
            beanUtilMock.when(() -> BeanUtil.copy(any(), eq(BillEntity.class)))
                        .thenReturn(billEntity);

            // Act and Assert
            assertDoesNotThrow(() -> billService.writeBackJDESyncFlag(bills));
        }
    }

    @Test
    @DisplayName("向JDE数据库回写标志位：异常场景，oracle数据库交互SQLException异常")
    void testBatchUpdate_jdeOracleSQLException() throws Exception {
        try (MockedStatic<OracleJdbc> oracleMock = mockStatic(OracleJdbc.class);
             MockedStatic<BeanUtil> beanUtilMock = mockStatic(BeanUtil.class)) {
            // Arrange
            var bill = new Bill();
            List<Bill> bills = IntStream.range(0, 10)
                                        .mapToObj(i -> bill)
                                        .collect(Collectors.toList());

            Connection connection = mock(Connection.class);
            oracleMock.when(OracleJdbc::getOracleConnection)
                      .thenReturn(connection);
            when(connection.prepareStatement(anyString())).thenThrow(new SQLException());

            var billEntity = new BillEntity();
            billEntity.setId(1L);
            billEntity.setTpFyr(2025);
            billEntity.setTpPn(2);
            billEntity.setTpCrtutime(new Date());
            beanUtilMock.when(() -> BeanUtil.copy(any(), eq(BillEntity.class)))
                        .thenReturn(billEntity);

            // Act and Assert
            assertDoesNotThrow(() -> billService.writeBackJDESyncFlag(bills));
        }
    }

    @Test
    @DisplayName("向JDE数据库回写标志位：正常场景")
    void testBatchUpdate_Success() throws Exception {
        try (MockedStatic<OracleJdbc> oracleMock = mockStatic(OracleJdbc.class);
             MockedStatic<BeanUtil> beanUtilMock = mockStatic(BeanUtil.class)) {
            // Arrange
            var bill = new Bill();
            List<Bill> bills = IntStream.range(0, 10)
                                        .mapToObj(i -> bill)
                                        .collect(Collectors.toList());

            Connection connection = mock(Connection.class);
            PreparedStatement preparedStatement = mock(PreparedStatement.class);

            when(preparedStatement.executeBatch()).thenReturn(new int[]{-3, -2, 1, 2});
            oracleMock.when(OracleJdbc::getOracleConnection)
                      .thenReturn(connection);
            when(connection.prepareStatement(anyString())).thenReturn(preparedStatement);

            when(jdbcTemplate.batchUpdate(anyString(), any(BatchPreparedStatementSetter.class))).thenReturn(
                    new int[]{Statement.EXECUTE_FAILED, Statement.SUCCESS_NO_INFO});

            var billEntity = new BillEntity();
            billEntity.setId(1L);
            billEntity.setTpFyr(2025);
            billEntity.setTpPn(2);
            billEntity.setTpCrtutime(new Date());
            beanUtilMock.when(() -> BeanUtil.copy(any(), eq(BillEntity.class)))
                        .thenReturn(billEntity);

            // Act
            billService.writeBackJDESyncFlag(bills);

            // Assert
            verify(connection, times(1)).commit();  // 150/100=2批次
            verify(preparedStatement, times(10)).addBatch();
        }
    }

    @Test
    @DisplayName("向JDE数据库回写标志位：正常场景")
    void testselectBillTenants_docosEmptyError() {
        // Act
        Set<BillPayer> result = billService.selectBillTenants(Collections.emptySet(), Collections.emptySet());


        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    @DisplayName("获取账单支付人，异常场景，返回支付人列表为空")
    void testGetInvoiceBillPayers_EmptyList_ThrowException() {
        try (MockedStatic<UserInfoUtils> userUtilsMock = mockStatic(UserInfoUtils.class)) {
            // Arrange
            userUtilsMock.when(UserInfoUtils::getUser)
                         .thenReturn(generateLoginUser());
            when(billSendConfigService.queryBillPayers(any())).thenReturn(Collections.emptyList());

            // Act & Assert
            AppException exception = assertThrows(AppException.class, () -> billService.getInvoiceBillPayers());

            assertEquals(RespCodeEnum.AN8_INVOICE_ERROR.getCode(), exception.getCode());
        }
    }

    @Test
    @DisplayName("获取账单支付人，正常场景")
    void testGetInvoiceBillPayers_Success() {
        try (MockedStatic<UserInfoUtils> userUtilsMock = mockStatic(UserInfoUtils.class)) {
            // Arrange
            LoginUser mockUser = generateLoginUser();
            mockUser.setRoles("SUPER_ADMIN");
            userUtilsMock.when(UserInfoUtils::getUser)
                         .thenReturn(mockUser);

            List<BillPayer> mockPayers = List.of(new BillPayer("alph-1", "an8-1"), new BillPayer("alph-2", "an8-2"));
            when(billSendConfigService.queryBillPayers(any())).thenReturn(mockPayers);

            // Act
            Set<BillPayer> result = billService.getInvoiceBillPayers();

            // Assert
            assertEquals(2, result.size());
            verify(billSendConfigService).queryBillPayers(any());
        }
    }

    @Test
    @DisplayName("查询B端账单：正常场景，非分页查询，结果为空")
    void testSelectBillVoList_EmptyResult() {
        try (MockedStatic<BeanUtil> beanUtilMock = mockStatic(BeanUtil.class)) {
            // Arrange
            Iterable<BillEntity> emptyIterable = Collections.emptyList();
            when(billRepository.findAll(any(Predicate.class))).thenReturn(emptyIterable);

            // Act
            List<Bill> result = billService.selectBillVoList(mock(Predicate.class));

            // Assert
            assertTrue(result.isEmpty());
            beanUtilMock.verifyNoInteractions();
        }
    }

    @Test
    @DisplayName("查询B端账单：正常场景，无分页查询")
    void testSelectBillVoList_WithValidEntities() {
        try (MockedStatic<BeanUtil> beanUtilMock = mockStatic(BeanUtil.class)) {
            // Arrange
            when(billRepository.findAll(any(Predicate.class))).thenReturn(List.of(new BillEntity(), new BillEntity()));
            beanUtilMock.when(() -> BeanUtil.copy(any(), eq(Bill.class)))
                        .thenReturn(new Bill());

            // Act
            List<Bill> result = billService.selectBillVoList(mock(Predicate.class));

            // Assert
            assertEquals(2, result.size());
            verify(billRepository).findAll(any(Predicate.class));
        }
    }

    @Test
    @DisplayName("查询B端账单：正常场景，分页查询")
    void testSelectBillVoListPaged_WithPageConversion() {
        try (MockedStatic<BeanUtil> beanUtilMock = mockStatic(BeanUtil.class)) {
            // Arrange
            Page<BillEntity> mockPage = new PageImpl<>(List.of(new BillEntity(), new BillEntity()));
            when(billRepository.findAll(any(Predicate.class), any(Pageable.class))).thenReturn(mockPage);
            beanUtilMock.when(() -> BeanUtil.copy(any(BillEntity.class), eq(BillSelectVo.class)))
                        .thenReturn(new BillSelectVo());

            // Act
            Page<BillSelectVo> result = billService.selectBillVoList(Pageable.ofSize(10), mock(Predicate.class));

            // Assert
            assertEquals(2, result.getContent()
                                  .size());
            verify(billRepository).findAll(any(Predicate.class), any(Pageable.class));
        }
    }

    @Test
    @DisplayName("更新账单阅读状态 - 正常场景")
    void updateBillReadStatus_normalScenario() {
        // Arrange
        BizBillReadReqVo reqVo = new BizBillReadReqVo();
        reqVo.setId(1L);
        reqVo.setMailOrMobileSource(1);

        BillEntity billEntity = new BillEntity();
        billEntity.setId(1L);
        billEntity.setMobileReadTime(null);

        when(billRepository.findById(1L)).thenReturn(Optional.of(billEntity));
        when(billRepository.getJpaQueryFactory()).thenReturn(jpaQueryFactory);
        JPAUpdateClause updateClause = mock(JPAUpdateClause.class);
        when(jpaQueryFactory.update(QBillEntity.billEntity)).thenReturn(updateClause);
        when(updateClause.set(any(), any(Integer.class))).thenReturn(updateClause);
        when(updateClause.set(any(), any(Date.class))).thenReturn(updateClause);
        when(updateClause.where(any(BooleanExpression.class))).thenReturn(updateClause);
        when(updateClause.execute()).thenReturn(1L);

        // Act
        Integer result = billService.updateBillReadStatus(reqVo);

        // Assert
        assertEquals(1, result);
        verify(updateClause).set(eq(QBillEntity.billEntity.readStatus), anyInt());
        verify(updateClause).set(eq(QBillEntity.billEntity.mobileReadTime), any(Date.class));
        verify(updateClause).where(QBillEntity.billEntity.id.eq(1L));
    }

    @Test
    @DisplayName("更新账单阅读状态 - 账单不存在")
    void updateBillReadStatus_billNotFound() {
        // Arrange
        BizBillReadReqVo reqVo = new BizBillReadReqVo();
        reqVo.setId(1L);

        Optional optional = Mockito.mock(Optional.class);
        when(billRepository.findById(anyLong())).thenReturn(optional);
        when(optional.get()).thenReturn(null);

        // Act
        Integer result = billService.updateBillReadStatus(reqVo);

        // Assert
        assertEquals(0, result);
        verify(billRepository, never()).getJpaQueryFactory();
    }

    @DisplayName("更新账单阅读状态 - 手机端已读")
    @Test
    void updateBillReadStatus_mobileAlreadyRead() {
        // Arrange
        BizBillReadReqVo reqVo = new BizBillReadReqVo();
        reqVo.setId(1L);
        reqVo.setMailOrMobileSource(1);

        BillEntity billEntity = new BillEntity();
        billEntity.setId(1L);
        billEntity.setMobileReadTime(new Date());

        when(billRepository.findById(1L)).thenReturn(Optional.of(billEntity));

        when(billRepository.findById(1L)).thenReturn(Optional.of(billEntity));
        when(billRepository.getJpaQueryFactory()).thenReturn(jpaQueryFactory);
        JPAUpdateClause updateClause = mock(JPAUpdateClause.class);
        when(jpaQueryFactory.update(QBillEntity.billEntity)).thenReturn(updateClause);

        // Act
        Integer result = billService.updateBillReadStatus(reqVo);

        // Assert
        assertEquals(0, result);
        verify(updateClause, never()).set(any(), any(Date.class));
    }

    @Test
    @DisplayName("更新账单阅读状态 - 站内信已读")
    void updateBillReadStatus_mailAlreadyRead() {
        // Arrange
        BizBillReadReqVo reqVo = new BizBillReadReqVo();
        reqVo.setId(1L);
        reqVo.setMailOrMobileSource(0);

        BillEntity billEntity = new BillEntity();
        billEntity.setId(1L);
        billEntity.setMailReadTime(new Date());

        when(billRepository.findById(1L)).thenReturn(Optional.of(billEntity));
        when(billRepository.getJpaQueryFactory()).thenReturn(jpaQueryFactory);
        JPAUpdateClause updateClause = mock(JPAUpdateClause.class);
        when(jpaQueryFactory.update(QBillEntity.billEntity)).thenReturn(updateClause);
        when(updateClause.set(any(), any(Integer.class))).thenReturn(updateClause);

        when(billRepository.findById(1L)).thenReturn(Optional.of(billEntity));

        // Act
        Integer result = billService.updateBillReadStatus(reqVo);

        // Assert
        assertEquals(0, result);
        verify(updateClause, never()).where(any(BooleanExpression.class));
    }

    @Test
    @DisplayName("异常场景：合同号或地址号为空")
    void queryBillReceiversByDocoAn8_EmptyInputs() {
        // Arrange
        String jdeDoco = StringUtils.EMPTY;
        String an8 = StringUtils.EMPTY;

        // Act
        Set<StaffBillReceiverRespVo> result = billService.queryBillReceiversByDocoAn8(jdeDoco, an8);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("异常场景：未找到有效的配置")
    void queryBillReceiversByDocoAn8_NoConfigsFound() {
        // Arrange
        String jdeDoco = "DOC001";
        String an8 = "AN8001";

        when(sendConfigRepository.queryActiveConfigsByDoco(jdeDoco))
                .thenReturn(Collections.emptyList());

        // Act
        Set<StaffBillReceiverRespVo> result = billService.queryBillReceiversByDocoAn8(jdeDoco, an8);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("异常场景：未找到有效的地址号链接")
    void queryBillReceiversByDocoAn8_NoLinksFound() {
        // Arrange
        String jdeDoco = "DOC001";
        String an8 = "AN8001";

        BillSendConfig config = new BillSendConfig();
        config.setId(1L);

        BillSendConfigAn8Link link = new BillSendConfigAn8Link();
        link.setConfigId(config.getId() + 3L); // 构造不相等场景

        when(sendConfigRepository.queryActiveConfigsByDoco(jdeDoco))
                .thenReturn(List.of(config));
        when(linkRepository.selectActiveAn8Links(any()))
                .thenReturn(List.of(link));

        // Act
        Set<StaffBillReceiverRespVo> result = billService.queryBillReceiversByDocoAn8(jdeDoco, an8);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("异常场景：账单已发送，无法删除")
    void deleteBill_BillAlreadySent() {
        // Arrange
        Predicate predicate = mock(Predicate.class);

        var billEntity = new BillEntity();
        billEntity.setId(1L);
        billEntity.setTpStatus(0);
        billEntity.setDelFlag("0");
        Iterable<BillEntity> billEntities = List.of(billEntity);
        when(billRepository.findAll(predicate)).thenReturn(billEntities);

        when(billRepository.save(any())).thenThrow(new RuntimeException());

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = mockStatic(UserInfoUtils.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser).thenReturn(randomLoginUser());

            // Act
            Boolean result = billService.deleteBill(predicate);

            // Assert
            assertTrue(result);
            verify(billRepository, times(1)).save(any(BillEntity.class));
        }
    }

    @Test
    @DisplayName("异常场景：未找到账单")
    void deleteBill_NoBillsFound() {
        // Arrange
        Predicate predicate = mock(Predicate.class);
        when(billRepository.findAll(any(Predicate.class))).thenReturn(null);

        // Act
        Boolean result = billService.deleteBill(predicate);

        // Assert
        assertFalse(result);
        verify(billRepository, never()).save(any(BillEntity.class));
    }

    @Test
    @DisplayName("正常场景：根据条件计数成功")
    void countByPredicate_Success() {
        // Arrange
        Predicate predicate = mock(Predicate.class);
        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = mockStatic(UserInfoUtils.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser).thenReturn(randomLoginUser());

            // Act
            billService.countByPredicate(predicate);

            // Assert
            verify(billRepository, times(1)).count(any(Predicate.class));
        }
    }

    @Test
    @DisplayName("异常场景：根据ID未找到账单")
    void selectBillById_NotFound() {
        // Arrange
        Long billId = 1L;
        Optional<BillEntity> mockOptional = Mockito.mock(Optional.class);
        when(mockOptional.get()).thenReturn(null);
        when(billRepository.findById(any())).thenReturn(mockOptional);

        // Act
        Bill result = billService.selectBillById(billId);

        // Assert
        assertNull(result);
        verify(billRepository, times(1)).findById(any());
    }

    @Test
    @DisplayName("异常场景：根据ID列表未找到账单")
    void selelctBillListByIds_NotFound() {
        // Act
        List<Bill> result = billService.selelctBillListByIds(Collections.emptyList());

        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("正常场景：分页查询账单（Specification）")
    void searchPagedBill_WithSpecification_Success() {
        // Arrange
        Specification specification = mock(Specification.class);
        PageRequest pageable = PageRequest.of(0, 10);

        BillEntity billEntity = new BillEntity();
        billEntity.setId(1L);

        BillUserSelectVo billUserSelectVo = new BillUserSelectVo();
        billUserSelectVo.setId(1L);

        Page<BillEntity> billEntityPage = new PageImpl<>(List.of(billEntity), pageable, 1);
        when(billRepository.findAll(specification, pageable)).thenReturn(billEntityPage);

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = mockStatic(BeanUtil.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(BillEntity.class), eq(BillUserSelectVo.class)))
                                .thenReturn(billUserSelectVo);

            // Act
            Page<BillUserSelectVo> result = billService.searchPagedBill(pageable, specification);

            // Assert
            assertEquals(1, result.getTotalElements());
            assertEquals(1L, result.getContent().get(0).getId());
            verify(billRepository, times(1)).findAll(specification, pageable);
        }
    }

    @Test
    @DisplayName("正常场景：分页查询账单（BillSendHistoryReqDto）")
    void searchPagedBill_WithBillSendHistoryReqDto_Success() {
        // Arrange
        BillSendHistoryReqDto reqDto = new BillSendHistoryReqDto();
        reqDto.setBus("BUS001");
        PageRequest pageable = PageRequest.of(0, 10);

        BillEntity billEntity = new BillEntity();
        billEntity.setId(1L);

        BillUserSelectVo billUserSelectVo = new BillUserSelectVo();
        billUserSelectVo.setId(1L);

        Page<BillEntity> billEntities = new PageImpl<>(List.of(billEntity), pageable, 1);
        when(billRepository.queryList(any(), any(), any(), any()))
                .thenReturn(billEntities);

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = mockStatic(BeanUtil.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(BillEntity.class), eq(BillUserSelectVo.class)))
                                .thenReturn(billUserSelectVo);

            // Act
            Page<BillUserSelectVo> result = billService.searchPagedBill(pageable, reqDto);

            // Assert
            assertEquals(1, result.getTotalElements());
            assertEquals(1L, result.getContent().get(0).getId());
            verify(billRepository, times(1)).queryList(any(), any(), any(), any());
        }
    }

    @Test
    @DisplayName("正常场景：全部已读")
    void setUserBillRead_allReadSuccess() {
        // Arrange
        long billId = 1L;
        long userId = 100L;

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = mockStatic(UserInfoUtils.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser).thenReturn(randomLoginUser());

            when(bizContentReadRepository.findFirstByContentIdAndAndTypeAndUserId(anyLong(), any(), anyLong()))
                    .thenReturn(Optional.of(new BizContentReadEntity()));

            // Act
            boolean result = billService.setUserBillRead(billId);

            // Assert
            assertTrue(result);
            verify(bizContentReadRepository, never()).save(any());
        }
    }

    @Test
    @DisplayName("正常场景：查询旧账单成功")
    void selectOldBill_Success() {
        // Arrange
        Bill bill = new Bill();
        bill.setId(1L);
        bill.setTpStatus(5);

        BillEntity billEntity = new BillEntity();
        billEntity.setId(1L);
        billEntity.setTpStatus(5);

        when(billRepository.selectOldBill(any(), any(), any(), any(), any(), any(), any())).thenReturn(List.of(billEntity));

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = mockStatic(UserInfoUtils.class)) {

            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any())).thenReturn(billEntity);
            mockedStaticUserInfo.when(UserInfoUtils::getUser).thenReturn(randomLoginUser());

            // Act
            List<BillEntity> result = billService.selectOldBill(bill);

            // Assert
            assertEquals(1, result.size());
            assertEquals(1L, result.get(0).getId());
            verify(billRepository, times(1)).selectOldBill(any(), any(), any(), any(), any(), any(), any());
        }
    }

    @Test
    @DisplayName("正常场景：根据条件查询账单成功")
    void searchBill_Success() {
        // Arrange
        Predicate predicate = mock(Predicate.class);

        BillEntity billEntity = new BillEntity();
        billEntity.setId(1L);

        BillUserSelectVo billUserSelectVo = new BillUserSelectVo();
        billUserSelectVo.setId(1L);

        when(billRepository.findAll(predicate)).thenReturn(List.of(billEntity));

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = mockStatic(BeanUtil.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(BillEntity.class), eq(BillUserSelectVo.class)))
                                .thenReturn(billUserSelectVo);

            // Act
            List<BillUserSelectVo> result = billService.searchBill(predicate);

            // Assert
            assertEquals(1, result.size());
            assertEquals(1L, result.get(0).getId());
            verify(billRepository, times(1)).findAll(predicate);
        }
    }

    @Test
    @DisplayName("正常场景：解析模板内容成功：格式1")
    void parseContent_format1_Success() throws Exception {
        // Arrange
        MessageTemplateEntity template = new MessageTemplateEntity();
        template.setHeadContent("Hello, ${xxx}!");
        template.setForeachContent("Everyone, ${xxx}!");

        List<String> headList = new ArrayList<>();
        headList.add("name");

        List<List<String>> foreachList = new ArrayList<>();
        List<String> row = new ArrayList<>();
        row.add("John");
        foreachList.add(row);

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = mockStatic(BeanUtil.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any())).thenReturn(new Object());

            // 使用反射调用私有方法
            Method parseContentMethod = BillServiceImpl.class.getDeclaredMethod("parseContent", MessageTemplateEntity.class, List.class, List.class);
            parseContentMethod.setAccessible(true);

            // Act
            String result = (String) parseContentMethod.invoke(billService, template, headList, foreachList);

            // Assert
            assertEquals("Hello, $name!Everyone, $John!", result);
        }
    }

    @Test
    @DisplayName("正常场景：解析模板内容成功：格式2")
    void parseContent_format2_Success() throws Exception {
        // Arrange
        MessageTemplateEntity template = new MessageTemplateEntity();
        template.setHeadContent("{xxx}!, Hello,");
        template.setForeachContent("Everyone, ${xxx}!");

        List<String> headList = new ArrayList<>();
        headList.add("name");

        List<List<String>> foreachList = new ArrayList<>();
        List<String> row = new ArrayList<>();
        row.add("John");
        foreachList.add(row);

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = mockStatic(BeanUtil.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any())).thenReturn(new Object());

            // 使用反射调用私有方法
            Method parseContentMethod = BillServiceImpl.class.getDeclaredMethod("parseContent", MessageTemplateEntity.class, List.class, List.class);
            parseContentMethod.setAccessible(true);

            // Act
            String result = (String) parseContentMethod.invoke(billService, template, headList, foreachList);

            // Assert
            assertEquals("name!, Hello,Everyone, $John!", result);
        }
    }

    @Test
    @DisplayName("异常场景：模板为空")
    void parseContent_TemplateNull() throws Exception {
        // Arrange
        MessageTemplateEntity template = null;
        List<String> headList = new ArrayList<>();
        List<List<String>> foreachList = new ArrayList<>();

        // 使用反射调用私有方法
        Method parseContentMethod = BillServiceImpl.class.getDeclaredMethod("parseContent", MessageTemplateEntity.class, List.class, List.class);
        parseContentMethod.setAccessible(true);

        // Act
        assertThrows(InvocationTargetException.class, () -> parseContentMethod.invoke(billService, template, headList,
                                                                                      foreachList));
    }

    @Test
    @DisplayName("异常场景：foreachList为空")
    void parseContent_ForeachListEmpty() throws Exception {
        // Arrange
        MessageTemplateEntity template = new MessageTemplateEntity();
        template.setHeadContent("Hello, ${name}!");
        template.setTailContent("Goodbye!");

        List<String> headList = new ArrayList<>();
        headList.add("name");

        List<List<String>> foreachList = new ArrayList<>();


        // 使用反射调用私有方法
        Method parseContentMethod = BillServiceImpl.class.getDeclaredMethod("parseContent", MessageTemplateEntity.class, List.class, List.class);
        parseContentMethod.setAccessible(true);

        // Act
        String result = (String) parseContentMethod.invoke(billService, template, headList, foreachList);

        // Assert
        assertEquals("Hello, ${name}!Goodbye!", result);
    }

    @Test
    @DisplayName("异常场景：billEntities为空")
    void batchSyncFlagIntoJDE_NullBillEntities() throws Exception {
        // Arrange
        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = mockStatic(BeanUtil.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new Object());

            // 使用反射调用私有方法
            Method batchSyncFlagIntoJDEMethod =
                    BillServiceImpl.class.getDeclaredMethod("batchSyncFlagIntoJDE", List.class,
                                                            PreparedStatement.class);
            batchSyncFlagIntoJDEMethod.setAccessible(true);

            // Act and Assert
            assertDoesNotThrow(() -> batchSyncFlagIntoJDEMethod.invoke(billService, null, null));
        }
    }

    @Test
    @DisplayName("异常场景：PreparedStatement为空")
    void batchSyncFlagIntoJDE_NullPreparedStatement() throws Exception {
        // Arrange
        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = mockStatic(BeanUtil.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new Object());

            // 使用反射调用私有方法
            Method batchSyncFlagIntoJDEMethod =
                    BillServiceImpl.class.getDeclaredMethod("batchSyncFlagIntoJDE", List.class,
                                                            PreparedStatement.class);
            batchSyncFlagIntoJDEMethod.setAccessible(true);

            // Act and Assert
            assertDoesNotThrow(() -> batchSyncFlagIntoJDEMethod.invoke(billService, List.of(new BillEntity()), null));
        }
    }


    // 辅助方法
    private EmailResultVo buildValidEmailResult() {
        EmailResultVo vo = new EmailResultVo();
        vo.setRequestId("req_123");
        vo.setSendStatus(EmailStatus.LOCAL_SUCCESS);
        vo.setResults(List.of(new EmailStatusDto()));
        return vo;
    }

    private BillEmailTrace buildEmailTrace(String requestId) {
        BillEmailTrace trace = new BillEmailTrace();
        trace.setRequestId(requestId);
        trace.setBillId(1L);
        return trace;
    }

    private BillEntity buildTestBill(Long id, Integer tpStatus) {
        BillEntity bill = new BillEntity();
        bill.setId(id);
        bill.setTpStatus(tpStatus);
        bill.setDelFlag("0");
        return bill;
    }

    private LoginUser generateLoginUser() {
        return LoginUser.builder()
                        .nickName("testUser")
                        .fromType("C")
                        .cid("testCid")
                        .build();
    }

}