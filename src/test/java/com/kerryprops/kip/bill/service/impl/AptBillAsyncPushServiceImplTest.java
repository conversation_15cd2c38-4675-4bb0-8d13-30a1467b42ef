package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptBillOperator;
import com.kerryprops.kip.bill.service.AptBillPushService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static com.kerryprops.kip.bill.utils.RandomUtil.randomObject;
import static org.mockito.ArgumentMatchers.any;

/**
 * AptBillAsyncPushServiceImplTest.
 *
 * <AUTHOR> Yu 2024-08-21 17:55:14
 **/
@ExtendWith(MockitoExtension.class)
class AptBillAsyncPushServiceImplTest {

    @Mock
    AptBillPushService aptBillPushService;

    @InjectMocks
    AptBillAsyncPushServiceImpl aptBillAsyncPushServiceImpl;

    @Test
    void asyncPushAptBill() {
        AptBill bill = randomObject(AptBill.class);
        List<AptBill> aptBills = List.of(bill);
        LoginUser user = randomObject(LoginUser.class);

        aptBillAsyncPushServiceImpl.asyncPushAptBill(aptBills, AptBillOperator.UNKNOWN, "conversationId", user);
        
        Mockito.verify(aptBillPushService, Mockito.times(1)).pushAptBill(any(), any(), any(), any());
    }

}