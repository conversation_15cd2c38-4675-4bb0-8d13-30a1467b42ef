package com.kerryprops.kip.bill.service;

import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptBillOperator;
import com.kerryprops.kip.bill.service.impl.AptBillAsyncPushServiceImpl;
import com.kerryprops.kip.bill.utils.RandomUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

/**
 * AptBillAsyncPushServiceTest.
 *
 * <AUTHOR> Yu 2024-08-09 09:52:28
 **/
@ExtendWith(MockitoExtension.class)
class AptBillAsyncPushServiceTest {

    @InjectMocks
    AptBillAsyncPushServiceImpl aptBillAsyncPushService;

    @Mock
    AptBillPushService aptBillPushService;

    @Test
    void asyncPushAptBill() {
        aptBillAsyncPushService.asyncPushAptBill(List.of(RandomUtil.randomObject(AptBill.class)), AptBillOperator.ADD, "", new LoginUser());
    }

}