package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.AptBillDirectDebitsAgreementRepository;
import com.kerryprops.kip.bill.dao.AptPaymentInfoRepository;
import com.kerryprops.kip.bill.dao.entity.AptBillDirectDebitsAgreement;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.feign.clients.CUserClient;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.entity.CustomerUserResource;
import com.kerryprops.kip.bill.feign.entity.WxOpenIDResource;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.model.s.AptBillAgreementBo;
import com.kerryprops.kip.bill.webservice.vo.resp.DirectDebitsAgreementResource;
import com.kerryprops.kip.hiveas.feign.dto.resp.RoomRespDto;
import com.kerryprops.kip.hiveas.webservice.resource.resp.BuildingSimple;
import com.kerryprops.kip.hiveas.webservice.resource.resp.FloorSimple;
import com.kerryprops.kip.hiveas.webservice.resource.resp.ProjectSimple;
import com.kerryprops.kip.hiveas.webservice.resource.resp.RoomResp;
import com.kerryprops.kip.pmw.client.resource.AsyncAgreementStatusChangedResource;
import com.kerryprops.kip.pmw.client.resource.TerminateAgreementOutputResource;
import com.querydsl.core.types.Predicate;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.kerryprops.kip.bill.common.enums.DirectDebitAgreementStatus.ACTIVE;
import static com.kerryprops.kip.bill.common.enums.DirectDebitAgreementStatus.TERMINATED;
import static com.kerryprops.kip.bill.common.enums.DirectDebitTerminatedType.USER_UNSIGN;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.SUCCESS;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomLoginUser;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomString;
import static com.kerryprops.kip.pmw.variables.PspName.ALIPAY;
import static com.kerryprops.kip.pmw.variables.PspName.WECHATPAY;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * AptBillAgreementServiceImplTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-04-23
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("C端协议Service类-单元测试")
class AptBillAgreementServiceImplTest {

    @Mock
    private AptPaymentInfoRepository aptPaymentInfoRepository;

    @Mock
    private AptBillDirectDebitsAgreementRepository agreementRepository;

    @Mock
    private HiveAsClient hiveAsClient;

    @Mock
    private CUserClient profileClient;

    @InjectMocks
    private AptBillAgreementServiceImpl aptBillAgreementService;

    @Test
    @DisplayName("正常场景：终止协议成功")
    public void agreementTerminated_shouldTerminateAgreementSuccessfully() {
        // Arrange
        AptBillDirectDebitsAgreement mockAgreement = new AptBillDirectDebitsAgreement();
        mockAgreement.setAgreementNo("AG123");
        mockAgreement.setAgreementStatus(ACTIVE);

        var outputBodyResource = new TerminateAgreementOutputResource.TerminateAgreementOutputBodyResource();
        outputBodyResource.setAgreementNo("AG123");
        outputBodyResource.setUnsignedTime(ZonedDateTime.now());
        outputBodyResource.setUnsignType(USER_UNSIGN.name());

        when(agreementRepository.findTopByAgreementNo("AG123")).thenReturn(mockAgreement);

        // Act
        aptBillAgreementService.agreementTerminated(outputBodyResource);

        // Assert
        assertEquals(TERMINATED, mockAgreement.getAgreementStatus());
        assertEquals(outputBodyResource.getUnsignedTime(), mockAgreement.getUnsignTime());
        verify(agreementRepository).save(mockAgreement);
    }

    @Test
    @DisplayName("保存/更新协议状态：支付宝协议：正常场景：保存或更新协议成功")
    public void saveOrUpdateAgreement_shouldSaveOrUpdateSuccessfully() {
        // Arrange
        var bodyResource = new AsyncAgreementStatusChangedResource.AsyncAgreementStatusChangedBodyResource();
        bodyResource.setPspName(ALIPAY.getName());
        bodyResource.setAgreementNo("AG123");
        bodyResource.setAgreementStatus(TERMINATED.name());
        bodyResource.setUnsignType(USER_UNSIGN.name());
        bodyResource.setUnsignTime(ZonedDateTime.now());

        AptBillDirectDebitsAgreement existingAgreement = new AptBillDirectDebitsAgreement();
        existingAgreement.setAgreementNo(bodyResource.getAgreementNo());
        existingAgreement.setAgreementStatus(ACTIVE);

        when(agreementRepository.findTopByAgreementNo(anyString())).thenReturn(existingAgreement);

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new Object());
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(null);

            // Act
            aptBillAgreementService.saveOrUpdateAgreement(bodyResource);

            // Assert
            verify(agreementRepository).save(existingAgreement);
            assertEquals(TERMINATED, existingAgreement.getAgreementStatus());
            assertEquals(USER_UNSIGN, existingAgreement.getUnsignType());
            assertEquals(bodyResource.getUnsignTime(), existingAgreement.getUnsignTime());
        }
    }

    @Test
    @DisplayName("保存/更新协议状态：支付宝协议：异常场景：协议不存在时创建新协议")
    public void saveOrUpdateAgreement_shouldCreateNewAgreementWhenNotFound() {
        // Arrange
        var bodyResource = new AsyncAgreementStatusChangedResource.AsyncAgreementStatusChangedBodyResource();
        bodyResource.setPspName(ALIPAY.getName());
        bodyResource.setAgreementNo("AG999");
        bodyResource.setAgreementStatus(ACTIVE.name());
        bodyResource.setSignTime(ZonedDateTime.now());

        when(agreementRepository.findTopByAgreementNo("AG999")).thenReturn(null);

        mockHiveRoom();

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new Object());
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(null);

            // Act
            aptBillAgreementService.saveOrUpdateAgreement(bodyResource);

            // Assert
            verify(agreementRepository).save(any(AptBillDirectDebitsAgreement.class));
        }
    }

    @Test
    @DisplayName("保存/更新协议状态：支付宝协议：正常场景：orderNo 不为空时处理")
    public void saveOrUpdateAgreement_shouldHandleOrderNoNotEmpty() {
        // Arrange
        var bodyResource = new AsyncAgreementStatusChangedResource.AsyncAgreementStatusChangedBodyResource();
        bodyResource.setPspName(ALIPAY.getName());
        bodyResource.setAgreementNo("AG456");
        bodyResource.setOrderNo("ORDER123");
        bodyResource.setAgreementStatus(ACTIVE.name());
        bodyResource.setSignTime(ZonedDateTime.now());

        when(agreementRepository.findTopByAgreementNo(anyString())).thenReturn(null);

        when(aptPaymentInfoRepository.getById(anyString())).thenReturn(new AptPaymentInfo());

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new Object());
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(null);

            // Act
            aptBillAgreementService.saveOrUpdateAgreement(bodyResource);

            // Assert
            var agreementCaptor = ArgumentCaptor.forClass(AptBillDirectDebitsAgreement.class);
            verify(agreementRepository).save(agreementCaptor.capture());
            assertNotNull(agreementCaptor.getValue());
            assertEquals(ALIPAY.getName(), agreementCaptor.getValue()
                                                          .getPspName());
        }
    }

    @Test
    @DisplayName("保存/更新协议状态：支付宝协议：异常场景：queryAgreementHiveInfo 返回 false")
    public void saveOrUpdateAgreement_shouldHandleQueryAgreementHiveInfoFalse() {
        // Arrange
        var bodyResource = new AsyncAgreementStatusChangedResource.AsyncAgreementStatusChangedBodyResource();
        bodyResource.setAgreementNo("AG789");
        bodyResource.setPspName(ALIPAY.getName());
        bodyResource.setAgreementStatus(ACTIVE.name());
        bodyResource.setSignTime(ZonedDateTime.now());

        when(agreementRepository.findTopByAgreementNo("AG789")).thenReturn(null);

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new Object());
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(null);

            // Act
            aptBillAgreementService.saveOrUpdateAgreement(bodyResource);

            // Assert
            verify(agreementRepository, never()).save(any());
        }
    }

    @Test
    @DisplayName("保存/更新协议状态：微信代扣，正常场景")
    public void saveOrUpdateAgreement_shouldCallSyncWxPayAgreementWhenPspNameIsNotAlipay() {
        // Arrange
        var bodyResource = new AsyncAgreementStatusChangedResource.AsyncAgreementStatusChangedBodyResource();
        bodyResource.setPspName(WECHATPAY.getName());
        bodyResource.setAgreementNo("AG123");
        bodyResource.setUnsignType(USER_UNSIGN.name());
        bodyResource.setAgreementStatus(TERMINATED.name());
        bodyResource.setUnsignTime(ZonedDateTime.now());

        AptBillDirectDebitsAgreement existingAgreement = new AptBillDirectDebitsAgreement();
        existingAgreement.setAgreementNo(bodyResource.getAgreementNo());
        existingAgreement.setAgreementStatus(ACTIVE);

        when(agreementRepository.findTopByAgreementNo(anyString())).thenReturn(existingAgreement);

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new Object());
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(null);

            // Act
            aptBillAgreementService.saveOrUpdateAgreement(bodyResource);

            // Assert
            verify(agreementRepository).save(existingAgreement);
            assertEquals(TERMINATED, existingAgreement.getAgreementStatus());
        }
    }

    @Test
    @DisplayName("保存/更新协议状态：微信代扣，异常场景，协议不存在时创建新协议")
    public void saveOrUpdateAgreement_shouldCreateNewAgreementWhenPspNameIsNotAlipayAndAgreementNotFound() {
        // Arrange
        var bodyResource = new AsyncAgreementStatusChangedResource.AsyncAgreementStatusChangedBodyResource();
        bodyResource.setPspName(WECHATPAY.getName());
        bodyResource.setAgreementNo("AG999");
        bodyResource.setAgreementStatus(ACTIVE.name());
        bodyResource.setSignTime(ZonedDateTime.now());

        when(agreementRepository.findTopByAgreementNo("AG999")).thenReturn(null);

        mockHiveRoom();

        var customerUserResource = new CustomerUserResource();
        customerUserResource.setPhoneNumber(randomString());
        when(profileClient.getCustomerUserById(any())).thenReturn(new RespWrapVo<>(new CustomerUserResource()));

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new Object());
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(null);

            // Act
            aptBillAgreementService.saveOrUpdateAgreement(bodyResource);

            // Assert
            verify(agreementRepository).save(any(AptBillDirectDebitsAgreement.class));
        }
    }

    @Test
    @DisplayName("正常场景：查询协议成功")
    public void queryAgreements_shouldReturnAgreementsSuccessfully() {
        // Arrange
        AptBillDirectDebitsAgreement agreement = new AptBillDirectDebitsAgreement();
        agreement.setAgreementNo("AG123");
        agreement.setAgreementStatus(ACTIVE);

        Pageable pageable = PageRequest.of(0, 10);
        Page<AptBillDirectDebitsAgreement> agreementPage = new PageImpl<>(List.of(agreement), pageable, 1);
        when(agreementRepository.findAll(any(Predicate.class), any(Pageable.class))).thenReturn(agreementPage);

        var resource = new DirectDebitsAgreementResource();
        resource.setAgreementNo("AG123");
        resource.setAgreementStatus(ACTIVE);

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(resource);
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());

            // Act
            Page<DirectDebitsAgreementResource> result =
                    aptBillAgreementService.queryAgreements(new AptBillAgreementBo(), pageable);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getTotalElements());
            assertEquals("AG123", result.getContent()
                                        .get(0)
                                        .getAgreementNo());
            assertEquals(ACTIVE, result.getContent()
                                       .get(0)
                                       .getAgreementStatus());
        }
    }

    @Test
    @DisplayName("异常场景：查询协议时无数据")
    public void queryAgreements_shouldReturnEmptyWhenNoDataFound() {
        // Arrange
        AptBillAgreementBo bo = new AptBillAgreementBo();
        Pageable pageable = PageRequest.of(0, 10);

        Page<AptBillDirectDebitsAgreement> emptyPage = new PageImpl<>(Collections.emptyList(), pageable, 0);

        when(agreementRepository.findAll(any(Predicate.class), any(Pageable.class))).thenReturn(emptyPage);

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new DirectDebitsAgreementResource());
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());

            // Act
            Page<DirectDebitsAgreementResource> result = aptBillAgreementService.queryAgreements(bo, pageable);

            // Assert
            assertNotNull(result);
            assertEquals(0, result.getTotalElements());
        }
    }

    @Test
    @DisplayName("正常场景：根据房间ID集合查询所有ACTIVE协议")
    public void findAllActiveByRoomIdIn_shouldReturnActiveAgreements() {
        // Arrange
        List<String> roomIds = List.of("ROOM1", "ROOM2");
        AptBillDirectDebitsAgreement agreement1 = new AptBillDirectDebitsAgreement();
        agreement1.setRoomId("ROOM1");
        agreement1.setAgreementStatus(ACTIVE);

        AptBillDirectDebitsAgreement agreement2 = new AptBillDirectDebitsAgreement();
        agreement2.setRoomId("ROOM2");
        agreement2.setAgreementStatus(ACTIVE);

        when(agreementRepository.findAllByRoomIdInAndAgreementStatusAndIsDel(roomIds, ACTIVE, 0)).thenReturn(
                List.of(agreement1, agreement2));

        // Act
        List<AptBillDirectDebitsAgreement> result = aptBillAgreementService.findAllActiveByRoomIdIn(roomIds);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("ROOM1", result.get(0)
                                    .getRoomId());
        assertEquals("ROOM2", result.get(1)
                                    .getRoomId());
    }

    @Test
    @DisplayName("正常场景：根据项目ID查询所有ACTIVE协议的房间ID")
    public void queryAllActiveAgreementRooms_shouldReturnRoomIds() {
        // Arrange
        String projectId = "PROJECT1";
        AptBillDirectDebitsAgreement agreement1 = new AptBillDirectDebitsAgreement();
        agreement1.setRoomId("ROOM1");

        AptBillDirectDebitsAgreement agreement2 = new AptBillDirectDebitsAgreement();
        agreement2.setRoomId("ROOM2");

        when(agreementRepository.findAllByProjectIdAndAgreementStatusAndIsDel(projectId, ACTIVE, 0)).thenReturn(
                List.of(agreement1, agreement2));

        // Act
        Collection<String> result = aptBillAgreementService.queryAllActiveAgreementRooms(projectId);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains("ROOM1"));
        assertTrue(result.contains("ROOM2"));
    }

    @Test
    @DisplayName("正常场景：根据项目ID查询所有ACTIVE协议的房间ID和PSP")
    public void queryAllActiveAgreementRoomsAndPsp_shouldReturnRoomIdsAndPsp() {
        // Arrange
        String projectId = "PROJECT1";
        AptBillDirectDebitsAgreement agreement1 = new AptBillDirectDebitsAgreement();
        agreement1.setRoomId("ROOM1");
        agreement1.setPspName("PSP1");

        AptBillDirectDebitsAgreement agreement2 = new AptBillDirectDebitsAgreement();
        agreement2.setRoomId("ROOM2");
        agreement2.setPspName("PSP2");

        when(agreementRepository.findAllByProjectIdAndAgreementStatusAndIsDel(projectId, ACTIVE, 0)).thenReturn(
                List.of(agreement1, agreement2));

        // Act
        Map<String, String> result = aptBillAgreementService.queryAllActiveAgreementRoomsAndPsp(projectId);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("PSP1", result.get("ROOM1"));
        assertEquals("PSP2", result.get("ROOM2"));
    }

    @Test
    @DisplayName("正常场景：根据协议编号集合查询协议")
    public void findAllByAgreementNos_shouldReturnAgreements() {
        // Arrange
        Collection<String> agreementNos = List.of("AG123", "AG456");
        AptBillDirectDebitsAgreement agreement1 = new AptBillDirectDebitsAgreement();
        agreement1.setAgreementNo("AG123");

        AptBillDirectDebitsAgreement agreement2 = new AptBillDirectDebitsAgreement();
        agreement2.setAgreementNo("AG456");

        when(agreementRepository.findAllByAgreementNoIn(agreementNos)).thenReturn(List.of(agreement1, agreement2));

        // Act
        List<AptBillDirectDebitsAgreement> result = aptBillAgreementService.findAllByAgreementNos(agreementNos);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("AG123", result.get(0)
                                    .getAgreementNo());
        assertEquals("AG456", result.get(1)
                                    .getAgreementNo());
    }

    @Test
    @DisplayName("获取协议相关用户信息，正常场景：用户信息存在时返回用户资源")
    public void determineAgreementUserProfile_shouldReturnUserResourceWhenUserExists() {
        // Arrange
        String projectId = "PROJECT1";
        String roomId = "ROOM1";
        String openId = "OPENID123";
        String appId = "APP123";

        WxOpenIDResource openIDResource = new WxOpenIDResource();
        openIDResource.setOpenId(openId);
        openIDResource.setUserId("USER123");

        CustomerUserResource userResource = new CustomerUserResource();
        userResource.setId("USER123");
        userResource.setPhoneNumber("123456789");

        RespWrapVo<CustomerUserResource> userVo = new RespWrapVo<>();
        userVo.setData(userResource);
        userVo.setCode(SUCCESS.getCode());

        when(profileClient.openIdList(appId, projectId, List.of(roomId))).thenReturn(List.of(openIDResource));
        when(profileClient.getCustomerUserById("USER123")).thenReturn(userVo);

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new Object());
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());

            // Act
            Optional<CustomerUserResource> result =
                    aptBillAgreementService.determineAgreementUserProfile(projectId, roomId, openId, appId);

            // Assert
            assertTrue(result.isPresent());
            assertEquals("USER123", result.get()
                                          .getId());
            assertEquals("123456789", result.get()
                                            .getPhoneNumber());
        }
    }

    @Test
    @DisplayName("获取协议相关用户信息，异常场景：openId未匹配时返回空")
    public void determineAgreementUserProfile_shouldReturnEmptyWhenOpenIdNotMatched() {
        // Arrange
        String projectId = "PROJECT1";
        String roomId = "ROOM1";
        String openId = "OPENID999";
        String appId = "APP123";

        WxOpenIDResource openIDResource = new WxOpenIDResource();
        openIDResource.setOpenId("OPENID123");
        openIDResource.setUserId("USER123");

        when(profileClient.openIdList(appId, projectId, List.of(roomId))).thenReturn(List.of(openIDResource));

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new Object());
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());

            // Act
            Optional<CustomerUserResource> result =
                    aptBillAgreementService.determineAgreementUserProfile(projectId, roomId, openId, appId);

            // Assert
            assertTrue(result.isEmpty());
        }
    }

    @Test
    @DisplayName("获取协议相关用户信息，异常场景：用户信息不存在时返回空")
    public void determineAgreementUserProfile_shouldReturnEmptyWhenUserNotFound() {
        // Arrange
        String projectId = "PROJECT1";
        String roomId = "ROOM1";
        String openId = "OPENID123";
        String appId = "APP123";

        WxOpenIDResource openIDResource = new WxOpenIDResource();
        openIDResource.setOpenId(openId);
        openIDResource.setUserId("USER999");

        RespWrapVo<CustomerUserResource> userVo = new RespWrapVo<>();
        userVo.setData(null);
        userVo.setCode(SUCCESS.getCode());

        when(profileClient.openIdList(appId, projectId, List.of(roomId))).thenReturn(List.of(openIDResource));
        when(profileClient.getCustomerUserById("USER999")).thenReturn(userVo);

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new Object());
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());

            // Act
            Optional<CustomerUserResource> result =
                    aptBillAgreementService.determineAgreementUserProfile(projectId, roomId, openId, appId);

            // Assert
            assertTrue(result.isEmpty());
        }
    }

    @Test
    @DisplayName("异常场景：openId列表为空时返回空")
    public void determineAgreementUserProfile_shouldReturnEmptyWhenOpenIdListIsEmpty() {
        // Arrange
        String projectId = "PROJECT1";
        String roomId = "ROOM1";
        String openId = "OPENID123";
        String appId = "APP123";

        when(profileClient.openIdList(appId, projectId, List.of(roomId))).thenReturn(Collections.emptyList());

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new Object());
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());

            // Act
            Optional<CustomerUserResource> result =
                    aptBillAgreementService.determineAgreementUserProfile(projectId, roomId, openId, appId);

            // Assert
            assertTrue(result.isEmpty());
        }
    }

    void mockHiveRoom() {
        RoomResp roomResp = new RoomResp();
        BuildingSimple building = new BuildingSimple();
        building.setId("BUILDING_ID");
        building.setName("Building Name");
        roomResp.setBuilding(building);

        ProjectSimple project = new ProjectSimple();
        project.setId("PROJECT_ID");
        project.setName("Project Name");
        roomResp.setProject(project);

        FloorSimple floor = new FloorSimple();
        floor.setId("FLOOR_ID");
        floor.setName("Floor Name");
        roomResp.setFloor(floor);

        RoomRespDto room = new RoomRespDto();
        room.setId("ROOM_ID");
        room.setRoomNo("Room Name");
        roomResp.setRoom(room);

        RespWrapVo<RoomResp> roomRespRespWrapVo = new RespWrapVo<>();
        roomRespRespWrapVo.setData(roomResp);
        roomRespRespWrapVo.setCode(SUCCESS.getCode());
        when(hiveAsClient.getRoomById(any())).thenReturn(roomRespRespWrapVo);
    }

}