package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.enums.InvoiceRedflagStatusEnum;
import com.kerryprops.kip.bill.common.utils.InvoiceUtils;
import com.kerryprops.kip.bill.config.EFapiaoConfig;
import com.kerryprops.kip.bill.config.SyncJdeConfig;
import com.kerryprops.kip.bill.dao.entity.EFapiaoJDEBill;
import com.kerryprops.kip.bill.dao.impl.JDBCJdeEFapiaoBillReadRepository;
import com.kerryprops.kip.bill.dao.impl.JDBCJdeEFapiaoBillWriteBackRepository;
import com.kerryprops.kip.bill.utils.InvoiceTestUtils;
import com.kerryprops.kip.bill.webservice.vo.req.CallBackKerryInvoiceMainVo;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;

import java.math.BigDecimal;
import java.util.List;

import static com.kerryprops.kip.bill.common.constants.JdeConstants.SALES_BILL_UNREADED;
import static com.kerryprops.kip.bill.common.utils.InvoiceUtils.INVOICE_STATUS_FAILED;
import static com.kerryprops.kip.bill.common.utils.InvoiceUtils.INVOICE_STATUS_SUCCESS;
import static com.kerryprops.kip.bill.common.utils.InvoiceUtils.STATUS_CANCEL;
import static com.kerryprops.kip.bill.common.utils.InvoiceUtils.STATUS_NORMAL;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;

class EFapiaoJdeBillServiceTest extends BaseIntegrationTest {

    @Autowired
    EFapiaoConfig eFapiaoConfig;

    @Autowired
    private JDBCJdeEFapiaoBillWriteBackRepository jdbcJdeBillWriteBackRepository;

    @Autowired
    private JDBCJdeEFapiaoBillReadRepository jdbcJdeBillReadRepository;

    @Autowired
    @Qualifier("jdeJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    /**
     * 提交开票请求：异常场景，表查询报异常
     */
    @Test
    void _01_queryByCompanyCodeAndMonthAndBu_exception() {
        doThrow(new RuntimeException("test")).when(jdbcJdeBillOriginRepository)
                .queryByCompanyCodeAndMonthAndBu(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any());
        doReturn(null).when(messageClient).sendWithReplyAlicloud(ArgumentMatchers.any());

        List<EFapiaoJDEBill> eFapiaoJDEBills = eFapiaoJdeBillService
                .queryByCompanyCodeAndMonthAndBu("companyCode", "bu", "month");

        assertEquals(0, eFapiaoJDEBills.size());
    }

    /**
     * 回写JDE已读表，正常场景，无账单相关数据
     */
    @Test
    void _02_writeBackUploaded_insert_success() {
        EFapiaoJDEBill eFapiaoJDEBill = getEFapiaoJDEBill();

        // 后续都是作废/红冲场景，复用该数据做原单
        eFapiaoJdeBillService.writeBackUploaded(eFapiaoJDEBill);

        int count = jdbcJdeBillReadRepository.countBySalesbillNo(eFapiaoJDEBill);
        assertEquals(1, count);
    }

    /**
     * 回写JDE已读表，正常场景，已有账单相关数据
     */
    @Test
    void _03_writeBackUploaded_update_success() {
        EFapiaoJDEBill eFapiaoJDEBill = getEFapiaoJDEBill();

        eFapiaoJdeBillService.writeBackUploaded(eFapiaoJDEBill);

        // 执行待测功能
        int count = jdbcJdeBillReadRepository.countBySalesbillNo(eFapiaoJDEBill);

        assertEquals(1, count);
    }

    /**
     * 回写JDE发票回写表：开票失败
     */
    @Test
    void _04_writeBackInvoice2Jde_invoice_status_fail() {
        EFapiaoJDEBill eFapiaoJDEBill = getEFapiaoJDEBill();

        CallBackKerryInvoiceMainVo mainVo = InvoiceTestUtils.getCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo(getSalesbillNoByJdeBill(eFapiaoJDEBill));
        mainVo.setInvoiceStatus(INVOICE_STATUS_FAILED);

        // 执行待测功能
        eFapiaoJdeBillService.writeBackInvoice2Jde(mainVo);

        int jdbcJdeBillReadCount = jdbcJdeBillReadRepository.countBySalesbillNo(eFapiaoJDEBill);
        assertEquals(1, jdbcJdeBillReadCount);

        int jdbcJdeBillReadStatus = getJdeBillReadStatus(eFapiaoJDEBill);
        assertEquals(SALES_BILL_UNREADED, String.valueOf(jdbcJdeBillReadStatus));

        assertEquals(0, countJdeBillWriteBack(eFapiaoJDEBill, mainVo));
    }

    /**
     * 回写JDE发票回写表：开票成功，正常场景
     */
    @Test
    void _05_writeBackInvoice2Jde_invoice_status_success() {
        EFapiaoJDEBill eFapiaoJDEBill = getEFapiaoJDEBill();

        CallBackKerryInvoiceMainVo mainVo = InvoiceTestUtils.getCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo(getSalesbillNoByJdeBill(eFapiaoJDEBill));
        mainVo.setInvoiceStatus(INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(STATUS_NORMAL);
        mainVo.setRedFlag(InvoiceRedflagStatusEnum.RED_FLAG_DEFAULT.getIndexStr());

        // 执行待测功能
        eFapiaoJdeBillService.writeBackInvoice2Jde(mainVo);

        assertEquals(1, countJdeBillWriteBack(eFapiaoJDEBill, mainVo));
    }

    /**
     * 回写JDE发票回写表：开票成功，红冲场景
     */
    @Test
    void _06_writeBackInvoice2Jde_red_flag_invoice_status_success() {
        EFapiaoJDEBill eFapiaoJDEBill = getEFapiaoJDEBill();

        CallBackKerryInvoiceMainVo mainVo = InvoiceTestUtils.getCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo(getSalesbillNoByJdeBill(eFapiaoJDEBill));
        mainVo.setInvoiceStatus(INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(STATUS_NORMAL);
        mainVo.setRedFlag(InvoiceRedflagStatusEnum.RED_FLAG_DEFAULT.getIndexStr());
        mainVo.setAmountWithTax(BigDecimal.valueOf(-1 - random.nextInt(1000)));
        mainVo.setAmountWithoutTax(BigDecimal.valueOf(-1 - random.nextInt(1000)));

        // 执行待测功能
        eFapiaoJdeBillService.writeBackInvoice2Jde(mainVo);

        assertEquals(1, countJdeBillWriteBack(eFapiaoJDEBill, mainVo));
    }

    /**
     * 回写JDE发票回写表：开票成功，作废场景
     */
    @Test
    void _07_writeBackInvoice2Jde_status_cancel() {
        EFapiaoJDEBill eFapiaoJDEBill = getEFapiaoJDEBill();

        CallBackKerryInvoiceMainVo mainVo = InvoiceTestUtils.getCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo(getSalesbillNoByJdeBill(eFapiaoJDEBill));
        mainVo.setInvoiceStatus(INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(STATUS_CANCEL);

        final String JDE_STATUS_NORMAL = "1";
        jdbcJdeBillWriteBackRepository.insert(mainVo
                , InvoiceUtils.transDateFormat(mainVo.getPaperDrewDate())
                , 123064, BigDecimal.TEN
                , BigDecimal.TEN, BigDecimal.TEN, JDE_STATUS_NORMAL);

        // 执行待测功能
        eFapiaoJdeBillService.writeBackInvoice2Jde(mainVo);

        assertEquals(1, countJdeBillWriteBack(eFapiaoJDEBill, mainVo));
    }

    @BeforeAll
    void prepareTestData() {
        // 初始化JDE的SCHEMA
        jdbcTemplate.execute("CREATE SCHEMA IF NOT EXISTS " + SyncJdeConfig.getJdeDbPrefix() + ";");

        // 初始化JDE已读表
        jdbcTemplate.execute("CREATE TABLE " +
                SyncJdeConfig.getJdeBillInvoiceRead() +
                " (TOKCO NCHAR(5) NOT NULL," +
                "  TODOC NUMBER NOT NULL," +
                "  TODCT NCHAR(2) NOT NULL," +
                "  TOSFX NCHAR(3) NOT NULL," +
                "  TOFX01 NCHAR(30)," +
                "  TOAN8 NUMBER," +
                "  TOEV01 NCHAR(1)," +
                "  TOEV02 NCHAR(1)," +
                "  TOEV03 NCHAR(1)," +
                "  TOUSER NCHAR(10)," +
                "  TOPID NCHAR(10)," +
                "  TOJOBN NCHAR(10)," +
                "  TOUPMJ NUMBER(6,0)," +
                "  TOUPMT NUMBER);");
        jdbcTemplate.execute("CREATE UNIQUE INDEX CRPDTA.F55GT02_0 ON " +
                SyncJdeConfig.getJdeBillInvoiceRead() +
                " (TOKCO ASC, TODCT ASC, TODOC ASC, TOSFX ASC)");
        jdbcTemplate.execute("CREATE UNIQUE INDEX CRPDTA.F55GT02_1 ON " +
                SyncJdeConfig.getJdeBillInvoiceRead() + " (TOFX01 ASC)");
    }

    private Integer countJdeBillWriteBack(EFapiaoJDEBill eFapiaoJDEBill, CallBackKerryInvoiceMainVo mainVo) {
        String selectSqlFormat = "SELECT count(*) FROM %s" +
                " WHERE IFKCO = ? AND IFDCT = ? AND IFDOC = ? AND IFSFX = ? AND IFGUI = ? ";
        String selectSql = String.format(selectSqlFormat, SyncJdeConfig.getJdeBillInvoiceWriteBack());
        return jdbcTemplate.query(selectSql, pstmt -> {
            int paramIndex = 1;
            pstmt.setString(paramIndex++, eFapiaoJDEBill.getKco());
            pstmt.setString(paramIndex++, eFapiaoJDEBill.getBillType());
            pstmt.setInt(paramIndex++, eFapiaoJDEBill.getDoc());
            pstmt.setString(paramIndex++, eFapiaoJDEBill.getPaymentItem());
            pstmt.setString(paramIndex, mainVo.getInvoiceNo());
        }, rs -> {
            rs.next();
            return rs.getInt(1);
        });
    }

    private int getJdeBillReadStatus(EFapiaoJDEBill eFapiaoJDEBill) {
        String selectSqlFormat = "SELECT * FROM %s" +
                " WHERE TODOC = ? AND TODCT = ? AND TOKCO = ? AND TOSFX = ? ";
        String selectSql = String.format(selectSqlFormat, SyncJdeConfig.getJdeBillInvoiceRead());
        List<Integer> billReadStatus = jdbcTemplate.query(selectSql,
                pstmt -> {
                    int paramIndex = 1;
                    pstmt.setInt(paramIndex++, eFapiaoJDEBill.getDoc());
                    pstmt.setString(paramIndex++, eFapiaoJDEBill.getBillType());
                    pstmt.setString(paramIndex++, eFapiaoJDEBill.getKco());
                    pstmt.setString(paramIndex, eFapiaoJDEBill.getPaymentItem());
                }
                , (rs, rowNum) -> rs.getInt("TOEV02"));
        return billReadStatus.get(0);
    }

    private EFapiaoJDEBill getEFapiaoJDEBill() {
        return EFapiaoJDEBill.builder()
                .kco("31007")
                .billType("RD")
                .doc(19004780)
                .paymentItem("001")
                .companyCode("31007")
                .an8("10023711")
                .mcu("31074200")
                .billDrawDate("2022-08-01")
                .purchaserName(random.nextInt() + StringUtils.EMPTY)
                .taxRate(BigDecimal.valueOf(random.nextInt(10000)))
                .goodsTaxNo(random.nextInt() + StringUtils.EMPTY)
                .invoiceType("NE")
                .build();
    }

    private String getSalesbillNoByJdeBill(EFapiaoJDEBill eFapiaoJDEBill) {
        return eFapiaoJDEBill.getKco() + eFapiaoJDEBill.getBillType()
                + eFapiaoJDEBill.getDoc() + eFapiaoJDEBill.getPaymentItem();
    }

}