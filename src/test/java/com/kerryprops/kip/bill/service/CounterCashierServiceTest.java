package com.kerryprops.kip.bill.service;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillPushStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.common.enums.CashierPayStatus;
import com.kerryprops.kip.bill.common.enums.PaymentCateEnum;
import com.kerryprops.kip.bill.common.enums.PaymentPayType;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptPayConfigRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptPayConfig;
import com.kerryprops.kip.bill.webservice.vo.req.CashierAptPaySearchRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierOfflinePayRequest;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierOfflinePayResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierPaymentTransactionResource;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.Mockito.doReturn;

/**
 * <AUTHOR> 2023-11-24 12:12:01
 **/
@DisplayName("收银台杂费-集成测试")
class CounterCashierServiceTest extends BaseIntegrationTest {

    @Resource
    CounterCashierService counterCashierService;

    @Resource
    AptPayConfigRepository aptPayConfigRepository;

    @Resource
    AptBillRepository aptBillRepository;

    @Test
    @DisplayName("收银台杂费-分页查询支付信息-正常场景")
    void _01_queryCashierAptPays_ok() {
        doReturn("").when(hiveAsClient).getPropertyManagementCo(ArgumentMatchers.any());
        _03_offlinePay_ok();

        CashierAptPaySearchRequest request = new CashierAptPaySearchRequest();
        request.setProjectId("192");
        request.setRoomId("TEST_ROOM_ID");
        request.setStatus(CashierPayStatus.CANCELLED);
        request.setPayType(Collections.singletonList("现金支付"));
        request.setPayDateStart(Date.from(ZonedDateTime.now().minusDays(1).toInstant()));
        request.setPayDateEnd(new Date());

        Page<CashierPaymentTransactionResource> resources = counterCashierService.queryCashierAptPays(request, PageRequest.of(0, 10));
        Assertions.assertTrue(resources.getContent().isEmpty());

        request.setStatus(null);
        resources = counterCashierService.queryCashierAptPays(request, PageRequest.of(0, 10));

        List<CashierPaymentTransactionResource> content = resources.getContent();
        Assertions.assertFalse(content.isEmpty());
        CashierPaymentTransactionResource resource = content.get(0);
        Assertions.assertNull(resource.getStatus());
    }

    @Test
    @DisplayName("收银台杂费-导出查询支付信息-正常场景")
    void _02_exportCashierAptPays_ok() {
        doReturn("").when(hiveAsClient).getPropertyManagementCo(ArgumentMatchers.any());

        CashierAptPaySearchRequest requestMock = new CashierAptPaySearchRequest();
        requestMock.setProjectId("192");
        requestMock.setRoomId("TEST_ROOM_ID");
        requestMock.setStatus(null);
        requestMock.setPayType(Collections.singletonList("现金支付"));
        requestMock.setPayDateStart(Date.from(ZonedDateTime.now().minusDays(1).toInstant()));
        requestMock.setPayDateEnd(new Date());

        var mockHttpServletResponse = new MockHttpServletResponse();

        // assert
        assertDoesNotThrow(() -> counterCashierService.exportCashierAptPays(requestMock, mockHttpServletResponse));
        assertFalse(StringUtils.isEmpty(mockHttpServletResponse.getHeader("Content-disposition")));
    }

    @Test
    @DisplayName("收银台杂费-离线支付-正常场景")
    void _03_offlinePay_ok() {
        String projectId = "192";
        String buildingId = "32014100";
        long configId = random.nextLong();
        double amt = random.nextDouble();

        AptBill bill = new AptBill();
        bill.setBillNo("1");
        bill.setCategory("1");
        bill.setBu("1");
        bill.setUnit("1");
        bill.setAn8("1");
        bill.setAlph("1");
        bill.setDoco("1");
        bill.setBeginDate(new Date());
        bill.setEndDate(new Date());
        bill.setYear(2023);
        bill.setMonth(11);
        bill.setBillMonth(11);
        bill.setAmt(amt);
        bill.setStatus(BillStatus.CASHIER_PAID);
        bill.setPaymentStatus(BillPaymentStatus.TO_BE_PAID);
        bill.setPaymentResult("");
        bill.setPushStatus(BillPushStatus.TO_BE_PUSHED);
        bill.setProjectId("");
        bill.setBuildingId("");
        bill.setFloorId("");
        bill.setRoomId("");
        bill.setRdGlc("");
        bill.setPositionItem(new PositionItemResponse());
        bill.setPaySession("");
        bill.setDeletedAt(0);
        bill.setPayTime(new Date());
        bill.setCreateTime(new Date());
        bill.setUpdateTime(new Date());

        aptBillRepository.save(bill);

        AptPayConfig payConfig = new AptPayConfig();
        payConfig.setChannel(BillPayChannel.OFFLINE);
        payConfig.setPaymentType(PaymentPayType.CASH.getInfo());
        payConfig.setPaymentCate(PaymentCateEnum.A004.name());
        payConfig.setPaymentDetail(PaymentPayType.CASH.getInfo());
        payConfig.setBankAccount("");
        payConfig.setMcu("");
        payConfig.setProjectId(projectId);
        payConfig.setBuildingId(buildingId);
        payConfig.setPositionItem(new PositionItemResponse());
        payConfig.setTax(random.nextDouble());
        payConfig.setMax(random.nextDouble());
        payConfig.setComments("");
        payConfig.setCompanyCode("");
        payConfig.setDeleteAt(0L);
        payConfig.setId(configId);
        payConfig.setCreateTime(new Date());
        payConfig.setUpdateTime(new Date());

        aptPayConfigRepository.save(payConfig);

        CashierOfflinePayRequest request = new CashierOfflinePayRequest();
        request.setProjectId(projectId);
        request.setBuildingId(buildingId);
        request.setPspTransNo("");
        request.setRoomId("TEST_ROOM_ID");
        request.setPaymentTime(new Date());
        request.setPayType(PaymentPayType.CASH.getInfo());
        request.setDescription("desc");
        request.setActualAmount(BigDecimal.valueOf(amt));
        request.setAdvanceAmount(new BigDecimal("0"));
        request.setPaymentCate(PaymentCateEnum.A004);
        request.setBillIds(List.of(bill.getId()));

        CashierOfflinePayResource resource = counterCashierService.offlinePay(request);

        Assertions.assertTrue(resource.getPaymentInfoId().startsWith("P"));
    }

}