package com.kerryprops.kip.bill.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kerryprops.kip.bill.common.enums.InvoiceTypeEnum;
import com.kerryprops.kip.bill.common.utils.StringFormatUtils;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.dao.entity.EFapiaoBillInvoice;
import com.kerryprops.kip.bill.dao.entity.InvoiceRecord;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.clients.MessageClient;
import com.kerryprops.kip.bill.feign.entity.SendSmsV2Command;
import com.kerryprops.kip.bill.feign.entity.SmsResultVo;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.webservice.vo.req.CallBackKerryInvoiceMainVo;
import com.kerryprops.kip.hiveas.webservice.vo.resp.BuildingResponseVo;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

import static com.kerryprops.kip.bill.common.enums.InvoiceTypeEnum.CE;
import static com.kerryprops.kip.bill.common.enums.InvoiceTypeEnum.QC;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomLoginUser;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;

/**
 * AptInvoiceSmsServiceTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-04-28
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("C端-发票信息发送-单元测试")
class AptInvoiceSmsServiceTest {

    @Mock
    MessageClient unifiedMessageClient;

    @Mock
    HiveAsClient hiveAsClient;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private AptInvoiceSmsService aptInvoiceSmsService;

    @Test
    @DisplayName("数电票，异常场景，邮箱格式异常")
    void testSendSms_emailSent_shouldReturn() {
        // Arrange
        var aptPaymentInfo = buildAptPaymentInfo();

        var invoiceRecord = new InvoiceRecord();
        invoiceRecord.setEmail("<EMAIL>");
        invoiceRecord.setPhone("1234567890");
        invoiceRecord.setIssuer("Test Issuer");
        invoiceRecord.setOrderNo("Order123");

        var mainVo = new CallBackKerryInvoiceMainVo();
        mainVo.setInvoiceType(QC.getKipCode());
        mainVo.setPdfUrl("http://example.com/pdf");

        String invokedInvoiceCode = "Code123";
        String invokedInvoiceNo = "No123";
        String convId = "Conv123";

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser).thenReturn(randomLoginUser());

            // Act
            aptInvoiceSmsService.sendSms(aptPaymentInfo, invoiceRecord, mainVo, invokedInvoiceCode, invokedInvoiceNo, convId);

            // Assert
            Mockito.verify(unifiedMessageClient, never()).sendSmsV2(any(SendSmsV2Command.class));
        }
    }

    @Test
    @DisplayName("正常场景：数电票，发送短信成功")
    void testSendSms_digital_success() {
        // Arrange
        var aptPaymentInfo = buildAptPaymentInfo();

        var invoiceRecord = new InvoiceRecord();
        invoiceRecord.setEmail(null);
        invoiceRecord.setPhone("1234567890");
        invoiceRecord.setIssuer("Test Issuer");
        invoiceRecord.setOrderNo("Order123");

        String invokedInvoiceCode = "Code123";
        String invokedInvoiceNo = "No123";
        String convId = "Conv123";

        var mainVo = new CallBackKerryInvoiceMainVo();
        mainVo.setInvoiceType(QC.getCode());
        mainVo.setInvoiceNo(invokedInvoiceNo);
        mainVo.setPdfUrl("http://example.com/pdf");



        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser).thenReturn(randomLoginUser());

            Mockito.doReturn(new BuildingResponseVo()).when(hiveAsClient).getBuildingById("B1");
            Mockito.doReturn(new SmsResultVo()).when(unifiedMessageClient).sendSmsV2(any(SendSmsV2Command.class));

            // Act
            aptInvoiceSmsService.sendSms(aptPaymentInfo, invoiceRecord, mainVo, invokedInvoiceCode, invokedInvoiceNo, convId);

            // Assert
            Mockito.verify(unifiedMessageClient, Mockito.times(1)).sendSmsV2(any(SendSmsV2Command.class));
        }
    }

    @Test
    @DisplayName("正常场景：税控票，发送短信成功")
    void testSendSms_invoice_success() {
        // Arrange
        var aptPaymentInfo = buildAptPaymentInfo();

        var invoiceRecord = new InvoiceRecord();
        invoiceRecord.setEmail(null);
        invoiceRecord.setPhone("1234567890");
        invoiceRecord.setIssuer("Test Issuer");
        invoiceRecord.setOrderNo("Order123");

        String invokedInvoiceCode = "Code123";
        String invokedInvoiceNo = "No123";
        String convId = "Conv123";

        var mainVo = new CallBackKerryInvoiceMainVo();
        mainVo.setInvoiceType(CE.getKipCode());
        mainVo.setPdfUrl("http://example.com/pdf");
        mainVo.setInvoiceCode(invokedInvoiceCode);
        mainVo.setInvoiceNo(invokedInvoiceNo);

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser).thenReturn(randomLoginUser());

            Mockito.doReturn(new BuildingResponseVo()).when(hiveAsClient).getBuildingById("B1");
            Mockito.doReturn(new SmsResultVo()).when(unifiedMessageClient).sendSmsV2(any(SendSmsV2Command.class));

            // Act
            aptInvoiceSmsService.sendSms(aptPaymentInfo, invoiceRecord, mainVo, invokedInvoiceCode, invokedInvoiceNo, convId);

            // Assert
            Mockito.verify(unifiedMessageClient, Mockito.times(1)).sendSmsV2(any(SendSmsV2Command.class));
        }
    }


    @Test
    @DisplayName("异常场景：手机号为空")
    void testSendSms_phoneIsEmpty() {
        // Arrange
        var aptPaymentInfo = buildAptPaymentInfo();

        var invoiceRecord = new InvoiceRecord();
        invoiceRecord.setEmail(null);
        invoiceRecord.setPhone(null); // Phone is empty
        invoiceRecord.setIssuer("Test Issuer");
        invoiceRecord.setOrderNo("Order123");

        CallBackKerryInvoiceMainVo mainVo = new CallBackKerryInvoiceMainVo();
        mainVo.setInvoiceType(QC.getKipCode());
        mainVo.setPdfUrl("http://example.com/pdf");

        String invokedInvoiceCode = "Code123";
        String invokedInvoiceNo = "No123";
        String convId = "Conv123";

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser).thenReturn(randomLoginUser());

            // Act
            aptInvoiceSmsService.sendSms(aptPaymentInfo, invoiceRecord, mainVo, invokedInvoiceCode, invokedInvoiceNo, convId);

            // Assert
            Mockito.verify(unifiedMessageClient, never()).sendSmsV2(any(SendSmsV2Command.class));
        }
    }

    @Test
    @DisplayName("正常场景：生成短信内容成功")
    void testGetContent_success() {
        // Arrange
        var invoice1 = new EFapiaoBillInvoice();
        invoice1.setSellerName("Seller A");
        invoice1.setBizType("PROPERTY_MANAGEMENT");
        invoice1.setPdfUrl("http://example.com/pdf1");

        var invoice2 = new EFapiaoBillInvoice();
        invoice2.setSellerName("Seller A");
        invoice2.setBizType("PROPERTY_MANAGEMENT");
        invoice2.setPdfUrl("http://example.com/pdf2");

        List<EFapiaoBillInvoice> invoices = List.of(invoice1, invoice2);

        List<String> phones = List.of("1234567890");
        String projectId = "P1";

        try (MockedStatic<StringFormatUtils> mockedStaticStringFormatUtils = Mockito.mockStatic(StringFormatUtils.class)) {
            mockedStaticStringFormatUtils.when(() -> StringFormatUtils.format(Mockito.anyString(), Mockito.anyMap()))
                                         .thenAnswer(invocation -> {
                                             Map<String, Object> params = invocation.getArgument(1);
                                             return "Formatted content with URL: " + params.get("url");
                                         });

            // Act
            String result = aptInvoiceSmsService.getContent(invoices, phones, projectId);

            // Assert
            assertNotNull(result);
            assertTrue(result.contains("Formatted content with URL: http://example.com/pdf1"));
            assertTrue(result.contains("http://example.com/pdf2"));
        }
    }

    private AptPaymentInfo buildAptPaymentInfo() {
        return AptPaymentInfo.builder().id("1")
                .projectId("P1")
                .buildingId("B1")
                .build();
    }

}