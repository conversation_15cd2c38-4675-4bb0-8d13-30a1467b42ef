package com.kerryprops.kip.bill.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.config.DataMigrationConfig;
import com.kerryprops.kip.bill.dao.entity.AptBillDirectDebitsAgreement;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.feign.clients.CUserClient;
import com.kerryprops.kip.bill.feign.clients.MessageClient;
import com.kerryprops.kip.bill.feign.entity.CustomerUserResource;
import com.kerryprops.kip.bill.feign.entity.HOAFeesPayFailedReminderCommand;
import com.kerryprops.kip.bill.feign.entity.PaymentConfirmedCommand;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.AptBillAgreementService;
import com.kerryprops.kip.pmw.client.resource.AsynPaymentResultResource;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.function.Consumer;

import static com.kerryprops.kip.bill.common.enums.BillPaymentStatus.DIRECT_DEBIT_PAID;
import static com.kerryprops.kip.bill.common.enums.BillPaymentStatus.PAID;
import static com.kerryprops.kip.bill.common.enums.PaymentPayType.ALIPAY;
import static com.kerryprops.kip.bill.common.enums.PaymentPayType.WECHAT;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomLoginUser;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomString;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyCollection;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * AptBillWxTemplateMsgServiceImplTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-04-16
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("C端-微信支付-微信模板消息-单元测试")
class AptBillWxTemplateMsgServiceImplTest {

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private DataMigrationConfig dm;

    @Mock
    private MessageClient messageClient;

    @Mock
    private CUserClient cUserClient;

    @Mock
    private AptBillAgreementService aptBillAgreementService;

    @InjectMocks
    private AptBillWxTemplateMsgServiceImpl aptBillWxTemplateMsgService;

    @Test
    @DisplayName("发送微信模板消息，代扣场景，正常场景：支付结果通知发送成功")
    public void sendPaymentResultNotice_direct_debit_shouldSendNotificationSuccessfully() {
        // Arrange
        when(aptBillAgreementService.findAllByAgreementNos(anyList())).thenReturn(null);

        var aptPaymentInfo = new AptPaymentInfo();
        aptPaymentInfo.setId("PAY123");
        aptPaymentInfo.setProjectId("PROJECT1");
        aptPaymentInfo.setBuildingId("BUILDING1");
        aptPaymentInfo.setPayType(WECHAT);
        aptPaymentInfo.setAgreementNo(randomString());
        aptPaymentInfo.setAmt(100.50);
        aptPaymentInfo.setPaymentTime(new Date());
        aptPaymentInfo.setPaymentStatus(DIRECT_DEBIT_PAID);

        var backResource = new AsynPaymentResultResource.AsynPaymentResultBodyResource();
        backResource.setGuestProfileId("USER123");

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new Object());
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());

            Consumer<PaymentConfirmedCommand> consumer = paymentConfirmedCommand -> {
                paymentConfirmedCommand.setRemark(randomString());
            };

            // Act
            aptBillWxTemplateMsgService.sendPaymentResultNotice(aptPaymentInfo, backResource, consumer);

            // Assert
            verify(messageClient, times(1)).sendPaymentResultWxMessage(any(PaymentConfirmedCommand.class));
        }
    }

    @Test
    @DisplayName("发送微信模板消息，正常用户支付场景，正常场景：支付结果通知发送成功")
    public void sendPaymentResultNotice_pay_shouldSendNotificationSuccessfully() {
        // Arrange
        var customerUserResource = new CustomerUserResource();
        customerUserResource.setId(randomString());
        when(cUserClient.queryUserByCode(anyInt())).thenReturn(new RespWrapVo<>(customerUserResource));

        var aptPaymentInfo = new AptPaymentInfo();
        aptPaymentInfo.setId("PAY123");
        aptPaymentInfo.setProjectId("PROJECT1");
        aptPaymentInfo.setBuildingId("BUILDING1");
        aptPaymentInfo.setBindUserId(123L);
        aptPaymentInfo.setPayType(WECHAT);
        aptPaymentInfo.setAgreementNo(randomString());
        aptPaymentInfo.setAmt(100.50);
        aptPaymentInfo.setPaymentTime(new Date());
        aptPaymentInfo.setPaymentStatus(PAID);

        var backResource = new AsynPaymentResultResource.AsynPaymentResultBodyResource();
        backResource.setGuestProfileId("USER123");

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new Object());
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());

            Consumer<PaymentConfirmedCommand> consumer = paymentConfirmedCommand -> {
                paymentConfirmedCommand.setRemark(randomString());
            };

            // Act
            aptBillWxTemplateMsgService.sendPaymentResultNotice(aptPaymentInfo, backResource, consumer);

            // Assert
            verify(messageClient, times(1)).sendPaymentResultWxMessage(any(PaymentConfirmedCommand.class));
        }
    }

    @Test
    @DisplayName("正常场景：微信代扣失败消息发送成功")
    public void sendDirectPaymentFailedMsg_shouldSendWxFailedMessageSuccessfully() {
        // Arrange
        AptPaymentInfo aptPaymentInfo = buildAptPaymentInfo();
        aptPaymentInfo.setPayType(WECHAT);

        var backResource = new AsynPaymentResultResource.AsynPaymentResultBodyResource();
        backResource.setGuestProfileId("USER123");

        var agreement = new AptBillDirectDebitsAgreement();
        agreement.setUserPhoneNumber("123456789");

        when(aptBillAgreementService.findAllByAgreementNos(List.of("AG123"))).thenReturn(List.of(agreement));

        when(dm.getPayFailedMsgUrl()).thenReturn("roomId=ROOM_ID");
        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new Object());
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());

            // Act
            aptBillWxTemplateMsgService.sendDirectPaymentFailedMsg(aptPaymentInfo, backResource);

            // Assert
            verify(messageClient, times(1)).sendPaymentFailedWxMessage(any(HOAFeesPayFailedReminderCommand.class));
        }
    }

    @Test
    @DisplayName("异常场景：支付宝代扣失败消息发送成功")
    public void sendDirectPaymentFailedMsg_shouldSendAlipayFailedMessageSuccessfully() {
        // Arrange
        AptPaymentInfo aptPaymentInfo = buildAptPaymentInfo();
        aptPaymentInfo.setPayType(ALIPAY);

        when(dm.getPayFailedMsgUrl()).thenReturn("roomId=ROOM_ID");

        var backResource = new AsynPaymentResultResource.AsynPaymentResultBodyResource();
        backResource.setGuestEmail("<EMAIL>");

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new Object());
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());

            // Act
            aptBillWxTemplateMsgService.sendDirectPaymentFailedMsg(aptPaymentInfo, backResource);

            // Assert
            verify(messageClient, times(1)).sendPaymentFailedWxMessage(any(HOAFeesPayFailedReminderCommand.class));
        }
    }

    @Test
    @DisplayName("异常场景：协议号未匹配时发送失败消息")
    public void sendDirectPaymentFailedMsg_shouldHandleMissingAgreement() {
        // Arrange
        AptPaymentInfo aptPaymentInfo = buildAptPaymentInfo();
        aptPaymentInfo.setPayType(WECHAT);

        var backResource = new AsynPaymentResultResource.AsynPaymentResultBodyResource();
        backResource.setGuestProfileId("USER123");

        when(aptBillAgreementService.findAllByAgreementNos(anyCollection())).thenReturn(Collections.emptyList());

        when(dm.getPayFailedMsgUrl()).thenReturn("roomId=ROOM_ID");

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new Object());
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());

            // Act
            aptBillWxTemplateMsgService.sendDirectPaymentFailedMsg(aptPaymentInfo, backResource);

            // Assert
            verify(messageClient, times(1)).sendPaymentFailedWxMessage(any(HOAFeesPayFailedReminderCommand.class));
        }
    }

    private AptPaymentInfo buildAptPaymentInfo() {
        AptPaymentInfo aptPaymentInfo = new AptPaymentInfo();
        aptPaymentInfo.setId("PAY123");
        aptPaymentInfo.setProjectId("PROJECT1");
        aptPaymentInfo.setBuildingId("BUILDING1");
        aptPaymentInfo.setPayType(ALIPAY);
        aptPaymentInfo.setAgreementNo("AG123");
        aptPaymentInfo.setAmt(200.00);
        aptPaymentInfo.setPaymentTime(new Date());
        aptPaymentInfo.setRoomId("ROOM1");
        return aptPaymentInfo;
    }


}