package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.constants.BillConstants;
import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.utils.DateUtils;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.BillRepository;
import com.kerryprops.kip.bill.dao.entity.BillEntity;
import com.kerryprops.kip.bill.feign.entity.TenantBillConfigResponse;
import com.kerryprops.kip.bill.feign.entity.TenantManagerItemResponse;
import com.kerryprops.kip.hiveas.common.enums.EnterStatusEnum;
import com.kerryprops.kip.hiveas.common.enums.TenantStatus;
import com.kerryprops.kip.hiveas.feign.dto.BuildingDTO;
import com.kerryprops.kip.hiveas.feign.dto.resp.BuildingRespDto;
import com.kerryprops.kip.hiveas.feign.dto.resp.TenantRespDto;
import com.kerryprops.kip.hiveas.webservice.vo.resp.BuildingVo;
import com.kerryprops.kip.hiveas.webservice.vo.resp.CenterRespVo;
import com.kerryprops.kip.hiveas.webservice.vo.resp.ProjectBuildingVO;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.mockito.Mockito.doReturn;

class API_syncFromProfileService_BillSendConfigServiceImplTest extends BaseIntegrationTest {

    private final String PROJECT_ID_1 = "PP1";

    private final String PROJECT_ID_2 = "PP2";

    @Autowired
    private BillRepository billRepository;

    @Autowired
    private BillSendConfigServiceImpl impl;

    private List<BillEntity> dataList = null;

    /*@Test
    void _01_syncFromProfileService_01_nullProjectId() {
        impl.syncFromProfileService(null);
    }

    @Test
    void _01_syncFromProfileService_01_emptyProjectId() {
        impl.syncFromProfileService(StringUtils.EMPTY);
    }

    @Test
    void _01_syncFromProfileService_02_hiveAsClient_getCenterByIds_return_null() {
        doReturn(null).when(hiveAsClient).getCenterByIds(ArgumentMatchers.any());
        impl.syncFromProfileService(PROJECT_ID_1);
    }

    @Test
    void _01_syncFromProfileService_02_hiveAsClient_getCenterByIds_return_empty_data() {
        doReturn(Collections.emptyList()).when(hiveAsClient).getCenterByIds(ArgumentMatchers.any());
        impl.syncFromProfileService(PROJECT_ID_1);
    }

    @Test
    void _01_syncFromProfileService_03_query_empty_doco_payers() {
        doReturn(buildHiveAsClientResponse_getCenterIds(PROJECT_ID_2)).when(hiveAsClient).getCenterByIds(ArgumentMatchers.any());
        impl.syncFromProfileService(PROJECT_ID_2);
    }

    @Test
    void _01_syncFromProfileService_04_hiveAsClient_getTenantIdByDoCo_return_null() {
        doReturn(buildHiveAsClientResponse_getCenterIds(PROJECT_ID_1)).when(hiveAsClient).getCenterByIds(ArgumentMatchers.any());
        doReturn(null).when(hiveAsClient).getTenantIdByDoCo(ArgumentMatchers.anyList());
        impl.syncFromProfileService(PROJECT_ID_1);
    }

    @Test
    void _01_syncFromProfileService_04_hiveAsClient_getTenantIdByDoCo_return_error() {
        doReturn(buildHiveAsClientResponse_getCenterIds(PROJECT_ID_1)).when(hiveAsClient).getCenterByIds(ArgumentMatchers.any());
        doReturn(new RespWrapVo<>(RespCodeEnum.UNKNOWN_ERROR)).when(hiveAsClient).getTenantIdByDoCo(ArgumentMatchers.anyList());
        impl.syncFromProfileService(PROJECT_ID_1);
    }

    @Test
    void _01_syncFromProfileService_04_hiveAsClient_getTenantIdByDoCo_return_null_data() {
        doReturn(buildHiveAsClientResponse_getCenterIds(PROJECT_ID_1)).when(hiveAsClient).getCenterByIds(ArgumentMatchers.any());
        doReturn(new RespWrapVo<>()).when(hiveAsClient).getTenantIdByDoCo(ArgumentMatchers.anyList());
        impl.syncFromProfileService(PROJECT_ID_1);
    }

    @Test
    void _01_syncFromProfileService_04_hiveAsClient_getTenantIdByDoCo_return_empty_data() {
        doReturn(buildHiveAsClientResponse_getCenterIds(PROJECT_ID_1)).when(hiveAsClient).getCenterByIds(ArgumentMatchers.any());
        doReturn(new RespWrapVo<>(Collections.emptySet())).when(hiveAsClient).getTenantIdByDoCo(ArgumentMatchers.anyList());
        impl.syncFromProfileService(PROJECT_ID_1);
    }

    @Test
    void _01_syncFromProfileService_04_hiveAsClient_getTenantIdByDoCo_return_expiredTenant() {
        doReturn(buildHiveAsClientResponse_getCenterIds(PROJECT_ID_1)).when(hiveAsClient).getCenterByIds(ArgumentMatchers.any());
        doReturn(buildHiveAsClientResponse_getTenantIdByDoCo(TenantStatus.DISABLE)).when(hiveAsClient).getTenantIdByDoCo(ArgumentMatchers.anyList());
        impl.syncFromProfileService(PROJECT_ID_1);
    }

    @Test
    void _01_syncFromProfileService_05_sUserClient_return_null() {
        doReturn(buildHiveAsClientResponse_getCenterIds(PROJECT_ID_1)).when(hiveAsClient).getCenterByIds(ArgumentMatchers.any());
        doReturn(buildHiveAsClientResponse_getTenantIdByDoCo(TenantStatus.ENABLE)).when(hiveAsClient).getTenantIdByDoCo(ArgumentMatchers.anyList());
        doReturn(null).when(sUserClient).queryJdeNotifyPersons(ArgumentMatchers.any());
        impl.syncFromProfileService(PROJECT_ID_1);
    }

    @Test
    void _01_syncFromProfileService_05_sUserClient_return_error() {
        doReturn(buildHiveAsClientResponse_getCenterIds(PROJECT_ID_1)).when(hiveAsClient).getCenterByIds(ArgumentMatchers.any());
        doReturn(buildHiveAsClientResponse_getTenantIdByDoCo(TenantStatus.ENABLE)).when(hiveAsClient).getTenantIdByDoCo(ArgumentMatchers.anyList());
        doReturn(new RespWrapVo<>(RespCodeEnum.UNKNOWN_ERROR)).when(sUserClient).queryJdeNotifyPersons(ArgumentMatchers.any());
        impl.syncFromProfileService(PROJECT_ID_1);
    }

    @Test
    void _01_syncFromProfileService_05_sUserClient_return_null_Data() {
        doReturn(buildHiveAsClientResponse_getCenterIds(PROJECT_ID_1)).when(hiveAsClient).getCenterByIds(ArgumentMatchers.any());
        doReturn(buildHiveAsClientResponse_getTenantIdByDoCo(TenantStatus.ENABLE)).when(hiveAsClient).getTenantIdByDoCo(ArgumentMatchers.anyList());
        doReturn(new RespWrapVo<>()).when(sUserClient).queryJdeNotifyPersons(ArgumentMatchers.any());
        impl.syncFromProfileService(PROJECT_ID_1);
    }

    @Test
    void _01_syncFromProfileService_05_sUserClient_return_empty_Data() {
        doReturn(buildHiveAsClientResponse_getCenterIds(PROJECT_ID_1)).when(hiveAsClient).getCenterByIds(ArgumentMatchers.any());
        doReturn(buildHiveAsClientResponse_getTenantIdByDoCo(TenantStatus.ENABLE)).when(hiveAsClient).getTenantIdByDoCo(ArgumentMatchers.anyList());
        doReturn(new RespWrapVo<>(Collections.emptyList())).when(sUserClient).queryJdeNotifyPersons(ArgumentMatchers.any());
        impl.syncFromProfileService(PROJECT_ID_1);
    }

    @Test
    void _01_syncFromProfileService_06_sUserClient_queryLoginAccounts_return_null() {
        doReturn(buildHiveAsClientResponse_getCenterIds(PROJECT_ID_1)).when(hiveAsClient).getCenterByIds(ArgumentMatchers.any());
        doReturn(buildHiveAsClientResponse_getTenantIdByDoCo(TenantStatus.ENABLE)).when(hiveAsClient).getTenantIdByDoCo(ArgumentMatchers.anyList());
        doReturn(buildSUserClient_queryJdeNotifyPersons()).when(sUserClient).queryJdeNotifyPersons(ArgumentMatchers.any());
        doReturn(null).when(sUserClient).queryLoginAccounts(ArgumentMatchers.anyList());
        impl.syncFromProfileService(PROJECT_ID_1);
    }

    @Test
    void _01_syncFromProfileService_06_sUserClient_queryLoginAccounts_return_empty() {
        doReturn(buildHiveAsClientResponse_getCenterIds(PROJECT_ID_1)).when(hiveAsClient).getCenterByIds(ArgumentMatchers.any());
        doReturn(buildHiveAsClientResponse_getTenantIdByDoCo(TenantStatus.ENABLE)).when(hiveAsClient).getTenantIdByDoCo(ArgumentMatchers.anyList());
        doReturn(buildSUserClient_queryJdeNotifyPersons()).when(sUserClient).queryJdeNotifyPersons(ArgumentMatchers.any());
        doReturn(new HashMap<>()).when(sUserClient).queryLoginAccounts(ArgumentMatchers.anyList());
        impl.syncFromProfileService(PROJECT_ID_1);
    }

    @Test
    void _01_syncFromProfileService_06_sUserClient_queryLoginAccounts_return_valid() {
        doReturn(buildHiveAsClientResponse_getCenterIds(PROJECT_ID_1)).when(hiveAsClient).getCenterByIds(ArgumentMatchers.any());
        doReturn(buildHiveAsClientResponse_getTenantIdByDoCo(TenantStatus.ENABLE)).when(hiveAsClient).getTenantIdByDoCo(ArgumentMatchers.anyList());
        doReturn(buildSUserClient_queryJdeNotifyPersons()).when(sUserClient).queryJdeNotifyPersons(ArgumentMatchers.any());

        TenantManagerItemResponse tenantManagerItemResponse = new TenantManagerItemResponse();
        tenantManagerItemResponse.setLoginNo("********");
        tenantManagerItemResponse.setUserName("test1");
        tenantManagerItemResponse.setId(1234L);
        doReturn(Map.of("***********", tenantManagerItemResponse)).when(sUserClient).queryLoginAccounts(ArgumentMatchers.anyList());

        impl.syncFromProfileService(PROJECT_ID_1);
    }
*/
    @BeforeEach
    void prepareTestData() {
        List<BillEntity> billEntities = Arrays.asList(
                BillEntity.builder().tpEv01("Y").tpDct("D1").tpMcu("********").tpUnit("P1-B1-R1").tpDoco("112233")
                        .tpAn8("********").tpAlph("A开发公司").tpFyr(22).tpPn(1).formatDate("2022-01-05").delFlag("0")
                        .tpStatus(5).mailStatus(BillConstants.MSG_SUCCESS).emailStatus(BillConstants.MSG_NOT_SEND)
                        .mailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-05 20:23:58"))
                        .readStatus(0).tpGtfilenm("R1.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("D1").tpMcu("********").tpUnit("P1-B1-R1").tpDoco("112233")
                        .tpAn8("********").tpAlph("A开发公司").tpFyr(22).tpPn(2).formatDate("2022-02-04").delFlag("0")
                        .tpStatus(5).mailStatus(BillConstants.MSG_FAILURE).emailStatus(BillConstants.MSG_SUCCESS)
                        .mailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-02-04 10:11:22"))
                        .emailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-02-04 11:30:20"))
                        .emailErr("[{\"email\":\"<EMAIL>\",\"sendStatus\":\"发送成功\",\"sendTime\":\"2022-02-04 11:41:41\"}]")
                        .readStatus(0).tpGtfilenm("R2.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("D1").tpMcu("43255200").tpUnit("P1-B1-R2").tpDoco("112233")
                        .tpAn8("********").tpAlph("A开发公司").tpFyr(22).tpPn(1).formatDate("2022-01-07").delFlag("1")
                        .deleteBy("jane.dong")
                        .deleteTime(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-08 11:22:59"))
                        .tpStatus(BillConstants.MSG_NOT_SEND).mailStatus(BillConstants.MSG_NOT_SEND)
                        .emailStatus(BillConstants.MSG_NOT_SEND).readStatus(0).tpGtfilenm("R3.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("D2").tpMcu("43255200").tpUnit("P1-B1-R2").tpDoco("112233")
                        .tpAn8("********").tpAlph("A开发公司").tpFyr(22).tpPn(1).formatDate("2022-01-08").delFlag("0")
                        .tpStatus(5).mailStatus(BillConstants.MSG_NOT_SEND).emailStatus(BillConstants.MSG_SUCCESS)
                        .emailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-08 16:10:20"))
                        .emailErr("[{\"email\":\"<EMAIL>\",\"sendStatus\":\"发送成功\",\"sendTime\":\"2022-01-08 16:31:41\"}]")
                        .readStatus(1).tpGtfilenm("R4.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("D2").tpMcu("43255300").tpUnit("P1-B1-R3").tpDoco("112233")
                        .tpAn8("********").tpAlph("A开发公司").tpFyr(22).tpPn(1).formatDate("2022-01-18").delFlag("0")
                        .tpStatus(5).mailStatus(BillConstants.MSG_NOT_SEND).emailStatus(BillConstants.MSG_PARTIAL_SUCCESS)
                        .emailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-18 14:00:05"))
                        .emailErr("[{\"email\":\"<EMAIL>\",\"sendStatus\":\"发送成功\",\"sendTime\":\"2022-01-18 14:31:41\"}]")
                        .readStatus(0).tpGtfilenm("R5.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("DN").tpMcu("37448300").tpUnit("P1-B1-R3").tpDoco("113300")
                        .tpAn8("********").tpAlph("A物业公司").tpFyr(22).tpPn(1).formatDate("2022-01-18").delFlag("0")
                        .tpStatus(5).mailStatus(BillConstants.MSG_SUCCESS).emailStatus(BillConstants.MSG_SUCCESS)
                        .mailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-18 13:14:16"))
                        .emailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-18 14:00:02"))
                        .emailErr("[{\"email\":\"<EMAIL>\",\"sendStatus\":\"发送成功\",\"sendTime\":\"2022-01-18 14:31:41\"}]")
                        .readStatus(0).tpGtfilenm("R6.PDF").build(),

                BillEntity.builder().tpEv01("Y").tpDct("DN").tpMcu("43254100").tpUnit("P2-B1-R1").tpDoco("221133")
                        .tpAn8("87654321").tpAlph("B开发公司1").tpFyr(22).tpPn(1).formatDate("2022-01-11").delFlag("0")
                        .tpStatus(5).mailStatus(BillConstants.MSG_SUCCESS).emailStatus(BillConstants.MSG_FAILURE)
                        .mailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-18 10:20:22"))
                        .emailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-11 10:30:45"))
                        .emailErr("[{\"email\":\"<EMAIL>\",\"sendStatus\":\"发送失败\",\"sendTime\":\"2022-01-11 10:30:41\"}]")
                        .readStatus(0).tpGtfilenm("R7.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("DN").tpMcu("38467100").tpUnit("P2-B1-R1").tpDoco("221155")
                        .tpAn8("11112222").tpAlph("B物业公司").tpFyr(22).tpPn(1).formatDate("2022-01-11").delFlag("0")
                        .tpStatus(5).mailStatus(BillConstants.MSG_SUCCESS).emailStatus(BillConstants.MSG_SENDING)
                        .mailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-11 12:01:02"))
                        .emailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-11 12:15:10"))
                        .emailErr("[{\"email\":\"<EMAIL>\",\"sendStatus\":\"发送中\",\"sendTime\":\"2022-01-11 12:15:41\"}]")
                        .readStatus(1).tpGtfilenm("R8.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("DN").tpMcu("43251100").tpUnit("P2-B1-R1").tpDoco("221144")
                        .tpAn8("87654321").tpAlph("B开发公司2").tpFyr(22).tpPn(1).formatDate("2022-01-12").delFlag("0")
                        .tpStatus(5).mailStatus(BillConstants.MSG_SUCCESS).emailStatus(BillConstants.MSG_NOT_SEND)
                        .mailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-12 10:24:30"))
                        .readStatus(0).tpGtfilenm("R9.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("DN").tpMcu("43254200").tpUnit("P2-B2-R1").tpDoco("221133")
                        .tpAn8("87654321").tpAlph("B开发公司2").tpFyr(21).tpPn(12).formatDate("2021-12-25").delFlag("0")
                        .tpStatus(BillConstants.MSG_NOT_SEND).mailStatus(BillConstants.MSG_NOT_SEND)
                        .emailStatus(BillConstants.MSG_NOT_SEND).readStatus(0).tpGtfilenm("R10.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("DN").tpMcu("38467200").tpUnit("P2-B2-R1").tpDoco("221155")
                        .tpAn8("11112222").tpAlph("B物业公司").tpFyr(21).tpPn(12).formatDate("2021-12-24").delFlag("0")
                        .tpStatus(BillConstants.MSG_NOT_SEND).mailStatus(BillConstants.MSG_NOT_SEND)
                        .emailStatus(BillConstants.MSG_NOT_SEND).readStatus(0).tpGtfilenm("R11.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("DN").tpMcu("43251200").tpUnit("P2-B2-R1").tpDoco("221144")
                        .tpAn8("87654321").tpAlph("B开发公司2").tpFyr(21).tpPn(12).formatDate("2021-12-16").delFlag("0")
                        .tpStatus(BillConstants.MSG_NOT_SEND).mailStatus(BillConstants.MSG_NOT_SEND)
                        .emailStatus(BillConstants.MSG_NOT_SEND).readStatus(1).tpGtfilenm("R12.PDF").build(),

                BillEntity.builder().tpEv01("Y").tpDct("DN").tpMcu("43158100").tpUnit("P3-B1-R1").tpDoco("331122")
                        .tpAn8("33331111").tpAlph("C公司1").tpFyr(22).tpPn(2).formatDate("2022-02-04").delFlag("1")
                        .tpStatus(BillConstants.MSG_FAILURE).mailStatus(BillConstants.MSG_FAILURE)
                        .emailStatus(BillConstants.MSG_FAILURE).readStatus(0).tpGtfilenm("R13.PDF")
                        .mailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-02-04 10:13:20"))
                        .emailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-02-04 10:15:30"))
                        .emailErr("[{\"email\":\"<EMAIL>\",\"sendStatus\":\"发送失败\",\"sendTime\":\"2022-02-04 10:31:41\"}]")
                        .build(),
                BillEntity.builder().tpEv01("Y").tpDct("DN").tpMcu("43158200").tpUnit("P3-B1-R1").tpDoco("331144")
                        .tpAn8("33332222").tpAlph("C公司2").tpFyr(22).tpPn(3).formatDate("2022-01-04").delFlag("1")
                        .deleteBy("jane.dong")
                        .deleteTime(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-02-08 11:22:59"))
                        .tpStatus(BillConstants.MSG_NOT_SEND).mailStatus(BillConstants.MSG_NOT_SEND)
                        .emailStatus(BillConstants.MSG_NOT_SEND).readStatus(0).tpGtfilenm("R14.PDF").build(),
                BillEntity.builder().tpEv01("").tpDct("DN").tpMcu("43158100").tpUnit("P3-B1-R1").tpDoco("331122")
                        .tpAn8("33331111").tpAlph("C公司1").tpFyr(22).tpPn(2).formatDate("2022-01-04").delFlag("1")
                        .tpStatus(BillConstants.MSG_NOT_SEND).mailStatus(BillConstants.MSG_NOT_SEND)
                        .emailStatus(BillConstants.MSG_NOT_SEND).readStatus(0).tpGtfilenm("R15.PDF").build()
        );
        this.dataList = billRepository.saveAll(billEntities);
    }

    @AfterEach
    void clearTestData() {
        billRepository.deleteAll(dataList);
    }

    private RespWrapVo<List<TenantBillConfigResponse>> buildSUserClient_queryJdeNotifyPersons() {
        TenantBillConfigResponse billConfigResponse = new TenantBillConfigResponse();
        billConfigResponse.setId(60L);
        billConfigResponse.setTenantId("8aaa801982d7f44b0182d82c399e0002");
        billConfigResponse.setManagerId(1382L);
        billConfigResponse.setUserName("test");
        billConfigResponse.setPhoneNumber("***********");
        billConfigResponse.setAddress("********");
        billConfigResponse.setEmail("<EMAIL>");
        billConfigResponse.setCreateTime(new Date());

        List<TenantBillConfigResponse> tenantBillConfigResponses = List.of(billConfigResponse);
        RespWrapVo<List<TenantBillConfigResponse>> respWrapVo = new RespWrapVo<>(tenantBillConfigResponses);
        return respWrapVo;
    }

    private RespWrapVo<Set<TenantRespDto>> buildHiveAsClientResponse_getTenantIdByDoCo(TenantStatus tenantStatus) {
        TenantRespDto.Room room = new TenantRespDto.Room();
        room.setId("8aaa801882d454f30182d59fddd20013");

        TenantRespDto.Contract contract = new TenantRespDto.Contract();
        contract.setStatus(tenantStatus == TenantStatus.ENABLE ?
                EnterStatusEnum.TAKE_EFFECT : EnterStatusEnum.INEFFECTIVE);
        contract.setDoco("112233");
        contract.setVersion(1);
        contract.setRoomIds(List.of(room));

        TenantRespDto tenantRespDto = new TenantRespDto();
        tenantRespDto.setId("8aaa801982d7f44b0182d82c399e0002");
        tenantRespDto.setName("前海二期T8办公测试租户一");
        tenantRespDto.setBrandName("前海二期T8办公测试租户一");
        tenantRespDto.setStatus(tenantStatus);
        tenantRespDto.setAdministrativeToiletEnabled(true);
        tenantRespDto.setUseFlexibleSpace(true);
        tenantRespDto.setSalesReported(false);
        tenantRespDto.setArea(BigDecimal.ZERO);
        tenantRespDto.setRoomIdSet(Set.of("8aaa801882d454f30182d59fddd20013"));
        tenantRespDto.setFloorIdSet(Set.of("8aaa801882d454f30182d46cb9ad0009"));
        tenantRespDto.setBuildingIdSet(Set.of("P1-B1"));
        tenantRespDto.setDoCoSet(Set.of("112233"));
        tenantRespDto.setContracts(List.of(contract));

        Set<TenantRespDto> tenantRespDtoSet = new HashSet<>();
        tenantRespDtoSet.add(tenantRespDto);

        RespWrapVo<Set<TenantRespDto>> respWrapVo = new RespWrapVo<>(tenantRespDtoSet);
        return respWrapVo;
    }

    private List<ProjectBuildingVO> buildHiveAsClientResponse_getCenterIds(String projectId) {
        List<BuildingDTO> buildingDTOs = null;
        if (PROJECT_ID_1.equalsIgnoreCase(projectId)) {
            //楼栋P1-B1的MCU列表：********,43255200,43255300,37448300
            BuildingDTO buildingDTO1 = new BuildingDTO();
            buildingDTO1.setPropertyManagementBU("********,43255200,43255300");
            buildingDTO1.setPropertyDeveloperBU("37448300");

            //楼栋P2-B1的MCU列表：43254100,38467100,43251100,
            BuildingDTO buildingDTO2 = new BuildingDTO();
            buildingDTO2.setPropertyManagementBU("43254100,43251100,");
            buildingDTO2.setPropertyDeveloperBU("38467100");

            //楼栋P2-B2的MCU列表：43254200,43251200
            BuildingDTO buildingDTO3 = new BuildingDTO();
            buildingDTO3.setPropertyManagementBU("43254200,43251200");
            buildingDTO3.setPropertyDeveloperBU("");

            buildingDTOs = List.of(buildingDTO1, buildingDTO2, buildingDTO3);
        } else if (PROJECT_ID_2.equalsIgnoreCase(projectId)) {
            //楼栋P3-B1的MCU列表：43158100,43158200
            BuildingDTO buildingDTO1 = new BuildingDTO();
            buildingDTO1.setPropertyManagementBU("43158100,43158200");
            buildingDTO1.setPropertyDeveloperBU("");

            buildingDTOs = List.of(buildingDTO1);
        }

        ProjectBuildingVO projectBuildingVO = new ProjectBuildingVO();
        projectBuildingVO.setBuildings(buildingDTOs);

        return List.of(projectBuildingVO);
    }


}
