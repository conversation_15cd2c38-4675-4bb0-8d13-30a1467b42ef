package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.config.PaymentConfigProps;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptDirectDebitsBatchBillRepository;
import com.kerryprops.kip.bill.dao.AptPaymentInfoRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptBillDirectDebitsAgreement;
import com.kerryprops.kip.bill.dao.entity.AptBillDirectDebitsBatch;
import com.kerryprops.kip.bill.dao.entity.AptDirectDebitsBatchBill;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.AptBillAgreementService;
import com.kerryprops.kip.bill.service.AptBillWxTemplateMsgService;
import com.kerryprops.kip.hiveas.feign.dto.resp.BuildingRespDto;
import com.kerryprops.kip.hiveas.webservice.vo.resp.BuildingResponseVo;
import com.kerryprops.kip.pmw.client.resource.CombinedPaymentTxOutputResource;
import com.kerryprops.kip.pmw.client.resource.DirectPayResource;
import com.kerryprops.kip.pmw.client.resource.SessionOutputResource;
import com.kerryprops.kip.pmw.client.service.PaymentClientService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.kerryprops.kip.bill.utils.RandomUtil.randomString;
import static com.kerryprops.kip.pmw.variables.PspName.ALIPAY;
import static com.kerryprops.kip.pmw.variables.PspName.WECHATPAY;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


/**
 * AptBillDirectDebitsBatchBillServiceImplTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-04-27
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("C端代扣-单元测试")
class AptBillDirectDebitsBatchBillServiceImplTest {

    @Mock
    private PaymentConfigProps paymentConfigProps;

    @Mock
    private AptBillRepository aptBillRepository;

    @Mock
    private AptPaymentInfoRepository paymentInfoRepository;

    @Mock
    private AptDirectDebitsBatchBillRepository batchBillRepository;

    @Mock
    private AptBillAgreementService agreementService;

    @Mock
    private AptBillWxTemplateMsgService templateMsgService;

    @Mock
    private PaymentClientService paymentClientService;

    @Mock
    private HiveAsClient hiveAsClient;

    @InjectMocks
    AptBillDirectDebitsBatchBillServiceImpl aptBillDirectDebitsBatchBillService;

    @DisplayName("combineBillsByRoomAndCreatePaymentInfo - 正常场景")
    @Test
    void combineBillsByRoomAndCreatePaymentInfo_normalScenario() {
        // Arrange
        AptBillDirectDebitsBatch batch = new AptBillDirectDebitsBatch();
        batch.setBatchNo("BATCH123");

        AptDirectDebitsBatchBill bill1 = new AptDirectDebitsBatchBill();
        bill1.setRoomId("ROOM1");
        bill1.setBillId(1L);

        AptDirectDebitsBatchBill bill2 = new AptDirectDebitsBatchBill();
        bill2.setRoomId("ROOM1");
        bill2.setBillId(2L);

        AptDirectDebitsBatchBill bill3 = new AptDirectDebitsBatchBill();
        bill3.setRoomId("ROOM2");
        bill3.setBillId(3L);

        List<AptDirectDebitsBatchBill> batchBills = List.of(bill1, bill2, bill3);

        AptBill aptBill1 = new AptBill();
        aptBill1.setId(1L);
        aptBill1.setAmt(100.0);
        aptBill1.setDeletedAt(0);

        AptBill aptBill2 = new AptBill();
        aptBill2.setId(2L);
        aptBill2.setAmt(200.0);
        aptBill2.setDeletedAt(0);

        AptBill aptBill3 = new AptBill();
        aptBill3.setId(3L);
        aptBill3.setAmt(300.0);
        aptBill3.setDeletedAt(0);

        when(aptBillRepository.findAllById(Set.of(1L, 2L))).thenReturn(List.of(aptBill1, aptBill2));
        when(aptBillRepository.findAllById(Set.of(3L))).thenReturn(List.of(aptBill3));


        when(agreementService.findAllActiveByRoomIdIn(anyList())).thenReturn(Collections.emptyList());

        var aptBillAgreement = new AptBillDirectDebitsAgreement();
        aptBillAgreement.setPspName(ALIPAY.getName());
        when(agreementService.getTerminatedAgreement(anyString())).thenReturn(aptBillAgreement);

        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::getKerryAccount)
                          .thenReturn("testUser");

            // Act
            aptBillDirectDebitsBatchBillService.combineBillsByRoomAndCreatePaymentInfo(batch, batchBills);

            // Assert
            verify(batchBillRepository, times(2)).saveAll(anyList());
            verify(paymentInfoRepository, times(2)).save(any(AptPaymentInfo.class));
        }
    }

    @DisplayName("combineBillsByRoomAndCreatePaymentInfo - 异常场景：无有效账单")
    @Test
    void combineBillsByRoomAndCreatePaymentInfo_noValidBills() {
        // Arrange
        AptBillDirectDebitsBatch batch = new AptBillDirectDebitsBatch();
        batch.setBatchNo("BATCH123");

        AptDirectDebitsBatchBill bill1 = new AptDirectDebitsBatchBill();
        bill1.setRoomId("ROOM1");
        bill1.setBillId(1L);

        List<AptDirectDebitsBatchBill> batchBills = List.of(bill1);

        when(aptBillRepository.findAllById(Set.of(1L))).thenReturn(Collections.emptyList());

        // Act
        aptBillDirectDebitsBatchBillService.combineBillsByRoomAndCreatePaymentInfo(batch, batchBills);

        // Assert
        verify(batchBillRepository, never()).saveAll(anyList());
        verify(paymentInfoRepository, never()).save(any(AptPaymentInfo.class));
    }

    @DisplayName("getAptBillsByPaymentOrderNo - 正常场景")
    @Test
    void getAptBillsByPaymentOrderNo_normalScenario() {
        // Arrange
        String paymentOrderNo = "ORDER123";

        AptDirectDebitsBatchBill batchBill1 = new AptDirectDebitsBatchBill();
        batchBill1.setBillId(1L);

        AptDirectDebitsBatchBill batchBill2 = new AptDirectDebitsBatchBill();
        batchBill2.setBillId(2L);

        AptBill aptBill1 = new AptBill();
        aptBill1.setId(1L);
        aptBill1.setAmt(100.0);

        AptBill aptBill2 = new AptBill();
        aptBill2.setId(2L);
        aptBill2.setAmt(200.0);

        when(batchBillRepository.findByPaymentInfoId(paymentOrderNo)).thenReturn(List.of(batchBill1, batchBill2));
        when(aptBillRepository.findAllById(Set.of(1L, 2L))).thenReturn(List.of(aptBill1, aptBill2));

        // Act
        List<AptBill> result = aptBillDirectDebitsBatchBillService.getAptBillsByPaymentOrderNo(paymentOrderNo);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(100.0, result.get(0)
                                  .getAmt());
        assertEquals(200.0, result.get(1)
                                  .getAmt());
    }

    @DisplayName("getAptBillsByPaymentOrderNo - 异常场景：paymentOrderNo为空")
    @Test
    void getAptBillsByPaymentOrderNo_emptyPaymentOrderNo() {
        // Arrange
        String paymentOrderNo = "";

        // Act
        List<AptBill> result = aptBillDirectDebitsBatchBillService.getAptBillsByPaymentOrderNo(paymentOrderNo);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @DisplayName("getAptBillsByPaymentOrderNo - 异常场景：无匹配的BatchBill")
    @Test
    void getAptBillsByPaymentOrderNo_noBatchBills() {
        // Arrange
        String paymentOrderNo = "ORDER123";

        when(batchBillRepository.findByPaymentInfoId(paymentOrderNo)).thenReturn(Collections.emptyList());

        // Act
        List<AptBill> result = aptBillDirectDebitsBatchBillService.getAptBillsByPaymentOrderNo(paymentOrderNo);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @DisplayName("getRoomBillsOfBatch - 正常场景")
    @Test
    void getRoomBillsOfBatch_normalScenario() {
        // Arrange
        AptDirectDebitsBatchBill bill1 = new AptDirectDebitsBatchBill();
        bill1.setRoomId("ROOM1");
        bill1.setBillId(1L);
        bill1.setPaymentInfoId(randomString());

        AptDirectDebitsBatchBill bill2 = new AptDirectDebitsBatchBill();
        bill2.setRoomId("ROOM2");
        bill2.setBillId(2L);
        bill2.setPaymentInfoId(randomString());

        AptDirectDebitsBatchBill bill3 = new AptDirectDebitsBatchBill();
        bill3.setRoomId("ROOM2");
        bill3.setBillId(3L);
        bill3.setPaymentInfoId(bill2.getPaymentInfoId());

        List<AptDirectDebitsBatchBill> batchBills = List.of(bill1, bill2, bill3);
        when(batchBillRepository.findByBatchNo("BATCH123")).thenReturn(batchBills);

        AptBillDirectDebitsBatch batch = new AptBillDirectDebitsBatch();
        batch.setBatchNo("BATCH123");

        // Act
        Map<String, List<AptDirectDebitsBatchBill>> result =
                aptBillDirectDebitsBatchBillService.getRoomBillsOfBatch(batch);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.containsKey(bill1.getPaymentInfoId()));
        assertTrue(result.containsKey(bill2.getPaymentInfoId()));
        assertEquals(2, result.get(bill2.getPaymentInfoId())
                              .size());
        assertEquals(1, result.get(bill1.getPaymentInfoId())
                              .size());
    }

    @DisplayName("getRoomBillsOfBatch - 异常场景：无账单数据")
    @Test
    void getRoomBillsOfBatch_noBills() {
        // Arrange
        AptBillDirectDebitsBatch batch = new AptBillDirectDebitsBatch();
        batch.setBatchNo("BATCH123");

        when(batchBillRepository.findByBatchNo("BATCH123")).thenReturn(Collections.emptyList());

        // Act
        Map<String, List<AptDirectDebitsBatchBill>> result =
                aptBillDirectDebitsBatchBillService.getRoomBillsOfBatch(batch);

        // Assert
        assertNull(result);
    }


    @DisplayName("conductRoomDirectPaymentHandler - 正常场景")
    @Test
    void conductRoomDirectPaymentHandler_normalScenario() {
        // Arrange
        String paymentInfoId = "PAYMENT123";

        AptDirectDebitsBatchBill bill1 = new AptDirectDebitsBatchBill();
        bill1.setRoomId("ROOM1");
        bill1.setBillId(1L);

        AptDirectDebitsBatchBill bill2 = new AptDirectDebitsBatchBill();
        bill2.setRoomId("ROOM1");
        bill2.setBillId(2L);

        List<AptDirectDebitsBatchBill> roomBatchBills = List.of(bill1, bill2);

        AptPaymentInfo paymentInfo = new AptPaymentInfo();
        paymentInfo.setPaymentStatus(BillPaymentStatus.DIRECT_DEBIT_PAYING);
        paymentInfo.setAmt(300.0);
        paymentInfo.setRoomId("ROOM1");

        AptBill aptBill = new AptBill();
        aptBill.setDeletedAt(0);
        aptBill.setAmt(150.0);
        aptBill.setBuildingId(randomString());

        AptBillDirectDebitsAgreement agreement = new AptBillDirectDebitsAgreement();
        agreement.setAgreementNo("AGREEMENT123");
        agreement.setPspName(ALIPAY.name());
        agreement.setUserProfileId("USER123");

        when(paymentInfoRepository.getById(paymentInfoId)).thenReturn(paymentInfo);
        when(aptBillRepository.findAllById(anySet())).thenReturn(List.of(aptBill));
        when(agreementService.findAllActiveByRoomIdIn(anyList())).thenReturn(List.of(agreement));

        var directPayResource = new DirectPayResource();
        directPayResource.setState("SUCCESS");
        var bodyResource = mockPaymentResource(directPayResource);

        var paymentOutput = new CombinedPaymentTxOutputResource(null, bodyResource, null);
        when(paymentClientService.createCombinedPaymentTransaction(anyString(), any(), any(), any(), any())).thenReturn(
                paymentOutput);

        when(paymentConfigProps.getNotifyUrl()).thenReturn(randomString());
        when(paymentConfigProps.getDirectDebitsPayTtl()).thenReturn(randomString());

        mockHiveAsClient();

        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::getKerryAccount)
                          .thenReturn("testUser");

            // Act
            aptBillDirectDebitsBatchBillService.conductRoomDirectPaymentHandler(paymentInfoId, roomBatchBills);

            // Assert
            verify(paymentInfoRepository, times(1)).save(any(AptPaymentInfo.class));
            verify(aptBillRepository, times(1)).findAllById(anySet());
            verify(paymentClientService, times(1)).createCombinedPaymentTransaction(anyString(), any(), any(), any(),
                                                                                    any());
        }
    }

    @DisplayName("conductRoomDirectPaymentHandler - Validate paymentInfoId scenarios")
    @ParameterizedTest
    @NullAndEmptySource
    void conductRoomDirectPaymentHandler_ValidatePaymentInfoIdScenarios(String paymentInfoId) {
        // Arrange
        var batchBill = new AptDirectDebitsBatchBill();
        batchBill.setBillId(1L);
        batchBill.setRoomId("ROOM1");

        List<AptDirectDebitsBatchBill> roomBatchBills = List.of(batchBill);

        var paymentInfo = new AptPaymentInfo();
        paymentInfo.setPaymentStatus(BillPaymentStatus.DIRECT_DEBIT_PAYING);
        paymentInfo.setAmt(300.0);
        paymentInfo.setRoomId("ROOM1");

        var aptBill = new AptBill();
        aptBill.setDeletedAt(0);
        aptBill.setAmt(150.0);
        aptBill.setRoomId("ROOM1");

        var agreement = new AptBillDirectDebitsAgreement();
        agreement.setAgreementNo("AGREEMENT123");
        agreement.setPspName(ALIPAY.name());
        agreement.setUserProfileId("USER123");

        var directPayResource = new DirectPayResource();
        directPayResource.setState("SUCCESS");
        directPayResource.setPspTransId("PSP123");
        directPayResource.setConfirmedPspTradeNo("TRADE123");
        var paymentResource = mockPaymentResource(directPayResource);

        when(paymentInfoRepository.getById(eq(paymentInfoId))).thenReturn(paymentInfo);
        when(aptBillRepository.findAllById(anySet())).thenReturn(List.of(aptBill));
        when(agreementService.findAllActiveByRoomIdIn(anyList())).thenReturn(List.of(agreement));
        when(paymentClientService.createCombinedPaymentTransaction(anyString(), any(), any(), any(), any()))
                .thenReturn(new CombinedPaymentTxOutputResource(null, paymentResource, null));

        mockHiveAsClient();

        // Act
        aptBillDirectDebitsBatchBillService.conductRoomDirectPaymentHandler(paymentInfoId, roomBatchBills);

        // Assert
        verify(paymentInfoRepository, times(1)).getById(paymentInfoId);
        verify(aptBillRepository, times(1)).findAllById(anySet());
        verify(agreementService, times(1)).findAllActiveByRoomIdIn(anyList());
        verify(paymentClientService, times(1)).createCombinedPaymentTransaction(anyString(), any(), any(), any(), any());
        verify(paymentInfoRepository, times(1)).save(any(AptPaymentInfo.class));
    }

    @DisplayName("conductRoomDirectPaymentHandler - 异常场景：支付状态非DIRECT_DEBIT_PAYING")
    @Test
    void conductRoomDirectPaymentHandler_invalidPaymentStatus() {
        // Arrange
        String paymentInfoId = "PAYMENT123";

        AptPaymentInfo paymentInfo = new AptPaymentInfo();
        paymentInfo.setPaymentStatus(BillPaymentStatus.DIRECT_DEBIT_FAILED);

        when(paymentInfoRepository.getById(eq(paymentInfoId))).thenReturn(paymentInfo);

        // Act
        aptBillDirectDebitsBatchBillService.conductRoomDirectPaymentHandler(paymentInfoId, Collections.emptyList());

        // Assert
        verify(paymentInfoRepository, never()).save(any(AptPaymentInfo.class));
    }

    @DisplayName("conductRoomDirectPaymentHandler - 异常场景，金额错误")
    @Test
    void conductRoomDirectPaymentHandler_amountError() {
        // Arrange
        String paymentInfoId = "PAYMENT123";

        AptDirectDebitsBatchBill bill1 = new AptDirectDebitsBatchBill();
        bill1.setRoomId("ROOM1");
        bill1.setBillId(1L);

        AptDirectDebitsBatchBill bill2 = new AptDirectDebitsBatchBill();
        bill2.setRoomId("ROOM1");
        bill2.setBillId(2L);

        List<AptDirectDebitsBatchBill> roomBatchBills = List.of(bill1, bill2);

        AptPaymentInfo paymentInfo = new AptPaymentInfo();
        paymentInfo.setPaymentStatus(BillPaymentStatus.DIRECT_DEBIT_PAYING);
        paymentInfo.setAmt(-300.0);
        paymentInfo.setRoomId("ROOM1");

        AptBill aptBill = new AptBill();
        aptBill.setDeletedAt(0);
        aptBill.setAmt(150.0);
        aptBill.setBuildingId(randomString());

        when(paymentInfoRepository.getById(paymentInfoId)).thenReturn(paymentInfo);
        when(aptBillRepository.findAllById(anySet())).thenReturn(List.of(aptBill));

        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::getKerryAccount)
                          .thenReturn("testUser");

            // Act
            aptBillDirectDebitsBatchBillService.conductRoomDirectPaymentHandler(paymentInfoId, roomBatchBills);

            // Assert
            verify(paymentInfoRepository, times(1)).save(any(AptPaymentInfo.class));
            verify(paymentClientService, never()).createCombinedPaymentTransaction(anyString(), any(), any(), any(),
                                                                                   any());
        }
    }

    @DisplayName("conductRoomDirectPaymentHandler - 异常场景，代扣协议已经失效")
    @Test
    void conductRoomDirectPaymentHandler_agreementInvalidError() {
        // Arrange
        String paymentInfoId = "PAYMENT123";

        AptDirectDebitsBatchBill bill1 = new AptDirectDebitsBatchBill();
        bill1.setRoomId("ROOM1");
        bill1.setBillId(1L);

        AptDirectDebitsBatchBill bill2 = new AptDirectDebitsBatchBill();
        bill2.setRoomId("ROOM1");
        bill2.setBillId(2L);

        List<AptDirectDebitsBatchBill> roomBatchBills = List.of(bill1, bill2);

        AptPaymentInfo paymentInfo = new AptPaymentInfo();
        paymentInfo.setPaymentStatus(BillPaymentStatus.DIRECT_DEBIT_PAYING);
        paymentInfo.setAmt(300.0);
        paymentInfo.setRoomId("ROOM1");

        AptBill aptBill = new AptBill();
        aptBill.setDeletedAt(0);
        aptBill.setAmt(150.0);
        aptBill.setBuildingId(randomString());

        AptBillDirectDebitsAgreement agreement = new AptBillDirectDebitsAgreement();
        agreement.setAgreementNo("AGREEMENT123");
        agreement.setPspName(ALIPAY.name());
        agreement.setUserProfileId("USER123");

        when(agreementService.findAllActiveByRoomIdIn(anyList())).thenReturn(Collections.emptyList());

        var tmpAgr = new AptBillDirectDebitsAgreement();
        tmpAgr.setPspName(WECHATPAY.getName());
        when(agreementService.getTerminatedAgreement(any())).thenReturn(tmpAgr);

        when(paymentInfoRepository.getById(paymentInfoId)).thenReturn(paymentInfo);
        when(aptBillRepository.findAllById(anySet())).thenReturn(List.of(aptBill));

        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::getKerryAccount)
                          .thenReturn("testUser");
            // Act
            aptBillDirectDebitsBatchBillService.conductRoomDirectPaymentHandler(paymentInfoId, roomBatchBills);

            // Assert
            verify(paymentInfoRepository, times(1)).save(any(AptPaymentInfo.class));
            verify(paymentClientService, never()).createCombinedPaymentTransaction(anyString(), any(), any(), any(),
                                                                                   any());
        }
    }

    @DisplayName("conductRoomDirectPaymentHandler - pmw反馈支付失败")
    @Test
    void conductRoomDirectPaymentHandler_pmwFeedbackPayFailed() {
        // Arrange
        String paymentInfoId = "PAYMENT123";

        AptDirectDebitsBatchBill bill1 = new AptDirectDebitsBatchBill();
        bill1.setRoomId("ROOM1");
        bill1.setBillId(1L);

        AptDirectDebitsBatchBill bill2 = new AptDirectDebitsBatchBill();
        bill2.setRoomId("ROOM1");
        bill2.setBillId(2L);

        List<AptDirectDebitsBatchBill> roomBatchBills = List.of(bill1, bill2);

        AptPaymentInfo paymentInfo = new AptPaymentInfo();
        paymentInfo.setPaymentStatus(BillPaymentStatus.DIRECT_DEBIT_PAYING);
        paymentInfo.setAmt(300.0);
        paymentInfo.setRoomId("ROOM1");

        AptBill aptBill = new AptBill();
        aptBill.setDeletedAt(0);
        aptBill.setAmt(150.0);
        aptBill.setBuildingId(randomString());

        AptBillDirectDebitsAgreement agreement = new AptBillDirectDebitsAgreement();
        agreement.setAgreementNo("AGREEMENT123");
        agreement.setPspName(ALIPAY.name());
        agreement.setUserProfileId("USER123");

        when(paymentInfoRepository.getById(paymentInfoId)).thenReturn(paymentInfo);
        when(aptBillRepository.findAllById(anySet())).thenReturn(List.of(aptBill));
        when(agreementService.findAllActiveByRoomIdIn(anyList())).thenReturn(List.of(agreement));

        var directPayResource = new DirectPayResource();
        directPayResource.setState("FAILED");
        directPayResource.setError("error1:error2");
        var bodyResource = new CombinedPaymentTxOutputResource.CombinedPaymentTxOutputBodyResource();
        bodyResource.setDirectPayResource(directPayResource);

        var sessionInfoBodyResource = new SessionOutputResource.SessionInfoBodyResource();
        sessionInfoBodyResource.setSessionId(randomString());
        var sessionDetail = new SessionOutputResource.SessionOutputBodyResource();
        sessionDetail.setSessionInfo(sessionInfoBodyResource);
        bodyResource.setSessionDetail(sessionDetail);

        var paymentOutput = new CombinedPaymentTxOutputResource(null, bodyResource, null);
        when(paymentClientService.createCombinedPaymentTransaction(anyString(), any(), any(), any(), any())).thenReturn(
                paymentOutput);

        when(paymentConfigProps.getNotifyUrl()).thenReturn(randomString());
        when(paymentConfigProps.getDirectDebitsPayTtl()).thenReturn(randomString());

        mockHiveAsClient();

        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::getKerryAccount)
                          .thenReturn("testUser");

            // Act
            aptBillDirectDebitsBatchBillService.conductRoomDirectPaymentHandler(paymentInfoId, roomBatchBills);

            // Assert
            verify(paymentInfoRepository, times(1)).save(any(AptPaymentInfo.class));
            verify(aptBillRepository, times(2)).findAllById(anySet());
            verify(paymentClientService, times(1)).createCombinedPaymentTransaction(anyString(), any(), any(), any(),
                                                                                    any());
            verify(templateMsgService, times(1)).sendDirectPaymentFailedMsg(any(), any());

        }
    }

    @DisplayName("resetAptBillToBePaidStatus - 正常场景")
    @Test
    void resetAptBillToBePaidStatus_normalScenario() {
        // Arrange
        AptDirectDebitsBatchBill batchBill1 = new AptDirectDebitsBatchBill();
        batchBill1.setBillId(1L);

        AptDirectDebitsBatchBill batchBill2 = new AptDirectDebitsBatchBill();
        batchBill2.setBillId(2L);

        List<AptDirectDebitsBatchBill> batchBills = List.of(batchBill1, batchBill2);

        AptBill aptBill1 = new AptBill();
        aptBill1.setBillNo("BILL1");
        aptBill1.setPaymentStatus(BillPaymentStatus.DIRECT_DEBIT_PAYING);

        AptBill aptBill2 = new AptBill();
        aptBill2.setBillNo("BILL2");
        aptBill2.setPaymentStatus(BillPaymentStatus.TO_BE_PAID);

        List<AptBill> aptBills = new ArrayList<>();
        aptBills.add(aptBill1);
        aptBills.add(aptBill2);

        when(aptBillRepository.findAllById(anySet())).thenReturn(aptBills);

        // Act
        aptBillDirectDebitsBatchBillService.resetAptBillToBePaidStatus(batchBills);

        // Assert
        verify(aptBillRepository, times(1)).findAllById(anySet());
        verify(aptBillRepository, times(1)).saveAll(anyList());
    }

    @DisplayName("resetAptBillToBePaidStatus - 异常场景：空的账单列表")
    @Test
    void resetAptBillToBePaidStatus_emptyBatchBills() {
        // Arrange
        List<AptDirectDebitsBatchBill> batchBills = new ArrayList<>();

        // Act
        aptBillDirectDebitsBatchBillService.resetAptBillToBePaidStatus(batchBills);

        // Assert
        verify(aptBillRepository, never()).findAllById(anySet());
        verify(aptBillRepository, never()).saveAll(anyList());
    }

    private void mockHiveAsClient() {
        var buildingRespDto = new BuildingRespDto();
        buildingRespDto.setPropertyManagementCo("32007");
        var buildingResponseVo = new BuildingResponseVo();
        buildingResponseVo.setBuilding(buildingRespDto);
        when(hiveAsClient.getBuildingById(any())).thenReturn(buildingResponseVo);
    }

    private CombinedPaymentTxOutputResource.CombinedPaymentTxOutputBodyResource mockPaymentResource(DirectPayResource directPayResource) {
        var paymentResource =
                new CombinedPaymentTxOutputResource.CombinedPaymentTxOutputBodyResource();
        paymentResource.setDirectPayResource(directPayResource);
        var sessionInfoBodyResource =
                new SessionOutputResource.SessionInfoBodyResource();
        sessionInfoBodyResource.setSessionId(randomString());

        var sessionOutputBodyResource =
                new SessionOutputResource.SessionOutputBodyResource();
        sessionOutputBodyResource.setSessionInfo(sessionInfoBodyResource);
        paymentResource.setSessionDetail(sessionOutputBodyResource);
        return paymentResource;
    }

}