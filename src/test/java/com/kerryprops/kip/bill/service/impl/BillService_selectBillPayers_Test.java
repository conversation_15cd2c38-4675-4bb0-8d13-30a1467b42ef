package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.dao.BillRepository;
import com.kerryprops.kip.bill.dao.EFapiaoBillInvoiceRepository;
import com.kerryprops.kip.bill.dao.entity.BillEntity;
import com.kerryprops.kip.bill.dao.entity.EFapiaoBillInvoice;
import com.kerryprops.kip.bill.service.IBillService;
import com.kerryprops.kip.bill.webservice.vo.resp.BillPayer;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

class BillService_selectBillPayers_Test extends BaseIntegrationTest {

    @Autowired
    private IBillService billService;

    @Autowired
    private BillRepository billRepository;

    @Autowired
    private EFapiaoBillInvoiceRepository eFapiaoBillInvoiceRepository;

    @Test
    void _01_selectBillPayers_empty() {
        Set<BillPayer> billPayers = billService.selectBillPayers(null);
        assertTrue(CollectionUtils.isEmpty(billPayers));

        billPayers = billService.selectBillPayers(random.nextInt() + "");
        assertTrue(CollectionUtils.isEmpty(billPayers));

        BillEntity bill = randomBill();
        bill = billRepository.saveAndFlush(bill);
        billPayers = billService.selectBillPayers(random.nextInt() + "");
        assertTrue(CollectionUtils.isEmpty(billPayers));
    }

    @Test
    void _02_selectBillPayers_single_payer() {
        BillEntity bill_1 = randomBill();
        bill_1 = billRepository.saveAndFlush(bill_1);
        BillEntity bill_2 = randomBill();
        billRepository.saveAndFlush(bill_2);

        Set<BillPayer> billPayers = billService.selectBillPayers(bill_1.getTpDoco());

        assertThat(billPayers)
                .isNotEmpty()
                .hasSize(1);

        BillPayer vo1 = billPayers.iterator().next();
        assertThat(vo1.getTpAlph()).isEqualTo(bill_1.getTpAlph());
        assertThat(vo1.getTpAn8()).isEqualTo(bill_1.getTpAn8());
    }

    @Test
    void _01_selectBillPayers_eFapiao_single_payer() {
        var eFapiaoBillInvoice1 = new EFapiaoBillInvoice();
        eFapiaoBillInvoice1.setAn8("an8-1");
        eFapiaoBillInvoice1.setDoco("doco");

        var eFapiaoBillInvoice2 = new EFapiaoBillInvoice();
        eFapiaoBillInvoice2.setAn8("an8-2");
        eFapiaoBillInvoice2.setDoco(eFapiaoBillInvoice1.getDoco());

        eFapiaoBillInvoiceRepository.saveAll(List.of(eFapiaoBillInvoice1, eFapiaoBillInvoice2));

        Set<BillPayer> billPayers = billService.selectBillPayers(eFapiaoBillInvoice2.getDoco());

        assertThat(billPayers.stream().filter(e -> e.getTpAn8().equals(eFapiaoBillInvoice1.getAn8())).count()).isEqualTo(1);
        assertThat(billPayers.stream().filter(e -> e.getTpAn8().equals(eFapiaoBillInvoice2.getAn8())).count()).isEqualTo(1);
    }

    @Test
    void _02_selectBillPayers_difference_an8s_multi_payers() {
        BillEntity bill_1 = randomBill();
        bill_1 = billRepository.saveAndFlush(bill_1);
        BillEntity bill_2 = randomBill();
        bill_2.setTpDoco(bill_1.getTpDoco());
        billRepository.saveAndFlush(bill_2);
        Set<BillPayer> billPayers = billService.selectBillPayers(bill_1.getTpDoco());
        assertTrue(CollectionUtils.isNotEmpty(billPayers));
        assertEquals(2, billPayers.size());
        Map<String, String> map = billPayers.stream()
                .collect(Collectors.toMap(BillPayer::getTpAn8, BillPayer::getTpAlph));
        assertEquals(bill_1.getTpAlph(), map.get(bill_1.getTpAn8()));
        assertEquals(bill_2.getTpAlph(), map.get(bill_2.getTpAn8()));
    }

    @Test
    void _02_selectBillPayers_same_an8_difference_alpha() {
        BillEntity bill_1 = randomBill();
        bill_1 = billRepository.saveAndFlush(bill_1);
        bill_1.setTpDoco("Doco_bill_1");
        bill_1.setTpAn8("An8_bill_1");

        BillEntity bill_2 = randomBill();
        bill_2.setTpDoco(bill_1.getTpDoco());
        bill_2.setTpAn8(bill_1.getTpAn8());
        bill_2.setCreateTime(new Date(bill_1.getCreateTime().getTime() + 1000));
        bill_2 = billRepository.saveAndFlush(bill_2);

        Set<BillPayer> billPayers = billService.selectBillPayers(bill_1.getTpDoco());

        assertThat(billPayers).isNotEmpty().hasSize(1);

        BillPayer vo1 = billPayers.iterator().next();
        assertThat(vo1.getTpAn8()).isEqualTo(bill_1.getTpAn8());
        assertThat(vo1.getTpAlph()).isEqualTo(bill_2.getTpAlph());
    }

    @AfterEach
    void clearTestData() {
        billRepository.deleteAll();
        eFapiaoBillInvoiceRepository.deleteAll();
    }

}
