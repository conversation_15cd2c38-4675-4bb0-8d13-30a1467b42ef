package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.enums.BillSelectMode;
import com.kerryprops.kip.bill.common.exceptions.AppException;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.config.PaymentConfigProps;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptJdeBillRepository;
import com.kerryprops.kip.bill.dao.AptPayConfigRepository;
import com.kerryprops.kip.bill.dao.AptPaymentBillRepository;
import com.kerryprops.kip.bill.dao.AptPaymentInfoRepository;
import com.kerryprops.kip.bill.dao.BillSelectConfigRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptPayConfig;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.clients.KipInvoiceClient;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.AptBillDirectDebitsBatchBillService;
import com.kerryprops.kip.bill.service.AptPayService;
import com.kerryprops.kip.bill.utils.RandomUtil;
import com.kerryprops.kip.bill.webservice.vo.req.AptPaymentVo;
import com.kerryprops.kip.bill.webservice.vo.resp.AptBillVo;
import com.kerryprops.kip.bill.webservice.vo.resp.AptPaymentInfoVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillSessionResult;
import com.kerryprops.kip.hiveas.feign.dto.resp.BuildingRespDto;
import com.kerryprops.kip.hiveas.webservice.vo.resp.BuildingResponseVo;
import com.kerryprops.kip.pmw.client.resource.SessionOutputResource;
import com.kerryprops.kip.pmw.client.service.PaymentClientService;
import com.querydsl.core.types.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import static com.kerryprops.kip.bill.common.enums.BillPaymentStatus.TO_BE_PAID;
import static com.kerryprops.kip.bill.common.enums.PaymentCateEnum.A003;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomString;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * PaymentBillServiceImplTest单元测试
 *
 * <AUTHOR>
 * @date 2024-12-13
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("C端-支付回调-账单部分-测试用例")
class PaymentBillServiceImplTest {

    @Mock
    private AptPaymentInfoRepository paymentInfoRepository;

    @Mock
    private AptPaymentBillRepository paymentBillRepository;

    @Mock
    private PaymentConfigProps paymentConfigProps;

    @Mock
    private AptBillService aptBillService;

    @Mock
    private PaymentClientService paymentClientService;

    @Mock
    private HiveAsClient hiveAsClient;

    @Mock
    private AptBillRepository aptBillRepository;

    @Mock
    private AptPayConfigRepository payConfigRepository;

    @Mock
    private AptPayService aptPayService;

    @Mock
    private AptJdeBillRepository jdeBillRepository;

    @Mock
    private KipInvoiceClient kipInvoiceClient;

    @Mock
    private AptBillDirectDebitsBatchBillService batchBillService;

    @Mock
    private BillSelectConfigRepository billSelectConfigRepository;

    @InjectMocks
    private PaymentBillServiceImpl paymentBillService;

    @Test
    @DisplayName("C端-支付-异常场景：部分账单不可支付")
    void _01_billPayment_bill_not_found_error() {
        assertThrows(AppException.class, () -> paymentBillService.billPayment(List.of(new AptPaymentVo()), true));
    }

    @Test
    @DisplayName("C端-支付-异常场景：账单金额有变化")
    void _02_billPayment_amt_changed_error() {
        var aptBillMock1 = new AptBill();
        aptBillMock1.setBillNo("bill_no_1");
        aptBillMock1.setAmt(1.1d);

        var aptBillMock2 = new AptBill();
        aptBillMock2.setBillNo("bill_no_2");
        aptBillMock2.setAmt(1.2d);
        doReturn(List.of(aptBillMock1, aptBillMock2)).when(aptBillService).queryAptBillsByBillNos(ArgumentMatchers.any());

        var aptPaymentVoMock1 = AptPaymentVo.builder().amount(aptBillMock1.getAmt()).billNo(aptBillMock1.getBillNo()).build();
        var aptPaymentVoMock2 = AptPaymentVo.builder().amount(aptBillMock2.getAmt() + 0.01d).billNo(aptBillMock2.getBillNo()).build();

        assertThrows(AppException.class, () -> paymentBillService.billPayment(List.of(aptPaymentVoMock1, aptPaymentVoMock2), true));
    }

    @Test
    @DisplayName("C端-支付-正常场景")
    void _03_billPayment_success() {
        // given
        final String PROJECT_ID_MOCK = "192_mock";
        final String BUILDING_ID_MOCK = "QHKC-A2_mock";
        var aptBillMock1 = new AptBill();
        aptBillMock1.setProjectId(PROJECT_ID_MOCK);
        aptBillMock1.setBuildingId(BUILDING_ID_MOCK);
        aptBillMock1.setBillNo("bill_no_1");
        aptBillMock1.setAmt(1.1d);

        var aptBillMock2 = new AptBill();
        aptBillMock2.setProjectId(PROJECT_ID_MOCK);
        aptBillMock2.setBuildingId(BUILDING_ID_MOCK);
        aptBillMock2.setBillNo("bill_no_2");
        aptBillMock2.setAmt(1.2d);
        doReturn(List.of(aptBillMock1, aptBillMock2)).when(aptBillService).queryAptBillsByBillNos(ArgumentMatchers.any());

        var aptPaymentVoMock1 = AptPaymentVo.builder().amount(aptBillMock1.getAmt()).billNo(aptBillMock1.getBillNo()).build();
        var aptPaymentVoMock2 = AptPaymentVo.builder().amount(aptBillMock2.getAmt()).billNo(aptBillMock2.getBillNo()).build();

        doReturn(List.of(new AptPayConfig())).when(payConfigRepository).findAll((Predicate) any());

        var buildingResponseVo = new BuildingResponseVo();
        BuildingRespDto buildingRespDto = new BuildingRespDto();
        buildingRespDto.setPropertyManagementCo(randomString());
        buildingResponseVo.setBuilding(buildingRespDto);
        doReturn(buildingResponseVo).when(hiveAsClient).getBuildingById(anyString());

        var sessionInfoBodyResource = new SessionOutputResource.SessionInfoBodyResource();
        sessionInfoBodyResource.setSessionId(randomString());
        SessionOutputResource.SessionOutputBodyResource sessionOutputBodyResource = new SessionOutputResource.SessionOutputBodyResource();
        sessionOutputBodyResource.setSessionInfo(sessionInfoBodyResource);
        SessionOutputResource sessionOutputResource = new SessionOutputResource(null, sessionOutputBodyResource, null);
        doReturn(sessionOutputResource).when(paymentClientService).createPaymentSession(anyString(), anyString(), any());

        // then
        BillSessionResult billSessionResult = paymentBillService.billPayment(List.of(aptPaymentVoMock1, aptPaymentVoMock2), true);

        // assert
        assertTrue(StringUtils.isNotEmpty(billSessionResult.getBillNo()));
        assertTrue(StringUtils.isNotEmpty(billSessionResult.getPaymentSessionId()));
    }

    @Test
    @DisplayName("C端-支付-取消支付-异常场景：账单未找到")
    void _04_cancelPayment_bill_not_found_error() {
        assertThrows(AppException.class, () -> paymentBillService.cancelPayment(randomString()));
    }

    @Test
    @DisplayName("C端-支付-取消支付-正常场景")
    void _05_cancelPayment_success() {
        // given
        var loginUserMock = new LoginUser();
        loginUserMock.setUserId(RandomUtil.randomObject(Long.class));
        UserInfoUtils.setUser(loginUserMock);

        var aptPaymentInfoMock = new AptPaymentInfo();
        aptPaymentInfoMock.setId(randomString());
        aptPaymentInfoMock.setBindUserId(loginUserMock.getUserId());
        aptPaymentInfoMock.setPaymentStatus(TO_BE_PAID);
        doReturn(Optional.of(aptPaymentInfoMock)).when(paymentInfoRepository).findById(anyString());

        doReturn(List.of(new AptBill())).when(aptBillRepository).queryAptBillByPaymentId(aptPaymentInfoMock.getId());

        // assert
        assertDoesNotThrow(() -> paymentBillService.cancelPayment(randomString()));
    }

    @Test
    @DisplayName("账单选择配置校验-任意选择模式")
    void test_validBillSelectConfig_any_bills() {
        // given
        var bills = List.of(createMockBill(1));
        doReturn(BillSelectMode.ANY_BILLS)
                .when(billSelectConfigRepository)
                .findBillSelectMode(anyString(), anyString(), anyString());

        // then
        assertDoesNotThrow(() -> paymentBillService.validBillSelectConfig(bills));
    }

    @Test
    @DisplayName("账单选择配置校验-不允许跳期选择-正常场景")
    void test_validBillSelectConfig_no_jump_success() {
        // given
        var bills = List.of(createMockBill(1), createMockBill(2));
        doReturn(BillSelectMode.BILLS_NO_JUMP_SELECT)
                .when(billSelectConfigRepository)
                .findBillSelectMode(anyString(), anyString(), anyString());
        doReturn(List.of(202401, 202402))
                .when(aptBillRepository)
                .findUnpaidBillDurations(anyString(), anyString(), anyString(), anyInt());

        // then
        assertDoesNotThrow(() -> paymentBillService.validBillSelectConfig(bills));
    }

    @Test
    @DisplayName("账单选择配置校验-不允许跳期选择-异常场景")
    void test_validBillSelectConfig_no_jump_error() {
        // given
        var bills = List.of(createMockBill(1), createMockBill(3));
        doReturn(BillSelectMode.BILLS_NO_JUMP_SELECT)
                .when(billSelectConfigRepository)
                .findBillSelectMode(anyString(), anyString(), anyString());
        doReturn(List.of(202401, 202402))
                .when(aptBillRepository)
                .findUnpaidBillDurations(anyString(), anyString(), anyString(), anyInt());

        // then
        var ex = assertThrows(AppException.class,
                () -> paymentBillService.validBillSelectConfig(bills));
        assertEquals("700011", ex.getCode());
    }

    @Test
    @DisplayName("账单选择配置校验-必须全选-正常场景")
    void test_validBillSelectConfig_all_required_success() {
        // given
        var bills = List.of(createMockBill(1), createMockBill(2));
        doReturn(BillSelectMode.ALL_BILLS_REQUIRED)
                .when(billSelectConfigRepository)
                .findBillSelectMode(anyString(), anyString(), anyString());
        doReturn(2L)
                .when(aptBillRepository)
                .countUnpaidBills(anyString(), anyString(), anyString());

        // then
        assertDoesNotThrow(() -> paymentBillService.validBillSelectConfig(bills));
    }

    @Test
    @DisplayName("账单选择配置校验-必须全选-异常场景")
    void test_validBillSelectConfig_all_required_error() {
        // given
        var bills = List.of(createMockBill(1));
        doReturn(BillSelectMode.ALL_BILLS_REQUIRED)
                .when(billSelectConfigRepository)
                .findBillSelectMode(anyString(), anyString(), anyString());
        doReturn(2L) // 总共有2个未支付账单,但只选择了1个
                .when(aptBillRepository)
                .countUnpaidBills(anyString(), anyString(), anyString());

        // then
        var ex = assertThrows(AppException.class,
                () -> paymentBillService.validBillSelectConfig(bills));
        assertEquals("700010", ex.getCode());
    }

    @DisplayName("正常场景：根据支付ID查询支付信息")
    @Test
    void testQueryPaymentInfoById_success() {
        // Arrange
        String paymentId = "PAYMENT123";
        AptPaymentInfo mockPaymentInfo = new AptPaymentInfo();
        mockPaymentInfo.setId(paymentId);
        mockPaymentInfo.setPaymentStatus(TO_BE_PAID);
        mockPaymentInfo.setAmt(100.0d);
        mockPaymentInfo.setAdvanceAmount(BigDecimal.TEN);
        mockPaymentInfo.setPaymentCate(A003);
        mockPaymentInfo.setInvoiceUrl("http://example.com/invoice.pdf");
        mockPaymentInfo.setXmlUrl("http://example.com/invoice.xml");
        mockPaymentInfo.setOfdUrl("http://example.com/invoice.ofd");

        when(paymentInfoRepository.findById(paymentId)).thenReturn(Optional.of(mockPaymentInfo));

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = mockStatic(BeanUtil.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), eq(AptBillVo.class)))
                                .thenReturn(new AptBillVo());
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), eq(AptPaymentInfoVo.class)))
                                .thenReturn(new AptPaymentInfoVo());


            // Act
            AptPaymentInfoVo result = paymentBillService.queryPaymentInfoById(paymentId);

            // Assert
            assertNotNull(result);
        }
    }

    @DisplayName("正常场景：申请发票成功")
    @Test
    void testApplyInvoice_success() {
        // Arrange
        String paymentId = "PAYMENT123";
        AptPaymentInfo mockPaymentInfo = new AptPaymentInfo();
        mockPaymentInfo.setId(paymentId);
        mockPaymentInfo.setAppliedInvoice(0);

        when(paymentInfoRepository.findById(paymentId)).thenReturn(Optional.of(mockPaymentInfo));

        // Act
        paymentBillService.applyInvoice(paymentId);

        // Assert
        verify(paymentInfoRepository, times(1)).save(mockPaymentInfo);
        assertEquals(2, mockPaymentInfo.getAppliedInvoice());
    }

    // 辅助方法用于创建测试账单
    private AptBill createMockBill(int month) {
        var bill = new AptBill();
        bill.setProjectId("P1");
        bill.setBuildingId("B1");
        bill.setRoomId("R1");
        bill.setYear(2024);
        bill.setMonth(month);
        return bill;
    }

}
