package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.DirectDebitsBatchStatus;
import com.kerryprops.kip.bill.common.exceptions.RestInvalidParamException;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.utils.PrimaryKeyUtil;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.config.PaymentConfigProps;
import com.kerryprops.kip.bill.dao.AptBillDirectDebitsBatchRepository;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptDirectDebitsBatchBillRepository;
import com.kerryprops.kip.bill.dao.AptPaymentInfoRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptBillDirectDebitsAgreement;
import com.kerryprops.kip.bill.dao.entity.AptBillDirectDebitsBatch;
import com.kerryprops.kip.bill.dao.entity.AptDirectDebitsBatchBill;
import com.kerryprops.kip.bill.dao.entity.AptPaymentTransInfo;
import com.kerryprops.kip.bill.dao.entity.AptSyncJdeJobLog;
import com.kerryprops.kip.bill.dao.entity.QAptBill;
import com.kerryprops.kip.bill.dao.entity.QAptDirectDebitsBatchBill;
import com.kerryprops.kip.bill.dao.entity.QAptPaymentInfo;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.AptBillAgreementService;
import com.kerryprops.kip.bill.service.AptBillDirectDebitsBatchBillService;
import com.kerryprops.kip.bill.service.model.s.AptBillDirectDebitsBatchBo;
import com.kerryprops.kip.bill.service.model.s.AptBillSearchReqBo;
import com.kerryprops.kip.bill.webservice.vo.req.DirectDebitsBatchesCreationRequest;
import com.kerryprops.kip.bill.webservice.vo.resp.AptPayDetailVo;
import com.kerryprops.kip.bill.webservice.vo.resp.DirectDebitsBatchBillAmountResource;
import com.kerryprops.kip.bill.webservice.vo.resp.DirectDebitsBatchBillDetailResource;
import com.kerryprops.kip.bill.webservice.vo.resp.DirectDebitsBillBatchQueryResource;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffAptBillRespVo;
import com.kerryprops.kip.hiveas.feign.dto.resp.BuildingRespDto;
import com.kerryprops.kip.hiveas.feign.dto.resp.ProjectRespDto;
import com.kerryprops.kip.hiveas.webservice.resource.resp.ProjectResp;
import com.kerryprops.kip.hiveas.webservice.vo.resp.BuildingResponseVo;
import com.kerryprops.kip.pmw.variables.PspName;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DateTimeException;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.kerryprops.kip.bill.common.enums.BillPaymentStatus.DIRECT_DEBIT_FAILED;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.AGREEMENT_NOT_EXISTS;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.BATCH_NOT_EXISTS;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.CLOSING_MONTH_FORMAT_NOT_VALID;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.DIRECT_DEBITS_BILL_NOT_FOUND;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.DIRECT_DEBITS_BILL_NOT_VALID;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.ONLY_AWAIT_BATCH_DELETABLE;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomLoginUser;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyCollection;
import static org.mockito.ArgumentMatchers.anyIterable;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * AptBillDirectDebitsBatchServiceImplTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-04-25
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("C端批量代扣-单元测试")
class AptBillDirectDebitsBatchServiceImplTest {

    @Mock
    private EntityManager entityManager;

    @Mock
    private HiveAsClient hiveAsClient;

    @Mock
    private AptBillRepository aptBillRepository;

    @Mock
    private AptBillAgreementService agreementService;

    @Mock
    private AptPaymentInfoRepository paymentInfoRepository;

    @Mock
    private AptBillDirectDebitsBatchRepository batchRepository;

    @Mock
    private AptBillDirectDebitsBatchBillService batchBillService;

    @Mock
    private PaymentConfigProps paymentConfigs;

    @Mock
    private AptDirectDebitsBatchBillRepository batchBillRepository;

    @InjectMocks
    private AptBillDirectDebitsBatchServiceImpl aptBillDirectDebitsBatchService;


    @Test
    @DisplayName("分页查询代扣账单，正常场景")
    void testQueryWithFailedAmountMapping() {
        // Arrange
        AptBillDirectDebitsBatchBo bo = new AptBillDirectDebitsBatchBo();

        var aptBillDirectDebitsBatch1 = new AptBillDirectDebitsBatch();
        aptBillDirectDebitsBatch1.setBatchNo("BATCH001");
        aptBillDirectDebitsBatch1.setBillCount(5);

        var aptBillDirectDebitsBatch2 = new AptBillDirectDebitsBatch();
        aptBillDirectDebitsBatch2.setBatchNo("BATCH002");
        aptBillDirectDebitsBatch2.setBillCount(3);

        List<AptBillDirectDebitsBatch> mockBatches = List.of(aptBillDirectDebitsBatch1, aptBillDirectDebitsBatch2);
        Page<AptBillDirectDebitsBatch> mockPage = new PageImpl<>(mockBatches, PageRequest.ofSize(10), 20);

        Map<String, Object> map = new HashMap<>();
        map.put("batchNo", "batchNo-1");
        map.put("sum_amt", BigDecimal.valueOf(800));
        List<Map<String, Object>> failedAmounts = List.of(map);

        when(batchRepository.findAll(any(Predicate.class), any(Pageable.class))).thenReturn(mockPage);
        when(batchBillRepository.queryFailedAmount(anyList())).thenReturn(failedAmounts);

        // Act
        Page<DirectDebitsBillBatchQueryResource> result =
                aptBillDirectDebitsBatchService.queryDirectDebitsBatches(bo, Pageable.ofSize(10));

        // Assert
        assertEquals(2, result.getContent()
                              .size());
        assertTrue(CollectionUtils.isNotEmpty(result.getContent()));
    }

    @Test
    @DisplayName("批量删除代扣：正常场景，已经删除场景")
    void deleteDirectDebitsBatch_alreadyDeleted_returnsBatchWithoutChanges() {
        AptBillDirectDebitsBatch batch = new AptBillDirectDebitsBatch();
        batch.setStatus(DirectDebitsBatchStatus.AWAIT);
        batch.setIsDel(1);
        when(batchRepository.findTopByBatchNo("batchNo")).thenReturn(batch);
        DirectDebitsBillBatchQueryResource result = aptBillDirectDebitsBatchService.deleteDirectDebitsBatch("batchNo");

        assertEquals(DirectDebitsBatchStatus.AWAIT, result.getStatus());
        verify(batchRepository, never()).save(any(AptBillDirectDebitsBatch.class));
    }

    @Test
    @DisplayName("批量删除代扣：正常场景")
    void deleteDirectDebitsBatch_returnsBatch() {
        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(generateLoginUser());

            AptBillDirectDebitsBatch batch = new AptBillDirectDebitsBatch();
            batch.setStatus(DirectDebitsBatchStatus.AWAIT);
            batch.setIsDel(0);
            when(batchRepository.findTopByBatchNo("batchNo")).thenReturn(batch);

            var aptDirectDebitsBatchBill1 = new AptDirectDebitsBatchBill();
            aptDirectDebitsBatchBill1.setBillId(1L);
            var aptDirectDebitsBatchBill2 = new AptDirectDebitsBatchBill();
            aptDirectDebitsBatchBill2.setBillId(2L);
            when(batchBillRepository.findByBatchNo("batchNo")).thenReturn(
                    List.of(aptDirectDebitsBatchBill1, aptDirectDebitsBatchBill2));

            when(batchRepository.save(batch)).thenAnswer(invocation -> invocation.getArgument(0));
            when(aptBillRepository.findAllById(anyIterable())).thenReturn(List.of(new AptBill()));

            // Act
            DirectDebitsBillBatchQueryResource result =
                    aptBillDirectDebitsBatchService.deleteDirectDebitsBatch("batchNo");

            // Assert
            assertEquals(DirectDebitsBatchStatus.AWAIT, result.getStatus());
            verify(batchRepository).save(any(AptBillDirectDebitsBatch.class));
            verify(aptBillRepository).saveAll(anyList());
        }
    }

    @Test
    @DisplayName("批量删除代扣：异常场景，批次已失效")
    void pushDirectDebitsBatchBills_lapsedBatch_throwsException() {
        AptBillDirectDebitsBatch batch = new AptBillDirectDebitsBatch();
        batch.setStatus(DirectDebitsBatchStatus.LAPSED);
        when(batchRepository.findTopByBatchNo("batchNo")).thenReturn(batch);
        assertThrows(RestInvalidParamException.class,
                     () -> aptBillDirectDebitsBatchService.pushDirectDebitsBatchBills("batchNo"));
    }

    @Test
    @DisplayName("批量删除代扣：异常场景，批次已删除")
    void pushDirectDebitsBatchBills_noBillsInBatch_throwsException() {
        AptBillDirectDebitsBatch batch = new AptBillDirectDebitsBatch();
        batch.setStatus(DirectDebitsBatchStatus.AWAIT);
        when(batchRepository.findTopByBatchNo("batchNo")).thenReturn(batch);
        when(batchBillRepository.findByBatchNo("batchNo")).thenReturn(Collections.emptyList());
        assertThrows(RestInvalidParamException.class,
                     () -> aptBillDirectDebitsBatchService.pushDirectDebitsBatchBills("batchNo"));
    }

    @Test
    @DisplayName("创建直接借记批次-无活动协议房间时抛出异常")
    void createDirectDebitsBatch_NoActiveRooms_ThrowsException() {
        // Arrange
        DirectDebitsBatchesCreationRequest request = new DirectDebitsBatchesCreationRequest();
        request.setProjectId("project123");
        request.setClosingMonth("202310");

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {

            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(generateLoginUser());
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new AptPayDetailVo(), new StaffAptBillRespVo(), new StaffAptBillRespVo());
            when(agreementService.queryAllActiveAgreementRoomsAndPsp(request.getProjectId())).thenReturn(
                    Collections.emptyMap());

            // Act & Assert
            RestInvalidParamException exception = assertThrows(RestInvalidParamException.class,
                                                               () -> aptBillDirectDebitsBatchService.createDirectDebitsBatch(
                                                                       request));
            assertEquals(AGREEMENT_NOT_EXISTS.getCode(), exception.getCode());
        }
    }

    @Test
    @DisplayName("创建直接借记批次-无符合条件的账单时抛出异常")
    void createDirectDebitsBatch_NoBillsFound_ThrowsException() {
        // Arrange
        DirectDebitsBatchesCreationRequest request = new DirectDebitsBatchesCreationRequest();
        request.setProjectId("project123");
        request.setClosingMonth("202310");

        Map<String, String> roomPspMap = new HashMap<>();
        roomPspMap.put("room1", "alipay");

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            // Arrange
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(generateLoginUser());
            when(agreementService.queryAllActiveAgreementRoomsAndPsp(request.getProjectId())).thenReturn(roomPspMap);
            when(aptBillRepository.findAll(any(Predicate.class))).thenReturn(Collections.emptyList());

            // Act & Assert
            RestInvalidParamException exception = assertThrows(RestInvalidParamException.class,
                                                               () -> aptBillDirectDebitsBatchService.createDirectDebitsBatch(
                                                                       request));
            assertEquals(DIRECT_DEBITS_BILL_NOT_FOUND.getCode(), exception.getCode());
        }
    }

    @Test
    @DisplayName("创建直接借记批次-成功创建批次并保存相关数据")
    void createDirectDebitsBatch_Success() throws Exception {
        // Arrange
        DirectDebitsBatchesCreationRequest request = new DirectDebitsBatchesCreationRequest();
        request.setProjectId("project123");
        request.setClosingMonth("202310");

        Map<String, String> roomPspMap = new HashMap<>();
        roomPspMap.put("room1", "alipay");
        roomPspMap.put("room2", "wechat");

        AptBill bill1 = new AptBill();
        bill1.setId(1L);
        bill1.setRoomId("room1");
        bill1.setBuildingId("building1");
        bill1.setPaymentStatus(BillPaymentStatus.TO_BE_PAID);

        AptBill bill2 = new AptBill();
        bill2.setId(2L);
        bill2.setRoomId("room2");
        bill2.setBuildingId("building2");
        bill2.setPaymentStatus(BillPaymentStatus.TO_BE_PAID);

        ProjectResp projectResp = new ProjectResp();
        ProjectRespDto projectDto = new ProjectRespDto();
        projectDto.setName("Test Project");
        projectResp.setProject(projectDto);

        var buildingResponse1 = new BuildingResponseVo();
        var building1 = new BuildingRespDto();
        building1.setPropertyManagementCo("CO1");
        buildingResponse1.setBuilding(building1);

        var buildingResponse2 = new BuildingResponseVo();
        var building2 = new BuildingRespDto();
        building2.setPropertyManagementCo("CO2");
        buildingResponse2.setBuilding(building2);

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class);
             MockedStatic<PrimaryKeyUtil> mockedPrimaryKey = Mockito.mockStatic(PrimaryKeyUtil.class)) {

            // Arrange
            when(agreementService.queryAllActiveAgreementRoomsAndPsp(request.getProjectId())).thenReturn(roomPspMap);
            when(aptBillRepository.findAll(any(Predicate.class))).thenReturn(List.of(bill1, bill2));
            when(hiveAsClient.getProjectById(request.getProjectId())).thenReturn(new RespWrapVo<>(projectResp));
            when(hiveAsClient.getBuildingById("building1")).thenReturn(buildingResponse1);
            when(hiveAsClient.getBuildingById("building2")).thenReturn(buildingResponse2);

            AptBillDirectDebitsBatch savedBatch = new AptBillDirectDebitsBatch();
            savedBatch.setBatchNo("BATCH_123");
            when(batchRepository.save(any())).thenReturn(savedBatch);

            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(generateLoginUser());
            mockedPrimaryKey.when(() -> PrimaryKeyUtil.primaryKey(any(), any()))
                            .thenReturn("BATCH_123");
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new AptPayDetailVo(), new StaffAptBillRespVo(), new StaffAptBillRespVo());

            // Act
            DirectDebitsBillBatchQueryResource result =
                    aptBillDirectDebitsBatchService.createDirectDebitsBatch(request);

            // Assert
            assertNotNull(result);
            assertEquals("BATCH_123", result.getBatchNo());

            verify(batchRepository).save(any());
            verify(batchBillRepository).saveAll(anyCollection());
            verify(aptBillRepository).saveAll(anyCollection());
        }
    }

    @Test
    @DisplayName("创建直接借记批次-楼宇未配置公司时抛出异常")
    void createDirectDebitsBatch_BuildingWithoutCo_ThrowsException() {
        // Arrange
        DirectDebitsBatchesCreationRequest request = new DirectDebitsBatchesCreationRequest();
        request.setProjectId("project123");
        request.setClosingMonth("202310");

        Map<String, String> roomPspMap = new HashMap<>();
        roomPspMap.put("room1", "alipay");

        AptBill bill = new AptBill();
        bill.setId(1L);
        bill.setRoomId("room1");
        bill.setBuildingId("invalidBuilding");
        bill.setPaymentStatus(BillPaymentStatus.TO_BE_PAID);

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {

            // Arrange
            when(agreementService.queryAllActiveAgreementRoomsAndPsp(request.getProjectId())).thenReturn(roomPspMap);
            when(aptBillRepository.findAll(any(Predicate.class))).thenReturn(Collections.singletonList(bill));
            when(hiveAsClient.getBuildingById("invalidBuilding")).thenReturn(null);
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(generateLoginUser());

            // Act & Assert
            assertThrows(RuntimeException.class,
                         () -> aptBillDirectDebitsBatchService.createDirectDebitsBatch(request));
            verify(batchBillRepository, never()).save(any());
        }
    }

    @Test
    @DisplayName("校验代扣协议：正常场景")
    void shouldPassValidClosingMonthFormat() {
        // Arrange
        DirectDebitsBatchesCreationRequest request = new DirectDebitsBatchesCreationRequest();
        request.setClosingMonth("202312");
        request.setProjectId("PROJ_001");

        when(paymentConfigs.isDirectDebitsDailyUniqBatch()).thenReturn(true);
        when(batchRepository.findAll(any(Predicate.class))).thenReturn(Collections.emptyList());

        // Act
        boolean result = aptBillDirectDebitsBatchService.validateDirectDebitsBatchCreation(request);

        // Assert
        assertTrue(result);
    }

    @Test
    @DisplayName("校验代扣协议：异常场景，无效账单截止月份日期场景")
    void shouldRejectInvalidClosingMonthFormat() {
        try (MockedStatic<DateTimeFormatter> formatterMock = Mockito.mockStatic(DateTimeFormatter.class)) {
            // Arrange
            DirectDebitsBatchesCreationRequest request = new DirectDebitsBatchesCreationRequest();
            request.setClosingMonth("2023-13");

            DateTimeFormatter mockFormatter = mock(DateTimeFormatter.class);
            formatterMock.when(() -> DateTimeFormatter.ofPattern("yyyyMM"))
                         .thenReturn(mockFormatter);
            when(mockFormatter.parse(anyString())).thenThrow(new DateTimeException("Invalid format"));

            // Act & Assert
            RestInvalidParamException exception = assertThrows(RestInvalidParamException.class,
                                                               () -> aptBillDirectDebitsBatchService.validateDirectDebitsBatchCreation(
                                                                       request));

            assertEquals(CLOSING_MONTH_FORMAT_NOT_VALID.getCode(), exception.getCode());
        }
    }

    @Test
    @DisplayName("查询批次账单-批次不存在时抛出异常")
    void queryBillsInBatch_BatchNotExists_ThrowsException() {
        // Arrange
        when(batchRepository.findTopByBatchNo(anyString())).thenReturn(null);

        // Act & Assert
        RestInvalidParamException exception = assertThrows(RestInvalidParamException.class,
                                                           () -> aptBillDirectDebitsBatchService.queryBillsInBatch(
                                                                   "invalidBatch", Pageable.unpaged(),
                                                                   new AptBillSearchReqBo()));
        assertEquals(BATCH_NOT_EXISTS.getCode(), exception.getCode());
    }

    @Test
    @DisplayName("查询批次账单-根据pspName过滤账单")
    void queryBillsInBatch_FilterByPspName_Success() {
        try (MockedStatic<BeanUtil> mockedBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class);) {
            // Arrange
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(generateLoginUser());

            var batch = new AptBillDirectDebitsBatch();
            batch.setBatchNo("BATCH_001");

            var searchReqBo = new AptBillSearchReqBo();
            searchReqBo.setPspName("alipay");

            List<AptDirectDebitsBatchBill> batchBills = List.of(createBatchBill());

            when(batchRepository.findTopByBatchNo(anyString())).thenReturn(batch);
            when(batchBillRepository.findByBatchNoAndPspName(anyString(), anyString())).thenReturn(batchBills);
            when(aptBillRepository.findAll(any(Predicate.class), any(Pageable.class))).thenReturn(Page.empty());
            mockedBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                          .thenReturn(new DirectDebitsBatchBillDetailResource());

            // Act
            Page<DirectDebitsBatchBillDetailResource> result =
                    aptBillDirectDebitsBatchService.queryBillsInBatch("BATCH_001", PageRequest.of(0, 10), searchReqBo);

            // Assert
            verify(batchBillRepository).findByBatchNoAndPspName("BATCH_001", "alipay");
            assertNotNull(result);
        }
    }

    @Test
    @DisplayName("查询批次账单-正常场景，处理失败支付状态账单")
    void queryBillsInBatch_DirectDebitFailedStatus_Success() {
        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class);
             MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            // Arrange
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(generateLoginUser());

            var directDebitsBatchBillDetailResource = new DirectDebitsBatchBillDetailResource();
            directDebitsBatchBillDetailResource.setId(123L);
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(directDebitsBatchBillDetailResource);

            AptBillDirectDebitsBatch batch = new AptBillDirectDebitsBatch();
            batch.setBatchNo("BATCH_002");

            AptBillSearchReqBo searchReqBo = new AptBillSearchReqBo();
            searchReqBo.setPaymentStatus(DIRECT_DEBIT_FAILED);

            AptDirectDebitsBatchBill aptDirectDebitsBatchBill = createBatchBill();
            aptDirectDebitsBatchBill.setBillId(directDebitsBatchBillDetailResource.getId());

            when(batchRepository.findTopByBatchNo(anyString())).thenReturn(batch);
            when(batchBillRepository.findByBatchNo(anyString())).thenReturn(List.of(aptDirectDebitsBatchBill));

            when(aptBillRepository.findAll(any(Predicate.class), any(Pageable.class))).thenReturn(
                    new PageImpl<>(List.of(new AptBill())));

            when(paymentInfoRepository.findTransInfoByIdIn(anyCollection())).thenReturn(
                    List.of(new AptPaymentTransInfo()));

            // 设置mock行为链
            List<Long> expectedBillIds = List.of(1L, 2L, 3L);

            JPAQueryFactory mockQueryFactory = mock(JPAQueryFactory.class);
            JPAQuery<Long> mockQuery = mock(JPAQuery.class);
            when(batchBillRepository.getJpaQueryFactory()).thenReturn(mockQueryFactory);
            when(mockQueryFactory.select(QAptDirectDebitsBatchBill.aptDirectDebitsBatchBill.billId)).thenReturn(
                    mockQuery);
            when(mockQuery.from(any(QAptDirectDebitsBatchBill.class), any(QAptPaymentInfo.class))).thenReturn(
                    mockQuery);
            when(mockQuery.where(any(Predicate.class))).thenReturn(mockQuery);
            when(mockQuery.fetch()).thenReturn(expectedBillIds);

            // Act
            Page<DirectDebitsBatchBillDetailResource> result =
                    aptBillDirectDebitsBatchService.queryBillsInBatch("BATCH_002", Pageable.ofSize(10), searchReqBo);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getTotalPages());
            assertEquals(1, result.getTotalElements());
        }
    }

    @Test
    @DisplayName("查询批次账单-正常场景，支付成功场景")
    void queryBillsInBatch_PageableQuery_Success() {
        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class);
             MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            // Arrange
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(generateLoginUser());

            var directDebitsBatchBillDetailResource = new DirectDebitsBatchBillDetailResource();
            directDebitsBatchBillDetailResource.setId(123L);
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(directDebitsBatchBillDetailResource);

            AptBillDirectDebitsBatch batch = new AptBillDirectDebitsBatch();
            batch.setBatchNo("BATCH_002");

            AptBillSearchReqBo searchReqBo = new AptBillSearchReqBo();
            searchReqBo.setPaymentStatus(DIRECT_DEBIT_FAILED);

            AptDirectDebitsBatchBill aptDirectDebitsBatchBill = createBatchBill();
            aptDirectDebitsBatchBill.setBillId(directDebitsBatchBillDetailResource.getId());

            when(batchRepository.findTopByBatchNo(anyString())).thenReturn(batch);
            when(batchBillRepository.findByBatchNo(anyString())).thenReturn(List.of(aptDirectDebitsBatchBill));

            when(aptBillRepository.findAll(any(Predicate.class), any(Pageable.class))).thenReturn(
                    new PageImpl<>(List.of(new AptBill())));

            var aptBillDirectDebitsAgreement = new AptBillDirectDebitsAgreement();
            aptBillDirectDebitsAgreement.setAgreementNo("AGREEMENT_001");
            when(agreementService.findAllByAgreementNos(anyCollection())).thenReturn(
                    List.of(aptBillDirectDebitsAgreement));

            AptPaymentTransInfo aptPaymentTransInfo = new AptPaymentTransInfo();
            aptPaymentTransInfo.setPaymentOrderNo(aptDirectDebitsBatchBill.getPaymentInfoId());
            aptPaymentTransInfo.setAgreementNo(aptBillDirectDebitsAgreement.getAgreementNo());
            when(paymentInfoRepository.findTransInfoByIdIn(anyCollection())).thenReturn(List.of(aptPaymentTransInfo));


            // 设置mock行为链
            List<Long> expectedBillIds = List.of(1L, 2L, 3L);

            JPAQueryFactory mockQueryFactory = mock(JPAQueryFactory.class);
            JPAQuery<Long> mockQuery = mock(JPAQuery.class);
            when(batchBillRepository.getJpaQueryFactory()).thenReturn(mockQueryFactory);
            when(mockQueryFactory.select(QAptDirectDebitsBatchBill.aptDirectDebitsBatchBill.billId)).thenReturn(
                    mockQuery);
            when(mockQuery.from(any(QAptDirectDebitsBatchBill.class), any(QAptPaymentInfo.class))).thenReturn(
                    mockQuery);
            when(mockQuery.where(any(Predicate.class))).thenReturn(mockQuery);
            when(mockQuery.fetch()).thenReturn(expectedBillIds);

            // Act
            Page<DirectDebitsBatchBillDetailResource> result =
                    aptBillDirectDebitsBatchService.queryBillsInBatch("BATCH_002", Pageable.ofSize(10), searchReqBo);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getTotalPages());
            assertEquals(1, result.getTotalElements());
        }
    }

    @Test
    @DisplayName("根据批次删除代扣账单，异常场景，当批次不存在时应该抛出批次不存在异常")
    void deleteDirectDebitsBatchBills_shouldThrowExceptionWhenBatchNotExists() {
        // Arrange
        String batchNo = "BATCH_001";
        List<String> billNos = List.of("BILL_001", "BILL_002");

        when(batchRepository.findTopByBatchNo(batchNo)).thenReturn(null);

        // Act & Assert
        RestInvalidParamException exception = assertThrows(RestInvalidParamException.class,
                                                           () -> aptBillDirectDebitsBatchService.deleteDirectDebitsBatchBills(
                                                                   batchNo, billNos));

        assertEquals(BATCH_NOT_EXISTS.getCode(), exception.getCode());
        verify(batchBillRepository, never()).deleteAll(any());
    }

    @Test
    @DisplayName("根据批次删除代扣账单，异常场景，当批次状态非待处理时应该抛出不可删除异常")
    void deleteDirectDebitsBatchBills_shouldThrowExceptionWhenBatchStatusInvalid() {
        // Arrange
        String batchNo = "BATCH_002";
        List<String> billNos = Collections.singletonList("BILL_003");

        AptBillDirectDebitsBatch batch = new AptBillDirectDebitsBatch();
        batch.setStatus(DirectDebitsBatchStatus.SENT);

        when(batchRepository.findTopByBatchNo(batchNo)).thenReturn(batch);

        // Act & Assert
        RestInvalidParamException exception = assertThrows(RestInvalidParamException.class,
                                                           () -> aptBillDirectDebitsBatchService.deleteDirectDebitsBatchBills(
                                                                   batchNo, billNos));

        assertEquals(ONLY_AWAIT_BATCH_DELETABLE.getCode(), exception.getCode());
        verify(batchBillRepository, never()).findByBatchNoAndBillNoIn(any(), any());
    }

    @Test
    @DisplayName("根据批次删除代扣账单，异常场景，当账单数量不匹配时应该抛出账单无效异常")
    void deleteDirectDebitsBatchBills_shouldThrowExceptionWhenBillCountMismatch() {
        // Arrange
        String batchNo = "BATCH_003";
        List<String> billNos = List.of("BILL_004", "BILL_005");

        AptBillDirectDebitsBatch batch = new AptBillDirectDebitsBatch();
        batch.setStatus(DirectDebitsBatchStatus.AWAIT);

        List<AptDirectDebitsBatchBill> batchBills = Collections.singletonList(new AptDirectDebitsBatchBill());

        when(batchRepository.findTopByBatchNo(batchNo)).thenReturn(batch);
        when(batchBillRepository.findByBatchNoAndBillNoIn(batchNo, billNos)).thenReturn(batchBills);

        // Act & Assert
        RestInvalidParamException exception = assertThrows(RestInvalidParamException.class,
                                                           () -> aptBillDirectDebitsBatchService.deleteDirectDebitsBatchBills(
                                                                   batchNo, billNos));

        assertEquals(DIRECT_DEBITS_BILL_NOT_VALID.getCode(), exception.getCode());
        verify(batchBillRepository, never()).deleteAll(any());
    }

    @Test
    @DisplayName("根据批次删除代扣账单，异常场景，当未找到关联账单时应该抛出账单无效异常")
    void deleteDirectDebitsBatchBills_shouldThrowExceptionWhenNoBillsFound() {
        // Arrange
        String batchNo = "BATCH_005";
        List<String> billNos = List.of("BILL_008", "BILL_009");

        AptBillDirectDebitsBatch batch = new AptBillDirectDebitsBatch();
        batch.setStatus(DirectDebitsBatchStatus.AWAIT);

        when(batchRepository.findTopByBatchNo(batchNo)).thenReturn(batch);
        when(batchBillRepository.findByBatchNoAndBillNoIn(batchNo, billNos)).thenReturn(Collections.emptyList());

        // Act & Assert
        RestInvalidParamException exception = assertThrows(RestInvalidParamException.class,
                                                           () -> aptBillDirectDebitsBatchService.deleteDirectDebitsBatchBills(
                                                                   batchNo, billNos));

        assertEquals(DIRECT_DEBITS_BILL_NOT_VALID.getCode(), exception.getCode());
        verify(batchBillRepository, never()).deleteAll(any());
    }

    @Test
    @DisplayName("根据批次删除代扣账单，正常场景，当成功删除账单时应该返回true并重置状态")
    void deleteDirectDebitsBatchBills_shouldDeleteSuccessfullyWhenAllConditionsMet() {
        // Arrange
        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(generateLoginUser());

            String batchNo = "BATCH_004";
            List<String> billNos = List.of("BILL_006", "BILL_007");

            AptBillDirectDebitsBatch batch = new AptBillDirectDebitsBatch();
            batch.setStatus(DirectDebitsBatchStatus.AWAIT);
            batch.setBillCount(2);

            var aptDirectDebitsBatchBill1 = createBatchBill();
            aptDirectDebitsBatchBill1.setBillNo("BILL_006");

            var aptDirectDebitsBatchBill2 = createBatchBill();
            aptDirectDebitsBatchBill2.setBillNo("BILL_007");
            List<AptDirectDebitsBatchBill> batchBills = List.of(aptDirectDebitsBatchBill2, aptDirectDebitsBatchBill2);

            when(batchRepository.findTopByBatchNo(batchNo)).thenReturn(batch);
            when(batchBillRepository.findByBatchNoAndBillNoIn(batchNo, billNos)).thenReturn(batchBills);

            // Act
            boolean result = aptBillDirectDebitsBatchService.deleteDirectDebitsBatchBills(batchNo, billNos);

            // Assert
            assertTrue(result);
            verify(batchBillRepository).deleteAll(batchBills);
            verify(batchBillService).resetAptBillToBePaidStatus(anyList());
        }
    }

    private AptDirectDebitsBatchBill createBatchBill() {
        AptDirectDebitsBatchBill bill = new AptDirectDebitsBatchBill();
        bill.setBillId(1L);
        bill.setPaymentInfoId("paymentInfoId-1");
        bill.setBillNo("BILL_001");
        bill.setCompanyCode("CO_001");
        bill.setPspName("alipay");
        return bill;
    }

    @DisplayName("正常场景：计算批次账单总金额成功")
    @Test
    void testCalculateBatchBillAmount_success() {
        // Arrange
        String batchNo = "BATCH123";
        var searchReqBo = new AptBillSearchReqBo();
        searchReqBo.setPaymentStatus(null);

        var batch = new AptBillDirectDebitsBatch();
        batch.setBatchNo(batchNo);
        batch.setStatus(DirectDebitsBatchStatus.AWAIT);

        var bill1 = new AptBill();
        bill1.setId(1L);
        bill1.setAmt(100.50);

        var bill2 = new AptBill();
        bill2.setId(2L);
        bill2.setAmt(200.75);

        var bill3 = new AptBill();
        bill3.setId(3L);
        bill3.setAmt(300.25);

        Mockito.when(batchRepository.findTopByBatchNo(batchNo))
               .thenReturn(batch);

        List<Long> expectedBillIds = List.of(1L, 2L, 3L);
        JPAQueryFactory mockQueryFactory = Mockito.mock(JPAQueryFactory.class);
        JPAQuery<Long> mockQuery = Mockito.mock(JPAQuery.class);

        Mockito.when(batchBillRepository.getJpaQueryFactory())
               .thenReturn(mockQueryFactory);
        Mockito.when(mockQueryFactory.selectDistinct(QAptDirectDebitsBatchBill.aptDirectDebitsBatchBill.billId))
               .thenReturn(mockQuery);
        Mockito.when(mockQuery.from(QAptDirectDebitsBatchBill.aptDirectDebitsBatchBill))
               .thenReturn(mockQuery);
        Mockito.when(mockQuery.where(QAptDirectDebitsBatchBill.aptDirectDebitsBatchBill.batchNo.eq(batchNo)))
               .thenReturn(mockQuery);
        Mockito.when(mockQuery.fetch())
               .thenReturn(expectedBillIds);

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class);
             MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new DirectDebitsBatchBillAmountResource());

            // Act and Assert
            assertThrows(NullPointerException.class,
                         () -> aptBillDirectDebitsBatchService.calculateBatchBillAmount(batchNo, searchReqBo));

        }
    }

    @DisplayName("状态为代扣失败，正常场景：计算批次账单总金额成功")
    @ParameterizedTest
    @CsvSource({"1;2;3, true", "x, false"
    })
    void testCalculateBatchBillAmount_directDebitFailed_success(String billIds, boolean isThrownError) {
        // Arrange
        String batchNo = "BATCH123";
        var searchReqBo = new AptBillSearchReqBo();
        searchReqBo.setPaymentStatus(DIRECT_DEBIT_FAILED);

        var batch = new AptBillDirectDebitsBatch();
        batch.setBatchNo(batchNo);
        batch.setStatus(DirectDebitsBatchStatus.AWAIT);

        var bill1 = new AptBill();
        bill1.setId(1L);
        bill1.setAmt(100.50);

        var bill2 = new AptBill();
        bill2.setId(2L);
        bill2.setAmt(200.75);

        var bill3 = new AptBill();
        bill3.setId(3L);
        bill3.setAmt(300.25);

        Mockito.when(batchRepository.findTopByBatchNo(batchNo))
               .thenReturn(batch);

        List<Long> expectedBillIds = "x".equals(billIds) ? Collections.emptyList() :
                List.of(Arrays.stream(billIds.split(";"))
                              .map(Long::valueOf)
                              .toArray(Long[]::new));

        JPAQueryFactory mockQueryFactory = Mockito.mock(JPAQueryFactory.class);
        JPAQuery<Long> mockQuery = Mockito.mock(JPAQuery.class);

        Mockito.when(batchBillRepository.getJpaQueryFactory())
               .thenReturn(mockQueryFactory);
        Mockito.when(mockQueryFactory.select(QAptDirectDebitsBatchBill.aptDirectDebitsBatchBill.billId))
               .thenReturn(mockQuery);
        Mockito.when(mockQuery.from(QAptDirectDebitsBatchBill.aptDirectDebitsBatchBill, QAptPaymentInfo.aptPaymentInfo))
               .thenReturn(mockQuery);
        Mockito.when(mockQuery.where(any(Predicate.class)))
               .thenReturn(mockQuery);
        Mockito.when(mockQuery.fetch())
               .thenReturn(expectedBillIds);


        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class);
             MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new DirectDebitsBatchBillAmountResource());

            // Act and Assert
            if (isThrownError) {
                assertThrows(NullPointerException.class,
                             () -> aptBillDirectDebitsBatchService.calculateBatchBillAmount(batchNo, searchReqBo));
            } else {
                DirectDebitsBatchBillAmountResource result =
                        aptBillDirectDebitsBatchService.calculateBatchBillAmount(batchNo, searchReqBo);
                assertNotNull(result);
                assertEquals(0, result.getTotalAmount()
                                      .compareTo(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP)));
            }

        }
    }


    @DisplayName("异常场景：批次不存在")
    @Test
    void testCalculateBatchBillAmount_batchNotFound() {
        // Arrange
        String batchNo = "INVALID_BATCH";
        AptBillSearchReqBo searchReqBo = new AptBillSearchReqBo();

        Mockito.when(batchRepository.findTopByBatchNo(batchNo))
               .thenReturn(null);

        // Act & Assert
        assertThrows(RestInvalidParamException.class,
                     () -> aptBillDirectDebitsBatchService.calculateBatchBillAmount(batchNo, searchReqBo));
    }

    @DisplayName("正常场景：检查过期批次并更新状态")
    @Test
    void testLapsedCheck_success() {
        // Arrange
        AptBillDirectDebitsBatch batch1 = new AptBillDirectDebitsBatch();
        batch1.setBatchNo("BATCH001");
        batch1.setStatus(DirectDebitsBatchStatus.AWAIT);
        batch1.setCreatedTime(ZonedDateTime.now()
                                           .minusDays(1));
        batch1.setIsDel(0);

        AptBillDirectDebitsBatch batch2 = new AptBillDirectDebitsBatch();
        batch2.setBatchNo("BATCH002");
        batch2.setStatus(DirectDebitsBatchStatus.AWAIT);
        batch2.setCreatedTime(ZonedDateTime.now()
                                           .minusDays(2));
        batch2.setIsDel(0);

        List<AptBillDirectDebitsBatch> batches = List.of(batch1, batch2);
        when(batchRepository.findAll(any(BooleanExpression.class))).thenReturn(batches);
        when(batchBillRepository.findByBatchNo(anyString())).thenReturn(List.of(new AptDirectDebitsBatchBill()));

        // Act
        List<DirectDebitsBillBatchQueryResource> result = aptBillDirectDebitsBatchService.lapsedCheck();

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(DirectDebitsBatchStatus.LAPSED, batch1.getStatus());
        assertEquals(DirectDebitsBatchStatus.LAPSED, batch2.getStatus());
        verify(batchRepository, times(2)).save(any());
    }

    @DisplayName("异常场景：无待处理批次")
    @Test
    void testLapsedCheck_noAwaitBatches() {
        // Arrange
        Mockito.when(batchRepository.findAll(any(BooleanExpression.class)))
               .thenReturn(Collections.emptyList());

        // Act
        List<DirectDebitsBillBatchQueryResource> result = aptBillDirectDebitsBatchService.lapsedCheck();

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(batchRepository, never()).saveAll(any());
    }

    @DisplayName("异常场景：无过期批次")
    @Test
    void testLapsedCheck_noLapsedBatches() {
        // Arrange
        AptBillDirectDebitsBatch batch = new AptBillDirectDebitsBatch();
        batch.setBatchNo("BATCH001");
        batch.setStatus(DirectDebitsBatchStatus.AWAIT);
        batch.setCreatedTime(ZonedDateTime.now());
        batch.setIsDel(0);

        List<AptBillDirectDebitsBatch> batches = List.of(batch);
        when(batchRepository.findAll(any(BooleanExpression.class))).thenReturn(batches);

        // Act
        List<DirectDebitsBillBatchQueryResource> result = aptBillDirectDebitsBatchService.lapsedCheck();

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(batchRepository, never()).saveAll(any());
    }


    private LoginUser generateLoginUser() {
        return LoginUser.builder()
                        .nickName("testUser")
                        .fromType("C")
                        .cid("testCid")
                        .build();
    }

}