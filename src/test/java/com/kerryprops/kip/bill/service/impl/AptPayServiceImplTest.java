package com.kerryprops.kip.bill.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.enums.BillPayModule;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptPayBillRepository;
import com.kerryprops.kip.bill.dao.AptPayRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPayConfig;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.kerryprops.kip.bill.common.constants.AppConstants.COMMA;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomObject;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomString;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

/**
 * AptPayServiceImplTest.
 *
 * <AUTHOR> Yu 2024-09-05 10:55:23
 **/
@ExtendWith(MockitoExtension.class)
class AptPayServiceImplTest {

    @Mock
    ObjectMapper objectMapper;

    @Mock
    AptPayRepository aptPayRepository;

    @Mock
    AptPayBillRepository payBillRepository;

    @Mock
    AptBillRepository aptBillRepository;

    @InjectMocks
    AptPayServiceImpl aptPayService;

    @ParameterizedTest
    @CsvSource({"KERRY,"
            , "KERRY,支付详情"
            , "CASHIER,支付详情"
            , "CASHIER_FEE,支付详情"
    })
    @DisplayName("取消支付-无对应账单场景：正常场景")
    void _01_cancelAptPay_success(BillPayModule billPayModule, String description) {
        // Arrange
        AptPaymentInfo aptPaymentInfo = randomObject(AptPaymentInfo.class);
        aptPaymentInfo.setBillPayModule(billPayModule);
        aptPaymentInfo.setDescription(description);
        aptPaymentInfo.setDeleted(0);

        // Act
        aptPayService.cancelAptPay(aptPaymentInfo, Collections.emptyList());

        // Assert
        ArgumentCaptor<AptPay> captor = ArgumentCaptor.forClass(AptPay.class);
        verify(aptPayRepository, times(1)).save(captor.capture());

        AptPay aptPayActual = captor.getValue();
        assertThat(aptPayActual.getDeletedAt()).isEqualTo(1);

        if (StringUtils.isEmpty(description)) {
            assertEquals("线上支付", aptPayActual.getComments());
        } else {
            assertEquals(aptPaymentInfo.getDescription(), aptPayActual.getComments());
        }

    }

    @ParameterizedTest
    @CsvSource({"KERRY,"
            , "KERRY,支付详情"
            , "CASHIER,支付详情"
            , "CASHIER_FEE,支付详情"
    })
    @DisplayName("取消支付-有对应账单场景：正常场景")
    void _02_cancelAptPay_without_bill_success(BillPayModule billPayModule, String description) {
        // Arrange
        AptPaymentInfo aptPaymentInfo = randomObject(AptPaymentInfo.class);
        aptPaymentInfo.setBillPayModule(billPayModule);
        aptPaymentInfo.setDescription(description);
        aptPaymentInfo.setDeleted(0);

        AptBill aptBillMock = randomObject(AptBill.class);
        var positionItemResponseMock = new PositionItemResponse();
        positionItemResponseMock.setBuildingName(randomString());
        aptBillMock.setPositionItem(positionItemResponseMock);

        // Act
        aptPayService.cancelAptPay(aptPaymentInfo, List.of(aptBillMock));

        // Assert
        ArgumentCaptor<AptPay> captor = ArgumentCaptor.forClass(AptPay.class);
        verify(aptPayRepository, times(1)).save(captor.capture());

        AptPay aptPayActual = captor.getValue();
        assertThat(aptPayActual.getDeletedAt()).isEqualTo(1);
        assertTrue((positionItemResponseMock.getBuildingName() + "-" + aptBillMock.getUnit() + COMMA +
                aptBillMock.getMonth() + "月" + aptBillMock.getCategory()).contains(aptPayActual.getPayDesc()));
    }

    @Test
    @DisplayName("取消支付-修复空指针异常")
    void _03_cancelAptPay_fix_null_success() {
        UserInfoUtils.setUser(new LoginUser());
        AptPaymentInfo aptPaymentInfo = randomObject(AptPaymentInfo.class);
        aptPaymentInfo.setBillPayModule(BillPayModule.KERRY);
        aptPaymentInfo.setDescription("支付详情");
        aptPaymentInfo.setDeleted(0);

        aptPayService.cancelAptPay(aptPaymentInfo, Collections.emptyList());
        UserInfoUtils.removeUser();

        ArgumentCaptor<AptPay> captor = ArgumentCaptor.forClass(AptPay.class);
        verify(aptPayRepository, times(1)).save(captor.capture());
        AptPay paymentInfo = captor.getValue();
        assertThat(paymentInfo.getDeletedAt()).isEqualTo(1);
    }

    @ParameterizedTest
    @CsvSource({"KERRY,,"
            , "KERRY,payAct,DIRECT_DEBIT_PAID"
            , "CASHIER,payAct,TO_BE_PAID"
            , "CASHIER_FEE,payAct,TO_BE_PAID"
    })
    @DisplayName("支付完成-无对应账单场景：正常场景")
    void _04_savePayInfo_without_bill_success(BillPayModule billPayModule, String payAct, BillPaymentStatus paymentStatus) {
        AptPaymentInfo aptPaymentInfo = randomObject(AptPaymentInfo.class);
        aptPaymentInfo.setBillPayModule(billPayModule);
        aptPaymentInfo.setPayAct(payAct);
        aptPaymentInfo.setPaymentStatus(paymentStatus);
        AptPayConfig aptPayConfig = randomObject(AptPayConfig.class);
        AptBill aptBill = randomObject(AptBill.class);
        doReturn(Optional.of(aptBill)).when(aptBillRepository).findLastBillByRoomId(anyString());
        double paidAmt = 1.11;

        aptPayService.savePayInfo(aptPaymentInfo, aptPayConfig, "tranxId", paidAmt, new Date());

        ArgumentCaptor<AptPay> captor = ArgumentCaptor.forClass(AptPay.class);
        verify(aptPayRepository, times(1)).save(captor.capture());
        AptPay paymentInfo = captor.getValue();
        assertThat(paymentInfo.getDeletedAt()).isZero();
        assertThat(paymentInfo.getTotalAmt()).isEqualTo(paidAmt);
    }

    @Test
    @DisplayName("支付完成-有对应账单场景：正常场景")
    void _05_savePayInfo_success() {
        AptPaymentInfo aptPaymentInfo = randomObject(AptPaymentInfo.class);
        AptPayConfig aptPayConfig = randomObject(AptPayConfig.class);
        AptBill aptBill = randomObject(AptBill.class);

        var positionItemResponseMock = new PositionItemResponse();
        positionItemResponseMock.setBuildingName(randomString());
        aptBill.setPositionItem(positionItemResponseMock);

        double paidAmt = 1.11;
        Mockito.doAnswer(v -> v.getArgument(0)).when(aptPayRepository).save(any());

        aptPayService.savePayInfo(aptPaymentInfo, aptPayConfig, "tranxId", paidAmt, new Date(), List.of(aptBill));

        ArgumentCaptor<AptPay> captor = ArgumentCaptor.forClass(AptPay.class);
        verify(aptPayRepository, times(1)).save(captor.capture());
        AptPay aptPay = captor.getValue();
        assertEquals(StringUtils.abbreviate(aptBill.getPositionItem().getBuildingName() + "-" + aptBill.getUnit()
                        + COMMA + aptBill.getMonth() + "月" + aptBill.getCategory(), StringUtils.EMPTY, 30)
                , aptPay.getPayDesc());
        assertThat(aptPay.getDeletedAt()).isZero();
        assertThat(aptPay.getTotalAmt()).isEqualTo(paidAmt);
    }

    @Test
    @DisplayName("支付完成-有对应账单场景：正常场景")
    void _06_savePayInfo_success() {
        // Arrange
        AptPaymentInfo aptPaymentInfo = randomObject(AptPaymentInfo.class);
        AptPayConfig aptPayConfig = randomObject(AptPayConfig.class);

        final String BUILDING_NAME_MOCK_1 = "A2";

        final String UNIT_MOCK_1 = "2A";
        final String UNIT_MOCK_2 = "15A";

        final String CATEGORY_MOCK_1 = "日常专项维修资金";
        final String CATEGORY_MOCK_2 = "管理费";

        var positionItemResponseMock = new PositionItemResponse();
        positionItemResponseMock.setBuildingName(BUILDING_NAME_MOCK_1);

        AptBill aptBillMock1 = randomObject(AptBill.class);
        aptBillMock1.setUnit(UNIT_MOCK_1);
        aptBillMock1.setCategory(CATEGORY_MOCK_1);
        aptBillMock1.setPositionItem(positionItemResponseMock);
        aptBillMock1.setMonth(1);

        AptBill aptBillMock2 = randomObject(AptBill.class);
        aptBillMock2.setUnit(UNIT_MOCK_1);
        aptBillMock2.setCategory(CATEGORY_MOCK_1);
        aptBillMock2.setPositionItem(positionItemResponseMock);
        aptBillMock2.setMonth(2);

        AptBill aptBillMock3 = randomObject(AptBill.class);
        aptBillMock3.setUnit(UNIT_MOCK_1);
        aptBillMock3.setCategory(CATEGORY_MOCK_1);
        aptBillMock3.setPositionItem(positionItemResponseMock);
        aptBillMock3.setMonth(11);

        AptBill aptBillMock4 = randomObject(AptBill.class);
        aptBillMock4.setUnit(UNIT_MOCK_2);
        aptBillMock4.setPositionItem(positionItemResponseMock);
        aptBillMock4.setCategory(CATEGORY_MOCK_2);
        aptBillMock4.setMonth(9);

        double paidAmt = 1.11;
        Mockito.doAnswer(v -> v.getArgument(0)).when(aptPayRepository).save(any());

        // Act
        aptPayService.savePayInfo(aptPaymentInfo, aptPayConfig, "tranxId", paidAmt, new Date()
                , List.of(aptBillMock1, aptBillMock2, aptBillMock3, aptBillMock4));

        // Assert
        ArgumentCaptor<AptPay> captor = ArgumentCaptor.forClass(AptPay.class);
        verify(aptPayRepository, times(1)).save(captor.capture());

        AptPay aptPayActual = captor.getValue();
        assertEquals(0, aptPayActual.getDeletedAt());
        assertEquals(paidAmt, aptPayActual.getTotalAmt());
        assertEquals(StringUtils.abbreviate(BUILDING_NAME_MOCK_1 + "-" + UNIT_MOCK_1 + COMMA + UNIT_MOCK_2
                        + COMMA + "9月" + CATEGORY_MOCK_2 + COMMA + "1/2/11月" + CATEGORY_MOCK_1, StringUtils.EMPTY, 30)
                , aptPayActual.getPayDesc());
    }

}