package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptJdeBillRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptJdeBill;
import com.kerryprops.kip.bill.dao.entity.JDEAptBill;
import com.kerryprops.kip.bill.service.StaffManualSyncAptBillService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.doReturn;

@Slf4j
class StaffManualSyncAptBillServiceImplTest extends BaseIntegrationTest {

    @Autowired
    StaffManualSyncAptBillService staffManualSyncAptBillService;

    @Autowired
    private AptJdeBillRepository aptJdeBillRepository;

    @Autowired
    private AptBillRepository billRepository;

    @Test
    void _01_bill_payment_status_TO_BE_PAY_change_to_PAID() {
        // F03B11.RPPST='A' 未付款, F03B11.RPPST='P' 已付款
        AptBill aptBill = randomAptBill();
        log.info("test with billNo: {}, amt: {}", aptBill.getBillNo(), aptBill.getAmt());
        aptBill = billRepository.save(aptBill);
        AptJdeBill aptJdeBill = randomAptJdeBill(aptBill);
        aptJdeBill = aptJdeBillRepository.save(aptJdeBill);
        JDEAptBill jdeBill = randomJDEBill(aptJdeBill);
        jdeBill.setPaymentStatus("P");
        doReturn(List.of(jdeBill)).when(jdeBillRepository).findAllByCombineKeys(ArgumentMatchers.anyLong()
                , ArgumentMatchers.anyString(), ArgumentMatchers.anyString(), ArgumentMatchers.anyString());
        staffManualSyncAptBillService.manualSync(aptBill, List.of(aptJdeBill));
        aptJdeBill = aptJdeBillRepository.findAllByBillNumber(aptBill.getBillNo()).get(0);
        assertEquals(1, aptJdeBill.getJdeVerification());

        aptBill = billRepository.findById(aptBill.getId()).get();
        assertEquals(BillStatus.JDE_VERIFIED, aptBill.getStatus());
        assertEquals(BillPaymentStatus.PAID, aptBill.getPaymentStatus());
    }

    /**
     * aptBill 对应 jde多个子账单情况下，部分已经支付场景
     */
    @Test
    void _02_bill_payment_status_TO_BE_PAY_change_to_PART_PAID() {
        // F03B11.RPPST='A' 未付款, F03B11.RPPST='P' 已付款
        AptBill aptBill = randomAptBill();
        log.info("test with billNo: {}, amt: {}", aptBill.getBillNo(), aptBill.getAmt());
        aptBill = billRepository.save(aptBill);
        AptJdeBill aptJdeBill_1 = randomAptJdeBill(aptBill);
        aptJdeBill_1 = aptJdeBillRepository.save(aptJdeBill_1);
        AptJdeBill aptJdeBill_2 = randomAptJdeBill(aptBill);
        aptJdeBill_2.setRdAg(aptBill.getAmt() * 100 - aptJdeBill_1.getRdAg());
        aptJdeBill_2 = aptJdeBillRepository.save(aptJdeBill_2);
        JDEAptBill jdeBill_1 = randomJDEBill(aptJdeBill_1);
        jdeBill_1.setPaymentStatus("P");
        JDEAptBill jdeBill_2 = randomJDEBill(aptJdeBill_2);
        jdeBill_2.setUpdatedDate(jdeBill_2.getUpdatedDate() + 100);

        doReturn(List.of(jdeBill_1), List.of(jdeBill_2)).when(jdeBillRepository).findAllByCombineKeys(ArgumentMatchers.anyLong()
                , ArgumentMatchers.anyString(), ArgumentMatchers.anyString(), ArgumentMatchers.anyString());

        staffManualSyncAptBillService.manualSync(aptBill, List.of(aptJdeBill_1, aptJdeBill_2));

        assertEquals(2, aptJdeBillRepository.findAllByBillNumber(aptBill.getBillNo()).size());

        aptBill = billRepository.findById(aptBill.getId()).get();
        assertEquals(BillStatus.TO_BE_PAID, aptBill.getStatus());
        assertEquals(BillPaymentStatus.PART_PAID, aptBill.getPaymentStatus());
    }

    /**
     * jde 账单状态无变化，但是金额(修改时间同时也会有改变)有变化 场景
     */
    @Test
    void _03_bill_amt_changed() {
        // F03B11.RPPST='A' 未付款, F03B11.RPPST='P' 已付款
        AptBill aptBill = randomAptBill();
        BigDecimal oldAmt = BigDecimal.valueOf(aptBill.getAmt());
        long offset = random.nextInt(100);
        BigDecimal newAmt = oldAmt.add(BigDecimal.valueOf(offset));

        aptBill = billRepository.save(aptBill);
        AptJdeBill aptJdeBill = randomAptJdeBill(aptBill);
        aptJdeBill = aptJdeBillRepository.save(aptJdeBill);
        JDEAptBill jdeBill = randomJDEBill(aptJdeBill);
        // change jde amt
        jdeBill.setUnpaidAmount(jdeBill.getUnpaidAmount() + offset * 100);
        jdeBill.setUpdatedDate(jdeBill.getUpdatedDate() + Math.abs(random.nextInt(50)));
        doReturn(List.of(jdeBill)).when(jdeBillRepository).findAllByCombineKeys(ArgumentMatchers.anyLong()
                , ArgumentMatchers.anyString(), ArgumentMatchers.anyString(), ArgumentMatchers.anyString());

        // Act
        staffManualSyncAptBillService.manualSync(aptBill, List.of(aptJdeBill));

        // Assert
        aptJdeBill = aptJdeBillRepository.findAllByBillNumber(aptBill.getBillNo()).get(0);
        assertEquals(0, aptJdeBill.getJdeVerification());
        assertEquals(BigDecimal.valueOf(aptBill.getAmt() * 100).setScale(2, RoundingMode.HALF_UP)
                , BigDecimal.valueOf(aptJdeBill.getRdAg()).setScale(2, RoundingMode.HALF_UP));

        aptBill = billRepository.findById(aptBill.getId()).get();
        assertEquals(BillStatus.TO_BE_PAID, aptBill.getStatus());
        assertEquals(BillPaymentStatus.TO_BE_PAID, aptBill.getPaymentStatus());
        assertEquals(newAmt, BigDecimal.valueOf(aptBill.getAmt()));
    }

    @Test
    void _04_jde_db_no_record_found() {
        AptBill aptBill = randomAptBill();
        log.info("test with billNo: {}, amt: {}", aptBill.getBillNo(), aptBill.getAmt());
        aptBill = billRepository.save(aptBill);
        AptJdeBill aptJdeBill = randomAptJdeBill(aptBill);
        aptJdeBill = aptJdeBillRepository.save(aptJdeBill);
        doReturn(Collections.emptyList()).when(jdeBillRepository).findAllByCombineKeys(ArgumentMatchers.anyLong()
                , ArgumentMatchers.anyString(), ArgumentMatchers.anyString(), ArgumentMatchers.anyString());
        staffManualSyncAptBillService.manualSync(aptBill, List.of(aptJdeBill));
        aptJdeBill = aptJdeBillRepository.findAllByBillNumber(aptBill.getBillNo()).get(0);
        assertEquals(0, aptJdeBill.getJdeVerification());

        aptBill = billRepository.findById(aptBill.getId()).get();
        assertEquals(BillStatus.TO_BE_PAID, aptBill.getStatus());
        assertEquals(BillPaymentStatus.TO_BE_PAID, aptBill.getPaymentStatus());
    }

}