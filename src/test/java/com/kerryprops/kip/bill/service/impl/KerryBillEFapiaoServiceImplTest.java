package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.common.enums.InvoiceState;
import com.kerryprops.kip.bill.common.enums.NotifyType;
import com.kerryprops.kip.bill.common.enums.SendStatus;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.dao.BizContentReadRepository;
import com.kerryprops.kip.bill.dao.EFapiaoBillInvoiceRepository;
import com.kerryprops.kip.bill.dao.EFapiaoSendRecordRepository;
import com.kerryprops.kip.bill.dao.entity.EFapiaoBillInvoice;
import com.kerryprops.kip.bill.dao.entity.EFapiaoSendRecord;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.clients.HiveClient;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.IBillService;
import com.kerryprops.kip.bill.service.model.s.BillEFapiaoSearchReqBo;
import com.kerryprops.kip.bill.service.model.s.BizBillEFapiaoSearchReqBo;
import com.kerryprops.kip.bill.webservice.vo.req.DocoBillPayer;
import com.kerryprops.kip.bill.webservice.vo.req.EmailResultVo;
import com.kerryprops.kip.bill.webservice.vo.req.EmailStatusDto;
import com.kerryprops.kip.bill.webservice.vo.req.SmsResultCallbackVo;
import com.kerryprops.kip.bill.webservice.vo.req.SmsStatusVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillInvoiceResource;
import com.kerryprops.kip.bill.webservice.vo.resp.BizBillInvoiceResource;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffBillReceiverRespVo;
import com.querydsl.core.types.Predicate;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static com.kerryprops.kip.bill.common.enums.InvoiceTypeEnum.QS;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomLoginUser;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomString;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyCollection;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * KerryBillEFapiaoServiceImplTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-04-23
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("S端-发票信息-单元测试")
class KerryBillEFapiaoServiceImplTest {

    @Mock
    private EFapiaoBillInvoiceRepository eFapiaoBillInvoiceRepository;

    @Mock
    private HiveAsClient hiveAsClient;

    @Mock
    private HiveClient hiveService;

    @Mock
    private IBillService billService;

    @Mock
    private EFapiaoSendRecordRepository fapiaoSendRecordRepository;

    @Mock
    private BizContentReadRepository bizContentReadRepository;

    @InjectMocks
    private KerryBillEFapiaoServiceImpl kerryBillEFapiaoService;

    @DisplayName("eFapiaoList - 项目ID为空")
    @Test
    void eFapiaoList_projectIdEmpty() {
        // Arrange
        BillEFapiaoSearchReqBo reqBo = new BillEFapiaoSearchReqBo();
        reqBo.setProjectId(null);

        Pageable pageable = PageRequest.of(0, 10);

        // Act & Assert
        assertThrows(NullPointerException.class, () -> kerryBillEFapiaoService.eFapiaoList(reqBo, pageable));
    }

    @DisplayName("eFapiaoList - 无发票数据")
    @Test
    void eFapiaoList_noInvoices() {
        // Arrange
        BillEFapiaoSearchReqBo reqBo = new BillEFapiaoSearchReqBo();
        reqBo.setProjectId("projectId");
        reqBo.setBuildingIds(List.of("building1"));

        Pageable pageable = PageRequest.of(0, 10);

        when(hiveAsClient.populateDataFields("projectId", List.of("building1"))).thenReturn(List.of("MCU1"));
        when(eFapiaoBillInvoiceRepository.findAll(any(Predicate.class), any(Pageable.class))).thenReturn(Page.empty());

        // Act
        Page<BillInvoiceResource> result = kerryBillEFapiaoService.eFapiaoList(reqBo, pageable);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }


    @DisplayName("eFapiaoList - 正常场景")
    @Test
    void eFapiaoList_normalScenario() {
        // Arrange
        BillEFapiaoSearchReqBo reqBo = new BillEFapiaoSearchReqBo();
        reqBo.setProjectId("projectId");
        reqBo.setBuildingIds(List.of("building1", "building2"));

        Pageable pageable = PageRequest.of(0, 10);

        EFapiaoBillInvoice invoice = new EFapiaoBillInvoice();
        invoice.setId(1L);
        invoice.setPurchaserName("TestPurchaser");
        invoice.setInvoiceType(QS.getKipCode());
        invoice.setDoco("12345");
        invoice.setAn8("12345678");
        invoice.setMcu("MCU1");
        invoice.setJdeUnit("JdeUnit");
        invoice.setAmountWithTax(BigDecimal.valueOf(100.00));
        invoice.setInvoiceNo("INV123");
        invoice.setState(InvoiceState.NORMAL);
        invoice.setEmailSendStatus(SendStatus.MSG_SENDING);

        Page<EFapiaoBillInvoice> pagedInvoices = new PageImpl<>(List.of(invoice), pageable, 1);

        DocoBillPayer key = DocoBillPayer.builder()
                                         .doco(invoice.getDoco())
                                         .an8(invoice.getAn8())
                                         .alph(invoice.getPurchaserName())
                                         .build();
        StaffBillReceiverRespVo receiver = new StaffBillReceiverRespVo();
        receiver.setUserId("123");
        receiver.setUserName("ReceiverName");

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class);
             MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class)) {

            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new BillInvoiceResource());

            when(hiveAsClient.populateDataFields("projectId", List.of("building1", "building2"))).thenReturn(
                    List.of("MCU1", "MCU2"));

            HiveClient.HiveBuBuildingResponse hiveResp = new HiveClient.HiveBuBuildingResponse();
            hiveResp.setId("buildingId1");
            hiveResp.setName("buildingIdName");
            hiveResp.setProjectId("projectId");
            when(hiveService.getBuildingByBuAndJdeRoomNo(anyList())).thenReturn(List.of(hiveResp));

            when(eFapiaoBillInvoiceRepository.findAll(any(Predicate.class), any(Pageable.class))).thenReturn(
                    pagedInvoices);
            when(billService.selectBatchBillReceivers(anySet())).thenReturn(Map.of(key, Set.of(receiver)));

            EFapiaoSendRecord sendRecord = new EFapiaoSendRecord();
            sendRecord.setBillFapiaoId(invoice.getId());
            sendRecord.setNotifyType(NotifyType.EMAIL);
            sendRecord.setSendStatus(SendStatus.MSG_SUCCESS);
            when(fapiaoSendRecordRepository.findAllByBillFapiaoIdIn(anyCollection())).thenReturn(List.of(sendRecord));

            var mockedInvoice = new EFapiaoBillInvoice();
            mockedInvoice.setEmailSendStatus(SendStatus.MSG_SENDING);
            when(eFapiaoBillInvoiceRepository.findById(anyLong())).thenReturn(Optional.of(mockedInvoice));

            // Act
            Page<BillInvoiceResource> result = kerryBillEFapiaoService.eFapiaoList(reqBo, pageable);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getTotalElements());
            assertEquals("TestPurchaser", result.getContent()
                                                .get(0)
                                                .getPurchaserName());
            assertEquals("12345", result.getContent()
                                        .get(0)
                                        .getDoco());
            assertEquals(BigDecimal.valueOf(100.00), result.getContent()
                                                           .get(0)
                                                           .getInvoiceAmt());
        }
    }

    @DisplayName("userEFapiaoList - 正常场景")
    @Test
    void userEFapiaoList_normalScenario() {
        // Arrange
        EFapiaoBillInvoice invoice = new EFapiaoBillInvoice();
        invoice.setId(1L);
        invoice.setPurchaserName("TestPurchaser");
        invoice.setInvoiceType(QS.getKipCode());
        invoice.setDoco("12345");
        invoice.setAn8("12345678");
        invoice.setMcu("MCU1");
        invoice.setJdeUnit("JdeUnit");
        invoice.setAmountWithTax(BigDecimal.valueOf(100.00));
        invoice.setInvoiceNo("INV123");

        Pageable pageable = PageRequest.of(0, 10);
        Page<EFapiaoBillInvoice> pagedInvoices = new PageImpl<>(List.of(invoice), pageable, 1);

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class);
             MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class)) {

            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new BizBillInvoiceResource());

            when(eFapiaoBillInvoiceRepository.findAll(any(Predicate.class), any(Pageable.class))).thenReturn(
                    pagedInvoices);
            when(bizContentReadRepository.findContentIdsByTypeAndUserIdAndContentIds(any(), anyLong(),
                                                                                     any())).thenReturn(
                    List.of(123L, 456L));

            BizBillEFapiaoSearchReqBo reqBo = new BizBillEFapiaoSearchReqBo();

            // Act
            Page<BizBillInvoiceResource> result = kerryBillEFapiaoService.userEFapiaoList(reqBo, pageable);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getTotalElements());
            assertEquals("TestPurchaser", result.getContent()
                                                .get(0)
                                                .getPurchaserName());
            assertEquals(BigDecimal.valueOf(100.00), result.getContent()
                                                           .get(0)
                                                           .getInvoiceAmt());
        }
    }

    @DisplayName("queryUrlsByBillInvoiceId - 正常场景")
    @Test
    void queryUrlsByBillInvoiceId_normalScenario() {
        // Arrange
        long invoiceId = 1L;

        EFapiaoBillInvoice invoice = new EFapiaoBillInvoice();
        invoice.setId(invoiceId);
        invoice.setPdfUrl(randomString());
        invoice.setXmlUrl(randomString());

        when(eFapiaoBillInvoiceRepository.findById(invoiceId)).thenReturn(Optional.of(invoice));

        // Act
        String result = kerryBillEFapiaoService.queryUrlsByBillInvoiceId(invoiceId);

        // Assert
        assertNotNull(result);
    }

    @DisplayName("queryUrlsByBillInvoiceId - 异常场景：发票ID不存在")
    @Test
    void queryUrlsByBillInvoiceId_invoiceNotFound() {
        // Arrange
        long invoiceId = 1L;

        when(eFapiaoBillInvoiceRepository.findById(invoiceId)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(RuntimeException.class, () -> kerryBillEFapiaoService.queryUrlsByBillInvoiceId(invoiceId));
    }

    @DisplayName("handleSmsCallback - 正常场景")
    @Test
    void handleSmsCallback_normalScenario() {
        // Arrange
        SmsResultCallbackVo callbackVo = new SmsResultCallbackVo();
        callbackVo.setRequestId(12345L);

        SmsStatusVo statusVo = new SmsStatusVo();
        statusVo.setPhone("1234567890");
        statusVo.setState(true);
        statusVo.setDeliverTime("2025-01-01 10:00:00");
        statusVo.setMessage("Success");

        callbackVo.setResults(List.of(statusVo));

        EFapiaoSendRecord sendRecord = new EFapiaoSendRecord();
        sendRecord.setRequestId("12345");
        sendRecord.setSms("1234567890");
        sendRecord.setSendStatus(SendStatus.MSG_SENDING);

        when(fapiaoSendRecordRepository.findAllByRequestId(anyString())).thenReturn(List.of(sendRecord));
        when(fapiaoSendRecordRepository.findAllByBatchNo(anyString())).thenReturn((List.of(sendRecord)));
        when(eFapiaoBillInvoiceRepository.findById(anyLong())).thenReturn(Optional.empty());

        // Act
        kerryBillEFapiaoService.handleSmsCallback(callbackVo);

        // Assert
        verify(fapiaoSendRecordRepository, times(1)).saveAll(anyList());
        assertEquals(SendStatus.MSG_SUCCESS, sendRecord.getSendStatus());
        assertEquals("Success", sendRecord.getSendDesc());
    }

    @DisplayName("handleSmsCallback - 异常场景：无发送记录")
    @Test
    void handleSmsCallback_noSendRecords() {
        // Arrange
        SmsResultCallbackVo callbackVo = new SmsResultCallbackVo();
        callbackVo.setRequestId(12345L);

        when(fapiaoSendRecordRepository.findAllByRequestId("12345")).thenReturn(Collections.emptyList());

        // Act
        kerryBillEFapiaoService.handleSmsCallback(callbackVo);

        // Assert
        verify(fapiaoSendRecordRepository, never()).saveAll(anyList());
    }

    @DisplayName("异常场景：无符合条件的发票")
    @Test
    void testEFapiaoList_noInvoices() {
        // Arrange
        BillEFapiaoSearchReqBo reqBo = new BillEFapiaoSearchReqBo();
        reqBo.setProjectId("PROJECT001");
        reqBo.setBuildingIds(List.of("BUILDING001", "BUILDING002"));

        Mockito.when(eFapiaoBillInvoiceRepository.findAll(any(Predicate.class))).thenReturn(Collections.emptyList());

        // Act
        List<BillInvoiceResource> result = kerryBillEFapiaoService.eFapiaoList(reqBo);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @DisplayName("正常场景：处理邮件回调成功")
    @Test
    void testHandleEmailCallback_success() {
        // Arrange
        var emailResultVo = new EmailResultVo();
        emailResultVo.setRequestId("12345");

        var statusDto = new EmailStatusDto();
        statusDto.setEmail("<EMAIL>");
        statusDto.setState(true);
        statusDto.setMessage("Success");
        statusDto.setDeliverTime("2025-04-29 09:00:00");
        emailResultVo.setResults(List.of(statusDto));

        var sendRecord = new EFapiaoSendRecord();
        sendRecord.setRequestId("12345");
        sendRecord.setBillFapiaoId(1234L);
        sendRecord.setEmail("<EMAIL>");
        sendRecord.setSendStatus(SendStatus.MSG_SENDING);

        Mockito.when(fapiaoSendRecordRepository.findAllByRequestId(anyString())).thenReturn(List.of(sendRecord));
        Mockito.when(fapiaoSendRecordRepository.findAllByBatchNo(any())).thenReturn(List.of(sendRecord));
        Mockito.when(eFapiaoBillInvoiceRepository.findById(anyLong())).thenReturn(Optional.empty());

        // Act
        kerryBillEFapiaoService.handleEmailCallback(emailResultVo);

        // Assert
        Mockito.verify(fapiaoSendRecordRepository, times(1)).saveAll(Mockito.anyList());
        assertEquals(SendStatus.MSG_SUCCESS, sendRecord.getSendStatus());
        assertEquals("Success", sendRecord.getSendDesc());
    }

    @DisplayName("异常场景：无发送记录")
    @Test
    void testHandleEmailCallback_noSendRecords() {
        // Arrange
        EmailResultVo emailResultVo = new EmailResultVo();
        emailResultVo.setRequestId("12345");

        Mockito.when(fapiaoSendRecordRepository.findAllByRequestId("12345")).thenReturn(Collections.emptyList());

        // Act
        kerryBillEFapiaoService.handleEmailCallback(emailResultVo);

        // Assert
        Mockito.verify(fapiaoSendRecordRepository, Mockito.never()).saveAll(Mockito.anyList());
    }

    @DisplayName("异常场景：无回调结果")
    @Test
    void testHandleEmailCallback_noResults() {
        // Arrange
        EmailResultVo emailResultVo = new EmailResultVo();
        emailResultVo.setRequestId("12345");
        emailResultVo.setResults(Collections.emptyList());

        EFapiaoSendRecord sendRecord = new EFapiaoSendRecord();
        sendRecord.setRequestId("12345");
        sendRecord.setEmail("<EMAIL>");
        sendRecord.setSendStatus(SendStatus.MSG_SENDING);

        Mockito.when(fapiaoSendRecordRepository.findAllByRequestId("12345")).thenReturn(List.of(sendRecord));

        // Act
        kerryBillEFapiaoService.handleEmailCallback(emailResultVo);

        // Assert
        Mockito.verify(fapiaoSendRecordRepository, Mockito.never()).saveAll(Mockito.anyList());
    }

    @DisplayName("异常场景：无回调结果")
    @Test
    void testHandleEmailCallback_param_null() {
        // Act and Assert
        assertDoesNotThrow(() ->kerryBillEFapiaoService.handleEmailCallback(null));
    }

}