package com.kerryprops.kip.bill;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Collection;
import java.util.Map;

/**
 * 集成测试基类，用于为基于 MockMvc 的测试提供通用方法.
 * 继承于 BaseIntegrationTest，封装了常用 HTTP 请求方法的实现.
 * 提供了格式化 URL、发送 HTTP 各类请求（GET、POST、PUT、DELETE）等方法，便于测试类直接调用.
 *
 * <AUTHOR> Yu
 */
@AutoConfigureMockMvc
public abstract class BaseControllerIntegrationTest extends BaseIntegrationTest {

    private static final ObjectMapper MAPPER = new ObjectMapper();

    @Resource
    protected MockMvc mockMvc;

    /**
     * 格式化URL字符串，并使用提供的参数替换URI变量.
     *
     * @param url          原始URL字符串，不能为空.
     * @param uriVariables 用于替换URL中URI变量的参数，不能为空.
     * @return 格式化后的URL字符串.
     * @throws IllegalArgumentException 如果url或uriVariables为空，则抛出此异常.
     */
    protected String formatUrl(String url, Object... uriVariables) {
        if (url == null || uriVariables == null) {
            throw new IllegalArgumentException("URL and uriVariables must not be null");
        }
        return UriComponentsBuilder.fromUriString(url).buildAndExpand(uriVariables).toUriString();
    }

    /**
     * 将JSON字符串转换为指定类型的Java对象.如果JSON是一个数组，则默认返回数组中的第一个元素作为结果.
     *
     * @param <T>          返回的对象类型.
     * @param jsonString   JSON格式的字符串，不能为null.
     * @param responseType 转换目标的Java类型，不能为null.
     * @return 转换后的Java对象，具体类型由responseType决定.
     * @throws IllegalArgumentException 如果jsonString或responseType为null，则抛出该异常.
     */
    @SneakyThrows
    protected <T> T parseJson(String jsonString, Class<T> responseType) {
        JsonNode jsonNode = MAPPER.readTree(jsonString);
        if (jsonNode.isArray()) {
            return MAPPER.treeToValue(jsonNode.get(0), responseType);
        }
        return MAPPER.treeToValue(jsonNode, responseType);
    }

    /**
     * 发送GET请求，该方法支持可选的请求头参数.
     *
     * @param url     请求的URL地址，不能为空.
     * @param headers 可选的请求头参数，可以是多个键值对形式的字符串.
     * @return 服务端响应的字符串内容.
     */
    protected String sendGetRequest(String url, String... headers) {
        return sendGetRequest(url, null, headers);
    }

    /**
     * 发送GET请求，支持携带查询参数和请求头.
     *
     * @param url     请求的URL地址，不能为空.
     * @param dto     请求的查询参数对象，可以是一个键值对的Map或Bean对象，允许为空.
     * @param headers 可选的请求头参数，可以是多个键值对形式的字符串，允许为空.
     * @return 服务端响应的字符串内容.
     */
    protected String sendGetRequest(String url, Object dto, String... headers) {
        MockHttpServletRequestBuilder builder = MockMvcRequestBuilders.get(url);
        builder.contentType(MediaType.APPLICATION_FORM_URLENCODED);
        setQueryParam(builder, dto);
        return sendRequest(builder, headers);
    }

    /**
     * 发送一个PUT请求，并返回响应内容.
     *
     * @param url     请求的URL地址，不能为空.
     * @param param   请求体的参数对象，可以是任意对象，如果参数为null，发送空请求体.
     * @param headers 可选的请求头参数，可以是多个键值对形式的字符串，允许为空.
     * @return 服务端响应的字符串内容.
     */
    protected String sendPutRequest(String url, Object param, String... headers) {
        MockHttpServletRequestBuilder builder = MockMvcRequestBuilders.put(url);
        builder.contentType(MediaType.APPLICATION_JSON)
                .content(toBody(param));
        return sendRequest(builder, headers);
    }

    /**
     * 发送POST请求，并返回服务端响应的字符串内容.
     *
     * @param url     请求的URL地址，不能为空.
     * @param param   请求体的参数对象，可以是任意对象，如果参数为null，发送空请求体.
     * @param headers 可选的请求头参数，可以是多个键值对形式的字符串，允许为空.
     * @return 服务端响应的字符串内容.
     */
    protected String sendPostRequest(String url, Object param, String... headers) {
        MockHttpServletRequestBuilder builder = MockMvcRequestBuilders.post(url);
        builder.contentType(MediaType.APPLICATION_JSON)
                .content(toBody(param));
        return sendRequest(builder, headers);
    }

    /**
     * 发送一个DELETE请求，不携带请求体，并返回服务端响应的字符串内容.
     *
     * @param url     请求的URL地址，不能为空.
     * @param headers 可选的请求头参数，可以是多个键值对形式的字符串，允许为空.
     * @return 服务端响应的字符串内容.
     */
    protected String sendDeleteRequest(String url, String... headers) {
        return sendDeleteRequest(url, null, headers);
    }

    protected String sendDeleteRequest(String url, Object param, String... headers) {
        MockHttpServletRequestBuilder builder = MockMvcRequestBuilders.delete(url);
        builder.contentType(MediaType.APPLICATION_FORM_URLENCODED);
        setQueryParam(builder, param);
        return sendRequest(builder, headers);
    }

    @SneakyThrows
    private byte[] toBody(Object param) {
        if (param == null) {
            return new byte[0];
        }
        if (param instanceof String str) {
            return str.getBytes();
        }
        return MAPPER.writeValueAsBytes(param);
    }

    @SneakyThrows
    private String sendRequest(MockHttpServletRequestBuilder builder, String... headers) {
        return mockMvc.perform(builder.headers(buildHttpHeaders(headers)))
                .andReturn().getResponse().getContentAsString();
    }

    @SuppressWarnings("unchecked")
    private void setQueryParam(MockHttpServletRequestBuilder builder, Object dto) {
        if (dto == null) {
            return;
        }

        if (dto instanceof Map<?, ?> map) {
            map.forEach((k, v) -> appendRequestParam(builder, k, v));
            return;
        }

        BeanMap dtoMap = BeanMap.create(dto);
        dtoMap.forEach((k, v) -> appendRequestParam(builder, k, v));
    }

    private void appendRequestParam(MockHttpServletRequestBuilder builder, Object k, Object v) {
        if (v == null || v instanceof MultipartFile) {
            return;
        }
        if (v instanceof Collection<?> c) {
            for (Object v1 : c) {
                builder.queryParam(k.toString(), v1.toString());
            }
        } else {
            builder.queryParam(k.toString(), v.toString());
        }
    }

    private HttpHeaders buildHttpHeaders(String... headers) {
        HttpHeaders httpHeaders = new HttpHeaders();
        if (headers == null) {
            return httpHeaders;
        }
        int length = headers.length;
        if ((length & 1) != 0) {
            throw new IllegalArgumentException("headers.length 必须为2的倍数");
        }
        for (int i = 0; i < length; i = i + 2) {
            String header = headers[i];
            String headerValue = headers[i + 1];
            httpHeaders.add(header, headerValue);
        }
        return httpHeaders;
    }

}
