package com.kerryprops.kip.bill.webservice;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.enums.InvoiceRecordStatus;
import com.kerryprops.kip.bill.common.enums.InvoiceState;
import com.kerryprops.kip.bill.common.enums.InvoiceTypeEnum;
import com.kerryprops.kip.bill.common.enums.SendStatus;
import com.kerryprops.kip.bill.common.exceptions.AppException;
import com.kerryprops.kip.bill.dao.BillSendConfigAn8LinkRepository;
import com.kerryprops.kip.bill.dao.BillSendConfigRepository;
import com.kerryprops.kip.bill.dao.EFapiaoBillInvoiceRepository;
import com.kerryprops.kip.bill.dao.entity.BillSendConfig;
import com.kerryprops.kip.bill.dao.entity.BillSendConfigAn8Link;
import com.kerryprops.kip.bill.dao.entity.EFapiaoBillInvoice;
import com.kerryprops.kip.bill.dao.entity.EFapiaoJDEBill;
import com.kerryprops.kip.bill.webservice.vo.req.BillInvoiceSendRequest;
import com.kerryprops.kip.bill.webservice.vo.req.BillInvoiceUploadImportVo;
import com.kerryprops.kip.bill.webservice.vo.req.BillInvoiceUploadVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillInvoiceUploadResultRespVo;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.springframework.mock.web.MockMultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;

/**
 * <AUTHOR> 2023-08-15 14:52:13
 **/
class StaffBillInvoiceFacadeTest extends BaseIntegrationTest {

    private final String EXPORT_RESULT_FAIL = "失败";

    @Resource
    StaffBillInvoiceFacade staffBillInvoiceFacade;

    @Resource
    EFapiaoBillInvoiceRepository eFapiaoBillInvoiceRepository;

    @Resource
    BillSendConfigRepository billSendConfigRepository;

    @Resource
    BillSendConfigAn8LinkRepository billSendConfigAn8LinkRepository;

    @Test
    void _01_sendBillInvoiceSms_success() {
        EFapiaoBillInvoice qo = buildBillInvoice();

        eFapiaoBillInvoiceRepository.save(qo);

        BillSendConfig qo1 = buildSendConfig(qo);

        billSendConfigRepository.save(qo1);

        BillSendConfigAn8Link qo2 = buildLink(qo, qo1.getId());

        billSendConfigAn8LinkRepository.save(qo2);

        BillInvoiceSendRequest dto = new BillInvoiceSendRequest();
        dto.setInvoiceIds(Set.of(qo.getId()));
        dto.setProjectId("192");

        Integer sendBillInvoiceSmsNum = staffBillInvoiceFacade.sendBillInvoiceSms(dto);
        Assertions.assertEquals(1, sendBillInvoiceSmsNum);
    }

    @Test
    void _02_getImportBillInvoiceList_param_null_error() {
        Assertions.assertThrows(AppException.class, () -> staffBillInvoiceFacade.getImportBillInvoiceList(null, null));
    }

    @Test
    void _03_getImportBillInvoiceList_head_lost_fail() throws IOException {
        File file = new File("src/test/resources/templates/import_jde_xforce/mock_import_jde_head_lost_fail_data.xlsx");
        InputStream inputStream = new FileInputStream(file);
        MockMultipartFile mockMultipartFile = new MockMultipartFile("name_test", inputStream);

        // 执行待验证部分
        Assertions.assertThrows(NullPointerException.class
                , () -> staffBillInvoiceFacade.getImportBillInvoiceList(mockMultipartFile, null));
    }

    @Test
    void _04_getImportBillInvoiceList_success() throws IOException {
        File file = new File("src/test/resources/templates/import_jde_xforce/mock_import_jde_success_data.xlsx");
        InputStream inputStream = new FileInputStream(file);
        MockMultipartFile mockMultipartFile = new MockMultipartFile("name_test", inputStream);

        // 执行待验证部分
        List<BillInvoiceUploadImportVo> billInvoiceUploadImportVos
                = staffBillInvoiceFacade.getImportBillInvoiceList(mockMultipartFile, null);

        assertTrue(CollectionUtils.isNotEmpty(billInvoiceUploadImportVos));
        billInvoiceUploadImportVos.forEach(importVo -> {
            Assertions.assertTrue(StringUtils.isNotEmpty(importVo.getKco()));
            Assertions.assertTrue(StringUtils.isNotEmpty(importVo.getBillType()));
            Assertions.assertTrue(0 != importVo.getDoc());
            Assertions.assertTrue(StringUtils.isNotEmpty(importVo.getPaymentItem()));
        });
    }

    @Disabled
    @Test
    void _05_uploadBillInvoice_query_bill_error() {
        doThrow(new RuntimeException()).when(eFapiaoJdeBillService).queryBySalesBillNo(ArgumentMatchers.anyString());

        // 执行待验证部分
        BillInvoiceUploadVo billInvoiceUploadVo = new BillInvoiceUploadVo();
        BillInvoiceUploadResultRespVo uploadResultRespVo
                = staffBillInvoiceFacade.uploadBillInvoice(billInvoiceUploadVo);

        Assertions.assertEquals(EXPORT_RESULT_FAIL, uploadResultRespVo.getInvoiceResult());
        assertTrue(StringUtils.isNotEmpty(uploadResultRespVo.getFailReason()));
    }

    @Disabled
    @Test
    void _06_uploadBillInvoice_query_bill_null() {
        doReturn(null).when(eFapiaoJdeBillService).queryBySalesBillNo(ArgumentMatchers.anyString());

        // 执行待验证部分
        BillInvoiceUploadVo billInvoiceUploadVo = new BillInvoiceUploadVo();
        BillInvoiceUploadResultRespVo uploadResultRespVo
                = staffBillInvoiceFacade.uploadBillInvoice(billInvoiceUploadVo);

        Assertions.assertEquals(EXPORT_RESULT_FAIL, uploadResultRespVo.getInvoiceResult());
        assertTrue(StringUtils.isNotEmpty(uploadResultRespVo.getFailReason()));
    }

    @Disabled
    @Test
    void _07_uploadBillInvoice_invoice_upload_error() {
        doReturn(EFapiaoJDEBill.builder().build()).when(eFapiaoJdeBillService).queryBySalesBillNo(ArgumentMatchers.anyString());
        doThrow(new RuntimeException()).when(eFapiaoBillService).invoiceUpload(ArgumentMatchers.any());

        // 执行待验证部分
        BillInvoiceUploadVo billInvoiceUploadVo = new BillInvoiceUploadVo();
        BillInvoiceUploadResultRespVo uploadResultRespVo
                = staffBillInvoiceFacade.uploadBillInvoice(billInvoiceUploadVo);

        Assertions.assertEquals(EXPORT_RESULT_FAIL, uploadResultRespVo.getInvoiceResult());
        assertTrue(StringUtils.isNotEmpty(uploadResultRespVo.getFailReason()));
    }

    @Disabled
    @Test
    void _08_uploadBillInvoice_success() {
        doReturn(EFapiaoJDEBill.builder().build()).when(eFapiaoJdeBillService).queryBySalesBillNo(ArgumentMatchers.anyString());
        doNothing().when(eFapiaoBillService).invoiceUpload(ArgumentMatchers.any());

        // 执行待验证部分
        BillInvoiceUploadVo billInvoiceUploadVo = new BillInvoiceUploadVo();
        BillInvoiceUploadResultRespVo uploadResultRespVo
                = staffBillInvoiceFacade.uploadBillInvoice(billInvoiceUploadVo);

        final String EXPORT_RESULT_SUCCESS = "成功";
        Assertions.assertEquals(EXPORT_RESULT_SUCCESS, uploadResultRespVo.getInvoiceResult());
        assertTrue(StringUtils.isEmpty(uploadResultRespVo.getFailReason()));
    }

    private EFapiaoBillInvoice buildBillInvoice() {
        EFapiaoBillInvoice qo = new EFapiaoBillInvoice();
        qo.setKco("11");
        qo.setCompanyCode("test1");
        qo.setDoc(********);
        qo.setPaymentItem("001");
        qo.setAn8("********");
        qo.setPurchaserName("xxx有限公司");
        qo.setPurchaserTaxNo("91110105625911314P");
        qo.setPurchaserAddress("购方公司地址");
        qo.setPurchaserBankName("花旗银行北京分行**********");
        qo.setInvoiceType(InvoiceTypeEnum.QC.getKipCode());
        qo.setMailingAddress("xxx");
        qo.setMcu("1");
        qo.setBillType("RN");
        qo.setDoco("250644");
        qo.setBillCode("U02M");
        qo.setGoodsTaxNo("商品货物税收分类编码");
        qo.setItemName("电费");
        qo.setItemSpec("2015/07/01-2015/09/30");
        qo.setAmountWithoutTax(new BigDecimal("10005"));
        qo.setTaxRate(new BigDecimal("6000"));
        qo.setTaxAmount(new BigDecimal("830"));
        qo.setAmountWithTax(new BigDecimal("830"));
        qo.setTaxDiscount("");
        qo.setTaxDiscountDesc("");
        qo.setSecondName("");
        qo.setFormatedDoco("");
        qo.setIsContactPerson("");
        qo.setIsTaxpayer("");
        qo.setTaxClassifyDesc("");
        qo.setConstant1("");
        qo.setConstant2("");
        qo.setConstant3(new BigDecimal("0"));
        qo.setConstant4(new BigDecimal("0"));
        qo.setConstant5(new BigDecimal("0"));
        qo.setExtendedDoc("");
        qo.setBillDrawDate("");
        qo.setDscrp1("");
        qo.setDscrp2("");
        qo.setDscrp3("");
        qo.setUserName("");
        qo.setProgramId("");
        qo.setConstant6("");
        qo.setCreateDateJde(0);
        qo.setCreateTimeJde(0);
        qo.setEvent1("");
        qo.setEvent2("");
        qo.setEvent3("");
        qo.setSalesBillNo("");
        qo.setEmailSendStatus(SendStatus.MSG_NOT_SEND);
        qo.setSmsSendStatus(SendStatus.MSG_NOT_SEND);
        qo.setMessageSendStatus(SendStatus.MSG_NOT_SEND);
        qo.setJdeUnit("");
        qo.setInvoiceRecordStatus(InvoiceRecordStatus.COMPLETED);
        qo.setSellerName("");
        qo.setPdfUrl("https://kip-public-qa.oss-cn-shanghai.aliyuncs.com/test.txt");
        qo.setInvoiceNo("");
        qo.setInvoiceCode("");
        qo.setBizType("");
        qo.setState(InvoiceState.UNKNOWN);
        qo.setErrorMessage("");
        qo.setPaperDrewDate("");
        qo.setCreatedTime(ZonedDateTime.now());
        qo.setUpdatedTime(ZonedDateTime.now());
        return qo;
    }

    private BillSendConfig buildSendConfig(EFapiaoBillInvoice qo) {
        BillSendConfig qo1 = new BillSendConfig();
        // qo1.setId(0L);
        qo1.setDoco(qo.getDoco());
        qo1.setPhoneNumber("19999999999");
        qo1.setTenantManagerId("");
        qo1.setEmailUsername("张三");
        qo1.setEmail("<EMAIL>");
        // qo1.setLoginNo("");
        // qo1.setMcu("");
        // qo1.setUnit("");
        qo1.setIsDel(0);
        qo1.setCreatedTime(ZonedDateTime.now());
        qo1.setUpdatedTime(ZonedDateTime.now());
        return qo1;
    }

    private BillSendConfigAn8Link buildLink(EFapiaoBillInvoice qo, Long configId) {
        BillSendConfigAn8Link qo2 = new BillSendConfigAn8Link();
        // qo2.setId(1L);
        qo2.setConfigId(configId);
        qo2.setAn8(qo.getAn8());
        qo2.setAlph("张三");
        qo2.setIsDel(0);
        qo2.setCreatedTime(ZonedDateTime.now());
        qo2.setUpdatedTime(ZonedDateTime.now());
        return qo2;
    }

}