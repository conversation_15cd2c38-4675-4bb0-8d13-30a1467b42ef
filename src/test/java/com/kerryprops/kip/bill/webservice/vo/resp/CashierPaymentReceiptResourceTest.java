package com.kerryprops.kip.bill.webservice.vo.resp;

import com.kerryprops.kip.bill.common.enums.CashierPayStatus;
import com.kerryprops.kip.bill.common.enums.PaymentPayType;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static com.kerryprops.kip.bill.utils.RandomUtil.randomObject;
import static org.assertj.core.api.Assertions.assertThat;

class CashierPaymentReceiptResourceTest {

    @Test
    @DisplayName("正确映射有效输入数据到CashierPaymentReceiptResource")
    void testOf_WithValidInput_ShouldMapCorrectly() {
        // Arrange
        AptPaymentInfo paymentInfo = randomObject(AptPaymentInfo.class);
        AptPay aptPay = randomObject(AptPay.class);

        // Act
        CashierPaymentReceiptResource result = CashierPaymentReceiptResource.of(paymentInfo, aptPay);

        // Assert
        assertThat(result.getPaymentInfoId()).isEqualTo(paymentInfo.getId());
        assertThat(result.getProjectId()).isEqualTo(paymentInfo.getProjectId());
        assertThat(result.getBuildingId()).isEqualTo(paymentInfo.getBuildingId());
        assertThat(result.getFloorId()).isEqualTo(paymentInfo.getFloorId());
        assertThat(result.getRoomId()).isEqualTo(paymentInfo.getRoomId());
        assertThat(result.getPayAct()).isEqualTo(paymentInfo.getPayAct());
        assertThat(result.getPspTransNo()).isEqualTo(paymentInfo.getPspTransNo());
        assertThat(result.getAdvanceAmt()).isEqualTo(paymentInfo.getAdvanceAmount());
        assertThat(result.getAccountFromType()).isEqualTo(paymentInfo.getCreateBy());
        assertThat(result.getBillPayModule()).isEqualTo(paymentInfo.getBillPayModule());
        assertThat(result.getDeletedAt()).isEqualTo(paymentInfo.getDeleted());
        assertThat(result.getPaymentCate()).isEqualTo(paymentInfo.getPaymentCate()
                                                                 .name());
        assertThat(result.getTotalAmt()).isEqualTo(BigDecimal.valueOf(paymentInfo.getAmt()));
        assertThat(result.getStatus()).isEqualTo(CashierPayStatus.of(paymentInfo, aptPay));
        assertThat(result.getFeeName()).isEqualTo(paymentInfo.getFeeName());
    }

    @Test
    @DisplayName("当AptPay为空时应使用PaymentInfo中的描述")
    void testOf_WithNullAptPay_UsesPaymentInfoDescription() {
        // Arrange
        AptPaymentInfo paymentInfo = randomObject(AptPaymentInfo.class);
        paymentInfo.setDescription("Test Description");

        // Act
        CashierPaymentReceiptResource result = CashierPaymentReceiptResource.of(paymentInfo, null);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getComment()).isEqualTo("Test Description");
    }

    @Test
    @DisplayName("当支付日期为空时应设置为null")
    void testOf_WithNullPayDate_SetsNullPayDate() {
        // Arrange
        AptPaymentInfo paymentInfo = randomObject(AptPaymentInfo.class);
        paymentInfo.setPaymentTime(null);

        // Act
        CashierPaymentReceiptResource result = CashierPaymentReceiptResource.of(paymentInfo, null);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getPayDate()).isNull();
    }

    @Test
    @DisplayName("当支付类型为空时应从PayTypeEnum映射")
    void testOf_WithBlankPayType_MapsFromPayTypeEnum() {
        // Arrange
        AptPaymentInfo paymentInfo = randomObject(AptPaymentInfo.class);
        paymentInfo.setPayTypeInfo("");
        paymentInfo.setPayType(PaymentPayType.WECHAT);

        // Act
        CashierPaymentReceiptResource result = CashierPaymentReceiptResource.of(paymentInfo, null);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getPayType()).isEqualTo(PaymentPayType.WECHAT.getInfo());
    }

    @Test
    @DisplayName("当支付类型未知时应返回null")
    void testGetPayType_WithUnknownPayType_ReturnsNull() {
        // Arrange
        CashierPaymentReceiptResource resource = new CashierPaymentReceiptResource();
        resource.setPayType(PaymentPayType.UNKNOWN.getInfo());

        // Act
        String result = resource.getPayType();

        // Assert
        assertThat(result).isNull();
    }

    @Test
    @DisplayName("当支付类型有效时应返回原始支付类型")
    void testGetPayType_WithValidPayType_ReturnsPayType() {
        // Arrange
        CashierPaymentReceiptResource resource = new CashierPaymentReceiptResource();
        resource.setPayType("Credit Card");

        // Act
        String result = resource.getPayType();

        // Assert
        assertThat(result).isEqualTo("Credit Card");
    }

}