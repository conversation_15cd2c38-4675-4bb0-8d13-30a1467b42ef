package com.kerryprops.kip.bill.webservice.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.enums.AptPayVerifyStatus;
import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import com.kerryprops.kip.bill.common.enums.BillPayModule;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.PayCancelTypeEnum;
import com.kerryprops.kip.bill.common.enums.PaymentCateEnum;
import com.kerryprops.kip.bill.common.utils.PrimaryKeyUtil;
import com.kerryprops.kip.bill.dao.AptPayConfigRepository;
import com.kerryprops.kip.bill.dao.AptPayRepository;
import com.kerryprops.kip.bill.dao.AptPaymentInfoRepository;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPayConfig;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.utils.RandomUtil;
import com.kerryprops.kip.bill.webservice.vo.req.CashierFeePayRequest;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierFeePayResource;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import com.kerryprops.kip.pmw.client.resource.HeaderResource;
import com.kerryprops.kip.pmw.client.resource.SessionOutputResource;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.springframework.web.util.UriComponentsBuilder;

import java.math.BigDecimal;
import java.net.URI;
import java.util.Date;
import java.util.Iterator;

import static org.mockito.Mockito.doReturn;

/**
 * <AUTHOR> 2023-12-19 10:21:48
 **/
class CounterCashierFeeControllerTest extends BaseIntegrationTest {

    @Resource
    AptPayConfigRepository aptPayConfigRepository;

    @Resource
    AptPaymentInfoRepository aptPaymentInfoRepository;

    @Resource
    AptPayRepository aptPayRepository;

    @AfterEach
    void cleanup() {
        aptPayRepository.deleteAll();
        aptPayConfigRepository.deleteAll();
        aptPaymentInfoRepository.deleteAll();
    }

    @Test
    void prepay_ok() {
        SessionOutputResource.SessionInfoBodyResource sessionInfo = new SessionOutputResource.SessionInfoBodyResource();
        sessionInfo.setSessionId("1");
        SessionOutputResource.SessionOutputBodyResource body = new SessionOutputResource.SessionOutputBodyResource();
        body.setSessionInfo(sessionInfo);
        SessionOutputResource sessionOutputResource = new SessionOutputResource(new HeaderResource(), body, "");
        doReturn(sessionOutputResource)
                .when(paymentClientService)
                .createPaymentSession(ArgumentMatchers.any(),
                        ArgumentMatchers.any(),
                        ArgumentMatchers.any());

        String projectId = "HKC";
        String buildingId = "QHKC-A2";
        String roomId = "8aaa82017ee81259017ef5e13d9f0000";
        String payType = "微信支付";

        AptPayConfig aptPayConfig = new AptPayConfig();
        aptPayConfig.setChannel(BillPayChannel.OFFLINE);
        aptPayConfig.setPaymentType(payType);
        aptPayConfig.setPaymentCate("A003");
        aptPayConfig.setPaymentDetail("1");
        aptPayConfig.setBankAccount("1");
        aptPayConfig.setMcu("1");
        aptPayConfig.setProjectId(projectId);
        aptPayConfig.setBuildingId(buildingId);
        aptPayConfig.setPositionItem(new PositionItemResponse());
        aptPayConfig.setTax(0.0D);
        aptPayConfig.setMax(0.0D);
        aptPayConfig.setComments("1");
        aptPayConfig.setCompanyCode("1");
        aptPayConfig.setDeleteAt(0L);
        aptPayConfig.setCreateTime(new Date());
        aptPayConfig.setUpdateTime(new Date());
        aptPayConfigRepository.insert(aptPayConfig);

        CashierFeePayRequest request = new CashierFeePayRequest();
        request.setProjectId(projectId);
        request.setBuildingId(buildingId);
        request.setRoomId(roomId);
        request.setBillDate(new Date());
        request.setPayType(payType);
        request.setDescription("111");
        request.setFeeId(11L);
        request.setFeeName("111");
        request.setBillAmount(new BigDecimal("1.1"));
        request.setPspTransNo("");
        CashierFeePayResource vo = restTemplate.postForObject("/cashier/apt/bill/fee/prepay", request, CashierFeePayResource.class);

        Assertions.assertNotNull(vo);
        Assertions.assertNotNull(vo.getPayAct());

        URI uri = UriComponentsBuilder.fromPath("/cashier/apt/bill/fee/pay/detail")
                .queryParam("paymentInfoId", vo.getPaymentInfoId())
                .build()
                .toUri();
        String vo2 = restTemplate.getForObject(uri, String.class);

        Assertions.assertNotNull(vo2);
    }

    @Test
    void queryCashierAptPays_ok() throws JsonProcessingException {
        String projectId = "192";
        String buildingId = "QHKC-A2";

        AptPay aptPay = mockCashierFeePay(projectId, buildingId);
        aptPayRepository.insert(aptPay);

        URI uri = UriComponentsBuilder.fromPath("/cashier/apt/bill/fee/pays")
                .queryParam("projectId", projectId)
                .queryParam("isFinanceStaff", 1)
                .build()
                .toUri();
        String vo = restTemplate.getForObject(uri, String.class);

        Assertions.assertNotNull(vo);
        JsonNode jsonNode = objectMapper.readTree(vo);
        JsonNode content = jsonNode.path("content");
        Assertions.assertNotNull(content);
        Iterator<JsonNode> iterator = content.iterator();
        Assertions.assertTrue(iterator.hasNext());
        Assertions.assertNull(iterator.next().path("status").asText(null));
    }

    @Test
    void queryCashierAptPays_timeout_cancel_ok() throws JsonProcessingException {
        AptPaymentInfo paymentInfo = RandomUtil.randomObject(AptPaymentInfo.class);
        paymentInfo.setId(PrimaryKeyUtil.createPaymentId());
        paymentInfo.setCancelType(PayCancelTypeEnum.TIMEOUT_CANCELLED.name());
        paymentInfo.setDeleted(1);
        aptPaymentInfoRepository.insert(paymentInfo);

        String projectId = "192";
        String buildingId = "QHKC-A2";
        AptPay aptPay = mockCashierFeePay(projectId, buildingId);
        aptPay.setPaymentInfoId(paymentInfo.getId());
        aptPay.setDeletedAt(1);
        aptPay.setPayAct(paymentInfo.getPayAct());
        aptPayRepository.insert(aptPay);

        URI uri = UriComponentsBuilder.fromPath("/cashier/apt/bill/fee/pays")
                .queryParam("projectId", projectId)
                .queryParam("isFinanceStaff", 1)
                .build()
                .toUri();
        String vo = restTemplate.getForObject(uri, String.class);

        Assertions.assertNotNull(vo);
        JsonNode jsonNode = objectMapper.readTree(vo);
        JsonNode content = jsonNode.path("content");
        Assertions.assertNotNull(content);
        Iterator<JsonNode> iterator = content.iterator();
        Assertions.assertFalse(iterator.hasNext());

        AptPaymentInfo paymentInfo2 = RandomUtil.randomObject(AptPaymentInfo.class);
        paymentInfo2.setId(PrimaryKeyUtil.createPaymentId());
        paymentInfo2.setDeleted(0);
        paymentInfo2.setCancelType("");
        AptPay aptPay2 = mockCashierFeePay(projectId, buildingId);
        aptPay2.setPaymentInfoId(paymentInfo2.getId());
        aptPayRepository.insert(aptPay2);

        URI uri2 = UriComponentsBuilder.fromPath("/cashier/apt/bill/fee/pays")
                .queryParam("projectId", projectId)
                .queryParam("isFinanceStaff", 1)
                .build()
                .toUri();
        String vo2 = restTemplate.getForObject(uri2, String.class);

        Assertions.assertNotNull(vo2);
        JsonNode jsonNode2 = objectMapper.readTree(vo2);
        JsonNode content2 = jsonNode2.path("content");
        Assertions.assertNotNull(content2);
        Iterator<JsonNode> iterator2 = content2.iterator();
        Assertions.assertTrue(iterator2.hasNext());
        Assertions.assertNull(iterator2.next().path("status").asText(null));
    }

    @Test
    void feeOfflinePay_ok() {
        String projectId = "HKC";
        String buildingId = "QHKC-A2";
        String roomId = "8aaa82017ee81259017ef5e13d9f0000";
        String payType = "现金支付";

        AptPayConfig aptPayConfig = new AptPayConfig();
        aptPayConfig.setChannel(BillPayChannel.OFFLINE);
        aptPayConfig.setPaymentType(payType);
        aptPayConfig.setPaymentCate("A003");
        aptPayConfig.setPaymentDetail("1");
        aptPayConfig.setBankAccount("1");
        aptPayConfig.setMcu("1");
        aptPayConfig.setProjectId(projectId);
        aptPayConfig.setBuildingId(buildingId);
        aptPayConfig.setPositionItem(new PositionItemResponse());
        aptPayConfig.setTax(0.0D);
        aptPayConfig.setMax(0.0D);
        aptPayConfig.setComments("1");
        aptPayConfig.setCompanyCode("1");
        aptPayConfig.setDeleteAt(0L);
        aptPayConfig.setCreateTime(new Date());
        aptPayConfig.setUpdateTime(new Date());
        aptPayConfigRepository.insert(aptPayConfig);

        CashierFeePayRequest request = new CashierFeePayRequest();
        request.setProjectId(projectId);
        request.setBuildingId(buildingId);
        request.setRoomId(roomId);
        request.setBillDate(new Date());
        request.setPayType(payType);
        request.setDescription(payType);
        request.setFeeId(11L);
        request.setFeeName("2");
        request.setBillAmount(BigDecimal.ZERO);
        request.setPspTransNo("1");

        CashierFeePayResource vo = restTemplate.postForObject("/cashier/apt/bill/fee/offline/pay", request, CashierFeePayResource.class);
        Assertions.assertNotNull(vo);

        AptPaymentInfo paymentInfo = aptPaymentInfoRepository.findById(vo.getPaymentInfoId()).orElse(null);
        Assertions.assertNotNull(paymentInfo);
        Assertions.assertEquals(BillPaymentStatus.PAID, paymentInfo.getPaymentStatus());

    }

    private AptPay mockCashierFeePay(String projectId, String buildingId) {
        AptPay aptPay = new AptPay();
        aptPay.setPayDesc("");
        aptPay.setBeginDate("");
        aptPay.setEndDate("");
        aptPay.setPayConfigId(0L);
        aptPay.setPayChannel(BillPayChannel.ONLINE);
        aptPay.setPayType("");
        aptPay.setPaymentCate(PaymentCateEnum.UNKNOWN.name());
        aptPay.setTax(0.0D);
        aptPay.setTaxAmt(0.0D);
        aptPay.setTotalAmt(0.0D);
        aptPay.setPayDate(new Date());
        aptPay.setPayTranx("");
        aptPay.setPayAct("");
        aptPay.setPayDetail("");
        aptPay.setVerifyStatus(AptPayVerifyStatus.TO_BE_VERIFIED);
        aptPay.setSendJdeStatus(0);
        aptPay.setProjectId(projectId);
        aptPay.setBuildingId(buildingId);
        aptPay.setFloorId("");
        aptPay.setRoomId("");
        aptPay.setBu("");
        aptPay.setUnit("");
        aptPay.setAn8("");
        aptPay.setAlph("");
        aptPay.setDoco("");
        aptPay.setPositionItem(new PositionItemResponse());
        aptPay.setComments("");
        aptPay.setDeletedAt(0);
        aptPay.setDeletedBy("");
        aptPay.setDeletedTime(new Date());
        aptPay.setCreateBy("");
        aptPay.setPaymentInfoId(PrimaryKeyUtil.createPaymentId());
        aptPay.setVersionNum(0);
        aptPay.setStatus(0);
        aptPay.setAdvanceAmount(new BigDecimal("0"));
        aptPay.setBillPayModule(BillPayModule.CASHIER_FEE);
        aptPay.setFeeId(0L);
        aptPay.setFeeName("");
        aptPay.setFeeTax(new BigDecimal("0"));
        aptPay.setFeeTaxAmount(new BigDecimal("0"));
        aptPay.setCreateTime(new Date());
        aptPay.setUpdateTime(new Date());
        return aptPay;
    }

}