package com.kerryprops.kip.bill.webservice.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.dao.BillSendConfigAn8LinkRepository;
import com.kerryprops.kip.bill.dao.BillSendConfigRepository;
import com.kerryprops.kip.bill.dao.entity.BillSendConfig;
import com.kerryprops.kip.bill.dao.entity.BillSendConfigAn8Link;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Random;

public class BaseClass_StaffBillSendConfigControllerTest extends BaseIntegrationTest {

    @Autowired
    protected ObjectMapper mapper;

    @Autowired
    protected BillSendConfigRepository configRepository;

    @Autowired
    protected BillSendConfigAn8LinkRepository an8LinkRepository;

    protected String buildString(int length) {
        Random random = new Random();
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < length; i++) {
            builder.append(String.valueOf(Math.abs(random.nextInt() % 10)));
        }
        return builder.toString();
    }

    protected List<BillSendConfig> buildBillSendConfigList() {
        List<BillSendConfig> requestConfigList = Lists.list(
                BillSendConfig.builder().doco("123456").email("<EMAIL>")
                        .phoneNumber(StringUtils.EMPTY).loginNo(StringUtils.EMPTY)
                        .tenantManagerId(StringUtils.EMPTY).emailUsername(StringUtils.EMPTY)
                        .mcu("mcu1,mcu2,mcu3").unit("unit1,unit2,unit3,unit4").isDel(0)
                        .projectId("xx").buildingId("buildingId1_123")
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now()).build(),
                BillSendConfig.builder().doco("112233").email("<EMAIL>")
                        .phoneNumber("13764912168").loginNo("33254407")
                        .tenantManagerId(StringUtils.EMPTY).emailUsername(StringUtils.EMPTY)
                        .mcu("mcu2,mcu4").unit("unit2").isDel(0)
                        .createdTime(ZonedDateTime.now())
                        .projectId("xx").buildingId("buildingId1_123")
                        .updatedTime(ZonedDateTime.now()).build(),

                BillSendConfig.builder().doco("654321").email("<EMAIL>")
                        .phoneNumber(StringUtils.EMPTY).loginNo(StringUtils.EMPTY)
                        .tenantManagerId(StringUtils.EMPTY).emailUsername(StringUtils.EMPTY)
                        .mcu("mcu2,mcu4").unit("unit2").isDel(1)
                        .projectId("xx").buildingId("buildingId2_123")
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now()).build(),

                BillSendConfig.builder().doco("111122").email("<EMAIL>")
                        .phoneNumber(StringUtils.EMPTY).loginNo(StringUtils.EMPTY)
                        .tenantManagerId(StringUtils.EMPTY).emailUsername(StringUtils.EMPTY)
                        .mcu("mcu3,mcu5").unit("unit6").isDel(0)
                        .projectId("xx").buildingId("buildingId2_123")
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now()).build()

        );
        return requestConfigList;
    }

    protected List<BillSendConfigAn8Link> buildBillSendConfigAn8Links(List<BillSendConfig> configList) {
        List<BillSendConfigAn8Link> requestAn8List = Lists.list(
                BillSendConfigAn8Link.builder()
                        .configId(configList.get(0).getId()).alph("company 1").an8("12345678")
                        .isDel(0).createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now()).build(),

                BillSendConfigAn8Link.builder()
                        .configId(configList.get(1).getId()).alph("company 2").an8("11223344")
                        .isDel(0).createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now()).build(),
                BillSendConfigAn8Link.builder()
                        .configId(configList.get(1).getId()).alph("company 3").an8("11223355")
                        .isDel(0).createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now()).build(),

                BillSendConfigAn8Link.builder()
                        .configId(configList.get(2).getId()).alph("company 4").an8("87654321")
                        .isDel(1).createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now()).build(),

                BillSendConfigAn8Link.builder()
                        .configId(configList.get(3).getId()).alph("test alph 1").an8("66554433")
                        .isDel(0).createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now()).build(),
                BillSendConfigAn8Link.builder()
                        .configId(configList.get(3).getId()).alph("test alph 2").an8("87654321")
                        .isDel(1).createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now()).build()

        );
        return requestAn8List;
    }

}
