package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.config.EFapiaoConfig;
import com.kerryprops.kip.bill.dao.entity.EFapiaoJDEBill;
import com.kerryprops.kip.bill.service.impl.EFapiaoBillServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;

@Slf4j
class EFapiaoSyncBillControllerTest extends BaseIntegrationTest {

    @Autowired
    EFapiaoBillServiceImpl eFapiaoBillService;

    @Autowired
    private EFapiaoSyncBillController eFapiaoSyncBillController;

    /**
     * 同步接口：正常场景（公司code，经营单元，起始日期为空）
     */
    @Test
    void _01_sync_company_bus_periods_empty_success() {
        EFapiaoConfig.Item eFapiaoItem = new EFapiaoConfig.Item();
        eFapiaoItem.setCompanyCode("testCompanyCode");
        eFapiaoItem.setStartDate("testStartDate");
        eFapiaoItem.setBus("");

        boolean result = eFapiaoSyncBillController.sync(eFapiaoItem, false);

        assertTrue(result);
    }

    /**
     * 同步接口：请求开票报异常
     */
    @Test
    void _02_sync_invoice_upload_exception() {
        EFapiaoJDEBill eFapiaoJDEBill = EFapiaoJDEBill.builder().build();
        List<EFapiaoJDEBill> eFapiaoJDEBills = List.of(eFapiaoJDEBill, eFapiaoJDEBill, eFapiaoJDEBill);
        doReturn(eFapiaoJDEBills).when(eFapiaoJdeBillService)
                .queryByCompanyCodeAndMonthAndBu(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any());

        doThrow(new RuntimeException()).when(eFapiaoBillService).invoiceUpload(ArgumentMatchers.any());
        doReturn(null).when(messageClient).sendWithReplyAlicloud(ArgumentMatchers.any());

        boolean result = eFapiaoSyncBillController.sync(null, true);

        assertTrue(result);
    }

    /**
     * 同步接口：正常场景
     */
    // @Test
    void _03_sync_success() {
        EFapiaoJDEBill eFapiaoJDEBill = EFapiaoJDEBill.builder().build();
        List<EFapiaoJDEBill> eFapiaoJDEBills = List.of(eFapiaoJDEBill, eFapiaoJDEBill, eFapiaoJDEBill);
        doReturn(eFapiaoJDEBills).when(eFapiaoJdeBillService)
                .queryByCompanyCodeAndMonthAndBu(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any());
        doNothing().when(eFapiaoBillService).invoiceUpload(ArgumentMatchers.any());

        boolean result = eFapiaoSyncBillController.sync(null, true);

        assertTrue(result);
    }

}
