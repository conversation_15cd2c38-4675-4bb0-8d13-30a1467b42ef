package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.BillRepository;
import com.kerryprops.kip.bill.dao.entity.BillEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.mockito.ArgumentMatchers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doReturn;

class API_fuzzyQueryDocos_StaffBillControllerTest extends BaseClass_StaffBillControllerTest {

    protected final String STAFF_JSON_ABNORMAL1 = "{\"buildingIds\":\"\",\"nickName\":\"JAKCAdmin\"," +
            "\"staff\":true,\"userId\":345,\"uniqueUserId\":\"345\",\"fromType\":\"S\",\"client\":false," +
            "\"superAdmin\":false,\"tenant\":false}";

    protected final String STAFF_JSON_ABNORMAL2 = "{\"nickName\":\"JAKCAdmin\"," +
            "\"staff\":true,\"userId\":345,\"uniqueUserId\":\"345\",\"fromType\":\"S\",\"client\":false," +
            "\"superAdmin\":false,\"tenant\":false}";

    private final String PROJECT_ID = "P2";

    private List<BillEntity> dataSet;

    @Autowired
    private BillRepository billRepository;

    protected void verify(ResponseEntity<RespWrapVo> response, List<String> expectedDocoList) {
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(RespCodeEnum.SUCCESS.getCode(), response.getBody().getCode());
        assertEquals(RespCodeEnum.SUCCESS.getMessage(), response.getBody().getMessage());

        List<String> contentList = (List<String>) response.getBody().getData();
        Set<String> actualDocoList = new HashSet<>(contentList);
        assertTrue(expectedDocoList.stream().sorted().collect(Collectors.joining())
                .equals(actualDocoList.stream().sorted().collect(Collectors.joining())));
    }

    @Test
    void _01_fuzzyQueryDocos_empty_projectId() {
        ResponseEntity<RespWrapVo> responseEntity = call_fuzzyQueryDocos_api(SUPER_ADMIN_JSON, StringUtils.EMPTY, null);
        RespWrapVo respWrapVo = responseEntity.getBody();
        Assertions.assertEquals(respWrapVo.getCode(), RespCodeEnum.UNKNOWN_ERROR.getCode());
    }

    @Test
    void _02_fuzzyQueryDocos_blank_projectId() {
        ResponseEntity<RespWrapVo> responseEntity = call_fuzzyQueryDocos_api(SUPER_ADMIN_JSON, "  ", null);
        RespWrapVo respWrapVo = responseEntity.getBody();
        Assertions.assertEquals(respWrapVo.getCode(), RespCodeEnum.UNKNOWN_ERROR.getCode());
    }

    @Test
    void _03_fuzzyQueryDocos_user_not_login() {
        ResponseEntity<RespWrapVo> responseEntity = call_fuzzyQueryDocos_api(null, PROJECT_ID, null);
        RespWrapVo respWrapVo = responseEntity.getBody();
        Assertions.assertEquals(respWrapVo.getCode(), RespCodeEnum.UNKNOWN_ERROR.getCode());
        Assertions.assertEquals("user_not_login", respWrapVo.getMessage());
    }

    @Test
    void _04_01_fuzzyQueryDocos_superAdmin_hiveAsClient_return_null() throws URISyntaxException, IOException {
        doReturn(null).when(hiveAsClient).getCenterByIds(ArgumentMatchers.any());

        ResponseEntity<RespWrapVo> responseEntity = call_fuzzyQueryDocos_api(SUPER_ADMIN_JSON, PROJECT_ID, null);

        List<String> expectDocoList = Collections.emptyList();
        verify(responseEntity, expectDocoList);
    }

    @Test
    void _04_02_fuzzyQueryDocos_superAdmin_hiveAsClient_return_empty() throws URISyntaxException, IOException {
        doReturn(Collections.emptyList()).when(hiveAsClient).getCenterByIds(ArgumentMatchers.any());

        ResponseEntity<RespWrapVo> responseEntity = call_fuzzyQueryDocos_api(SUPER_ADMIN_JSON, PROJECT_ID, null);

        List<String> expectDocoList = Collections.emptyList();
        verify(responseEntity, expectDocoList);
    }

    @Test
    void _04_03_fuzzyQueryDocos_superAdmin_hiveAsClient_return_valid_data() throws URISyntaxException, IOException {
        mockJdeBusByProject(PROJECT_ID);
        ResponseEntity<RespWrapVo> responseEntity = call_fuzzyQueryDocos_api(SUPER_ADMIN_JSON, PROJECT_ID, null);

        List<String> expectDocoList = Lists.newArrayList("112208", "112209", "112210", "112211", "112212");
        verify(responseEntity, expectDocoList);
    }

    @Test
    void _05_fuzzyQueryDocos_staff_null_buildingId() throws URISyntaxException, IOException {
        mockProjectId(PROJECT_ID);
        ResponseEntity<RespWrapVo> responseEntity = call_fuzzyQueryDocos_api(STAFF_JSON_ABNORMAL1, PROJECT_ID, null);

        List<String> expectDocoList = Collections.emptyList();
        verify(responseEntity, expectDocoList);
    }

    @Test
    void _06_fuzzyQueryDocos_staff_empty_buildingId() throws URISyntaxException, IOException {
        mockProjectId(PROJECT_ID);
        ResponseEntity<RespWrapVo> responseEntity = call_fuzzyQueryDocos_api(STAFF_JSON_ABNORMAL2, PROJECT_ID, null);

        List<String> expectDocoList = Collections.emptyList();
        verify(responseEntity, expectDocoList);
    }

    @Test
    void _07_01_fuzzyQueryDocos_staff_hiveAsClient_return_null() throws URISyntaxException, IOException {
        mockProjectId(PROJECT_ID);
        doReturn(null).when(hiveAsClient).convertToJdeBus((String[]) ArgumentMatchers.any());

        ResponseEntity<RespWrapVo> responseEntity = call_fuzzyQueryDocos_api(STAFF_JSON, PROJECT_ID, null);
        RespWrapVo respWrapVo = responseEntity.getBody();

        assertTrue(CollectionUtils.isEmpty((List<String>) respWrapVo.getData()));
    }

    @Test
    void _07_02_fuzzyQueryDocos_staff_hiveAsClient_return_empty() throws URISyntaxException, IOException {
        mockProjectId(PROJECT_ID);
        doReturn(Collections.EMPTY_LIST).when(hiveAsClient).getBuildingByIds(ArgumentMatchers.any());

        ResponseEntity<RespWrapVo> responseEntity = call_fuzzyQueryDocos_api(STAFF_JSON, PROJECT_ID, null);
        RespWrapVo respWrapVo = responseEntity.getBody();

        Assertions.assertEquals(respWrapVo.getCode(), RespCodeEnum.SUCCESS.getCode());
        Assertions.assertEquals(respWrapVo.getMessage(), RespCodeEnum.SUCCESS.getMessage());

        Collection collection = (Collection) respWrapVo.getData();
        Assertions.assertEquals(collection.size(), 0);
    }

    @ParameterizedTest
    @NullAndEmptySource
    void _08_01_fuzzyQueryDocos_staff_building1(String query) throws URISyntaxException, IOException {
        mockProjectId(PROJECT_ID);
        mockBuildingBU("P1-B1");

        final String xUserJson = "{\"buildingIds\":\"P1-B1\",\"nickName\":\"JAKCAdmin\"," +
                "\"staff\":true,\"userId\":345,\"uniqueUserId\":\"345\",\"fromType\":\"S\",\"client\":false," +
                "\"superAdmin\":false,\"tenant\":false}";
        ResponseEntity<RespWrapVo> responseEntity = call_fuzzyQueryDocos_api(xUserJson, PROJECT_ID, query);

        List<String> expectDocoList = Collections.emptyList();
        verify(responseEntity, expectDocoList);
    }

    @Test
    void _08_02_fuzzyQueryDocos_staff_building2() throws URISyntaxException, IOException {
        mockJdeBusByProject(PROJECT_ID);
        mockJdeBusByBuildingBU("P2-B1");

        final String xUserJson = "{\"buildingIds\":\"P2-B1\",\"nickName\":\"JAKCAdmin\"," +
                "\"staff\":true,\"userId\":345,\"uniqueUserId\":\"345\",\"fromType\":\"S\",\"client\":false," +
                "\"superAdmin\":false,\"tenant\":false}";
        ResponseEntity<RespWrapVo> responseEntity = call_fuzzyQueryDocos_api(xUserJson, PROJECT_ID, null);

        List<String> expectDocoList = Lists.newArrayList("112208", "112209");
        verify(responseEntity, expectDocoList);
    }

    @Test
    void _08_03_fuzzyQueryDocos_staff_building3() throws URISyntaxException, IOException {
        mockJdeBusByProject(PROJECT_ID);
        mockJdeBusByBuildingBU("P1-B1;P2-B1");

        final String xUserJson = "{\"buildingIds\":\"P1-B1,P2-B1\",\"nickName\":\"JAKCAdmin\"," +
                "\"staff\":true,\"userId\":345,\"uniqueUserId\":\"345\",\"fromType\":\"S\",\"client\":false," +
                "\"superAdmin\":false,\"tenant\":false}";
        ResponseEntity<RespWrapVo> responseEntity = call_fuzzyQueryDocos_api(xUserJson, PROJECT_ID, null);

        List<String> expectDocoList = Lists.newArrayList("112208", "112209");
        verify(responseEntity, expectDocoList);
    }

    @ParameterizedTest
    @CsvSource({
            "1, 112208;112209",
            "11, 112208;112209",
            "112, 112208;112209",
            "1122, 112208;112209",
            "11220, 112208;112209",
            "112208, 112208",
            "1122088, "
    })
    void _09_01_fuzzyQueryDocos_staff_building2_filter_doco(String query, String expectDocos) throws URISyntaxException, IOException {
        mockJdeBusByProject(PROJECT_ID);
        mockJdeBusByBuildingBU("P2-B1");

        final String xUserJson = "{\"buildingIds\":\"P2-B1\",\"nickName\":\"JAKCAdmin\"," +
                "\"staff\":true,\"userId\":345,\"uniqueUserId\":\"345\",\"fromType\":\"S\",\"client\":false," +
                "\"superAdmin\":false,\"tenant\":false}";
        ResponseEntity<RespWrapVo> responseEntity = call_fuzzyQueryDocos_api(xUserJson, PROJECT_ID, query);

        List<String> expectDocoList = StringUtils.isEmpty(expectDocos) ?
                Collections.EMPTY_LIST : Stream.of(expectDocos.split(";")).collect(Collectors.toList());
        verify(responseEntity, expectDocoList);
    }

    @BeforeEach
    void prepareTestData() {
        List<Integer> deleteBillIndexList = Lists.list(1, 7, 13);
        List<BillEntity> billEntities = buildInitialDataset();
        for (int i = 1; i <= billEntities.size(); i++) {
            String doco = "1122" + String.format("%02d", i);
            String an8 = "112233" + String.format("%02d", i);
            String delFlag = deleteBillIndexList.contains(Integer.valueOf(i)) ? "1" : "0";

            BillEntity billEntity = billEntities.get(i - 1);
            billEntity.setTpDoco(doco);
            billEntity.setTpAn8(an8);
            billEntity.setDelFlag(delFlag);
        }

        dataSet = billRepository.saveAll(billEntities);
    }

    @AfterEach
    void clearTestData() {
        billRepository.deleteAll(dataSet);
    }

    private ResponseEntity<RespWrapVo> call_fuzzyQueryDocos_api(String xUserJson, String projectId, String query) {
        // headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        if (Objects.nonNull(xUserJson)) {
            headers.set("x-user", xUserJson);
        }

        HttpEntity request = new HttpEntity<>(null, headers);
        String url = "http://localhost:" + serverPort + String.format("/s/bill/%s/docos", projectId) +
                (StringUtils.isEmpty(query) ? StringUtils.EMPTY : "?query=" + query);

        return restTemplate.exchange(url, HttpMethod.GET, request, RespWrapVo.class);
    }

}
