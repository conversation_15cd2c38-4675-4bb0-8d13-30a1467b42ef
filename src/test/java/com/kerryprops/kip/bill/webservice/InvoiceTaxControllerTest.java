package com.kerryprops.kip.bill.webservice;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.webservice.vo.resp.TaxClassifyCodeResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;

import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;

@Slf4j
class InvoiceTaxControllerTest extends BaseIntegrationTest {

    @Autowired
    private InvoiceTaxController invoiceTaxController;

    @Test
    void _01_getClassifyCode_responseNull() {
        doReturn(null).when(kipInvoiceClient)
                .getClassifyCode(ArgumentMatchers.any(String.class), ArgumentMatchers.any(String.class));

        TaxClassifyCodeResponse coTaxClassifyCode = invoiceTaxController.getClassifyCode("31007", "PROPERTY_MANAGEMENT");

        Assertions.assertNull(coTaxClassifyCode);
    }

    @Test
    void _02_getClassifyCode_responseFail() {
        RespWrapVo<TaxClassifyCodeResponse> respWrapVo = new RespWrapVo<>();
        respWrapVo.setCode(RespCodeEnum.NOT_FOUND.getCode());

        doReturn(respWrapVo).when(kipInvoiceClient)
                .getClassifyCode(ArgumentMatchers.any(String.class), ArgumentMatchers.any(String.class));

        TaxClassifyCodeResponse coTaxClassifyCode = invoiceTaxController.getClassifyCode("31007", "PROPERTY_MANAGEMENT");

        Assertions.assertNull(coTaxClassifyCode);
    }

    @Test
    void _03_getClassifyCode_responseDataEmpty() {
        RespWrapVo<TaxClassifyCodeResponse> respWrapVo = new RespWrapVo<>();
        respWrapVo.setCode(RespCodeEnum.SUCCESS.getCode());
        respWrapVo.setData(null);

        doReturn(respWrapVo).when(kipInvoiceClient)
                .getClassifyCode(ArgumentMatchers.any(String.class), ArgumentMatchers.any(String.class));

        TaxClassifyCodeResponse coTaxClassifyCode = invoiceTaxController.getClassifyCode("31007", "PROPERTY_MANAGEMENT");

        Assertions.assertNull(coTaxClassifyCode);
    }

    @Test
    void _04_getClassifyCode_throwException() throws Exception {
        doThrow(new RuntimeException("runtimeException")).when(kipInvoiceClient)
                .getClassifyCode(ArgumentMatchers.any(String.class), ArgumentMatchers.any(String.class));
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("X-User", "{\"fromType\":\"S\",\"roles\":\"SUPER_ADMIN\"}");
        HttpEntity request = new HttpEntity<>(null, httpHeaders);
        String url = "http://localhost:" + serverPort + "/tax/co/1/fee_type/1/classifications";
        ResponseEntity<TaxClassifyCodeResponse> obj = restTemplate.exchange(url, HttpMethod.GET, request, TaxClassifyCodeResponse.class);
        Assertions.assertNull(obj.getBody());
    }

    @Test
    void _05_getClassifyCode_success() {
        TaxClassifyCodeResponse taxClassifyCodeResponse = new TaxClassifyCodeResponse();
        taxClassifyCodeResponse.setCompanyCode(random.nextLong() + "");
        taxClassifyCodeResponse.setFeeType(random.nextLong() + "");
        taxClassifyCodeResponse.setClassifyCode(random.nextLong() + "");

        RespWrapVo<TaxClassifyCodeResponse> respWrapVo = new RespWrapVo<>();
        respWrapVo.setCode(RespCodeEnum.SUCCESS.getCode());
        respWrapVo.setData(taxClassifyCodeResponse);

        doReturn(respWrapVo).when(kipInvoiceClient)
                .getClassifyCode(ArgumentMatchers.any(String.class), ArgumentMatchers.any(String.class));

        TaxClassifyCodeResponse coTaxClassifyCode = invoiceTaxController.getClassifyCode(taxClassifyCodeResponse.getCompanyCode()
                , taxClassifyCodeResponse.getFeeType());

        Assertions.assertEquals(coTaxClassifyCode.getClassifyCode(), taxClassifyCodeResponse.getClassifyCode());
        Assertions.assertEquals(coTaxClassifyCode.getFeeType(), taxClassifyCodeResponse.getFeeType());
        Assertions.assertEquals(coTaxClassifyCode.getCompanyCode(), taxClassifyCodeResponse.getCompanyCode());
    }

}
