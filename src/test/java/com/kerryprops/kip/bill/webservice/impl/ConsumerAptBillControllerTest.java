package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.service.AptBillAgreementService;
import com.kerryprops.kip.bill.service.PaymentBillService;
import com.kerryprops.kip.bill.webservice.vo.resp.AptPaymentInfoVo;
import com.kerryprops.kip.bill.webservice.vo.resp.RoomAndUserSignResource;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.kerryprops.kip.bill.common.enums.BillPaymentStatus.CANCEL;
import static com.kerryprops.kip.bill.common.enums.BillPaymentStatus.TO_BE_PAID;
import static com.kerryprops.kip.bill.common.enums.PayCancelTypeEnum.TIMEOUT_CANCELLED;
import static com.kerryprops.kip.bill.common.enums.PaymentPayType.UNKNOWN;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * ConsumerAptBillControllerTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-5-9
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("C端支付账单-单元测试")
class ConsumerAptBillControllerTest {

    @Mock
    private AptBillAgreementService billAgreementService;

    @Mock
    private PaymentBillService paymentBillService;

    @InjectMocks
    private ConsumerAptBillController consumerAptBillController;

    @Test
    @DisplayName("userAndSignInfo - 正常场景：返回房间和用户签约信息")
    void userAndSignInfo_success() {
        // Arrange
        String roomId = "ROOM123";
        var mockResource = new RoomAndUserSignResource();

        when(billAgreementService.getUserAndRoomSignInfo(anyString())).thenReturn(Optional.of(mockResource));

        // Act
        RoomAndUserSignResource result = consumerAptBillController.userAndSignInfo(roomId);

        // Assert
        assertNotNull(result);
    }

    @Test
    @DisplayName("queryPaymentPage - 正常场景：返回支付信息分页")
    void queryPaymentPage_success() {
        // Arrange
        String buildingId = "BUILDING123";
        String roomId = "ROOM123";
        var aptPaymentInfo = new AptPaymentInfo();
        aptPaymentInfo.setId("PAYMENT123");
        List<AptPaymentInfo> mockPaymentInfos = List.of(aptPaymentInfo);

        when(paymentBillService.queryMyPaymentInfoList(any(), any(), any())).thenReturn(mockPaymentInfos);

        var aptPaymentInfoVo = new AptPaymentInfoVo();
        aptPaymentInfoVo.setId("PAYMENT123");
        aptPaymentInfoVo.setPayType(UNKNOWN);

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = mockStatic(BeanUtil.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any())).thenReturn(aptPaymentInfoVo);

            // Act
            RespWrapVo<List<AptPaymentInfoVo>> result = consumerAptBillController.queryPaymentPage(buildingId, roomId);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getData().size());
            assertEquals(aptPaymentInfoVo.getId(), result.getData().get(0).getId());
        }
    }

    @Test
    @DisplayName("queryPaymentInfo - 正常场景：返回支付信息")
    void queryPaymentInfo_success() {
        // Arrange
        String orderNo = "ORDER123";
        AptPaymentInfoVo mockPaymentInfoVo = new AptPaymentInfoVo();
        mockPaymentInfoVo.setId(orderNo);
        mockPaymentInfoVo.setPaymentStatus(TO_BE_PAID);
        mockPaymentInfoVo.setAptBillList(Collections.emptyList());
        mockPaymentInfoVo.setCreateTime(new Timestamp(System.currentTimeMillis()));

        when(paymentBillService.queryPaymentInfoById(anyString())).thenReturn(mockPaymentInfoVo);

        // Act
        RespWrapVo<AptPaymentInfoVo> result = consumerAptBillController.queryPaymentInfo(orderNo);

        // Assert
        assertNotNull(result);
        assertEquals(orderNo, result.getData().getId());
        verify(paymentBillService, times(1)).queryPaymentInfoById(orderNo);
    }

    @Test
    @DisplayName("queryPaymentInfo - 超时场景：支付状态变更为取消")
    void queryPaymentInfo_timeout() {
        // Arrange
        String orderNo = "ORDER123";
        AptPaymentInfoVo mockPaymentInfoVo = new AptPaymentInfoVo();
        mockPaymentInfoVo.setId(orderNo);
        mockPaymentInfoVo.setPaymentStatus(TO_BE_PAID);
        mockPaymentInfoVo.setAptBillList(Collections.emptyList());
        mockPaymentInfoVo.setCreateTime(new Timestamp(System.currentTimeMillis() - Duration.ofMinutes(20).toMillis()));

        when(paymentBillService.queryPaymentInfoById(anyString())).thenReturn(mockPaymentInfoVo);

        // Act
        RespWrapVo<AptPaymentInfoVo> result = consumerAptBillController.queryPaymentInfo(orderNo);

        // Assert
        assertNotNull(result);
        assertEquals(CANCEL, result.getData().getPaymentStatus());
        assertEquals(TIMEOUT_CANCELLED.name(), result.getData().getCancelType());
        verify(paymentBillService, times(1)).queryPaymentInfoById(orderNo);
    }
}