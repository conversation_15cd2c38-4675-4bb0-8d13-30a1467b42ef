package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.exceptions.ExceptionResource;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptJdeBillRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptJdeBill;
import com.kerryprops.kip.bill.service.StaffManualSyncAptBillService;
import com.kerryprops.kip.bill.webservice.vo.resp.SyncStaffAptBillRespVo;
import com.querydsl.core.types.Predicate;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doReturn;

class StaffManualSyncAptBillControllerTest extends BaseIntegrationTest {

    @Autowired
    StaffManualSyncAptBillService manualSyncService;

    @Autowired
    AptBillRepository billRepository;

    @Autowired
    AptJdeBillRepository aptJdeBillRepository;

    @Test
    void _01_bill_not_found_exception() {
        ResponseEntity<ExceptionResource> resp = call_manual_sync_api_error(String.valueOf(random.nextInt()));
        assertEquals(HttpStatus.BAD_REQUEST, resp.getStatusCode());
        assertEquals(RespCodeEnum.NOT_FOUND.getMessage(), resp.getBody().getErrorText());
        assertEquals(RespCodeEnum.NOT_FOUND.getCode(), resp.getBody().getErrorCode());
    }

    @Test
    void _02_bill_paying_exception() {
        AptBill aptBill = randomAptBill();
        aptBill.setPaymentStatus(BillPaymentStatus.PAYING);
        billRepository.save(aptBill);
        String billNo = aptBill.getBillNo();
        ResponseEntity<ExceptionResource> resp = call_manual_sync_api_error(billNo);
        assertEquals(HttpStatus.BAD_REQUEST, resp.getStatusCode());
        assertEquals(RespCodeEnum.SYNC_PAYING_BILL_ERROR.getMessage(), resp.getBody().getErrorText());
        assertEquals(RespCodeEnum.SYNC_PAYING_BILL_ERROR.getCode(), resp.getBody().getErrorCode());
    }

    @Test
    void _03_not_allow_sync_paid_and_not_jde_verified_bill_exception() {
        AptBill aptBill = randomAptBill();
        aptBill.setStatus(BillStatus.ONLINE_PAID);
        aptBill.setPaymentStatus(BillPaymentStatus.PAID);
        aptBill.setPaymentResult("线上支付");
        billRepository.save(aptBill);
        String billNo = aptBill.getBillNo();
        ResponseEntity<ExceptionResource> resp = call_manual_sync_api_error(billNo);
        assertEquals(HttpStatus.BAD_REQUEST, resp.getStatusCode());
        assertEquals(RespCodeEnum.SYNC_PAID_AND_NOT_JDE_VERIFIED_BILL_ERROR.getMessage(), resp.getBody().getErrorText());
        assertEquals(RespCodeEnum.SYNC_PAID_AND_NOT_JDE_VERIFIED_BILL_ERROR.getCode(), resp.getBody().getErrorCode());
    }

    @Test
    void _04_apt_jde_bill_not_found_return_no_change() {
        AptBill aptBill = randomAptBill();
        billRepository.save(aptBill);
        String billNo = aptBill.getBillNo();
        ResponseEntity<SyncStaffAptBillRespVo> resp = call_manual_sync_api(billNo);
        SyncStaffAptBillRespVo vo = resp.getBody();
        assertEquals(HttpStatus.OK, resp.getStatusCode());
        assertTrue(CollectionUtils.isEmpty(vo.getChangedFields()));
    }

    @Test
    void _05_amount_changed() {
        AptBill aptBill = randomAptBill();
        billRepository.save(aptBill);
        String billNo = aptBill.getBillNo();

        doReturn(List.of(new AptJdeBill())).when(aptJdeBillRepository).findAll((Predicate) ArgumentMatchers.any());

        aptBill.setAmt(aptBill.getAmt() + random.nextDouble());
        doReturn(aptBill).when(manualSyncService).manualSync(ArgumentMatchers.any(), ArgumentMatchers.anyList());
        ResponseEntity<SyncStaffAptBillRespVo> resp = call_manual_sync_api(billNo);
        SyncStaffAptBillRespVo vo = resp.getBody();
        assertEquals(HttpStatus.OK, resp.getStatusCode());
        assertEquals(1, vo.getChangedFields().size());
    }

    private ResponseEntity<SyncStaffAptBillRespVo> call_manual_sync_api(String billNo) {
        // headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        headers.set("x-user", "{ \"fromType\": \"S\", \"loginAccount\": \"<EMAIL>\", \"userId\": \"********\", \"nickName\": \"siyuan.liu\", \"roles\": \"SUPER_ADMIN\" }");
        HttpEntity request = new HttpEntity<>(null, headers);
        String url = "http://localhost:" + serverPort + "/s/apt/pay/bill/" + billNo + "/sync";
        return restTemplate.exchange(url, HttpMethod.POST, request, SyncStaffAptBillRespVo.class);
    }

    private ResponseEntity<ExceptionResource> call_manual_sync_api_error(String billNo) {
        String url = "http://localhost:" + serverPort + "/s/apt/pay/bill/" + billNo + "/sync";
        return restTemplate.exchange(url, HttpMethod.POST, null, ExceptionResource.class);
    }


}