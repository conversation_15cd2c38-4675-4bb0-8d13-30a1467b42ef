package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.entity.BillSendConfig;
import com.kerryprops.kip.bill.dao.entity.BillSendConfigAn8Link;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.util.Comparator;
import java.util.List;

class API_delete_StaffBillSendConfigControllerTest extends BaseClass_StaffBillSendConfigControllerTest {

    private List<BillSendConfig> configList;

    private List<BillSendConfigAn8Link> an8LinkList;

    @Test
    void _02_delete_01_nonExistenceId() {
        BillSendConfig billSendConfig = configList.stream().max(Comparator.comparing(BillSendConfig::getId)).get();
        long maxId = billSendConfig.getId() + 1;

        ResponseEntity<RespWrapVo> responseEntity = call_delete_api(List.of(maxId));
        RespWrapVo<Boolean> respWrapVo = (RespWrapVo<Boolean>) responseEntity.getBody();
        Assertions.assertEquals(respWrapVo.getCode(), RespCodeEnum.SUCCESS.getCode());
        Assertions.assertEquals(respWrapVo.getMessage(), RespCodeEnum.SUCCESS.getMessage());
        Assertions.assertEquals(respWrapVo.getData(), Boolean.FALSE);
    }

    @Test
    void _02_delete_02_invalidId() {
        ResponseEntity<RespWrapVo> responseEntity = call_delete_api(List.of(Long.valueOf(-1)));
        RespWrapVo<Boolean> respWrapVo = (RespWrapVo<Boolean>) responseEntity.getBody();
        Assertions.assertEquals(respWrapVo.getCode(), RespCodeEnum.SUCCESS.getCode());
        Assertions.assertEquals(respWrapVo.getMessage(), RespCodeEnum.SUCCESS.getMessage());
        Assertions.assertEquals(respWrapVo.getData(), Boolean.FALSE);
    }

    @Test
    void _02_delete_03_1doco_1an8() {
        Long configId1 = configList.get(0).getId();
        Long configId2 = configList.get(3).getId();
        deleteValidData(List.of(configId1, configId2));
    }

    @Test
    void _02_delete_04_1doco_2an8() {
        Long configId = configList.get(1).getId();
        deleteValidData(List.of(configId));
    }

    @Test
    void _02_delete_05_deleted_config() {
        Long configId = configList.get(2).getId();
        deleteValidData(List.of(configId));
    }

    @BeforeEach
    void prepareTestData() {
        List<BillSendConfig> requestConfigList = buildBillSendConfigList();
        configList = configRepository.saveAll(requestConfigList);

        List<BillSendConfigAn8Link> requestAn8List = buildBillSendConfigAn8Links(configList);
        an8LinkList = an8LinkRepository.saveAll(requestAn8List);
    }

    @AfterEach
    void clearTestData() {
        configRepository.deleteAll(configList);
        an8LinkRepository.deleteAll(an8LinkList);
    }

    private void deleteValidData(List<Long> configIds) {
        ResponseEntity<RespWrapVo> responseEntity = call_delete_api(configIds);

        RespWrapVo<Boolean> respWrapVo = (RespWrapVo<Boolean>) responseEntity.getBody();
        Assertions.assertEquals(respWrapVo.getCode(), RespCodeEnum.SUCCESS.getCode());
        Assertions.assertEquals(respWrapVo.getMessage(), RespCodeEnum.SUCCESS.getMessage());
        Assertions.assertEquals(respWrapVo.getData(), Boolean.TRUE);

        for (int i = 0; i < configIds.size(); i++) {
            Long configId = configIds.get(i);
            BillSendConfig billSendConfig = configRepository.findById(configId).get();
            Assertions.assertEquals(billSendConfig.getIsDel(), 1);

            List<BillSendConfigAn8Link> an8Links = an8LinkRepository.selectAn8Links(configId);
            an8Links.stream().forEach(billSendConfigAn8Link -> {
                Assertions.assertEquals(billSendConfigAn8Link.getIsDel(), 1);
            });
        }
    }

    private ResponseEntity<RespWrapVo> call_delete_api(List<Long> configIds) {
        // build http-headers
        final String X_USER = "{\"buildingIds\":\"\",\"nickName\":\"JAKCAdmin\"," +
                "\"staff\":true,\"userId\":345,\"uniqueUserId\":\"345\",\"fromType\":\"S\",\"client\":false," +
                "\"superAdmin\":false,\"tenant\":false}";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("x-user", X_USER);

        //request
        HttpEntity request = new HttpEntity<>(null, headers);
        String url = "http://localhost:" + serverPort + "/s/bill/sendconfig";

        if (CollectionUtils.isNotEmpty(configIds)) {
            url = url + "?ids=" + configIds.get(0);
            for (int i = 1; i < configIds.size(); i++) {
                url = url + "&ids=" + configIds.get(i);
            }
        }

        return restTemplate.exchange(url, HttpMethod.DELETE, request, RespWrapVo.class);
    }

}
