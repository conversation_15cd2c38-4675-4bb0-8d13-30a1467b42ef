package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.exceptions.AppException;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptJdeBillRepository;
import com.kerryprops.kip.bill.dao.AptPayBillRepository;
import com.kerryprops.kip.bill.dao.AptPayRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptJdeBill;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPayBill;
import com.kerryprops.kip.bill.dao.entity.AptSyncJdeJobLog;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.impl.AptBillAsyncPushServiceImpl;
import com.kerryprops.kip.bill.service.model.s.AptBillManageSearchReqBo;
import com.kerryprops.kip.bill.webservice.vo.req.AptBillExportReqVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptBillManageSearchReqVo;
import com.kerryprops.kip.bill.webservice.vo.resp.AptBillExportRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffAptBillManageRespVo;
import com.querydsl.core.types.Predicate;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

/**
 * StaffAptBillControllerTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-04-24
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("C端账单-单元测试")
class StaffAptBillControllerTest {

    @Mock
    private AptBillRepository billRepository;

    @Mock
    private RedisTemplate<String, String> redisTemplate;

    @Mock
    private AptJdeBillRepository aptJdeBillRepository;

    @Mock
    private AptPayRepository payRepository;

    @Mock
    private AptPayBillRepository payBillRepository;

    @Mock
    private AptBillAsyncPushServiceImpl aptBillAsyncPushService;

    @InjectMocks
    private StaffAptBillController staffAptBillController;


    @Test
    @DisplayName("查询支付状态种类，正常场景")
    void testQueryPaymentStatus_ShouldReturnFullMapping() {
        // 执行方法
        RespWrapVo<Map<String, String>> result = staffAptBillController.queryPaymentStatus();

        // 验证返回结构
        Map<String, String> statusMap = result.getData();
        assertEquals(3, statusMap.size());
        // 验证具体映射值
        assertEquals(BillPaymentStatus.PAYMENT_STATUS_PAID, statusMap.get(BillPaymentStatus.PAID.name()));
        assertEquals(BillPaymentStatus.PAYMENT_STATUS_TO_BE_PAID, statusMap.get(BillPaymentStatus.TO_BE_PAID.name()));
        assertEquals(BillPaymentStatus.PAYMENT_STATUS_PAYING, statusMap.get(BillPaymentStatus.PAYING.name()));
        // 验证响应包装成功状态
        assertEquals(RespCodeEnum.SUCCESS.getCode(), result.getCode());
    }

    @Test
    @DisplayName("测试queryAlphs方法，正常场景")
    void testQueryAlphs_WhenMaxBindingScopeExists() {
        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            // Arrange
            List<String> mockScopes = List.of("scope1", "scope2");
            mockedUserInfo.when(UserInfoUtils::maxBindingScope)
                          .thenReturn(mockScopes);

            // 模拟数据库返回
            List<String> mockResult = List.of("A101", "A102");
            when(billRepository.queryAlphs(true, mockScopes, "PROJ_001", "ROOM_101")).thenReturn(mockResult);

            // Act
            RespWrapVo<List<String>> response = staffAptBillController.queryAlphs("PROJ_001", "ROOM_101");

            // Assert
            assertEquals(RespCodeEnum.SUCCESS.getCode(), response.getCode());
        }
    }

    @Test
    @DisplayName("测试queryCategorys方法，正常场景")
    void testQueryCategorys_WhenMaxBindingScopeExists() {
        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            // Arrange
            List<String> mockScopes = List.of("scope1", "scope2");
            mockedUserInfo.when(UserInfoUtils::maxBindingScope)
                          .thenReturn(mockScopes);

            // Act
            RespWrapVo<List<String>> response = staffAptBillController.queryCategorys("PROJ_001", "ROOM_101");

            // Assert
            assertEquals(RespCodeEnum.SUCCESS.getCode(), response.getCode());
        }
    }

    @Test
    @DisplayName("查询C端账单，正常场景，返回为空")
    void testList_EmptyResult_ShouldReturnEmptyResp() {
        try (MockedStatic<BeanUtil> mockedBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            // 模拟BeanUtil转换请求参数
            var mockBo = new AptBillManageSearchReqBo();
            mockedBeanUtil.when(() -> BeanUtil.copy(any(AptBillManageSearchReqVo.class), any()))
                          .thenReturn(mockBo);
            mockedUserInfo.when(UserInfoUtils::getUser)
                          .thenReturn(generateLoginUser());

            // 构造空分页结果
            Page<AptBill> emptyPage = Page.empty();
            when(billRepository.findAll(any(Predicate.class), any(Pageable.class))).thenReturn(emptyPage);

            // 执行查询
            RespWrapVo<Page<StaffAptBillManageRespVo>> result =
                    staffAptBillController.list(Pageable.unpaged(), new AptBillManageSearchReqVo());

            // 验证空结果处理
            assertNull(result.getData());
            // 验证仓库方法调用
            verify(billRepository).findAll(any(Predicate.class), eq(Pageable.unpaged()));
        }
    }

    @Test
    @DisplayName("查询C端账单，正常场景")
    void testList_NonEmptyResult_ShouldConvertEntities() {
        try (MockedStatic<BeanUtil> mockedBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            // 模拟两次BeanUtil转换
            AptBillManageSearchReqBo mockBo = new AptBillManageSearchReqBo();
            StaffAptBillManageRespVo mockVo = new StaffAptBillManageRespVo();

            mockedBeanUtil.when(
                                  () -> BeanUtil.copy(any(AptBillManageSearchReqVo.class),
                                                      eq(AptBillManageSearchReqBo.class)))
                          .thenReturn(mockBo);
            mockedBeanUtil.when(() -> BeanUtil.copy(any(AptBill.class), eq(StaffAptBillManageRespVo.class)))
                          .thenReturn(mockVo);
            mockedUserInfo.when(UserInfoUtils::getUser)
                          .thenReturn(generateLoginUser());

            // 构造分页数据
            Page<AptBill> mockPage = new PageImpl<>(List.of(new AptBill(), new AptBill()));
            when(billRepository.findAll(any(Predicate.class), any(Pageable.class))).thenReturn(mockPage);

            // 执行查询
            RespWrapVo<Page<StaffAptBillManageRespVo>> result =
                    staffAptBillController.list(Pageable.ofSize(10), new AptBillManageSearchReqVo());

            // 验证结果转换
            assertEquals(2, result.getData()
                                  .getContent()
                                  .size());
            // 验证转换方法调用次数
            mockedBeanUtil.verify(() -> BeanUtil.copy(any(AptBill.class), eq(StaffAptBillManageRespVo.class)),
                                  times(2));
        }
    }

    @Test
    @DisplayName("全量推送，异常场景，账单列表为空")
    void testPushAll_EmptyBillList_ShouldReturnFalse() {
        // 构造空查询结果
        try (MockedStatic<UserInfoUtils> userInfoMock = Mockito.mockStatic(UserInfoUtils.class)) {
            userInfoMock.when(UserInfoUtils::getUser)
                        .thenReturn(generateLoginUser());
            when(billRepository.findAll(any(Predicate.class))).thenReturn(Collections.emptyList());

            RespWrapVo<Boolean> result = staffAptBillController.pushAll("PROJ_001");

            assertFalse(result.getData());
            verify(billRepository).findAll(any(Predicate.class));
        }
    }

    @Test
    @DisplayName("全量推送，异常场景，redis被占用")
    void testPushAll_WithExistingRedisKey_ShouldThrowException() {
        try (MockedStatic<UserInfoUtils> userInfoMock = Mockito.mockStatic(UserInfoUtils.class)) {
            userInfoMock.when(UserInfoUtils::getUser)
                        .thenReturn(generateLoginUser());

            // 构造带projectId的账单数据
            AptBill bill = new AptBill();
            bill.setProjectId("PROJ_001");
            when(billRepository.findAll(any(Predicate.class))).thenReturn(List.of(bill));

            // 模拟Redis存在键
            var valueOperations = Mockito.mock(ValueOperations.class);
            when(redisTemplate.opsForValue()).thenReturn(valueOperations);
            when(valueOperations.get(any())).thenReturn(new Object());

            // Act and Assert
            assertThrows(AppException.class, () -> staffAptBillController.pushAll("PROJ_001"));
        }
    }

    @Test
    @DisplayName("全量推送，正常场景")
    void testPushAll_SuccessScenario_ShouldReturnTrue() {
        // Arrange
        try (MockedStatic<UserInfoUtils> userInfoMock = Mockito.mockStatic(UserInfoUtils.class)) {
            // 设置用户信息mock
            userInfoMock.when(UserInfoUtils::getUser)
                        .thenReturn(generateLoginUser());

            // 构造有效账单数据
            AptBill bill = new AptBill();
            bill.setProjectId("PROJ_001");
            when(billRepository.findAll(any(Predicate.class))).thenReturn(List.of(bill));

            // 模拟Redis未占用
            var valueOperations = Mockito.mock(ValueOperations.class);
            when(redisTemplate.opsForValue()).thenReturn(valueOperations);
            when(valueOperations.get(any())).thenReturn(null);

            // Act
            RespWrapVo<Boolean> result = staffAptBillController.pushAll("PROJ_001");

            // Assert
            assertTrue(result.getData());
            verify(aptBillAsyncPushService).asyncPushAptBill(eq(List.of(bill)), any(), any(), any());
        }
    }

    @Test
    @DisplayName("按条件推送，异常场景，账单列表为空")
    void pushConditional_EmptyBillList_ShouldReturnFalse() {
        // Arrange
        try (MockedStatic<UserInfoUtils> userInfoMock = Mockito.mockStatic(UserInfoUtils.class);
             MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);) {
            userInfoMock.when(UserInfoUtils::getUser)
                        .thenReturn(generateLoginUser());
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new AptBillManageSearchReqBo());

            var aptBill = new AptBill();
            aptBill.setPaymentStatus(BillPaymentStatus.PAID);
            List<AptBill> aptBills = new ArrayList<>();
            aptBills.add(aptBill);
            when(billRepository.findAll(any(Predicate.class))).thenReturn(aptBills);

            // Act
            RespWrapVo<Boolean> result = staffAptBillController.pushConditional(new AptBillManageSearchReqVo());

            // assert
            assertFalse(result.getData());
            verify(billRepository).findAll(any(Predicate.class));
        }
    }

    @Test
    @DisplayName("选择推送，异常场景，账单列表为空")
    void pushSelected_EmptyBillIdsList_ShouldReturnFalse() {
        // Act
        RespWrapVo<Boolean> result = staffAptBillController.pushSelected(Collections.emptyList());

        // assert
        assertNotNull(result);
        verify(billRepository, never()).findAll(any(Predicate.class));
    }

    @Test
    @DisplayName("选择推送，异常场景，账单列表为空")
    void pushSelected_EmptyBillList_ShouldReturnFalse() {
        // Arrange
        try (MockedStatic<UserInfoUtils> userInfoMock = Mockito.mockStatic(UserInfoUtils.class);
             MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);) {
            userInfoMock.when(UserInfoUtils::getUser)
                        .thenReturn(generateLoginUser());
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new AptBillManageSearchReqBo());

            when(billRepository.findAll(any(Predicate.class))).thenReturn(Collections.emptyList());

            // Act
            RespWrapVo<Boolean> result = staffAptBillController.pushSelected(List.of(1L, 2L));

            // assert
            assertFalse(result.getData());
            verify(billRepository).findAll(any(Predicate.class));
        }
    }

    @Test
    @DisplayName("导出C端账单，正常场景，数据为空")
    void testExport_EmptyBillList_ShouldSkipProcessing() {
        // Arrange
        try (MockedStatic<UserInfoUtils> userInfoMock = Mockito.mockStatic(UserInfoUtils.class)) {
            userInfoMock.when(UserInfoUtils::getUser)
                        .thenReturn(generateLoginUser());

            var reqVo = new AptBillExportReqVo();
            when(billRepository.findAll(any(Predicate.class))).thenReturn(Collections.emptyList());

            // Act
            staffAptBillController.export(reqVo, null);

            // Assert
            verifyNoInteractions(aptJdeBillRepository, payBillRepository, payRepository);
        }

    }

    @Test
    @DisplayName("导出C端账单，正常场景，数据不为空")
    void testExport_NormalBills_ShouldGenerateExportData(){
        try (MockedStatic<UserInfoUtils> userInfoMock = Mockito.mockStatic(UserInfoUtils.class);
             MockedStatic<BeanUtil> beanUtilMock = Mockito.mockStatic(BeanUtil.class)) {
            // Arrange
            userInfoMock.when(UserInfoUtils::getUser)
                        .thenReturn(generateLoginUser());

            AptBill bill = buildTestAptBill(1L, 2023, 10, BillStatus.JDE_VERIFIED);
            bill.setPaymentStatus(BillPaymentStatus.PAID);
            List<AptBill> bills = List.of(bill);

            // 设置BeanUtil转换行为
            AptBillExportRespVo aptBillExportRespVo = new AptBillExportRespVo();
            aptBillExportRespVo.setId(1L);
            aptBillExportRespVo.setBillNo(bill.getBillNo());
            beanUtilMock.when(() -> BeanUtil.copy(any(AptBill.class), eq(AptBillExportRespVo.class)))
                        .thenReturn(aptBillExportRespVo);

            AptJdeBill jdeBill = new AptJdeBill();
            jdeBill.setBillNumber(bill.getBillNo());
            jdeBill.setJdeVerificationTime(new Date());

            when(billRepository.findAll(any(Predicate.class))).thenReturn(bills);
            when(aptJdeBillRepository.findAll(any(Predicate.class))).thenReturn(List.of(jdeBill));

            var mockAptPay = new AptPay();
            mockAptPay.setId(123L);
            mockAptPay.setPayDate(new Date());
            when(payRepository.findAll(any(Predicate.class))).thenReturn(List.of(mockAptPay));

            var aptPayBill = new AptPayBill();
            aptPayBill.setBillId(aptBillExportRespVo.getId());
            aptPayBill.setPayId(mockAptPay.getId());
            when(payBillRepository.findAll(any(Predicate.class))).thenReturn(List.of(aptPayBill));

            // Act and Assert
            assertThrows(NullPointerException.class, () -> staffAptBillController.export(new AptBillExportReqVo(),
                                                                                     null));
        }
    }

    private AptBill buildTestAptBill(Long id, int year, int month, BillStatus status) {
        AptBill bill = new AptBill();
        bill.setId(id);
        bill.setYear(year);
        bill.setMonth(month);
        bill.setStatus(status);
        bill.setBillNo("BILL_" + id);
        return bill;
    }

    private LoginUser generateLoginUser() {
        return LoginUser.builder()
                        .nickName("testUser")
                        .fromType("C")
                        .cid("testCid")
                        .build();
    }

}

