package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.enums.PaymentCateEnum;
import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.AptJdeBillRepository;
import com.kerryprops.kip.bill.dao.entity.AptJdeBill;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
public class ExternalApiRestTest extends BaseIntegrationTest {

    @Autowired
    private AptJdeBillRepository aptJdeBillRepository;

    @Test
    public void _01_no_data_() {
        aptJdeBillRepository.deleteAll();
        ResponseEntity<RespWrapVo> response = call_query_api();
        assertEquals(2, ((List<String>) response.getBody().getData()).size());
    }

    @Test
    public void _02_combination_() {
        //mock data
        ArrayList dataList = new ArrayList();
        dataList.add(buildAptJdeBill("B1001", "M01M", "测试"));
        dataList.add(buildAptJdeBill("B1002", "U01M", "水费"));
        dataList.add(buildAptJdeBill("B1003", null, "测试"));
        dataList.add(buildAptJdeBill("B1004", "M01M", "管理费"));
        dataList.add(buildAptJdeBill("B1005", StringUtils.EMPTY, StringUtils.EMPTY));
        aptJdeBillRepository.saveAll(dataList);

        //execute
        ResponseEntity<RespWrapVo> response = call_query_api();

        //verify
        List<String> expectedList = Arrays.asList(PaymentCateEnum.A003.name(), PaymentCateEnum.A004.name(), "M01M", "U01M");
        verify(response, expectedList);
    }

    protected void verify(ResponseEntity<RespWrapVo> response, List<String> expectedList) {
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(RespCodeEnum.SUCCESS.getCode(), response.getBody().getCode());
        assertEquals(RespCodeEnum.SUCCESS.getMessage(), response.getBody().getMessage());

        List<String> actualList = (List<String>) response.getBody().getData();
        assertTrue(expectedList.stream().sorted().collect(Collectors.joining())
                .equals(actualList.stream().sorted().collect(Collectors.joining())));
    }

    private ResponseEntity<RespWrapVo> call_query_api() {
        HttpEntity request = new HttpEntity<>(null, null);
        String url = "http://localhost:" + serverPort + "/s/external/queryGlcGroup";

        return restTemplate.exchange(url, HttpMethod.GET, request, RespWrapVo.class);
    }

    private AptJdeBill buildAptJdeBill(String billNumber, String ticketCode, String feeDescription) {
        return AptJdeBill.builder().rdAg(Math.abs(random.nextDouble()))
                .rdAlph("财务名字")
                .rdAn8("12345678")
                .rdDoco("112233")
                .billNumber(billNumber)
                .rdEdbt("EDI批号")
                .rdDoc((long) random.nextInt(28888888))
                .rdDct("RD")
                .rdKco((random.nextInt() + "companyCode").substring(0, 5))
                .rdSfx("001")
                .rdGlc(ticketCode)
                .rdDl01(feeDescription)
                .rdDl02("4")
                .rdDate01("122182")
                .rdDate02("122212")
                .rdMcu((Math.abs(random.nextInt()) + "_JDEBU").substring(0, 5))
                .rdUnit((Math.abs(random.nextInt()) + "_Unit").substring(0, 8))
                .rdUds1("20220701")
                .rdUds2("20220731")
                .rdUds3("2022-02-14 18:16:14")
                .jdeVerification(0)
                .rdUpmj((long) random.nextInt())
                .rdUpmt(String.valueOf(random.nextInt()))
                .build();
    }

}
