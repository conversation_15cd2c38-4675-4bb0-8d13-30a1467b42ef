package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.exceptions.AppException;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.utils.jde.OracleSql;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.AptPayConfigRepository;
import com.kerryprops.kip.bill.dao.entity.AptPayConfig;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.webservice.vo.req.AptOtherPayConfigSaveVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptOtherPayConfigUpdateVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptPayConfigSaveVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptPayConfigUpdateVo;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;

import static com.kerryprops.kip.bill.utils.RandomUtil.randomLoginUser;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * StaffAptPayConfigControllerTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-04-16
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("C端支付配置Controller-单元测试")
class StaffAptPayConfigControllerTest {

    @Mock
    private HiveAsClient hiveAsClient;

    @Mock
    private AptPayConfigRepository aptPayConfigRepository;

    @InjectMocks
    private StaffAptPayConfigController staffAptPayConfigController;

    @Test
    @DisplayName("save - 异常场景：付费方式已存在")
    void save_paymentTypeExists() {
        // Arrange
        AptPayConfigSaveVo saveReqVo = new AptPayConfigSaveVo();
        saveReqVo.setProjectId("192");
        saveReqVo.setPaymentType("微信支付");
        saveReqVo.setCompanyCode("31007");

        AptPayConfig existingConfig = new AptPayConfig();
        when(aptPayConfigRepository.findAll(any(BooleanExpression.class))).thenReturn(List.of(existingConfig));

        // Act & Assert
        assertThrows(AppException.class, () -> staffAptPayConfigController.save(saveReqVo));
        verify(aptPayConfigRepository, never()).save(any(AptPayConfig.class));
    }

    @Test
    @DisplayName("update - 异常场景：配置不存在")
    void update_configNotFound() {
        // Arrange
        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());
            AptPayConfigUpdateVo updateReqVo = new AptPayConfigUpdateVo();
            updateReqVo.setId(1L);

            when(aptPayConfigRepository.findOne(any(Predicate.class))).thenReturn(Optional.empty());

            // Act & Assert
            assertThrows(RuntimeException.class, () -> staffAptPayConfigController.update(updateReqVo));
            verify(aptPayConfigRepository, never()).save(any(AptPayConfig.class));
        }
    }

    @Test
    @DisplayName("update - 异常场景：hive数据不存在")
    void update_hiveDataNotFound() {
        // Arrange
        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());
            AptPayConfigUpdateVo updateReqVo = new AptPayConfigUpdateVo();
            updateReqVo.setId(1L);
            updateReqVo.setProjectId("111");

            AptPayConfig existingConfig = new AptPayConfig();
            existingConfig.setProjectId("222"); // 不同的楼盘ID

            when(aptPayConfigRepository.findOne(any(Predicate.class))).thenReturn(Optional.of(existingConfig));

            // Act & Assert
            assertThrows(RuntimeException.class, () -> staffAptPayConfigController.update(updateReqVo));
            verify(aptPayConfigRepository, never()).save(any(AptPayConfig.class));
        }
    }

    @Test
    @DisplayName("saveOtherPay - 正常场景：保存成功")
    void saveOtherPay_success() {
        // Arrange
        var saveReqVo = new AptOtherPayConfigSaveVo();
        saveReqVo.setProjectId("192");
        saveReqVo.setPaymentType("微信支付");

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = mockStatic(BeanUtil.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new AptPayConfigSaveVo());

            // Act and Assert
            assertThrows(IllegalArgumentException.class, () -> staffAptPayConfigController.saveOtherPay(saveReqVo));
        }
    }

    @Test
    @DisplayName("updateOtherPay - 正常场景：更新成功")
    void updateOtherPay_success() {
        // Arrange
        var updateReqVo = new AptOtherPayConfigUpdateVo();
        updateReqVo.setId(1L);
        updateReqVo.setProjectId("192");
        updateReqVo.setPaymentType("支付宝支付");

        AptPayConfig existingConfig = new AptPayConfig();
        existingConfig.setProjectId("192");

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = mockStatic(BeanUtil.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new AptPayConfigUpdateVo());

            // Act and Assert
            assertThrows(RuntimeException.class, () -> staffAptPayConfigController.updateOtherPay(updateReqVo));

        }
    }

}