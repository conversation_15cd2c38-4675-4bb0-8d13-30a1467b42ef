package com.kerryprops.kip.bill.webservice.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.BillRepository;
import com.kerryprops.kip.bill.dao.entity.BillEntity;
import com.kerryprops.kip.bill.feign.entity.QueryJdeBillSendPersonListRequest;
import com.kerryprops.kip.bill.feign.entity.TenantBillConfigResponse;
import com.kerryprops.kip.hiveas.feign.dto.resp.TenantRespDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.ArgumentMatchers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doReturn;

@Slf4j
class API_list_StaffBillControllerTest extends BaseClass_StaffBillControllerTest {

    @Autowired
    protected BillRepository billRepository;

    @Test
    public void _01_user_not_login_() {
        ResponseEntity<RespWrapVo> resp = call_list_api(null, StringUtils.EMPTY);
        assertEquals(HttpStatus.OK, resp.getStatusCode());
        assertEquals("User not login.", resp.getBody().getMessage());
        assertEquals(RespCodeEnum.UNKNOWN_ERROR.getCode(), resp.getBody().getCode());
    }

    /*@ParameterizedTest
    @CsvSource({
            "?, R1.PDF;R2.PDF;R4.PDF;R5.PDF;R6.PDF;R7.PDF;R8.PDF;R9.PDF;R10.PDF;R11.PDF;R12.PDF;R13.PDF",
            "?delFlag=1, R3.PDF;R14.PDF",
            "?delFlag=1&deleteTimeStart=2022-01-08&deleteTimeEnd=2022-01-31, R3.PDF",
            "?formatDate=2022-01-18,R5.PDF;R6.PDF",
            "?tpDoco=221133,R7.PDF;R10.PDF",
            "?tpAn8=11112222,R8.PDF;R11.PDF",
            "?tpUnit=P2-B2-R1,R10.PDF;R11.PDF;R12.PDF",
            "?tpAlph=B开发公司2, R9.PDF;R10.PDF;R12.PDF",
            "?tpFyr=21,R10.PDF;R11.PDF;R12.PDF",
            "?tpPn=2,R2.PDF;R13.PDF",
            "?tpDct=D2,R4.PDF;R5.PDF",
            "?readStatus=1,R4.PDF;R8.PDF;R12.PDF",
            "?tpStatus=10, R13.PDF",
            "?mailStatus=10,R2.PDF;R13.PDF",
            "?emailStatus=10,R7.PDF;R13.PDF",
            "?emailStatus=30,R2.PDF;R4.PDF;R5.PDF;R6.PDF"
    })
    public void _02_admin_query_one_param_(String param, String tpFilenms) {
        mockDoco();
        ResponseEntity<RespWrapVo> response = call_list_api(SUPER_ADMIN_JSON, param);

        //verification
        String[] tpFilenmArray = StringUtils.split(tpFilenms, ";");
        List<String> expectedTpFilenmList = Arrays.asList(tpFilenmArray);
        verify(response, expectedTpFilenmList);
    }*/

    /*@ParameterizedTest
    @CsvSource({
            "true, P2-B1, , R7.PDF;R8.PDF;R9.PDF",
            "true, P2-B1;P3-B1, , R7.PDF;R8.PDF;R9.PDF;R13.PDF",
            "true, ,P2, R7.PDF;R8.PDF;R9.PDF;R10.PDF;R11.PDF;R12.PDF",
            "true, ,P1;P3, R1.PDF;R2.PDF;R4.PDF;R5.PDF;R6.PDF;R13.PDF",
            "true,P2-B1,P2;P3, R7.PDF;R8.PDF;R9.PDF",
            "true,P2-B1;P3-B1,P2, R7.PDF;R8.PDF;R9.PDF",
            "true,P2-B1;P3-B1,P2;P3, R7.PDF;R8.PDF;R9.PDF;R13.PDF",

            "false, P2-B1, , R7.PDF;R8.PDF;R9.PDF",
            "false, P2-B1;P3-B1, , R7.PDF;R8.PDF;R9.PDF",
            "false, ,P2, R7.PDF;R8.PDF;R9.PDF",
            "false, ,P1;P3, R1.PDF;R2.PDF;R4.PDF;R5.PDF;R6.PDF",
            "false, ,P3, ",
            "false,P2-B1,P2;P3, R7.PDF;R8.PDF;R9.PDF",
            "false,P2-B1;P3-B1,P2, R7.PDF;R8.PDF;R9.PDF",
            "false,P2-B1;P3-B1,P2;P3, R7.PDF;R8.PDF;R9.PDF",
    })
    public void _03_combination_query_bu_(boolean isAdmin, String buildingIds, String projectIds, String tpGtFilenms)
            throws URISyntaxException, IOException {
        mockJdeBusByProject(projectIds);
        mockJdeBusByBuildingBU(isAdmin ? StringUtils.EMPTY : "P1-B1;P2-B1");
        mockJdeBusByBuildingBU(buildingIds);
        mockDoco();

        String xUserJson = isAdmin ? SUPER_ADMIN_JSON : STAFF_JSON;
        String param = "?1=1" +
                (StringUtils.isEmpty(buildingIds) ? StringUtils.EMPTY : "&buildingIds=" + convertSearchedId(buildingIds)) +
                (StringUtils.isEmpty(projectIds) ? StringUtils.EMPTY : "&projectId=" + convertSearchedId(projectIds));
        ResponseEntity<RespWrapVo> response = call_list_api(xUserJson, param);

        //verification
        String[] tpFilenmArray = StringUtils.isEmpty(tpGtFilenms) ? null : StringUtils.split(tpGtFilenms, ";");
        List<String> expectedTpFilenmList = tpFilenmArray == null || tpFilenmArray.length == 0 ?
                new ArrayList<>() : Arrays.asList(tpFilenmArray);
        verify(response, expectedTpFilenmList);
    }*/

    @BeforeEach
    public void prepareTestData() {
        List<BillEntity> billEntities = buildInitialDataset();
        billRepository.saveAll(billEntities);
    }

    @AfterEach
    public void clearTestData() {
        billRepository.deleteAll();
    }

    protected void verify(ResponseEntity<RespWrapVo> response, List<String> expectedTpFilenmList) {
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(RespCodeEnum.SUCCESS.getCode(), response.getBody().getCode());
        assertEquals(RespCodeEnum.SUCCESS.getMessage(), response.getBody().getMessage());

        Map<String, Object> map = (Map<String, Object>) response.getBody().getData();
        assertEquals(map.get("totalElements"), expectedTpFilenmList.size());
        assertEquals(map.get("totalPages"), CollectionUtils.isEmpty(expectedTpFilenmList) ? 0 : 1);

        Set<String> actualTpFilenmList = new HashSet<>();
        List<Map<String, Object>> contentList = (List<Map<String, Object>>) map.get("content");
        contentList.forEach(stringObjectMap -> {
            actualTpFilenmList.add(String.valueOf(stringObjectMap.get("tpGtfilenm")));
        });
        assertTrue(expectedTpFilenmList.stream().sorted().collect(Collectors.joining())
                .equals(actualTpFilenmList.stream().sorted().collect(Collectors.joining())));
    }

/*    private void mockDoco() {
        final String hiveRespJson = "{\n" +
                "\t\"id\": \"8aaa82308184efa40181862b3c010003\",\n" +
                "\t\"name\": \"福州项目小区商业租户1\",\n" +
                "\t\"brandName\": \"福州T2租户1w\",\n" +
                "\t\"status\": \"ENABLE\",\n" +
                "\t\"administrativeToiletEnabled\": true,\n" +
                "\t\"useFlexibleSpace\": true,\n" +
                "\t\"salesReported\": false,\n" +
                "\t\"area\": 100.00,\n" +
                "\t\"roomIdSet\": [\n" +
                "\t\t\"8aaa80cd80a6b9680180abe92cef000c\"\n" +
                "\t],\n" +
                "\t\"floorIdSet\": [\n" +
                "\t\t\"8a8485308069a70d01806a125a77001c\"\n" +
                "\t],\n" +
                "\t\"buildingIdSet\": [\n" +
                "\t\t\"FZJLZX-002\",\n" +
                "\t\t\"FZJLZX-R2\"\n" +
                "\t],\n" +
                "\t\"doCoSet\": [\n" +
                "\t\t\"999370\"\n" +
                "\t],\n" +
                "\t\"contracts\": [{\n" +
                "\t\t\"doco\": \"999370\",\n" +
                "\t\t\"version\": 1,\n" +
                "\t\t\"status\": \"TAKE_EFFECT\",\n" +
                "\t\t\"roomIds\": [{\n" +
                "\t\t\t\"id\": \"8aaa80cd80a6b9680180abe92cef000c\"\n" +
                "\t\t}]\n" +
                "\t}]\n" +
                "}";

        final String staffRespJson = "[{\n" +
                "\t\"id\": 500,\n" +
                "\t\"tenantId\": \"8a84800c8103abc3018104bd211a0000\",\n" +
                "\t\"managerId\": null,\n" +
                "\t\"userName\": null,\n" +
                "\t\"phoneNumber\": null,\n" +
                "\t\"email\": \"<EMAIL>\",\n" +
                "\t\"address\": \"10023951\",\n" +
                "\t\"createTime\": \"2022-06-06\"\n" +
                "}]";

        Set<TenantRespDto> tenantRespDtoSet = new HashSet<>();
        tenantRespDtoSet.add(JSONObject.parseObject(hiveRespJson, TenantRespDto.class));
        RespWrapVo<Set<TenantRespDto>> hiveRespWrapVo = new RespWrapVo<>(tenantRespDtoSet);
        doReturn(hiveRespWrapVo).when(hiveAsClient).getTenantIdByDoCo(ArgumentMatchers.anyList());

        List<TenantBillConfigResponse> tenantBillConfigResponseList = (List<TenantBillConfigResponse>)
                JSONArray.parseArray(staffRespJson, TenantBillConfigResponse.class);
        RespWrapVo<List<TenantBillConfigResponse>> staffResponseList = new RespWrapVo<>(tenantBillConfigResponseList);
        doReturn(staffResponseList).when(sUserClient).queryJdeNotifyPersons(ArgumentMatchers.any(QueryJdeBillSendPersonListRequest.class));
    }*/

    private ResponseEntity<RespWrapVo> call_list_api(String xUserJson, String urlParam) {
        // headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("x-user", xUserJson);

        HttpEntity request = new HttpEntity<>(null, headers);
        String url = "http://localhost:" + serverPort + "/s/bill/list" + urlParam;

        return restTemplate.exchange(url, HttpMethod.GET, request, RespWrapVo.class);
    }

}
