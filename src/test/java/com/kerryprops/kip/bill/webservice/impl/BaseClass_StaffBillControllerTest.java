package com.kerryprops.kip.bill.webservice.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.constants.BillConstants;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.utils.BuConverter;
import com.kerryprops.kip.bill.common.utils.DateUtils;
import com.kerryprops.kip.bill.dao.entity.BillEntity;
import com.kerryprops.kip.hiveas.feign.dto.BuildingDTO;
import com.kerryprops.kip.hiveas.feign.dto.ProjectDTO;
import com.kerryprops.kip.hiveas.feign.dto.resp.ProjectRespDto;
import com.kerryprops.kip.hiveas.webservice.vo.resp.BuildingRespVo;
import com.kerryprops.kip.hiveas.webservice.vo.resp.BuildingResponseVo;
import com.kerryprops.kip.hiveas.webservice.vo.resp.BuildingVo;
import com.kerryprops.kip.hiveas.webservice.vo.resp.CenterRespVo;
import com.kerryprops.kip.hiveas.webservice.vo.resp.ProjectBuildingVO;
import org.apache.commons.lang3.StringUtils;
import org.mockito.ArgumentMatchers;

import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static org.mockito.Mockito.doReturn;

public class BaseClass_StaffBillControllerTest extends BaseIntegrationTest {

    protected final String SUPER_ADMIN_JSON = "{\"nickName\":\"SuperAdmin\",\"roles\":\"SUPER_ADMIN\",\"staff\":true," +
            "\"userId\":123," + "\"uniqueUserId\":\"123\",\"phoneNumber\":\"13661600321\",\"fromType\":\"S\"," +
            "\"client\":false," + "\"superAdmin\":true,\"tenant\":false}";

    protected final String STAFF_JSON = "{\"buildingIds\":\"P1-B1,P2-B1\",\"nickName\":\"JAKCAdmin\"," +
            "\"staff\":true,\"userId\":345,\"uniqueUserId\":\"345\",\"fromType\":\"S\",\"client\":false," +
            "\"superAdmin\":false,\"tenant\":false}";

    protected final String BASE_PATH = "templates/staff_bill/list/";

    protected void mockBuildingBU(String buildingId) throws URISyntaxException, IOException {
        if (StringUtils.isEmpty(buildingId)) {
            return;
        }

        List<BuildingResponseVo> buildingRespVoSet = Lists.newLinkedList();
        String[] array = StringUtils.split(buildingId, ";");
        for (String str : array) {
            String hiveFilepath = BASE_PATH + "HiveVas_Building_" + str.trim() + ".json";
            String hiveData = Files.readString(Path.of(getClass().getClassLoader().getResource(hiveFilepath).toURI()));
            buildingRespVoSet.add(JSONObject.parseObject(hiveData, BuildingResponseVo.class));
        }

        List<String> buList = Arrays.stream(array).collect(Collectors.toList());
        doReturn(buildingRespVoSet).when(hiveAsClient).getBuildingByIds(buList);
    }

    protected void mockJdeBusByBuildingBU(String buildingId) throws URISyntaxException, IOException {
        if (StringUtils.isEmpty(buildingId)) {
            return;
        }

        Set<BuildingRespVo> buildingRespVoSet = new HashSet<>();
        String[] array = StringUtils.split(buildingId, ";");
        for (String str : array) {
            String hiveFilepath = BASE_PATH + "HiveVas_Building_" + str.trim() + ".json";
            String hiveData = Files.readString(Path.of(getClass().getClassLoader().getResource(hiveFilepath).toURI()));
            buildingRespVoSet.add(JSONObject.parseObject(hiveData, BuildingRespVo.class));
        }

        List<String> bus = new ArrayList<>();
        bus.addAll(buildingRespVoSet.stream().map(e -> BuConverter.getBuList(e.getBuilding().getPropertyManagementBU()))
                .flatMap(Collection::stream).collect(Collectors.toList()));
        bus.addAll(buildingRespVoSet.stream().map(e -> BuConverter.getBuList(e.getBuilding().getPropertyDeveloperBU()))
                .flatMap(Collection::stream).collect(Collectors.toList()));

        List<String> buList = Arrays.stream(array).collect(Collectors.toList());
        doReturn((Lists.newArrayList(bus))).when(hiveAsClient).convertToJdeBus(buList);
    }

    protected void mockProjectId(String projectId) throws URISyntaxException, IOException {
        if (StringUtils.isEmpty(projectId)) {
            return;
        }

        List<CenterRespVo> centerRespVoList = new LinkedList<>();
        List<ProjectBuildingVO> projectBuildingVOs = new LinkedList<>();
        String[] array = StringUtils.split(projectId, ";");
        for (String str : array) {
            String hiveFilepath = BASE_PATH + "HiveVas_Project_" + str.trim() + ".json";
            System.out.println("[project]" + hiveFilepath);
            String hiveData = Files.readString(Path.of(getClass().getClassLoader().getResource(hiveFilepath).toURI()));
            centerRespVoList.add(JSONObject.parseObject(hiveData, CenterRespVo.class));
        }

        // 类型转换，兼容新的hive接口
        centerRespVoList.forEach(centerRespVo -> {
            ProjectBuildingVO projectBuildingVO = new ProjectBuildingVO();

            ProjectRespDto projectRespDto = centerRespVo.getCenter();
            ProjectDTO projectDTO = BeanUtil.copy(projectRespDto, ProjectDTO.class);

            List<BuildingDTO> buildings = new ArrayList<>();
            centerRespVo.getBuildings().forEach(buildingVo -> {
                BuildingDTO buildingDTO = BeanUtil.copy(buildingVo.getBuilding(), BuildingDTO.class);
                buildings.add(buildingDTO);
            });

            projectBuildingVO.setProject(projectDTO);
            projectBuildingVO.setBuildings(buildings);
            projectBuildingVOs.add(projectBuildingVO);
        });

        doReturn(projectBuildingVOs).when(hiveAsClient).getCenterByIds(ArgumentMatchers.any());
    }

    protected void mockJdeBusByProject(String projectId) throws URISyntaxException, IOException {
        if (StringUtils.isEmpty(projectId)) {
            return;
        }

        List<CenterRespVo> centerRespVoList = new LinkedList<>();
        String[] array = StringUtils.split(projectId, ";");
        for (String str : array) {
            String hiveFilepath = BASE_PATH + "HiveVas_Project_" + str.trim() + ".json";
            System.out.println("[project]" + hiveFilepath);
            String hiveData = Files.readString(Path.of(getClass().getClassLoader().getResource(hiveFilepath).toURI()));
            centerRespVoList.add(JSONObject.parseObject(hiveData, CenterRespVo.class));
        }

        List<BuildingVo> buildingVos = centerRespVoList.stream()
                .filter(centerRespVo -> centerRespVo.getBuildings() != null)
                .map(CenterRespVo::getBuildings).flatMap(Collection::stream).collect(Collectors.toList());

        Set<String> bus = new HashSet<>();
        bus.addAll(buildingVos.stream().map(e -> BuConverter.getBuList(e.getBuilding().getPropertyManagementBU()))
                .flatMap(Collection::stream).collect(Collectors.toList()));
        bus.addAll(buildingVos.stream().map(e -> BuConverter.getBuList(e.getBuilding().getPropertyDeveloperBU()))
                .flatMap(Collection::stream).collect(Collectors.toList()));
        doReturn((Lists.newArrayList(bus))).when(hiveAsClient).convertToJdeBus((String[]) ArgumentMatchers.any());
    }

    protected String convertSearchedId(String id) {
        if (StringUtils.isEmpty(id)) {
            return StringUtils.EMPTY;
        }

        return id.trim().replaceAll(";", ",");
    }

    protected List<BillEntity> buildInitialDataset() {
        List<BillEntity> billEntities = Arrays.asList(
                BillEntity.builder().tpEv01("Y").tpDct("D1").tpMcu("43255100").tpUnit("P1-B1-R1").tpDoco("112233")
                        .tpAn8("12345678").tpAlph("A开发公司").tpFyr(22).tpPn(1).formatDate("2022-01-05").delFlag("0")
                        .tpStatus(5).mailStatus(BillConstants.MSG_SUCCESS).emailStatus(BillConstants.MSG_NOT_SEND)
                        .mailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-05 20:23:58"))
                        .readStatus(0).tpGtfilenm("R1.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("D1").tpMcu("43255100").tpUnit("P1-B1-R1").tpDoco("112233")
                        .tpAn8("12345678").tpAlph("A开发公司").tpFyr(22).tpPn(2).formatDate("2022-02-04").delFlag("0")
                        .tpStatus(5).mailStatus(BillConstants.MSG_FAILURE).emailStatus(BillConstants.MSG_SUCCESS)
                        .mailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-02-04 10:11:22"))
                        .emailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-02-04 11:30:20"))
                        .emailErr("[{\"email\":\"<EMAIL>\",\"sendStatus\":\"发送成功\",\"sendTime\":\"2022-02-04 11:41:41\"}]")
                        .readStatus(0).tpGtfilenm("R2.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("D1").tpMcu("43255200").tpUnit("P1-B1-R2").tpDoco("112233")
                        .tpAn8("12345678").tpAlph("A开发公司").tpFyr(22).tpPn(1).formatDate("2022-01-07").delFlag("1")
                        .deleteBy("jane.dong")
                        .deleteTime(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-08 11:22:59"))
                        .tpStatus(BillConstants.MSG_NOT_SEND).mailStatus(BillConstants.MSG_NOT_SEND)
                        .emailStatus(BillConstants.MSG_NOT_SEND).readStatus(0).tpGtfilenm("R3.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("D2").tpMcu("43255200").tpUnit("P1-B1-R2").tpDoco("112233")
                        .tpAn8("12345678").tpAlph("A开发公司").tpFyr(22).tpPn(1).formatDate("2022-01-08").delFlag("0")
                        .tpStatus(5).mailStatus(BillConstants.MSG_NOT_SEND).emailStatus(BillConstants.MSG_SUCCESS)
                        .emailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-08 16:10:20"))
                        .emailErr("[{\"email\":\"<EMAIL>\",\"sendStatus\":\"发送成功\",\"sendTime\":\"2022-01-08 16:31:41\"}]")
                        .readStatus(1).tpGtfilenm("R4.PDF").build(),

                BillEntity.builder().tpEv01("Y").tpDct("D2").tpMcu("43255300").tpUnit("P1-B1-R3").tpDoco("112233")
                        .tpAn8("12345678").tpAlph("A开发公司").tpFyr(22).tpPn(1).formatDate("2022-01-18").delFlag("0")
                        .tpStatus(5).mailStatus(BillConstants.MSG_NOT_SEND).emailStatus(BillConstants.MSG_PARTIAL_SUCCESS)
                        .emailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-18 14:00:05"))
                        .emailErr("[{\"email\":\"<EMAIL>\",\"sendStatus\":\"发送成功\",\"sendTime\":\"2022-01-18 14:31:41\"}]")
                        .readStatus(0).tpGtfilenm("R5.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("DN").tpMcu("37448300").tpUnit("P1-B1-R3").tpDoco("113300")
                        .tpAn8("12345678").tpAlph("A物业公司").tpFyr(22).tpPn(1).formatDate("2022-01-18").delFlag("0")
                        .tpStatus(5).mailStatus(BillConstants.MSG_SUCCESS).emailStatus(BillConstants.MSG_SUCCESS)
                        .mailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-18 13:14:16"))
                        .emailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-18 14:00:02"))
                        .emailErr("[{\"email\":\"<EMAIL>\",\"sendStatus\":\"发送成功\",\"sendTime\":\"2022-01-18 14:31:41\"}]")
                        .readStatus(0).tpGtfilenm("R6.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("DN").tpMcu("43254100").tpUnit("P2-B1-R1").tpDoco("221133")
                        .tpAn8("87654321").tpAlph("B开发公司1").tpFyr(22).tpPn(1).formatDate("2022-01-11").delFlag("0")
                        .tpStatus(5).mailStatus(BillConstants.MSG_SUCCESS).emailStatus(BillConstants.MSG_FAILURE)
                        .mailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-18 10:20:22"))
                        .emailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-11 10:30:45"))
                        .emailErr("[{\"email\":\"<EMAIL>\",\"sendStatus\":\"发送失败\",\"sendTime\":\"2022-01-11 10:30:41\"}]")
                        .readStatus(0).tpGtfilenm("R7.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("DN").tpMcu("38467100").tpUnit("P2-B1-R1").tpDoco("221155")
                        .tpAn8("11112222").tpAlph("B物业公司").tpFyr(22).tpPn(1).formatDate("2022-01-11").delFlag("0")
                        .tpStatus(5).mailStatus(BillConstants.MSG_SUCCESS).emailStatus(BillConstants.MSG_SENDING)
                        .mailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-11 12:01:02"))
                        .emailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-11 12:15:10"))
                        .emailErr("[{\"email\":\"<EMAIL>\",\"sendStatus\":\"发送中\",\"sendTime\":\"2022-01-11 12:15:41\"}]")
                        .readStatus(1).tpGtfilenm("R8.PDF").build(),

                BillEntity.builder().tpEv01("Y").tpDct("DN").tpMcu("43251100").tpUnit("P2-B1-R1").tpDoco("221144")
                        .tpAn8("87654321").tpAlph("B开发公司2").tpFyr(22).tpPn(1).formatDate("2022-01-12").delFlag("0")
                        .tpStatus(5).mailStatus(BillConstants.MSG_SUCCESS).emailStatus(BillConstants.MSG_NOT_SEND)
                        .mailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-12 10:24:30"))
                        .readStatus(0).tpGtfilenm("R9.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("DN").tpMcu("43254200").tpUnit("P2-B2-R1").tpDoco("221133")
                        .tpAn8("87654321").tpAlph("B开发公司2").tpFyr(21).tpPn(12).formatDate("2021-12-25").delFlag("0")
                        .tpStatus(BillConstants.MSG_NOT_SEND).mailStatus(BillConstants.MSG_NOT_SEND)
                        .emailStatus(BillConstants.MSG_NOT_SEND).readStatus(0).tpGtfilenm("R10.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("DN").tpMcu("38467200").tpUnit("P2-B2-R1").tpDoco("221155")
                        .tpAn8("11112222").tpAlph("B物业公司").tpFyr(21).tpPn(12).formatDate("2021-12-24").delFlag("0")
                        .tpStatus(BillConstants.MSG_NOT_SEND).mailStatus(BillConstants.MSG_NOT_SEND)
                        .emailStatus(BillConstants.MSG_NOT_SEND).readStatus(0).tpGtfilenm("R11.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("DN").tpMcu("43251200").tpUnit("P2-B2-R1").tpDoco("221144")
                        .tpAn8("87654321").tpAlph("B开发公司2").tpFyr(21).tpPn(12).formatDate("2021-12-16").delFlag("0")
                        .tpStatus(BillConstants.MSG_NOT_SEND).mailStatus(BillConstants.MSG_NOT_SEND)
                        .emailStatus(BillConstants.MSG_NOT_SEND).readStatus(1).tpGtfilenm("R12.PDF").build(),

                BillEntity.builder().tpEv01("Y").tpDct("DN").tpMcu("43158100").tpUnit("P3-B1-R1").tpDoco("331122")
                        .tpAn8("33331111").tpAlph("C公司").tpFyr(22).tpPn(2).formatDate("2022-02-04").delFlag("0")
                        .tpStatus(BillConstants.MSG_FAILURE).mailStatus(BillConstants.MSG_FAILURE)
                        .emailStatus(BillConstants.MSG_FAILURE).readStatus(0).tpGtfilenm("R13.PDF")
                        .mailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-02-04 10:13:20"))
                        .emailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-02-04 10:15:30"))
                        .emailErr("[{\"email\":\"<EMAIL>\",\"sendStatus\":\"发送失败\",\"sendTime\":\"2022-02-04 10:31:41\"}]")
                        .build(),
                BillEntity.builder().tpEv01("Y").tpDct("DN").tpMcu("43158200").tpUnit("P3-B1-R1").tpDoco("331122")
                        .tpAn8("33331111").tpAlph("C公司").tpFyr(22).tpPn(3).formatDate("2022-01-04").delFlag("1")
                        .deleteBy("jane.dong")
                        .deleteTime(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-02-08 11:22:59"))
                        .tpStatus(BillConstants.MSG_NOT_SEND).mailStatus(BillConstants.MSG_NOT_SEND)
                        .emailStatus(BillConstants.MSG_NOT_SEND).readStatus(0).tpGtfilenm("R14.PDF").build(),
                BillEntity.builder().tpEv01("").tpDct("DN").tpMcu("43158200").tpUnit("P3-B1-R1").tpDoco("331122")
                        .tpAn8("33331111").tpAlph("C公司").tpFyr(22).tpPn(2).formatDate("2022-01-04").delFlag("0")
                        .tpStatus(BillConstants.MSG_NOT_SEND).mailStatus(BillConstants.MSG_NOT_SEND)
                        .emailStatus(BillConstants.MSG_NOT_SEND).readStatus(0).tpGtfilenm("R15.PDF").build()
        );

        return billEntities;
    }

}
