package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.constants.BillConstants;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.BillRepository;
import com.kerryprops.kip.bill.dao.entity.BillEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
public class API_selectBillDistinctYears_BizBillControllerTest extends BaseIntegrationTest {

    private static final String OK_X_USER = "{\"userId\":1012,\"fromType\":\"B\",\"loginAccount\":\"********\"," +
            "\"nickName\":\"%E8%91%A3%E9%9D%99%E8%8C%B9\",\"phoneNumber\":\"***********\"," +
            "\"companyIds\":\"4028e3817c7229a7017c722e870e0000,,8aaa82257cb6de27017cb6e723aa0000\"," +
            "\"projectIds\":\"HKC,TKC\",\"buildingIds\":\"HKC-T1,HKC-R1,BKC-N1,TKC-R\",\"source\":\"web\"}";

    private static final String NG_X_USER = "{\"userId\":1012,\"fromType\":\"B\",\"loginAccount\":\"********\"," +
            "\"nickName\":\"%E8%91%A3%E9%9D%99%E8%8C%B9\",\"phoneNumber\":\"***********\"," +
            "\"companyIds\":\"4028e3817c7229a7017c722e870e0000,,8aaa82257cb6de27017cb6e723aa0000\"," +
            "\"projectIds\":\"HKC,TKC\",\"buildingIds\":\"HKC-T1,HKC-R1,BKC-N1,TKC-R\",\"source\":\"web\"}";

    private static final String ADMIN_X_USER = "{\"nickName\":\"SuperAdmin\",\"roles\":\"SUPER_ADMIN\",\"staff\":true," +
            "\"userId\":123,\"uniqueUserId\":\"123\",\"phoneNumber\":\"***********\",\"fromType\":\"S\",\"client\":false," +
            "\"superAdmin\":true,\"tenant\":false}";

    private static final String C_X_USER = "{\"cId\":\"123455\",\"client\":true,\"companyIds\":\"015\",\"fromType\":\"C\"," +
            "\"nickName\":\"David\",\"phoneNumber\":\"***********\",\"roles\":\"OFFICE_AUTHORIZER,APARTMENT_AUTHORIZER\"," +
            "\"staff\":false,\"superAdmin\":false,\"tenant\":false,\"uniqueUserId\":\"123455\"}";

    private static final String NG_B_X_USER = "{\"companyIds\":null,\"nickName\":\"jane.dong\",\"staff\":false," +
            "\"userId\":1012,\"uniqueUserId\":\"26\",\"phoneNumber\":\"13764912345\",\"fromType\":\"B\",\"client\":false," +
            "\"superAdmin\":false,\"tenant\":true}";

    @Autowired
    private BillRepository billRepository;

    @ParameterizedTest
    @NullAndEmptySource
    public void _01_selectBillDistinctYears_null_empty_xUser(String xUserJson) {
        ResponseEntity<RespWrapVo<List<Integer>>> response = call_api(xUserJson);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNull(response.getBody());
    }

    @Test
    public void _02_selectBillDistinctYears_fromType_S() {
        ResponseEntity<RespWrapVo<List<Integer>>> response = call_api(ADMIN_X_USER);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNull(response.getBody());
    }

    @Test
    public void _03_selectBillDistinctYears_fromType_C() {
        ResponseEntity<RespWrapVo<List<Integer>>> response = call_api(C_X_USER);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNull(response.getBody());
    }

    @Test
    public void _04_selectBillDistinctYears_fromType_B_without_companyIds() {
        ResponseEntity<RespWrapVo<List<Integer>>> response = call_api(NG_B_X_USER);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(CollectionUtils.isEmpty(response.getBody().getData()));
    }

    @Test
    public void _05_selectBillDistinctYears_no_data() {
        ResponseEntity<RespWrapVo<List<Integer>>> responseEntity = call_api(OK_X_USER);
        List<Integer> dataList = (List<Integer>) responseEntity.getBody().getData();
        Assertions.assertTrue(CollectionUtils.isEmpty(dataList));
    }

    @Test
    public void _06_selectBillDistinctYears_all_deleted_data() {
        //prepare test data
        List<BillEntity> oldBillEntites = List.of(
                buildBillEntity("Y", true, 0, 22, 1),
                buildBillEntity("Y", true, 5, 22, 2),
                buildBillEntity("Y", true, 20, 22, 3),
                buildBillEntity("", true, 0, 22, 4)
        );
        List<BillEntity> newBillEntities = billRepository.saveAll(oldBillEntites);

        //execute & verify
        ResponseEntity<RespWrapVo<List<Integer>>> responseEntity = call_api(OK_X_USER);
        List<Integer> dataList = (List<Integer>) responseEntity.getBody().getData();
        Assertions.assertTrue(CollectionUtils.isEmpty(dataList));

        //clear test data
        billRepository.deleteAll(newBillEntities);
    }

    @BeforeEach
    public void clearTestData() {
        billRepository.deleteAll();
    }

    @Test
    public void _07_selectBillDistinctYears_not_display_on_web() {
        //prepare test data
        List<BillEntity> oldBillEntites = List.of(
                buildBillEntity("X", false, 0, 22, 1),
                buildBillEntity("", false, 5, 22, 2),
                buildBillEntity("", false, 20, 22, 3),
                buildBillEntity("", false, 0, 22, 4)
        );
        List<BillEntity> newBillEntities = billRepository.saveAll(oldBillEntites);

        //execute & verify
        ResponseEntity<RespWrapVo<List<Integer>>> responseEntity = call_api(OK_X_USER);
        List<Integer> dataList = (List<Integer>) responseEntity.getBody().getData();
        Assertions.assertTrue(CollectionUtils.isEmpty(dataList));

        //clear test data
        billRepository.deleteAll(newBillEntities);
    }

    @Test
    public void _08_selectBillDistinctYears_valid() {
        //prepare test data
        List<BillEntity> oldBillEntites = List.of(
                buildBillEntity("", false, 0, 22, 1),
                buildBillEntity("Y", true, 5, 22, 2),
                buildBillEntity("", true, 20, 22, 3),
                buildBillEntity("Y", false, 0, 22, 4),
                buildBillEntity("Y", false, 5, 22, 5),
                buildBillEntity("Y", false, 20, 22, 6),
                buildBillEntity("Y", false, 0, 21, 7),
                buildBillEntity("Y", false, 5, 20, 8),
                buildBillEntity("Y", false, 20, 19, 9)
        );
        List<BillEntity> newBillEntities = billRepository.saveAll(oldBillEntites);

        //execute & verify
        ResponseEntity<RespWrapVo<List<Integer>>> responseEntity = call_api(OK_X_USER);
        List<Integer> dataList = (List<Integer>) responseEntity.getBody().getData();
        String actualYears = dataList.stream().map(integer -> String.valueOf(integer)).collect(Collectors.joining(","));
        String expectYears = "2022,2021,2020,2019";
        Assertions.assertEquals(expectYears, actualYears);

        //clear test data
        billRepository.deleteAll(newBillEntities);
    }

    private ResponseEntity<RespWrapVo<List<Integer>>> call_api(String xUserJson) {
        // headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("x-user", xUserJson);

        HttpEntity request = new HttpEntity<>(null, headers);
        String url = "http://localhost:" + serverPort + "/b/bill/months";
        ParameterizedTypeReference<RespWrapVo<List<Integer>>> reference = new ParameterizedTypeReference<>() {

        };
        return restTemplate.exchange(url, HttpMethod.GET, request, reference);
    }

    private BillEntity buildBillEntity(String tpEv01, boolean deleted, int tpStatus, int tpFyr, int tpPn) {
        BillEntity entity = BillEntity.builder()
                .tpDct("DN").tpDl01("付款通知书").tpGtitnm("Debit Note")
                .tpGtfilenm(tpStatus == 20 ? StringUtils.EMPTY : "RD001.PDF")
                .fileUrl(tpStatus == 20 ? StringUtils.EMPTY : "https://kip-private-dev.oss-cn-shanghai.aliyuncs.com/abc_RD001.PDF")
                .tpCo("31019").tpDl03("嘉里(沈阳)房地产开发有限公司").tpMcu("31194100").tpUnit("208")
                .tpAlph("DEV测试").tpDc("嘉里沈阳，租赁，商场").tpCrtutime(new Date())
                .tpEv01(tpEv01).tpEv02(StringUtils.EMPTY).tpEv03("Y").delFlag(deleted ? "1" : "0")
                .formatDate("2018-11-20").tpAn8("********").tpDoco("123456")
                .tpStatus(tpStatus).tpFyr(tpFyr).tpPn(tpPn).billMonth(100 * tpFyr + tpPn)
                .mailStatus(tpStatus == 5 ? BillConstants.MSG_SUCCESS : BillConstants.MSG_NOT_SEND)
                .build();
        return entity;
    }

}
