package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.BillRepository;
import com.kerryprops.kip.bill.dao.entity.BillEntity;
import com.kerryprops.kip.bill.webservice.vo.resp.BillPayer;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;

class API_queryPayers_StaffBillControllerTest extends BaseClass_StaffBillControllerTest {

    private List<BillEntity> dataSet;

    @Autowired
    private BillRepository billRepository;

    @BeforeEach
    void prepareTestData() {
        String[] docos = {"112211",                                             //1
                "112233", "112233", "112233", "112233", "112233",               //2~6
                "112244", "112244", "112244", "112244", "112244", "112244",     //7~12
                "112255", "112255", "112255"                                    //13~15
        };
        String[] an8s = {"11223301",
                "11223302", "11223303", "11223304", "11223305", "11223306",                 //2~6
                "11223307", "11223308", "11223309", "11223310", "11223311", "11223312",     //7~12
                "11223313", "11223314", "11223315"                                          //13~15
        };
        String[] alphs = {"公司1",
                "公司2", "公司12", "公司21", "公司2", "公司21",           //2~6
                "公司3", "公司3", "公司3", "公司3", "公司3", "公司3",     //7~12
                "公司4", "公司4", "公司4"                               //13~15
        };
        List<Integer> deleteBillIndexList = Lists.list(1, 7, 13);
        List<BillEntity> billEntities = buildInitialDataset();
        for (int i = 1; i <= billEntities.size(); i++) {
            String doco = docos[i - 1];
            String an8 = an8s[i - 1];
            String alph = alphs[i - 1];
            String delFlag = deleteBillIndexList.contains(Integer.valueOf(i)) ? "1" : "0";

            BillEntity billEntity = billEntities.get(i - 1);
            billEntity.setTpDoco(doco);
            billEntity.setTpAn8(an8);
            billEntity.setTpAlph(alph);
            billEntity.setDelFlag(delFlag);
        }

        dataSet = billRepository.saveAll(billEntities);
    }

    @AfterEach
    void clearTestData() {
        billRepository.deleteAll(dataSet);
    }

    @Test
    void _01_queryPayers_emptyDoco() {
        ResponseEntity<RespWrapVo> responseEntity = call_queryPayers_api(StringUtils.EMPTY);
        RespWrapVo respWrapVo = responseEntity.getBody();
        assertEquals(respWrapVo.getCode(), RespCodeEnum.UNKNOWN_ERROR.getCode());
    }

    @Test
    void _02_queryPayers_1blankDoco() {
        ResponseEntity<RespWrapVo> responseEntity = call_queryPayers_api(" ");
        RespWrapVo respWrapVo = responseEntity.getBody();
        assertEquals(respWrapVo.getCode(), RespCodeEnum.UNKNOWN_ERROR.getCode());
    }

    @Test
    void _03_queryPayers_2blanksDoco() {
        ResponseEntity<RespWrapVo> responseEntity = call_queryPayers_api("  ");
        RespWrapVo respWrapVo = responseEntity.getBody();
        assertEquals(respWrapVo.getCode(), RespCodeEnum.UNKNOWN_ERROR.getCode());
    }

    @Test
    void _04_queryPayers_allDeletedDocos() {
        ResponseEntity<RespWrapVo> responseEntity = call_queryPayers_api("112211");

        assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
        assertEquals(RespCodeEnum.SUCCESS.getCode(), responseEntity.getBody().getCode());
        assertEquals(RespCodeEnum.SUCCESS.getMessage(), responseEntity.getBody().getMessage());
        List<BillPayer> actualPayerList = (List<BillPayer>) responseEntity.getBody().getData();
        assertEquals(actualPayerList.size(), 0);
    }

    @Test
    void _05_queryPayers_partDeletedDocos() {
        ResponseEntity<RespWrapVo> responseEntity = call_queryPayers_api("112255");
        List<Map<String, String>> responseList = (List<Map<String, String>>) responseEntity.getBody().getData();
        List<Map<String, String>> responseListSorted = responseList.stream()
                .sorted(Comparator.comparing(o -> o.get("tpAn8"))).collect(Collectors.toList());
        responseEntity.getBody().setData(responseListSorted);

        List<BillPayer> expectPayerList = Lists.list(
                BillPayer.builder().tpAlph("公司4").tpAn8("11223314").build(),
                BillPayer.builder().tpAlph("公司4").tpAn8("11223315").build());
        verify(responseEntity, expectPayerList);
    }

    @Test
    void _06_queryPayers_verifyResultSetSorter() {
        ResponseEntity<RespWrapVo> responseEntity = call_queryPayers_api("112233");
        List<Map<String, String>> responseList = (List<Map<String, String>>) responseEntity.getBody().getData();
        List<Map<String, String>> responseListSorted = responseList.stream()
                .sorted(Comparator.comparing(o -> o.get("tpAn8"))).collect(Collectors.toList());
        responseEntity.getBody().setData(responseListSorted);

        List<BillPayer> expectPayerList = Lists.list(
                BillPayer.builder().tpAlph("公司2").tpAn8("11223302").build(),
                BillPayer.builder().tpAlph("公司12").tpAn8("11223303").build(),
                BillPayer.builder().tpAlph("公司21").tpAn8("11223304").build(),
                BillPayer.builder().tpAlph("公司2").tpAn8("11223305").build(),
                BillPayer.builder().tpAlph("公司21").tpAn8("11223306").build());
        verify(responseEntity, expectPayerList);
    }

    private void verify(ResponseEntity<RespWrapVo> response, List<BillPayer> expectPayerList) {
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(RespCodeEnum.SUCCESS.getCode(), response.getBody().getCode());
        assertEquals(RespCodeEnum.SUCCESS.getMessage(), response.getBody().getMessage());

        List<Map<String, Object>> actualList = (List<Map<String, Object>>) response.getBody().getData();
        assertEquals(actualList.size(), expectPayerList.size());

        for (int i = 0; i < actualList.size(); i++) {
            Map<String, Object> actualPayerMap = actualList.get(i);
            String actualTpAlph = (String) actualPayerMap.get("tpAlph");
            String actualTpAn8 = (String) actualPayerMap.get("tpAn8");

            BillPayer expectBillPayer = expectPayerList.get(i);

            assertEquals(actualTpAlph, expectBillPayer.getTpAlph());
            assertEquals(actualTpAn8, expectBillPayer.getTpAn8());
        }
    }

    private ResponseEntity<RespWrapVo> call_queryPayers_api(String doco) {
        // headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity request = new HttpEntity<>(null, headers);
        String url = "http://localhost:" + serverPort + "/s/bill/payers/" + doco;

        return restTemplate.exchange(url, HttpMethod.GET, request, RespWrapVo.class);
    }

}
