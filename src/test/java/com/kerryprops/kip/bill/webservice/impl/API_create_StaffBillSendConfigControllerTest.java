package com.kerryprops.kip.bill.webservice.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.kerryprops.kip.bill.common.constants.BillConstants;
import com.kerryprops.kip.bill.common.exceptions.ExceptionResource;
import com.kerryprops.kip.bill.common.utils.DateUtils;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.BillRepository;
import com.kerryprops.kip.bill.dao.entity.BillEntity;
import com.kerryprops.kip.bill.feign.entity.TenantManagerItemResponse;
import com.kerryprops.kip.bill.webservice.vo.req.BillPayerVo;
import com.kerryprops.kip.bill.webservice.vo.req.StaffBillSendConfigInsertVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
class API_create_StaffBillSendConfigControllerTest extends BaseClass_StaffBillSendConfigControllerTest {

    private final String METHOD_ARGUMENT_ERROR_CODE = "120003";

    @Autowired
    private BillRepository billRepository;

    @ParameterizedTest
    @NullAndEmptySource
    void _01_create_validate_01_doco_blank(String doco) throws JsonProcessingException {
        StaffBillSendConfigInsertVo saveVo = buildInsertVo();
        saveVo.setDoco(doco);

        ResponseEntity<ExceptionResource> responseEntity = call_insert_api_exception(saveVo);
        ExceptionResource exceptionResource = responseEntity.getBody();
        Assertions.assertEquals(METHOD_ARGUMENT_ERROR_CODE, exceptionResource.getCode());
        Assertions.assertTrue(exceptionResource.getErrorText().contains("doco can not be null"));
    }

    @Test
    void _01_create_validate_01_doco_over_length() throws JsonProcessingException {
        StaffBillSendConfigInsertVo saveVo = buildInsertVo();
        saveVo.setDoco(buildString(33));

        ResponseEntity<ExceptionResource> responseEntity = call_insert_api_exception(saveVo);
        ExceptionResource exceptionResource = responseEntity.getBody();
        Assertions.assertEquals(METHOD_ARGUMENT_ERROR_CODE, exceptionResource.getCode());
        Assertions.assertTrue(exceptionResource.getErrorText().contains("doco must be within 32 characters in length"));

    }

    @ParameterizedTest
    @NullAndEmptySource
    void _01_create_validate_02_email_blank(String email) throws JsonProcessingException {
        StaffBillSendConfigInsertVo insertVo = buildInsertVo();
        insertVo.setEmail(email);

        ResponseEntity<ExceptionResource> responseEntity = call_insert_api_exception(insertVo);
        ExceptionResource exceptionResource = responseEntity.getBody();
        Assertions.assertEquals(METHOD_ARGUMENT_ERROR_CODE, exceptionResource.getCode());
        Assertions.assertTrue(exceptionResource.getErrorText().contains("email address can not be null"));
    }

    @Test
    void _01_create_validate_02_email_over_length() throws JsonProcessingException {
        StaffBillSendConfigInsertVo insertVo = buildInsertVo();
        insertVo.setEmail(buildString(129));

        ResponseEntity<ExceptionResource> responseEntity = call_insert_api_exception(insertVo);
        ExceptionResource exceptionResource = responseEntity.getBody();
        Assertions.assertEquals(METHOD_ARGUMENT_ERROR_CODE, exceptionResource.getCode());
        Assertions.assertTrue(exceptionResource.getErrorText().contains("email address must be within 128 characters in length"));
    }

    @ParameterizedTest
    @NullAndEmptySource
    void _01_create_validate_03_payer_blank(List<BillPayerVo> payerVoList) throws JsonProcessingException {
        StaffBillSendConfigInsertVo insertVo = buildInsertVo();
        insertVo.setBillPayerVos(payerVoList);

        ResponseEntity<ExceptionResource> responseEntity = call_insert_api_exception(insertVo);
        ExceptionResource exceptionResource = responseEntity.getBody();
        Assertions.assertEquals(METHOD_ARGUMENT_ERROR_CODE, exceptionResource.getCode());
        Assertions.assertTrue(exceptionResource.getErrorText().contains("payer is required"));
    }

    @Test
    void _01_create_validate_04_payer_alph_over_length() throws JsonProcessingException {
        StaffBillSendConfigInsertVo insertVo = buildInsertVo();
        List<BillPayerVo> payerVoList = insertVo.getBillPayerVos();
        BillPayerVo billPayerVo = payerVoList.get(0);
        billPayerVo.setAlph(buildString(256));

        ResponseEntity<ExceptionResource> responseEntity = call_insert_api_exception(insertVo);
        ExceptionResource exceptionResource = responseEntity.getBody();
        Assertions.assertEquals(METHOD_ARGUMENT_ERROR_CODE, exceptionResource.getCode());
        Assertions.assertTrue(exceptionResource.getErrorText().contains("alph must be within 255 characters in length"));
    }

    @ParameterizedTest
    @NullAndEmptySource
    void _01_create_validate_05_payer_an8_blank(String an8) throws JsonProcessingException {
        StaffBillSendConfigInsertVo insertVo = buildInsertVo();
        List<BillPayerVo> payerVoList = insertVo.getBillPayerVos();
        BillPayerVo billPayerVo = payerVoList.get(0);
        billPayerVo.setAn8(an8);

        ResponseEntity<ExceptionResource> responseEntity = call_insert_api_exception(insertVo);
        ExceptionResource exceptionResource = responseEntity.getBody();
        Assertions.assertEquals(METHOD_ARGUMENT_ERROR_CODE, exceptionResource.getCode());
        Assertions.assertTrue(exceptionResource.getErrorText().contains("an8 can not be null"));
    }

    @Test
    void _01_create_validate_05_payer_an8_over_length() throws JsonProcessingException {
        StaffBillSendConfigInsertVo insertVo = buildInsertVo();
        List<BillPayerVo> payerVoList = insertVo.getBillPayerVos();
        BillPayerVo billPayerVo = payerVoList.get(0);
        billPayerVo.setAn8(buildString(33));

        ResponseEntity<ExceptionResource> responseEntity = call_insert_api_exception(insertVo);
        ExceptionResource exceptionResource = responseEntity.getBody();
        Assertions.assertEquals(METHOD_ARGUMENT_ERROR_CODE, exceptionResource.getCode());
        Assertions.assertTrue(exceptionResource.getErrorText().contains("an8 must be within 32 characters in length"));
    }

    @Test
    void _01_create_validate_06_phone_number_over_length() throws JsonProcessingException {
        StaffBillSendConfigInsertVo insertVo = buildInsertVo();
        insertVo.setPhoneNumber(buildString(33));

        ResponseEntity<ExceptionResource> responseEntity = call_insert_api_exception(insertVo);
        ExceptionResource exceptionResource = responseEntity.getBody();
        Assertions.assertEquals(METHOD_ARGUMENT_ERROR_CODE, exceptionResource.getCode());
        Assertions.assertTrue(exceptionResource.getErrorText().contains("phoneNumber must be within 32 characters in length"));
    }

    @Test
    void _01_create_validate_07_login_no_over_length() throws JsonProcessingException {
        StaffBillSendConfigInsertVo insertVo = buildInsertVo();
        insertVo.setLoginNo(buildString(65));

        ResponseEntity<ExceptionResource> responseEntity = call_insert_api_exception(insertVo);
        ExceptionResource exceptionResource = responseEntity.getBody();
        Assertions.assertEquals(METHOD_ARGUMENT_ERROR_CODE, exceptionResource.getCode());
        Assertions.assertTrue(exceptionResource.getErrorText().contains("loginNo must be within 64 characters in length"));
    }

    @Test
    void _01_create_user_not_login() throws JsonProcessingException {
        StaffBillSendConfigInsertVo insertVo = buildInsertVo();

        ResponseEntity<ExceptionResource> responseEntity = call_insert_api_exception(insertVo);
        Assertions.assertEquals(responseEntity.getStatusCode(), HttpStatus.BAD_REQUEST);

        ExceptionResource resource = responseEntity.getBody();
        Assertions.assertEquals(resource.getErrorCode(), "400008");
        Assertions.assertEquals(resource.getErrorText(), "用户未登录");
    }

    @Test
    void _01_create_1doco_1an8() throws JsonProcessingException {
        //prepare test data
        List<BillEntity> billEntities = billRepository.saveAll(buildBillDataSet());
        StaffBillSendConfigInsertVo insertVo = new StaffBillSendConfigInsertVo();
        insertVo.setDoco("123456");
        insertVo.setEmail("<EMAIL>");
        insertVo.setBillPayerVos(Lists.list(BillPayerVo.builder().alph("Company_X1").an8("87654321").build()));

        StaffBillSendConfigInsertVo insertVo2 = buildInsertVo();
        //execute
        ResponseEntity<RespWrapVo> responseEntity = call_insert_api(insertVo2);

        //verify
        Map<String, Object> map = (Map<String, Object>) responseEntity.getBody().getData();
        Assertions.assertTrue((Integer) map.get("id") > 0);
        Assertions.assertEquals((String) map.get("doco"), insertVo2.getDoco());
        Assertions.assertEquals((String) map.get("phoneNumber"), insertVo2.getPhoneNumber());
        Assertions.assertEquals((String) map.get("tenantManagerId"), StringUtils.EMPTY);
        Assertions.assertEquals((String) map.get("emailUsername"), StringUtils.EMPTY);
        Assertions.assertEquals((String) map.get("email"), insertVo2.getEmail());
        Assertions.assertEquals((String) map.get("loginNo"), StringUtils.EMPTY);
        Assertions.assertEquals((String) map.get("mcu"), "mcu2");
        Assertions.assertEquals((String) map.get("unit"), "unit2,unit4");
        Assertions.assertEquals((Integer) map.get("isDel"), 0);

        BillPayerVo billPayerVo = insertVo.getBillPayerVos().get(0);
        List<Map<String, Object>> list = (List<Map<String, java.lang.Object>>) map.get("an8LinkList");
        Map<String, Object> an8Map = list.get(0);
        Assertions.assertTrue((Integer) an8Map.get("id") > 0);
        Assertions.assertEquals((Integer) an8Map.get("configId"), (Integer) map.get("id"));
        Assertions.assertEquals((String) an8Map.get("alph"), billPayerVo.getAlph());
        Assertions.assertEquals((String) an8Map.get("an8"), billPayerVo.getAn8());
        Assertions.assertEquals((Integer) an8Map.get("isDel"), 0);

        //clear test data
        billRepository.deleteAll(billEntities);
    }

    @Test
    void _01_create_1doco_2an8() throws JsonProcessingException {
        //prepare test data
        final String data = "a********9b********9c********9d1";
        List<BillEntity> billEntities = billRepository.saveAll(buildBillDataSet());

        TenantManagerItemResponse tenantManagerItemResponse = new TenantManagerItemResponse();
        tenantManagerItemResponse.setId(1234l);
        tenantManagerItemResponse.setLoginNo("********");
        tenantManagerItemResponse.setPhoneNumber(data);
        tenantManagerItemResponse.setUserName("tester");
        List<TenantManagerItemResponse> tenantManagerItemResponses = List.of(tenantManagerItemResponse);
        Mockito.doReturn(tenantManagerItemResponses).when(sUserClient).queryLoginAccounts(ArgumentMatchers.anyString());

        StaffBillSendConfigInsertVo insertVo = buildInsertVo();
        insertVo.setDoco(data);
        insertVo.setLoginNo(buildString(64));
        insertVo.setEmail(buildString(128));
        insertVo.setPhoneNumber(data);
        insertVo.setBillPayerVos(
                Lists.list(BillPayerVo.builder().alph(buildString(255)).an8(buildString(32)).build(),
                        BillPayerVo.builder().alph(buildString(255)).an8(buildString(32)).build()));

        //execute
        ResponseEntity<RespWrapVo> responseEntity = call_insert_api(insertVo);

        //verify
        Map<String, Object> map = (Map<String, Object>) responseEntity.getBody().getData();
        Assertions.assertTrue((Integer) map.get("id") > 0);
        Assertions.assertEquals((String) map.get("doco"), insertVo.getDoco());
        Assertions.assertEquals((String) map.get("phoneNumber"), insertVo.getPhoneNumber());
        Assertions.assertEquals((String) map.get("tenantManagerId"), String.valueOf(tenantManagerItemResponse.getId()));
        Assertions.assertEquals((String) map.get("emailUsername"), tenantManagerItemResponse.getUserName());
        Assertions.assertEquals((String) map.get("email"), insertVo.getEmail());
        Assertions.assertEquals((String) map.get("loginNo"), tenantManagerItemResponse.getLoginNo());
        Assertions.assertEquals((String) map.get("mcu"), "mcu2");
        Assertions.assertEquals((String) map.get("unit"), "unit3,unit5");
        Assertions.assertEquals((Integer) map.get("isDel"), 0);

        List<Map<String, Object>> list = (List<Map<String, java.lang.Object>>) map.get("an8LinkList");
        for (int i = 0; i < list.size(); i++) {
            BillPayerVo billPayerVo = insertVo.getBillPayerVos().get(i);
            Map<String, Object> an8Map = list.get(i);
            Assertions.assertTrue((Integer) an8Map.get("id") > 0);
            Assertions.assertEquals((Integer) an8Map.get("configId"), (Integer) map.get("id"));
            Assertions.assertEquals((String) an8Map.get("alph"), billPayerVo.getAlph());
            Assertions.assertEquals((String) an8Map.get("an8"), billPayerVo.getAn8());
            Assertions.assertEquals((Integer) an8Map.get("isDel"), 0);
        }

        //clear test data
        billRepository.deleteAll(billEntities);
    }

    private StaffBillSendConfigInsertVo buildInsertVo() {
        StaffBillSendConfigInsertVo vo = new StaffBillSendConfigInsertVo();
        vo.setDoco("123456");
        vo.setEmail("<EMAIL>");
        vo.setLoginNo("B123456");
        vo.setPhoneNumber("13764912168");
        vo.setProjectId("192");
        vo.setBuildingId("32014100");
        vo.setBuildingName("A2");
        vo.setBillPayerVos(Lists.list(BillPayerVo.builder().alph("Company_X1").an8("87654321").build()));
        return vo;
    }

    private ResponseEntity<RespWrapVo> call_insert_api(StaffBillSendConfigInsertVo insertVo)
            throws JsonProcessingException {
        final String X_USER = "{\"buildingIds\":\"\",\"nickName\":\"JAKCAdmin\"," +
                "\"staff\":true,\"userId\":345,\"uniqueUserId\":\"345\",\"fromType\":\"S\",\"client\":false," +
                "\"superAdmin\":false,\"tenant\":false}";

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.parseMediaType(MediaType.APPLICATION_JSON_UTF8_VALUE));
        httpHeaders.set("x-user", X_USER);

        String json = mapper.writeValueAsString(insertVo);
        Map<String, Object> hashMap = mapper.readValue(json, HashMap.class);

        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(hashMap, httpHeaders);

        String url = "http://localhost:" + serverPort + "/s/bill/sendconfig/create";
        return restTemplate.postForEntity(url, httpEntity, RespWrapVo.class);
    }

    private ResponseEntity<ExceptionResource> call_insert_api_exception(StaffBillSendConfigInsertVo insertVo)
            throws JsonProcessingException {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.parseMediaType(MediaType.APPLICATION_JSON_UTF8_VALUE));

        String json = mapper.writeValueAsString(insertVo);
        Map<String, Object> hashMap = mapper.readValue(json, HashMap.class);

        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(hashMap, httpHeaders);

        String url = "http://localhost:" + serverPort + "/s/bill/sendconfig/create";
        return restTemplate.postForEntity(url, httpEntity, ExceptionResource.class);
    }

    private List<BillEntity> buildBillDataSet() {
        List<BillEntity> billEntities = Arrays.asList(
                BillEntity.builder().tpEv01("Y").tpDct("D1").tpMcu("mcu1").tpUnit("unit1").tpDoco("123456")
                        .tpAn8("********").tpAlph("A开发公司").tpFyr(22).tpPn(1).formatDate("2022-01-05").delFlag("1")
                        .tpStatus(5).mailStatus(BillConstants.MSG_SUCCESS).emailStatus(BillConstants.MSG_NOT_SEND)
                        .mailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-05 20:23:58"))
                        .readStatus(0).tpGtfilenm("R1.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("D1").tpMcu("mcu2").tpUnit("unit2").tpDoco("123456")
                        .tpAn8("********").tpAlph("A开发公司").tpFyr(22).tpPn(2).formatDate("2022-02-04").delFlag("0")
                        .tpStatus(5).mailStatus(BillConstants.MSG_FAILURE).emailStatus(BillConstants.MSG_SUCCESS)
                        .mailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-02-04 10:11:22"))
                        .emailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-02-04 11:30:20"))
                        .emailErr("[{\"email\":\"<EMAIL>\",\"sendStatus\":\"发送成功\",\"sendTime\":\"2022-02-04 11:41:41\"}]")
                        .readStatus(0).tpGtfilenm("R2.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("D1").tpMcu("mcu2").tpUnit("unit4").tpDoco("123456")
                        .tpAn8("********").tpAlph("A开发公司").tpFyr(22).tpPn(1).formatDate("2022-01-07").delFlag("0")
                        .deleteBy("jane.dong")
                        .deleteTime(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-08 11:22:59"))
                        .tpStatus(BillConstants.MSG_NOT_SEND).mailStatus(BillConstants.MSG_NOT_SEND)
                        .emailStatus(BillConstants.MSG_NOT_SEND).readStatus(0).tpGtfilenm("R3.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("D2").tpMcu("mcu2").tpUnit("unit3").tpDoco("a********9b********9c********9d1")
                        .tpAn8("********").tpAlph("A开发公司").tpFyr(22).tpPn(1).formatDate("2022-01-08").delFlag("0")
                        .tpStatus(5).mailStatus(BillConstants.MSG_NOT_SEND).emailStatus(BillConstants.MSG_SUCCESS)
                        .emailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-08 16:10:20"))
                        .emailErr("[{\"email\":\"<EMAIL>\",\"sendStatus\":\"发送成功\",\"sendTime\":\"2022-01-08 16:31:41\"}]")
                        .readStatus(1).tpGtfilenm("R4.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("D2").tpMcu("mcu2").tpUnit("unit5").tpDoco("a********9b********9c********9d1")
                        .tpAn8("********").tpAlph("A开发公司").tpFyr(22).tpPn(1).formatDate("2022-01-18").delFlag("0")
                        .tpStatus(5).mailStatus(BillConstants.MSG_NOT_SEND).emailStatus(BillConstants.MSG_PARTIAL_SUCCESS)
                        .emailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-18 14:00:05"))
                        .emailErr("[{\"email\":\"<EMAIL>\",\"sendStatus\":\"发送成功\",\"sendTime\":\"2022-01-18 14:31:41\"}]")
                        .readStatus(0).tpGtfilenm("R5.PDF").build()
        );
        return billEntities;
    }

}
