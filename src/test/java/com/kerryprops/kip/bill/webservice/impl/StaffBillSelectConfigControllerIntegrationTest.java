package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.dao.BillSelectConfigRepository;
import com.kerryprops.kip.bill.dao.entity.BillSelectConfig;
import com.kerryprops.kip.bill.webservice.vo.req.BillSelectConfigAddDto;
import com.kerryprops.kip.bill.webservice.vo.req.BillSelectConfigPutDto;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;

import static com.kerryprops.kip.bill.utils.RandomUtil.randomObject;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * StaffBillSelectConfigControllerIntegrationTest.
 *
 * <AUTHOR> Yu
 **/
class StaffBillSelectConfigControllerIntegrationTest extends BaseIntegrationTest {

    private static final String BASE_URL = "/s/bill/select_configs";

    @Resource
    BillSelectConfigRepository billSelectConfigRepository;

    @Test
    @DisplayName("正常新增账单选择配置")
    void add_success() {
        BillSelectConfigAddDto addDto = randomObject(BillSelectConfigAddDto.class);

        var response = restTemplate.postForEntity(BASE_URL, addDto, BillSelectConfig.class);

        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getId());
    }

    @Test
    @DisplayName("检测配置是否存在-结果为false")
    void not_exist() {
        BillSelectConfig billSelectConfig = randomObject(BillSelectConfig.class);
        billSelectConfig.setProjectId("P101");
        billSelectConfig.setRoomId("Room1");
        billSelectConfig.setBuildingId("Building1");
        billSelectConfigRepository.save(billSelectConfig);
        String url = BASE_URL + "/exist?projectId=" + billSelectConfig.getProjectId();
        var response = restTemplate.getForEntity(url, StaffBillSelectConfigController.ExistVo.class);

        assertTrue(response.getStatusCode().is2xxSuccessful());
        var existVo = response.getBody();
        assertNotNull(existVo);
        assertThat(existVo.exist()).isFalse();
    }

    @Test
    @DisplayName("检测配置是否存在-结果为true")
    void exist_success() {
        BillSelectConfig billSelectConfig = randomObject(BillSelectConfig.class);
        billSelectConfig.setProjectId("P102");
        billSelectConfig.setRoomId(StringUtils.EMPTY);
        billSelectConfig.setBuildingId(StringUtils.EMPTY);
        billSelectConfigRepository.save(billSelectConfig);
        String url = BASE_URL + "/exist?projectId=" + billSelectConfig.getProjectId();

        var response = restTemplate.getForEntity(url, StaffBillSelectConfigController.ExistVo.class);

        assertTrue(response.getStatusCode().is2xxSuccessful());
        var existVo = response.getBody();
        assertNotNull(existVo);
        assertThat(existVo.exist()).isTrue();
    }

    @Test
    @DisplayName("分页获取账单选择配置列表-结果为空")
    void query_config_list_success() {
        var response = restTemplate.getForEntity(BASE_URL + "?projectId=1&page=0&size=10", String.class);

        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
    }

    @Test
    @DisplayName("成功更新账单选择配置")
    void update_success() {
        BillSelectConfigAddDto addDto = randomObject(BillSelectConfigAddDto.class);

        var addResponse = restTemplate.postForEntity(BASE_URL, addDto, BillSelectConfig.class);

        Long configId = addResponse.getBody().getId();

        var requestEntity = new HttpEntity<>(randomObject(BillSelectConfigPutDto.class));

        var response = restTemplate.exchange(BASE_URL + "/{id}", HttpMethod.PUT, requestEntity,
                BillSelectConfig.class, configId);

        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
    }

    @Test
    @DisplayName("成功删除账单选择配置")
    void delete_success() {
        BillSelectConfigAddDto addDto = randomObject(BillSelectConfigAddDto.class);
        var addResponse = restTemplate.postForEntity(BASE_URL, addDto, BillSelectConfig.class);

        Long configId = addResponse.getBody().getId();

        var response = restTemplate.exchange(BASE_URL + "/{id}", HttpMethod.DELETE, null,
                BillSelectConfig.class, configId);

        assertTrue(response.getStatusCode().is2xxSuccessful());
    }

}