package com.kerryprops.kip.bill.webservice;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.webservice.vo.HealthResource;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * HealthController单元测试类
 *
 * <AUTHOR>
 * @date 2024-10-17
 */
class HealthControllerTest extends BaseIntegrationTest {

    @Autowired
    private HealthController healthController;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Test
    @Disabled
    void _01_health_success() {
        HealthResource healthResourceActual = healthController.health();
        Assertions.assertEquals(HealthResource.FAIL_RESPONSE.getHealth(), healthResourceActual.getHealth());

        String randomKey = randomString();
        redisTemplate.opsForValue().set(randomKey, randomString());
        Assertions.assertEquals(Boolean.TRUE, redisTemplate.hasKey(randomKey));

    }

}
