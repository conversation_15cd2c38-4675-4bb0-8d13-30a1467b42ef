package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.exceptions.AppException;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.utils.DataScopeUtils;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.clients.HiveClient;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.IBillService;
import com.kerryprops.kip.bill.service.model.leg.Bill;
import com.kerryprops.kip.bill.service.model.leg.BillSelectVo;
import com.kerryprops.kip.bill.service.model.leg.BillUserSelectVo;
import com.kerryprops.kip.bill.service.model.s.BillSearchReqBo;
import com.kerryprops.kip.bill.service.model.s.BillSendHistoryReqDto;
import com.kerryprops.kip.bill.webservice.vo.req.BillSearchReqVo;
import com.kerryprops.kip.bill.webservice.vo.req.BillSendHistoryReqVo;
import com.kerryprops.kip.bill.webservice.vo.req.BillSendReqVo;
import com.kerryprops.kip.bill.webservice.vo.req.DocoBillPayer;
import com.kerryprops.kip.bill.webservice.vo.req.EmailResultVo;
import com.kerryprops.kip.bill.webservice.vo.resp.ExportStaffBillRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffBillHistoryRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffBillReceiverRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffBillRespVo;
import com.querydsl.core.types.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.kerryprops.kip.bill.common.enums.EmailStatus.LOCAL_SUCCESS;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * StaffBillControllerTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-04-23
 */
@DisplayName("B端账单Controller-单元测试")
@ExtendWith(MockitoExtension.class)
class StaffBillControllerTest {

    @Mock
    private IBillService billService;

    @Mock
    private HiveAsClient hiveAsClient;

    @Mock
    private HiveClient hiveService;

    @Mock
    private RedisTemplate<String, String> redisTemplate;

    @InjectMocks
    private StaffBillController staffBillController;


    @Test
    @DisplayName("查询电子账单列表及阅读情况，正常场景")
    void list_testBillSearchProcess() {
        // Arrange
        BillSearchReqVo reqVo = new BillSearchReqVo();
        reqVo.setBuildingIds(Arrays.asList("B001", "B002"));

        // 配置静态方法mock
        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = mockStatic(UserInfoUtils.class)) {
            // 模拟静态方法行为
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(generateLoginUser());

            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), eq(BillSearchReqBo.class)))
                                .thenReturn(new BillSearchReqBo());

            StaffBillRespVo staffBillRespVo1 = new StaffBillRespVo();
            staffBillRespVo1.setTpDoco("DOC-1");
            staffBillRespVo1.setTpAn8("43560001");
            staffBillRespVo1.setTpAlph("ALPHA-1");
            staffBillRespVo1.setTpMcu("MCU1,MCU2");
            staffBillRespVo1.setTpUnit("UNIT1,UNIT2");

            StaffBillRespVo staffBillRespVo2 = new StaffBillRespVo();
            staffBillRespVo2.setTpDoco("DOC-2");
            staffBillRespVo2.setTpAn8("43560002");
            staffBillRespVo2.setTpAlph("ALPHA-2");
            staffBillRespVo2.setTpMcu("MCU1,MCU2");
            staffBillRespVo2.setTpUnit("UNIT1,UNIT2");

            StaffBillRespVo staffBillRespVo3 = new StaffBillRespVo();
            staffBillRespVo3.setTpDoco(StringUtils.EMPTY); // doco/an8为空场景

            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(BillSelectVo.class), eq(StaffBillRespVo.class)))
                                .thenReturn(staffBillRespVo1, staffBillRespVo2);

            // 模拟服务层响应
            var billSelectVo1 = new BillSelectVo();
            var billSelectVo2 = new BillSelectVo();
            var billSelectVo3 = new BillSelectVo();

            when(billService.selectBillVoList(any(Pageable.class), any(Predicate.class))).thenReturn(
                    new PageImpl<>(List.of(billSelectVo1, billSelectVo2, billSelectVo3)));

            var docoBillPayer1 = DocoBillPayer.builder()
                                              .doco(staffBillRespVo1.getTpDoco())
                                              .an8(staffBillRespVo1.getTpAn8())
                                              .alph(staffBillRespVo1.getTpAlph())
                                              .build();
            var docoBillPayer2 = DocoBillPayer.builder()
                                              .doco(staffBillRespVo2.getTpDoco())
                                              .an8(staffBillRespVo2.getTpAn8())
                                              .alph(staffBillRespVo2.getTpAlph())
                                              .build();
            Set<StaffBillReceiverRespVo> staffBillReceiverSet = Set.of(new StaffBillReceiverRespVo());
            Map<DocoBillPayer, Set<StaffBillReceiverRespVo>> docoAn8SetMap =
                    Map.of(docoBillPayer1, staffBillReceiverSet, docoBillPayer2, staffBillReceiverSet);
            when(billService.selectBatchBillReceivers(anySet())).thenReturn(docoAn8SetMap);

            // 模拟hive服务响应
            var hiveResponse1 = new HiveClient.HiveBuBuildingResponse();
            hiveResponse1.setId("B001");
            hiveResponse1.setName("Building-1");

            var hiveResponse2 = new HiveClient.HiveBuBuildingResponse();
            hiveResponse2.setId("B002");
            hiveResponse2.setName("Building-2");
            List<HiveClient.HiveBuBuildingResponse> hiveResponses = List.of(hiveResponse1, hiveResponse2);

            when(hiveService.getBuildingByBuAndJdeRoomNo(anyList())).thenReturn(hiveResponses);

            // Act
            RespWrapVo<Page<StaffBillRespVo>> result = staffBillController.list(Pageable.ofSize(10), reqVo);

            // Assert
            assertNotNull(result.getData());
            assertEquals(3, result.getData()
                                  .getContent()
                                  .size());

            // 验证建筑信息填充
            result.getData()
                  .forEach(resp -> {
                      assertNotNull(resp.getBuildingId());
                      assertNotNull(resp.getBuildingName());
                  });
        }
    }


    @Test
    @DisplayName("根据楼栋模糊查询账单数据，正常场景")
    void fuzzyQueryByProjectId_success() {
        // Arrange
        try (MockedStatic<DataScopeUtils> mockedStaticDataScopeUtils = mockStatic(DataScopeUtils.class)) {
            mockedStaticDataScopeUtils.when(() -> DataScopeUtils.getBusByProjectId(any(), anyString()))
                                      .thenReturn(List.of("BU-1", "BU-2"));

            // Act and Assert
            String projectId = "192";
            assertDoesNotThrow(() -> staffBillController.fuzzyQueryDocos(projectId, "DOCO-1"));
            assertDoesNotThrow(() -> staffBillController.queryDcts(projectId));
            assertDoesNotThrow(() -> staffBillController.fuzzyQueryPayersByProjectId(projectId, "0", "alph-1"));
            assertDoesNotThrow(() -> staffBillController.queryDocos(projectId));

        }
    }

    @Test
    @DisplayName("导出账单数据，正常场景")
    void exportList_normalData_shouldGenerateExcel() {
        // Arrange
        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(generateLoginUser());

            // 准备测试数据
            var billSearchReqVo = new BillSearchReqVo();
            billSearchReqVo.setBuildingIds(List.of("B001", "B002"));

            var billSearchReqBo = new BillSearchReqBo();
            billSearchReqBo.setProjectId("192");
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(billSearchReqBo);

            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(Bill.class), eq(ExportStaffBillRespVo.class)))
                                .thenReturn(new ExportStaffBillRespVo());

            when(hiveAsClient.convertToJdeBus(anyList())).thenReturn(List.of("bu-1", "bu-2"));
            when(hiveAsClient.convertToJdeBus(any(String[].class))).thenReturn(List.of("bu-3", "bu-4"));


            // 模拟服务层返回数据
            when(billService.selectBillVoList(any(Predicate.class))).thenReturn(List.of(new Bill()));

            // Act and Assert
            assertThrows(NullPointerException.class, () -> staffBillController.exportList(billSearchReqVo, null));
        }
    }

    @Test
    @DisplayName("发送B端账单，空参数校验")
    void sendBillList_emptyBillIds_shouldReturnError() {
        // Arrange
        BillSendReqVo reqVo = new BillSendReqVo();

        // Act
        RespWrapVo<Integer> result = staffBillController.sendBillList(reqVo);

        // Assert
        assertEquals("200010", result.getCode());
        assertEquals("没有选择账单无法发送", result.getMessage());
    }

    @Test
    @DisplayName("发送B端账单，非法发送类型")
    void sendBillList_invalidSendType_shouldReturnError() {
        // Arrange
        BillSendReqVo reqVo = new BillSendReqVo();
        reqVo.setBillIds(Arrays.asList(1L, 2L));
        reqVo.setSendType(3);

        // Act
        RespWrapVo<Integer> result = staffBillController.sendBillList(reqVo);

        // Assert
        assertEquals("200015", result.getCode());
        assertEquals("发送类型选择错误", result.getMessage());
    }

    @Test
    @DisplayName("发送B端账单，并发操作检测")
    void sendBillList_concurrentOperation_shouldThrowException() {
        try (MockedStatic<UserInfoUtils> userUtils = mockStatic(UserInfoUtils.class)) {
            // Arrange
            LoginUser mockUser = generateLoginUser();

            userUtils.when(UserInfoUtils::getUser)
                     .thenReturn(mockUser);

            when(redisTemplate.hasKey(anyString())).thenReturn(true);

            BillSendReqVo reqVo = validRequest();

            // Act and Assert
            assertThrows(AppException.class, () -> staffBillController.sendBillList(reqVo));

        }
    }

    @Test
    @DisplayName("发送B端账单，无有效账单数据")
    void sendBillList_emptyBillList_shouldReturnZero() {
        try (MockedStatic<UserInfoUtils> userUtils = mockStatic(UserInfoUtils.class)) {
            userUtils.when(UserInfoUtils::getUser)
                     .thenReturn(generateLoginUser());

            when(redisTemplate.hasKey(anyString())).thenReturn(false);
            when(billService.selectBillVoList(any())).thenReturn(Collections.emptyList());
            BillSendReqVo reqVo = validRequest();

            // 执行测试
            RespWrapVo<Integer> result = staffBillController.sendBillList(reqVo);

            // 验证返回0
            assertEquals(0, result.getData()
                                  .intValue());
        }
    }

    @Test
    @DisplayName("发送B端账单，正常发送流程")
    void sendBillList_normalFlow_shouldReturnSuccess() {
        try (MockedStatic<UserInfoUtils> userUtils = mockStatic(UserInfoUtils.class)) {
            // Arrange
            userUtils.when(UserInfoUtils::getUser)
                     .thenReturn(generateLoginUser());

            when(redisTemplate.hasKey(anyString())).thenReturn(false);
            when(billService.selectBillVoList(any())).thenReturn(List.of(new Bill(), new Bill()));
            BillSendReqVo reqVo = validRequest();

            // Act
            RespWrapVo<Integer> result = staffBillController.sendBillList(reqVo);

            // Assert
            assertEquals(2, result.getData()
                                  .intValue());
        }
    }

    @Test
    @DisplayName("查询账单发送历史，正常场景")
    void sendHistory_testSendHistory_NormalCase() {
        // Arrange
        try (MockedStatic<BeanUtil> mockedBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {

            // Mock静态方法
            mockedUserInfo.when(UserInfoUtils::getUser)
                          .thenReturn(generateLoginUser());
            mockedBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                          .thenReturn(new StaffBillHistoryRespVo());

            // Mock依赖调用
            when(hiveAsClient.convertToJdeBus(anyList())).thenReturn(List.of("BUS1", "BUS2"));
            when(billService.searchPagedBill(any(Pageable.class), any(BillSendHistoryReqDto.class))).thenReturn(
                    new PageImpl<>(List.of(createBillUserSelectVo("DOC1"), createBillUserSelectVo("DOC2"))));

            // 构造请求参数
            BillSendHistoryReqVo reqVo = new BillSendHistoryReqVo();
            reqVo.setTpAlph("ALPH1");
            reqVo.setTpMcu("MCU1");
            reqVo.setTpDoco("DOCO1");
            reqVo.setTpDct("Dct1");
            reqVo.setTpFyr(2024);
            reqVo.setTpPn(12);
            reqVo.setBuildingIds(List.of("B1", "B2"));
            reqVo.setEmailDateStart(new Date(System.currentTimeMillis()));
            reqVo.setEmailDateEnd(new Date(System.currentTimeMillis()));

            // Act
            RespWrapVo<Page<StaffBillHistoryRespVo>> result =
                    staffBillController.sendHistory(Pageable.ofSize(10), reqVo);

            // Assert
            assertNotNull(result);
            assertEquals(2, result.getData()
                                  .getContent()
                                  .size());
            assertTrue(result.getData()
                             .getContent()
                             .stream()
                             .allMatch(vo -> vo.getTpAlph()
                                               .contains("-")));

            // 验证service调用
            ArgumentCaptor<BillSendHistoryReqDto> dtoCaptor = ArgumentCaptor.forClass(BillSendHistoryReqDto.class);
            verify(billService).searchPagedBill(any(Pageable.class), dtoCaptor.capture());
            assertEquals("BUS1,BUS2", dtoCaptor.getValue()
                                               .getBus());
        }
    }

    @Test
    @DisplayName("查询账单发送历史，正常场景")
    void sendHistory_testSendHistory_SuperAdminCase() {
        // Arrange
        try (MockedStatic<BeanUtil> mockedBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {

            // Mock静态方法
            mockedUserInfo.when(UserInfoUtils::getUser)
                          .thenReturn(generateLoginUser());
            mockedBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                          .thenReturn(new StaffBillHistoryRespVo());

            // Mock依赖调用
            when(hiveAsClient.convertToJdeBus(anyList())).thenReturn(List.of("BUS1", "BUS2"));
            when(billService.searchPagedBill(any(Pageable.class), any(BillSendHistoryReqDto.class))).thenReturn(
                    new PageImpl<>(List.of(createBillUserSelectVo("DOC1"), createBillUserSelectVo("DOC2"))));

            // 构造请求参数
            BillSendHistoryReqVo reqVo = new BillSendHistoryReqVo();
            reqVo.setTpAlph("ALPH1");
            reqVo.setTpMcu("MCU1");
            reqVo.setTpDoco("DOCO1");
            reqVo.setTpDct("Dct1");
            reqVo.setTpFyr(2024);
            reqVo.setTpPn(12);
            reqVo.setBuildingIds(List.of("B1", "B2"));
            reqVo.setEmailDateStart(new Date(System.currentTimeMillis()));
            reqVo.setEmailDateEnd(new Date(System.currentTimeMillis()));

            // Act
            RespWrapVo<Page<StaffBillHistoryRespVo>> result =
                    staffBillController.sendHistory(Pageable.ofSize(10), reqVo);

            // Assert
            assertNotNull(result);
            assertEquals(2, result.getData()
                                  .getContent()
                                  .size());
            assertTrue(result.getData()
                             .getContent()
                             .stream()
                             .allMatch(vo -> vo.getTpAlph()
                                               .contains("-")));

            // 验证service调用
            ArgumentCaptor<BillSendHistoryReqDto> dtoCaptor = ArgumentCaptor.forClass(BillSendHistoryReqDto.class);
            verify(billService).searchPagedBill(any(Pageable.class), dtoCaptor.capture());
            assertEquals("BUS1,BUS2", dtoCaptor.getValue()
                                               .getBus());
        }
    }

    @Test
    @DisplayName("查询账单发送历史，异常场景，用户无权限")
    void sendHistory_testSendHistory_userHaveNoBu() {
        // Arrange
        try (MockedStatic<BeanUtil> mockedBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {

            // Mock静态方法
            mockedUserInfo.when(UserInfoUtils::getUser)
                          .thenReturn(generateLoginUser());
            mockedBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                          .thenReturn(new StaffBillHistoryRespVo());

            // Mock依赖调用
            when(hiveAsClient.convertToJdeBus(anyList())).thenReturn(Collections.emptyList());

            // 构造请求参数
            BillSendHistoryReqVo reqVo = new BillSendHistoryReqVo();
            reqVo.setBuildingIds(List.of("B1", "B2"));

            // Act and Assert
            assertThrows(RuntimeException.class, () -> staffBillController.sendHistory(Pageable.ofSize(10), reqVo));
        }
    }

    @Test
    @DisplayName("查询账单发送历史，异常场景，未登录")
    void sendHistory_testSendHistory_notLogin() {
        // Arrange
        try (MockedStatic<BeanUtil> mockedBeanUtil = Mockito.mockStatic(BeanUtil.class)) {

            // Mock静态方法
            mockedBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                          .thenReturn(new StaffBillHistoryRespVo());

            // 构造请求参数
            BillSendHistoryReqVo reqVo = new BillSendHistoryReqVo();

            // Act and Assert
            assertThrows(RuntimeException.class, () -> staffBillController.sendHistory(Pageable.ofSize(10), reqVo));
        }
    }

    @Test
    @DisplayName("导出账单发送历史，正常场景")
    void exportHistory_testSendHistory_NormalCase() {
        // Arrange
        try (MockedStatic<BeanUtil> mockedBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {

            // Mock静态方法
            mockedUserInfo.when(UserInfoUtils::getUser)
                          .thenReturn(generateLoginUser());
            mockedBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                          .thenReturn(new StaffBillHistoryRespVo());

            // Mock依赖调用
            BillUserSelectVo billUserSelectVo1 = createBillUserSelectVo("DOC1");
            billUserSelectVo1.setEmailDate(null);
            BillUserSelectVo billUserSelectVo2 = createBillUserSelectVo("DOC2");
            billUserSelectVo2.setEmailDate(null);

            when(hiveAsClient.convertToJdeBus(anyList())).thenReturn(List.of("BUS1", "BUS2"));
            when(billService.searchPagedBill(any(BillSendHistoryReqDto.class))).thenReturn(
                    List.of(billUserSelectVo1, billUserSelectVo2));


            // 构造请求参数
            BillSendHistoryReqVo reqVo = new BillSendHistoryReqVo();
            reqVo.setBuildingIds(List.of("B1", "B2"));
            reqVo.setEmailDateStart(new Date(System.currentTimeMillis()));
            reqVo.setEmailDateEnd(new Date(System.currentTimeMillis()));

            // Act
            assertThrows(RuntimeException.class, () -> staffBillController.exportHistory(reqVo, null));

            // Assert
            ArgumentCaptor<BillSendHistoryReqDto> dtoCaptor = ArgumentCaptor.forClass(BillSendHistoryReqDto.class);
            verify(billService).searchPagedBill(dtoCaptor.capture());
            assertEquals("BUS1,BUS2", dtoCaptor.getValue()
                                               .getBus());
        }
    }

    @Test
    @DisplayName("获取电子账单详细信息，正常场景")
    void getInfo_success() {
        // Arrange
        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::getUser)
                          .thenReturn(generateLoginUser());

            when(hiveAsClient.convertToJdeBus(anyList())).thenReturn(List.of("BUS1", "BUS2"));

            // Act and Assert
            assertDoesNotThrow(() -> staffBillController.getInfo(123L));
        }
    }

    @Test
    @DisplayName("移除账单，正常场景")
    void deleteBill_success() {
        // Arrange
        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::getUser)
                          .thenReturn(generateLoginUser());

            when(hiveAsClient.convertToJdeBus(anyList())).thenReturn(List.of("BUS1", "BUS2"));

            // Act and Assert
            assertDoesNotThrow(() -> staffBillController.deleteBill(List.of(1L, 2L)));
        }
    }

    @Test
    @DisplayName("emailCallBack - 正常场景：返回 true")
    void emailCallBack_success() {
        // Arrange
        var emailResultVo = new EmailResultVo();
        emailResultVo.setRequestId("email123");
        emailResultVo.setSendStatus(LOCAL_SUCCESS);

        when(billService.emailCallBack(any(EmailResultVo.class))).thenReturn(true);

        // Act
        RespWrapVo<Boolean> result = staffBillController.emailCallBack(emailResultVo);

        // Assert
        assertNotNull(result);
        verify(billService, times(1)).emailCallBack(any(EmailResultVo.class));
    }

    private BillUserSelectVo createBillUserSelectVo(String doco) {
        BillUserSelectVo vo = new BillUserSelectVo();
        vo.setTpDoco(doco);
        vo.setEmailDate(new Date());
        return vo;
    }

    // 辅助方法：创建有效请求
    private BillSendReqVo validRequest() {
        var billSendReqVo = new BillSendReqVo();
        billSendReqVo.setBillIds(Arrays.asList(1001L, 1002L));
        billSendReqVo.setSendType(1);
        return billSendReqVo;
    }

    // 需测试的目标方法（示例结构）
    private LoginUser generateLoginUser() {
        return LoginUser.builder()
                        .nickName("testUser")
                        .fromType("C")
                        .cid("testCid")
                        .projectIds("BJKC")
                        .build();
    }
}