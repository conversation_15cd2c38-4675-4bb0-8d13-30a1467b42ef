package com.kerryprops.kip.bill.webservice;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.dao.BillSendConfigAn8LinkRepository;
import com.kerryprops.kip.bill.dao.BillSendConfigRepository;
import com.kerryprops.kip.bill.dao.entity.BillSendConfig;
import com.kerryprops.kip.bill.dao.entity.BillSendConfigAn8Link;
import com.kerryprops.kip.bill.service.IBillSendConfigService;
import com.kerryprops.kip.bill.webservice.vo.resp.BillSendConfigResource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.ZonedDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

class IBillSendConfigService_queryConfig_Test extends BaseIntegrationTest {

    @Autowired
    private IBillSendConfigService configService;

    @Autowired
    private BillSendConfigRepository configRepository;

    @Autowired
    private BillSendConfigAn8LinkRepository an8LinkRepository;

    @Disabled
    @Test
    void _01_normally_query() {
        List<BillSendConfig> oldConfigs = buildBillSendConfigs();
        List<BillSendConfig> newConfigs = configRepository.saveAll(oldConfigs);
        List<BillSendConfigAn8Link> oldAn8Links = buildBillSendConfigAn8Links(newConfigs);
        List<BillSendConfigAn8Link> newAn8Links = an8LinkRepository.saveAll(oldAn8Links);

        BillSendConfigResource configResource = configService.queryById(newConfigs.get(0).getId());
        assertNotNull(configResource);
        assertEquals(newConfigs.get(0).getId(), configResource.getId());
        assertNotNull(configResource.getAn8LinkList().get(0));
        assertEquals(newAn8Links.get(0).getId(), configResource.getAn8LinkList().get(0).getId());
        configRepository.deleteAll();
        an8LinkRepository.deleteAll();
    }

    @Disabled
    @Test
    void _02_empty() {
        List<BillSendConfig> oldConfigs = buildBillSendConfigs();
        List<BillSendConfig> newConfigs = configRepository.saveAll(oldConfigs);
        List<BillSendConfigAn8Link> oldAn8Links = buildBillSendConfigAn8Links(newConfigs);
        List<BillSendConfigAn8Link> newAn8Links = an8LinkRepository.saveAll(oldAn8Links);

        BillSendConfigResource configResource = configService.queryById(newConfigs.get(1).getId());
        assertNotNull(configResource);
        assertEquals(newConfigs.get(1).getId(), configResource.getId());
        assertTrue(CollectionUtils.isEmpty(configResource.getAn8LinkList()));
        configRepository.deleteAll();
        an8LinkRepository.deleteAll();
    }

    private List<BillSendConfig> buildBillSendConfigs() {
        return List.of(
                BillSendConfig.builder().doco("112233").email("<EMAIL>")
                        .phoneNumber(StringUtils.EMPTY).loginNo(StringUtils.EMPTY)
                        .mcu("mcu1").unit("unit1").isDel(0)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .build(),
                BillSendConfig.builder().doco("224411").email("<EMAIL>")
                        .phoneNumber("13764912345").loginNo("60939742")
                        .mcu("mcu1").unit("unit2").isDel(1)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .build(),
                BillSendConfig.builder().doco("112255").email("<EMAIL>")
                        .phoneNumber("13764912346").loginNo("12345678")
                        .mcu("mcu1").unit("unit2").isDel(0)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .build(),
                BillSendConfig.builder().doco("112244").email("<EMAIL>")
                        .phoneNumber("13764912345").loginNo("60939742")
                        .mcu("mcu1").unit("unit2").isDel(0)
                        .createdTime(ZonedDateTime.now()).updatedTime(ZonedDateTime.now())
                        .build());
    }


    private List<BillSendConfigAn8Link> buildBillSendConfigAn8Links(List<BillSendConfig> configs) {
        return List.of(
                BillSendConfigAn8Link.builder().configId(configs.get(0).getId()).isDel(0).alph("alph_1").an8("an8_1").build(),

                BillSendConfigAn8Link.builder().configId(configs.get(1).getId()).isDel(1).alph("用户名称2").an8("用户编号2").build(),
                BillSendConfigAn8Link.builder().configId(configs.get(1).getId()).isDel(1).alph("用户名称3").an8("用户编号3").build(),

                BillSendConfigAn8Link.builder().configId(configs.get(2).getId()).isDel(0).alph("alph_2").an8("an8_2").build(),

                BillSendConfigAn8Link.builder().configId(configs.get(3).getId()).isDel(0).alph("用户名称4").an8("用户编号4").build(),
                BillSendConfigAn8Link.builder().configId(configs.get(3).getId()).isDel(1).alph("用户名称1").an8("用户编号1").build()
        );
    }


}
