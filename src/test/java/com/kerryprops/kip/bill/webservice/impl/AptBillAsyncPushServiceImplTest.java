package com.kerryprops.kip.bill.webservice.impl;

import com.alibaba.fastjson.JSONObject;
import com.kerryprops.kip.bill.common.enums.BillPushStatus;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptBillOperator;
import com.kerryprops.kip.bill.feign.clients.BUserClient;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.entity.TenantStaffResponse;
import com.kerryprops.kip.bill.service.impl.AptBillPushServiceImpl;
import com.kerryprops.kip.hiveas.webservice.resource.resp.RoomResp;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;

@SuppressWarnings("unchecked")
@ExtendWith(MockitoExtension.class)
class AptBillAsyncPushServiceImplTest {

    private final String ROOM_ID = "123456";

    private final List<AptBill> APT_BILL_LIST = new ArrayList<>();

    private final String BASE_PATH = "templates/staff_apt_bill/";

    @Mock
    private HiveAsClient hiveAsClient;

    @Mock
    private BUserClient bUserClient;

    @InjectMocks
    private AptBillPushServiceImpl aptBillAsyncPushService;

    @Test
    void testHiveAsClient_nullResponse() throws Exception {
        RespWrapVo<RoomResp> roomRespRespWrapVo = null;
        given(hiveAsClient.getRoomById(any())).willReturn(roomRespRespWrapVo);

        BillPushStatus billPushStatus = aptBillAsyncPushService.pushOneRoomBill(ROOM_ID, APT_BILL_LIST, AptBillOperator.PUSH_ALL);
        Assertions.assertEquals(billPushStatus, BillPushStatus.PUSH_FAILED);
    }

    @ParameterizedTest
    @CsvSource({
            "HiveResponse_1_NoData.json,3",
            "HiveResponse_2_CodeFailed.json,3",
    })
    void testHiveAsClient_invalidResponse(String hiveFilename, int expectedCode)
            throws URISyntaxException, IOException {
        String hiveFilepath = BASE_PATH + hiveFilename;
        String hiveData = Files.readString(Path.of(getClass().getClassLoader().getResource(hiveFilepath).toURI()));
        RespWrapVo<RoomResp> roomRespRespWrapVo = JSONObject.parseObject(hiveData, RespWrapVo.class);
        given(hiveAsClient.getRoomById(any())).willReturn(roomRespRespWrapVo);

        BillPushStatus billPushStatus = aptBillAsyncPushService.pushOneRoomBill(ROOM_ID, APT_BILL_LIST, AptBillOperator.PUSH_ALL);
        Assertions.assertEquals(BillPushStatus.PUSH_FAILED, billPushStatus);
    }

    @ParameterizedTest
    @CsvSource({
            "HiveResponse_6_OK.json,,BUserClientResponse_4_OK.json,2",
            "HiveResponse_6_OK.json,BUserClientResponse_1_NoData.json,BUserClientResponse_4_OK.json,2",
            "HiveResponse_6_OK.json,BUserClientResponse_2_CodeFailed.json,BUserClientResponse_4_OK.json,2",
            "HiveResponse_6_OK.json,BUserClientResponse_3_ListIsEmpty.json,BUserClientResponse_4_OK.json,2",

            "HiveResponse_6_OK.json,BUserClientResponse_1_NoData.json,,4",
            "HiveResponse_6_OK.json,BUserClientResponse_1_NoData.json,BUserClientResponse_1_NoData.json,4",
            "HiveResponse_6_OK.json,BUserClientResponse_1_NoData.json,BUserClientResponse_2_CodeFailed.json,4",
            "HiveResponse_6_OK.json,BUserClientResponse_1_NoData.json,BUserClientResponse_3_ListIsEmpty.json,4",
    })
    void testBUserClient(String hiveFilename, String bUserFilename1,
                         String bUserFilename2, int expectedCode)
            throws URISyntaxException, IOException {
        String hiveFilepath = BASE_PATH + hiveFilename;
        String hiveData = Files.readString(Path.of(getClass().getClassLoader().getResource(hiveFilepath).toURI()));
        RoomResp roomResp = JSONObject.parseObject(hiveData, RoomResp.class);
        RespWrapVo<RoomResp> roomRespRespWrapVo = new RespWrapVo<>(roomResp);
        given(hiveAsClient.getRoomById(any())).willReturn(roomRespRespWrapVo);

        RespWrapVo<List<TenantStaffResponse>> tsr1 = buildTenantStaffResponse(bUserFilename1, false);
        RespWrapVo<List<TenantStaffResponse>> tsr2 = buildTenantStaffResponse(bUserFilename2, doParseTenantElement(bUserFilename2));
        given(bUserClient.getStaffList(any(), eq(1), any())).willReturn(tsr1);
        given(bUserClient.getStaffList(any(), eq(2), any())).willReturn(tsr2);

        BillPushStatus billPushStatus = aptBillAsyncPushService.pushOneRoomBill(ROOM_ID, APT_BILL_LIST, AptBillOperator.PUSH_ALL);
        Assertions.assertEquals(billPushStatus.getCode(), expectedCode);
    }

    @ParameterizedTest
    @CsvSource({
            "HiveResponse_3_ProjectIdIsNull.json,,3",
            "HiveResponse_4_BuildingIsNull.json,,3",
            "HiveResponse_5_RoomIsNull.json,, 3",
            "HiveResponse_6_OK.json,BUserClientResponse_4_OK.json,2",
    })
    void testHiveAsClient_invalidResponseContent(String hiveFilename, String bUserFilename, int expectedCode)
            throws URISyntaxException, IOException {
        String hiveFilepath = BASE_PATH + hiveFilename;
        String hiveData = Files.readString(Path.of(getClass().getClassLoader().getResource(hiveFilepath).toURI()));
        RoomResp roomResp = JSONObject.parseObject(hiveData, RoomResp.class);
        RespWrapVo<RoomResp> roomRespRespWrapVo = new RespWrapVo<>(roomResp);
        given(hiveAsClient.getRoomById(any())).willReturn(roomRespRespWrapVo);

        RespWrapVo<List<TenantStaffResponse>> tsr = buildTenantStaffResponse(bUserFilename, doParseTenantElement(bUserFilename));
        if (expectedCode == 2) {
            given(bUserClient.getStaffList(any(), eq(1), any())).willReturn(tsr);
        }

        BillPushStatus billPushStatus = aptBillAsyncPushService.pushOneRoomBill(ROOM_ID, APT_BILL_LIST, AptBillOperator.PUSH_ALL);
        Assertions.assertEquals(billPushStatus.getCode(), expectedCode);
    }

    private RespWrapVo<List<TenantStaffResponse>> buildTenantStaffResponse
            (String bUserFilename, boolean parseTenantElement) throws URISyntaxException, IOException {
        RespWrapVo<List<TenantStaffResponse>> tsr = null;
        if (StringUtils.isEmpty(bUserFilename)) {
            return tsr;
        }

        String bUserFilepath = BASE_PATH + bUserFilename;
        String bUserData = Files.readString(Path.of(getClass().getClassLoader().getResource(bUserFilepath).toURI()));
        if (parseTenantElement) {
            List<TenantStaffResponse> tenantStaffResponses = new LinkedList<>();
            tenantStaffResponses.add(JSONObject.parseObject(bUserData, TenantStaffResponse.class));
            tsr = new RespWrapVo<>(tenantStaffResponses);
        } else {
            tsr = JSONObject.parseObject(bUserData, RespWrapVo.class);
        }
        return tsr;
    }

    private boolean doParseTenantElement(String bUserFilename) {
        return "BUserClientResponse_4_OK.json".equalsIgnoreCase(bUserFilename);
    }

}
