package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.IOException;
import java.net.URISyntaxException;

import static org.junit.jupiter.api.Assertions.assertEquals;

@Slf4j
class API_sendHistory_StaffBillControllerTest extends BaseClass_StaffBillControllerTest {


    @Test
    void _01_user_not_login_() {
        ResponseEntity<RespWrapVo> resp = call_sendHistory_api(null, StringUtils.EMPTY);
        assertEquals(HttpStatus.OK, resp.getStatusCode());
        assertEquals("User not login.", resp.getBody().getMessage());
        assertEquals(RespCodeEnum.UNKNOWN_ERROR.getCode(), resp.getBody().getCode());
    }

    @ParameterizedTest
    @ValueSource(strings = {StringUtils.EMPTY, "\"buildingIds\":null,", "\"buildingIds\":\"\","})
    void _02_staff_login_invalid_building_() {
        String staffXUserJSON = "{\"buildingIds\":\"\",\"nickName\":\"JAKCAdmin\"," +
                "\"staff\":true,\"userId\":345,\"uniqueUserId\":\"345\",\"fromType\":\"S\",\"client\":false," +
                "\"superAdmin\":false,\"tenant\":false}";
        ResponseEntity<RespWrapVo> resp = call_sendHistory_api(staffXUserJSON, StringUtils.EMPTY);
        assertEquals(HttpStatus.OK, resp.getStatusCode());
        assertEquals("Invalid user.", resp.getBody().getMessage());
        assertEquals(RespCodeEnum.UNKNOWN_ERROR.getCode(), resp.getBody().getCode());
    }

    @ParameterizedTest
    @CsvSource({
            "true, P2-B1, ",
            "true, P2-B1;P3-B1,",
            "true, ,P2",
            "true, ,P1;P3",

            "false, P2-B1, ",
            "false, P2-B1;P3-B1,",
            "false, ,P2",
            "false, ,P1;P3",
    })
    void _03_combination_query_bu_(boolean isAdmin, String buildingIds, String projectIds)
            throws URISyntaxException, IOException {
        mockProjectId(projectIds);
        mockBuildingBU(isAdmin ? StringUtils.EMPTY : "P1-B1;P2-B1");
        mockBuildingBU(buildingIds);

        String xUserJson = isAdmin ? SUPER_ADMIN_JSON : STAFF_JSON;
        String param = "?1=1" +
                (StringUtils.isEmpty(buildingIds) ? StringUtils.EMPTY : "&buildingIds=" + convertSearchedId(buildingIds)) +
                (StringUtils.isEmpty(projectIds) ? StringUtils.EMPTY : "&projectId=" + convertSearchedId(projectIds));
        ResponseEntity<RespWrapVo> response = call_sendHistory_api(xUserJson, param);
    }

    private String buildStaffXUserJSON(String buildingInfo) {
        String staffXUserJSON = "{" +
                //"\"buildingIds\":\"\"," +
                (StringUtils.isEmpty(buildingInfo) ? StringUtils.EMPTY : buildingInfo) +
                "\"nickName\":\"JAKCAdmin\"," +
                "\"staff\":true,\"userId\":345,\"uniqueUserId\":\"345\",\"fromType\":\"S\",\"client\":false," +
                "\"superAdmin\":false,\"tenant\":false}";

        return staffXUserJSON;
    }

    private ResponseEntity<RespWrapVo> call_sendHistory_api(String xUserJson, String urlParam) {
        // headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("x-user", xUserJson);

        HttpEntity request = new HttpEntity<>(null, headers);
        String url = "http://localhost:" + serverPort + "/s/bill/history" + urlParam;

        return restTemplate.exchange(url, HttpMethod.GET, request, RespWrapVo.class);
    }

}
