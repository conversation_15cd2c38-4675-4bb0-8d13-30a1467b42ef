package com.kerryprops.kip.bill.webservice.scheduler;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.webservice.impl.EFapiaoSyncBillController;
import com.kerryprops.kip.bill.webservice.impl.StaffAptPayController;
import com.kerryprops.kip.bill.webservice.impl.StaffBillSendConfigController;
import com.kerryprops.kip.bill.webservice.impl.StaffSyncAptBillController;
import com.kerryprops.kip.bill.webservice.impl.StaffSyncBillController;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import static org.mockito.Mockito.doReturn;

/**
 * SyncJdeBillScheduler单元测试类
 *
 * <AUTHOR>
 * @date 2024-10-16
 */
class SyncJdeBillSchedulerTest extends BaseIntegrationTest {

    @Mock
    private StaffSyncBillController syncBillController;

    @Mock
    private StaffSyncAptBillController staffSyncAptBillController;

    @Mock
    private StaffAptPayController staffAptPayController;

    @Mock
    private StaffBillSendConfigController staffBillSendConfigController;

    @Mock
    private EFapiaoSyncBillController eFapiaoSyncBillController;

    @InjectMocks
    private SyncJdeBillScheduler syncJdeBillScheduler;

    @Test
    void _01_scheduleBBill_success() {
        doReturn(new RespWrapVo<Boolean>()).when(syncBillController).sync();

        // 执行待验证部分
        Assertions.assertDoesNotThrow(() -> syncJdeBillScheduler.scheduleBBill());
    }

    @Test
    void _02_fixEmailStatus_success() {
        doReturn(new RespWrapVo<Boolean>()).when(syncBillController).syncBillEmailStatus(ArgumentMatchers.any());

        // 执行待验证部分
        Assertions.assertDoesNotThrow(() -> syncJdeBillScheduler.fixEmailStatus());
    }

    @Test
    void _03_scheduleEFapiaoBBill_success() {
        doReturn(Boolean.TRUE).when(eFapiaoSyncBillController)
                .sync(null, true);

        // 执行待验证部分
        Assertions.assertDoesNotThrow(() -> syncJdeBillScheduler.scheduleEFapiaoBBill());
    }

    @Test
    void _04_scheduleCBill_success() {
        doReturn(new RespWrapVo<Boolean>()).when(staffSyncAptBillController).sync(null);

        // 执行待验证部分
        Assertions.assertDoesNotThrow(() -> syncJdeBillScheduler.scheduleCBill());
    }

    @Test
    void _05_scheduleCBillHive_success() {
        doReturn(new RespWrapVo<Boolean>()).when(staffSyncAptBillController).syncHive();

        // 执行待验证部分
        Assertions.assertDoesNotThrow(() -> syncJdeBillScheduler.scheduleCBillHive());
    }

    @Test
    void _06_scheduleWriterPaytoJde_success() {
        doReturn(new RespWrapVo<String>()).when(staffAptPayController).writeBack(ArgumentMatchers.any());

        // 执行待验证部分
        Assertions.assertDoesNotThrow(() -> syncJdeBillScheduler.scheduleWriterPaytoJde());
    }

    @Test
    void _07_confirmFromJde_success() {
        doReturn(new RespWrapVo<String>()).when(staffAptPayController).autoConfirmJdePayStatus(ArgumentMatchers.any());

        // 执行待验证部分
        Assertions.assertDoesNotThrow(() -> syncJdeBillScheduler.confirmFromJde());
    }

    @Test
    void _08_syncBillSendConfigMcus_success() {
        doReturn(new RespWrapVo<Boolean>()).when(staffBillSendConfigController).syncMcuFromTenantBills();

        // 执行待验证部分
        Assertions.assertDoesNotThrow(() -> syncJdeBillScheduler.syncBillSendConfigMcus());
    }

    @Test
    void _09_syncBillSendConfigEnterpriseAccounts_success() {
        doReturn(new RespWrapVo<Boolean>()).when(staffBillSendConfigController).syncEnterpriseAccounts();

        // 执行待验证部分
        Assertions.assertDoesNotThrow(() -> syncJdeBillScheduler.syncBillSendConfigEnterpriseAccounts());
    }

}