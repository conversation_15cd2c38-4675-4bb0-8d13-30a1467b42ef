package com.kerryprops.kip.bill.webservice.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kerryprops.kip.bill.dao.entity.BillSendConfig;
import com.kerryprops.kip.bill.dao.entity.BillSendConfigAn8Link;
import com.kerryprops.kip.bill.webservice.vo.resp.BillSendConfigResource;
import com.kerryprops.kip.hiveas.feign.dto.resp.BuildingRespDto;
import com.kerryprops.kip.hiveas.webservice.resource.resp.ProjectResp;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doReturn;

class API_list_StaffBillSendConfigControllerTest extends BaseClass_StaffBillSendConfigControllerTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    private List<BillSendConfig> configList;

    private List<BillSendConfigAn8Link> an8LinkList;

    protected void verify(ResponseEntity<ListResp> response,
                          List<String> expectConfigIdList, List<String> expectAn8IdList) {
        assertEquals(HttpStatus.OK, response.getStatusCode());

        ListResp pages = response.getBody();
        assertEquals(pages.getTotalElements(), expectConfigIdList.size());
        assertEquals(pages.getTotalPages(), CollectionUtils.isEmpty(expectConfigIdList) ? 0 : 1);

        Set<String> actualIdList = new HashSet<>();
        Set<String> actualAn8IdList = new HashSet<>();
        pages.getContent().forEach(stringObjectMap -> {
            actualIdList.add(String.valueOf(stringObjectMap.getId()));

            List<BillSendConfigAn8Link> an8LinkList = stringObjectMap.getAn8LinkList();
            an8LinkList.stream().forEach(an8Map -> {
                actualAn8IdList.add(String.valueOf(an8Map.getId()));
            });
        });
        assertTrue(expectConfigIdList.stream().sorted().collect(Collectors.joining())
                .equals(actualIdList.stream().sorted().collect(Collectors.joining())));
        assertTrue(expectAn8IdList.stream().sorted().collect(Collectors.joining())
                .equals(actualAn8IdList.stream().sorted().collect(Collectors.joining())));
    }

    @Test
    void _01_list_all_active_items() {
        ResponseEntity<ListResp> responseEntity = call_list_api(0, 20, StringUtils.EMPTY);

        List<String> expectConfigIdList = Lists.list(0, 1, 3).stream()
                .map(index -> String.valueOf(configList.get(index).getId())).collect(Collectors.toList());
        List<String> expectAn8IdList = Lists.list(0, 1, 2, 4).stream()
                .map(index -> String.valueOf(an8LinkList.get(index).getId())).collect(Collectors.toList());
        verify(responseEntity, expectConfigIdList, expectAn8IdList);
    }

    @Test
    void _02_list_filter_doco() {
        String urlParam = "&doco=112233";
        ResponseEntity<ListResp> responseEntity = call_list_api(0, 20, urlParam);

        List<String> expectConfigIdList = Lists.list(String.valueOf(configList.get(1).getId()));
        List<String> expectAn8IdList = Lists.list(1, 2).stream()
                .map(index -> String.valueOf(an8LinkList.get(index).getId())).collect(Collectors.toList());
        verify(responseEntity, expectConfigIdList, expectAn8IdList);
    }

    @Test
    void _03_list_filter_unit() {
        String urlParam = "&unit=unit2";
        ResponseEntity<ListResp> responseEntity = call_list_api(0, 20, urlParam);

        List<String> expectConfigIdList = Lists.list(0, 1).stream()
                .map(index -> String.valueOf(configList.get(index).getId())).collect(Collectors.toList());
        List<String> expectAn8IdList = Lists.list(0, 1, 2).stream()
                .map(index -> String.valueOf(an8LinkList.get(index).getId())).collect(Collectors.toList());
        verify(responseEntity, expectConfigIdList, expectAn8IdList);
    }

    @Test
    void _04_list_filter_alph() {
        String urlParam = "&alph=alph";
        ResponseEntity<ListResp> responseEntity = call_list_api(0, 20, urlParam);

        List<String> expectConfigIdList = Lists.list(String.valueOf(configList.get(3).getId()));
        List<String> expectAn8IdList = Lists.list(String.valueOf(an8LinkList.get(4).getId()));
        verify(responseEntity, expectConfigIdList, expectAn8IdList);
    }

    @Test
    void _05_list_filter_an8() {
        String urlParam = "&an8=3355";
        ResponseEntity<ListResp> responseEntity = call_list_api(0, 20, urlParam);

        List<String> expectConfigIdList = Lists.list(1).stream()
                .map(index -> String.valueOf(configList.get(index).getId())).collect(Collectors.toList());
        List<String> expectAn8IdList = Lists.list(1, 2).stream()
                .map(index -> String.valueOf(an8LinkList.get(index).getId())).collect(Collectors.toList());
        verify(responseEntity, expectConfigIdList, expectAn8IdList);
    }

    @Test
    void _06_list_filter_mcu() {
        String urlParam = "&mcu=mcu3";
        ResponseEntity<ListResp> responseEntity = call_list_api(0, 20, urlParam);

        List<String> expectConfigIdList = Lists.list(0, 3).stream()
                .map(index -> String.valueOf(configList.get(index).getId())).collect(Collectors.toList());
        List<String> expectAn8IdList = Lists.list(0, 4).stream()
                .map(index -> String.valueOf(an8LinkList.get(index).getId())).collect(Collectors.toList());
        verify(responseEntity, expectConfigIdList, expectAn8IdList);
    }

    @BeforeEach
    private void prepareTestData() {
        List<BillSendConfig> requestConfigList = buildBillSendConfigList();
        configList = configRepository.saveAll(requestConfigList);

        List<BillSendConfigAn8Link> requestAn8List = buildBillSendConfigAn8Links(configList);
        an8LinkList = an8LinkRepository.saveAll(requestAn8List);

        ProjectResp projectResp = new ProjectResp();
        List<BuildingRespDto> buildings = Lists.newArrayList();
        projectResp.setBuildings(buildings);
        BuildingRespDto bdto = new BuildingRespDto();
        buildings.add(bdto);
        String mcuStr = StringUtils.join(configList.stream().map(BillSendConfig::getMcu)
                .flatMap(mcus -> Arrays.stream(StringUtils.split(mcus, ",")))
                .collect(Collectors.toList()), ",");
        bdto.setPropertyDeveloperBU(mcuStr);

        doReturn(projectResp).when(hiveAsClient).getProjectByID(ArgumentMatchers.any());
    }

    @AfterEach
    private void clearTestData() {
        configRepository.deleteAll(configList);
        an8LinkRepository.deleteAll(an8LinkList);
    }


    private ResponseEntity<ListResp> call_list_api(int page, int size, String urlParam) {
        final String X_USER = "{\"buildingIds\":\"\",\"nickName\":\"JAKCAdmin\"," +
                "\"staff\":true,\"userId\":345,\"uniqueUserId\":\"345\",\"fromType\":\"S\",\"client\":false," +
                "\"superAdmin\":false,\"tenant\":false}";

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.set("x-user", X_USER);

        HttpEntity request = new HttpEntity<>(null, httpHeaders);

        String data = "?page=" + String.valueOf(page) + "&size=" + String.valueOf(size) + "&sort=doco,asc&sort=createdTime,desc&projectId=xx";
        String url = "http://localhost:" + serverPort + "/s/bill/sendconfig/list" + data + urlParam;
        return restTemplate.exchange(url, HttpMethod.GET, request, ListResp.class);
    }

    @Data
    private static class ListResp {

        private List<BillSendConfigResource> content;

        private int totalPages;

        private int totalElements;

        private int size;

        private int number;

    }

}
