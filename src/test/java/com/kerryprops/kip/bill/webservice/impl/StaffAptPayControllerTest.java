package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.enums.AptSyncJobStatus;
import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import com.kerryprops.kip.bill.common.enums.PaymentCateEnum;
import com.kerryprops.kip.bill.common.enums.PaymentPayType;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.utils.jde.OracleSql;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptJdeBillRepository;
import com.kerryprops.kip.bill.dao.AptPayBillRepository;
import com.kerryprops.kip.bill.dao.AptPayConfigRepository;
import com.kerryprops.kip.bill.dao.AptPayRepository;
import com.kerryprops.kip.bill.dao.AptPaymentBillRepository;
import com.kerryprops.kip.bill.dao.AptPaymentInfoRepository;
import com.kerryprops.kip.bill.dao.AptSyncJdeJobLogRepository;
import com.kerryprops.kip.bill.dao.AptSyncJdeJobRepository;
import com.kerryprops.kip.bill.dao.AptSyncPaidBillToJdeRepository;
import com.kerryprops.kip.bill.dao.AptSyncPayToJdeRepository;
import com.kerryprops.kip.bill.dao.BillCommonSettingRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptJdeBill;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPayBill;
import com.kerryprops.kip.bill.dao.entity.AptPayConfig;
import com.kerryprops.kip.bill.dao.entity.AptPaymentBill;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.dao.entity.AptSyncJdeJob;
import com.kerryprops.kip.bill.dao.entity.AptSyncJdeJobLog;
import com.kerryprops.kip.bill.dao.entity.AptSyncPaidBillToJde;
import com.kerryprops.kip.bill.dao.entity.BillCommonSetting;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.clients.MessageClient;
import com.kerryprops.kip.bill.feign.entity.EmailSendCommand;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.AptBillDirectDebitsBatchBillService;
import com.kerryprops.kip.bill.service.AptBillOperationService;
import com.kerryprops.kip.bill.service.AptSyncPayStatusService;
import com.kerryprops.kip.bill.service.impl.AptBillService;
import com.kerryprops.kip.bill.service.model.AptPaymentTimestamp;
import com.kerryprops.kip.bill.service.model.JdeConfirmResult;
import com.kerryprops.kip.bill.service.model.s.AptPayBo;
import com.kerryprops.kip.bill.webservice.vo.req.AptPaySaveVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptPaySearchVo;
import com.kerryprops.kip.bill.webservice.vo.resp.AptPayDetailVo;
import com.kerryprops.kip.bill.webservice.vo.resp.AptPayExportVo;
import com.kerryprops.kip.bill.webservice.vo.resp.RoomAn8RespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffAptBillRespVo;
import com.kerryprops.kip.hiveas.webservice.resource.resp.RoomResp;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.EntityPath;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.data.domain.PageRequest;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.random.RandomGenerator;

import static com.kerryprops.kip.bill.common.enums.AptPayVerifyStatus.TO_BE_VERIFIED;
import static com.kerryprops.kip.bill.common.enums.AptPayVerifyStatus.VERIFIED;
import static com.kerryprops.kip.bill.common.enums.AptSyncJobStatus.PROCESSING;
import static com.kerryprops.kip.bill.common.enums.BillCommonSettingEnum.APT_JDE_PAYMENT_TIMESTAMP;
import static com.kerryprops.kip.bill.common.enums.BillPayChannel.ONLINE;
import static com.kerryprops.kip.bill.common.enums.BillPaymentStatus.CANCEL;
import static com.kerryprops.kip.bill.common.enums.BillPaymentStatus.PAID;
import static com.kerryprops.kip.bill.common.enums.BillStatus.ONLINE_PAID;
import static com.kerryprops.kip.bill.common.enums.PayCancelTypeEnum.ADMIN_CANCELLED;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomLoginUser;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * StaffAptPayControllerTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-04-16
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("C端支付-单元测试")
class StaffAptPayControllerTest {

    private static final String PROJECT_ID = "192";

    @Mock
    private MessageClient messageClient;

    @Mock
    private HiveAsClient hiveAsClient;

    @Mock
    private AptPayRepository aptPayRepository;

    @Mock
    private AptPaymentInfoRepository aptPaymentInfoRepository;

    @Mock
    private AptBillRepository aptBillRepository;

    @Mock
    private AptPayConfigRepository payConfigRepository;

    @Mock
    private AptSyncJdeJobRepository syncJdeJobRepository;

    @Mock
    private AptPayBillRepository payBillRepository;

    @Mock
    private AptPaymentBillRepository aptPaymentBillRepository;

    @Mock
    private AptJdeBillRepository jdeBillRepository;

    @Mock
    private AptSyncPayToJdeRepository syncJdeLogRepository;

    @Mock
    private AptSyncJdeJobLogRepository syncJdeJobLogRepository;

    @Mock
    private AptBillDirectDebitsBatchBillService batchBillService;

    @Mock
    private AptBillService aptBillService;

    @Mock
    private BillCommonSettingRepository billCommonSettingRepository;

    @Mock
    private AptBillRepository billRepository;

    @Mock
    private AptSyncPayStatusService aptSyncPayStatusService;

    @Mock
    private AptBillOperationService operationService;

    @Mock
    private AptSyncPaidBillToJdeRepository paidBillToJdeRepository;

    @InjectMocks
    private StaffAptPayController staffAptPayController;

    @Test
    @DisplayName("支付状态回写JDE：异常场景，支付账单为空")
    void writeBack_aptPayEmpty_ShouldReturn() {
        // Arrange
        mockProjectInfo();

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class);
             MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new AptSyncJdeJobLog());

            // Act and Assert
            Assertions.assertDoesNotThrow(() -> staffAptPayController.writeBack(PROJECT_ID));
        }
    }

    @Test
    @DisplayName("支付状态回写JDE：线下支付场景，正常场景")
    void writeBack_offline_pay_success() {
        // Arrange
        mockProjectInfo();

        var aptPay1 = new AptPay();
        aptPay1.setId(1L);
        aptPay1.setAn8("12345678");
        aptPay1.setPayDate(new Date());
        aptPay1.setPayChannel(BillPayChannel.OFFLINE);
        aptPay1.setPaymentInfoId("paymentInfoId-1");
        aptPay1.setPayConfigId(3L);
        aptPay1.setComments("comments-1");
        aptPay1.setAdvanceAmount(BigDecimal.ZERO);
        aptPay1.setTaxAmt(0.03d);

        var aptPay2 = new AptPay();
        aptPay2.setId(2L);
        aptPay2.setAn8("87654321");
        aptPay2.setPayDate(new Date());
        aptPay2.setPayChannel(BillPayChannel.OFFLINE);
        aptPay2.setPaymentInfoId("paymentInfoId-2");
        aptPay2.setPayConfigId(4L);
        aptPay2.setComments("comments-2");
        aptPay2.setAdvanceAmount(BigDecimal.valueOf(2.0));
        aptPay2.setComments("comments-2");
        aptPay2.setTaxAmt(0);
        when(aptPayRepository.findAll(any(Predicate.class))).thenReturn(List.of(aptPay1, aptPay2));

        AptPayConfig payConfig = new AptPayConfig();
        payConfig.setId(1L);
        payConfig.setBankAccount("bankAccount-*********");
        when(payConfigRepository.findById(any(Long.class))).thenReturn(Optional.of(payConfig));


        var aptPaymentBill = new AptPaymentBill();
        aptPaymentBill.setBillId(1L);
        when(aptPaymentBillRepository.findAllByPaymentInfoIdAndDeleted(any(String.class), any())).thenReturn(
                List.of(aptPaymentBill));

        final int NOT_DELETED = 0;
        AptBill aptBill1 = new AptBill();
        aptBill1.setDeletedAt(NOT_DELETED);
        aptBill1.setBillNo("billNo-1");

        AptBill aptBill2 = new AptBill();
        aptBill2.setDeletedAt(NOT_DELETED);
        aptBill2.setBillNo("billNo-2");
        when(aptBillRepository.findByIdIn(any(List.class))).thenReturn(List.of(aptBill1, aptBill2));

        AptJdeBill aptJdeBill1 = new AptJdeBill();
        AptJdeBill aptJdeBill2 = new AptJdeBill();
        when(jdeBillRepository.findAll(any(Predicate.class))).thenReturn(List.of(aptJdeBill1), List.of(aptJdeBill2));

        // mock返回值即入参
        when(syncJdeLogRepository.saveAll(anyList())).thenAnswer(invocation -> invocation.getArgument(0));

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class);
             MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<OracleSql> mockedStaticOracleSql = Mockito.mockStatic(OracleSql.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new AptSyncJdeJobLog());
            mockedStaticOracleSql.when(() -> OracleSql.getInsertResult(anyString()))
                                 .thenReturn(Boolean.TRUE, Boolean.TRUE, Boolean.TRUE, Boolean.TRUE, Boolean.FALSE);

            // Act
            staffAptPayController.writeBack(PROJECT_ID);

            // Assert
            verify(syncJdeJobRepository, times(3)).save(any());
            verify(syncJdeJobRepository, times(3)).save(any());

        }
    }

    @Test
    @DisplayName("支付状态回写JDE：线上支付场景，正常场景")
    void writeBack_online_pay_success() {
        // Arrange
        mockProjectInfo();
        mockOnlineAptPay();

        AptPayConfig payConfig = new AptPayConfig();
        payConfig.setId(2L);
        payConfig.setBankAccount("bankAccount-********");
        when(payConfigRepository.findById(any(Long.class))).thenReturn(Optional.of(payConfig));

        var aptPayBill = new AptPayBill();
        aptPayBill.setBillNo("billNo-1");
        when(payBillRepository.findAll(any(Predicate.class))).thenReturn(List.of(aptPayBill));

        var aptJdeBill = new AptJdeBill();
        aptJdeBill.setRdAg(200.0d);
        when(jdeBillRepository.findAll(any(Predicate.class))).thenReturn(List.of(aptJdeBill, aptJdeBill));

        var aptBill = new AptBill();
        aptBill.setBillNo("billNo-1");
        when(batchBillService.getAptBillsByPaymentOrderNo(any(String.class))).thenReturn(List.of(aptBill));

        // mock返回值即入参
        when(syncJdeLogRepository.saveAll(anyList())).thenAnswer(invocation -> invocation.getArgument(0));

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class);
             MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<OracleSql> mockedStaticOracleSql = Mockito.mockStatic(OracleSql.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new AptSyncJdeJobLog());
            mockedStaticOracleSql.when(() -> OracleSql.getInsertResult(anyString()))
                                 .thenReturn(Boolean.TRUE, Boolean.TRUE, Boolean.TRUE, Boolean.TRUE, Boolean.FALSE);

            // Act
            staffAptPayController.writeBack(PROJECT_ID);

            // Assert
            verify(syncJdeJobRepository, times(3)).save(any());
            verify(syncJdeJobRepository, times(3)).save(any());
        }
    }

    @Test
    @DisplayName("支付状态回写JDE：线上支付场景，异常场景，支付金额错误")
    void writeBack_online_pay_error() {
        // Arrange
        mockProjectInfo();
        mockOnlineAptPay();

        AptPayConfig payConfig = new AptPayConfig();
        payConfig.setId(2L);
        payConfig.setBankAccount("bankAccount-********");
        when(payConfigRepository.findById(any(Long.class))).thenReturn(Optional.of(payConfig));

        var aptPayBill = new AptPayBill();
        aptPayBill.setBillNo("billNo-1");
        when(payBillRepository.findAll(any(Predicate.class))).thenReturn(List.of(aptPayBill));

        var aptJdeBill = new AptJdeBill();
        aptJdeBill.setRdAg(400.0d); // 总金额mock为错误，本应该为200
        when(jdeBillRepository.findAll(any(Predicate.class))).thenReturn(List.of(aptJdeBill, aptJdeBill));

        var aptBill = new AptBill();
        aptBill.setBillNo("billNo-1");
        when(batchBillService.getAptBillsByPaymentOrderNo(any(String.class))).thenReturn(List.of(aptBill));

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class);
             MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new AptSyncJdeJobLog());

            // Act
            staffAptPayController.writeBack(PROJECT_ID);

            // Assert
            verify(messageClient, times(2)).sendWithReplyAlicloud(any(EmailSendCommand.class));
        }
    }

    @Test
    @DisplayName("线下支付账单 匹配到KIP：异常场景：bu为空")
    void autoConfirmJdePayStatus_whenBuStringEmpty_shouldThrowException() {
        // arrange
        when(aptBillService.buildHiveBuString(any())).thenReturn(null);

        // Act
        Exception exception =
                assertThrows(RuntimeException.class, () -> staffAptPayController.autoConfirmJdePayStatus(false));

        // Assert
        assertTrue(StringUtils.isNotEmpty(exception.getMessage()));
    }

    @Test
    @DisplayName("线下支付账单 匹配到KIP：异常场景：JDE账单为空")
    void autoConfirmJdePayStatus_whenAptJdeBillEmpty_shouldThrowException() {
        // arrange
        when(aptBillService.buildHiveBuString(any())).thenReturn("test_bu");
        when(billCommonSettingRepository.findAll(any(BooleanExpression.class))).thenReturn(Collections.emptyList());

        JdeConfirmResult mockResult = new JdeConfirmResult();
        mockResult.setAptJdeBills(Collections.emptyList());
        mockResult.setTimestamp(new AptPaymentTimestamp());

        when(aptSyncPayStatusService.confirmAllPaidJdeItems(anyString())).thenReturn(mockResult);
        when(aptSyncPayStatusService.confirmAllUnpaidJdeItems(anyString())).thenReturn(mockResult);

        // Act
        RespWrapVo<String> response = staffAptPayController.autoConfirmJdePayStatus(false);

        // Assert
        assertTrue(StringUtils.isNotEmpty(response.getData()));
    }

    @Test
    @DisplayName("线下支付账单 匹配到KIP：正常场景，时间戳不存在，则全量匹配")
    void autoConfirmJdePayStatus_whenFirstExecution_shouldDoFullVerification() {
        // Arrange
        when(aptBillService.buildHiveBuString(any())).thenReturn("test_bu");
        when(billCommonSettingRepository.findAll(any(BooleanExpression.class))).thenReturn(Collections.emptyList());

        var aptBill = new AptBill();
        aptBill.setStatus(ONLINE_PAID);
        when(billRepository.findOne(any(Predicate.class))).thenReturn(Optional.of(aptBill));

        Mockito.when(billRepository.save(Mockito.any(AptBill.class)))
               .thenAnswer(invocation -> invocation.getArgument(0));

        int jdeVerificationYes = 1;
        int jdeVerificationNo = 0;

        var aptJdeBill1 = new AptJdeBill();
        aptJdeBill1.setBillNumber("billNumber-1");
        aptJdeBill1.setRdAg(RandomGenerator.getDefault()
                                           .nextDouble());
        aptJdeBill1.setJdeVerification(jdeVerificationYes);

        var aptJdeBill2 = new AptJdeBill();
        aptJdeBill2.setBillNumber("billNumber-2");
        aptJdeBill2.setRdAg(RandomGenerator.getDefault()
                                           .nextDouble());
        aptJdeBill2.setJdeVerification(jdeVerificationNo);

        var aptJdeBill3 = new AptJdeBill();
        aptJdeBill3.setBillNumber("billNumber-34");
        aptJdeBill3.setRdAg(RandomGenerator.getDefault()
                                           .nextDouble());
        aptJdeBill3.setJdeVerification(jdeVerificationYes);

        var aptJdeBill4 = new AptJdeBill();
        aptJdeBill4.setBillNumber("billNumber-34");
        aptJdeBill4.setRdAg(RandomGenerator.getDefault()
                                           .nextDouble());
        aptJdeBill4.setJdeVerification(jdeVerificationNo);

        var aptPaymentTimestamp = new AptPaymentTimestamp();

        JdeConfirmResult mockResult1 = new JdeConfirmResult();
        mockResult1.setTimestamp(aptPaymentTimestamp);
        mockResult1.setAptJdeBills(List.of(aptJdeBill1));
        JdeConfirmResult mockResult2 = new JdeConfirmResult();
        mockResult2.setTimestamp(aptPaymentTimestamp);
        mockResult2.setAptJdeBills(List.of(aptJdeBill2, aptJdeBill3));

        when(aptSyncPayStatusService.confirmAllPaidJdeItems(anyString())).thenReturn(mockResult1);
        when(aptSyncPayStatusService.confirmAllUnpaidJdeItems(anyString())).thenReturn(mockResult2);

        // Act
        RespWrapVo<String> response = staffAptPayController.autoConfirmJdePayStatus(false);

        // Assert
        verify(aptSyncPayStatusService).confirmAllPaidJdeItems("test_bu");
        verify(aptSyncPayStatusService).confirmAllUnpaidJdeItems("test_bu");
        assertTrue(StringUtils.isNotEmpty(response.getData()));
    }

    @Test
    @DisplayName("线下支付账单 匹配到KIP：正常场景，间戳存在，则增量匹配")
    void autoConfirmJdePayStatus_whenAutoFalseAndSettingExists_shouldDoIncremental() {
        // Arrange
        when(aptBillService.buildHiveBuString(any())).thenReturn("test_bu");

        BillCommonSetting existingSetting = new BillCommonSetting();
        existingSetting.setContent("{\"timestamp\":\"2024-01-01\"}");
        existingSetting.setType(APT_JDE_PAYMENT_TIMESTAMP.getCode());
        when(billCommonSettingRepository.findAll(any(Predicate.class))).thenReturn(List.of(existingSetting));

        int jdeVerificationYes = 1;
        var aptJdeBill = new AptJdeBill();
        aptJdeBill.setBillNumber("billNumber-1");
        aptJdeBill.setRdAg(RandomGenerator.getDefault()
                                          .nextDouble());
        aptJdeBill.setJdeVerification(jdeVerificationYes);

        JdeConfirmResult mockResult = new JdeConfirmResult();
        mockResult.setAptJdeBills(List.of(aptJdeBill));
        mockResult.setTimestamp(new AptPaymentTimestamp());
        when(aptSyncPayStatusService.confirmIncrementalJdeItems(anyString(), any())).thenReturn(mockResult);

        var aptBill = new AptBill();
        aptBill.setStatus(ONLINE_PAID);
        when(billRepository.findOne(any(Predicate.class))).thenReturn(Optional.of(aptBill));

        Mockito.when(billRepository.save(Mockito.any(AptBill.class)))
               .thenAnswer(invocation -> invocation.getArgument(0));

        // Act
        staffAptPayController.autoConfirmJdePayStatus(true);

        // Assert
        verify(aptSyncPayStatusService, never()).confirmAllPaidJdeItems(anyString());
        verify(aptSyncPayStatusService, times(1)).confirmIncrementalJdeItems(anyString(), any());
    }

    @Test
    @DisplayName("线上支付账单匹配到JDE，未创建syncJdeJob")
    void confirmToJde_syncJdeJobNotFoundError() {
        // Arrange
        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());

            // Act and Assert
            assertThrows(RuntimeException.class, () -> staffAptPayController.confirmToJde());
        }
    }

    @Test
    @DisplayName("线上支付账单匹配到JDE，AptPayBill账单为空")
    void confirmToJde_aptPayBillEmptyError() {
        // Arrange
        mockProjectInfo();

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class);
             MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new AptSyncJdeJobLog());

            // Act and Assert
            assertDoesNotThrow(() -> staffAptPayController.confirmToJde());
        }
    }


    @Test
    @DisplayName("线上支付账单匹配到JDE，正常场景")
    void confirmToJde_SyncSuccess() {
        // Arrange
        mockProjectInfo();

        // 1. 模拟查询到待处理的支付账单
        List<AptPayBill> payBills =
                List.of(new AptPayBill(1001L, 2001L, "billNo1", 0), new AptPayBill(1002L, 2002L, "billNo1", 0));
        when(payBillRepository.findAllByConfirmed(AptSyncJobStatus.CREATED.getStatus())).thenReturn(payBills);

        // 2. 模拟所有记录处理成功
        var aptBill = new AptBill();
        aptBill.setBillNo("billNo-1");
        when(billRepository.findById(anyLong())).thenReturn(Optional.of(aptBill));

        var aptPay = new AptPay();
        aptPay.setPayConfigId(1L);
        aptPay.setPayDate(new Date());
        when(aptPayRepository.findById(anyLong())).thenReturn(Optional.of(aptPay));

        when(payConfigRepository.findById(anyLong())).thenReturn(Optional.of(new AptPayConfig()));
        when(jdeBillRepository.findAllByBillNumber(anyString())).thenReturn(List.of(new AptJdeBill()));
        when(paidBillToJdeRepository.save(any())).thenAnswer(invocation -> invocation.getArgument(0));

        var aptPayConfig = new AptPayConfig();
        aptPayConfig.setTax(RandomGenerator.getDefault()
                                           .nextDouble());
        aptPayConfig.setMax(RandomGenerator.getDefault()
                                           .nextDouble());
        when(payConfigRepository.findById(anyLong())).thenReturn(Optional.of(aptPayConfig));

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class);
             MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<OracleSql> mockedStaticOracleSql = Mockito.mockStatic(OracleSql.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new AptSyncJdeJobLog());
            mockedStaticOracleSql.when(() -> OracleSql.getInsertResult(anyString()))
                                 .thenReturn(Boolean.TRUE);

            // Act
            staffAptPayController.confirmToJde();

            // Assert
            verify(paidBillToJdeRepository, times(4)).save(any(AptSyncPaidBillToJde.class));
        }
    }

    @Test
    @DisplayName("线上支付账单匹配到JDE，异常场景：数据库异常")
    void confirmToJde_database_error() {
        // Arrange
        mockProjectInfo();

        // 1. 模拟查询到待处理的支付账单
        List<AptPayBill> payBills =
                List.of(new AptPayBill(1001L, 2001L, "billNo1", 0), new AptPayBill(1002L, 2002L, "billNo1", 0));
        when(payBillRepository.findAllByConfirmed(AptSyncJobStatus.CREATED.getStatus())).thenReturn(payBills);

        // 2. 模拟所有记录处理成功
        var aptBill = new AptBill();
        aptBill.setBillNo("billNo-1");
        when(billRepository.findById(anyLong())).thenReturn(Optional.of(aptBill));

        var aptPay = new AptPay();
        aptPay.setPayConfigId(1L);
        aptPay.setPayDate(new Date());
        when(aptPayRepository.findById(anyLong())).thenReturn(Optional.of(aptPay));

        when(payConfigRepository.findById(anyLong())).thenReturn(Optional.of(new AptPayConfig()));
        when(jdeBillRepository.findAllByBillNumber(anyString())).thenReturn(List.of(new AptJdeBill()));
        when(paidBillToJdeRepository.save(any())).thenAnswer(invocation -> invocation.getArgument(0));

        var aptPayConfig = new AptPayConfig();
        aptPayConfig.setTax(RandomGenerator.getDefault()
                                           .nextDouble());
        aptPayConfig.setMax(RandomGenerator.getDefault()
                                           .nextDouble());
        when(payConfigRepository.findById(anyLong())).thenReturn(Optional.of(aptPayConfig));

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class);
             MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<OracleSql> mockedStaticOracleSql = Mockito.mockStatic(OracleSql.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new AptSyncJdeJobLog());
            mockedStaticOracleSql.when(() -> OracleSql.getInsertResult(anyString()))
                                 .thenReturn(Boolean.FALSE);

            // Act
            staffAptPayController.confirmToJde();

            // Assert
            var aptPayBillCaptor = ArgumentCaptor.forClass(AptPayBill.class);
            verify(payBillRepository, times(4)).save(aptPayBillCaptor.capture());
            assertEquals(AptSyncJobStatus.FAILED.getStatus(), aptPayBillCaptor.getValue()
                                                                              .getConfirmed());
        }
    }

    @Test
    @DisplayName("导出账单，正常场景")
    void exportSuccess() {
        // Arrange
        var aptPay = new AptPay();
        aptPay.setPayChannel(ONLINE);
        when(aptPayRepository.findAll(any(Predicate.class))).thenReturn(List.of(aptPay));

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class);
             MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class)) {

            var aptPayExportVo = new AptPayExportVo();
            aptPayExportVo.setPaymentCate(PaymentCateEnum.A004.name());
            aptPayExportVo.setPayType(PaymentPayType.WX_DIRECT_DEBIT.getInfo());
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new AptPayBo(), aptPayExportVo);
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());

            // Act
            assertThrows(NullPointerException.class, () -> staffAptPayController.export(new AptPaySearchVo(), null));
        }
    }

    @Test
    @DisplayName("查询历史住户姓名列表，线下收款页面使用，正常场景")
    void roomAn8_success() {
        // Arrange
        Tuple tuple = Mockito.mock(Tuple.class);
        when(tuple.get(any(Integer.class), any())).thenReturn("BU_001");

        JPAQueryFactory jpaQueryFactory = Mockito.mock(JPAQueryFactory.class);
        JPAQuery jpaQueryForQuery = Mockito.mock(JPAQuery.class);
        JPAQuery jpaQueryForSelect = Mockito.mock(JPAQuery.class);
        JPAQuery jpaQueryBaseForFrom = Mockito.mock(JPAQuery.class);
        JPAQuery queryBaseForWhere = Mockito.mock(JPAQuery.class);
        JPAQuery abstractJPAQueryForGroupBy = Mockito.mock(JPAQuery.class);

        when(billRepository.getJpaQueryFactory()).thenReturn(jpaQueryFactory);
        when(jpaQueryFactory.query()).thenReturn(jpaQueryForQuery);
        when(jpaQueryForQuery.select(any(Expression[].class))).thenReturn(jpaQueryForSelect);
        when(jpaQueryForSelect.from(any(EntityPath.class))).thenReturn(jpaQueryBaseForFrom);
        when(jpaQueryBaseForFrom.where(any(Predicate.class))).thenReturn(queryBaseForWhere);
        when(queryBaseForWhere.groupBy(any(Expression[].class))).thenReturn(abstractJPAQueryForGroupBy);
        when(abstractJPAQueryForGroupBy.fetch()).thenReturn(List.of(tuple));

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());

            // Act
            RespWrapVo<List<RoomAn8RespVo>> respWrapVo =
                    staffAptPayController.roomAn8(PageRequest.of(10, 1), "roomId-1");

            // Assert
            assertTrue(CollectionUtils.isNotEmpty(respWrapVo.getData()));
        }
    }

    @Test
    @DisplayName("详情查询接口，异常场景：查找AptPay不存在时抛出异常")
    @MockitoSettings(strictness = Strictness.LENIENT)
    void detail_whenAptPayNotFound_throwException() {
        when(aptPayRepository.findOne(any(Predicate.class))).thenReturn(Optional.empty());

        RuntimeException ex = assertThrows(RuntimeException.class, () -> staffAptPayController.detail(1L));
        assertTrue(StringUtils.isNotEmpty(ex.getMessage()));
    }

    @Test
    @DisplayName("详情查询接口，正常场景：支付渠道为直接借记且账单存在")
    void detail_whenDirectDebitChannel_shouldCallBatchService() {
        // 模拟AptPay存在且渠道为DIRECT_DEBITS
        AptPay mockPay = AptPay.builder()
                               .payChannel(BillPayChannel.DIRECT_DEBITS)
                               .paymentInfoId("100")
                               .build();
        when(aptPayRepository.findOne(any(Predicate.class))).thenReturn(Optional.of(mockPay));

        // 模拟batchBillService返回账单
        List<AptBill> mockBills = List.of(new AptBill(), new AptBill());
        when(batchBillService.getAptBillsByPaymentOrderNo(anyString())).thenReturn(mockBills);

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new AptPayDetailVo(), new StaffAptBillRespVo(), new StaffAptBillRespVo());
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());


            // Act
            RespWrapVo<AptPayDetailVo> result = staffAptPayController.detail(1L);

            // Asset
            assertEquals(2, result.getData()
                                  .getBills()
                                  .size());
            verify(batchBillService).getAptBillsByPaymentOrderNo(anyString());
        }
    }

    @Test
    @DisplayName("详情查询接口，正常场景：非直接借记渠道且关联账单为空")
    void detail_whenNonDirectDebitAndNoPayBills_returnEmptyBills() {
        // 模拟AptPay存在且渠道非直接借记
        AptPay mockPay = AptPay.builder()
                               .payChannel(ONLINE)
                               .paymentInfoId("100")
                               .build();
        when(aptPayRepository.findOne(any(Predicate.class))).thenReturn(Optional.of(mockPay));

        // 模拟payBillRepository返回空列表
        when(payBillRepository.findAllByPayId(1L)).thenReturn(Collections.emptyList());

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class);) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new AptPayDetailVo(), new StaffAptBillRespVo(), new StaffAptBillRespVo());
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());


            // Act
            RespWrapVo<AptPayDetailVo> result = staffAptPayController.detail(1L);

            // Assert
            assertNull(result.getData()
                             .getBills()); // 或根据具体实现断言
            verify(billRepository, never()).findAllById(anyList());
        }

    }

    @Test
    @DisplayName("详情查询接口，正常场景：常获取关联账单")
    void whenPayBillsExist_shouldReturnConvertedBills() {
        // 模拟AptPay存在且渠道非直接借记
        AptPay mockPay = AptPay.builder()
                               .payChannel(ONLINE)
                               .paymentInfoId("100")
                               .build();
        when(aptPayRepository.findOne(any(Predicate.class))).thenReturn(Optional.of(mockPay));

        // 模拟payBillRepository返回关联关系
        List<AptPayBill> payBills = Arrays.asList(new AptPayBill(), new AptPayBill());
        when(payBillRepository.findAllByPayId(anyLong())).thenReturn(payBills);

        // 模拟billRepository返回真实账单
        List<AptBill> mockBills = Arrays.asList(new AptBill(), new AptBill());
        when(billRepository.findAllById(anyList())).thenReturn(mockBills);

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class);) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new AptPayDetailVo(), new StaffAptBillRespVo(), new StaffAptBillRespVo());
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());

            // Act
            RespWrapVo<AptPayDetailVo> result = staffAptPayController.detail(1L);

            // Assert
            assertEquals(2, result.getData()
                                  .getBills()
                                  .size());
        }
    }


    @Test
    @DisplayName("异常场景：RoomResp不存在")
    void save_RoomRespNotFound() {
        // Arrange
        AptPaySaveVo saveReqVo = new AptPaySaveVo();
        saveReqVo.setPayConfigId(1L);

        when(hiveAsClient.getRoomById(any())).thenReturn(new RespWrapVo<>());

        // Act & Assert
        assertThrows(RuntimeException.class, () -> staffAptPayController.save(saveReqVo));
    }

    @Test
    @DisplayName("异常场景：payConfig不存在")
    void save_payConfigNotFound() {
        // Arrange
        AptPaySaveVo saveReqVo = new AptPaySaveVo();
        saveReqVo.setPayConfigId(1L);

        Optional mockOptional = mock(Optional.class);
        when(mockOptional.orElse(any())).thenReturn(null);
        when(payConfigRepository.findById(1L)).thenReturn(mockOptional);

        when(hiveAsClient.getRoomById(any())).thenReturn(new RespWrapVo<>(new RoomResp()));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> staffAptPayController.save(saveReqVo));
    }

    @Test
    @DisplayName("异常场景：payConfig状态不合法")
    void save_payConfigInvalid() {
        // Arrange
        AptPaySaveVo saveReqVo = new AptPaySaveVo();
        saveReqVo.setPayConfigId(1L);

        AptPayConfig payConfig = new AptPayConfig();
        payConfig.setChannel(BillPayChannel.ONLINE);

        Optional mockOptional = mock(Optional.class);
        when(mockOptional.orElse(any())).thenReturn(payConfig);
        when(payConfigRepository.findById(1L)).thenReturn(mockOptional);

        when(hiveAsClient.getRoomById(any())).thenReturn(new RespWrapVo<>(new RoomResp()));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> staffAptPayController.save(saveReqVo));
    }

    @Test
    @DisplayName("查询支付类别：正常场景")
    void queryPaymentCate_success() {
        // Act
        RespWrapVo<List<Map<String, String>>> result = staffAptPayController.queryPaymentCate();

        // Assert
        assertNotNull(result);
    }

    @Test
    @DisplayName("正常场景：目标数据未审核，正常审核")
    void verify_success() {
        // Arrange
        Long id = 1L;
        AptPay aptPay = new AptPay();
        aptPay.setId(id);
        aptPay.setVerifyStatus(TO_BE_VERIFIED);

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class);) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());

            when(aptPayRepository.findOne(any(Predicate.class))).thenReturn(Optional.of(aptPay));

            // Act
            RespWrapVo<Boolean> result = staffAptPayController.verify(id);

            // Assert
            assertEquals(true, result.getData());
            assertEquals(VERIFIED, aptPay.getVerifyStatus());
            verify(aptPayRepository, times(1)).save(aptPay);
        }
    }

    @Test
    @DisplayName("异常场景：目标数据不存在")
    void verify_notFound() {
        // Arrange
        Long id = 3L;
        when(aptPayRepository.findOne(any(Predicate.class))).thenReturn(Optional.empty());

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class);) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());
            // Act & Assert
            assertThrows(RuntimeException.class, () -> staffAptPayController.verify(id));
        }
    }

    @Test
    @DisplayName("switchVerifyById - 正常场景：状态切换成功")
    void switchVerifyById_success() {
        // Arrange
        Long id = 1L;
        AptPay aptPay = new AptPay();
        aptPay.setId(id);
        aptPay.setVerifyStatus(TO_BE_VERIFIED);

        when(aptPayRepository.findOne(any(Predicate.class))).thenReturn(Optional.of(aptPay));

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = mockStatic(UserInfoUtils.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser).thenReturn(randomLoginUser());

            // Act
            Boolean result = staffAptPayController.switchVerifyById(id);

            // Assert
            assertTrue(result);
            assertEquals(VERIFIED, aptPay.getVerifyStatus());
            verify(aptPayRepository, times(1)).save(aptPay);
        }
    }

    @Test
    @DisplayName("switchVerifyById - 异常场景：目标数据未创建")
    void switchVerifyById_notCreated() {
        // Arrange
        Long id = 1L;
        AptPay aptPay = new AptPay();
        aptPay.setId(id);
        aptPay.setVerifyStatus(TO_BE_VERIFIED);
        aptPay.setSendJdeStatus(PROCESSING.getStatus());

        when(aptPayRepository.findOne(any(Predicate.class))).thenReturn(Optional.of(aptPay));

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = mockStatic(UserInfoUtils.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());
            // Act & Assert
            assertThrows(RuntimeException.class, () -> staffAptPayController.switchVerifyById(id));
        }
    }

    @Test
    @DisplayName("deleteOfflinePay - 正常场景：删除线下支付成功")
    void deleteOfflinePay_success() {
        // Arrange
        Long id = 1L;
        AptPay aptPay = new AptPay();
        aptPay.setId(id);
        aptPay.setPaymentInfoId("paymentInfoId-1");

        var aptPaymentInfo = new AptPaymentInfo();
        aptPaymentInfo.setId("paymentInfoId-1");
        aptPaymentInfo.setPaymentStatus(PAID);

        when(aptPayRepository.findOne(any(Predicate.class))).thenReturn(Optional.of(aptPay));
        when(aptPaymentInfoRepository.findTopById(anyString())).thenReturn(aptPaymentInfo);

        when(aptBillRepository.queryAptBillByPaymentId(any())).thenReturn(List.of(new AptBill()));
        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = mockStatic(UserInfoUtils.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser).thenReturn(randomLoginUser());

            // Act
            RespWrapVo<Boolean> result = staffAptPayController.deleteOfflinePay(id);

            // Assert
            assertTrue(result.getData());
            assertEquals(CANCEL, aptPaymentInfo.getPaymentStatus());
            assertEquals(ADMIN_CANCELLED.name(), aptPaymentInfo.getCancelType());
            verify(aptPayRepository, times(1)).save(aptPay);
            verify(aptPaymentInfoRepository, times(1)).save(aptPaymentInfo);
        }
    }

    @Test
    @DisplayName("deleteOfflinePay - 异常场景：目标数据不存在")
    void deleteOfflinePay_notFound() {
        // Arrange
        Long id = 2L;
        when(aptPayRepository.findOne(any(Predicate.class))).thenReturn(Optional.empty());

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = mockStatic(UserInfoUtils.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());
            // Act & Assert
            assertThrows(RuntimeException.class, () -> staffAptPayController.deleteOfflinePay(id));
        }
    }

    @Test
    @DisplayName("deleteOfflinePay - 异常场景：支付id为空")
    void deleteOfflinePay_paymentInfoIdEmpty() {
        // Arrange
        // Arrange
        Long id = 3L;
        AptPay aptPay = new AptPay();
        aptPay.setId(id);
        aptPay.setPaymentInfoId(null);

        var aptPaymentInfo = new AptPaymentInfo();
        aptPaymentInfo.setId("paymentInfoId-1");
        aptPaymentInfo.setPaymentStatus(PAID);

        when(aptPayRepository.findOne(any(Predicate.class))).thenReturn(Optional.of(aptPay));

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = mockStatic(UserInfoUtils.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());
            // Act & Assert
            assertThrows(RuntimeException.class, () -> staffAptPayController.deleteOfflinePay(id));
        }
    }

    @Test
    @DisplayName("deleteOfflinePay - 异常场景：支付记录未找到")
    void deleteOfflinePay_paymentInfoNotFound() {
        // Arrange
        Long id = 3L;
        AptPay aptPay = new AptPay();
        aptPay.setId(id);
        aptPay.setPaymentInfoId("paymentInfoId-1");

        when(aptPayRepository.findOne(any(Predicate.class))).thenReturn(Optional.of(aptPay));
        when(aptPaymentInfoRepository.findTopById(anyString())).thenReturn(null);

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = mockStatic(UserInfoUtils.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());
            // Act & Assert
            assertThrows(RuntimeException.class, () -> staffAptPayController.deleteOfflinePay(id));
        }
    }

    @Test
    @DisplayName("正常场景：所有依赖数据存在，方法执行成功")
    void writerLocal_success() {
        // Arrange
        AptPayBill payBill = new AptPayBill();
        payBill.setBillId(1L);
        payBill.setPayId(2L);

        AptBill aptBill = new AptBill();
        aptBill.setBillNo("BILL123");
        aptBill.setAmt(100.0d);

        AptPay aptPay = new AptPay();
        aptPay.setPayConfigId(3L);
        aptPay.setPayDate(new Date());

        AptPayConfig aptPayConfig = new AptPayConfig();
        aptPayConfig.setMax(100.0d);
        aptPayConfig.setTax(100.0d);

        when(billRepository.findById(1L)).thenReturn(Optional.of(aptBill));
        when(aptPayRepository.findById(2L)).thenReturn(Optional.of(aptPay));
        when(payConfigRepository.findById(3L)).thenReturn(Optional.of(aptPayConfig));
        when(jdeBillRepository.findAllByBillNumber(any())).thenReturn(List.of(new AptJdeBill()));

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = mockStatic(BeanUtil.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new AptSyncPaidBillToJde());

            // Act
            ReflectionTestUtils.invokeMethod(staffAptPayController, "writerLocal", payBill, 100L);

            // Assert
            verify(billRepository, times(1)).findById(1L);
            verify(aptPayRepository, times(1)).findById(2L);
            verify(payConfigRepository, times(1)).findById(3L);
        }
    }

    @Test
    @DisplayName("异常场景：AptBill 数据不存在")
    void writerLocal_billNotFound() {
        // Arrange
        AptPayBill payBill = new AptPayBill();
        payBill.setBillId(1L);

        when(billRepository.findById(1L)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(RuntimeException.class, () -> ReflectionTestUtils.invokeMethod(staffAptPayController, "writerLocal", payBill, 100L));
    }

    @Test
    @DisplayName("异常场景：AptPay 数据不存在")
    void writerLocal_payNotFound() {
        // Arrange
        AptPayBill payBill = new AptPayBill();
        payBill.setBillId(1L);
        payBill.setPayId(2L);

        AptBill aptBill = new AptBill();
        aptBill.setBillNo("BILL123");

        when(billRepository.findById(1L)).thenReturn(Optional.of(aptBill));
        when(aptPayRepository.findById(2L)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(RuntimeException.class, () -> ReflectionTestUtils.invokeMethod(staffAptPayController, "writerLocal", payBill, 100L));
        verify(billRepository, times(1)).findById(1L);
    }

    @Test
    @DisplayName("异常场景：AptPayConfig 数据不存在")
    void writerLocal_payConfigNotFound() {
        // Arrange
        AptPayBill payBill = new AptPayBill();
        payBill.setBillId(1L);
        payBill.setPayId(2L);

        AptBill aptBill = new AptBill();
        aptBill.setBillNo("BILL123");

        AptPay aptPay = new AptPay();
        aptPay.setPayConfigId(3L);

        when(billRepository.findById(1L)).thenReturn(Optional.of(aptBill));
        when(aptPayRepository.findById(2L)).thenReturn(Optional.of(aptPay));
        when(payConfigRepository.findById(3L)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(RuntimeException.class, () -> ReflectionTestUtils.invokeMethod(staffAptPayController, "writerLocal", payBill, 100L));
        verify(billRepository, times(1)).findById(1L);
        verify(aptPayRepository, times(1)).findById(2L);
    }

    @Test
    @DisplayName("异常场景：AptPayConfig 数据不存在")
    void writerLocal_aptJdeBillNotFound() {
        // Arrange
        AptPayBill payBill = new AptPayBill();
        payBill.setBillId(1L);
        payBill.setPayId(2L);

        AptBill aptBill = new AptBill();
        aptBill.setBillNo("BILL123");
        aptBill.setAmt(100.0d);

        AptPay aptPay = new AptPay();
        aptPay.setPayConfigId(3L);
        aptPay.setPayDate(new Date());

        AptPayConfig aptPayConfig = new AptPayConfig();
        aptPayConfig.setMax(100.0d);
        aptPayConfig.setTax(100.0d);

        when(billRepository.findById(1L)).thenReturn(Optional.of(aptBill));
        when(aptPayRepository.findById(2L)).thenReturn(Optional.of(aptPay));
        when(payConfigRepository.findById(3L)).thenReturn(Optional.of(aptPayConfig));
        when(jdeBillRepository.findAllByBillNumber(any())).thenReturn(Collections.emptyList());

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = mockStatic(BeanUtil.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new AptSyncPaidBillToJde());

            /// Act & Assert
            assertThrows(RuntimeException.class, () -> ReflectionTestUtils.invokeMethod(staffAptPayController, "writerLocal", payBill, 100L));
            verify(billRepository, times(1)).findById(1L);
            verify(aptPayRepository, times(1)).findById(2L);
            verify(payConfigRepository, times(1)).findById(3L);
        }
    }

    private void mockProjectInfo() {
        AptSyncJdeJob mockJob = new AptSyncJdeJob();
        mockJob.setId(1L);
        mockJob.setLocalCnt(5);
        mockJob.setRemoteCnt(5);
        when(syncJdeJobRepository.save(any())).thenReturn(mockJob);
    }

    private void mockOnlineAptPay() {
        var aptPay1 = new AptPay();
        aptPay1.setId(1L);
        aptPay1.setAn8("********");
        aptPay1.setPayDate(new Date());
        aptPay1.setPayChannel(ONLINE);
        aptPay1.setPaymentInfoId("paymentInfoId-3");
        aptPay1.setPayConfigId(5L);
        aptPay1.setComments("comments-1");
        aptPay1.setAdvanceAmount(BigDecimal.ZERO);
        aptPay1.setTotalAmt(4.0d);
        aptPay1.setTaxAmt(0.03d);

        var aptPay2 = new AptPay();
        aptPay2.setId(2L);
        aptPay2.setAn8("33334444");
        aptPay2.setPayDate(new Date());
        aptPay2.setPayChannel(BillPayChannel.DIRECT_DEBITS);
        aptPay2.setPaymentInfoId("paymentInfoId-4");
        aptPay2.setPayConfigId(6L);
        aptPay2.setAdvanceAmount(BigDecimal.ZERO);
        aptPay2.setTotalAmt(4.0d);
        aptPay2.setComments("comments-2");
        aptPay2.setTaxAmt(0);
        when(aptPayRepository.findAll(any(Predicate.class))).thenReturn(List.of(aptPay1, aptPay2));
    }
}