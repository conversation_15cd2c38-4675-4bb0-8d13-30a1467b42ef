package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.service.IBillService;
import com.kerryprops.kip.bill.webservice.vo.resp.BillUnreadInfoVo;
import com.kerryprops.kip.bill.webservice.vo.resp.ContentUnreadInfoVo;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.Date;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * BizBillControllerTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @since - 2025-04-16
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("业务账单Controller-单元测试")
class BizBillControllerTest {

    @Mock
    private IBillService billService;

    @InjectMocks
    private BizBillController bizBillController;

    @Test
    @DisplayName("updateReadTime - 正常场景：更新阅读时间")
    void updateReadTime_success() {
        // Arrange
        Long id = 1L;
        int updatedCount = 1;
        when(billService.updateReadTime(id)).thenReturn(updatedCount);

        // Act
        RespWrapVo<Integer> result = bizBillController.updateReadTime(id);

        // Assert
        assertNotNull(result);
        assertEquals(updatedCount, result.getData());
        verify(billService, times(1)).updateReadTime(id);
    }

    @Test
    @DisplayName("userHasUnreadBill - 正常场景：用户有未读账单")
    void userHasUnreadBill_success() {
        // Arrange
        Set<String> an8s = Set.of("AN8_1", "AN8_2");
        when(billService.getBillPayers()).thenReturn(Set.of());
        when(billService.getDocos()).thenReturn(Set.of());
        when(billService.countByPredicate(any())).thenReturn(2L);

        // Act
        RespWrapVo<BillUnreadInfoVo> result = bizBillController.userHasUnreadBill();

        // Assert
        assertNotNull(result);
        assertEquals(2L, result.getData()
                               .getUnreadBillCount());
        verify(billService, times(1)).getBillPayers();
        verify(billService, times(1)).countByPredicate(any());
    }

    @Test
    @DisplayName("userBillReadStatus - 正常场景：获取用户账单阅读状态")
    void userBillReadStatus_success() {
        // Arrange
        var mockContentUnreadInfoVo = new ContentUnreadInfoVo();
        when(billService.userBillReadStatus(any())).thenReturn(mockContentUnreadInfoVo);

        // Act
        ContentUnreadInfoVo result = bizBillController.userBillReadStatus();

        // Assert
        assertNotNull(result);
        verify(billService, times(1)).userBillReadStatus(any());
    }

    @Test
    @DisplayName("setUserBillReaded - 正常场景：设置单个账单为已读")
    void setUserBillReaded_success() {
        // Arrange
        Long id = 1L;
        when(billService.setUserBillRead(id)).thenReturn(true);

        // Act
        Boolean result = bizBillController.setUserBillReaded(id);

        // Assert
        assertTrue(result);
        verify(billService, times(1)).setUserBillRead(id);
    }

    @Test
    @DisplayName("setUserBillAllReaded - 正常场景：设置所有账单为已读")
    void setUserBillAllReaded_success() {
        // Arrange
        when(billService.setUserBillAllReaded(any())).thenReturn(true);

        // Act
        Boolean result = bizBillController.setUserBillAllReaded();

        // Assert
        assertTrue(result);
        verify(billService, times(1)).setUserBillAllReaded(any());
    }

    @Test
    @DisplayName("getSendDate - 正常场景：mailDate 和 emailDate 都不为空，返回较早的日期")
    void getSendDate_bothDatesNonNull() throws Exception {
        // Arrange
        BizBillController controller = new BizBillController();
        Date mailDate = new Date(System.currentTimeMillis() - 1000); // earlier date
        Date emailDate = new Date();

        Method method = BizBillController.class.getDeclaredMethod("getSendDate", Date.class, Date.class);
        method.setAccessible(true);

        // Act
        Date result = (Date) method.invoke(controller, mailDate, emailDate);

        // Assert
        assertEquals(mailDate, result);
    }

    @Test
    @DisplayName("getSendDate - 正常场景：只有 mailDate 不为空，返回 mailDate")
    void getSendDate_mailDateNonNull() throws Exception {
        // Arrange
        BizBillController controller = new BizBillController();
        Date mailDate = new Date();
        Date emailDate = null;

        Method method = BizBillController.class.getDeclaredMethod("getSendDate", Date.class, Date.class);
        method.setAccessible(true);

        // Act
        Date result = (Date) method.invoke(controller, mailDate, emailDate);

        // Assert
        assertEquals(mailDate, result);
    }

    @Test
    @DisplayName("getSendDate - 正常场景：只有 emailDate 不为空，返回 emailDate")
    void getSendDate_emailDateNonNull() throws Exception {
        // Arrange
        BizBillController controller = new BizBillController();
        Date mailDate = null;
        Date emailDate = new Date();

        Method method = BizBillController.class.getDeclaredMethod("getSendDate", Date.class, Date.class);
        method.setAccessible(true);

        // Act
        Date result = (Date) method.invoke(controller, mailDate, emailDate);

        // Assert
        assertEquals(emailDate, result);
    }

    @Test
    @DisplayName("getSendDate - 正常场景：mailDate 和 emailDate 都为空，返回 null")
    void getSendDate_bothDatesNull() throws Exception {
        // Arrange
        BizBillController controller = new BizBillController();
        Date mailDate = null;
        Date emailDate = null;

        Method method = BizBillController.class.getDeclaredMethod("getSendDate", Date.class, Date.class);
        method.setAccessible(true);

        // Act
        Date result = (Date) method.invoke(controller, mailDate, emailDate);

        // Assert
        assertNull(result);
    }

}