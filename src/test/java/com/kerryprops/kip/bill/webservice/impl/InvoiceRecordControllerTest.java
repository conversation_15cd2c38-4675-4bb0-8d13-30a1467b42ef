package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.enums.InvoiceTypeEnum;
import com.kerryprops.kip.bill.common.utils.InvoiceUtils;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.config.KerryInvoiceProperties;
import com.kerryprops.kip.bill.dao.entity.InvoiceRecord;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.clients.KipInvoiceClient;
import com.kerryprops.kip.bill.feign.entity.UploadInvoiceMainV2Vo;
import com.kerryprops.kip.bill.service.PaymentBillService;
import com.kerryprops.kip.bill.service.impl.InvoiceApplicationService;
import com.kerryprops.kip.bill.webservice.vo.req.InvoiceUploadResource;
import com.kerryprops.kip.bill.webservice.vo.resp.AptBillVo;
import com.kerryprops.kip.bill.webservice.vo.resp.AptPaymentInfoVo;
import com.kerryprops.kip.hiveas.feign.dto.resp.BuildingRespDto;
import com.kerryprops.kip.hiveas.webservice.vo.resp.BuildingResponseVo;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.List;

import static com.kerryprops.kip.bill.common.enums.BillPayModule.CASHIER;
import static com.kerryprops.kip.bill.common.enums.BillPayModule.CASHIER_FEE;
import static com.kerryprops.kip.bill.common.enums.InvoiceTypeEnum.QS;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.SUCCESS;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomObject;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomString;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * InvoiceRecordControllerTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-04-29
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("开票提交/回调-单元测试")
class InvoiceRecordControllerTest {

    @Mock
    private KipInvoiceClient kipInvoiceClient;

    @Mock
    private HiveAsClient hiveAsClient;

    @Mock
    private PaymentBillService paymentBillService;

    @Mock
    private InvoiceApplicationService invoiceApplicationService;

    @Mock
    private KerryInvoiceProperties kerryInvoiceProperties;

    @InjectMocks
    private InvoiceRecordController invoiceRecordController;

    @DisplayName("正常场景：发票上传成功")
    @Test
    public void testInvoiceUpload_success() {
        // Arrange
        setInvoicePayData();

        var aptBillVo = new AptBillVo();
        aptBillVo.setIsBilling("1");
        aptBillVo.setBuildingId(randomString());

        AptPaymentInfoVo aptPaymentInfoVo = randomObject(AptPaymentInfoVo.class);
        aptPaymentInfoVo.setAptBillList(List.of(aptBillVo));
        when(paymentBillService.queryPaymentInfoById(anyString())).thenReturn(aptPaymentInfoVo);

        var resource = randomObject(InvoiceUploadResource.class);
        resource.setInvoiceType(QS.getKipCode());
        try (MockedStatic<InvoiceUtils> mockedStaticInvoiceUtils = mockStatic(InvoiceUtils.class)) {
            mockedStaticInvoiceUtils.when(() -> InvoiceUtils.getSign(any(), any(), any()))
                                    .thenReturn(randomString());

            // Act
            RespWrapVo<InvoiceRecord> actualResponse = invoiceRecordController.invoiceUpload(resource);

            // Assert
            assertNotNull(actualResponse);
            assertEquals(SUCCESS.getCode(), actualResponse.getCode());
            verify(kipInvoiceClient, times(1)).invoiceUploadV2(any(UploadInvoiceMainV2Vo.class));
        }
    }

    @DisplayName("正常场景：预收款场景，发票上传成功")
    @Test
    public void testInvoiceUpload_hasPrepaid_success() {
        // Arrange
        setInvoicePayData();

        var aptBillVo = new AptBillVo();
        aptBillVo.setIsBilling("1");
        aptBillVo.setBuildingId(randomString());

        AptPaymentInfoVo aptPaymentInfoVo = randomObject(AptPaymentInfoVo.class);
        aptPaymentInfoVo.setAptBillList(List.of(aptBillVo));
        aptPaymentInfoVo.setAmt(1.0d);
        aptPaymentInfoVo.setAdvanceAmount(BigDecimal.ONE);
        aptPaymentInfoVo.setIsAdvanceBilling("1");
        aptPaymentInfoVo.setFeeId(123L);
        aptPaymentInfoVo.setBillPayModule(CASHIER);
        when(paymentBillService.queryPaymentInfoById(anyString())).thenReturn(aptPaymentInfoVo);

        var resource = randomObject(InvoiceUploadResource.class);
        resource.setInvoiceType(QS.getKipCode());
        try (MockedStatic<InvoiceUtils> mockedStaticInvoiceUtils = mockStatic(InvoiceUtils.class)) {
            mockedStaticInvoiceUtils.when(() -> InvoiceUtils.getSign(any(), any(), any()))
                                    .thenReturn(randomString());

            // Act
            RespWrapVo<InvoiceRecord> actualResponse = invoiceRecordController.invoiceUpload(resource);

            // Assert
            assertNotNull(actualResponse);
            assertEquals(SUCCESS.getCode(), actualResponse.getCode());
            verify(kipInvoiceClient, times(1)).invoiceUploadV2(any(UploadInvoiceMainV2Vo.class));
        }
    }

    @DisplayName("正常场景：杂费场景，发票上传成功")
    @Test
    public void testInvoiceUpload_hasFeePay_success() {
        // Arrange
        setInvoicePayData();

        var aptBillVo = new AptBillVo();
        aptBillVo.setIsBilling("1");
        aptBillVo.setBuildingId(randomString());

        AptPaymentInfoVo aptPaymentInfoVo = randomObject(AptPaymentInfoVo.class);
        aptPaymentInfoVo.setAptBillList(List.of(aptBillVo));
        aptPaymentInfoVo.setAmt(1.0d);
        aptPaymentInfoVo.setAdvanceAmount(BigDecimal.ZERO);
        aptPaymentInfoVo.setIsAdvanceBilling("1");
        aptPaymentInfoVo.setFeeId(123L);
        aptPaymentInfoVo.setBillPayModule(CASHIER_FEE);
        when(paymentBillService.queryPaymentInfoById(anyString())).thenReturn(aptPaymentInfoVo);

        var resource = randomObject(InvoiceUploadResource.class);
        resource.setInvoiceType(QS.getKipCode());
        try (MockedStatic<InvoiceUtils> mockedStaticInvoiceUtils = mockStatic(InvoiceUtils.class)) {
            mockedStaticInvoiceUtils.when(() -> InvoiceUtils.getSign(any(), any(), any()))
                                    .thenReturn(randomString());

            // Act
            RespWrapVo<InvoiceRecord> actualResponse = invoiceRecordController.invoiceUpload(resource);

            // Assert
            assertNotNull(actualResponse);
            assertEquals(SUCCESS.getCode(), actualResponse.getCode());
            verify(kipInvoiceClient, times(1)).invoiceUploadV2(any(UploadInvoiceMainV2Vo.class));
        }
    }

    private void setInvoicePayData() {
        var invoiceRecord = new InvoiceRecord();
        invoiceRecord.setInvoiceCode("123456");
        invoiceRecord.setInvoiceNo("INV001");
        invoiceRecord.setAmount(BigDecimal.valueOf(100.0));

        RespWrapVo<InvoiceRecord> expectedResponse = new RespWrapVo<>();
        expectedResponse.setCode(SUCCESS.getCode());
        expectedResponse.setMessage(SUCCESS.getMessage());
        expectedResponse.setData(invoiceRecord);

        BuildingRespDto building = new BuildingRespDto();
        building.setPropertyManagementCo("PMCo");
        BuildingResponseVo buildingResponseVo = new BuildingResponseVo();
        buildingResponseVo.setBuilding(building);
        when(hiveAsClient.getBuildingById(anyString())).thenReturn(buildingResponseVo);

        when(kipInvoiceClient.invoiceUploadV2(any(UploadInvoiceMainV2Vo.class))).thenReturn(new RespWrapVo<>());
        when(kerryInvoiceProperties.getSystemId()).thenReturn("systemId");
    }
}