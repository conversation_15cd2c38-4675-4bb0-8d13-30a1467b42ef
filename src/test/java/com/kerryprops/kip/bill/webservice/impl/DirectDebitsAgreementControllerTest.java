package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.exceptions.RestInvalidParamException;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.utils.jde.OracleSql;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.entity.AptBillDirectDebitsAgreement;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.AptBillAgreementService;
import com.kerryprops.kip.bill.service.model.s.AptBillAgreementBo;
import com.kerryprops.kip.bill.webservice.vo.req.DirectDebitsAgreementsQueryRequest;
import com.kerryprops.kip.hiveas.common.enums.RespCodeEnum;
import com.kerryprops.kip.hiveas.feign.dto.resp.RoomRespDto;
import com.kerryprops.kip.hiveas.webservice.resource.resp.BuildingSimple;
import com.kerryprops.kip.hiveas.webservice.resource.resp.ProjectSimple;
import com.kerryprops.kip.hiveas.webservice.resource.resp.RoomResp;
import com.kerryprops.kip.pmw.client.resource.AsyncAgreementStatusChangedResource;
import com.kerryprops.kip.pmw.client.resource.QueryAgreementOutputResource;
import com.kerryprops.kip.pmw.client.resource.TerminateAgreementInputResource;
import com.kerryprops.kip.pmw.client.resource.TerminateAgreementOutputResource;
import com.kerryprops.kip.pmw.client.service.PaymentClientService;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Pageable;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * DirectDebitsAgreementControllerTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-04-24
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("公寓小区-物业费代扣-协议管理")
class DirectDebitsAgreementControllerTest {

    @Mock
    private HiveAsClient hiveAsClient;

    @Mock
    private AptBillAgreementService aptBillAgreementService;

    @Mock
    private PaymentClientService paymentClient;

    @InjectMocks
    private DirectDebitsAgreementController directDebitsAgreementController;

    @Test
    @DisplayName("代扣协议列表查询，正常场景")
    void directDebitAgreements() {
        // Arrange
        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new AptBillAgreementBo());

            var queryRequest = new DirectDebitsAgreementsQueryRequest();
            queryRequest.setProjectId("123456");

            // Act and Assert
            assertDoesNotThrow(
                    () -> directDebitsAgreementController.directDebitAgreements(Pageable.ofSize(10), queryRequest));
        }
    }

    @Test
    @DisplayName("代扣协议列表查询，异常场景：协议不存在")
    void testTerminateAptBillAgreement_WhenAgreementNotExists() {
        // Arrange
        when(aptBillAgreementService.findAllByAgreementNos(any())).thenReturn(Collections.emptyList());

        // Act
        boolean result = directDebitsAgreementController.terminateAptBillAgreement("NON_EXIST");

        // Assert
        assertTrue(result);
    }

    @Test
    @DisplayName("代扣协议列表查询，正常场景")
    void testTerminateAptBillAgreement_NormalFlow() {
        // Arrange
        AptBillDirectDebitsAgreement mockAgreement = new AptBillDirectDebitsAgreement();
        mockAgreement.setRoomId("ROOM_001");
        when(aptBillAgreementService.findAllByAgreementNos(any())).thenReturn(List.of(mockAgreement));

        // 构造支付客户端响应
        when(paymentClient.terminateAgreement(any())).thenReturn(
                new TerminateAgreementOutputResource(null, null, null));


        boolean result = directDebitsAgreementController.terminateAptBillAgreement("AGMT_001");

        //Act
        assertTrue(result);

        // Assert
        verify(paymentClient, times(1)).terminateAgreement(any());
    }

    @Test
    @DisplayName("同步代扣协议列表查询，异常场景，协议为空")
    void syncAgreement_WhenAgreementListEmpty_ShouldThrowException() {
        // Arrange
        QueryAgreementOutputResource.QueryAgreementOutputBodyResource mockBody =
                new QueryAgreementOutputResource.QueryAgreementOutputBodyResource();
        mockBody.setAgreementList(Collections.emptyList());
        QueryAgreementOutputResource mockResponse = new QueryAgreementOutputResource(null, mockBody, null);

        when(paymentClient.queryAgreement(any())).thenReturn(mockResponse);

        // Act and Assert
        assertThrows(RestInvalidParamException.class, () -> directDebitsAgreementController.syncAgreement("AGMT_001"));

    }

    @Test
    @DisplayName("同步代扣协议列表查询，异常场景，目标协议未找到")
    void testSyncAgreement_WhenAgreementNotExist_ShouldThrowException() {
        // 构造包含其他协议的响应
        QueryAgreementOutputResource.PmwAgreementResource otherAgreement =
                new QueryAgreementOutputResource.PmwAgreementResource();
        otherAgreement.setAgreementNo("OTHER_AGMT");
        QueryAgreementOutputResource.QueryAgreementOutputBodyResource mockBody =
                new QueryAgreementOutputResource.QueryAgreementOutputBodyResource();
        mockBody.setAgreementList(List.of(otherAgreement));

        when(paymentClient.queryAgreement(any())).thenReturn(new QueryAgreementOutputResource(null, mockBody, null));

        // Act and Assert
        assertThrows(RestInvalidParamException.class, () -> directDebitsAgreementController.syncAgreement("AGMT_001"));
    }

    @Test
    @DisplayName("同步代扣协议列表查询，正常场景")
    void testSyncAgreement_NormalFlow_ShouldSyncSuccess() {
        // Arrange
        QueryAgreementOutputResource.PmwAgreementResource targetAgreement =
                new QueryAgreementOutputResource.PmwAgreementResource();
        targetAgreement.setAgreementNo("AGMT_001");
        QueryAgreementOutputResource.QueryAgreementOutputBodyResource mockBody =
                new QueryAgreementOutputResource.QueryAgreementOutputBodyResource();
        mockBody.setAgreementList(List.of(targetAgreement));

        when(paymentClient.queryAgreement(any())).thenReturn(new QueryAgreementOutputResource(null, mockBody, null));

        // Act
        boolean result = directDebitsAgreementController.syncAgreement("AGMT_001");

        // Assert
        assertTrue(result);
        verify(aptBillAgreementService).syncAgreement(eq(targetAgreement));
    }

    @Test
    @DisplayName("校验代扣协议，异常场景：房间不存在")
    void verifyDirectDebit_InvalidRoom() {
        // 构造无效房间响应
        RespWrapVo<RoomResp> invalidResponse = new RespWrapVo<>();
        invalidResponse.setCode("ERROR");
        when(hiveAsClient.getRoomById(anyString())).thenReturn(invalidResponse);

        var command = new DirectDebitsAgreementController.WxLifeDirectDebitPreVerifyCommand();
        command.setBillKey("ROOM_001");
        command.setOpenId("OPEN_001");
        command.setSerialNo(123L);
        command.setCompanyId("COMPANY_001");

        var response =
                (DirectDebitsAgreementController.DirectDebitConfigVerifyResponse) directDebitsAgreementController.verifyDirectDebit(
                        command);

        // Assert
        assertEquals("NO_EXIST", response.getReturnCode());
    }


    @Test
    @DisplayName("校验代扣协议，异常场景：该户号已通过其他渠道签约（不支持签约）")
    void verifyDirectDebit_SignedChannel() {
        // Arrange
        RespWrapVo<RoomResp> response = new RespWrapVo<>();
        response.setCode(RespCodeEnum.SUCCESS.getCode());
        response.setData(new RoomResp());
        when(hiveAsClient.getRoomById(anyString())).thenReturn(response);

        // 构造存在相同openId的协议
        var agmt = new AptBillDirectDebitsAgreement();
        agmt.setPspLogonId("OPEN_001");
        when(aptBillAgreementService.findAllActiveByRoomIdIn(anyList())).thenReturn(List.of(agmt));

        var command = new DirectDebitsAgreementController.WxLifeDirectDebitPreVerifyCommand();
        command.setBillKey("ROOM_001");
        command.setOpenId(agmt.getPspLogonId());
        command.setSerialNo(123L);
        command.setCompanyId("COMPANY_001");

        // Act
        var result =
                (DirectDebitsAgreementController.DirectDebitConfigVerifyResponse) directDebitsAgreementController.verifyDirectDebit(
                        command);

        // Assert
        assertEquals("SIGNED_CHANNEL", result.getReturnCode());
    }


    @Test
    @DisplayName("校验代扣协议，异常场景：该户号已被其他用户签约（不支持签约）")
    void verifyDirectDebit_SignedOther() {
        // Arrange
        // Arrange
        RespWrapVo<RoomResp> response = new RespWrapVo<>();
        response.setCode(RespCodeEnum.SUCCESS.getCode());
        response.setData(new RoomResp());
        when(hiveAsClient.getRoomById(anyString())).thenReturn(response);

        AptBillDirectDebitsAgreement agmt = new AptBillDirectDebitsAgreement();
        agmt.setPspLogonId("OTHER_OPENID");
        when(aptBillAgreementService.findAllActiveByRoomIdIn(anyList())).thenReturn(List.of(agmt));

        var command = new DirectDebitsAgreementController.WxLifeDirectDebitPreVerifyCommand();
        command.setBillKey("ROOM_001");
        command.setOpenId(agmt.getPspLogonId() + "xxx"); // 不能匹配，被其他用户签约
        command.setSerialNo(123L);
        command.setCompanyId("COMPANY_001");

        // Act
        var result =
                (DirectDebitsAgreementController.DirectDebitConfigVerifyResponse) directDebitsAgreementController.verifyDirectDebit(
                        command);

        // Assert
        assertEquals("SIGNED_OTHER", result.getReturnCode());
    }

    @Test
    @DisplayName("校验代扣协议，正常场景")
    void verifyDirectDebit_Success() {
        // Arrange
        var buildingSimple = new BuildingSimple();
        buildingSimple.setName("Test Building");

        var roomRespDto = new RoomRespDto();
        roomRespDto.setRoomNo("1001");

        var projectSimple = new ProjectSimple();
        projectSimple.setName("192-Name");

        RoomResp roomResp = new RoomResp();
        roomResp.setBuilding(buildingSimple);
        roomResp.setRoom(roomRespDto);
        roomResp.setProject(projectSimple);

        RespWrapVo<RoomResp> validResponse = new RespWrapVo<>();
        validResponse.setCode(RespCodeEnum.SUCCESS.getCode());
        validResponse.setData(roomResp);

        when(hiveAsClient.getRoomById(anyString())).thenReturn(validResponse);

        var command = new DirectDebitsAgreementController.WxLifeDirectDebitPreVerifyCommand();
        command.setBillKey("ROOM_001");
        command.setSerialNo(123L);
        command.setCompanyId("COMPANY_001");

        // Act
        var result =
                (DirectDebitsAgreementController.DirectDebitConfigVerifyResponse) directDebitsAgreementController.verifyDirectDebit(
                        command);
        // Assert
        assertEquals("SUCCESS", result.getReturnCode());
        assertTrue(StringUtils.isNotEmpty(result.getCustomerName()));
        assertTrue(StringUtils.isNotEmpty(result.getCustomerAddress()));
    }

    @Test
    @DisplayName("正常场景：协议回调成功")
    public void testAgreementCallback_Success() {
        // Arrange
        String agreementNo = "AGMT_001";
        var bodyResource = new AsyncAgreementStatusChangedResource.AsyncAgreementStatusChangedBodyResource();
        bodyResource.setAgreementNo(agreementNo);
        var request = new AsyncAgreementStatusChangedResource(null, bodyResource, null);

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new Object());

            // Act
            String result = directDebitsAgreementController.agreementCallback(agreementNo, request);

            // Assert
            assertEquals("accepted", result);
            verify(aptBillAgreementService, times(1)).saveOrUpdateAgreement(eq(bodyResource));
        }
    }

    @Test
    @DisplayName("单元测试，测试类：WxLifeDirectDebitConfirmAgreementCommand")
    void testWxLifeDirectDebitConfirmAgreementCommandFields() {
        // Arrange
        var command = new DirectDebitsAgreementController.WxLifeDirectDebitConfirmAgreementCommand();

        // Act
        command.setSerialNo(123L);
        command.setBillKey("bill_001");
        command.setCompanyId("company_001");
        command.setOpenId("open_001");
        command.setMerchantId("merchant_001");
        command.setChangeType("update");
        command.setAccountType("postpaid");
        command.setContractTerminationMod(1);
        command.setChargeDayOfMonth(15);
        command.setChargeLimitAmount(1000);
        command.setChargeAmount(500);
        command.setPhoneNo("**********");
        command.setGovId("ID12345");
        command.setGovName("John Doe");
        command.setExtra1("extra1");
        command.setExtra2("extra2");

        // Assert
        assertEquals(123L, command.getSerialNo());
        assertEquals("bill_001", command.getBillKey());
        assertEquals("company_001", command.getCompanyId());
        assertEquals("open_001", command.getOpenId());
        assertEquals("merchant_001", command.getMerchantId());
        assertEquals("update", command.getChangeType());
        assertEquals("postpaid", command.getAccountType());
        assertEquals(1, command.getContractTerminationMod());
        assertEquals(15, command.getChargeDayOfMonth());
        assertEquals(1000, command.getChargeLimitAmount());
        assertEquals(500, command.getChargeAmount());
        assertEquals("**********", command.getPhoneNo());
        assertEquals("ID12345", command.getGovId());
        assertEquals("John Doe", command.getGovName());
        assertEquals("extra1", command.getExtra1());
        assertEquals("extra2", command.getExtra2());
    }

    @Test
    @DisplayName("单元测试，测试类：Test all fields of DirectDebitConfigVerifyResponse")
    void testDirectDebitConfigVerifyResponseFields() {
        // Arrange
        DirectDebitsAgreementController.DirectDebitConfigVerifyResponse response =
                new DirectDebitsAgreementController.DirectDebitConfigVerifyResponse();

        // Act
        response.setMerchantId("merchant_001");
        response.setSerialNo(123L);
        response.setBillKey("bill_001");
        response.setCompanyId("company_001");
        response.setOpenId("open_001");
        response.setCustomerName("John Doe");
        response.setCustomerAddress("123 Main St");
        response.setAccountType("postpaid");
        response.setReturnCode("SUCCESS");
        response.setReturnMsg("Operation successful");

        // Assert
        assertEquals("merchant_001", response.getMerchantId());
        assertEquals(123L, response.getSerialNo());
        assertEquals("bill_001", response.getBillKey());
        assertEquals("company_001", response.getCompanyId());
        assertEquals("open_001", response.getOpenId());
        assertEquals("John Doe", response.getCustomerName());
        assertEquals("123 Main St", response.getCustomerAddress());
        assertEquals("postpaid", response.getAccountType());
        assertEquals("SUCCESS", response.getReturnCode());
        assertEquals("Operation successful", response.getReturnMsg());
    }
}