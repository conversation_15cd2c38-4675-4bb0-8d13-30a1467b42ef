package com.kerryprops.kip.bill.webservice.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.kerryprops.kip.bill.common.constants.BillConstants;
import com.kerryprops.kip.bill.common.exceptions.ExceptionResource;
import com.kerryprops.kip.bill.common.utils.DateUtils;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.entity.BillEntity;
import com.kerryprops.kip.bill.dao.entity.BillSendConfig;
import com.kerryprops.kip.bill.dao.entity.BillSendConfigAn8Link;
import com.kerryprops.kip.bill.feign.entity.TenantManagerItemResponse;
import com.kerryprops.kip.bill.webservice.vo.req.BillPayerVo;
import com.kerryprops.kip.bill.webservice.vo.req.StaffBillSendConfigUpdateVo;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

class API_update_StaffBillSendConfigControllerTest extends BaseClass_StaffBillSendConfigControllerTest {

    private static final String X_USER = "{\"buildingIds\":\"\",\"nickName\":\"JAKCAdmin\"," +
            "\"staff\":true,\"userId\":345,\"uniqueUserId\":\"345\",\"fromType\":\"S\",\"client\":false," +
            "\"superAdmin\":false,\"tenant\":false}";

    private final String METHOD_ARGUMENT_ERROR_CODE = "120003";

    private List<BillSendConfig> configList;

    private List<BillSendConfigAn8Link> an8LinkList;

    @Test
    void _03_update_validate_01_id_out_of_range() throws JsonProcessingException {
        StaffBillSendConfigUpdateVo saveVo = buildUpdateVo();
        saveVo.setId(0L);

        ResponseEntity<ExceptionResource> responseEntity = call_update_api_exception(X_USER, saveVo);
        ExceptionResource exceptionResource = responseEntity.getBody();
        Assertions.assertEquals(METHOD_ARGUMENT_ERROR_CODE, exceptionResource.getCode());
        Assertions.assertTrue(exceptionResource.getErrorText().contains("id must be equal to and greater than 1"));
    }

    @ParameterizedTest
    @NullAndEmptySource
    void _03_update_validate_02_doco_blank(String doco) throws JsonProcessingException {
        StaffBillSendConfigUpdateVo saveVo = buildUpdateVo();
        saveVo.setDoco(doco);

        ResponseEntity<ExceptionResource> responseEntity = call_update_api_exception(X_USER, saveVo);
        ExceptionResource exceptionResource = responseEntity.getBody();
        Assertions.assertEquals(METHOD_ARGUMENT_ERROR_CODE, exceptionResource.getCode());
        Assertions.assertTrue(exceptionResource.getErrorText().contains("doco can not be null"));
    }

    @Test
    void _03_update_validate_02_doco_over_length() throws JsonProcessingException {
        StaffBillSendConfigUpdateVo saveVo = buildUpdateVo();
        saveVo.setDoco(buildString(33));

        ResponseEntity<ExceptionResource> responseEntity = call_update_api_exception(X_USER, saveVo);
        ExceptionResource exceptionResource = responseEntity.getBody();
        Assertions.assertEquals(METHOD_ARGUMENT_ERROR_CODE, exceptionResource.getCode());
        Assertions.assertTrue(exceptionResource.getErrorText().contains("doco must be within 32 characters in length"));
    }

    @ParameterizedTest
    @NullAndEmptySource
    void _03_update_validate_03_email_blank(String email) throws JsonProcessingException {
        StaffBillSendConfigUpdateVo saveVo = buildUpdateVo();
        saveVo.setEmail(email);

        ResponseEntity<ExceptionResource> responseEntity = call_update_api_exception(X_USER, saveVo);
        ExceptionResource exceptionResource = responseEntity.getBody();
        Assertions.assertEquals(METHOD_ARGUMENT_ERROR_CODE, exceptionResource.getCode());
        Assertions.assertTrue(exceptionResource.getErrorText().contains("email address can not be null"));
    }

    @Test
    void _03_update_validate_03_email_over_length() throws JsonProcessingException {
        StaffBillSendConfigUpdateVo saveVo = buildUpdateVo();
        saveVo.setEmail(buildString(129));

        ResponseEntity<ExceptionResource> responseEntity = call_update_api_exception(X_USER, saveVo);
        ExceptionResource exceptionResource = responseEntity.getBody();
        Assertions.assertEquals(METHOD_ARGUMENT_ERROR_CODE, exceptionResource.getCode());
        Assertions.assertTrue(exceptionResource.getErrorText().contains("email address must be within 128 characters in length"));
    }

    @ParameterizedTest
    @NullAndEmptySource
    void _03_update_validate_04_payer_blank(List<BillPayerVo> payerVoList) throws JsonProcessingException {
        StaffBillSendConfigUpdateVo saveVo = buildUpdateVo();
        saveVo.setBillPayerVos(payerVoList);

        ResponseEntity<ExceptionResource> responseEntity = call_update_api_exception(X_USER, saveVo);
        ExceptionResource exceptionResource = responseEntity.getBody();
        Assertions.assertEquals(METHOD_ARGUMENT_ERROR_CODE, exceptionResource.getCode());
        Assertions.assertTrue(exceptionResource.getErrorText().contains("payer is required"));
    }

    @Test
    void _03_update_validate_05_payer_alph_over_length() throws JsonProcessingException {
        StaffBillSendConfigUpdateVo saveVo = buildUpdateVo();
        List<BillPayerVo> payerVoList = saveVo.getBillPayerVos();
        BillPayerVo billPayerVo = payerVoList.get(0);
        billPayerVo.setAlph(buildString(256));

        ResponseEntity<ExceptionResource> responseEntity = call_update_api_exception(X_USER, saveVo);
        ExceptionResource exceptionResource = responseEntity.getBody();
        Assertions.assertEquals(METHOD_ARGUMENT_ERROR_CODE, exceptionResource.getCode());
        Assertions.assertTrue(exceptionResource.getErrorText().contains("alph must be within 255 characters in length"));
    }

    @ParameterizedTest
    @NullAndEmptySource
    void _03_update_validate_06_payer_an8_blank(String an8) throws JsonProcessingException {
        StaffBillSendConfigUpdateVo saveVo = buildUpdateVo();
        List<BillPayerVo> payerVoList = saveVo.getBillPayerVos();
        BillPayerVo billPayerVo = payerVoList.get(0);
        billPayerVo.setAn8(an8);

        ResponseEntity<ExceptionResource> responseEntity = call_update_api_exception(X_USER, saveVo);
        ExceptionResource exceptionResource = responseEntity.getBody();
        Assertions.assertEquals(METHOD_ARGUMENT_ERROR_CODE, exceptionResource.getCode());
        Assertions.assertTrue(exceptionResource.getErrorText().contains("an8 can not be null"));
    }

    @Test
    void _03_update_validate_06_payer_an8_over_length() throws JsonProcessingException {
        StaffBillSendConfigUpdateVo saveVo = buildUpdateVo();
        List<BillPayerVo> payerVoList = saveVo.getBillPayerVos();
        BillPayerVo billPayerVo = payerVoList.get(0);
        billPayerVo.setAn8(buildString(33));

        ResponseEntity<ExceptionResource> responseEntity = call_update_api_exception(X_USER, saveVo);
        ExceptionResource exceptionResource = responseEntity.getBody();
        Assertions.assertEquals(METHOD_ARGUMENT_ERROR_CODE, exceptionResource.getCode());
        Assertions.assertTrue(exceptionResource.getErrorText().contains("an8 must be within 32 characters in length"));
    }

    @Test
    void _03_update_validate_07_phone_number_over_length() throws JsonProcessingException {
        StaffBillSendConfigUpdateVo saveVo = buildUpdateVo();
        saveVo.setPhoneNumber(buildString(33));

        ResponseEntity<ExceptionResource> responseEntity = call_update_api_exception(X_USER, saveVo);
        ExceptionResource exceptionResource = responseEntity.getBody();
        Assertions.assertEquals(METHOD_ARGUMENT_ERROR_CODE, exceptionResource.getCode());
        Assertions.assertTrue(exceptionResource.getErrorText().contains("phoneNumber must be within 32 characters in length"));
    }

    @Test
    void _03_update_validate_08_login_no_over_length() throws JsonProcessingException {
        StaffBillSendConfigUpdateVo saveVo = buildUpdateVo();
        saveVo.setLoginNo(buildString(65));

        ResponseEntity<ExceptionResource> responseEntity = call_update_api_exception(X_USER, saveVo);
        ExceptionResource exceptionResource = responseEntity.getBody();
        Assertions.assertEquals(METHOD_ARGUMENT_ERROR_CODE, exceptionResource.getCode());
        Assertions.assertTrue(exceptionResource.getErrorText().contains("loginNo must be within 64 characters in length"));
    }

    @Test
    void _03_update_user_not_login() throws JsonProcessingException {
        StaffBillSendConfigUpdateVo saveVo = buildUpdateVo();

        ResponseEntity<ExceptionResource> responseEntity = call_update_api_exception(null, saveVo);
        Assertions.assertEquals(responseEntity.getStatusCode(), HttpStatus.BAD_REQUEST);

        ExceptionResource resource = responseEntity.getBody();
        Assertions.assertEquals(resource.getErrorCode(), "400008");
        Assertions.assertEquals(resource.getErrorText(), "用户未登录");
    }

    @Test
    void _03_update_nonExistent_Id() throws JsonProcessingException {
        BillSendConfig billSendConfig = configList.stream().max(Comparator.comparing(BillSendConfig::getId)).get();
        long maxId = billSendConfig.getId() + 1;

        StaffBillSendConfigUpdateVo saveVo = buildUpdateVo();
        saveVo.setId(maxId);

        ResponseEntity<ExceptionResource> responseEntity = call_update_api_exception(X_USER, saveVo);
        Assertions.assertEquals(responseEntity.getStatusCode(), HttpStatus.BAD_REQUEST);

        ExceptionResource resource = responseEntity.getBody();
        Assertions.assertEquals(resource.getErrorCode(), "100002");
        Assertions.assertEquals(resource.getErrorText(), "资源不存在");
    }

    @Test
    void _03_update_deletedId() throws JsonProcessingException {
        StaffBillSendConfigUpdateVo saveVo = buildUpdateVo();
        saveVo.setId(configList.get(2).getId());

        ResponseEntity<ExceptionResource> responseEntity = call_update_api_exception(X_USER, saveVo);
        Assertions.assertEquals(responseEntity.getStatusCode(), HttpStatus.BAD_REQUEST);

        ExceptionResource resource = responseEntity.getBody();
        Assertions.assertEquals(resource.getErrorCode(), "400009");
        Assertions.assertEquals(resource.getErrorText(), "不允许更新已删除数据");
    }

    @Test
    void _03_update_replace_an8() throws JsonProcessingException {
        //prepare
        StaffBillSendConfigUpdateVo saveVo = buildUpdateVo();
        saveVo.setId(configList.get(0).getId());

        List<BillPayerVo> billPayerVos = saveVo.getBillPayerVos();
        BillPayerVo billPayerVo = billPayerVos.get(0);
        billPayerVo.setAlph("company 11");
        billPayerVo.setAn8("********");

        TenantManagerItemResponse tenantManagerItemResponse = new TenantManagerItemResponse();
        tenantManagerItemResponse.setId(1234l);
        tenantManagerItemResponse.setUserName("tester");
        tenantManagerItemResponse.setPhoneNumber(saveVo.getPhoneNumber());
        tenantManagerItemResponse.setLoginNo("********");
        Mockito.doReturn(List.of(tenantManagerItemResponse)).when(sUserClient).queryLoginAccounts(ArgumentMatchers.anyString());

        //execution
        ResponseEntity<RespWrapVo> responseEntity = call_update_api(saveVo);

        //verify
        Map<String, Object> map = (Map<String, Object>) responseEntity.getBody().getData();
        Assertions.assertEquals((Integer) map.get("id"), saveVo.getId().intValue());
        Assertions.assertEquals((String) map.get("doco"), saveVo.getDoco());
        Assertions.assertEquals((String) map.get("phoneNumber"), saveVo.getPhoneNumber());
        Assertions.assertEquals((String) map.get("loginNo"), tenantManagerItemResponse.getLoginNo());
        Assertions.assertEquals((String) map.get("email"), saveVo.getEmail());
        Assertions.assertEquals((String) map.get("tenantManagerId"), String.valueOf(tenantManagerItemResponse.getId()));
        Assertions.assertEquals((String) map.get("emailUsername"), tenantManagerItemResponse.getUserName());
        Assertions.assertEquals((String) map.get("loginNo"), tenantManagerItemResponse.getLoginNo());
        Assertions.assertEquals((String) map.get("mcu"), "mcu1,mcu2,mcu3");
        Assertions.assertEquals((String) map.get("unit"), "unit1,unit2,unit3,unit4");
        Assertions.assertEquals((Integer) map.get("isDel"), 0);

        List<Map<String, Object>> list = (List<Map<String, java.lang.Object>>) map.get("an8LinkList");

        Map<String, Object> an8Map = list.get(0);
        Assertions.assertTrue((Integer) an8Map.get("id") > 0);
        Assertions.assertEquals((Integer) an8Map.get("configId"), (Integer) map.get("id"));
        Assertions.assertEquals((Integer) an8Map.get("isDel"), 0);
        Assertions.assertEquals((String) an8Map.get("alph"), billPayerVo.getAlph());
        Assertions.assertEquals((String) an8Map.get("an8"), billPayerVo.getAn8());
    }

    @Test
    void _03_update_add_an8() throws JsonProcessingException {
        //prepare
        StaffBillSendConfigUpdateVo saveVo = buildUpdateVo();
        saveVo.setId(configList.get(0).getId());
        saveVo.setPhoneNumber("18264935168");
        saveVo.setLoginNo("59287577");
        saveVo.setEmail("<EMAIL>");

        List<BillPayerVo> billPayerVos = saveVo.getBillPayerVos();
        billPayerVos.add(BillPayerVo.builder().alph("company 2").an8("********").build());

        TenantManagerItemResponse tenantManagerItemResponse = new TenantManagerItemResponse();
        tenantManagerItemResponse.setId(5678l);
        tenantManagerItemResponse.setUserName("tester2");
        tenantManagerItemResponse.setPhoneNumber(saveVo.getPhoneNumber());
        tenantManagerItemResponse.setLoginNo("********");
        Mockito.doReturn(List.of(tenantManagerItemResponse)).when(sUserClient).queryLoginAccounts(ArgumentMatchers.anyString());

        //execute
        ResponseEntity<RespWrapVo> responseEntity = call_update_api(saveVo);

        //verify
        Map<String, Object> map = (Map<String, Object>) responseEntity.getBody().getData();
        Assertions.assertEquals((Integer) map.get("id"), saveVo.getId().intValue());
        Assertions.assertEquals((String) map.get("doco"), saveVo.getDoco());
        Assertions.assertEquals((String) map.get("phoneNumber"), saveVo.getPhoneNumber());
        Assertions.assertEquals((String) map.get("loginNo"), tenantManagerItemResponse.getLoginNo());
        Assertions.assertEquals((String) map.get("email"), saveVo.getEmail());
        Assertions.assertEquals((String) map.get("tenantManagerId"), String.valueOf(tenantManagerItemResponse.getId()));
        Assertions.assertEquals((String) map.get("emailUsername"), tenantManagerItemResponse.getUserName());
        Assertions.assertEquals((String) map.get("loginNo"), tenantManagerItemResponse.getLoginNo());
        Assertions.assertEquals((String) map.get("mcu"), "mcu1,mcu2,mcu3");
        Assertions.assertEquals((String) map.get("unit"), "unit1,unit2,unit3,unit4");
        Assertions.assertEquals((Integer) map.get("isDel"), 0);

        List<String> expectBillPayerKeyList = saveVo.getBillPayerVos().stream()
                .map(BillPayerVo::groupByKey).collect(Collectors.toList());

        List<Map<String, Object>> list = (List<Map<String, java.lang.Object>>) map.get("an8LinkList");
        Assertions.assertTrue(list.size() >= 2);

        List<String> actualBillPayerKeyList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> an8Map = list.get(i);
            Assertions.assertTrue((Integer) an8Map.get("id") > 0);
            Assertions.assertEquals((Integer) an8Map.get("configId"), (Integer) map.get("id"));
            Assertions.assertEquals((Integer) an8Map.get("isDel"), 0);

            StringBuilder builder = new StringBuilder();
            builder.append((String) an8Map.get("an8")).append("_").append((String) an8Map.get("alph")).append("_");
            actualBillPayerKeyList.add(builder.toString());
        }
        String expectPayerStr = expectBillPayerKeyList.stream().sorted().collect(Collectors.joining());
        String actualPayerStr = actualBillPayerKeyList.stream().sorted().collect(Collectors.joining());
        Assertions.assertTrue(actualPayerStr.contains(expectPayerStr));
    }

    @Test
    void _03_update_delete_an8() throws JsonProcessingException {
        //prepare
        StaffBillSendConfigUpdateVo saveVo = buildUpdateVo();
        saveVo.setId(configList.get(1).getId());

        BillSendConfigAn8Link an8Link1 = this.an8LinkList.get(1);
        BillSendConfigAn8Link an8Link2 = this.an8LinkList.get(2);
        BillPayerVo billPayerVo1 = BillPayerVo.builder().alph(an8Link1.getAlph()).an8(an8Link1.getAn8()).build();
        BillPayerVo billPayerVo2 = BillPayerVo.builder().alph(an8Link2.getAlph()).an8(an8Link2.getAn8()).build();
        saveVo.setBillPayerVos(Lists.list(billPayerVo1));

        TenantManagerItemResponse tenantManagerItemResponse = new TenantManagerItemResponse();
        tenantManagerItemResponse.setId(1122l);
        tenantManagerItemResponse.setUserName("tester2");
        tenantManagerItemResponse.setPhoneNumber(saveVo.getPhoneNumber());
        tenantManagerItemResponse.setLoginNo(saveVo.getLoginNo());
        Mockito.doReturn(List.of(tenantManagerItemResponse)).when(sUserClient).queryLoginAccounts(ArgumentMatchers.anyString());

        //execute
        ResponseEntity<RespWrapVo> responseEntity = call_update_api(saveVo);

        //verify
        Map<String, Object> map = (Map<String, Object>) responseEntity.getBody().getData();
        List<Map<String, Object>> list = (List<Map<String, java.lang.Object>>) map.get("an8LinkList");

        List<String> actualBillPayerKeyList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> an8Map = list.get(i);
            Assertions.assertTrue((Integer) an8Map.get("id") > 0);
            Assertions.assertEquals((Integer) an8Map.get("configId"), (Integer) map.get("id"));
            Assertions.assertEquals((Integer) an8Map.get("isDel"), 0);

            StringBuilder builder = new StringBuilder();
            builder.append((String) an8Map.get("an8")).append("_").append((String) an8Map.get("alph")).append("_");
            actualBillPayerKeyList.add(builder.toString());
            System.out.println("actual[" + i + "]=" + builder.toString());
        }
        System.out.println("expect[1]=" + billPayerVo1.groupByKey());
        System.out.println("expect[2]=" + billPayerVo2.groupByKey());
        Assertions.assertTrue(actualBillPayerKeyList.contains(billPayerVo1.groupByKey()));
        Assertions.assertFalse(actualBillPayerKeyList.contains(billPayerVo2.groupByKey()));
    }

    @BeforeEach
    void prepareTestData() {
        List<BillSendConfig> requestConfigList = buildBillSendConfigList();
        this.configList = configRepository.saveAll(requestConfigList);

        List<BillSendConfigAn8Link> requestAn8List = buildBillSendConfigAn8Links(configList);
        this.an8LinkList = an8LinkRepository.saveAll(requestAn8List);
    }

    @AfterEach
    void clearTestData() {
        configRepository.deleteAll(configList);
        an8LinkRepository.deleteAll(an8LinkList);
    }

    private StaffBillSendConfigUpdateVo buildUpdateVo() {
        StaffBillSendConfigUpdateVo vo = new StaffBillSendConfigUpdateVo();
        vo.setId(Long.valueOf(1l));
        vo.setDoco("123456");
        vo.setEmail("<EMAIL>");
        vo.setLoginNo("B123456");
        vo.setPhoneNumber("13764912168");
        vo.setProjectId("192");
        vo.setBuildingId("32014100");
        vo.setBuildingName("A2_test");
        vo.setBillPayerVos(Lists.list(BillPayerVo.builder().alph("Company_X1").an8("********").build()));
        return vo;
    }

    private ResponseEntity<RespWrapVo> call_update_api(StaffBillSendConfigUpdateVo updateVo)
            throws JsonProcessingException {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.parseMediaType(MediaType.APPLICATION_JSON_UTF8_VALUE));
        httpHeaders.set("x-user", X_USER);

        String json = mapper.writeValueAsString(updateVo);
        Map<String, Object> hashMap = mapper.readValue(json, HashMap.class);

        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(hashMap, httpHeaders);

        String url = "http://localhost:" + serverPort + "/s/bill/sendconfig/update";
        return restTemplate.exchange(url, HttpMethod.PUT, httpEntity, RespWrapVo.class);
    }

    private ResponseEntity<ExceptionResource> call_update_api_exception(
            String xUserJson, StaffBillSendConfigUpdateVo updateVo)
            throws JsonProcessingException {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.parseMediaType(MediaType.APPLICATION_JSON_UTF8_VALUE));
        Optional.ofNullable(xUserJson).ifPresent(str -> httpHeaders.set("x-user", str));

        String json = mapper.writeValueAsString(updateVo);
        Map<String, Object> hashMap = mapper.readValue(json, HashMap.class);

        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(hashMap, httpHeaders);

        String url = "http://localhost:" + serverPort + "/s/bill/sendconfig/update";
        return restTemplate.exchange(url, HttpMethod.PUT, httpEntity, ExceptionResource.class);
    }

    private List<BillEntity> buildBillDataSet() {
        List<BillEntity> billEntities = Arrays.asList(
                BillEntity.builder().tpEv01("Y").tpDct("D1").tpMcu("mcu1").tpUnit("unit1").tpDoco("123456")
                        .tpAn8("********").tpAlph("A开发公司").tpFyr(22).tpPn(1).formatDate("2022-01-05").delFlag("1")
                        .tpStatus(5).mailStatus(BillConstants.MSG_SUCCESS).emailStatus(BillConstants.MSG_NOT_SEND)
                        .mailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-05 20:23:58"))
                        .readStatus(0).tpGtfilenm("R1.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("D1").tpMcu("mcu2").tpUnit("unit2").tpDoco("123456")
                        .tpAn8("********").tpAlph("A开发公司").tpFyr(22).tpPn(2).formatDate("2022-02-04").delFlag("0")
                        .tpStatus(5).mailStatus(BillConstants.MSG_FAILURE).emailStatus(BillConstants.MSG_SUCCESS)
                        .mailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-02-04 10:11:22"))
                        .emailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-02-04 11:30:20"))
                        .emailErr("[{\"email\":\"<EMAIL>\",\"sendStatus\":\"发送成功\",\"sendTime\":\"2022-02-04 11:41:41\"}]")
                        .readStatus(0).tpGtfilenm("R2.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("D1").tpMcu("mcu2").tpUnit("unit4").tpDoco("123456")
                        .tpAn8("********").tpAlph("A开发公司").tpFyr(22).tpPn(1).formatDate("2022-01-07").delFlag("0")
                        .deleteBy("jane.dong")
                        .deleteTime(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-08 11:22:59"))
                        .tpStatus(BillConstants.MSG_NOT_SEND).mailStatus(BillConstants.MSG_NOT_SEND)
                        .emailStatus(BillConstants.MSG_NOT_SEND).readStatus(0).tpGtfilenm("R3.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("D2").tpMcu("mcu2").tpUnit("unit3").tpDoco("a********9b********9c********9d1")
                        .tpAn8("********").tpAlph("A开发公司").tpFyr(22).tpPn(1).formatDate("2022-01-08").delFlag("0")
                        .tpStatus(5).mailStatus(BillConstants.MSG_NOT_SEND).emailStatus(BillConstants.MSG_SUCCESS)
                        .emailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-08 16:10:20"))
                        .emailErr("[{\"email\":\"<EMAIL>\",\"sendStatus\":\"发送成功\",\"sendTime\":\"2022-01-08 16:31:41\"}]")
                        .readStatus(1).tpGtfilenm("R4.PDF").build(),
                BillEntity.builder().tpEv01("Y").tpDct("D2").tpMcu("mcu2").tpUnit("unit5").tpDoco("a********9b********9c********9d1")
                        .tpAn8("********").tpAlph("A开发公司").tpFyr(22).tpPn(1).formatDate("2022-01-18").delFlag("0")
                        .tpStatus(5).mailStatus(BillConstants.MSG_NOT_SEND).emailStatus(BillConstants.MSG_PARTIAL_SUCCESS)
                        .emailDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD_HH_MM_SS, "2022-01-18 14:00:05"))
                        .emailErr("[{\"email\":\"<EMAIL>\",\"sendStatus\":\"发送成功\",\"sendTime\":\"2022-01-18 14:31:41\"}]")
                        .readStatus(0).tpGtfilenm("R5.PDF").build()
        );
        return billEntities;
    }

}
