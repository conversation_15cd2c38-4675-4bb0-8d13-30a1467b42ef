package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.BillRepository;
import com.kerryprops.kip.bill.dao.entity.BillEntity;
import com.kerryprops.kip.hiveas.webservice.vo.resp.BuildingRespVo;
import com.kerryprops.kip.hiveas.webservice.vo.resp.CenterRespVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;


@Slf4j
public class API_queryDocos_StaffBillControllerTest extends BaseClass_StaffBillControllerTest {

    protected final String STAFF_JSON_ABNORMAL1 = "{\"buildingIds\":\"\",\"nickName\":\"JAKCAdmin\"," +
            "\"staff\":true,\"userId\":345,\"uniqueUserId\":\"345\",\"fromType\":\"S\",\"client\":false," +
            "\"superAdmin\":false,\"tenant\":false}";

    protected final String STAFF_JSON_ABNORMAL2 = "{\"nickName\":\"JAKCAdmin\"," +
            "\"staff\":true,\"userId\":345,\"uniqueUserId\":\"345\",\"fromType\":\"S\",\"client\":false," +
            "\"superAdmin\":false,\"tenant\":false}";

    private final String PROJECT_ID = "P2";

    private final String ERROR_TIPS = "error_test";

    private List<BillEntity> dataSet;

    @Autowired
    private BillRepository billRepository;

    protected void verify(ResponseEntity<RespWrapVo> response, List<String> expectedDocoList) {
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(RespCodeEnum.SUCCESS.getCode(), response.getBody().getCode());
        assertEquals(RespCodeEnum.SUCCESS.getMessage(), response.getBody().getMessage());

        List<String> contentList = (List<String>) response.getBody().getData();
        Set<String> actualDocoList = new HashSet<>(contentList);
        assertTrue(expectedDocoList.stream().sorted().collect(Collectors.joining())
                .equals(actualDocoList.stream().sorted().collect(Collectors.joining())));
    }

    @Test
    void _01_queryDocos_emptyProjectId() {
        ResponseEntity<RespWrapVo> responseEntity = call_queryDocos_api(SUPER_ADMIN_JSON, StringUtils.EMPTY);
        RespWrapVo respWrapVo = responseEntity.getBody();
        assertEquals(respWrapVo.getCode(), RespCodeEnum.UNKNOWN_ERROR.getCode());
    }

    @Test
    void _02_queryDocos_blankProjectId() {
        ResponseEntity<RespWrapVo> responseEntity = call_queryDocos_api(SUPER_ADMIN_JSON, "  ");
        RespWrapVo respWrapVo = responseEntity.getBody();
        assertEquals(respWrapVo.getCode(), RespCodeEnum.UNKNOWN_ERROR.getCode());
    }

    @Test
    void _03_queryDocos_userNotLogin() {
        ResponseEntity<RespWrapVo> responseEntity = call_queryDocos_api(null, PROJECT_ID);
        RespWrapVo respWrapVo = responseEntity.getBody();
        assertEquals(respWrapVo.getCode(), RespCodeEnum.UNKNOWN_ERROR.getCode());
        assertEquals("user_not_login", respWrapVo.getMessage());
    }

    @Test
    void _04_01_queryDocos_superAdmin_nullHiveAsClientResponse() throws URISyntaxException, IOException {
        doReturn(null).when(hiveAsClient).getCenterByIds(ArgumentMatchers.any());

        ResponseEntity<RespWrapVo> responseEntity = call_queryDocos_api(SUPER_ADMIN_JSON, PROJECT_ID);

        List<String> expectDocoList = Collections.emptyList();
        verify(responseEntity, expectDocoList);
    }

    @Test
    void _04_02_queryDocos_superAdmin_errorHiveAsClientResponse() throws URISyntaxException, IOException {
        doReturn(null).when(hiveAsClient).getCenterByIds(ArgumentMatchers.any());

        ResponseEntity<RespWrapVo> responseEntity = call_queryDocos_api(SUPER_ADMIN_JSON, PROJECT_ID);

        List<String> expectDocoList = Collections.emptyList();
        verify(responseEntity, expectDocoList);
    }

    @Test
    void _04_03_queryDocos_superAdmin_validHiveAsClientResponse() throws URISyntaxException, IOException {
        mockJdeBusByProject(PROJECT_ID);
        ResponseEntity<RespWrapVo> responseEntity = call_queryDocos_api(SUPER_ADMIN_JSON, PROJECT_ID);

        List<String> expectDocoList = Lists.newArrayList("112208", "112209", "112210", "112211", "112212");
        verify(responseEntity, expectDocoList);
    }

    @Test
    void _05_queryDocos_staff_nullBuildingId() throws URISyntaxException, IOException {
        mockProjectId(PROJECT_ID);
        ResponseEntity<RespWrapVo> responseEntity = call_queryDocos_api(STAFF_JSON_ABNORMAL1, PROJECT_ID);

        List<String> expectDocoList = Collections.emptyList();
        verify(responseEntity, expectDocoList);
    }

    @Test
    void _06_queryDocos_staff_emptyBuildingId() throws URISyntaxException, IOException {
        mockProjectId(PROJECT_ID);
        ResponseEntity<RespWrapVo> responseEntity = call_queryDocos_api(STAFF_JSON_ABNORMAL2, PROJECT_ID);

        List<String> expectDocoList = Collections.emptyList();
        verify(responseEntity, expectDocoList);
    }

    @Test
    void _07_01_queryDocos_staff_nullHiveAsClientResponse() throws URISyntaxException, IOException {
        mockJdeBusByProject(PROJECT_ID);
        doThrow(new RuntimeException(ERROR_TIPS)).when(hiveAsClient).convertToJdeBus(ArgumentMatchers.anyList());

        ResponseEntity<RespWrapVo> responseEntity = call_queryDocos_api(STAFF_JSON, PROJECT_ID);
        RespWrapVo respWrapVo = responseEntity.getBody();

        assertEquals(respWrapVo.getCode(), RespCodeEnum.UNKNOWN_ERROR.getCode());
        assertEquals(ERROR_TIPS, respWrapVo.getMessage());
    }

    @Test
    void _07_02_queryDocos_staff_invalidHiveAsClientResponse() throws URISyntaxException, IOException {
        mockJdeBusByProject(PROJECT_ID);

        doThrow(new RuntimeException(ERROR_TIPS)).when(hiveAsClient).convertToJdeBus(ArgumentMatchers.anyList());

        ResponseEntity<RespWrapVo> responseEntity = call_queryDocos_api(STAFF_JSON, PROJECT_ID);
        RespWrapVo respWrapVo = responseEntity.getBody();

        assertEquals(respWrapVo.getCode(), RespCodeEnum.UNKNOWN_ERROR.getCode());
        assertEquals(ERROR_TIPS, respWrapVo.getMessage());
    }

    @Test
    void _07_03_queryDocos_staff_emptyHiveAsClientResponse() throws URISyntaxException, IOException {
        mockProjectId(PROJECT_ID);
        doReturn(Collections.EMPTY_LIST).when(hiveAsClient).getBuildingByIds(ArgumentMatchers.any());

        ResponseEntity<RespWrapVo> responseEntity = call_queryDocos_api(STAFF_JSON, PROJECT_ID);
        RespWrapVo respWrapVo = responseEntity.getBody();

        assertEquals(respWrapVo.getCode(), RespCodeEnum.SUCCESS.getCode());
        assertEquals(respWrapVo.getMessage(), RespCodeEnum.SUCCESS.getMessage());

        Collection collection = (Collection) respWrapVo.getData();
        assertEquals(collection.size(), 0);
    }

    @Test
    void _08_01_queryDocos_staff_building1() throws URISyntaxException, IOException {
        mockProjectId(PROJECT_ID);
        mockBuildingBU("P1-B1");

        final String xUserJson = "{\"buildingIds\":\"P1-B1\",\"nickName\":\"JAKCAdmin\"," +
                "\"staff\":true,\"userId\":345,\"uniqueUserId\":\"345\",\"fromType\":\"S\",\"client\":false," +
                "\"superAdmin\":false,\"tenant\":false}";
        ResponseEntity<RespWrapVo> responseEntity = call_queryDocos_api(xUserJson, PROJECT_ID);

        List<String> expectDocoList = Collections.emptyList();
        verify(responseEntity, expectDocoList);
    }

    @Test
    void _08_02_queryDocos_staff_building2() throws URISyntaxException, IOException {
        mockJdeBusByProject(PROJECT_ID);
        mockJdeBusByBuildingBU("P2-B1");

        final String xUserJson = "{\"buildingIds\":\"P2-B1\",\"nickName\":\"JAKCAdmin\"," +
                "\"staff\":true,\"userId\":345,\"uniqueUserId\":\"345\",\"fromType\":\"S\",\"client\":false," +
                "\"superAdmin\":false,\"tenant\":false}";
        ResponseEntity<RespWrapVo> responseEntity = call_queryDocos_api(xUserJson, PROJECT_ID);

        List<String> expectDocoList = Lists.newArrayList("112208", "112209");
        verify(responseEntity, expectDocoList);
    }

    @Test
    void _08_03_queryDocos_staff_building3() throws URISyntaxException, IOException {
        mockJdeBusByProject(PROJECT_ID);
        mockJdeBusByBuildingBU("P1-B1;P2-B1");

        final String xUserJson = "{\"buildingIds\":\"P1-B1,P2-B1\",\"nickName\":\"JAKCAdmin\"," +
                "\"staff\":true,\"userId\":345,\"uniqueUserId\":\"345\",\"fromType\":\"S\",\"client\":false," +
                "\"superAdmin\":false,\"tenant\":false}";
        ResponseEntity<RespWrapVo> responseEntity = call_queryDocos_api(xUserJson, PROJECT_ID);

        List<String> expectDocoList = Lists.newArrayList("112208", "112209");
        verify(responseEntity, expectDocoList);
    }

    @BeforeEach
    void prepareTestData() {
        List<Integer> deleteBillIndexList = Lists.list(1, 7, 13);
        List<BillEntity> billEntities = buildInitialDataset();
        for (int i = 1; i <= billEntities.size(); i++) {
            String doco = "1122" + String.format("%02d", i);
            String an8 = "112233" + String.format("%02d", i);
            String delFlag = deleteBillIndexList.contains(Integer.valueOf(i)) ? "1" : "0";

            BillEntity billEntity = billEntities.get(i - 1);
            billEntity.setTpDoco(doco);
            billEntity.setTpAn8(an8);
            billEntity.setDelFlag(delFlag);
        }

        dataSet = billRepository.saveAll(billEntities);
    }

    @AfterEach
    void clearTestData() {
        billRepository.deleteAll(dataSet);
    }

    private ResponseEntity<RespWrapVo> call_queryDocos_api(String xUserJson, String projectId) {
        // headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        if (Objects.nonNull(xUserJson)) {
            headers.set("x-user", xUserJson);
        }

        HttpEntity request = new HttpEntity<>(null, headers);
        String url = "http://localhost:" + serverPort + "/s/bill/docos/" + projectId;

        return restTemplate.exchange(url, HttpMethod.GET, request, RespWrapVo.class);
    }

}
