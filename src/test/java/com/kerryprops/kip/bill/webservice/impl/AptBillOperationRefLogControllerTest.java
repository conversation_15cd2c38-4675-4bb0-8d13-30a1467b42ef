package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.AptBillOperationService;
import com.kerryprops.kip.bill.webservice.vo.req.BillRefLogSearchRequest;
import com.kerryprops.kip.bill.webservice.vo.resp.AptBillOperationLogResource;
import com.kerryprops.kip.bill.webservice.vo.resp.OperationChangedFiledRespVo;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import java.util.List;

@DisplayName("C端-账单操作记录-单元测试类")
class AptBillOperationRefLogControllerTest extends BaseIntegrationTest {

    private static final String PROJECT_ID_TEST = "192";

    @Autowired
    private AptBillOperationService operationService;

    @Autowired
    private AptBillOperationRefLogController aptBillOperationRefLogController;

    @BeforeAll
    public void prepareTestData() {
        LoginUser loginUser = LoginUser.builder()
                .fromType("S")
                .cId(randomString())
                .projectIds(PROJECT_ID_TEST)
                .roles("SUPER_ADMIN")
                .build();
        UserInfoUtils.setUser(loginUser);

        AptBill aptBillMock1 = randomAptBill();
        aptBillMock1.setId(random.nextLong());
        aptBillMock1.setProjectId(PROJECT_ID_TEST);
        operationService.saveAutoOperationLog(aptBillMock1, List.of(generateRandomChangedFiled(), generateRandomChangedFiled()));

        AptBill aptBillMock2 = randomAptBill();
        aptBillMock2.setId(random.nextLong());
        aptBillMock2.setProjectId(PROJECT_ID_TEST);
        operationService.saveAutoOperationLog(aptBillMock2, List.of(generateRandomChangedFiled(), generateRandomChangedFiled()));

        AptBill aptBillMock3 = randomAptBill();
        aptBillMock3.setId(random.nextLong());
        aptBillMock3.setProjectId(PROJECT_ID_TEST);
        operationService.saveAutoOperationLog(aptBillMock3, List.of(generateRandomChangedFiled(), generateRandomChangedFiled()));

        AptBill aptBillMock4 = randomAptBill();
        aptBillMock4.setId(random.nextLong());
        aptBillMock4.setProjectId(randomString());
        operationService.saveAutoOperationLog(aptBillMock4, List.of(generateRandomChangedFiled(), generateRandomChangedFiled()));
    }

    private OperationChangedFiledRespVo generateRandomChangedFiled() {
        OperationChangedFiledRespVo operationChangedFiledRespVo = new OperationChangedFiledRespVo();
        operationChangedFiledRespVo.setFieldName(randomString());
        operationChangedFiledRespVo.setFieldAlias(randomString());
        operationChangedFiledRespVo.setFieldOldValue(randomString());
        operationChangedFiledRespVo.setFieldNewValue(randomString());
        return operationChangedFiledRespVo;
    }

    @Disabled
    @Test
    @DisplayName("C端-账单操作记录-分页查询")
    void _01_billsOperationLog_success() {
        BillRefLogSearchRequest billRefLogSearchRequestTest = new BillRefLogSearchRequest();
        billRefLogSearchRequestTest.setProjectId(PROJECT_ID_TEST);

        // 执行待验证部分
        Page<AptBillOperationLogResource> aptBillOperationLogResourcePage1 = aptBillOperationRefLogController
                .billsOperationLog(PageRequest.of(0, 2), billRefLogSearchRequestTest);
        Page<AptBillOperationLogResource> aptBillOperationLogResourcePage2 = aptBillOperationRefLogController
                .billsOperationLog(PageRequest.of(1, 2), billRefLogSearchRequestTest);

        // 验证执行结果
        Assertions.assertEquals(2, aptBillOperationLogResourcePage1.getTotalPages());
        Assertions.assertEquals(2, aptBillOperationLogResourcePage1.get().count());
        Assertions.assertEquals(1, aptBillOperationLogResourcePage2.get().count());
    }

    @Disabled
    @Test
    @DisplayName("C端-账单操作记录-导出")
    void _02_billsOperationLogExport_success() {
        BillRefLogSearchRequest billRefLogSearchRequestMock = new BillRefLogSearchRequest();
        billRefLogSearchRequestMock.setProjectId(PROJECT_ID_TEST);

        HttpServletResponse httpServletResponseMock = new MockHttpServletResponse();

        // 执行待验证部分
        Assertions.assertDoesNotThrow(() -> aptBillOperationRefLogController
                .billsOperationLogExport(billRefLogSearchRequestMock, httpServletResponseMock));
    }

}
