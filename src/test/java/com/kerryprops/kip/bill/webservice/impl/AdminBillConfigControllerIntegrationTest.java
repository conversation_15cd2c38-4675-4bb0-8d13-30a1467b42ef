package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.BaseControllerIntegrationTest;
import com.kerryprops.kip.bill.dao.entity.BillConfigEntity;
import com.kerryprops.kip.bill.service.IBillConfigService;
import com.kerryprops.kip.bill.utils.RandomUtil;
import com.kerryprops.kip.bill.webservice.vo.req.BillConfigAddDto;
import com.kerryprops.kip.bill.webservice.vo.req.BillConfigListDto;
import com.kerryprops.kip.bill.webservice.vo.req.BillConfigUpdateDto;
import lombok.SneakyThrows;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * AdminBillConfigControllerIntegrationTest.
 *
 * <AUTHOR> Yu 2025-03-10 12:11:26
 **/
class AdminBillConfigControllerIntegrationTest extends BaseControllerIntegrationTest {

    private static final String BASE_URL = "/admin/bill/configs";

    private static final String[] ADMIN_ROLE = {"X-User", "{\"fromType\":\"S\",\"roles\":\"SUPER_ADMIN\"}"};

    @Autowired
    private IBillConfigService billConfigService;

    @Test
    @SneakyThrows
    @DisplayName("正常查询账单配置")
    void getBillConfig_ok() {
        // 准备测试数据
        BillConfigEntity entity = mockAddBillConfig();

        // 执行请求
        String resultStr = sendGetRequest(formatUrl(BASE_URL + "/{id}", entity.getId()), ADMIN_ROLE);

        // 验证结果
        BillConfigEntity result = parseJson(resultStr, BillConfigEntity.class);
        assertThat(result).usingRecursiveComparison().isEqualTo(entity);
    }

    @Test
    @SneakyThrows
    @DisplayName("正常查询账单配置列表")
    void listBillConfigs_ok() {
        // 准备测试数据
        BillConfigEntity addBillConfig = mockAddBillConfig();
        BillConfigListDto queryDto = toQuery(addBillConfig);

        // 执行请求
        String resultStr = sendGetRequest(BASE_URL, queryDto, ADMIN_ROLE);

        // 验证结果
        BillConfigEntity result = parseJson(resultStr, BillConfigEntity.class);
        assertThat(result.getSourceType()).isEqualTo(queryDto.getSourceType());
        assertThat(result.getSourceName()).isEqualTo(queryDto.getSourceName());
        assertThat(result.getHttpService()).isEqualTo(queryDto.getHttpService());
    }

    @Test
    @SneakyThrows
    @DisplayName("正常新增账单配置")
    void addBillConfig_ok() {
        // 准备测试数据
        BillConfigAddDto addDto = new BillConfigAddDto();
        addDto.setSourceType("testSourceType");
        addDto.setSourceName("testSourceName");
        addDto.setHttpService("testHttpService");

        // 执行请求
        String resultStr = sendPostRequest(BASE_URL, addDto, ADMIN_ROLE);

        // 验证结果
        BillConfigEntity result = parseJson(resultStr, BillConfigEntity.class);
        assertThat(result).isNotNull();
        assertThat(result.getSourceType()).isEqualTo(addDto.getSourceType());
        assertThat(result.getSourceName()).isEqualTo(addDto.getSourceName());
    }

    @Test
    @SneakyThrows
    @DisplayName("正常更新账单配置")
    void updateBillConfig_ok() {
        // 准备测试数据
        BillConfigEntity entity = mockAddBillConfig();
        BillConfigUpdateDto updateDto = new BillConfigUpdateDto();
        updateDto.setSourceType("updatedSourceType");
        updateDto.setSourceName("updatedSourceName");
        updateDto.setHttpService("updatedHttpService");
        updateDto.setDelFlag("1");

        // 执行请求
        String resultStr = sendPutRequest(formatUrl(BASE_URL + "/{id}", entity.getId()), updateDto, ADMIN_ROLE);

        // 验证结果 - 查询更新后的数据进行验证
        BillConfigEntity result = parseJson(resultStr, BillConfigEntity.class);
        assertThat(result).isNotNull();
        // 验证更新的字段
        assertThat(result.getSourceType()).isEqualTo(updateDto.getSourceType());
        assertThat(result.getSourceName()).isEqualTo(updateDto.getSourceName());
        assertThat(result.getHttpService()).isEqualTo(updateDto.getHttpService());
    }

    @Test
    @SneakyThrows
    @DisplayName("正常删除账单配置")
    void deleteBillConfig_ok() {
        BillConfigEntity entity = mockAddBillConfig();

        String resultStr = sendDeleteRequest(formatUrl(BASE_URL + "/{id}", entity.getId()), ADMIN_ROLE);

        BillConfigEntity result = parseJson(resultStr, BillConfigEntity.class);
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(entity.getId());
    }

    private BillConfigListDto toQuery(BillConfigEntity addBillConfig) {
        BillConfigListDto billConfigListDto = new BillConfigListDto();
        billConfigListDto.setSourceType(addBillConfig.getSourceType());
        billConfigListDto.setSourceName(addBillConfig.getSourceName());
        billConfigListDto.setDelFlag(addBillConfig.getDelFlag());
        billConfigListDto.setHttpService(addBillConfig.getHttpService());
        return billConfigListDto;
    }

    private BillConfigEntity mockAddBillConfig() {
        BillConfigAddDto addDto = RandomUtil.randomObject(BillConfigAddDto.class);
        return billConfigService.addBillConfig(addDto);
    }

}