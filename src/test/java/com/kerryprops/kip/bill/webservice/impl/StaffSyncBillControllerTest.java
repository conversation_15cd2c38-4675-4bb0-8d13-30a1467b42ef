package com.kerryprops.kip.bill.webservice.impl;

import com.alibaba.fastjson.JSON;
import com.kerryprops.kip.bill.common.constants.BillConstants;
import com.kerryprops.kip.bill.common.enums.SendBillStatus;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.utils.jde.OracleJdbc;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.config.DataMigrationConfig;
import com.kerryprops.kip.bill.dao.BillEmailTraceRepository;
import com.kerryprops.kip.bill.dao.BillRepository;
import com.kerryprops.kip.bill.dao.entity.BillEmailTrace;
import com.kerryprops.kip.bill.dao.entity.BillEntity;
import com.kerryprops.kip.bill.feign.clients.FileClient;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.clients.MessageClient;
import com.kerryprops.kip.bill.feign.entity.EmailSendCommand;
import com.kerryprops.kip.bill.feign.entity.UploadPublicFileResponse;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.IBillConfigService;
import com.kerryprops.kip.bill.service.IBillService;
import com.kerryprops.kip.bill.service.model.leg.Bill;
import com.kerryprops.kip.bill.webservice.vo.req.EmailResultVo;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffBillReceiverRespVo;
import com.kerryprops.kip.hiveas.feign.dto.BuildingDTO;
import com.kerryprops.kip.hiveas.webservice.vo.resp.ProjectBuildingVO;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.query.sql.internal.NativeQueryImpl;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.kerryprops.kip.bill.utils.RandomUtil.randomLoginUser;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomString;
import static java.util.Collections.singletonList;
import static org.apache.commons.collections4.IterableUtils.emptyIterable;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * StaffSyncBillControllerTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-04-02
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("同步B端账单-单元测试")
class StaffSyncBillControllerTest {

    private static final String MOCK_BILL_PROJECTS = "192,180,185";

    private static final String MOCK_SQL = "SELECT * FROM table";

    private static final String MOCK_CODE = "369000";


    @Mock
    private IBillConfigService billConfigService;

    @Mock
    private DataMigrationConfig dataMigrationConfig;

    @Mock
    private HiveAsClient hiveAsClient;

    @Mock
    private EntityManager em;

    @Mock
    private FileClient fileClient;

    @Mock
    private MessageClient messageClient;

    @Mock
    private IBillService billService;

    @Mock
    private BillEmailTraceRepository emailTraceRepository;

    @Mock
    private BillRepository billRepository;

    @InjectMocks
    private StaffSyncBillController staffSyncBillController;

    @Test
    @DisplayName("定时任务同步，正常场景")
    void sync_ok() throws SQLException {
        // Arrange
        setUpProjectInfo();
        when(fileClient.uploadPrivateFile(anyString())).thenReturn(UploadPublicFileResponse.builder()
                                                                                           .url("testUrl")
                                                                                           .build());
        try (MockedStatic<OracleJdbc> mocked = Mockito.mockStatic(OracleJdbc.class)) {
            // Mock返回账单数据
            setMockSqlOK(mocked);

            // Act
            RespWrapVo<Boolean> response = staffSyncBillController.sync();

            // Assert
            assertEquals(Boolean.TRUE, response.getData());
        }
    }

    @Test
    @DisplayName("定时任务同步，异常场景：第一次获取OracleConnection异常")
    void sync_getOracleConnectionFirstTimeError_ShouldReturnFalse() throws SQLException {
        // Arrange
        setUpProjectInfo();
        try (MockedStatic<OracleJdbc> mocked = Mockito.mockStatic(OracleJdbc.class)) {
            mockSqlFirstThrow(mocked);

            // Act
            RespWrapVo<Boolean> response = staffSyncBillController.sync();

            // Assert
            assertEquals(Boolean.FALSE, response.getData());
        }
    }

    @Test
    @DisplayName("定时任务同步，异常场景：第二次获取OracleConnection异常")
    void sync_getOracleConnectionSecondTimeError_ShouldReturnFalse() throws SQLException {
        // Arrange
        setUpProjectInfo();
        try (MockedStatic<OracleJdbc> mocked = Mockito.mockStatic(OracleJdbc.class)) {
            // Mock返回账单数据
            mockSqlSecondThrow(mocked);

            // Act
            RespWrapVo<Boolean> response = staffSyncBillController.sync();

            // Assert
            assertEquals(Boolean.TRUE, response.getData());
            verify(messageClient).sendWithReplyAlicloud(any(EmailSendCommand.class));
        }
    }


    @Test
    @DisplayName("定时任务同步，异常场景：Oracle读数报错")
    void sync_getOracleResultSetError_ShouldReturnFalse() throws SQLException {
        // Arrange
        setUpProjectInfo();
        try (MockedStatic<OracleJdbc> mocked = Mockito.mockStatic(OracleJdbc.class)) {
            // Mock返回账单数据
            setMockSqlThrow(mocked);

            // Act
            RespWrapVo<Boolean> response = staffSyncBillController.sync();

            // Assert
            assertEquals(Boolean.TRUE, response.getData());
            verify(messageClient).sendWithReplyAlicloud(any(EmailSendCommand.class));
        }
    }

    @Test
    @DisplayName("定时任务同步，异常场景：BU为空")
    void sync_buStringEmptyError_ShouldReturnFalse() {
        // Arrange
        when(dataMigrationConfig.getProjects()).thenReturn(null);

        // Act
        RespWrapVo<Boolean> response = staffSyncBillController.sync();

        // Assert
        assertEquals(Boolean.FALSE, response.getData());
    }

    @Test
    @DisplayName("定时任务同步，异常场景：账单读取来源没有配置")
    void sync_billSourceEmptyError_ShouldReturnFalse() {
        // Arrange
        when(dataMigrationConfig.getProjects()).thenReturn(MOCK_BILL_PROJECTS);

        var buildingDTO1 = new BuildingDTO();
        buildingDTO1.setPropertyManagementBU("PM_BU1");
        buildingDTO1.setPropertyDeveloperBU("PD_BU1");

        var projectBuilding = new ProjectBuildingVO();
        projectBuilding.setBuildings(List.of(buildingDTO1));
        List<ProjectBuildingVO> projectBuildingList = List.of(projectBuilding);

        when(hiveAsClient.getCenterByIds(any())).thenReturn(projectBuildingList);
        when(billConfigService.getBillConfigList()).thenReturn(Collections.emptyMap());

        // Act & Assert
        assertThrows(RuntimeException.class, () -> staffSyncBillController.sync());
    }

    @Test
    @DisplayName("手动同步，正常场景")
    void syncManu_ok() throws SQLException {
        // Arrange
        setUpProjectInfo();
        when(fileClient.uploadPrivateFile(anyString())).thenReturn(UploadPublicFileResponse.builder()
                                                                                           .url("testUrl")
                                                                                           .build());

        try (MockedStatic<OracleJdbc> mocked = Mockito.mockStatic(OracleJdbc.class)) {
            // Mock返回账单数据
            setMockSqlOK(mocked);

            // Act
            RespWrapVo<Boolean> response = staffSyncBillController.sync(0, "2025-04-03");

            // Assert
            assertEquals(Boolean.TRUE, response.getData());
        }
    }

    @Test
    @DisplayName("手动同步，异常场景：第一次获取OracleConnection异常")
    void syncManu_getOracleConnectionFirstTimeError_ShouldReturnFalse() throws SQLException {
        // Arrange
        setUpProjectInfo();
        try (MockedStatic<OracleJdbc> mocked = Mockito.mockStatic(OracleJdbc.class)) {
            // Mock返回账单数据
            mockSqlFirstThrow(mocked);

            // Act
            RespWrapVo<Boolean> response = staffSyncBillController.sync(0, "2025-04-03");

            // Assert
            assertEquals(Boolean.FALSE, response.getData());
        }
    }

    @Test
    @DisplayName("手动同步，异常场景：第二次获取OracleConnection异常")
    void syncManu_getOracleConnectionSecondTimeError_ShouldReturnFalse() throws SQLException {
        // Arrange
        setUpProjectInfo();
        try (MockedStatic<OracleJdbc> mocked = Mockito.mockStatic(OracleJdbc.class)) {
            // Mock返回账单数据
            mockSqlSecondThrow(mocked);

            // Act
            RespWrapVo<Boolean> response = staffSyncBillController.sync(0, "2025-04-03");

            // Assert
            assertEquals(Boolean.TRUE, response.getData());
            verify(messageClient).sendWithReplyAlicloud(any(EmailSendCommand.class));

        }
    }

    @Test
    @DisplayName("手动同步，异常场景：Oracle读数报错")
    void syncManu_getOracleResultSetError_ShouldReturnFalse() throws SQLException {
        // Arrange
        setUpProjectInfo();
        try (MockedStatic<OracleJdbc> mocked = Mockito.mockStatic(OracleJdbc.class)) {
            // Mock返回账单数据
            setMockSqlThrow(mocked);

            // Act
            RespWrapVo<Boolean> response = staffSyncBillController.sync(0, "2025-04-03");

            // Assert
            assertEquals(Boolean.TRUE, response.getData());
            verify(messageClient).sendWithReplyAlicloud(any(EmailSendCommand.class));
        }
    }

    @Test
    @DisplayName("手动同步，异常场景：BU为空")
    void syncManu_buStringEmptyError_ShouldReturnFalse() {
        // Arrange
        when(dataMigrationConfig.getProjects()).thenReturn(null);


        // Act
        RespWrapVo<Boolean> response = staffSyncBillController.sync(0, "2025-04-03");

        // Assert
        assertEquals(Boolean.FALSE, response.getData());
    }

    @Test
    @DisplayName("手动同步，异常场景：账单读取来源没有配置")
    void syncManu_billSourceEmptyError_ShouldReturnFalse() {
        // Arrange
        when(dataMigrationConfig.getProjects()).thenReturn(MOCK_BILL_PROJECTS);

        var buildingDTO1 = new BuildingDTO();
        buildingDTO1.setPropertyManagementBU("PM_BU1");
        buildingDTO1.setPropertyDeveloperBU("PD_BU1");

        var projectBuilding = new ProjectBuildingVO();
        projectBuilding.setBuildings(List.of(buildingDTO1));
        List<ProjectBuildingVO> projectBuildingList = List.of(projectBuilding);

        when(hiveAsClient.getCenterByIds(any())).thenReturn(projectBuildingList);
        when(billConfigService.getBillConfigList()).thenReturn(Collections.emptyMap());

        // Act & Assert
        assertThrows(RuntimeException.class, () -> staffSyncBillController.sync(0, "2025-04-03"));
    }

    @Test
    @DisplayName("同步邮件发送状态：异常场景，查询邮件状态异常")
    void syncEmailStatusr_getSendStatusError_ShouldReturnTrue() {
        // Arrange
        var billEmailTrace = new BillEmailTrace();
        billEmailTrace.setRequestId("435324324");
        when(emailTraceRepository.findAll(any(BooleanExpression.class))).thenReturn(List.of(billEmailTrace));

        when(messageClient.getSendStatus(anyString())).thenThrow(new RuntimeException("Network error"));

        // Act
        RespWrapVo<Boolean> respWrapVoActual =
                staffSyncBillController.syncEmailStatus(Collections.emptyList(), Boolean.TRUE);

        // Assert
        assertEquals(Boolean.TRUE, respWrapVoActual.getData());
    }

    @Test
    @DisplayName("同步邮件发送状态：正常场景")
    void syncEmailStatusr_success_ShouldReturnTrue() {
        // Arrange
        var billEmailTrace = new BillEmailTrace();
        billEmailTrace.setRequestId("435324324");
        when(emailTraceRepository.findAll(any(BooleanExpression.class))).thenReturn(List.of(billEmailTrace));

        RespWrapVo<EmailResultVo> emailResultVo = new RespWrapVo<>(new EmailResultVo());
        when(messageClient.getSendStatus(anyString())).thenReturn(emailResultVo);

        // Act
        RespWrapVo<Boolean> respWrapVoActual =
                staffSyncBillController.syncEmailStatus(Collections.emptyList(), Boolean.TRUE);

        // Assert
        assertEquals(Boolean.TRUE, respWrapVoActual.getData());
    }

    @Test
    @DisplayName("同步邮件发送状态：邮箱发送请求id列表为空异常")
    void syncEmailStatusr_requestIdListEmpty_ShouldReturnTrue() {
        // Arrange
        when(emailTraceRepository.findAll(any(BooleanExpression.class))).thenReturn(Collections.emptyList());

        // Act
        RespWrapVo<Boolean> respWrapVoActual =
                staffSyncBillController.syncEmailStatus(Collections.emptyList(), Boolean.TRUE);

        // Assert
        assertEquals(Boolean.FALSE, respWrapVoActual.getData());
    }

    @Test
    @DisplayName("同步邮件发送状态：账单为空")
    void syncBillEmailStatus_WhenNoBillsFound_ShouldReturnSuccess() {
        // Arrange
        when(billRepository.findAll(any(Predicate.class))).thenReturn(emptyIterable());

        // Act
        RespWrapVo<Boolean> result = staffSyncBillController.syncBillEmailStatus(null);

        // Assert
        assertTrue(result.getData());
    }

    @Test
    @DisplayName("同步邮件发送状态：邮件发送用户信息emailError为空")
    void processBill_WithEmptyEmailError_ShouldRevertStatus() {
        // Arrange
        BillEntity bill = createTestBill(1L, null);
        when(billRepository.findAll(any(Predicate.class))).thenReturn(singletonList(bill));

        // Act
        staffSyncBillController.syncBillEmailStatus(null);

        // Assert
        verify(billRepository).updateEmailStatus(eq(1L), eq(BillConstants.MSG_NOT_SEND),
                                                 eq(BillConstants.MSG_NOT_SEND));
    }

    @Test
    @DisplayName("同步邮件发送状态：正常场景")
    void syncBillEmailStatus_WithValidBillId_ShouldProcessSingleBill() {
        // Arrange
        Long billId1 = 122L;
        BillEntity mockBill1 = createTestBill(billId1, createMockEmailError(SendBillStatus.SEND_SUCCESS, 2));

        Long billId2 = 123L;
        BillEntity mockBill2 = createTestBill(billId2, createMockEmailError(SendBillStatus.SEND_FAIL, 1));
        when(billRepository.findAll(any(Predicate.class))).thenReturn(List.of(mockBill1, mockBill2));

        // Act
        RespWrapVo<Boolean> result = staffSyncBillController.syncBillEmailStatus(null);

        // Assert
        assertTrue(result.getData());
        verify(billRepository).updateEmailStatus(eq(billId1), eq(BillConstants.MSG_SUCCESS));
        verify(billRepository).updateEmailStatus(eq(billId2), eq(BillConstants.MSG_FAILURE));
    }


    @Test
    @DisplayName("同步邮件发送状态：正常场景")
    void processBill_WithPendingRequests_ShouldQueryStatus() {
        // Arrange
        String requestId = "123";
        String emailErr = createMockEmailErrorWithPending(requestId);
        BillEntity bill = createTestBill(1L, emailErr);
        when(billRepository.findAll(any(Predicate.class))).thenReturn(singletonList(bill));
        when(messageClient.getSendStatus(requestId)).thenReturn(new RespWrapVo<>(new EmailResultVo()));

        // Act
        staffSyncBillController.syncBillEmailStatus(null);

        // Assert
        verify(messageClient).getSendStatus(requestId);
        verify(billService).emailCallBack(any(EmailResultVo.class));
    }

    @Test
    @DisplayName("同步邮件发送状态：messageClient服务调用异常")
    void processBill_WhenEmailCheckFails_ShouldLogError() {
        // Arrange
        String requestId = "456";
        String emailErr = createMockEmailErrorWithPending(requestId);
        BillEntity bill = createTestBill(1L, emailErr);
        when(billRepository.findAll(any(Predicate.class))).thenReturn(singletonList(bill));
        when(messageClient.getSendStatus(requestId)).thenThrow(new RuntimeException("Network error"));

        // Act
        RespWrapVo<Boolean> result = staffSyncBillController.syncBillEmailStatus(null);

        // Assert
        assertTrue(result.getData());
    }

    @Test
    @DisplayName("异常场景：权限不通过")
    void whenInvalidCode_thenThrowException() {
        // 鉴权code为空
        assertThrows(RuntimeException.class, () -> staffSyncBillController.queryDic(MOCK_SQL, null));

        // 鉴权code错误
        assertThrows(RuntimeException.class, () -> staffSyncBillController.queryDic(MOCK_SQL, MOCK_SQL));
    }

    @Test
    @DisplayName("异常场景：空SQL")
    void whenEmptySql_thenReturnFalse() {
        String result = staffSyncBillController.queryDic("", MOCK_CODE);
        assertEquals("false", result);
    }

    @Test
    @DisplayName("异常场景：非Select语句")
    void whenNonSelectSql_thenReturnFalse() {
        String result = staffSyncBillController.queryDic("update table set col=1", MOCK_CODE);
        assertEquals("false", result);
    }

    @Test
    @DisplayName("正常场景")
    void whenValidSelect_thenReturnFormattedResult() {
        // Arrange
        List<Object[]> resultList = Arrays.asList(new Object[]{1, "data1"}, new Object[]{2, "data2"});
        NativeQueryImpl query = mock(NativeQueryImpl.class);
        when(query.getResultList()).thenReturn(resultList);

        when(em.createNativeQuery(anyString())).thenReturn(query);

        // Act
        String actual = staffSyncBillController.queryDic("select * from table", MOCK_CODE);

        // Assert
        String expected = "查询结果：\r\n\r\n   0: [1, data1]\r\n\r\n   1: [2, data2]\r\n\r\n";
        assertEquals(expected, actual);
    }

    @Test
    @DisplayName("异常场景：数据库返回结果为null")
    void whenResultNull_thenUseToString() {
        // Arrange
        NativeQueryImpl query = mock(NativeQueryImpl.class);
        when(query.getResultList()).thenReturn(null);

        when(em.createNativeQuery(anyString())).thenReturn(query);

        // Act
        assertThrows(NullPointerException.class, () -> staffSyncBillController.queryDic(MOCK_SQL, MOCK_CODE));
    }

    @Test
    @DisplayName("正常场景：执行SQL删除成功")
    void queryDicDeleteDelete_success() {
        // Arrange
        String sql = "DELETE FROM table WHERE id = 1";
        String code = "369000";
        Query mockQuery = mock(Query.class);
        when(mockQuery.executeUpdate()).thenReturn(1);

        when(em.createNativeQuery(sql)).thenReturn(mockQuery);

        // Act
        String result = staffSyncBillController.queryDicDeleteDelete(sql, code);

        // Assert
        assertEquals("true", result);
        verify(mockQuery, times(1)).executeUpdate();
    }

    @Test
    @DisplayName("异常场景：SQL为空")
    void queryDicDeleteDelete_emptySql() {
        // Arrange
        String sql = "";
        String code = "369000";

        // Act
        String result = staffSyncBillController.queryDicDeleteDelete(sql, code);

        // Assert
        assertEquals("false", result);
    }

    @Test
    @DisplayName("异常场景：Code无效")
    void queryDicDeleteDelete_invalidCode() {
        // Arrange
        String sql = "DELETE FROM table WHERE id = 1";
        String code = StringUtils.EMPTY;

        // Act & Assert
        assertThrows(RuntimeException.class, () -> staffSyncBillController.queryDicDeleteDelete(sql, code));
    }

    @Test
    @DisplayName("正常场景：保存账单成功，已有最新账单，不需要保存")
    void saveBill_existingNewBills_success() throws Exception {
        // Arrange
        Bill bill = new Bill();
        bill.setId(1L);
        bill.setTpCrtutime(new Date());

        Map<String, String> billSource = new HashMap<>();
        billSource.put("sourceKey", "sourceValue");

        var billEntity = new BillEntity();
        billEntity.setTpCrtutime(new Date(System.currentTimeMillis() + 1000L)); // 更加新的时间
        when(billService.selectDuplicateBill(any())).thenReturn(List.of(billEntity));

        Method method = StaffSyncBillController.class.getDeclaredMethod("saveBill", Bill.class, Map.class);
        method.setAccessible(true);

        // Act
        int result = (int) method.invoke(staffSyncBillController, bill, billSource);

        // Assert
        assertEquals(0, result);
    }

    @Test
    @DisplayName("正常场景：保存账单成功，无最新账单，需要保存")
    void saveBill_notExistingNewBills_success() throws Exception {
        // Arrange
        Bill bill = new Bill();
        bill.setId(1L);
        bill.setTpCrtutime(new Date(System.currentTimeMillis() + 1000L)); // 更新的时间

        Map<String, String> billSource = new HashMap<>();
        billSource.put("sourceKey", "sourceValue");

        var billEntity = new BillEntity();
        billEntity.setTpCrtutime(new Date());
        billEntity.setTpStatus(0);
        when(billService.selectDuplicateBill(any())).thenReturn(new ArrayList<>(List.of(billEntity)));

        Method method = StaffSyncBillController.class.getDeclaredMethod("saveBill", Bill.class, Map.class);
        method.setAccessible(true);

        // Act
        int result = (int) method.invoke(staffSyncBillController, bill, billSource);

        // Assert
        assertEquals(0, result);
    }

    @Test
    @DisplayName("上传文件到OSS，异常场景，报错")
    void uploadFileToOss_error() throws Exception {
        // Arrange
        Method method = StaffSyncBillController.class.getDeclaredMethod("uploadFileToOss", String.class);
        method.setAccessible(true);

        // Act
        String result = (String) method.invoke(staffSyncBillController, randomString());

        // Assert
        assertNull(result);
    }

    void setUpProjectInfo() {
        var buildingDTO1 = new BuildingDTO();
        buildingDTO1.setPropertyManagementBU("PM_BU1");
        buildingDTO1.setPropertyDeveloperBU("PD_BU1");

        var buildingDTO2 = new BuildingDTO();
        buildingDTO2.setPropertyManagementBU("PM_BU2");
        buildingDTO2.setPropertyDeveloperBU("PD_BU2");

        var projectBuilding1 = new ProjectBuildingVO();
        projectBuilding1.setBuildings(List.of(buildingDTO1, buildingDTO2));

        var projectBuilding2 = new ProjectBuildingVO();
        projectBuilding2.setBuildings(List.of(buildingDTO1));
        List<ProjectBuildingVO> projectBuildingList = List.of(projectBuilding1, projectBuilding2);

        when(hiveAsClient.getCenterByIds(any())).thenReturn(projectBuildingList);
        when(billConfigService.getBillConfigList()).thenReturn(Map.of("source1", "service1", "source2", "service2"));
        when(dataMigrationConfig.getProjects()).thenReturn(MOCK_BILL_PROJECTS);
    }

    private void mockSqlFirstThrow(MockedStatic<OracleJdbc> mocked) throws SQLException {
        Statement statement = mock(Statement.class);
        when(statement.executeQuery(anyString())).thenThrow(new RuntimeException());

        Connection conn = mock(Connection.class);
        when(conn.createStatement()).thenReturn(statement);
        mocked.when(() -> OracleJdbc.getOracleConnection())
              .thenReturn(conn);
    }

    private void mockSqlSecondThrow(MockedStatic<OracleJdbc> mocked) throws SQLException {
        ResultSet resultSet = mock(ResultSet.class);
        when(resultSet.next()).thenReturn(true, true, false);
        when(resultSet.getInt(anyString())).thenReturn(1);

        Statement statement = mock(Statement.class);
        when(statement.executeQuery(anyString())).thenReturn(resultSet).thenThrow(new RuntimeException());


        Connection conn = mock(Connection.class);
        when(conn.createStatement()).thenReturn(statement);
        mocked.when(() -> OracleJdbc.getOracleConnection())
              .thenReturn(conn);
    }

    private void setMockSqlOK(MockedStatic<OracleJdbc> mocked) throws SQLException {
        ResultSet resultSet = mock(ResultSet.class);
        when(resultSet.next()).thenReturn(true, true, false);
        when(resultSet.getInt(anyString())).thenReturn(1);
        when(resultSet.getString(anyString())).thenReturn("test");

        Statement statement = mock(Statement.class);
        when(statement.executeQuery(anyString())).thenReturn(resultSet);

        Connection conn = mock(Connection.class);
        when(conn.createStatement()).thenReturn(statement);
        mocked.when(() -> OracleJdbc.getOracleConnection())
              .thenReturn(conn);
    }

    private void setMockSqlThrow(MockedStatic<OracleJdbc> mocked) throws SQLException {
        ResultSet resultSet = mock(ResultSet.class);
        when(resultSet.next()).thenReturn(true, true, false);
        when(resultSet.getInt(anyString())).thenReturn(1);
        when(resultSet.getString(anyString())).thenThrow(new RuntimeException());

        Statement statement = mock(Statement.class);
        when(statement.executeQuery(anyString())).thenReturn(resultSet);

        Connection conn = mock(Connection.class);
        when(conn.createStatement()).thenReturn(statement);
        mocked.when(() -> OracleJdbc.getOracleConnection())
              .thenReturn(conn);
    }

    private BillEntity createTestBill(Long id, String emailErr) {
        BillEntity bill = new BillEntity();
        bill.setId(id);
        bill.setEmailErr(emailErr);
        return bill;
    }

    private String createMockEmailError(SendBillStatus status, int count) {
        List<StaffBillReceiverRespVo> receivers = IntStream.range(0, count)
                                                           .mapToObj(i -> {
                                                               StaffBillReceiverRespVo vo =
                                                                       new StaffBillReceiverRespVo();
                                                               vo.setSendStatus(status.getMessage());
                                                               return vo;
                                                           })
                                                           .collect(Collectors.toList());
        return JSON.toJSONString(receivers);
    }

    private String createMockEmailErrorWithPending(String requestId) {
        StaffBillReceiverRespVo vo = new StaffBillReceiverRespVo();
        vo.setRequestId(requestId);
        vo.setSendStatus(SendBillStatus.SENDING.getMessage());
        return JSON.toJSONString(singletonList(vo));
    }

}