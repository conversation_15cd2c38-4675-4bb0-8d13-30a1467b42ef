package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.utils.DataScopeUtils;
import com.kerryprops.kip.bill.dao.entity.EFapiaoBillInvoice;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.EFapiaoJdeBillService;
import com.kerryprops.kip.bill.service.EFapiaoSendService;
import com.kerryprops.kip.bill.service.KerryBillEFapiaoService;
import com.kerryprops.kip.bill.service.model.s.BillEFapiaoSearchReqBo;
import com.kerryprops.kip.bill.webservice.vo.req.BillInvoiceSearchRequest;
import com.kerryprops.kip.bill.webservice.vo.req.BillInvoiceSendRequest;
import com.kerryprops.kip.bill.webservice.vo.req.BillInvoiceUploadVo;
import com.kerryprops.kip.bill.webservice.vo.req.EmailResultVo;
import com.kerryprops.kip.bill.webservice.vo.req.SmsResultCallbackVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillInvoiceResource;
import com.kerryprops.kip.bill.webservice.vo.resp.BillInvoiceUploadResultExportVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillInvoiceUploadResultRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillPayer;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffBillReceiverRespVo;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.mock.web.MockHttpServletResponse;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.kerryprops.kip.bill.utils.RandomUtil.randomLoginUser;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * StaffBillInvoiceControllerTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-5-7
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("C端开票账单-单元测试")
class StaffBillInvoiceControllerTest {

    @Mock
    private HiveAsClient hiveAsClient;

    @Mock
    private EFapiaoSendService eFapiaoSendService;

    @Mock
    private KerryBillEFapiaoService kerryBillEFapiaoService;

    @Mock
    private EFapiaoJdeBillService eFapiaoJdeBillService;

    @InjectMocks
    private StaffBillInvoiceController staffBillInvoiceController;

    @DisplayName("正常场景：分页查询开票账单列表")
    @Test
    public void testBillInvoiceList_success() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        var request = new BillInvoiceSearchRequest();
        request.setInvoiceStatus("PAID");

        var reqBo = new BillEFapiaoSearchReqBo();
        reqBo.setInvoiceState("PAID");

        Page<BillInvoiceResource> mockPage = new PageImpl<>(List.of(new BillInvoiceResource()));

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = mockStatic(BeanUtil.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), eq(BillEFapiaoSearchReqBo.class)))
                                .thenReturn(reqBo);

            when(kerryBillEFapiaoService.eFapiaoList(eq(reqBo), eq(pageable))).thenReturn(mockPage);

            // Act
            Page<BillInvoiceResource> result = staffBillInvoiceController.billInvoiceList(pageable, request);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getContent()
                                  .size());
            verify(kerryBillEFapiaoService, times(1)).eFapiaoList(eq(reqBo), eq(pageable));
        }
    }

    @DisplayName("正常场景：模糊查询购方名称")
    @Test
    public void testFuzzyQueryPurchaserNames_success() {
        // Arrange
        String projectId = "PROJECT123";
        String query = "Company";

        List<String> busList = List.of("BUS1", "BUS2");

        try (MockedStatic<DataScopeUtils> mockedDataScopeUtils = mockStatic(DataScopeUtils.class)) {
            mockedDataScopeUtils.when(() -> DataScopeUtils.getBusByProjectId(any(), any()))
                                .thenReturn(busList);

            // Act
            staffBillInvoiceController.fuzzyQueryPurchaserNames(projectId, query);

            // Assert
            verify(kerryBillEFapiaoService, times(1)).fuzzyQueryPurchaserNames(eq(busList), eq(query));

        }
    }

    @DisplayName("正常场景：成功发送开票账单邮件")
    @Test
    public void testSendBillInvoiceEmail_success() {
        // Arrange
        var request = new BillInvoiceSendRequest();
        request.setProjectId("PROJECT123");
        request.setInvoiceIds(Set.of(1L, 2L));

        StaffBillReceiverRespVo receiver = new StaffBillReceiverRespVo();
        receiver.setEmail("<EMAIL>");

        EFapiaoBillInvoice invoice = new EFapiaoBillInvoice();
        invoice.setId(1L);
        invoice.setAmountWithTax(BigDecimal.valueOf(100.00));

        Map<StaffBillReceiverRespVo, Set<EFapiaoBillInvoice>> receiverInvoiceMap = Map.of(receiver, Set.of(invoice));
        when(eFapiaoSendService.invoiceReceivers(eq("PROJECT123"), eq(Set.of(1L, 2L)))).thenReturn(receiverInvoiceMap);

        // Act
        Integer result = staffBillInvoiceController.sendBillInvoiceEmail(request);

        // Assert
        assertNotNull(result);
        assertEquals(1, result);
        verify(eFapiaoSendService, times(1)).invoiceReceivers(eq("PROJECT123"), eq(Set.of(1L, 2L)));
        verify(eFapiaoSendService, times(1)).notifyByEmail(eq("PROJECT123"), eq(receiver), anySet(), anyMap());
    }

    @DisplayName("异常场景：查询业务单异常")
    @Test
    public void testUploadBillInvoice_queryBillError() {
        // Arrange
        var uploadVo = new BillInvoiceUploadVo();
        uploadVo.setKco("KCO123");
        uploadVo.setBillType("TYPE1");
        uploadVo.setDoc(19004780);
        uploadVo.setPaymentItem("ITEM789");

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = mockStatic(BeanUtil.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), eq(BillInvoiceUploadResultRespVo.class)))
                                .thenThrow(new RuntimeException());

            // Act
            BillInvoiceUploadResultRespVo result = staffBillInvoiceController.uploadBillInvoice(uploadVo);

            // Assert
            assertNotNull(result);
            verify(eFapiaoJdeBillService, never()).queryBySalesBillNo(anyString());
        }
    }

    @DisplayName("发送发票，站内信发送，正常场景")
    @Test
    void sendBillInvoiceMessage() {
        assertDoesNotThrow(() -> staffBillInvoiceController.sendBillInvoiceMessage(new BillInvoiceSendRequest()));
    }

    @Test
    @DisplayName("exportBillInvoiceResultList - 正常场景：导出成功")
    void exportBillInvoiceResultList_success() {
        // Arrange
        List<BillInvoiceUploadResultExportVo> resultList = List.of(new BillInvoiceUploadResultExportVo());
        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = mockStatic(BeanUtil.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new Object());

            // Act and Assert
            assertDoesNotThrow(() -> staffBillInvoiceController.exportBillInvoiceResultList(resultList, new MockHttpServletResponse()));
        }
    }

    @Test
    @DisplayName("handleSmsCallback - 正常场景：处理短信回调成功")
    void handleSmsCallback_success() {
        // Arrange
        var smsResultCallbackVo = new SmsResultCallbackVo();

        // Act
        staffBillInvoiceController.handleSmsCallback(smsResultCallbackVo);

        // Assert
        verify(kerryBillEFapiaoService, times(1)).handleSmsCallback(smsResultCallbackVo);
    }

    @Test
    @DisplayName("handleEmailCallback - 正常场景：处理邮件回调成功")
    void handleEmailCallback_success() {
        // Arrange
        var emailResultVo = new EmailResultVo();

        // Act
        staffBillInvoiceController.handleEmailCallback(emailResultVo);

        // Assert
        verify(kerryBillEFapiaoService, times(1)).handleEmailCallback(emailResultVo);
    }

}