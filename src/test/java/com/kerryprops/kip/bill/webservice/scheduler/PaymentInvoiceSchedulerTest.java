package com.kerryprops.kip.bill.webservice.scheduler;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.dao.AptPayRepository;
import com.kerryprops.kip.bill.dao.AptPaymentInfoRepository;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.feign.clients.KipInvoiceClient;
import com.kerryprops.kip.bill.feign.entity.InvoiceVo;
import com.kerryprops.kip.bill.service.impl.InvoiceApplicationService;
import jakarta.persistence.EntityManager;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.List;
import java.util.stream.Stream;

import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;

/**
 * PaymentInvoiceScheduler单元测试类
 *
 * <AUTHOR>
 * @date 2024-10-16
 */
class PaymentInvoiceSchedulerTest extends BaseIntegrationTest {

    @Mock
    private EntityManager entityManager;

    @Mock
    private AptPaymentInfoRepository aptPaymentInfoRepository;

    @Mock
    private AptPayRepository aptPayRepository;

    @Mock
    private KipInvoiceClient kipInvoiceClient;

    @Mock
    private InvoiceApplicationService invoiceApplicationService;

    @InjectMocks
    private PaymentInvoiceScheduler paymentInvoiceScheduler;

    @Test
    void _01_pullInvoicedStatus_success() {
        AptPaymentInfo aptPaymentInfoTest = new AptPaymentInfo();
        aptPaymentInfoTest.setId(randomString());
        doReturn(Stream.of(aptPaymentInfoTest)).when(aptPaymentInfoRepository).streamAllByInvoicedStatus(ArgumentMatchers.any());

        AptPay aptPayTest = new AptPay();
        aptPayTest.setPaymentInfoId(randomString());
        doReturn(Stream.of(aptPayTest)).when(aptPayRepository).streamAllByInvoicedStatus(ArgumentMatchers.any());

        doNothing().when(entityManager).detach(ArgumentMatchers.any());
        doReturn(List.of(new InvoiceVo())).when(kipInvoiceClient).listBillingInvoice(ArgumentMatchers.any());
        doNothing().when(invoiceApplicationService).updateInvoicedStatus(ArgumentMatchers.any(), ArgumentMatchers.any());

        // 执行待验证部分
        Assertions.assertDoesNotThrow(() -> paymentInvoiceScheduler.pullInvoicedStatus());
    }

}