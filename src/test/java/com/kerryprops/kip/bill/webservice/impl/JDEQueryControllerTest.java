package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.JDEQueryService;
import com.kerryprops.kip.bill.webservice.impl.JDEQueryController.ErrorResponse;
import com.kerryprops.kip.bill.webservice.impl.JDEQueryController.JDEQueryRequest;
import com.kerryprops.kip.bill.webservice.impl.JDEQueryController.QueryResult;
import com.kerryprops.kip.bill.webservice.impl.JDEQueryController.QueryResultResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.sql.SQLException;
import java.sql.SQLSyntaxErrorException;
import java.sql.SQLTimeoutException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for {@link JDEQueryController}.
 */
@ExtendWith(MockitoExtension.class)
class JDEQueryControllerTest {

    @Mock
    private JDEQueryService jdeQueryService;

    @InjectMocks
    private JDEQueryController jdeQueryController;

    @Test
    void shouldReturnDataSuccessfully() {
        // Arrange
        JDEQueryRequest validRequest = new JDEQueryRequest("SELECT * FROM test_table");
        int page = 0;
        int size = 100;
        List<Map<String, Object>> mockData = List.of(
                Map.of("ID", 1, "NAME", "Test 1"),
                Map.of("ID", 2, "NAME", "Test 2")
        );
        QueryResult mockQueryResult = new QueryResult(mockData);

        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::isSuperAdmin).thenReturn(true);
            
            try {
                doReturn(mockQueryResult).when(jdeQueryService).executeQuery(anyString(), anyInt());
            } catch (SQLException e) {
                fail("Mock setup failed: " + e.getMessage());
            }

            // Act
            ResponseEntity<?> response = jdeQueryController.getJDEData(validRequest, page, size);

            // Assert
            assertEquals(HttpStatus.OK, response.getStatusCode());
            assertInstanceOf(QueryResultResponse.class, response.getBody());
            
            QueryResultResponse resultResponse = (QueryResultResponse) response.getBody();
            assertEquals(2, resultResponse.data().size());
            assertEquals(2, resultResponse.count());
            assertEquals(0, resultResponse.page());
            assertEquals(100, resultResponse.size());
            assertEquals(1, resultResponse.totalPages());
            
            try {
                verify(jdeQueryService).executeQuery(contains("SELECT * FROM (SELECT a.*, ROWNUM"), eq(300));
            } catch (SQLException e) {
                fail("Verification failed: " + e.getMessage());
            }
        }
    }

    @Test
    void shouldReturnNotFoundForEmptyResults() {
        // Arrange
        JDEQueryRequest validRequest = new JDEQueryRequest("SELECT * FROM test_table");
        QueryResult emptyResult = new QueryResult(Collections.emptyList());

        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::isSuperAdmin).thenReturn(true);
            
            try {
                doReturn(emptyResult).when(jdeQueryService).executeQuery(anyString(), anyInt());
            } catch (SQLException e) {
                fail("Mock setup failed: " + e.getMessage());
            }

            // Act
            ResponseEntity<?> response = jdeQueryController.getJDEData(validRequest, 0, 100);

            // Assert
            assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
            assertInstanceOf(ErrorResponse.class, response.getBody());
            assertEquals("No data found for the query", ((ErrorResponse) response.getBody()).message());
        }
    }

    @Test
    void shouldReturnForbiddenWhenNotAdmin() {
        // Arrange
        JDEQueryRequest validRequest = new JDEQueryRequest("SELECT * FROM test_table");
        
        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::isSuperAdmin).thenReturn(false);

            // Act
            ResponseEntity<?> response = jdeQueryController.getJDEData(validRequest, 0, 100);

            // Assert
            assertEquals(HttpStatus.FORBIDDEN, response.getStatusCode());
            assertInstanceOf(ErrorResponse.class, response.getBody());
            assertEquals("Access denied: Administrator privileges required", ((ErrorResponse) response.getBody()).message());
            
            try {
                verify(jdeQueryService, never()).executeQuery(anyString(), anyInt());
            } catch (SQLException e) {
                fail("Verification failed: " + e.getMessage());
            }
        }
    }

    @Test
    void shouldReturnBadRequestForEmptySql() {
        // Arrange
        JDEQueryRequest emptyRequest = new JDEQueryRequest("");
        
        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::isSuperAdmin).thenReturn(true);

            // Act
            ResponseEntity<?> response = jdeQueryController.getJDEData(emptyRequest, 0, 100);

            // Assert
            assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
            assertInstanceOf(ErrorResponse.class, response.getBody());
            assertEquals("Query cannot be empty", ((ErrorResponse) response.getBody()).message());
        }
    }

    @Test
    void shouldReturnBadRequestForNullSql() {
        // Arrange
        JDEQueryRequest nullSqlRequest = new JDEQueryRequest(null);
        
        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::isSuperAdmin).thenReturn(true);

            // Act
            ResponseEntity<?> response = jdeQueryController.getJDEData(nullSqlRequest, 0, 100);

            // Assert
            assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
            assertInstanceOf(ErrorResponse.class, response.getBody());
            assertEquals("Query cannot be empty", ((ErrorResponse) response.getBody()).message());
        }
    }

    @Test
    void shouldReturnBadRequestForNonSelectQuery() {
        // Arrange
        JDEQueryRequest insertRequest = new JDEQueryRequest("INSERT INTO test_table VALUES (1, 'test')");
        
        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::isSuperAdmin).thenReturn(true);

            // Act
            ResponseEntity<?> response = jdeQueryController.getJDEData(insertRequest, 0, 100);

            // Assert
            assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
            assertInstanceOf(ErrorResponse.class, response.getBody());
            assertEquals("Only SELECT statements are allowed", ((ErrorResponse) response.getBody()).message());
        }
    }

    @Test
    void shouldSanitizePageAndSizeParameters() {
        // Arrange
        JDEQueryRequest validRequest = new JDEQueryRequest("SELECT * FROM test_table");
        int negativePage = -1;
        int oversizedSize = 2000; // Greater than MAX_PAGE_SIZE (1000)
        
        List<Map<String, Object>> mockData = List.of(Map.of("ID", 1, "NAME", "Test"));
        QueryResult mockQueryResult = new QueryResult(mockData);

        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::isSuperAdmin).thenReturn(true);
            
            try {
                doReturn(mockQueryResult).when(jdeQueryService).executeQuery(anyString(), anyInt());
            } catch (SQLException e) {
                fail("Mock setup failed: " + e.getMessage());
            }

            // Act
            ResponseEntity<?> response = jdeQueryController.getJDEData(validRequest, negativePage, oversizedSize);

            // Assert
            assertEquals(HttpStatus.OK, response.getStatusCode());
            QueryResultResponse resultResponse = (QueryResultResponse) response.getBody();
            assertEquals(0, resultResponse.page()); // Negative page corrected to 0
            assertEquals(1000, resultResponse.size()); // Oversized value capped to MAX_PAGE_SIZE
        }
    }

    @Test
    void shouldHandleSQLSyntaxError() {
        // Arrange
        JDEQueryRequest validRequest = new JDEQueryRequest("SELECT * FROM test_table");
        SQLSyntaxErrorException syntaxException = new SQLSyntaxErrorException("Invalid SQL syntax");
        
        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::isSuperAdmin).thenReturn(true);
            
            try {
                doThrow(syntaxException).when(jdeQueryService).executeQuery(anyString(), anyInt());
            } catch (SQLException e) {
                fail("Mock setup failed: " + e.getMessage());
            }

            // Act
            ResponseEntity<?> response = jdeQueryController.getJDEData(validRequest, 0, 100);

            // Assert
            assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
            assertInstanceOf(ErrorResponse.class, response.getBody());
            assertEquals("Invalid SQL syntax: Invalid SQL syntax", ((ErrorResponse) response.getBody()).message());
        }
    }

    @Test
    void shouldHandleSQLTimeout() {
        // Arrange
        JDEQueryRequest validRequest = new JDEQueryRequest("SELECT * FROM test_table");
        SQLTimeoutException timeoutException = new SQLTimeoutException("Query timed out");
        
        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::isSuperAdmin).thenReturn(true);
            
            try {
                doThrow(timeoutException).when(jdeQueryService).executeQuery(anyString(), anyInt());
            } catch (SQLException e) {
                fail("Mock setup failed: " + e.getMessage());
            }

            // Act
            ResponseEntity<?> response = jdeQueryController.getJDEData(validRequest, 0, 100);

            // Assert
            assertEquals(HttpStatus.REQUEST_TIMEOUT, response.getStatusCode());
            assertInstanceOf(ErrorResponse.class, response.getBody());
            assertEquals("Query execution timed out. Please simplify your query.", ((ErrorResponse) response.getBody()).message());
        }
    }

    @Test
    void shouldHandleGeneralSQLException() {
        // Arrange
        JDEQueryRequest validRequest = new JDEQueryRequest("SELECT * FROM test_table");
        SQLException sqlException = new SQLException("Database error");
        
        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::isSuperAdmin).thenReturn(true);
            
            try {
                doThrow(sqlException).when(jdeQueryService).executeQuery(anyString(), anyInt());
            } catch (SQLException e) {
                fail("Mock setup failed: " + e.getMessage());
            }

            // Act
            ResponseEntity<?> response = jdeQueryController.getJDEData(validRequest, 0, 100);

            // Assert
            assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
            assertInstanceOf(ErrorResponse.class, response.getBody());
            assertEquals("Database error: Database error", ((ErrorResponse) response.getBody()).message());
        }
    }

    @Test
    void shouldHandleGeneralException() {
        // Arrange
        JDEQueryRequest validRequest = new JDEQueryRequest("SELECT * FROM test_table");
        RuntimeException unexpectedException = new RuntimeException("Unexpected error");
        
        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::isSuperAdmin).thenReturn(true);
            
            try {
                doThrow(unexpectedException).when(jdeQueryService).executeQuery(anyString(), anyInt());
            } catch (SQLException e) {
                fail("Mock setup failed: " + e.getMessage());
            }

            // Act
            ResponseEntity<?> response = jdeQueryController.getJDEData(validRequest, 0, 100);

            // Assert
            assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
            assertInstanceOf(ErrorResponse.class, response.getBody());
            assertEquals("An unexpected error occurred: Unexpected error", ((ErrorResponse) response.getBody()).message());
        }
    }

    @Test
    void shouldCalculatePaginationCorrectly() {
        // Arrange
        JDEQueryRequest validRequest = new JDEQueryRequest("SELECT * FROM test_table");
        int page = 2;
        int size = 10;
        
        // Create a list with 25 items to test pagination
        List<Map<String, Object>> mockData = Collections.nCopies(5, Map.of("ID", 1, "NAME", "Test"));
        QueryResult mockQueryResult = new QueryResult(mockData, 25); // Total count of 25
        
        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::isSuperAdmin).thenReturn(true);
            
            try {
                doReturn(mockQueryResult).when(jdeQueryService).executeQuery(anyString(), anyInt());
            } catch (SQLException e) {
                fail("Mock setup failed: " + e.getMessage());
            }

            // Act
            ResponseEntity<?> response = jdeQueryController.getJDEData(validRequest, page, size);

            // Assert
            assertEquals(HttpStatus.OK, response.getStatusCode());
            QueryResultResponse resultResponse = (QueryResultResponse) response.getBody();
            assertEquals(2, resultResponse.page());
            assertEquals(10, resultResponse.size());
            assertEquals(3, resultResponse.totalPages()); // 25 items with page size 10 = 3 pages
        }
    }

    @Test
    void shouldExecuteQueryWithParameters() {
        // Arrange
        JDEQueryRequest paramRequest = new JDEQueryRequest("SELECT * FROM test_table WHERE col1 = 'param1' AND col2 = 123");
        
        List<Map<String, Object>> mockData = List.of(Map.of("ID", 1, "NAME", "Test"));
        QueryResult mockQueryResult = new QueryResult(mockData);
        
        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::isSuperAdmin).thenReturn(true);
            
            try {
                doReturn(mockQueryResult).when(jdeQueryService).executeQuery(anyString(), anyInt());
            } catch (SQLException e) {
                fail("Mock setup failed: " + e.getMessage());
            }

            // Act
            ResponseEntity<?> response = jdeQueryController.getJDEData(paramRequest, 0, 100);

            // Assert
            assertEquals(HttpStatus.OK, response.getStatusCode());
            try {
                verify(jdeQueryService).executeQuery(anyString(), anyInt());
            } catch (SQLException e) {
                fail("Verification failed: " + e.getMessage());
            }
        }
    }
    
    @Test
    void shouldHandleZeroPageSize() {
        // Arrange
        JDEQueryRequest validRequest = new JDEQueryRequest("SELECT * FROM test_table");
        int page = 0;
        int size = 0; // Invalid page size
        
        List<Map<String, Object>> mockData = List.of(Map.of("ID", 1, "NAME", "Test"));
        QueryResult mockQueryResult = new QueryResult(mockData, 5); // Total count of 5
        
        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::isSuperAdmin).thenReturn(true);
            
            try {
                doReturn(mockQueryResult).when(jdeQueryService).executeQuery(anyString(), anyInt());
            } catch (SQLException e) {
                fail("Mock setup failed: " + e.getMessage());
            }

            // Act
            ResponseEntity<?> response = jdeQueryController.getJDEData(validRequest, page, size);

            // Assert
            assertEquals(HttpStatus.OK, response.getStatusCode());
            QueryResultResponse resultResponse = (QueryResultResponse) response.getBody();
            assertEquals(1, resultResponse.size()); // Size should be corrected to minimum of 1
            assertEquals(5, resultResponse.totalPages()); // With size=1 and 5 items, there should be 5 pages
        }
    }
}
