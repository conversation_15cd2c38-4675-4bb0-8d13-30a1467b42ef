package com.kerryprops.kip.bill.webservice.impl;

import com.alibaba.excel.exception.ExcelGenerateException;
import com.kerryprops.kip.bill.common.exceptions.RestInvalidParamException;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.dao.AptBillDirectDebitsBatchRepository;
import com.kerryprops.kip.bill.dao.entity.AptBillDirectDebitsBatch;
import com.kerryprops.kip.bill.dao.entity.AptDirectDebitsBatchBill;
import com.kerryprops.kip.bill.service.AptBillDirectDebitsBatchBillService;
import com.kerryprops.kip.bill.service.AptBillDirectDebitsBatchService;
import com.kerryprops.kip.bill.service.model.s.AptBillDirectDebitsBatchBo;
import com.kerryprops.kip.bill.service.model.s.AptBillSearchReqBo;
import com.kerryprops.kip.bill.webservice.vo.req.AptBillSearchReqVo;
import com.kerryprops.kip.bill.webservice.vo.req.DirectDebitsBatchesCreationRequest;
import com.kerryprops.kip.bill.webservice.vo.req.DirectDebitsBatchesQueryRequest;
import com.kerryprops.kip.bill.webservice.vo.req.DirectDebitsDeleteBatchBillsRequest;
import com.kerryprops.kip.bill.webservice.vo.resp.AptDirectDebitsBatchBillExportVo;
import com.kerryprops.kip.bill.webservice.vo.resp.DirectDebitsBatchBillAmountResource;
import com.kerryprops.kip.bill.webservice.vo.resp.DirectDebitsBatchBillDetailResource;
import com.kerryprops.kip.bill.webservice.vo.resp.DirectDebitsBillBatchQueryResource;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import com.querydsl.core.types.Predicate;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.kerryprops.kip.bill.utils.RandomUtil.randomString;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * DirectDebitsBillBatchControllerTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-5-7
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("C端-批量代扣账单-单元测试")
class DirectDebitsBillBatchControllerTest {

    @Mock
    private AptBillDirectDebitsBatchRepository batchRepository;

    @Mock
    private AptBillDirectDebitsBatchBillService batchBillService;

    @Mock
    private AptBillDirectDebitsBatchService directDebitsBatchService;

    @InjectMocks
    private DirectDebitsBillBatchController directDebitsBillBatchController;

    @DisplayName("正常场景：成功获取物业费代扣的批头列表")
    @Test
    public void testGetDirectDebitsBatches_success() {
        // Arrange
        var request = new DirectDebitsBatchesQueryRequest();
        request.setProjectId("PROJECT123");

        var bo = new AptBillDirectDebitsBatchBo();
        bo.setProjectId("PROJECT123");

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), eq(AptBillDirectDebitsBatchBo.class)))
                                .thenReturn(bo);

            // Act
            directDebitsBillBatchController.getDirectDebitsBatches(PageRequest.of(0, 10), request);

            // Assert
            verify(directDebitsBatchService, times(1)).queryDirectDebitsBatches(any(AptBillDirectDebitsBatchBo.class),
                                                                                any(Pageable.class));
        }
    }

    @DisplayName("异常场景：缺少项目ID")
    @Test
    public void testGetDirectDebitsBatches_missingProjectId() {
        // Arrange
        DirectDebitsBatchesQueryRequest request = new DirectDebitsBatchesQueryRequest();
        Pageable pageable = PageRequest.of(0, 10);

        // Act & Assert
        assertThrows(RestInvalidParamException.class,
                     () -> directDebitsBillBatchController.getDirectDebitsBatches(pageable, request));
    }

    @DisplayName("正常场景：成功创建物业费代扣的批头")
    @Test
    public void testCreateDirectDebitsBatch_success() {
        // Arrange
        var request = new DirectDebitsBatchesCreationRequest();
        request.setProjectId("PROJECT123");
        request.setClosingMonth("2023-10");

        DirectDebitsBillBatchQueryResource expectedResponse = new DirectDebitsBillBatchQueryResource();
        expectedResponse.setBatchNo("BATCH001");

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any()))
                                .thenReturn(new AptBillDirectDebitsBatchBo());

            when(directDebitsBatchService.createDirectDebitsBatch(
                    any(DirectDebitsBatchesCreationRequest.class))).thenReturn(expectedResponse);

            // Act
            DirectDebitsBillBatchQueryResource result =
                    directDebitsBillBatchController.createDirectDebitsBatch(request);

            // Assert
            assertNotNull(result);
            assertEquals("BATCH001", result.getBatchNo());
            verify(directDebitsBatchService, times(1)).createDirectDebitsBatch(
                    any(DirectDebitsBatchesCreationRequest.class));
        }
    }

    @DisplayName("异常场景：缺少项目ID")
    @Test
    public void testCreateDirectDebitsBatch_missingProjectId() {
        // Arrange
        var request = new DirectDebitsBatchesCreationRequest();
        request.setProjectId(null);
        request.setClosingMonth("2023-10");

        // Act & Assert
        assertThrows(RestInvalidParamException.class,
                     () -> directDebitsBillBatchController.createDirectDebitsBatch(request));
    }

    @DisplayName("异常场景：缺少结算月份")
    @Test
    public void testCreateDirectDebitsBatch_missingClosingMonth() {
        // Arrange
        DirectDebitsBatchesCreationRequest request = new DirectDebitsBatchesCreationRequest();
        request.setProjectId("PROJECT123");
        request.setClosingMonth(StringUtils.EMPTY);

        // Act & Assert
        assertThrows(RestInvalidParamException.class,
                     () -> directDebitsBillBatchController.createDirectDebitsBatch(request));
    }

    @DisplayName("正常场景：删除物业费代扣的批头")
    @Test
    public void testDeleteDirectDebitsBatches_success() {
        // Act & Assert
        assertDoesNotThrow(() -> directDebitsBillBatchController.deleteDirectDebitsBatches(randomString()));
    }

    @DisplayName("正常场景：获取批次的所有代扣账单列表")
    @Test
    public void testPushDirectDebitsBatchBills_success() {
        // Act & Assert
        assertDoesNotThrow(() -> directDebitsBillBatchController.pushDirectDebitsBatchBills(randomString()));
    }

    @DisplayName("正常场景：成功删除指定的物业费代扣账单")
    @Test
    public void testDeleteBillsInBatch_success() {
        // Arrange
        String batchNo = "BATCH001";
        var request = new DirectDebitsDeleteBatchBillsRequest();
        request.setBillNos(List.of("BILL001", "BILL002"));

        when(directDebitsBatchService.deleteDirectDebitsBatchBills(batchNo, request.getBillNos())).thenReturn(true);

        // Act
        directDebitsBillBatchController.deleteBillsInBatch(batchNo, request);

        // Assert
        verify(directDebitsBatchService, times(1)).deleteDirectDebitsBatchBills(batchNo, request.getBillNos());
    }

    @DisplayName("异常场景：账单编号列表为空")
    @Test
    public void testDeleteBillsInBatch_emptyBillNos() {
        // Arrange
        String batchNo = "BATCH001";
        DirectDebitsDeleteBatchBillsRequest request = new DirectDebitsDeleteBatchBillsRequest();
        request.setBillNos(Collections.emptyList());

        // Act & Assert
        assertThrows(RestInvalidParamException.class,
                     () -> directDebitsBillBatchController.deleteBillsInBatch(batchNo, request));
    }

    @DisplayName("正常场景：成功导出指定批次的代扣账单列表")
    @Test
    public void testExportBillsInBatch_success() {
        // Arrange
        String batchNo = "BATCH001";

        PositionItemResponse positionItemResponse = new PositionItemResponse();
        positionItemResponse.setProjectName(randomString());
        positionItemResponse.setBuildingName(randomString());
        positionItemResponse.setRoomName(randomString());

        var directDebitsBatchBillDetailResource = new DirectDebitsBatchBillDetailResource();
        directDebitsBatchBillDetailResource.setPositionItem(positionItemResponse);

        List<DirectDebitsBatchBillDetailResource> billList = List.of(directDebitsBatchBillDetailResource);
        HttpServletResponse response = Mockito.mock(HttpServletResponse.class);

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), eq(AptBillSearchReqBo.class)))
                                .thenReturn(new AptBillSearchReqBo());
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), eq(AptDirectDebitsBatchBillExportVo.class)))
                                .thenReturn(new AptDirectDebitsBatchBillExportVo());

            when(directDebitsBatchService.queryBillsInBatch(eq(batchNo), any(AptBillSearchReqBo.class))).thenReturn(
                    billList);

            // Act
            assertThrows(ExcelGenerateException.class,
                         () -> directDebitsBillBatchController.exportBillsInBatch(batchNo, new AptBillSearchReqVo(),
                                                                                  response));

            // Assert
            verify(directDebitsBatchService, times(1)).queryBillsInBatch(eq(batchNo), any(AptBillSearchReqBo.class));
        }
    }

    @DisplayName("异常场景：导出内容为空")
    @Test
    public void testExportBillsInBatch_emptyContent() {
        // Arrange
        String batchNo = "BATCH001";
        when(directDebitsBatchService.queryBillsInBatch(eq(batchNo), any(AptBillSearchReqBo.class))).thenReturn(
                Collections.emptyList());

        HttpServletResponse response = Mockito.mock(HttpServletResponse.class);
        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), eq(AptBillSearchReqBo.class)))
                                .thenReturn(new AptBillSearchReqBo());
            // Act & Assert
            assertThrows(RestInvalidParamException.class,
                         () -> directDebitsBillBatchController.exportBillsInBatch(batchNo, new AptBillSearchReqVo(),
                                                                                  response));
        }

    }

    @DisplayName("正常场景：成功获取批次的代扣账单总金额")
    @Test
    public void testGetBillAmount_success() {
        // Arrange
        String batchNo = "BATCH001";
        var req = new AptBillSearchReqVo();

        var expectedResponse = new DirectDebitsBatchBillAmountResource();
        expectedResponse.setTotalAmount(BigDecimal.valueOf(1000));

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), eq(AptBillSearchReqBo.class)))
                                .thenReturn(new AptBillSearchReqBo());

            when(directDebitsBatchService.calculateBatchBillAmount(eq(batchNo), any(AptBillSearchReqBo.class)))
                    .thenReturn(expectedResponse);

            // Act
            DirectDebitsBatchBillAmountResource result = directDebitsBillBatchController.getBillAmount(batchNo, req);

            // Assert
            assertNotNull(result);
            assertEquals(BigDecimal.valueOf(1000), result.getTotalAmount());
            verify(directDebitsBatchService, times(1)).calculateBatchBillAmount(eq(batchNo), any(AptBillSearchReqBo.class));
        }
    }

    @DisplayName("正常场景：成功检查过期的批次")
    @Test
    public void testLapsedCheck_success() {
        // Arrange
        List<DirectDebitsBillBatchQueryResource> expectedResponse = List.of(new DirectDebitsBillBatchQueryResource());
        when(directDebitsBatchService.lapsedCheck()).thenReturn(expectedResponse);

        // Act
        List<DirectDebitsBillBatchQueryResource> result = directDebitsBillBatchController.lapsedCheck();

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(directDebitsBatchService, times(1)).lapsedCheck();
    }

    @DisplayName("正常场景：成功执行批次的直接支付")
    @Test
    public void testConductDirectPayment_success() {
        // Arrange
        AptBillDirectDebitsBatch batch = new AptBillDirectDebitsBatch();
        batch.setBillCount(10);
        batch.setBillSentCount(5);

        List<AptBillDirectDebitsBatch> batches = List.of(batch);
        Map<String, List<AptDirectDebitsBatchBill>> paymentInfoMap = Map.of(
                "PAYMENT_INFO_1", List.of(new AptDirectDebitsBatchBill())
        );

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            when(batchRepository.findAll(any(Predicate.class))).thenReturn(batches);
            when(batchBillService.getRoomBillsOfBatch(batch)).thenReturn(paymentInfoMap);

            // Act
            directDebitsBillBatchController.conductDirectPayment();

            // Assert
            verify(batchRepository, times(1)).findAll(any(Predicate.class));
            verify(batchBillService, times(1)).getRoomBillsOfBatch(batch);
            verify(batchBillService, times(1)).conductRoomDirectPaymentHandler(eq("PAYMENT_INFO_1"), anyList());
            verify(batchRepository, times(1)).save(batch);
            assertEquals(batch.getBillCount(), batch.getBillSentCount());
        }
    }

    @DisplayName("异常场景：没有符合条件的批次")
    @Test
    public void testConductDirectPayment_noBatches() {
        // Arrange
        when(batchRepository.findAll(any(Predicate.class))).thenReturn(Collections.emptyList());

        // Act
        directDebitsBillBatchController.conductDirectPayment();

        // Assert
        verify(batchRepository, times(1)).findAll(any(Predicate.class));
        verify(batchBillService, times(0)).getRoomBillsOfBatch(any());
        verify(batchRepository, times(0)).save(any());
    }

}