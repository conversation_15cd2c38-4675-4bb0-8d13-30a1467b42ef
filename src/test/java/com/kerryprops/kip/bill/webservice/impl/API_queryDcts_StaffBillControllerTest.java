package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.BillRepository;
import com.kerryprops.kip.bill.dao.entity.BillEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;

@Slf4j
public class API_queryDcts_StaffBillControllerTest extends BaseClass_StaffBillControllerTest {

    protected final String STAFF_JSON_ABNORMAL1 = "{\"buildingIds\":\"\",\"nickName\":\"JAKCAdmin\"," +
            "\"staff\":true,\"userId\":345,\"uniqueUserId\":\"345\",\"fromType\":\"S\",\"client\":false," +
            "\"superAdmin\":false,\"tenant\":false}";

    protected final String STAFF_JSON_ABNORMAL2 = "{\"nickName\":\"JAKCAdmin\"," +
            "\"staff\":true,\"userId\":345,\"uniqueUserId\":\"345\",\"fromType\":\"S\",\"client\":false," +
            "\"superAdmin\":false,\"tenant\":false}";

    private final String PROJECT_ID = "P2";

    private List<BillEntity> dataSet;

    @Autowired
    private BillRepository billRepository;

    protected void verify(ResponseEntity<RespWrapVo> response, List<String> expectedDctList) {
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(RespCodeEnum.SUCCESS.getCode(), response.getBody().getCode());
        assertEquals(RespCodeEnum.SUCCESS.getMessage(), response.getBody().getMessage());

        List<String> contentList = (List<String>) response.getBody().getData();
        Set<String> actualDctList = new HashSet<>(contentList);
        assertTrue(expectedDctList.stream().sorted().collect(Collectors.joining(","))
                .equals(actualDctList.stream().sorted().collect(Collectors.joining(","))));
    }

    @Test
    void _01_queryDcts_empty_projectId() {
        ResponseEntity<RespWrapVo> responseEntity = call_queryDcts_api(SUPER_ADMIN_JSON, StringUtils.EMPTY);
        RespWrapVo respWrapVo = responseEntity.getBody();
        assertEquals(respWrapVo.getCode(), RespCodeEnum.UNKNOWN_ERROR.getCode());
    }

    @Test
    void _02_queryDcts_blank_projectId() {
        ResponseEntity<RespWrapVo> responseEntity = call_queryDcts_api(SUPER_ADMIN_JSON, "  ");
        RespWrapVo respWrapVo = responseEntity.getBody();
        assertEquals(respWrapVo.getCode(), RespCodeEnum.UNKNOWN_ERROR.getCode());
    }

    @Test
    void _03_queryDcts_user_not_login() {
        ResponseEntity<RespWrapVo> responseEntity = call_queryDcts_api(null, PROJECT_ID);
        RespWrapVo respWrapVo = responseEntity.getBody();
        assertEquals(RespCodeEnum.UNKNOWN_ERROR.getCode(), respWrapVo.getCode());
        assertEquals("user_not_login", respWrapVo.getMessage());
    }

    @Test
    void _04_01_queryDcts_superAdmin_hiveAsClient_return_null() throws URISyntaxException, IOException {
        doReturn(null).when(hiveAsClient).getCenterByIds(ArgumentMatchers.any());

        ResponseEntity<RespWrapVo> responseEntity = call_queryDcts_api(SUPER_ADMIN_JSON, PROJECT_ID);

        List<String> expectDctList = Collections.emptyList();
        verify(responseEntity, expectDctList);
    }

    @Test
    void _04_02_queryDcts_superAdmin_hiveAsClient_return_empty_data() throws URISyntaxException, IOException {
        doReturn(Collections.emptyList()).when(hiveAsClient).getCenterByIds(ArgumentMatchers.any());

        ResponseEntity<RespWrapVo> responseEntity = call_queryDcts_api(SUPER_ADMIN_JSON, PROJECT_ID);

        List<String> expectDctList = Collections.emptyList();
        verify(responseEntity, expectDctList);
    }

    @Test
    void _04_03_queryDcts_superAdmin_hiveAsClient_return_valid() throws URISyntaxException, IOException {
        mockJdeBusByProject(PROJECT_ID);
        ResponseEntity<RespWrapVo> responseEntity = call_queryDcts_api(SUPER_ADMIN_JSON, PROJECT_ID);

        List<String> expectDctList = Lists.newArrayList("D2", "D3", "UA");
        verify(responseEntity, expectDctList);
    }

    @Test
    void _05_queryDcts_staff_null_buildingId() throws URISyntaxException, IOException {
        mockProjectId(PROJECT_ID);
        ResponseEntity<RespWrapVo> responseEntity = call_queryDcts_api(STAFF_JSON_ABNORMAL1, PROJECT_ID);

        List<String> expectDctList = Collections.emptyList();
        verify(responseEntity, expectDctList);
    }

    @Test
    void _06_queryDcts_staff_emptyBuildingId() throws URISyntaxException, IOException {
        mockProjectId(PROJECT_ID);
        ResponseEntity<RespWrapVo> responseEntity = call_queryDcts_api(STAFF_JSON_ABNORMAL2, PROJECT_ID);

        List<String> expectDctList = Collections.emptyList();
        verify(responseEntity, expectDctList);
    }

    @Test
    void _07_02_queryDcts_staff_hiveAsClient_return_error() throws URISyntaxException, IOException {
        final String ERROR_TIPS = "xxxx";
        mockProjectId(PROJECT_ID);

        doThrow(new RuntimeException(ERROR_TIPS)).when(hiveAsClient).convertToJdeBus(ArgumentMatchers.anyList());

        ResponseEntity<RespWrapVo> responseEntity = call_queryDcts_api(STAFF_JSON, PROJECT_ID);
        RespWrapVo respWrapVo = responseEntity.getBody();

        assertEquals(respWrapVo.getCode(), RespCodeEnum.UNKNOWN_ERROR.getCode());
        assertEquals(ERROR_TIPS, respWrapVo.getMessage());
    }

    @Test
    void _07_03_queryDcts_staff_hiveAsClient_return_empty_data() throws URISyntaxException, IOException {
        mockProjectId(PROJECT_ID);
        doReturn(Collections.EMPTY_LIST).when(hiveAsClient).getBuildingByIds(ArgumentMatchers.any());

        ResponseEntity<RespWrapVo> responseEntity = call_queryDcts_api(STAFF_JSON, PROJECT_ID);
        RespWrapVo respWrapVo = responseEntity.getBody();

        assertEquals(respWrapVo.getCode(), RespCodeEnum.SUCCESS.getCode());
        assertEquals(respWrapVo.getMessage(), RespCodeEnum.SUCCESS.getMessage());

        Collection collection = (Collection) respWrapVo.getData();
        assertEquals(collection.size(), 0);
    }

    @Test
    void _08_01_queryDcts_staff_building1() throws URISyntaxException, IOException {
        mockProjectId(PROJECT_ID);
        mockBuildingBU("P1-B1");

        final String xUserJson = "{\"buildingIds\":\"P1-B1\",\"nickName\":\"JAKCAdmin\"," +
                "\"staff\":true,\"userId\":345,\"uniqueUserId\":\"345\",\"fromType\":\"S\",\"client\":false," +
                "\"superAdmin\":false,\"tenant\":false}";
        ResponseEntity<RespWrapVo> responseEntity = call_queryDcts_api(xUserJson, PROJECT_ID);

        List<String> expectDctList = Collections.emptyList();
        verify(responseEntity, expectDctList);
    }

    @Test
    void _08_02_queryDcts_staff_building2() throws URISyntaxException, IOException {
        mockJdeBusByProject(PROJECT_ID);
        mockJdeBusByBuildingBU("P2-B1");

        final String xUserJson = "{\"buildingIds\":\"P2-B1\",\"nickName\":\"JAKCAdmin\"," +
                "\"staff\":true,\"userId\":345,\"uniqueUserId\":\"345\",\"fromType\":\"S\",\"client\":false," +
                "\"superAdmin\":false,\"tenant\":false}";
        ResponseEntity<RespWrapVo> responseEntity = call_queryDcts_api(xUserJson, PROJECT_ID);

        List<String> expectDctList = Lists.newArrayList("D3");
        verify(responseEntity, expectDctList);
    }

    @Test
    void _08_03_queryDcts_staff_building3() throws URISyntaxException, IOException {
        mockJdeBusByProject(PROJECT_ID);
        mockJdeBusByBuildingBU("P1-B1;P2-B1");


        final String xUserJson = "{\"buildingIds\":\"P1-B1,P2-B1\",\"nickName\":\"JAKCAdmin\"," +
                "\"staff\":true,\"userId\":345,\"uniqueUserId\":\"345\",\"fromType\":\"S\",\"client\":false," +
                "\"superAdmin\":false,\"tenant\":false}";
        ResponseEntity<RespWrapVo> responseEntity = call_queryDcts_api(xUserJson, PROJECT_ID);

        List<String> expectDctList = Lists.newArrayList("D3");
        verify(responseEntity, expectDctList);
    }

    @BeforeEach
    void prepareTestData() {
        List<String> dcts = List.of("D1", "D1", "D1", "D1", "D1", "D1",     //1~6
                "D1", "D3", "D3", "UA", "D2", "D2",                                   //7~12
                "D1", "D1", "D1");                                                    //13~15
        List<Integer> deleteBillIndexList = Lists.list(1, 7, 13);
        List<BillEntity> billEntities = buildInitialDataset();
        for (int i = 1; i <= billEntities.size(); i++) {
            BillEntity billEntity = billEntities.get(i - 1);

            String delFlag = deleteBillIndexList.contains(i) ? "1" : "0";
            billEntity.setDelFlag(delFlag);
            billEntity.setTpDct(dcts.get(i - 1));
        }

        dataSet = billRepository.saveAll(billEntities);
    }

    @AfterEach
    void clearTestData() {
        billRepository.deleteAll(dataSet);
    }

    private ResponseEntity<RespWrapVo> call_queryDcts_api(String xUserJson, String projectId) {
        // headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        if (Objects.nonNull(xUserJson)) {
            headers.set("x-user", xUserJson);
        }

        HttpEntity request = new HttpEntity<>(null, headers);
        String url = "http://localhost:" + serverPort + "/s/bill/dcts/" + projectId;

        return restTemplate.exchange(url, HttpMethod.GET, request, RespWrapVo.class);
    }

}
