package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.exceptions.ExceptionResource;
import com.kerryprops.kip.bill.webservice.vo.req.ImportKerryBillRequest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.List;

import static com.kerryprops.kip.bill.common.aop.SignValidAop.MAX_REQUEST;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.METHOD_SIGN_VALID_ERROR;

/**
 * BillImportController和其切面单元测试类
 *
 * <AUTHOR>
 * @date 2024-10-17
 */
class BillImportControllerTest extends BaseIntegrationTest {

    /**
     * 导入账单，验证其切面：签名校验不通过
     */
    @Test
    void _01_import_sign_invalid_exception() {
        ResponseEntity<ExceptionResource> responseEntity = call_import_api(randomString()
                , String.valueOf(LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli()));
        ExceptionResource exceptionResource = responseEntity.getBody();

        Assertions.assertEquals(METHOD_SIGN_VALID_ERROR.getCode(), exceptionResource.getCode());
        Assertions.assertEquals(METHOD_SIGN_VALID_ERROR.getCode(), exceptionResource.getErrorCode());
        Assertions.assertEquals(METHOD_SIGN_VALID_ERROR.getMessage(), exceptionResource.getErrorText());
    }

    /**
     * 导入账单，验证其切面：时间戳超时
     */
    @Test
    void _02_import_timeout_intercept_success() {
        ResponseEntity<ExceptionResource> responseEntity = call_import_api(randomString()
                , String.valueOf(LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli() + MAX_REQUEST + 6000));
        ExceptionResource exceptionResource = responseEntity.getBody();

        Assertions.assertEquals(METHOD_SIGN_VALID_ERROR.getCode(), exceptionResource.getCode());
        Assertions.assertEquals(METHOD_SIGN_VALID_ERROR.getCode(), exceptionResource.getErrorCode());
        Assertions.assertEquals(METHOD_SIGN_VALID_ERROR.getMessage(), exceptionResource.getErrorText());
    }

    private ResponseEntity<ExceptionResource> call_import_api(String sign, String timestamp) {
        // headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("sign", sign);
        headers.set("timestamp", timestamp);

        // body
        ImportKerryBillRequest.ImportKerryBill importKerryBillTest = new ImportKerryBillRequest.ImportKerryBill();
        importKerryBillTest.setDocumentType(randomString());
        importKerryBillTest.setDocumentTextDesc(randomString());
        importKerryBillTest.setDocumentEngDesc(randomString());
        importKerryBillTest.setBillFileUrl(randomString());
        importKerryBillTest.setBillFilename(randomString());
        importKerryBillTest.setDisplayWebsite(randomString());
        importKerryBillTest.setBillSource(randomString());
        importKerryBillTest.setCompanyCode(randomString());
        importKerryBillTest.setCompanyName(randomString());
        importKerryBillTest.setAn8(randomString());
        importKerryBillTest.setAlph(randomString());
        importKerryBillTest.setDoco(randomString());
        importKerryBillTest.setMcu(randomString());
        importKerryBillTest.setMcuDesc(randomString());
        importKerryBillTest.setUnit(randomString());
        importKerryBillTest.setUnitDesc(randomString());
        importKerryBillTest.setBillYear(random.nextInt());
        importKerryBillTest.setBillMonth(random.nextInt());
        importKerryBillTest.setPrintTime(randomString());
        importKerryBillTest.setGenerateTime(new Date());

        ImportKerryBillRequest importKerryBillRequestTest = new ImportKerryBillRequest();
        importKerryBillRequestTest.setBills(List.of(importKerryBillTest));
        HttpEntity<ImportKerryBillRequest> request = new HttpEntity<>(importKerryBillRequestTest, headers);

        String url = LOCAL_HOST_DOMAIN + serverPort + "/s/tenants/bills/import";

        return restTemplate.exchange(url, HttpMethod.POST, request, ExceptionResource.class);
    }

}
