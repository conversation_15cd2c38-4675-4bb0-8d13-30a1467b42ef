package com.kerryprops.kip.bill.webservice.vo.resp;

import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import static com.kerryprops.kip.bill.utils.RandomUtil.randomObject;
import static org.assertj.core.api.Assertions.assertThat;

/**
 * CashierPaymentTransactionResourceTest.
 *
 * <AUTHOR> Yu 2025-01-21 15:44:21
 **/
class CashierPaymentTransactionResourceTest {

    @DisplayName("正常转换为响应对象")
    @ParameterizedTest
    @ValueSource(strings = {"ONLINE", "OFFLINE", "DIRECT_DEBITS"})
    void of_success(BillPayChannel billPayChannel) {
        AptPay vo = randomObject(AptPay.class);
        vo.setPayChannel(billPayChannel);

        CashierPaymentTransactionResource resource = CashierPaymentTransactionResource.of(vo);

        assertThat(resource).usingRecursiveComparison()
                .ignoringFields("accountFromType", "advanceAmt", "isAdvanceBilling",
                        "canInvoiceBillAmount", "comment", "status", "totalAmt")
                .isEqualTo(vo);
        assertThat(resource.getTotalAmt().doubleValue()).isEqualTo(vo.getTotalAmt());
        assertThat(resource.getAccountFromType()).isEqualTo(vo.getCreateBy());
        if (BillPayChannel.OFFLINE.equals(billPayChannel)) {
            assertThat(resource.getComment()).isEqualTo(vo.getComments());
        } else {
            assertThat(resource.getComment()).isEqualTo(vo.getPayDesc());
        }
    }

}