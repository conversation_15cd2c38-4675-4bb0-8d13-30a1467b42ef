# 并行执行配置 - 启用最佳性能
junit.jupiter.execution.parallel.enabled=true
junit.jupiter.execution.parallel.mode.default=concurrent
junit.jupiter.execution.parallel.mode.classes.default=concurrent

# 并行策略配置
# dynamic: 根据可用处理器动态调整线程数
# fixed: 使用固定线程数
# custom: 使用自定义策略
junit.jupiter.execution.parallel.config.strategy=dynamic

# 动态策略的并行度因子 (推荐值: 1.0 表示使用所有可用处理器)
junit.jupiter.execution.parallel.config.dynamic.factor=1.0

# 最大并行度 (可选，限制最大线程数以避免资源过度消耗)
# junit.jupiter.execution.parallel.config.dynamic.max-pool-size=256

# 固定策略的线程数 (如果使用 fixed 策略)
# junit.jupiter.execution.parallel.config.fixed.parallelism=8

# 自定义线程池配置 (如果使用 custom 策略)
# junit.jupiter.execution.parallel.config.custom.class=com.example.CustomParallelExecutionConfigurationStrategy

# 测试实例生命周期 - 每个方法一个实例以确保线程安全
junit.jupiter.testinstance.lifecycle.default=per_method

# 显示名称生成器 - 提高测试报告可读性
junit.jupiter.displayname.generator.default=org.junit.jupiter.api.DisplayNameGenerator$ReplaceUnderscores

# 条件测试执行
junit.jupiter.conditions.deactivate=org.junit.*DisabledCondition

# 扩展自动检测
junit.jupiter.extensions.autodetection.enabled=true

# 测试方法排序 (可选 - 如果需要特定顺序)
# junit.jupiter.testmethod.order.default=org.junit.jupiter.api.MethodOrderer$OrderAnnotation

# 测试类排序 (可选)
# junit.jupiter.testclass.order.default=org.junit.jupiter.api.ClassOrderer$OrderAnnotation

# 参数化测试显示名称
junit.jupiter.params.displayname.default={displayName} [{index}] {arguments}

# 超时配置 (防止测试无限期运行)
junit.jupiter.execution.timeout.default=5m
junit.jupiter.execution.timeout.testable.method.default=1m
junit.jupiter.execution.timeout.testtemplate.method.default=1m