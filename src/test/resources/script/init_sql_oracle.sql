create schema if not exists `CRPDTA`;
create table  if not exists `CRPDTA`.`F55GT01K`
(
    `TOKCO`  varchar(5)    not null,
    `TODCT`  varchar(2)    not null,
    `TODOC`  int           not null,
    `TOSFX`  varchar(3)    not null,
    `TOMCU`  varchar(8)    not null,
    `TOUNIT` varchar(12)   not null,
    `TODOCO` varchar(6)    not null,
    `TOAN8`  varchar(8)    not null
);

create table if not exists `CRPDTA`.`F58Q9002`
(
    `IFDOC`  int         NOT NULL,
    `IFDCT`  varchar(2)  NOT NULL,
    `IFKCO`  varchar(5)  NOT NULL,
    `IFSFX`  varchar(3)  NOT NULL,
    `IFLINN` int         NOT NULL,
    `IFMCU`  varchar(12),
    `IFDOC0` int,
    `IFGUI`  varchar(25) NOT NULL,
    `IFTAX`  varchar(20),
    `IFAG`   int,
    `IFATXA` int,
    `IFSTAM` int,
    `IFURAB` int,
    `IFURAT` int,
    `IFURCD` varchar(2),
    `IFURDT` int,
    `IFURRF` varchar(15),
    `IFEV01` varchar(1),
    `IFEV02` varchar(1),
    `IFEV03` varchar(1),
    `IFUSER` varchar(10),
    `IFUPMJ` int,
    `IFUPMT` int,
    `IFJOBN` varchar(10),
    `IFPID`  varchar(10),
    `IFCINV` varchar(20),
    `IFPCM`  varchar(10),
    `IFTXR1` int
);

CREATE UNIQUE INDEX IF NOT EXISTS uniq_f58q9002 ON `CRPDTA`.`F58Q9002`(`IFDOC`, `IFKCO`, `IFDCT`, `IFSFX`, `IFLINN`, `IFGUI`);
CREATE INDEX IF NOT EXISTS idx_f58q9002_1 ON `CRPDTA`.`F58Q9002`(`IFDOC0`, `IFLINN`);

commit;
