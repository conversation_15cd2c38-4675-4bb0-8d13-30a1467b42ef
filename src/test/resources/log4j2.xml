<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
    <Appenders>
        <!-- 简化的控制台输出 -->
        <Console name="CONSOLE" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{HH:mm:ss.SSS} %-5level %msg%n" />
        </Console>
    </Appenders>

    <Loggers>
        <!-- 关闭所有第三方库的详细日志 -->
        <Logger name="org.springframework" level="ERROR"/>
        <Logger name="org.hibernate" level="ERROR"/>
        <Logger name="org.apache" level="ERROR"/>
        <Logger name="com.zaxxer.hikari" level="ERROR"/>
        <Logger name="org.eclipse.jetty" level="ERROR"/>
        <Logger name="com.github.tomakehurst.wiremock" level="ERROR"/>
        <Logger name="org.zalando.logbook" level="OFF"/>

        <!-- 关闭 Spring Boot 测试相关警告 -->
        <Logger name="org.springframework.boot" level="ERROR"/>

        <!-- 关闭重复 JSON 对象警告 -->
        <Logger name="org.springframework.boot.test.context.filter.DuplicateJsonObjectContextCustomizerFactory" level="OFF"/>

        <!-- 应用日志 - 只保留测试相关的错误信息 -->
        <Logger name="com.kerryprops.kip.bill.interceptors.BizAuthInterceptor" level="ERROR"/>
        <Logger name="com.kerryprops.kip.bill.common.exceptions.handler.AppExceptionHandler" level="OFF"/>
        <Logger name="com.kerryprops.kip.bill" level="ERROR"/>

        <!-- Root logger 设为ERROR，只显示错误 -->
        <Root level="ERROR">
            <AppenderRef ref="CONSOLE"/>
        </Root>
    </Loggers>
</Configuration>